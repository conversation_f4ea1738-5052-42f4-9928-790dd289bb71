name: Production Monitoring & Alerting

on:
  schedule:
    # Run every 5 minutes during business hours (9 AM - 6 PM UTC)
    - cron: '*/5 9-18 * * 1-5'
    # Run every 15 minutes outside business hours
    - cron: '*/15 0-8,19-23 * * *'
    - cron: '*/15 * * * 0,6'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to monitor'
        required: true
        default: 'production'
        type: choice
        options:
        - production
        - staging
      alert_threshold:
        description: 'Response time alert threshold (ms)'
        required: false
        default: '2000'
        type: string

env:
  FORCE_COLOR: '1'
  CI: 'true'

jobs:
  health-monitoring:
    name: Health & Performance Monitoring
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        environment: [production, staging]
    
    steps:
    - name: Set environment URLs
      id: urls
      run: |
        if [[ "${{ matrix.environment }}" == "production" ]]; then
          echo "backend-url=https://ailex-ad-agent-backend.fly.dev" >> $GITHUB_OUTPUT
          echo "frontend-url=https://ailex.yourdomain.com" >> $GITHUB_OUTPUT
        else
          echo "backend-url=https://ailex-ad-agent-backend-staging.fly.dev" >> $GITHUB_OUTPUT
          echo "frontend-url=https://staging.ailex.yourdomain.com" >> $GITHUB_OUTPUT
        fi
        
    - name: Backend Health Check
      id: backend-health
      run: |
        echo "Checking backend health for ${{ matrix.environment }}..."
        
        # Test liveness endpoint
        START_TIME=$(date +%s%3N)
        if curl -f --max-time 10 -s "${{ steps.urls.outputs.backend-url }}/api/v1/health/liveness"; then
          END_TIME=$(date +%s%3N)
          RESPONSE_TIME=$((END_TIME - START_TIME))
          echo "✅ Backend liveness check passed (${RESPONSE_TIME}ms)"
          echo "liveness=success" >> $GITHUB_OUTPUT
          echo "liveness-time=$RESPONSE_TIME" >> $GITHUB_OUTPUT
        else
          echo "❌ Backend liveness check failed"
          echo "liveness=failure" >> $GITHUB_OUTPUT
          echo "liveness-time=0" >> $GITHUB_OUTPUT
        fi
        
        # Test readiness endpoint
        START_TIME=$(date +%s%3N)
        if curl -f --max-time 15 -s "${{ steps.urls.outputs.backend-url }}/api/v1/health/readiness"; then
          END_TIME=$(date +%s%3N)
          RESPONSE_TIME=$((END_TIME - START_TIME))
          echo "✅ Backend readiness check passed (${RESPONSE_TIME}ms)"
          echo "readiness=success" >> $GITHUB_OUTPUT
          echo "readiness-time=$RESPONSE_TIME" >> $GITHUB_OUTPUT
        else
          echo "❌ Backend readiness check failed"
          echo "readiness=failure" >> $GITHUB_OUTPUT
          echo "readiness-time=0" >> $GITHUB_OUTPUT
        fi
        
    - name: Frontend Health Check
      id: frontend-health
      run: |
        echo "Checking frontend health for ${{ matrix.environment }}..."
        
        START_TIME=$(date +%s%3N)
        if curl -f --max-time 15 -L -s "${{ steps.urls.outputs.frontend-url }}" > /dev/null; then
          END_TIME=$(date +%s%3N)
          RESPONSE_TIME=$((END_TIME - START_TIME))
          echo "✅ Frontend accessibility check passed (${RESPONSE_TIME}ms)"
          echo "frontend=success" >> $GITHUB_OUTPUT
          echo "frontend-time=$RESPONSE_TIME" >> $GITHUB_OUTPUT
        else
          echo "❌ Frontend accessibility check failed"
          echo "frontend=failure" >> $GITHUB_OUTPUT
          echo "frontend-time=0" >> $GITHUB_OUTPUT
        fi
        
    - name: API Integration Test
      id: api-test
      run: |
        echo "Testing API integration for ${{ matrix.environment }}..."
        
        # Test health API endpoint
        if curl -f --max-time 10 -s "${{ steps.urls.outputs.backend-url }}/api/v1/health" | jq -e '.status == "healthy"' > /dev/null; then
          echo "✅ API health endpoint working"
          echo "api=success" >> $GITHUB_OUTPUT
        else
          echo "❌ API health endpoint failed"
          echo "api=failure" >> $GITHUB_OUTPUT
        fi
        
    - name: Performance Analysis
      id: performance
      run: |
        echo "Analyzing performance for ${{ matrix.environment }}..."
        
        ALERT_THRESHOLD=${{ github.event.inputs.alert_threshold || '2000' }}
        BACKEND_TIME=${{ steps.backend-health.outputs.liveness-time }}
        FRONTEND_TIME=${{ steps.frontend-health.outputs.frontend-time }}
        
        echo "Backend response time: ${BACKEND_TIME}ms"
        echo "Frontend response time: ${FRONTEND_TIME}ms"
        echo "Alert threshold: ${ALERT_THRESHOLD}ms"
        
        PERFORMANCE_ISSUES=""
        
        if [[ $BACKEND_TIME -gt $ALERT_THRESHOLD ]]; then
          PERFORMANCE_ISSUES="${PERFORMANCE_ISSUES}- Backend response time (${BACKEND_TIME}ms) exceeds threshold (${ALERT_THRESHOLD}ms)\n"
        fi
        
        if [[ $FRONTEND_TIME -gt $ALERT_THRESHOLD ]]; then
          PERFORMANCE_ISSUES="${PERFORMANCE_ISSUES}- Frontend response time (${FRONTEND_TIME}ms) exceeds threshold (${ALERT_THRESHOLD}ms)\n"
        fi
        
        if [[ -n "$PERFORMANCE_ISSUES" ]]; then
          echo "performance=warning" >> $GITHUB_OUTPUT
          echo -e "issues<<EOF" >> $GITHUB_OUTPUT
          echo -e "$PERFORMANCE_ISSUES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
        else
          echo "performance=good" >> $GITHUB_OUTPUT
          echo "issues=" >> $GITHUB_OUTPUT
        fi
        
    - name: Create Alert Issue
      if: steps.backend-health.outputs.liveness == 'failure' || steps.backend-health.outputs.readiness == 'failure' || steps.frontend-health.outputs.frontend == 'failure'
      uses: actions/github-script@v7
      with:
        script: |
          const environment = '${{ matrix.environment }}';
          const backendHealth = '${{ steps.backend-health.outputs.liveness }}';
          const readinessHealth = '${{ steps.backend-health.outputs.readiness }}';
          const frontendHealth = '${{ steps.frontend-health.outputs.frontend }}';
          const apiTest = '${{ steps.api-test.outputs.api }}';
          
          const issues = [];
          if (backendHealth === 'failure') issues.push('Backend liveness check failed');
          if (readinessHealth === 'failure') issues.push('Backend readiness check failed');
          if (frontendHealth === 'failure') issues.push('Frontend accessibility failed');
          if (apiTest === 'failure') issues.push('API integration test failed');
          
          const title = `🚨 ${environment.toUpperCase()} Health Check Alert - ${new Date().toISOString()}`;
          
          const body = `## Health Check Alert
          
          **Environment:** ${environment}
          **Time:** ${new Date().toISOString()}
          **Workflow Run:** [#${{ github.run_number }}](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
          
          ### Failed Checks
          ${issues.map(issue => `- ❌ ${issue}`).join('\n')}
          
          ### Status Summary
          - Backend Liveness: ${backendHealth === 'success' ? '✅' : '❌'}
          - Backend Readiness: ${readinessHealth === 'success' ? '✅' : '❌'}
          - Frontend: ${frontendHealth === 'success' ? '✅' : '❌'}
          - API Integration: ${apiTest === 'success' ? '✅' : '❌'}
          
          ### URLs
          - Backend: ${{ steps.urls.outputs.backend-url }}
          - Frontend: ${{ steps.urls.outputs.frontend-url }}
          
          ### Next Steps
          1. Check application logs in Fly.io dashboard
          2. Verify database and Redis connectivity
          3. Check recent deployments for potential issues
          4. Monitor for automatic recovery
          
          **This issue will be automatically updated with recovery status.**
          `;
          
          // Create issue
          const issue = await github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: title,
            body: body,
            labels: ['alert', 'health-check', environment, 'urgent']
          });
          
          console.log(`Created alert issue: ${issue.data.html_url}`);
          
    - name: Performance Warning
      if: steps.performance.outputs.performance == 'warning'
      run: |
        echo "::warning title=Performance Alert::Performance issues detected in ${{ matrix.environment }}"
        echo -e "${{ steps.performance.outputs.issues }}"
        
  # SSL Certificate Monitoring
  ssl-monitoring:
    name: SSL Certificate Monitoring
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || github.event.inputs.environment == 'production'
    
    steps:
    - name: Check SSL Certificate Expiry
      run: |
        echo "Checking SSL certificate expiry..."
        
        DOMAINS=("ailex-ad-agent-backend.fly.dev" "ailex.yourdomain.com")
        
        for domain in "${DOMAINS[@]}"; do
          echo "Checking SSL for $domain..."
          
          # Get certificate expiry date
          EXPIRY=$(echo | openssl s_client -servername $domain -connect $domain:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
          
          if [[ -n "$EXPIRY" ]]; then
            EXPIRY_EPOCH=$(date -d "$EXPIRY" +%s)
            CURRENT_EPOCH=$(date +%s)
            DAYS_UNTIL_EXPIRY=$(( (EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))
            
            echo "SSL certificate for $domain expires in $DAYS_UNTIL_EXPIRY days"
            
            if [[ $DAYS_UNTIL_EXPIRY -lt 30 ]]; then
              echo "::warning title=SSL Certificate Expiry::SSL certificate for $domain expires in $DAYS_UNTIL_EXPIRY days"
            fi
            
            if [[ $DAYS_UNTIL_EXPIRY -lt 7 ]]; then
              echo "::error title=SSL Certificate Critical::SSL certificate for $domain expires in $DAYS_UNTIL_EXPIRY days - immediate action required"
            fi
          else
            echo "::error title=SSL Check Failed::Could not retrieve SSL certificate information for $domain"
          fi
        done
        
  # Database and Redis Monitoring
  database-monitoring:
    name: Database & Cache Monitoring
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    
    steps:
    - name: Test Database Connectivity
      run: |
        echo "Testing database connectivity through health endpoints..."
        
        # Test production backend health which includes DB check
        if curl -f --max-time 10 "https://ailex-ad-agent-backend.fly.dev/api/v1/health/readiness"; then
          echo "✅ Database connectivity test passed"
        else
          echo "❌ Database connectivity test failed"
          echo "::error title=Database Connection::Database connectivity check failed"
        fi
        
    - name: Test Redis Connectivity
      run: |
        echo "Testing Redis connectivity through health endpoints..."
        
        # The readiness endpoint should include Redis checks
        if curl -f --max-time 10 "https://ailex-ad-agent-backend.fly.dev/api/v1/health/readiness" | grep -q "redis"; then
          echo "✅ Redis connectivity test passed"
        else
          echo "❌ Redis connectivity test may have failed"
          echo "::warning title=Redis Connection::Redis connectivity check uncertain"
        fi

  # Monitoring Summary
  monitoring-summary:
    name: Monitoring Summary
    runs-on: ubuntu-latest
    needs: [health-monitoring, ssl-monitoring, database-monitoring]
    if: always()
    
    steps:
    - name: Generate monitoring report
      run: |
        echo "# Monitoring Summary Report"
        echo ""
        echo "**Date:** $(date -u +"%Y-%m-%d %H:%M:%S UTC")"
        echo "**Workflow Run:** [#${{ github.run_number }}](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})"
        echo ""
        echo "## Job Results"
        echo "- Health Monitoring: ${{ needs.health-monitoring.result }}"
        echo "- SSL Monitoring: ${{ needs.ssl-monitoring.result }}"
        echo "- Database Monitoring: ${{ needs.database-monitoring.result }}"
        echo ""
        
        OVERALL_STATUS="SUCCESS"
        if [[ "${{ needs.health-monitoring.result }}" != "success" ]]; then
          OVERALL_STATUS="FAILURE"
          echo "❌ Critical: Health monitoring failed"
        fi
        
        if [[ "${{ needs.database-monitoring.result }}" != "success" && "${{ needs.database-monitoring.result }}" != "skipped" ]]; then
          OVERALL_STATUS="FAILURE"
          echo "❌ Critical: Database monitoring failed"
        fi
        
        echo ""
        echo "**Overall Status: $OVERALL_STATUS**"
        
        if [[ "$OVERALL_STATUS" == "SUCCESS" ]]; then
          echo "🎉 All monitoring checks passed!"
        else
          echo "⚠️ Some monitoring checks failed - review the job details above"
        fi