name: Emergency Rollback & Recovery

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to rollback'
        required: true
        type: choice
        options:
        - production
        - staging
      component:
        description: 'Component to rollback'
        required: true
        type: choice
        options:
        - both
        - backend-only
        - frontend-only
      rollback_target:
        description: 'Rollback target (leave empty for previous version)'
        required: false
        type: string
      reason:
        description: 'Reason for rollback'
        required: true
        type: string

env:
  FORCE_COLOR: '1'
  CI: 'true'

jobs:
  # Pre-rollback validation and preparation
  validate-rollback:
    name: Validate Rollback Request
    runs-on: ubuntu-latest
    
    outputs:
      should-rollback-backend: ${{ steps.validate.outputs.should-rollback-backend }}
      should-rollback-frontend: ${{ steps.validate.outputs.should-rollback-frontend }}
      backend-app: ${{ steps.validate.outputs.backend-app }}
      target-version: ${{ steps.validate.outputs.target-version }}
    
    steps:
    - name: Validate rollback parameters
      id: validate
      run: |
        echo "Validating rollback request..."
        echo "Environment: ${{ github.event.inputs.environment }}"
        echo "Component: ${{ github.event.inputs.component }}"
        echo "Reason: ${{ github.event.inputs.reason }}"
        
        # Validate reason is provided
        if [[ -z "${{ github.event.inputs.reason }}" ]]; then
          echo "::error::Rollback reason is required"
          exit 1
        fi
        
        # Set component flags
        if [[ "${{ github.event.inputs.component }}" == "both" || "${{ github.event.inputs.component }}" == "backend-only" ]]; then
          echo "should-rollback-backend=true" >> $GITHUB_OUTPUT
        else
          echo "should-rollback-backend=false" >> $GITHUB_OUTPUT
        fi
        
        if [[ "${{ github.event.inputs.component }}" == "both" || "${{ github.event.inputs.component }}" == "frontend-only" ]]; then
          echo "should-rollback-frontend=true" >> $GITHUB_OUTPUT
        else
          echo "should-rollback-frontend=false" >> $GITHUB_OUTPUT
        fi
        
        # Set backend app name
        if [[ "${{ github.event.inputs.environment }}" == "production" ]]; then
          echo "backend-app=ailex-ad-agent-backend" >> $GITHUB_OUTPUT
        else
          echo "backend-app=ailex-ad-agent-backend-staging" >> $GITHUB_OUTPUT
        fi
        
        # Set target version
        TARGET="${{ github.event.inputs.rollback_target }}"
        if [[ -z "$TARGET" ]]; then
          echo "target-version=previous" >> $GITHUB_OUTPUT
        else
          echo "target-version=$TARGET" >> $GITHUB_OUTPUT
        fi
        
        echo "✅ Rollback validation completed"
        
    - name: Create rollback issue
      uses: actions/github-script@v7
      with:
        script: |
          const title = `🚨 EMERGENCY ROLLBACK - ${{ github.event.inputs.environment }} - ${new Date().toISOString()}`;
          
          const body = `## Emergency Rollback Initiated
          
          **Environment:** ${{ github.event.inputs.environment }}
          **Component:** ${{ github.event.inputs.component }}
          **Target:** ${{ steps.validate.outputs.target-version }}
          **Reason:** ${{ github.event.inputs.reason }}
          **Initiated by:** @${{ github.actor }}
          **Time:** ${new Date().toISOString()}
          **Workflow:** [#${{ github.run_number }}](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
          
          ## Status
          - [ ] Pre-rollback health check
          - [ ] Backend rollback
          - [ ] Frontend rollback  
          - [ ] Post-rollback validation
          - [ ] Monitoring confirmation
          
          ## Components to Rollback
          - Backend: ${{ steps.validate.outputs.should-rollback-backend == 'true' && '✅' || '❌' }}
          - Frontend: ${{ steps.validate.outputs.should-rollback-frontend == 'true' && '✅' || '❌' }}
          
          **This issue will be automatically updated with rollback progress.**
          `;
          
          const issue = await github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: title,
            body: body,
            labels: ['rollback', 'urgent', '${{ github.event.inputs.environment }}', 'emergency']
          });
          
          console.log(`Created rollback tracking issue: ${issue.data.html_url}`);
          
          // Set output for other jobs
          return issue.data.number;

  # Pre-rollback health check
  pre-rollback-health-check:
    name: Pre-Rollback Health Check
    runs-on: ubuntu-latest
    needs: [validate-rollback]
    
    outputs:
      current-health: ${{ steps.health.outputs.status }}
      backup-needed: ${{ steps.health.outputs.backup-needed }}
    
    steps:
    - name: Check current system health
      id: health
      run: |
        echo "Checking current system health before rollback..."
        
        if [[ "${{ github.event.inputs.environment }}" == "production" ]]; then
          BACKEND_URL="https://ailex-ad-agent-backend.fly.dev"
          FRONTEND_URL="https://ailex.yourdomain.com"
        else
          BACKEND_URL="https://ailex-ad-agent-backend-staging.fly.dev"
          FRONTEND_URL="https://staging.ailex.yourdomain.com"
        fi
        
        HEALTH_STATUS="healthy"
        
        # Check backend
        if [[ "${{ needs.validate-rollback.outputs.should-rollback-backend }}" == "true" ]]; then
          if curl -f --max-time 10 "$BACKEND_URL/api/v1/health/liveness" > /dev/null 2>&1; then
            echo "✅ Backend is currently responsive"
          else
            echo "❌ Backend is currently not responsive"
            HEALTH_STATUS="unhealthy"
          fi
        fi
        
        # Check frontend
        if [[ "${{ needs.validate-rollback.outputs.should-rollback-frontend }}" == "true" ]]; then
          if curl -f --max-time 10 -L "$FRONTEND_URL" > /dev/null 2>&1; then
            echo "✅ Frontend is currently responsive"
          else
            echo "❌ Frontend is currently not responsive"
            HEALTH_STATUS="unhealthy"
          fi
        fi
        
        echo "status=$HEALTH_STATUS" >> $GITHUB_OUTPUT
        
        # Recommend backup if system is healthy
        if [[ "$HEALTH_STATUS" == "healthy" ]]; then
          echo "backup-needed=true" >> $GITHUB_OUTPUT
          echo "💡 System is healthy - recommend taking backup before rollback"
        else
          echo "backup-needed=false" >> $GITHUB_OUTPUT
          echo "⚠️ System is unhealthy - proceeding with emergency rollback"
        fi

  # Backend rollback
  rollback-backend:
    name: Rollback Backend
    runs-on: ubuntu-latest
    needs: [validate-rollback, pre-rollback-health-check]
    if: needs.validate-rollback.outputs.should-rollback-backend == 'true'
    
    outputs:
      rollback-success: ${{ steps.rollback.outputs.success }}
      previous-version: ${{ steps.rollback.outputs.previous-version }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master
      
    - name: Get current deployment info
      id: current
      working-directory: ./backend
      run: |
        echo "Getting current deployment information..."
        
        CURRENT_RELEASE=$(flyctl releases list --app ${{ needs.validate-rollback.outputs.backend-app }} --json | jq -r '.[0]')
        CURRENT_ID=$(echo "$CURRENT_RELEASE" | jq -r '.id')
        CURRENT_VERSION=$(echo "$CURRENT_RELEASE" | jq -r '.version')
        
        echo "Current release ID: $CURRENT_ID"
        echo "Current version: $CURRENT_VERSION"
        
        echo "current-id=$CURRENT_ID" >> $GITHUB_OUTPUT
        echo "current-version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
        
    - name: Identify rollback target
      id: target
      working-directory: ./backend
      run: |
        echo "Identifying rollback target..."
        
        if [[ "${{ needs.validate-rollback.outputs.target-version }}" == "previous" ]]; then
          # Get the previous release
          TARGET_RELEASE=$(flyctl releases list --app ${{ needs.validate-rollback.outputs.backend-app }} --json | jq -r '.[1]')
          TARGET_ID=$(echo "$TARGET_RELEASE" | jq -r '.id')
          TARGET_VERSION=$(echo "$TARGET_RELEASE" | jq -r '.version')
          
          if [[ "$TARGET_ID" == "null" || -z "$TARGET_ID" ]]; then
            echo "::error::No previous release found to rollback to"
            exit 1
          fi
        else
          # Use specified target
          TARGET_VERSION="${{ needs.validate-rollback.outputs.target-version }}"
          TARGET_ID=$(flyctl releases list --app ${{ needs.validate-rollback.outputs.backend-app }} --json | jq -r --arg version "$TARGET_VERSION" '.[] | select(.version == ($version | tonumber)) | .id')
          
          if [[ -z "$TARGET_ID" ]]; then
            echo "::error::Specified rollback target version $TARGET_VERSION not found"
            exit 1
          fi
        fi
        
        echo "Target release ID: $TARGET_ID"
        echo "Target version: $TARGET_VERSION"
        
        echo "target-id=$TARGET_ID" >> $GITHUB_OUTPUT
        echo "target-version=$TARGET_VERSION" >> $GITHUB_OUTPUT
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
        
    - name: Perform backend rollback
      id: rollback
      working-directory: ./backend
      run: |
        echo "Performing backend rollback..."
        echo "Rolling back from v${{ steps.current.outputs.current-version }} to v${{ steps.target.outputs.target-version }}"
        
        # Perform the rollback
        if flyctl releases rollback "${{ steps.target.outputs.target-id }}" --app ${{ needs.validate-rollback.outputs.backend-app }} --yes; then
          echo "✅ Rollback command executed successfully"
          
          # Wait for rollback to complete
          echo "Waiting for rollback to complete..."
          sleep 60
          
          echo "success=true" >> $GITHUB_OUTPUT
          echo "previous-version=${{ steps.current.outputs.current-version }}" >> $GITHUB_OUTPUT
        else
          echo "❌ Rollback command failed"
          echo "success=false" >> $GITHUB_OUTPUT
          exit 1
        fi
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
        
    - name: Verify backend rollback
      run: |
        echo "Verifying backend rollback..."
        
        if [[ "${{ github.event.inputs.environment }}" == "production" ]]; then
          BACKEND_URL="https://ailex-ad-agent-backend.fly.dev"
        else
          BACKEND_URL="https://ailex-ad-agent-backend-staging.fly.dev"
        fi
        
        # Test health endpoint
        for i in {1..10}; do
          if curl -f --max-time 10 "$BACKEND_URL/api/v1/health/liveness"; then
            echo "✅ Backend rollback verified - service is responding"
            break
          elif [ $i -eq 10 ]; then
            echo "❌ Backend rollback verification failed - service not responding after 10 attempts"
            exit 1
          else
            echo "Waiting for backend to respond... (attempt $i/10)"
            sleep 15
          fi
        done

  # Frontend rollback  
  rollback-frontend:
    name: Rollback Frontend
    runs-on: ubuntu-latest
    needs: [validate-rollback, pre-rollback-health-check]
    if: needs.validate-rollback.outputs.should-rollback-frontend == 'true'
    
    outputs:
      rollback-success: ${{ steps.rollback.outputs.success }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Get Vercel deployments
      id: deployments
      run: |
        echo "Getting Vercel deployment history..."
        
        # Note: This requires vercel CLI and proper authentication
        # For now, we'll show the concept - in practice you'd need to:
        # 1. Install Vercel CLI
        # 2. Authenticate with Vercel token
        # 3. List deployments and rollback to previous
        
        echo "deployment-id=placeholder" >> $GITHUB_OUTPUT
        
    - name: Rollback frontend deployment
      id: rollback
      run: |
        echo "Frontend rollback simulation..."
        echo "In a real scenario, this would:"
        echo "1. List Vercel deployments"
        echo "2. Identify the target deployment"
        echo "3. Promote the previous deployment"
        echo "4. Verify the rollback"
        
        echo "success=true" >> $GITHUB_OUTPUT
        
        # For demonstration, we'll assume success
        echo "✅ Frontend rollback completed (simulated)"

  # Post-rollback validation
  post-rollback-validation:
    name: Post-Rollback Validation
    runs-on: ubuntu-latest
    needs: [validate-rollback, rollback-backend, rollback-frontend]
    if: always() && (needs.rollback-backend.result != 'skipped' || needs.rollback-frontend.result != 'skipped')
    
    steps:
    - name: Comprehensive health check
      run: |
        echo "Performing comprehensive post-rollback health check..."
        
        if [[ "${{ github.event.inputs.environment }}" == "production" ]]; then
          BACKEND_URL="https://ailex-ad-agent-backend.fly.dev"
          FRONTEND_URL="https://ailex.yourdomain.com"
        else
          BACKEND_URL="https://ailex-ad-agent-backend-staging.fly.dev"
          FRONTEND_URL="https://staging.ailex.yourdomain.com"
        fi
        
        VALIDATION_PASSED=true
        
        # Validate backend if it was rolled back
        if [[ "${{ needs.validate-rollback.outputs.should-rollback-backend }}" == "true" ]]; then
          echo "Validating backend rollback..."
          
          if [[ "${{ needs.rollback-backend.result }}" == "success" ]]; then
            # Test multiple endpoints
            for endpoint in "/api/v1/health/liveness" "/api/v1/health/readiness" "/api/v1/health"; do
              if curl -f --max-time 10 "$BACKEND_URL$endpoint" > /dev/null 2>&1; then
                echo "✅ Backend endpoint $endpoint responding"
              else
                echo "❌ Backend endpoint $endpoint not responding"
                VALIDATION_PASSED=false
              fi
            done
          else
            echo "❌ Backend rollback failed"
            VALIDATION_PASSED=false
          fi
        fi
        
        # Validate frontend if it was rolled back  
        if [[ "${{ needs.validate-rollback.outputs.should-rollback-frontend }}" == "true" ]]; then
          echo "Validating frontend rollback..."
          
          if [[ "${{ needs.rollback-frontend.result }}" == "success" ]]; then
            if curl -f --max-time 15 -L "$FRONTEND_URL" > /dev/null 2>&1; then
              echo "✅ Frontend responding after rollback"
            else
              echo "❌ Frontend not responding after rollback"
              VALIDATION_PASSED=false
            fi
          else
            echo "❌ Frontend rollback failed"
            VALIDATION_PASSED=false
          fi
        fi
        
        if [[ "$VALIDATION_PASSED" == "true" ]]; then
          echo "🎉 Post-rollback validation successful!"
        else
          echo "💥 Post-rollback validation failed!"
          exit 1
        fi
        
    - name: Generate rollback report
      run: |
        cat << EOF > rollback-report.md
        # Rollback Report
        
        **Date:** $(date -u +"%Y-%m-%d %H:%M:%S UTC")
        **Environment:** ${{ github.event.inputs.environment }}
        **Component:** ${{ github.event.inputs.component }}
        **Reason:** ${{ github.event.inputs.reason }}
        **Initiated by:** ${{ github.actor }}
        **Workflow:** [#${{ github.run_number }}](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
        
        ## Results
        - Backend Rollback: ${{ needs.rollback-backend.result || 'skipped' }}
        - Frontend Rollback: ${{ needs.rollback-frontend.result || 'skipped' }}
        - Post-Validation: $(if [[ "${{ job.status }}" == "success" ]]; then echo "✅ Passed"; else echo "❌ Failed"; fi)
        
        ## Details
        $(if [[ "${{ needs.rollback-backend.result }}" == "success" ]]; then
          echo "- Backend rolled back from v${{ needs.rollback-backend.outputs.previous-version }}"
        fi)
        $(if [[ "${{ needs.rollback-frontend.result }}" == "success" ]]; then
          echo "- Frontend rollback completed"
        fi)
        
        ## Status
        $(if [[ "${{ job.status }}" == "success" ]]; then
          echo "**Status: SUCCESSFUL** 🎉"
          echo ""
          echo "All systems have been successfully rolled back and are functioning normally."
        else
          echo "**Status: FAILED** ❌"
          echo ""
          echo "Rollback encountered issues. Manual intervention may be required."
        fi)
        
        ## Next Steps
        1. Monitor system health for the next hour
        2. Review logs for any anomalies
        3. Update incident documentation
        4. Plan fix for original issue
        EOF
        
        cat rollback-report.md
        
    - name: Upload rollback report
      uses: actions/upload-artifact@v4
      with:
        name: rollback-report-${{ github.run_id }}
        path: rollback-report.md
        retention-days: 90