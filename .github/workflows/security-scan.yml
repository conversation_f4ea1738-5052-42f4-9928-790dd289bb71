name: Security Scanning

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  dependency-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
          
      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pip-audit safety bandit
          
      - name: Python Dependency Security Audit
        run: |
          cd backend
          pip-audit --format=json --output=../python-audit.json --desc requirements.txt || true
          safety check --json --output=../python-safety.json || true
          
      - name: Node.js Dependency Security Audit
        run: |
          cd frontend
          npm audit --audit-level=moderate --json > ../npm-audit.json || true
          
      - name: Upload dependency scan results
        uses: actions/upload-artifact@v3
        with:
          name: dependency-scan-results
          path: |
            python-audit.json
            python-safety.json
            npm-audit.json

  static-analysis:
    name: Static Application Security Testing (SAST)
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          
      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install bandit semgrep
          
      - name: Run Bandit Security Linter
        run: |
          cd backend
          bandit -r . -f json -o ../bandit-results.json -ll || true
          bandit -r . -f txt -o ../bandit-results.txt -ll || true
          
      - name: Run Semgrep Security Analysis
        run: |
          semgrep --config=auto --json --output=semgrep-results.json backend/ || true
          
      - name: Upload SAST results
        uses: actions/upload-artifact@v3
        with:
          name: sast-results
          path: |
            bandit-results.json
            bandit-results.txt
            semgrep-results.json

  secrets-scan:
    name: Secret Detection
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full history for better secret detection
          
      - name: Install TruffleHog
        run: |
          curl -sSfL https://raw.githubusercontent.com/trufflesecurity/trufflehog/main/scripts/install.sh | sh -s -- -b /usr/local/bin
          
      - name: Run TruffleHog Secret Scan
        run: |
          trufflehog filesystem . --json --no-update > trufflehog-results.json || true
          
      - name: Install GitLeaks
        run: |
          wget -O gitleaks.tar.gz https://github.com/zricethezav/gitleaks/releases/download/v8.18.0/gitleaks_8.18.0_linux_x64.tar.gz
          tar -xzf gitleaks.tar.gz
          sudo mv gitleaks /usr/local/bin/
          
      - name: Run GitLeaks Secret Scan
        run: |
          gitleaks detect --report-format json --report-path gitleaks-results.json --source . || true
          
      - name: Upload secret scan results
        uses: actions/upload-artifact@v3
        with:
          name: secret-scan-results
          path: |
            trufflehog-results.json
            gitleaks-results.json

  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Build Docker Image
        run: |
          cd backend
          docker build -t ailex-backend:security-test .
          
      - name: Install Trivy
        run: |
          sudo apt-get update
          sudo apt-get install wget apt-transport-https gnupg lsb-release
          wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | sudo apt-key add -
          echo "deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -sc) main" | sudo tee -a /etc/apt/sources.list.d/trivy.list
          sudo apt-get update
          sudo apt-get install trivy
          
      - name: Run Trivy Container Scan
        run: |
          trivy image --format json --output trivy-results.json ailex-backend:security-test || true
          trivy image --format table ailex-backend:security-test || true
          
      - name: Upload container scan results
        uses: actions/upload-artifact@v3
        with:
          name: container-scan-results
          path: trivy-results.json

  infrastructure-scan:
    name: Infrastructure Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Install Checkov
        run: |
          pip install checkov
          
      - name: Run Checkov Infrastructure Scan
        run: |
          checkov -d . --framework dockerfile,github_actions --output json --output-file-path checkov-results.json || true
          checkov -d . --framework dockerfile,github_actions || true
          
      - name: Install Docker Bench Security
        run: |
          git clone https://github.com/docker/docker-bench-security.git
          
      - name: Upload infrastructure scan results
        uses: actions/upload-artifact@v3
        with:
          name: infrastructure-scan-results
          path: checkov-results.json

  license-compliance:
    name: License Compliance Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          
      - name: Install license scanners
        run: |
          pip install pip-licenses
          npm install -g license-checker
          
      - name: Check Python licenses
        run: |
          cd backend
          pip install -r requirements.txt
          pip-licenses --format=json --output-file=../python-licenses.json
          
      - name: Check Node.js licenses
        run: |
          cd frontend
          npm install
          license-checker --json --out ../npm-licenses.json
          
      - name: Upload license scan results
        uses: actions/upload-artifact@v3
        with:
          name: license-scan-results
          path: |
            python-licenses.json
            npm-licenses.json

  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [dependency-scan, static-analysis, secrets-scan, container-scan, infrastructure-scan, license-compliance]
    if: always()
    
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v3
        
      - name: Generate Security Summary
        run: |
          echo "# Security Scan Summary" > security-summary.md
          echo "Generated on: $(date)" >> security-summary.md
          echo "" >> security-summary.md
          
          echo "## Scan Results" >> security-summary.md
          echo "- Dependency Scan: $(ls dependency-scan-results/ 2>/dev/null | wc -l) files" >> security-summary.md
          echo "- Static Analysis: $(ls sast-results/ 2>/dev/null | wc -l) files" >> security-summary.md
          echo "- Secret Detection: $(ls secret-scan-results/ 2>/dev/null | wc -l) files" >> security-summary.md
          echo "- Container Scan: $(ls container-scan-results/ 2>/dev/null | wc -l) files" >> security-summary.md
          echo "- Infrastructure Scan: $(ls infrastructure-scan-results/ 2>/dev/null | wc -l) files" >> security-summary.md
          echo "- License Compliance: $(ls license-scan-results/ 2>/dev/null | wc -l) files" >> security-summary.md
          
      - name: Upload security summary
        uses: actions/upload-artifact@v3
        with:
          name: security-summary
          path: security-summary.md