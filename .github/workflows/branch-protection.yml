name: Branch Protection and Quality Gates

on:
  pull_request:
    branches: [main, develop]
    types: [opened, synchronize, reopened, ready_for_review]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'
  FORCE_COLOR: '1'
  CI: 'true'

jobs:
  # Quality gate checks for pull requests
  quality-gate-pr:
    name: PR Quality Gate
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    outputs:
      meets-requirements: ${{ steps.check.outputs.meets-requirements }}
      coverage-backend: ${{ steps.check.outputs.coverage-backend }}
      coverage-frontend: ${{ steps.check.outputs.coverage-frontend }}
      type-coverage: ${{ steps.check.outputs.type-coverage }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: Get changed files
      id: changed-files
      uses: tj-actions/changed-files@v41
      with:
        files: |
          backend/**/*.py
          frontend/**/*.{ts,tsx,js,jsx}
          .github/workflows/**
          
    - name: Check PR requirements
      id: check
      run: |
        echo "Checking PR quality gate requirements..."
        
        # Check PR title format
        PR_TITLE="${{ github.event.pull_request.title }}"
        if [[ ! "$PR_TITLE" =~ ^(feat|fix|docs|style|refactor|test|chore|ci|perf|build)(\(.+\))?: .+ ]]; then
          echo "::error::PR title must follow conventional commit format: type(scope): description"
          echo "meets-requirements=false" >> $GITHUB_OUTPUT
          exit 1
        fi
        
        # Check PR description
        PR_BODY="${{ github.event.pull_request.body }}"
        if [[ -z "$PR_BODY" ]] || [[ ${#PR_BODY} -lt 20 ]]; then
          echo "::error::PR description must be at least 20 characters long"
          echo "meets-requirements=false" >> $GITHUB_OUTPUT
          exit 1
        fi
        
        # Check if PR is not too large
        CHANGED_FILES_COUNT="${{ steps.changed-files.outputs.all_changed_files_count }}"
        if [[ $CHANGED_FILES_COUNT -gt 50 ]]; then
          echo "::warning::PR contains $CHANGED_FILES_COUNT changed files. Consider breaking it into smaller PRs."
        fi
        
        echo "meets-requirements=true" >> $GITHUB_OUTPUT
        echo "coverage-backend=pending" >> $GITHUB_OUTPUT
        echo "coverage-frontend=pending" >> $GITHUB_OUTPUT
        echo "type-coverage=pending" >> $GITHUB_OUTPUT
        
  # Run tests and get coverage
  test-coverage:
    name: Test Coverage Check
    runs-on: ubuntu-latest
    needs: [quality-gate-pr]
    if: needs.quality-gate-pr.outputs.meets-requirements == 'true'
    
    outputs:
      backend-coverage: ${{ steps.backend-coverage.outputs.coverage }}
      frontend-coverage: ${{ steps.frontend-coverage.outputs.coverage }}
      type-coverage: ${{ steps.type-coverage.outputs.coverage }}
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Install backend dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-cov
        
    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci
      
    - name: Run backend tests with coverage
      id: backend-coverage
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
        ENVIRONMENT: testing
        PYTHONPATH: ${{ github.workspace }}/backend
      run: |
        python -m pytest tests/ -v --cov=. --cov-report=term-missing --cov-report=json:coverage.json
        
        # Extract coverage percentage
        COVERAGE=$(python -c "import json; data=json.load(open('coverage.json')); print(f\"{data['totals']['percent_covered']:.1f}\")")
        echo "Backend coverage: $COVERAGE%"
        echo "coverage=$COVERAGE" >> $GITHUB_OUTPUT
        
        # Check threshold
        if (( $(echo "$COVERAGE < 80" | bc -l) )); then
          echo "::error::Backend test coverage ($COVERAGE%) is below required threshold (80%)"
          exit 1
        fi
        
    - name: Run frontend tests with coverage
      id: frontend-coverage
      working-directory: ./frontend
      run: |
        npm run test:ci
        
        # Extract coverage from summary
        if [ -f "coverage/coverage-summary.json" ]; then
          COVERAGE=$(node -p "require('./coverage/coverage-summary.json').total.lines.pct")
          echo "Frontend coverage: $COVERAGE%"
          echo "coverage=$COVERAGE" >> $GITHUB_OUTPUT
          
          # Check threshold
          if (( $(echo "$COVERAGE < 80" | bc -l) )); then
            echo "::error::Frontend test coverage ($COVERAGE%) is below required threshold (80%)"
            exit 1
          fi
        else
          echo "::error::Coverage summary not found"
          exit 1
        fi
        
    - name: Run type checking with coverage
      id: type-coverage
      working-directory: ./backend
      run: |
        mypy --html-report type-coverage-report --txt-report type-coverage-report . || true
        
        if [ -f "type-coverage-report/index.txt" ]; then
          TYPE_COVERAGE=$(grep "Total coverage:" type-coverage-report/index.txt | grep -o '[0-9]*%' | head -1 | tr -d '%' || echo "0")
          echo "Type coverage: $TYPE_COVERAGE%"
          echo "coverage=$TYPE_COVERAGE" >> $GITHUB_OUTPUT
          
          if [ "$TYPE_COVERAGE" -lt "90" ]; then
            echo "::error::Type coverage ($TYPE_COVERAGE%) is below required threshold (90%)"
            exit 1
          fi
        else
          echo "::warning::Type coverage report not found"
          echo "coverage=0" >> $GITHUB_OUTPUT
        fi
  
  # Security checks for PR
  security-check-pr:
    name: PR Security Check
    runs-on: ubuntu-latest
    needs: [quality-gate-pr]
    if: needs.quality-gate-pr.outputs.meets-requirements == 'true'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy security scan
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        severity: 'CRITICAL,HIGH'
        exit-code: '1'
        
    - name: Upload security scan results
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
  
  # Final quality gate
  final-quality-gate:
    name: Final Quality Gate
    runs-on: ubuntu-latest
    needs: [quality-gate-pr, test-coverage, security-check-pr]
    if: always()
    
    steps:
    - name: Check all requirements
      run: |
        echo "Checking final quality gate..."
        
        # Check if all previous jobs passed
        if [[ "${{ needs.quality-gate-pr.result }}" != "success" ]]; then
          echo "::error::PR quality gate failed"
          exit 1
        fi
        
        if [[ "${{ needs.test-coverage.result }}" != "success" ]]; then
          echo "::error::Test coverage requirements not met"
          exit 1
        fi
        
        if [[ "${{ needs.security-check-pr.result }}" != "success" ]]; then
          echo "::error::Security checks failed"
          exit 1
        fi
        
        echo "✅ All quality gate requirements met!"
        echo "- Backend coverage: ${{ needs.test-coverage.outputs.backend-coverage }}%"
        echo "- Frontend coverage: ${{ needs.test-coverage.outputs.frontend-coverage }}%"
        echo "- Type coverage: ${{ needs.test-coverage.outputs.type-coverage }}%"
        
    - name: Add PR comment with results
      uses: actions/github-script@v7
      if: always()
      with:
        script: |
          const backendCoverage = '${{ needs.test-coverage.outputs.backend-coverage }}';
          const frontendCoverage = '${{ needs.test-coverage.outputs.frontend-coverage }}';
          const typeCoverage = '${{ needs.test-coverage.outputs.type-coverage }}';
          
          const status = '${{ needs.quality-gate-pr.result }}' === 'success' &&
                        '${{ needs.test-coverage.result }}' === 'success' &&
                        '${{ needs.security-check-pr.result }}' === 'success' ? '✅ PASSED' : '❌ FAILED';
          
          const body = `## Quality Gate Results
          
          **Status:** ${status}
          
          ### Coverage Results
          - 🐍 Backend Coverage: **${backendCoverage}%** (Required: ≥80%)
          - ⚛️ Frontend Coverage: **${frontendCoverage}%** (Required: ≥80%)
          - 🔧 Type Coverage: **${typeCoverage}%** (Required: ≥90%)
          
          ### Checks
          - ${'${{ needs.quality-gate-pr.result }}' === 'success' ? '✅' : '❌'} PR Format & Description
          - ${'${{ needs.test-coverage.result }}' === 'success' ? '✅' : '❌'} Test Coverage
          - ${'${{ needs.security-check-pr.result }}' === 'success' ? '✅' : '❌'} Security Scan
          
          ${status === '✅ PASSED' ? 
            '🎉 This PR meets all quality requirements and is ready for review!' : 
            '⚠️ This PR does not meet quality requirements. Please address the issues above.'}
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: body
          });