name: Run Tests

on:
  push:
    branches: [main, develop, feature/*]
  pull_request:
    branches: [main, develop]
  merge_group:

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'
  FORCE_COLOR: '1'
  CI: 'true'

jobs:
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    strategy:
      fail-fast: false
      matrix:
        python-version: ['3.10', '3.11']
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >--
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >--
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.cache/pip
          ~/.local/lib/python${{ matrix.python-version }}/site-packages
        key: ${{ runner.os }}-pip-${{ matrix.python-version }}-${{ hashFiles('backend/requirements.txt', 'backend/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-pip-${{ matrix.python-version }}-
          ${{ runner.os }}-pip-

    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip wheel setuptools
        pip install -r requirements.txt
        pip install pytest-cov pytest-xdist pytest-timeout pytest-mock

    - name: Run linting and type checking
      working-directory: ./backend
      run: |
        echo "::group::Code formatting check"
        black --check --diff .
        echo "::endgroup::"
        
        echo "::group::Import sorting check"
        isort --check-only --diff .
        echo "::endgroup::"
        
        echo "::group::Flake8 linting"
        flake8 . --count --show-source --statistics
        echo "::endgroup::"
        
    - name: Run type checking with MyPy
      working-directory: ./backend
      run: |
        mypy . --junit-xml mypy-results.xml || true
        
    - name: Upload MyPy results
      uses: dorny/test-reporter@v1
      if: always()
      with:
        name: MyPy Type Check Results
        path: './backend/mypy-results.xml'
        reporter: java-junit
        fail-on-error: false

    - name: Run unit tests
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
        ENVIRONMENT: testing
        PYTHONPATH: ${{ github.workspace }}/backend
        COVERAGE_FILE: .coverage.unit
      run: |
        python -m pytest tests/unit/ -v \
          --cov=. \
          --cov-report=xml:coverage-unit.xml \
          --cov-report=term-missing \
          --cov-report=html:htmlcov-unit \
          --timeout=300 \
          -n auto \
          --tb=short

    - name: Run integration tests
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
        ENVIRONMENT: testing
        PYTHONPATH: ${{ github.workspace }}/backend
        COVERAGE_FILE: .coverage.integration
      run: |
        python -m pytest tests/integration/ -v \
          --cov=. \
          --cov-report=xml:coverage-integration.xml \
          --cov-report=term-missing \
          --timeout=600 \
          --tb=short

    - name: Combine coverage reports
      working-directory: ./backend
      run: |
        coverage combine .coverage.unit .coverage.integration || true
        coverage xml -o coverage-combined.xml
        coverage report --show-missing
        
    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v4
      with:
        files: ./backend/coverage-unit.xml,./backend/coverage-integration.xml,./backend/coverage-combined.xml
        flags: backend
        name: backend-coverage-${{ matrix.python-version }}
        fail_ci_if_error: false
        verbose: true
        
    - name: Upload coverage HTML reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: coverage-html-${{ matrix.python-version }}
        path: backend/htmlcov-*
        retention-days: 7

  type-checking:
    name: Type Checking and Coverage
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ env.PYTHON_VERSION }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache pip dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.cache/pip
          ~/.local/lib/python${{ env.PYTHON_VERSION }}/site-packages
        key: ${{ runner.os }}-pip-typecheck-${{ env.PYTHON_VERSION }}-${{ hashFiles('backend/requirements.txt', 'backend/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-pip-typecheck-${{ env.PYTHON_VERSION }}-
          ${{ runner.os }}-pip-typecheck-
          ${{ runner.os }}-pip-

    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Run comprehensive type checking
      working-directory: ./backend
      run: |
        echo "::group::Type Coverage Report"
        mypy --html-report type-coverage-report --txt-report type-coverage-report . || true
        echo "::endgroup::"
        
        echo "::group::Detailed Type Analysis"
        mypy --show-error-context --show-column-numbers --show-error-codes . || true
        echo "::endgroup::"
        
        echo "::group::Type Coverage Summary"
        if [ -f "type-coverage-report/index.txt" ]; then
          echo "Type coverage summary:"
          cat type-coverage-report/index.txt
        fi
        echo "::endgroup::"

    - name: Upload type coverage report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: type-coverage-report
        path: backend/type-coverage-report/
        retention-days: 30
        
    - name: Check type coverage threshold
      working-directory: ./backend
      run: |
        if [ -f "type-coverage-report/index.txt" ]; then
          TYPE_COVERAGE=$(grep "Total coverage:" type-coverage-report/index.txt | grep -o '[0-9]*%' | head -1 | tr -d '%' || echo "0")
          echo "Type coverage: $TYPE_COVERAGE%"
          if [ "$TYPE_COVERAGE" -lt "90" ]; then
            echo "::error::Type coverage ($TYPE_COVERAGE%) is below required threshold (90%)"
            exit 1
          fi
          echo "::notice::Type coverage ($TYPE_COVERAGE%) meets threshold requirement"
        else
          echo "::warning::Type coverage report not found"
        fi

    - name: Comment PR with type coverage
      uses: actions/github-script@v6
      if: github.event_name == 'pull_request' && always()
      with:
        script: |
          const fs = require('fs');
          const path = './backend/type-coverage-report/index.txt';
          
          if (fs.existsSync(path)) {
            const typeReport = fs.readFileSync(path, 'utf8');
            const body = `## 🔍 Type Coverage Report
            
            \`\`\`
            ${typeReport}
            \`\`\`
            
            📊 [View detailed type coverage report](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: body
            });
          }

  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        node-version: ['16', '18', '20']

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Cache node modules
      uses: actions/cache@v4
      with:
        path: |
          ~/.npm
          frontend/node_modules
          frontend/.next/cache
        key: ${{ runner.os }}-node-${{ matrix.node-version }}-${{ hashFiles('frontend/package-lock.json') }}
        restore-keys: |
          ${{ runner.os }}-node-${{ matrix.node-version }}-
          ${{ runner.os }}-node-

    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci

    - name: Run ESLint
      working-directory: ./frontend
      run: npm run lint

    - name: Run type checking
      working-directory: ./frontend
      run: npm run type-check

    - name: Run tests
      working-directory: ./frontend
      run: npm run test:ci

    - name: Upload test coverage
      uses: codecov/codecov-action@v4
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage-${{ matrix.node-version }}
        fail_ci_if_error: false
        verbose: true
        
    - name: Check test coverage threshold
      working-directory: ./frontend
      run: |
        if [ -f "coverage/coverage-summary.json" ]; then
          COVERAGE=$(node -p "require('./coverage/coverage-summary.json').total.lines.pct")
          echo "Test coverage: $COVERAGE%"
          if (( $(echo "$COVERAGE < 80" | bc -l) )); then
            echo "::error::Test coverage ($COVERAGE%) is below required threshold (80%)"
            exit 1
          fi
          echo "::notice::Test coverage ($COVERAGE%) meets threshold requirement"
        else
          echo "::warning::Coverage summary not found"
        fi

    - name: Build application
      working-directory: ./frontend
      env:
        NODE_ENV: production
      run: npm run build

    - name: Check bundle size
      working-directory: ./frontend
      env:
        ANALYZE: true
      run: |
        npm run analyze
        
    - name: Upload bundle analysis
      uses: actions/upload-artifact@v4
      if: matrix.node-version == '18'
      with:
        name: bundle-analysis
        path: frontend/.next/analyze/
        retention-days: 7

  docker-build-test:
    name: Docker Build Test
    runs-on: ubuntu-latest
    needs: [backend-tests]
    if: github.event_name == 'pull_request' || github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Cache Docker layers
      uses: actions/cache@v4
      with:
        path: /tmp/.buildx-cache
        key: ${{ runner.os }}-buildx-${{ github.sha }}
        restore-keys: |
          ${{ runner.os }}-buildx-

    - name: Build Docker image
      working-directory: ./backend
      run: |
        docker buildx build \
          --cache-from type=local,src=/tmp/.buildx-cache \
          --cache-to type=local,dest=/tmp/.buildx-cache-new,mode=max \
          --load \
          -t ailex-backend:test \
          --target production \
          .
          
    - name: Security scan Docker image
      run: |
        docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
          -v $PWD:/tmp/.cache/ \
          aquasec/trivy:latest image \
          --severity HIGH,CRITICAL \
          --exit-code 1 \
          ailex-backend:test

    - name: Test Docker image
      run: |
        # Start container with health check
        docker run --rm -d --name test-container -p 8000:8000 \
          -e ENVIRONMENT=testing \
          -e DATABASE_URL=sqlite:///test.db \
          -e REDIS_URL=redis://localhost:6379/0 \
          -e LOG_LEVEL=INFO \
          --health-cmd="curl -f http://localhost:8000/api/v1/health/liveness || exit 1" \
          --health-interval=10s \
          --health-timeout=5s \
          --health-retries=3 \
          ailex-backend:test
        
        # Wait for health check to pass
        timeout 60s bash -c 'until docker inspect --format="{{json .State.Health.Status}}" test-container | grep -q "healthy"; do sleep 2; done'
        
        # Test endpoints
        curl -f http://localhost:8000/api/v1/health/liveness
        curl -f http://localhost:8000/api/v1/health/readiness
        
        # Check logs for errors
        docker logs test-container
        
        # Cleanup
        docker stop test-container
        
    - name: Move cache
      run: |
        rm -rf /tmp/.buildx-cache
        mv /tmp/.buildx-cache-new /tmp/.buildx-cache

  security-checks:
    name: Security Checks
    runs-on: ubuntu-latest
    continue-on-error: false

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Run Bandit security linter
      working-directory: ./backend
      run: |
        pip install bandit
        echo "::group::Bandit Security Scan"
        bandit -r . -f json -o bandit-report.json
        bandit -r . -ll  # Show only medium and high severity issues
        echo "::endgroup::"

    - name: Run Safety check
      working-directory: ./backend
      run: |
        pip install safety
        echo "::group::Safety Dependency Check"
        safety check --json --output safety-report.json
        safety check  # Show readable output
        echo "::endgroup::"

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: './backend'
        format: 'sarif'
        output: 'trivy-results.sarif'
        severity: 'CRITICAL,HIGH'
        exit-code: '1'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
        
    - name: Upload security reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: security-reports
        path: |
          backend/bandit-report.json
          backend/safety-report.json
        retention-days: 30

  # Quality Gate Job
  quality-gate:
    name: Quality Gate
    runs-on: ubuntu-latest
    needs: [backend-tests, type-checking, frontend-tests, security-checks]
    if: always()
    
    steps:
    - name: Check test results
      run: |
        echo "Checking quality gate requirements..."
        
        # Check if all required jobs passed
        if [[ "${{ needs.backend-tests.result }}" != "success" ]]; then
          echo "::error::Backend tests failed"
          exit 1
        fi
        
        if [[ "${{ needs.type-checking.result }}" != "success" ]]; then
          echo "::error::Type checking failed"
          exit 1
        fi
        
        if [[ "${{ needs.frontend-tests.result }}" != "success" ]]; then
          echo "::error::Frontend tests failed"
          exit 1
        fi
        
        if [[ "${{ needs.security-checks.result }}" != "success" ]]; then
          echo "::error::Security checks failed"
          exit 1
        fi
        
        echo "::notice::All quality gate requirements met! ✅"
        
    - name: Generate quality report
      run: |
        cat << EOF > quality-report.md
        # Quality Gate Report
        
        **Date:** $(date -u +"%Y-%m-%d %H:%M:%S UTC")
        **Commit:** ${{ github.sha }}
        **Branch:** ${{ github.ref_name }}
        
        ## Results
        - ✅ Backend Tests: ${{ needs.backend-tests.result }}
        - ✅ Type Checking: ${{ needs.type-checking.result }}
        - ✅ Frontend Tests: ${{ needs.frontend-tests.result }}
        - ✅ Security Checks: ${{ needs.security-checks.result }}
        
        ## Requirements Met
        - Test coverage ≥ 80%
        - Type coverage ≥ 90%
        - No critical security vulnerabilities
        - All linting checks passed
        
        **Status: PASSED** 🎉
        EOF
        
        cat quality-report.md
        
    - name: Upload quality report
      uses: actions/upload-artifact@v4
      with:
        name: quality-gate-report
        path: quality-report.md
        retention-days: 30