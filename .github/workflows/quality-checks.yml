name: Quality Checks (Cost-Effective)

on:
  push:
    branches: [main, develop, feature/*]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '20'
  PYTHON_VERSION: '3.11'
  FORCE_COLOR: '1'
  CI: 'true'

# Cost-effective CI/CD focusing on types and essential quality
jobs:
  # Fast type checking and linting (runs on every push/PR)
  quick-checks:
    name: Type Checking & Linting
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    # Backend: uv for fast Python setup
    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"
    
    - name: Set up Python with uv
      run: |
        cd backend
        uv python install ${{ env.PYTHON_VERSION }}
        uv sync --dev --frozen
    
    - name: Backend Type Checking (MyPy)
      run: |
        cd backend
        uv run mypy . --strict --ignore-missing-imports --show-error-codes
    
    - name: Backend Linting (Ruff - faster than flake8)
      run: |
        cd backend
        uv run ruff check . --output-format=github
        uv run ruff format --check .
    
    # Frontend: pnpm for fast Node.js setup
    - name: Setup Node.js with pnpm cache
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'pnpm'
        cache-dependency-path: frontend/pnpm-lock.yaml
    
    - name: Install pnpm
      run: npm install -g pnpm
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        pnpm install --frozen-lockfile
    
    - name: Frontend Type Checking (TypeScript)
      run: |
        cd frontend
        pnpm run type-check
    
    - name: Frontend Linting (ESLint)
      run: |
        cd frontend
        pnpm run lint

  # Security checks (runs on main/develop only to save minutes)
  security-checks:
    name: Security Scanning
    runs-on: ubuntu-latest
    timeout-minutes: 15
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"
    
    - name: Set up Python with uv
      run: |
        cd backend
        uv python install ${{ env.PYTHON_VERSION }}
        uv sync --dev --frozen
    
    - name: Backend Security Scan (Bandit)
      run: |
        cd backend
        uv run bandit -r . -f json -o bandit-report.json || true
        uv run bandit -r . -f txt
    
    - name: Python Dependency Security (Safety)
      run: |
        cd backend
        uv run safety check --json --output safety-report.json || true
        uv run safety check
    
    - name: Upload security reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: security-reports
        path: |
          backend/bandit-report.json
          backend/safety-report.json
        retention-days: 30

  # Essential tests (runs on main/develop and PRs to main)
  essential-tests:
    name: Essential Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop' || github.base_ref == 'main'
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"
    
    - name: Set up Python with uv
      run: |
        cd backend
        uv python install ${{ env.PYTHON_VERSION }}
        uv sync --dev --frozen
    
    - name: Backend Unit Tests
      run: |
        cd backend
        uv run pytest tests/unit/ -v --tb=short --maxfail=5
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
    
    - name: Setup Node.js with pnpm cache
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'pnpm'
        cache-dependency-path: frontend/pnpm-lock.yaml
    
    - name: Install pnpm
      run: npm install -g pnpm
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        pnpm install --frozen-lockfile
    
    - name: Frontend Unit Tests
      run: |
        cd frontend
        pnpm run test:unit --passWithNoTests

  # Full test suite (runs only on main branch to save minutes)
  full-tests:
    name: Full Test Suite
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: github.ref == 'refs/heads/main'
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"
    
    - name: Set up Python with uv
      run: |
        cd backend
        uv python install ${{ env.PYTHON_VERSION }}
        uv sync --dev --frozen
    
    - name: Backend Full Test Suite with Coverage
      run: |
        cd backend
        uv run pytest tests/ -v --cov=. --cov-report=xml --cov-report=html --tb=short
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
    
    - name: Setup Node.js with pnpm cache
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'pnpm'
        cache-dependency-path: frontend/pnpm-lock.yaml
    
    - name: Install pnpm
      run: npm install -g pnpm
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        pnpm install --frozen-lockfile
    
    - name: Frontend Full Test Suite with Coverage
      run: |
        cd frontend
        pnpm run test:coverage
    
    - name: Upload coverage reports
      uses: actions/upload-artifact@v4
      with:
        name: coverage-reports
        path: |
          backend/coverage.xml
          backend/htmlcov/
          frontend/coverage/
        retention-days: 30

  # Type coverage check (runs on main only)
  type-coverage:
    name: Type Coverage Report
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"
    
    - name: Set up Python with uv
      run: |
        cd backend
        uv python install ${{ env.PYTHON_VERSION }}
        uv sync --dev --frozen
    
    - name: Generate Type Coverage Report
      run: |
        cd backend
        uv run mypy . --html-report type-coverage-report --strict --ignore-missing-imports
    
    - name: Upload type coverage report
      uses: actions/upload-artifact@v4
      with:
        name: type-coverage-report
        path: backend/type-coverage-report/
        retention-days: 30
