name: Deploy AiLex Ad Agent System

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      skip_tests:
        description: 'Skip test execution'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'
  FORCE_COLOR: '1'
  CI: 'true'

jobs:
  # Pre-deployment validation
  validate-deployment:
    name: Validate Deployment
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'
    outputs:
      should-deploy-production: ${{ steps.check.outputs.should-deploy-production }}
      should-deploy-staging: ${{ steps.check.outputs.should-deploy-staging }}
      environment: ${{ steps.check.outputs.environment }}
    
    steps:
    - name: Check deployment conditions
      id: check
      run: |
        if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
          echo "should-deploy-production=${{ github.event.inputs.environment == 'production' }}" >> $GITHUB_OUTPUT
          echo "should-deploy-staging=${{ github.event.inputs.environment == 'staging' }}" >> $GITHUB_OUTPUT
          echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
        elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
          echo "should-deploy-production=true" >> $GITHUB_OUTPUT
          echo "should-deploy-staging=false" >> $GITHUB_OUTPUT
          echo "environment=production" >> $GITHUB_OUTPUT
        elif [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
          echo "should-deploy-production=false" >> $GITHUB_OUTPUT
          echo "should-deploy-staging=true" >> $GITHUB_OUTPUT
          echo "environment=staging" >> $GITHUB_OUTPUT
        else
          echo "should-deploy-production=false" >> $GITHUB_OUTPUT
          echo "should-deploy-staging=false" >> $GITHUB_OUTPUT
          echo "environment=none" >> $GITHUB_OUTPUT
        fi

  # Test and lint jobs
  test-backend:
    name: Test Backend
    runs-on: ubuntu-latest
    if: github.event.inputs.skip_tests != 'true'
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >--
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >--
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache pip dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.cache/pip
          ~/.local/lib/python${{ env.PYTHON_VERSION }}/site-packages
        key: ${{ runner.os }}-deploy-pip-${{ env.PYTHON_VERSION }}-${{ hashFiles('backend/requirements.txt', 'backend/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-deploy-pip-${{ env.PYTHON_VERSION }}-
          ${{ runner.os }}-deploy-pip-
          ${{ runner.os }}-pip-

    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Run tests
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
        ENVIRONMENT: testing
      run: |
        python -m pytest tests/ -v --cov=. --cov-report=xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage

  test-frontend:
    name: Test Frontend
    runs-on: ubuntu-latest
    if: github.event.inputs.skip_tests != 'true'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci

    - name: Run type check
      working-directory: ./frontend
      run: npm run type-check

    - name: Run linting
      working-directory: ./frontend
      run: npm run lint

    - name: Build application
      working-directory: ./frontend
      run: npm run build

  # Security scanning
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    if: github.event.inputs.skip_tests != 'true'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        severity: 'CRITICAL,HIGH'
        exit-code: '1'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Build and deploy backend to Fly.io
  deploy-backend:
    name: Deploy Backend to Fly.io
    runs-on: ubuntu-latest
    needs: [validate-deployment, test-backend, security-scan]
    if: |
      (needs.validate-deployment.outputs.should-deploy-production == 'true' ||
       needs.validate-deployment.outputs.should-deploy-staging == 'true') &&
      (github.event.inputs.skip_tests == 'true' ||
       (needs.test-backend.result == 'success' && needs.security-scan.result == 'success'))
    
    environment:
      name: ${{ needs.validate-deployment.outputs.environment }}
      url: ${{ steps.deploy.outputs.url }}
    
    outputs:
      deployment-url: ${{ steps.deploy.outputs.url }}
      deployment-id: ${{ steps.deploy.outputs.deployment-id }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master
      
    - name: Set deployment variables
      id: vars
      run: |
        if [[ "${{ needs.validate-deployment.outputs.environment }}" == "production" ]]; then
          echo "app-name=ailex-ad-agent-backend" >> $GITHUB_OUTPUT
          echo "config-file=fly.toml" >> $GITHUB_OUTPUT
          echo "health-url=https://ailex-ad-agent-backend.fly.dev" >> $GITHUB_OUTPUT
        else
          echo "app-name=ailex-ad-agent-backend-staging" >> $GITHUB_OUTPUT
          echo "config-file=fly.staging.toml" >> $GITHUB_OUTPUT
          echo "health-url=https://ailex-ad-agent-backend-staging.fly.dev" >> $GITHUB_OUTPUT
        fi
        
    - name: Create staging config if needed
      working-directory: ./backend
      if: needs.validate-deployment.outputs.environment == 'staging'
      run: |
        if [ ! -f "fly.staging.toml" ]; then
          cp fly.toml fly.staging.toml
          sed -i 's/ailex-ad-agent-backend/ailex-ad-agent-backend-staging/g' fly.staging.toml
        fi
        
    - name: Pre-deployment health check
      run: |
        echo "Checking current deployment health..."
        curl -f "${{ steps.vars.outputs.health-url }}/api/v1/health/liveness" || {
          echo "::warning::Current deployment not responding, proceeding with deployment"
        }
        
    - name: Deploy to Fly.io with zero-downtime
      id: deploy
      working-directory: ./backend
      run: |
        echo "Starting deployment to ${{ steps.vars.outputs.app-name }}"
        
        # Deploy with rolling strategy for zero-downtime
        flyctl deploy \
          --config ${{ steps.vars.outputs.config-file }} \
          --app ${{ steps.vars.outputs.app-name }} \
          --remote-only \
          --strategy rolling \
          --wait-timeout 300 \
          --verbose
        
        # Get deployment info
        DEPLOYMENT_ID=$(flyctl status --app ${{ steps.vars.outputs.app-name }} --json | jq -r '.id')
        echo "deployment-id=$DEPLOYMENT_ID" >> $GITHUB_OUTPUT
        echo "url=${{ steps.vars.outputs.health-url }}" >> $GITHUB_OUTPUT
        
        echo "Deployment completed with ID: $DEPLOYMENT_ID"
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
        
    - name: Wait for deployment stabilization
      run: |
        echo "Waiting for deployment to stabilize..."
        sleep 30
        
    - name: Comprehensive post-deployment health checks
      run: |
        echo "Running comprehensive health checks..."
        BASE_URL="${{ steps.vars.outputs.health-url }}"
        
        # Test liveness endpoint
        echo "Testing liveness endpoint..."
        for i in {1..5}; do
          if curl -f --max-time 10 "$BASE_URL/api/v1/health/liveness"; then
            echo "✅ Liveness check passed (attempt $i)"
            break
          elif [ $i -eq 5 ]; then
            echo "❌ Liveness check failed after 5 attempts"
            exit 1
          else
            echo "Retrying liveness check in 10s... (attempt $i/5)"
            sleep 10
          fi
        done
        
        # Test readiness endpoint
        echo "Testing readiness endpoint..."
        for i in {1..5}; do
          if curl -f --max-time 10 "$BASE_URL/api/v1/health/readiness"; then
            echo "✅ Readiness check passed (attempt $i)"
            break
          elif [ $i -eq 5 ]; then
            echo "❌ Readiness check failed after 5 attempts"
            exit 1
          else
            echo "Retrying readiness check in 10s... (attempt $i/5)"
            sleep 10
          fi
        done
        
        # Test API endpoints
        echo "Testing API endpoints..."
        if curl -f --max-time 10 "$BASE_URL/api/v1/health"; then
          echo "✅ General health endpoint accessible"
        else
          echo "⚠️  General health endpoint not responding"
        fi
        
        echo "🎉 All health checks completed successfully!"
        
    - name: Rollback on failure
      if: failure()
      working-directory: ./backend
      run: |
        echo "Deployment failed, attempting rollback..."
        
        # Get previous deployment
        PREVIOUS_DEPLOYMENT=$(flyctl releases list --app ${{ steps.vars.outputs.app-name }} --json | jq -r '.[1].id' 2>/dev/null)
        
        if [ "$PREVIOUS_DEPLOYMENT" != "null" ] && [ "$PREVIOUS_DEPLOYMENT" != "" ]; then
          echo "Rolling back to deployment: $PREVIOUS_DEPLOYMENT"
          flyctl releases rollback "$PREVIOUS_DEPLOYMENT" --app ${{ steps.vars.outputs.app-name }}
          
          # Wait for rollback to complete
          sleep 30
          
          # Test rollback
          if curl -f "${{ steps.vars.outputs.health-url }}/api/v1/health/liveness"; then
            echo "✅ Rollback successful"
          else
            echo "❌ Rollback failed - manual intervention required"
            exit 1
          fi
        else
          echo "No previous deployment found for rollback"
          exit 1
        fi
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

  # Deploy frontend to Vercel
  deploy-frontend:
    name: Deploy Frontend to Vercel
    runs-on: ubuntu-latest
    needs: [validate-deployment, test-frontend, deploy-backend]
    if: |
      (needs.validate-deployment.outputs.should-deploy-production == 'true' ||
       needs.validate-deployment.outputs.should-deploy-staging == 'true') &&
      needs.deploy-backend.result == 'success' &&
      (github.event.inputs.skip_tests == 'true' || needs.test-frontend.result == 'success')
    
    environment:
      name: frontend-${{ needs.validate-deployment.outputs.environment }}
      url: ${{ steps.deploy.outputs.preview-url }}
    
    outputs:
      deployment-url: ${{ steps.deploy.outputs.preview-url }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
      
    - name: Set environment variables
      working-directory: ./frontend
      run: |
        # Create environment-specific variables
        if [[ "${{ needs.validate-deployment.outputs.environment }}" == "production" ]]; then
          echo "NEXT_PUBLIC_API_URL=${{ needs.deploy-backend.outputs.deployment-url }}/api/v1" >> .env.production.local
          echo "NEXT_PUBLIC_ENVIRONMENT=production" >> .env.production.local
        else
          echo "NEXT_PUBLIC_API_URL=${{ needs.deploy-backend.outputs.deployment-url }}/api/v1" >> .env.local
          echo "NEXT_PUBLIC_ENVIRONMENT=staging" >> .env.local
        fi
        
    - name: Build application
      working-directory: ./frontend
      run: |
        echo "Building frontend for ${{ needs.validate-deployment.outputs.environment }}..."
        npm run build
        
    - name: Run build validation
      working-directory: ./frontend
      run: |
        echo "Validating build output..."
        
        # Check if build output exists
        if [ ! -d ".next" ]; then
          echo "::error::Build output directory not found"
          exit 1
        fi
        
        # Check for critical files
        if [ ! -f ".next/BUILD_ID" ]; then
          echo "::error::BUILD_ID file not found"
          exit 1
        fi
        
        echo "Build validation completed successfully"

    - name: Deploy to Vercel
      id: deploy
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        working-directory: ./frontend
        vercel-args: ${{ needs.validate-deployment.outputs.environment == 'production' && '--prod' || '' }}
        alias-domains: |
          ${{ needs.validate-deployment.outputs.environment == 'production' && 
              'ailex.yourdomain.com' || 
              'staging.ailex.yourdomain.com' }}
              
    - name: Wait for deployment propagation
      run: |
        echo "Waiting for deployment to propagate globally..."
        sleep 20
        
    - name: Test frontend deployment
      run: |
        echo "Testing frontend deployment..."
        FRONTEND_URL="${{ steps.deploy.outputs.preview-url }}"
        
        # Test homepage
        echo "Testing homepage..."
        for i in {1..5}; do
          if curl -f --max-time 15 -L "$FRONTEND_URL"; then
            echo "✅ Homepage accessible (attempt $i)"
            break
          elif [ $i -eq 5 ]; then
            echo "❌ Homepage not accessible after 5 attempts"
            exit 1
          else
            echo "Retrying homepage test in 10s... (attempt $i/5)"
            sleep 10
          fi
        done
        
        # Test API integration
        echo "Testing API integration..."
        if curl -f --max-time 15 -L "$FRONTEND_URL/api/health"; then
          echo "✅ API integration working"
        else
          echo "⚠️  API integration test failed (non-critical)"
        fi
        
        echo "🎉 Frontend deployment tests completed!"

  # Integration tests on deployed environment
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [deploy-backend, deploy-frontend]
    if: success()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install test dependencies
      run: |
        pip install requests pytest pytest-asyncio
        
    - name: Run integration tests
      env:
        BACKEND_URL: ${{ needs.deploy-backend.outputs.deployment-url }}
        FRONTEND_URL: ${{ needs.deploy-frontend.outputs.deployment-url }}
      run: |
        echo "Running integration tests against deployed environment..."
        
        # Test backend-frontend integration
        python -c "
        import requests
        import time
        import sys
        
        backend_url = '${{ needs.deploy-backend.outputs.deployment-url }}'
        frontend_url = '${{ needs.deploy-frontend.outputs.deployment-url }}'
        
        print('Testing backend health...')
        response = requests.get(f'{backend_url}/api/v1/health/liveness', timeout=10)
        assert response.status_code == 200, f'Backend health check failed: {response.status_code}'
        
        print('Testing frontend accessibility...')
        response = requests.get(frontend_url, timeout=10)
        assert response.status_code == 200, f'Frontend not accessible: {response.status_code}'
        
        print('Integration tests passed!')
        "
        
    - name: Performance baseline test
      env:
        BACKEND_URL: ${{ needs.deploy-backend.outputs.deployment-url }}
        FRONTEND_URL: ${{ needs.deploy-frontend.outputs.deployment-url }}
      run: |
        echo "Running performance baseline tests..."
        
        # Simple performance check
        python -c "
        import requests
        import time
        
        backend_url = '${{ needs.deploy-backend.outputs.deployment-url }}'
        
        # Test response time
        start_time = time.time()
        response = requests.get(f'{backend_url}/api/v1/health/liveness')
        response_time = time.time() - start_time
        
        print(f'Backend response time: {response_time:.2f}s')
        
        if response_time > 5.0:
            print('Warning: Response time is higher than expected')
        else:
            print('Response time within acceptable range')
        "

  # Notification and monitoring setup
  post-deployment:
    name: Post-Deployment Tasks
    runs-on: ubuntu-latest
    needs: [validate-deployment, deploy-backend, deploy-frontend, integration-tests]
    if: always()
    
    steps:
    - name: Generate deployment report
      run: |
        cat << EOF > deployment-report.md
        # Deployment Report
        
        **Date:** $(date -u +"%Y-%m-%d %H:%M:%S UTC")
        **Environment:** ${{ needs.validate-deployment.outputs.environment || 'unknown' }}
        **Commit:** ${{ github.sha }}
        **Branch:** ${{ github.ref_name }}
        **Triggered by:** ${{ github.event_name }}
        
        ## Deployment Results
        - Backend: ${{ needs.deploy-backend.result }} 
        - Frontend: ${{ needs.deploy-frontend.result }}
        - Integration Tests: ${{ needs.integration-tests.result }}
        
        ## URLs
        - Backend: ${{ needs.deploy-backend.outputs.deployment-url }}
        - Frontend: ${{ needs.deploy-frontend.outputs.deployment-url }}
        
        ## Status
        $(if [[ "${{ needs.deploy-backend.result }}" == "success" && "${{ needs.deploy-frontend.result }}" == "success" ]]; then
            echo "**Status: SUCCESS** 🎉"
        else
            echo "**Status: FAILED** ❌"
        fi)
        EOF
        
        cat deployment-report.md
        
    - name: Upload deployment report
      uses: actions/upload-artifact@v4
      with:
        name: deployment-report-${{ github.run_id }}
        path: deployment-report.md
        retention-days: 30
        
    - name: Notify on success
      if: needs.deploy-backend.result == 'success' && needs.deploy-frontend.result == 'success'
      run: |
        echo "✅ Deployment successful!"
        echo "Backend: ${{ needs.deploy-backend.outputs.deployment-url }}"
        echo "Frontend: ${{ needs.deploy-frontend.outputs.deployment-url }}"
        # Add Slack/Discord notification here if needed
        
    - name: Notify on failure
      if: needs.deploy-backend.result == 'failure' || needs.deploy-frontend.result == 'failure'
      run: |
        echo "❌ Deployment failed!"
        echo "Backend: ${{ needs.deploy-backend.result }}"
        echo "Frontend: ${{ needs.deploy-frontend.result }}"
        # Add Slack/Discord notification here if needed
        
        # Create GitHub issue on production deployment failure
        if [[ "${{ needs.validate-deployment.outputs.environment }}" == "production" ]]; then
          echo "::error::Production deployment failed - manual intervention required"
        fi