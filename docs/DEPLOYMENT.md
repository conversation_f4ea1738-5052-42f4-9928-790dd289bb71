# AiLex Ad Agent System - Deployment Guide

This document provides comprehensive instructions for deploying the AiLex Ad Agent System to production and staging environments.

## Architecture Overview

- **Backend**: FastAPI application deployed on Fly.io
- **Frontend**: Next.js application deployed on Vercel
- **Database**: PostgreSQL on Fly.io
- **Cache/Queue**: Redis on Fly.io
- **Monitoring**: Prometheus, Grafana, Loki stack
- **CI/CD**: GitHub Actions

## Prerequisites

### Required Tools

1. **Fly.io CLI**
   ```bash
   curl -L https://fly.io/install.sh | sh
   flyctl auth login
   ```

2. **Vercel CLI**
   ```bash
   npm i -g vercel
   vercel login
   ```

3. **Docker** (for local development)
   ```bash
   # Install Docker Desktop or Docker Engine
   docker --version
   ```

### Required Accounts

- Fly.io account (for backend hosting)
- Vercel account (for frontend hosting)
- GitHub account (for CI/CD)
- OpenAI account (for AI services)
- Google Cloud account (for Google Ads API)

## Environment Setup

### 1. Backend Environment Variables

Create the following secrets in Fly.io:

```bash
# Required API Keys
flyctl secrets set OPENAI_API_KEY="sk-your-openai-key"
flyctl secrets set GOOGLE_ADS_DEVELOPER_TOKEN="your-developer-token"
flyctl secrets set GOOGLE_ADS_CLIENT_ID="your-client-id"
flyctl secrets set GOOGLE_ADS_CLIENT_SECRET="your-client-secret"
flyctl secrets set GOOGLE_ADS_REFRESH_TOKEN="your-refresh-token"
flyctl secrets set GOOGLE_ADS_CUSTOMER_ID="your-customer-id"

# Optional Services
flyctl secrets set SENTRY_DSN="https://your-sentry-dsn"
flyctl secrets set CORS_ORIGINS="https://your-frontend-domain.vercel.app"
```

### 2. Frontend Environment Variables

Set these in Vercel dashboard or via CLI:

```bash
# Authentication
vercel env add NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
vercel env add CLERK_SECRET_KEY

# API Configuration
vercel env add NEXT_PUBLIC_API_BASE_URL
vercel env add NEXT_PUBLIC_BACKEND_URL

# Analytics (Optional)
vercel env add NEXT_PUBLIC_GOOGLE_ANALYTICS_ID
vercel env add NEXT_PUBLIC_SENTRY_DSN
```

## Deployment Methods

### Method 1: Automated Deployment (Recommended)

#### Production Deployment
```bash
# Run the automated deployment script
./scripts/deploy-production.sh
```

#### Staging Deployment
```bash
# Deploy to staging environment
./scripts/deploy-staging.sh
```

### Method 2: Manual Deployment

#### Backend Deployment

1. **Create and configure Fly.io resources:**
   ```bash
   # Create PostgreSQL database
   flyctl postgres create --name ailex-postgres --region iad

   # Create Redis instance
   flyctl redis create --name ailex-redis --region iad

   # Create the app
   cd backend
   flyctl apps create ailex-ad-agent-backend
   ```

2. **Attach resources:**
   ```bash
   flyctl postgres attach ailex-postgres
   flyctl redis attach ailex-redis
   ```

3. **Deploy:**
   ```bash
   flyctl deploy --remote-only
   ```

#### Frontend Deployment

1. **Deploy to Vercel:**
   ```bash
   cd frontend
   vercel --prod
   ```

### Method 3: CI/CD Pipeline

The system includes automated GitHub Actions workflows:

- **Tests**: Run on every PR and push
- **Deploy**: Automatic deployment on push to `main` (production) or `develop` (staging)

#### Setup GitHub Secrets

Add these secrets to your GitHub repository:

```
FLY_API_TOKEN=your-fly-api-token
VERCEL_TOKEN=your-vercel-token
VERCEL_ORG_ID=your-vercel-org-id
VERCEL_PROJECT_ID=your-vercel-project-id
```

## Configuration Files

### Backend Configuration

- `fly.toml` - Fly.io deployment configuration
- `Dockerfile` - Multi-stage Docker build
- `docker-compose.yml` - Local development environment
- `.env.production` - Production environment template

### Frontend Configuration

- `vercel.json` - Vercel deployment configuration
- `next.config.js` - Next.js configuration
- `.env.production` - Production environment template

## Monitoring and Observability

### Health Checks

The system includes comprehensive health check endpoints:

- `/api/v1/health/liveness` - Basic liveness check
- `/api/v1/health/readiness` - Service readiness check
- `/api/v1/health` - Comprehensive health status

### Monitoring Stack

Optional monitoring setup using Docker Compose:

```bash
# Start monitoring stack
cd monitoring
docker-compose -f docker-compose.monitoring.yml up -d

# Access services
# Grafana: http://localhost:3001 (admin/admin123)
# Prometheus: http://localhost:9090
# AlertManager: http://localhost:9093
```

### Logging

- **Backend**: Structured logging with JSON format
- **Frontend**: Browser console and Sentry integration
- **Aggregation**: Loki for log collection and analysis

## Post-Deployment Verification

### 1. Health Checks
```bash
# Backend health
curl https://ailex-ad-agent-backend.fly.dev/api/v1/health/liveness

# Full health status
curl https://ailex-ad-agent-backend.fly.dev/api/v1/health
```

### 2. API Testing
```bash
# Test API endpoints
curl https://ailex-ad-agent-backend.fly.dev/api/v1/campaigns
curl https://ailex-ad-agent-backend.fly.dev/api/v1/agents
```

### 3. Frontend Verification
- Visit your Vercel deployment URL
- Test authentication flow
- Verify API connectivity
- Check dashboard functionality

## Scaling Configuration

### Backend Scaling

Modify `fly.toml` for scaling:

```toml
[[vm]]
  memory = "2gb"
  cpu_kind = "shared"
  cpus = 2

[http_service]
  min_machines_running = 2
  max_machines_running = 10
```

### Database Scaling

```bash
# Scale PostgreSQL
flyctl postgres scale --vm-size shared-cpu-2x ailex-postgres

# Scale Redis
flyctl redis scale --vm-size shared-cpu-2x ailex-redis
```

## Troubleshooting

### Common Issues

1. **Deployment Failures**
   ```bash
   # Check logs
   flyctl logs --app ailex-ad-agent-backend
   
   # Check status
   flyctl status --app ailex-ad-agent-backend
   ```

2. **Database Connection Issues**
   ```bash
   # Check PostgreSQL status
   flyctl postgres status ailex-postgres
   
   # Connect to database
   flyctl postgres connect --app ailex-postgres
   ```

3. **Redis Connection Issues**
   ```bash
   # Check Redis status
   flyctl redis status ailex-redis
   ```

4. **Frontend Build Failures**
   - Check Vercel build logs
   - Verify environment variables
   - Check TypeScript errors

### Recovery Procedures

1. **Rollback Deployment**
   ```bash
   # Backend rollback
   flyctl releases --app ailex-ad-agent-backend
   flyctl rollback v1 --app ailex-ad-agent-backend
   
   # Frontend rollback (via Vercel dashboard)
   ```

2. **Database Recovery**
   ```bash
   # Create database backup
   flyctl postgres backup create ailex-postgres
   
   # Restore from backup
   flyctl postgres restore --app ailex-postgres backup-id
   ```

## Security Considerations

1. **Secrets Management**
   - Never commit secrets to version control
   - Use Fly.io secrets for backend
   - Use Vercel environment variables for frontend
   - Rotate secrets regularly

2. **Network Security**
   - Configure CORS origins properly
   - Use HTTPS only in production
   - Implement rate limiting
   - Monitor for suspicious activity

3. **Access Control**
   - Limit Fly.io organization access
   - Use GitHub branch protection rules
   - Implement proper authentication flows

## Cost Optimization

1. **Fly.io Optimization**
   - Use shared CPU instances for development
   - Scale down during off-hours
   - Monitor resource usage

2. **Vercel Optimization**
   - Optimize bundle size
   - Use edge functions efficiently
   - Monitor bandwidth usage

3. **Database Optimization**
   - Regular database maintenance
   - Optimize queries
   - Monitor storage usage

## Support and Resources

- [Fly.io Documentation](https://fly.io/docs/)
- [Vercel Documentation](https://vercel.com/docs)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Next.js Documentation](https://nextjs.org/docs)

For additional support, check the project's GitHub issues or contact the development team.