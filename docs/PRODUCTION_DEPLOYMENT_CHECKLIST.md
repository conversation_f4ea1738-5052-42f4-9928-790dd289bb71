# Production Deployment Checklist
# Google Ads AI Agent System

**Version:** 1.0  
**Last Updated:** 2025-01-06  
**Environment:** Production  

## Pre-Deployment Phase

### 1. Prerequisites Verification ✅
- [ ] **Fly.io Account Setup**
  - [ ] Fly.io CLI installed and authenticated
  - [ ] Organization/team access configured
  - [ ] Billing information set up
  
- [ ] **Vercel Account Setup**
  - [ ] Vercel CLI installed and authenticated
  - [ ] Project created and linked
  - [ ] Domain configuration ready

- [ ] **GitHub Configuration**
  - [ ] Repository secrets configured
  - [ ] Branch protection rules enabled
  - [ ] CI/CD workflows tested in staging

### 2. Environment Configuration ✅
- [ ] **Backend Environment (.env.production)**
  ```bash
  ENVIRONMENT=production
  DATABASE_URL=<Fly.io PostgreSQL URL>
  REDIS_URL=<Fly.io Redis URL>
  OPENAI_API_KEY=<Production OpenAI Key>
  GOOGLE_ADS_DEVELOPER_TOKEN=<Production Token>
  SENTRY_DSN=<Production Sentry DSN>
  LOG_LEVEL=INFO
  ```

- [ ] **Frontend Environment**
  ```bash
  NEXT_PUBLIC_API_URL=https://ailex-ad-agent-backend.fly.dev/api/v1
  NEXT_PUBLIC_ENVIRONMENT=production
  NEXT_PUBLIC_APP_NAME="AiLex Ad Agent System"
  ```

### 3. Infrastructure Setup ✅

#### 3.1 Fly.io Backend Setup
```bash
# 1. Create PostgreSQL database
flyctl postgres create \
  --name ailex-postgres \
  --region iad \
  --vm-size shared-cpu-2x \
  --volume-size 20

# 2. Create Redis instance
flyctl redis create \
  --name ailex-redis \
  --region iad

# 3. Create backend application
cd backend
flyctl apps create ailex-ad-agent-backend --org personal

# 4. Attach databases
flyctl postgres attach ailex-postgres
flyctl redis attach ailex-redis

# 5. Set production secrets
flyctl secrets set OPENAI_API_KEY="your-production-key"
flyctl secrets set GOOGLE_ADS_DEVELOPER_TOKEN="your-token"
flyctl secrets set GOOGLE_ADS_CLIENT_ID="your-client-id"
flyctl secrets set GOOGLE_ADS_CLIENT_SECRET="your-secret"
flyctl secrets set GOOGLE_ADS_REFRESH_TOKEN="your-refresh-token"
flyctl secrets set GOOGLE_ADS_CUSTOMER_ID="1234567890"
flyctl secrets set SENTRY_DSN="your-sentry-dsn"
flyctl secrets set CORS_ORIGINS="https://ailex.yourdomain.com"
```

#### 3.2 Vercel Frontend Setup  
```bash
# 1. Link project to Vercel
cd frontend
vercel link

# 2. Set production environment variables
vercel env add NEXT_PUBLIC_API_URL production
vercel env add NEXT_PUBLIC_ENVIRONMENT production
vercel env add VERCEL_TOKEN # For GitHub Actions

# 3. Configure custom domain (optional)
vercel domains add ailex.yourdomain.com
```

### 4. Security Configuration ✅
- [ ] **Secrets Management**
  - [ ] All sensitive data stored as secrets (not environment variables)
  - [ ] Secrets rotation schedule documented
  - [ ] Access logs monitoring enabled

- [ ] **Network Security**  
  - [ ] HTTPS forced on all endpoints
  - [ ] CORS properly configured
  - [ ] Security headers implemented
  - [ ] Rate limiting configured

- [ ] **Access Control**
  - [ ] Deploy keys configured
  - [ ] Team access permissions set
  - [ ] Audit logging enabled

## Deployment Phase

### 5. Initial Deployment ✅

#### 5.1 Backend Deployment
```bash
# Run from backend directory
flyctl deploy --remote-only

# Verify deployment
flyctl status
flyctl logs
```

#### 5.2 Frontend Deployment
```bash
# Run from frontend directory  
vercel --prod

# Verify deployment
curl -I https://ailex.yourdomain.com
```

### 6. Post-Deployment Validation ✅

#### 6.1 Health Checks
- [ ] **Backend Health Endpoints**
  ```bash
  # Liveness check
  curl https://ailex-ad-agent-backend.fly.dev/api/v1/health/liveness
  
  # Readiness check  
  curl https://ailex-ad-agent-backend.fly.dev/api/v1/health/readiness
  
  # General health
  curl https://ailex-ad-agent-backend.fly.dev/api/v1/health
  ```

- [ ] **Frontend Accessibility**
  ```bash
  # Homepage load test
  curl -I https://ailex.yourdomain.com
  
  # JavaScript bundle loading
  curl https://ailex.yourdomain.com/_next/static/chunks/main-*.js
  ```

#### 6.2 Integration Testing
- [ ] **API Integration**
  - [ ] Authentication endpoints working
  - [ ] Campaign management APIs responding
  - [ ] Google Ads API integration functional
  - [ ] Database connectivity confirmed

- [ ] **Frontend-Backend Integration**  
  - [ ] API calls from frontend successful
  - [ ] Error handling working correctly
  - [ ] Authentication flow complete

### 7. Performance Validation ✅

#### 7.1 Response Time Testing
```bash
# Backend response times
time curl https://ailex-ad-agent-backend.fly.dev/api/v1/health/liveness

# Frontend load times  
curl -w "@curl-format.txt" -o /dev/null https://ailex.yourdomain.com
```

#### 7.2 Load Testing (Optional)
```bash
# Simple load test with ab (Apache Bench)
ab -n 100 -c 10 https://ailex-ad-agent-backend.fly.dev/api/v1/health/liveness
```

- [ ] **Performance Criteria Met**
  - [ ] Backend response time <2 seconds
  - [ ] Frontend load time <3 seconds  
  - [ ] Database queries optimized
  - [ ] Error rates <1%

## Post-Deployment Phase

### 8. Monitoring Setup ✅

#### 8.1 Application Monitoring
- [ ] **Fly.io Monitoring**
  - [ ] Metrics dashboard configured
  - [ ] Log aggregation working
  - [ ] Alerts set up for downtime
  - [ ] Resource usage monitoring

- [ ] **Vercel Monitoring**
  - [ ] Analytics enabled
  - [ ] Performance monitoring active
  - [ ] Error tracking configured
  - [ ] Build notifications set up

#### 8.2 External Monitoring
- [ ] **GitHub Actions Monitoring**  
  - [ ] Workflow success rates tracked
  - [ ] Failure notifications configured
  - [ ] Performance metrics logged

- [ ] **Third-party Monitoring** (Optional)
  - [ ] Uptime monitoring (Pingdom, UptimeRobot)
  - [ ] APM solution (New Relic, DataDog)
  - [ ] Error tracking (Sentry)

### 9. Documentation Updates ✅
- [ ] **Deployment Documentation**
  - [ ] Production URLs documented
  - [ ] Access credentials stored securely
  - [ ] Troubleshooting guide updated
  - [ ] Team runbooks created

- [ ] **Operational Documentation**
  - [ ] Monitoring dashboards linked
  - [ ] Alert response procedures
  - [ ] Backup and recovery plans
  - [ ] Scaling procedures documented

### 10. Team Enablement ✅
- [ ] **Access Provisioning**
  - [ ] Team members added to Fly.io org
  - [ ] Vercel project access granted  
  - [ ] GitHub repository permissions
  - [ ] Production system access documented

- [ ] **Training Delivery**
  - [ ] Deployment procedures walkthrough
  - [ ] Monitoring and alerting training
  - [ ] Incident response procedures
  - [ ] Rollback procedures practiced

## Ongoing Operations

### 11. Daily Operations ✅
- [ ] **Health Monitoring**
  - [ ] Check automated health reports
  - [ ] Review error rates and performance
  - [ ] Monitor resource utilization
  - [ ] Verify backup completion

### 12. Weekly Operations ✅  
- [ ] **Performance Review**
  - [ ] Analyze response time trends
  - [ ] Review scaling events
  - [ ] Check security scan results
  - [ ] Update dependency status

### 13. Monthly Operations ✅
- [ ] **Security Maintenance**
  - [ ] Review and rotate secrets
  - [ ] Update security policies
  - [ ] Analyze security scan results
  - [ ] Conduct access review

- [ ] **Performance Optimization**
  - [ ] Review and optimize queries
  - [ ] Analyze bundle sizes
  - [ ] Update caching strategies  
  - [ ] Scale resources as needed

## Emergency Procedures

### 14. Incident Response ✅
- [ ] **Escalation Contacts**
  ```
  Primary: Infrastructure Team Lead
  Secondary: Development Team Lead  
  Emergency: On-call Engineer
  ```

- [ ] **Communication Channels**
  - [ ] Status page updates
  - [ ] Team notifications (Slack/Teams)
  - [ ] Customer communications
  - [ ] Stakeholder updates

### 15. Rollback Procedures ✅
- [ ] **Automated Rollback**
  ```bash
  # Trigger emergency rollback
  gh workflow run rollback.yml \
    -f environment=production \
    -f component=both \
    -f reason="Production incident"
  ```

- [ ] **Manual Rollback**
  ```bash
  # Backend manual rollback
  flyctl releases rollback [RELEASE_ID]
  
  # Frontend manual rollback  
  vercel rollback [DEPLOYMENT_URL] --prod
  ```

## Validation Checklist

### 16. Final Production Readiness ✅
- [ ] **Technical Validation**
  - [ ] All services running and healthy
  - [ ] Performance metrics within targets
  - [ ] Security scans completed
  - [ ] Integration tests passing

- [ ] **Operational Validation**  
  - [ ] Monitoring and alerting functional
  - [ ] Team access configured
  - [ ] Documentation complete
  - [ ] Runbooks tested

- [ ] **Business Validation**
  - [ ] Key functionality working
  - [ ] User acceptance criteria met
  - [ ] Compliance requirements satisfied
  - [ ] Risk assessment completed

## Sign-off

**Deployment Manager:** _________________ **Date:** _________

**Technical Lead:** _________________ **Date:** _________  

**Security Review:** _________________ **Date:** _________

**Business Approval:** _________________ **Date:** _________

---

## Quick Reference

### Key URLs
- **Backend API:** https://ailex-ad-agent-backend.fly.dev
- **Frontend:** https://ailex.yourdomain.com  
- **Fly.io Dashboard:** https://fly.io/dashboard
- **Vercel Dashboard:** https://vercel.com/dashboard

### Emergency Contacts
- **Infrastructure Team:** [team-email]
- **On-call Engineer:** [phone-number]
- **Manager:** [contact-info]

### Critical Commands
```bash
# Check health
curl https://ailex-ad-agent-backend.fly.dev/api/v1/health/liveness

# View logs  
flyctl logs -a ailex-ad-agent-backend

# Emergency rollback
gh workflow run rollback.yml -f environment=production -f component=both -f reason="Emergency"
```

Remember: Always test in staging first, have a rollback plan, and monitor closely during deployment windows!