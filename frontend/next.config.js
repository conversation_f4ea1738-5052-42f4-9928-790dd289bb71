const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable standalone output for Docker
  output: 'standalone',

  typescript: {
    // Disable TypeScript errors during production builds
    ignoreBuildErrors: true,
  },
  eslint: {
    // Disable ESLint during production builds
    ignoreDuringBuilds: true,
  },
  images: {
    domains: ['localhost'],
  },
  // Enable experimental features for better performance
  experimental: {
    optimizePackageImports: ['lucide-react'],
    serverComponentsExternalPackages: ['@clerk/nextjs'],
  },
  // Performance optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Webpack configuration for Clerk compatibility
  webpack: (config) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      '@clerk/nextjs/server': '@clerk/nextjs/dist/cjs/server.js',
    }
    return config
  },
}

module.exports = withBundleAnalyzer(nextConfig)