# Frontend Testing Guide

This document outlines the comprehensive testing infrastructure for the Google Ads AI Agent Next.js frontend.

## Overview

The testing setup includes:
- **Jest** for test runner and framework
- **React Testing Library** for component testing
- **<PERSON><PERSON> (Mock Service Worker)** for API mocking
- **TypeScript** with strict configuration
- **ESLint** with comprehensive rules
- **Coverage reporting** with thresholds

## Getting Started

### Installation

All testing dependencies are already installed. To install them manually:

```bash
npm install --save-dev jest jest-environment-jsdom @testing-library/react @testing-library/jest-dom @testing-library/user-event msw
```

### Running Tests

```bash
# Run tests once
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI (no watch, with coverage)
npm run test:ci

# Debug tests
npm run test:debug
```

## Test Structure

```
frontend/
├── __tests__/
│   ├── components/          # Component tests
│   │   ├── ui/
│   │   └── campaigns/
│   ├── hooks/              # Hook tests
│   ├── lib/                # Utility function tests
│   ├── integration/        # Integration tests
│   ├── mocks/             # MSW handlers and server setup
│   │   ├── handlers.ts
│   │   └── server.ts
│   └── utils/             # Test utilities
│       └── test-utils.tsx
├── jest.config.js         # Jest configuration
├── jest.setup.ts          # Test setup and global mocks
└── TESTING.md            # This guide
```

## Test Categories

### 1. Component Tests

Test React components in isolation with proper mocking of dependencies.

**Example: Button Component**
```typescript
import { render, screen } from '../../utils/test-utils'
import { Button } from '@/components/ui/button'

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument()
  })

  it('calls onClick when clicked', async () => {
    const handleClick = jest.fn()
    const { user } = render(<Button onClick={handleClick}>Click me</Button>)
    
    await user.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
```

### 2. Hook Tests

Test custom React hooks with proper mocking of API calls and dependencies.

**Example: useCampaigns Hook**
```typescript
import { renderHook, waitFor } from '@testing-library/react'
import { useCampaigns } from '@/hooks/use-campaigns'

describe('useCampaigns', () => {
  it('fetches campaigns successfully', async () => {
    const { result } = renderHook(() => useCampaigns(), {
      wrapper: createWrapper(),
    })

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.campaigns).toHaveLength(3)
  })
})
```

### 3. Utility Function Tests

Test pure functions and utilities.

**Example: Utils Functions**
```typescript
import { formatCurrency, cn } from '@/lib/utils'

describe('formatCurrency', () => {
  it('formats USD currency correctly', () => {
    expect(formatCurrency(1234.56)).toBe('$1,234.56')
  })
})
```

### 4. Integration Tests

Test complete user flows and component interactions.

## Mocking Strategy

### API Mocking with MSW

We use MSW to mock API calls at the network level:

```typescript
// __tests__/mocks/handlers.ts
export const handlers = [
  http.get('*/campaigns', () => {
    return HttpResponse.json(createMockPaginatedResponse(campaigns))
  }),
  // ... other handlers
]
```

### Authentication Mocking

Clerk authentication is mocked globally in `jest.setup.ts`:

```typescript
jest.mock('@clerk/nextjs', () => ({
  useUser: jest.fn(() => ({
    user: { id: 'test-user-id', firstName: 'Test', lastName: 'User' },
    isLoaded: true,
    isSignedIn: true,
  })),
  // ... other mocks
}))
```

### SWR Mocking

SWR is configured with no deduping interval for tests:

```typescript
<SWRConfig value={{ dedupingInterval: 0, provider: () => new Map() }}>
  {children}
</SWRConfig>
```

## Test Utilities

### Custom Render Function

Located in `__tests__/utils/test-utils.tsx`:

```typescript
import { render } from '../utils/test-utils'

// This render function includes all necessary providers:
// - ClerkProvider for authentication
// - ThemeProvider for theming
// - SWRConfig for data fetching
const { user } = render(<MyComponent />)
```

### Mock Data Factories

```typescript
// Create mock campaign data
const mockCampaign = createMockCampaign({
  id: '1',
  name: 'Test Campaign',
  status: 'active',
})

// Create mock API responses
const mockResponse = createMockApiResponse(data)
const mockPaginatedResponse = createMockPaginatedResponse(items)
```

## Coverage Configuration

Coverage thresholds are set in `jest.config.js`:

```javascript
coverageThreshold: {
  global: {
    branches: 70,
    functions: 70,
    lines: 70,
    statements: 70,
  },
}
```

Files included in coverage:
- `app/**/*.{js,jsx,ts,tsx}`
- `components/**/*.{js,jsx,ts,tsx}`
- `hooks/**/*.{js,jsx,ts,tsx}`
- `lib/**/*.{js,jsx,ts,tsx}`
- `utils/**/*.{js,jsx,ts,tsx}`

## TypeScript Configuration

Enhanced with strict options in `tsconfig.json`:
- `noUncheckedIndexedAccess`: true
- `exactOptionalPropertyTypes`: true
- `noImplicitReturns`: true
- `noFallthroughCasesInSwitch`: true

## ESLint Configuration

Comprehensive rules for:
- **TypeScript**: Strict type checking and best practices
- **React**: Hooks rules and component best practices
- **Accessibility**: jsx-a11y rules for WCAG compliance
- **Testing**: testing-library specific rules
- **Imports**: Unused import detection and cleanup

## Best Practices

### 1. Test Structure

```typescript
describe('ComponentName', () => {
  describe('rendering', () => {
    it('renders correctly with default props', () => {
      // Test default rendering
    })
  })

  describe('interaction', () => {
    it('handles user interactions', async () => {
      // Test user interactions
    })
  })

  describe('accessibility', () => {
    it('meets accessibility standards', () => {
      // Test accessibility
    })
  })
})
```

### 2. Async Testing

Always use `waitFor` for async operations:

```typescript
await waitFor(() => {
  expect(screen.getByText('Loading complete')).toBeInTheDocument()
})
```

### 3. User Event Testing

Use `@testing-library/user-event` for realistic user interactions:

```typescript
const { user } = render(<Component />)
await user.click(button)
await user.type(input, 'text')
```

### 4. Accessibility Testing

Test for proper ARIA attributes and keyboard navigation:

```typescript
expect(button).toHaveAttribute('aria-label', 'Expected label')
expect(button).toBeEnabled()
```

### 5. Error Handling

Test both success and error scenarios:

```typescript
it('handles API errors gracefully', async () => {
  server.use(
    http.get('*/api', () => HttpResponse.error())
  )
  
  render(<Component />)
  // Test error state
})
```

## CI/CD Integration

Tests are automatically run in GitHub Actions:

1. **Linting**: ESLint checks code quality
2. **Type Checking**: TypeScript compilation
3. **Testing**: Full test suite with coverage
4. **Build**: Production build verification
5. **Bundle Analysis**: Bundle size monitoring

## Debugging Tests

### VS Code Debugging

1. Set breakpoints in test files
2. Run `npm run test:debug`
3. Connect VS Code debugger to Node process

### Console Logging

Use `screen.debug()` to see current DOM state:

```typescript
render(<Component />)
screen.debug() // Prints current DOM to console
```

## Performance Testing

Monitor test performance and bundle size:

```bash
# Analyze bundle size
npm run analyze

# Check test performance
npm run test:coverage -- --verbose
```

## Adding New Tests

### For Components:
1. Create test file: `__tests__/components/path/component.test.tsx`
2. Import test utilities
3. Write comprehensive tests for rendering, interaction, and accessibility

### For Hooks:
1. Create test file: `__tests__/hooks/hook-name.test.tsx`
2. Use `renderHook` from RTL
3. Mock dependencies appropriately

### For Integration:
1. Create test file: `__tests__/integration/feature-name.test.tsx`
2. Test complete user workflows
3. Include error scenarios and edge cases

## Troubleshooting

### Common Issues:

1. **Tests timing out**: Increase timeout or check for unresolved promises
2. **Mock not working**: Ensure mock is defined before component import
3. **Coverage not accurate**: Check file paths in coverage configuration
4. **ESLint errors**: Review and fix linting rules in `.eslintrc.json`

### Getting Help:

- Check Jest documentation for test runner issues
- Refer to Testing Library docs for component testing
- Review MSW documentation for API mocking
- Check Next.js testing guide for framework-specific issues

## Continuous Improvement

Regular tasks:
- Review and update test coverage thresholds
- Add new test patterns as the application grows
- Keep testing dependencies updated
- Monitor test performance and optimize slow tests
- Review and improve mock reliability

This testing infrastructure provides a solid foundation for maintaining code quality and catching regressions early in the development process.