import { authMiddleware } from "@clerk/nextjs"

export default authMiddleware({
  // Routes that can be accessed while signed out
  publicRoutes: [
    "/",
    "/sign-in(.*)",
    "/sign-up(.*)",
    "/api/health",
  ],
  // Routes that are always accessible, even if user is signed out
  ignoredRoutes: [
    "/api/health",
    "/_next/static(.*)",
    "/_next/image(.*)",
    "/favicon.ico",
    "/public/(.*)",
  ],
  // Redirect users to dashboard after signing in
  afterAuth(auth, req) {
    // If user is signed in and trying to access sign-in/sign-up pages, redirect to dashboard
    if (auth.userId && (req.nextUrl.pathname === "/sign-in" || req.nextUrl.pathname === "/sign-up")) {
      return Response.redirect(new URL("/dashboard", req.url))
    }
    
    // If user is not signed in and trying to access protected routes, redirect to sign-in
    if (!auth.userId && !auth.isPublicRoute) {
      return Response.redirect(new URL("/sign-in", req.url))
    }
    
    // Allow the request to proceed
    return null
  },
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    // Always run for API routes
    "/(api|trpc)(.*)",
  ],
}