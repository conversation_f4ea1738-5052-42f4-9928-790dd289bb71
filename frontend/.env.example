# AiLex Ad Agent System - Frontend Environment Configuration
# Copy this file to .env.local and fill in your actual values

# Application Settings
NEXT_PUBLIC_APP_NAME="AiLex Ad Agent System"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_ENVIRONMENT="development"

# API Configuration
NEXT_PUBLIC_API_BASE_URL="http://localhost:8000/api/v1"
NEXT_PUBLIC_BACKEND_URL="http://localhost:8000"

# Authentication (Supabase)
NEXT_PUBLIC_SUPABASE_URL="https://your-project-id.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"

# Authentication Configuration
NEXT_PUBLIC_SIGN_IN_URL="/auth/sign-in"
NEXT_PUBLIC_SIGN_UP_URL="/auth/sign-up"
NEXT_PUBLIC_FORGOT_PASSWORD_URL="/auth/forgot-password"
NEXT_PUBLIC_RESET_PASSWORD_URL="/auth/reset-password"
NEXT_PUBLIC_AFTER_SIGN_IN_URL="/dashboard"
NEXT_PUBLIC_AFTER_SIGN_UP_URL="/dashboard"

# Analytics and Monitoring
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_DARK_MODE=true

# API Rate Limiting
NEXT_PUBLIC_API_RATE_LIMIT=100

# WebSocket Configuration (if needed)
NEXT_PUBLIC_WS_URL="ws://localhost:8000/ws"

# Sentry (Error Tracking)
NEXT_PUBLIC_SENTRY_DSN="https://your-sentry-dsn"
SENTRY_ORG="your-org"
SENTRY_PROJECT="ailex-frontend"
SENTRY_AUTH_TOKEN="your-sentry-auth-token"

# Build Configuration
ANALYZE=false
NEXT_TELEMETRY_DISABLED=1