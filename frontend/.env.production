# AiLex Ad Agent System - Frontend Production Environment Configuration

# Application Settings
NEXT_PUBLIC_APP_NAME="AiLex Ad Agent System"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_ENVIRONMENT="production"

# API Configuration
NEXT_PUBLIC_API_BASE_URL="https://ailex-ad-agent-backend.fly.dev/api/v1"
NEXT_PUBLIC_BACKEND_URL="https://ailex-ad-agent-backend.fly.dev"

# Authentication (Supabase) - Set in Vercel Environment Variables
NEXT_PUBLIC_SUPABASE_URL="https://pamppqrhytvyclvdbbxx.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBhbXBwcXJoeXR2eWNsdmRiYnh4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMzMjgwODIsImV4cCI6MjA1ODkwNDA4Mn0.zaKZknUIc8TfxQUnpS07Fw_x6yl4mWn5zuUoVHoYPHQ"

# Authentication Configuration
NEXT_PUBLIC_SIGN_IN_URL="/auth/sign-in"
NEXT_PUBLIC_SIGN_UP_URL="/auth/sign-up"
NEXT_PUBLIC_FORGOT_PASSWORD_URL="/auth/forgot-password"
NEXT_PUBLIC_RESET_PASSWORD_URL="/auth/reset-password"
NEXT_PUBLIC_AFTER_SIGN_IN_URL="/dashboard"
NEXT_PUBLIC_AFTER_SIGN_UP_URL="/dashboard"

# Analytics and Monitoring - Set in Vercel Environment Variables
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=""

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_DARK_MODE=true

# API Configuration
NEXT_PUBLIC_API_RATE_LIMIT=200

# WebSocket Configuration
NEXT_PUBLIC_WS_URL="wss://ailex-ad-agent-backend.fly.dev/ws"

# Sentry (Error Tracking) - Set in Vercel Environment Variables
NEXT_PUBLIC_SENTRY_DSN=""
SENTRY_ORG=""
SENTRY_PROJECT="ailex-frontend"
SENTRY_AUTH_TOKEN=""

# Build Configuration
ANALYZE=false
NEXT_TELEMETRY_DISABLED=1