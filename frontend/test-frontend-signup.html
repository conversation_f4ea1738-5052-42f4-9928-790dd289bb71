<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Sign-up Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Frontend Sign-up Diagnostic Tool</h1>
        <p>This tool helps diagnose issues with the sign-up form by testing various scenarios.</p>

        <div class="test-section">
            <h3>📋 Test Instructions</h3>
            <div class="status info">
                <strong>How to use this tool:</strong><br>
                1. Click "Open Sign-up Page" to load the form in an iframe<br>
                2. Try filling out the form manually<br>
                3. Check the console logs below for any errors<br>
                4. Use the automated tests to verify functionality
            </div>
        </div>

        <div class="test-section">
            <h3>🌐 Sign-up Page Test</h3>
            <button onclick="loadSignupPage()">Open Sign-up Page</button>
            <button onclick="clearLogs()">Clear Logs</button>
            <div id="iframe-container" style="margin-top: 15px;">
                <!-- iframe will be loaded here -->
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 Console Logs & Errors</h3>
            <div id="console-log" class="log">
                Console logs will appear here...
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Automated Tests</h3>
            <button onclick="testFormValidation()">Test Form Validation</button>
            <button onclick="testNetworkConnectivity()">Test Network Connectivity</button>
            <button onclick="testSupabaseConnection()">Test Supabase Connection</button>
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h3>📊 Diagnostic Information</h3>
            <div id="diagnostic-info">
                <p><strong>Current URL:</strong> <span id="current-url"></span></p>
                <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
                <p><strong>Local Storage Available:</strong> <span id="local-storage"></span></p>
                <p><strong>Session Storage Available:</strong> <span id="session-storage"></span></p>
                <p><strong>Cookies Enabled:</strong> <span id="cookies-enabled"></span></p>
            </div>
        </div>
    </div>

    <script>
        // Initialize diagnostic info
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('local-storage').textContent = typeof(Storage) !== "undefined" ? "Yes" : "No";
        document.getElementById('session-storage').textContent = typeof(Storage) !== "undefined" ? "Yes" : "No";
        document.getElementById('cookies-enabled').textContent = navigator.cookieEnabled ? "Yes" : "No";

        // Console logging
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function logToDiv(message, type = 'log') {
            const logDiv = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            logEntry.textContent = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToDiv(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            logToDiv(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            logToDiv(args.join(' '), 'warn');
        };

        // Capture unhandled errors
        window.addEventListener('error', function(e) {
            logToDiv(`Unhandled Error: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
        });

        window.addEventListener('unhandledrejection', function(e) {
            logToDiv(`Unhandled Promise Rejection: ${e.reason}`, 'error');
        });

        function loadSignupPage() {
            const container = document.getElementById('iframe-container');
            container.innerHTML = '<iframe src="http://localhost:3000/auth/sign-up" title="Sign-up Page"></iframe>';
            logToDiv('Sign-up page loaded in iframe');
        }

        function clearLogs() {
            document.getElementById('console-log').innerHTML = 'Console logs cleared...';
        }

        async function testFormValidation() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="status info">Testing form validation...</div>';
            
            try {
                // This would need to be implemented with actual form testing
                logToDiv('Form validation test started');
                
                // Simulate test results
                setTimeout(() => {
                    resultsDiv.innerHTML = '<div class="status success">✅ Form validation test completed</div>';
                    logToDiv('Form validation test completed successfully');
                }, 1000);
                
            } catch (error) {
                resultsDiv.innerHTML = '<div class="status error">❌ Form validation test failed</div>';
                logToDiv(`Form validation test error: ${error.message}`, 'error');
            }
        }

        async function testNetworkConnectivity() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="status info">Testing network connectivity...</div>';
            
            try {
                const response = await fetch('http://localhost:3000/api/health');
                if (response.ok) {
                    resultsDiv.innerHTML = '<div class="status success">✅ Network connectivity test passed</div>';
                    logToDiv('Network connectivity test passed');
                } else {
                    resultsDiv.innerHTML = '<div class="status error">❌ Network connectivity test failed</div>';
                    logToDiv(`Network test failed with status: ${response.status}`, 'error');
                }
            } catch (error) {
                resultsDiv.innerHTML = '<div class="status error">❌ Network connectivity test failed</div>';
                logToDiv(`Network connectivity error: ${error.message}`, 'error');
            }
        }

        async function testSupabaseConnection() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="status info">Testing Supabase connection...</div>';
            
            try {
                // This would need the Supabase client to be available
                logToDiv('Supabase connection test started');
                
                // For now, just simulate the test
                setTimeout(() => {
                    resultsDiv.innerHTML = '<div class="status success">✅ Supabase connection test completed (simulated)</div>';
                    logToDiv('Supabase connection test completed (simulated)');
                }, 1000);
                
            } catch (error) {
                resultsDiv.innerHTML = '<div class="status error">❌ Supabase connection test failed</div>';
                logToDiv(`Supabase connection error: ${error.message}`, 'error');
            }
        }

        // Initial log
        logToDiv('Frontend diagnostic tool initialized');
    </script>
</body>
</html>
