/**
 * Debug script to test module resolution
 * This helps identify if the issue is with path mapping or file accessibility
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Debugging Module Resolution');
console.log('==============================');

// Check if files exist
const filesToCheck = [
  './lib/utils.ts',
  './lib/supabase.ts',
  './lib/api.ts',
  './components/ui/button.tsx',
  './hooks/use-toast.ts',
  './contexts/auth-context.tsx'
];

console.log('\n📁 File Existence Check:');
filesToCheck.forEach(file => {
  const exists = fs.existsSync(file);
  const fullPath = path.resolve(file);
  console.log(`${exists ? '✅' : '❌'} ${file} ${exists ? `(${fullPath})` : '(NOT FOUND)'}`);
});

// Check tsconfig.json
console.log('\n⚙️  TypeScript Configuration:');
try {
  const tsconfig = JSON.parse(fs.readFileSync('./tsconfig.json', 'utf8'));
  console.log('✅ tsconfig.json found');
  console.log('📍 Base URL:', tsconfig.compilerOptions?.baseUrl || 'NOT SET');
  console.log('🔗 Path mappings:');
  if (tsconfig.compilerOptions?.paths) {
    Object.entries(tsconfig.compilerOptions.paths).forEach(([key, value]) => {
      console.log(`   ${key} → ${JSON.stringify(value)}`);
    });
  } else {
    console.log('   ❌ No path mappings found');
  }
} catch (error) {
  console.log('❌ Error reading tsconfig.json:', error.message);
}

// Check next.config.js
console.log('\n⚙️  Next.js Configuration:');
try {
  const nextConfigPath = './next.config.js';
  if (fs.existsSync(nextConfigPath)) {
    console.log('✅ next.config.js found');
    const content = fs.readFileSync(nextConfigPath, 'utf8');
    const hasWebpackConfig = content.includes('webpack:');
    const hasPathAliases = content.includes('resolve.alias');
    console.log(`🔧 Webpack config: ${hasWebpackConfig ? '✅ Present' : '❌ Missing'}`);
    console.log(`🔗 Path aliases: ${hasPathAliases ? '✅ Present' : '❌ Missing'}`);
  } else {
    console.log('❌ next.config.js not found');
  }
} catch (error) {
  console.log('❌ Error reading next.config.js:', error.message);
}

// Check package.json
console.log('\n📦 Package Information:');
try {
  const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
  console.log('✅ package.json found');
  console.log('📛 Name:', packageJson.name);
  console.log('🏷️  Version:', packageJson.version);
  console.log('⚛️  Next.js version:', packageJson.dependencies?.next || 'NOT FOUND');
  console.log('📘 TypeScript version:', packageJson.devDependencies?.typescript || 'NOT FOUND');
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
}

// Check current working directory
console.log('\n📂 Environment Information:');
console.log('📍 Current directory:', process.cwd());
console.log('📁 Directory contents:');
try {
  const files = fs.readdirSync('.').filter(f => !f.startsWith('.') && !f.includes('node_modules'));
  files.forEach(file => {
    const stat = fs.statSync(file);
    console.log(`   ${stat.isDirectory() ? '📁' : '📄'} ${file}`);
  });
} catch (error) {
  console.log('❌ Error reading directory:', error.message);
}

console.log('\n🎯 Summary:');
console.log('If all files exist and configurations look correct,');
console.log('the issue might be with Vercel\'s build environment.');
console.log('Consider using relative imports as a temporary workaround.');
