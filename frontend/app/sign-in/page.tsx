'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function SignInPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the full authentication page
    router.replace('/auth/sign-in')
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="w-full max-w-md space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-2xl font-bold">Redirecting...</h1>
          <p className="text-muted-foreground">
            Taking you to the sign-in page
          </p>
        </div>
      </div>
    </div>
  )
}