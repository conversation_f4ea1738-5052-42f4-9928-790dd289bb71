"use client"

import { useState, useEffect } from "react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { AgentDetailsDialog } from "@/components/agents/agent-details-dialog"
import { useAgents } from "@/hooks/use-agents"
import { useToast } from "@/hooks/use-toast"
import { formatDateTime, getInitials } from "../../lib/utils"
import type { Agent } from "@/lib/api"
import {
  Bot,
  Brain,
  Eye,
  Pause,
  Play,
  Settings,
  Activity,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  RefreshCw,
  Zap,
  Target,
} from "lucide-react"

function getStatusColor(status: string) {
  switch (status) {
    case "active":
      return "bg-green-500"
    case "idle":
      return "bg-yellow-500"
    case "error":
      return "bg-red-500"
    default:
      return "bg-gray-500"
  }
}

function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "active":
      return "default"
    case "idle":
      return "secondary"
    case "error":
      return "destructive"
    default:
      return "outline"
  }
}

function AgentsSkeleton() {
  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-3">
              <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-20" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Agents Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div>
                    <Skeleton className="h-5 w-32 mb-2" />
                    <Skeleton className="h-4 w-48" />
                  </div>
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-16 w-full" />
              <div className="grid grid-cols-2 gap-4">
                {Array.from({ length: 4 }).map((_, j) => (
                  <div key={j}>
                    <Skeleton className="h-3 w-16 mb-1" />
                    <Skeleton className="h-4 w-12" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

export default function AgentsPage() {
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null)
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)
  const { toast } = useToast()

  const {
    agents,
    isLoading,
    isError,
    refresh,
    startAgent,
    stopAgent,
  } = useAgents({ limit: 50 })

  useEffect(() => {
    if (isError) {
      toast({
        title: "Error loading agents",
        description: "Failed to load agents. Please try refreshing the page.",
        variant: "destructive",
      })
    }
  }, [isError, toast])

  const handleViewDetails = (agentId: string) => {
    setSelectedAgentId(agentId)
    setShowDetailsDialog(true)
  }

  const handleToggleStatus = async (agent: Agent) => {
    try {
      if (agent.status === "active") {
        await stopAgent(agent.id, agent.name)
      } else {
        await startAgent(agent.id, agent.name)
      }
    } catch (error) {
      console.error("Error toggling agent status:", error)
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout
        title="AI Agents"
        description="Monitor and manage your AI optimization agents"
      >
        <AgentsSkeleton />
      </DashboardLayout>
    )
  }

  const activeAgents = agents.filter(agent => agent.status === "active").length
  const idleAgents = agents.filter(agent => agent.status === "idle").length
  const errorAgents = agents.filter(agent => agent.status === "error").length

  // Mock aggregate data since we don't have real metrics yet
  const totalTasks = Math.floor(Math.random() * 500) + 200
  const avgSuccessRate = 0.92
  const systemHealth = errorAgents === 0 ? "optimal" : errorAgents < 2 ? "good" : "degraded"

  // Mock recent activity logs
  const recentLogs = [
    {
      time: new Date(Date.now() - 5 * 60 * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      agent: agents[0]?.name || "Campaign Planner",
      action: "Completed campaign optimization analysis",
      type: "success",
    },
    {
      time: new Date(Date.now() - 10 * 60 * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      agent: agents[1]?.name || "Keyword Optimizer",
      action: "Updated bid adjustments for high-performing keywords",
      type: "success",
    },
    {
      time: new Date(Date.now() - 15 * 60 * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      agent: agents[2]?.name || "Ad Copy Generator",
      action: "Generated new ad variations for testing",
      type: "info",
    },
  ]

  return (
    <DashboardLayout
      title="AI Agents"
      description="Monitor and manage your AI optimization agents"
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={() => refresh()}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>

        {/* Agent System Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Bot className="h-4 w-4" />
                Active Agents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeAgents}</div>
              <p className="text-xs text-muted-foreground">
                {agents.length} total agents
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Tasks Completed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalTasks}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +24 in the last hour
                </span>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(avgSuccessRate * 100).toFixed(1)}%
              </div>
              <Progress value={avgSuccessRate * 100} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">System Health</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <div className={`h-3 w-3 rounded-full ${
                  systemHealth === "optimal" ? "bg-green-500" : 
                  systemHealth === "good" ? "bg-yellow-500" : "bg-red-500"
                }`} />
                <span className="text-sm font-medium capitalize">{systemHealth}</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {errorAgents === 0 ? "All systems operational" : `${errorAgents} agents need attention`}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Agents Grid */}
        {agents.length > 0 ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
            {agents.map((agent) => (
              <Card key={agent.id} className="relative">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback className="bg-primary text-primary-foreground">
                          {getInitials(agent.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-base">{agent.name}</CardTitle>
                        <CardDescription className="text-sm">
                          {agent.description || "AI optimization agent"}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className={`h-2 w-2 rounded-full ${getStatusColor(agent.status)}`} />
                      <Badge variant={getStatusBadgeVariant(agent.status) as any}>
                        {agent.status}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Current Status */}
                  <div className="rounded-lg bg-muted/50 p-3">
                    <div className="flex items-center gap-2 text-sm font-medium mb-1">
                      <Activity className="h-3 w-3" />
                      Status
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {agent.status === "active" 
                        ? "Agent is running and processing tasks"
                        : agent.status === "idle"
                        ? "Agent is idle, waiting for tasks"
                        : "Agent encountered an error"
                      }
                    </p>
                  </div>

                  {/* Agent Metrics */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <CheckCircle className="h-3 w-3" />
                        Type
                      </div>
                      <p className="font-medium capitalize">{agent.type.replace(/_/g, " ")}</p>
                    </div>
                    <div>
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        Campaign
                      </div>
                      <p className="font-medium">{agent.campaign_id ? "Assigned" : "Available"}</p>
                    </div>
                    <div>
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <TrendingUp className="h-3 w-3" />
                        Created
                      </div>
                      <p className="font-medium">
                        {agent.created_at ? formatDateTime(agent.created_at).split(" ")[0] : "Unknown"}
                      </p>
                    </div>
                    <div>
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <Activity className="h-3 w-3" />
                        Updated
                      </div>
                      <p className="font-medium">
                        {agent.updated_at ? formatDateTime(agent.updated_at).split(" ")[0] : "Unknown"}
                      </p>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex-1"
                      onClick={() => handleViewDetails(agent.id)}
                    >
                      <Eye className="mr-2 h-3 w-3" />
                      View Details
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={agent.status === "active" ? "text-yellow-600" : "text-green-600"}
                      onClick={() => handleToggleStatus(agent)}
                    >
                      {agent.status === "active" ? (
                        <Pause className="h-3 w-3" />
                      ) : (
                        <Play className="h-3 w-3" />
                      )}
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleViewDetails(agent.id)}
                    >
                      <Settings className="h-3 w-3" />
                    </Button>
                  </div>

                  {/* Last Activity */}
                  <div className="text-xs text-muted-foreground border-t pt-2">
                    Last activity: {agent.last_activity ? formatDateTime(agent.last_activity) : "Never"}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Bot className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
            <h3 className="text-lg font-semibold mb-2">No AI agents configured</h3>
            <p className="text-muted-foreground mb-4">
              Set up AI agents to start automating your campaign optimization.
            </p>
            <Button>
              <Zap className="mr-2 h-4 w-4" />
              Configure Agents
            </Button>
          </div>
        )}

        {/* System Logs */}
        {recentLogs.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Recent Agent Activity</CardTitle>
              <CardDescription>
                Latest actions and status updates from your AI agents
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentLogs.map((log, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-3 rounded-lg border p-3"
                  >
                    <div
                      className={`h-2 w-2 rounded-full ${
                        log.type === "success" ? "bg-green-500" : "bg-blue-500"
                      }`}
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-muted-foreground">{log.time}</span>
                        <span className="text-sm font-medium">{log.agent}</span>
                      </div>
                      <p className="text-sm text-muted-foreground">{log.action}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Agent Details Dialog */}
        {selectedAgentId && (
          <AgentDetailsDialog
            open={showDetailsDialog}
            onOpenChange={setShowDetailsDialog}
            agentId={selectedAgentId}
          />
        )}
      </div>
    </DashboardLayout>
  )
}