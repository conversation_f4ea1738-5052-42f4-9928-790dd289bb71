/**
 * Sign Up Page
 * Public page for user registration
 */

import { Suspense } from 'react'
import { Metadata } from 'next'
import { SignUpForm } from '@/components/auth/sign-up-form'

// Force dynamic rendering to avoid static generation issues with auth context
export const dynamic = 'force-dynamic'

export const metadata: Metadata = {
  title: 'Sign Up | AiLex Ad Agent System',
  description: 'Create your AiLex Ad Agent account to start managing Google Ads campaigns with AI-powered optimization.',
}

interface SignUpPageProps {
  searchParams: { redirectTo?: string }
}

export default function SignUpPage({ searchParams }: SignUpPageProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <Suspense fallback={<SignUpPageSkeleton />}>
          <SignUpForm redirectTo={searchParams.redirectTo} />
        </Suspense>
      </div>
    </div>
  )
}

function SignUpPageSkeleton() {
  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white shadow-md rounded-lg p-6 space-y-4 animate-pulse">
        <div className="text-center space-y-2">
          <div className="h-8 bg-gray-200 rounded w-3/4 mx-auto"></div>
          <div className="h-4 bg-gray-200 rounded w-full"></div>
        </div>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-10 bg-gray-200 rounded"></div>
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-10 bg-gray-200 rounded"></div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-10 bg-gray-200 rounded"></div>
          </div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-10 bg-gray-200 rounded"></div>
          </div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
            <div className="h-10 bg-gray-200 rounded"></div>
          </div>
          <div className="h-10 bg-blue-200 rounded"></div>
        </div>
      </div>
    </div>
  )
}