/**
 * Reset Password Page
 * Page for users to set a new password after clicking the reset link
 */

import { Suspense } from 'react'
import { Metadata } from 'next'
import { ResetPasswordForm } from '@/components/auth/reset-password-form'

// Force dynamic rendering to avoid static generation issues with auth context
export const dynamic = 'force-dynamic'

export const metadata: Metadata = {
  title: 'Reset Password | AiLex Ad Agent System',
  description: 'Set a new password for your AiLex Ad Agent account.',
}

export default function ResetPasswordPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <Suspense fallback={<ResetPasswordPageSkeleton />}>
          <ResetPasswordForm />
        </Suspense>
      </div>
    </div>
  )
}

function ResetPasswordPageSkeleton() {
  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white shadow-md rounded-lg p-6 space-y-4 animate-pulse">
        <div className="text-center space-y-2">
          <div className="h-8 bg-gray-200 rounded w-3/4 mx-auto"></div>
          <div className="h-4 bg-gray-200 rounded w-full"></div>
        </div>
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
            <div className="h-10 bg-gray-200 rounded"></div>
          </div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-10 bg-gray-200 rounded"></div>
          </div>
          <div className="h-10 bg-blue-200 rounded"></div>
        </div>
      </div>
    </div>
  )
}