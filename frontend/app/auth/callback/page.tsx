/**
 * Auth Callback Page
 * Handles authentication callbacks from Supabase Auth (email confirmation, password reset, etc.)
 */

'use client'

// Force dynamic rendering to avoid static generation issues with useSearchParams
export const dynamic = 'force-dynamic'

import { useEffect, useState, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { supabase } from '@/lib/supabase'

function CallbackContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState<string>('')

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Exchange the code for a session
        const { data, error } = await supabase.auth.exchangeCodeForSession(window.location.href)

        if (error) {
          console.error('Auth callback error:', error)
          setStatus('error')
          setMessage(error.message || 'Authentication failed')
          return
        }

        if (data?.session) {
          console.log('Auth callback successful:', data.user?.email)
          setStatus('success')
          setMessage('Authentication successful! Redirecting...')

          // Get the type and next parameters
          const type = searchParams.get('type')
          const next = searchParams.get('next') || '/dashboard'

          // Handle different callback types
          switch (type) {
            case 'signup':
              setMessage('Email confirmed! Redirecting to dashboard...')
              break
            case 'recovery':
              setMessage('Password reset link verified! Redirecting to reset password...')
              router.push('/auth/reset-password')
              return
            case 'invite':
              setMessage('Invitation accepted! Redirecting to dashboard...')
              break
            default:
              setMessage('Authentication successful! Redirecting...')
              break
          }

          // Redirect after a short delay
          setTimeout(() => {
            router.push(next)
          }, 2000)
        } else {
          setStatus('error')
          setMessage('No session received from authentication')
        }
      } catch (error) {
        console.error('Unexpected auth callback error:', error)
        setStatus('error')
        setMessage('An unexpected error occurred during authentication')
      }
    }

    // Only run if we have URL parameters
    if (window.location.href.includes('code=') || window.location.href.includes('error=')) {
      handleAuthCallback()
    } else {
      // No auth parameters, redirect to sign in
      setStatus('error')
      setMessage('No authentication data received. Redirecting to sign in...')
      setTimeout(() => {
        router.push('/auth/sign-in')
      }, 2000)
    }
  }, [router, searchParams])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full mx-auto">
        <div className="bg-white shadow-md rounded-lg p-8 text-center space-y-6">
          {status === 'loading' && (
            <>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  Completing Authentication
                </h2>
                <p className="text-gray-600">
                  Please wait while we process your authentication...
                </p>
              </div>
            </>
          )}

          {status === 'success' && (
            <>
              <div className="rounded-full h-12 w-12 bg-green-100 mx-auto flex items-center justify-center">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  Authentication Successful
                </h2>
                <p className="text-gray-600">{message}</p>
              </div>
            </>
          )}

          {status === 'error' && (
            <>
              <div className="rounded-full h-12 w-12 bg-red-100 mx-auto flex items-center justify-center">
                <svg
                  className="h-6 w-6 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  Authentication Error
                </h2>
                <p className="text-gray-600 mb-4">{message}</p>
                <button
                  onClick={() => router.push('/auth/sign-in')}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Go to Sign In
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    }>
      <CallbackContent />
    </Suspense>
  )
}