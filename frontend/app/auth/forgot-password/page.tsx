/**
 * Forgot Password Page
 * Public page for password reset requests
 */

import { Metadata } from 'next'
import { ForgotPasswordForm } from '@/components/auth/forgot-password-form'

// Force dynamic rendering to avoid static generation issues with auth context
export const dynamic = 'force-dynamic'

export const metadata: Metadata = {
  title: 'Forgot Password | AiLex Ad Agent System',
  description: 'Reset your AiLex Ad Agent account password.',
}

export default function ForgotPasswordPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <ForgotPasswordForm />
      </div>
    </div>
  )
}