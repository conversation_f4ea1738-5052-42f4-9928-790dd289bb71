"use client"

import { useState, useEffect } from "react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Skeleton } from "@/components/ui/skeleton"
import { useAnalyticsReport, useAnalyticsFilters, useReportExport } from "@/hooks/use-analytics"
import { useCampaigns } from "@/hooks/use-campaigns"
import { useToast } from "@/hooks/use-toast"
import { formatCurrency, formatPercentage, formatNumber } from "../../lib/utils"
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"
import {
  TrendingUp,
  TrendingDown,
  Calendar,
  Download,
  Filter,
  BarChart3,
  PieChart as PieChartIcon,
  RefreshCw,
  Settings,
  FileText,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  Target,
} from "lucide-react"

// Mock data for when API is not available
const mockPerformanceData = [
  { date: "Jan 1", impressions: 12000, clicks: 840, conversions: 28, spend: 420, revenue: 1680 },
  { date: "Jan 2", impressions: 13500, clicks: 945, conversions: 35, spend: 472, revenue: 2100 },
  { date: "Jan 3", impressions: 11800, clicks: 826, conversions: 32, spend: 413, revenue: 1920 },
  { date: "Jan 4", impressions: 14200, clicks: 994, conversions: 41, spend: 497, revenue: 2460 },
  { date: "Jan 5", impressions: 15600, clicks: 1092, conversions: 38, spend: 546, revenue: 2280 },
  { date: "Jan 6", impressions: 13800, clicks: 966, conversions: 44, spend: 483, revenue: 2640 },
  { date: "Jan 7", impressions: 16200, clicks: 1134, conversions: 52, spend: 567, revenue: 3120 },
]

const mockCampaignPerformance = [
  { name: "Personal Injury", spend: 1250, conversions: 85, roi: 3.2 },
  { name: "Family Law", spend: 980, conversions: 62, roi: 2.8 },
  { name: "Criminal Defense", spend: 650, conversions: 28, roi: 1.9 },
  { name: "Corporate Law", spend: 1450, conversions: 98, roi: 4.1 },
]

const mockDeviceBreakdown = [
  { name: "Desktop", value: 45, color: "#3b82f6" },
  { name: "Mobile", value: 40, color: "#10b981" },
  { name: "Tablet", value: 15, color: "#f59e0b" },
]

function AnalyticsSkeleton() {
  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-3">
              <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-20 mb-2" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Chart */}
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-4 w-60" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-80 w-full" />
        </CardContent>
      </Card>
    </div>
  )
}

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState("overview")
  const { toast } = useToast()
  const { campaigns } = useCampaigns()
  const { exportReport, isExporting } = useReportExport()
  
  const {
    timeRange,
    setTimeRange,
    startDate,
    setStartDate,
    endDate,
    setEndDate,
    selectedCampaigns,
    setSelectedCampaigns,
    selectedMetrics,
    setSelectedMetrics,
    getFilters,
  } = useAnalyticsFilters()

  const filters = getFilters()
  const { report, isLoading, isError, refresh } = useAnalyticsReport("performance", filters)

  useEffect(() => {
    if (isError) {
      toast({
        title: "Error loading analytics data",
        description: "Failed to load analytics data. Using mock data for demonstration.",
        variant: "destructive",
      })
    }
  }, [isError, toast])

  // Use real data if available, otherwise fall back to mock data
  const performanceData = report?.data || mockPerformanceData
  const campaignPerformance = report?.campaign_data || mockCampaignPerformance
  const deviceBreakdown = report?.device_data || mockDeviceBreakdown

  const handleExportReport = async () => {
    try {
      if (report?.id) {
        await exportReport(report.id, "pdf")
      } else {
        toast({
          title: "Export not available",
          description: "Please generate a report first before exporting.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Export failed:", error)
    }
  }

  const calculateMetrics = (data: any[]) => {
    if (!data || data.length === 0) return { totalImpressions: 0, totalClicks: 0, totalConversions: 0, totalSpend: 0, totalRevenue: 0, conversionRate: 0, roas: 0 }
    
    const totalImpressions = data.reduce((sum, d) => sum + (d.impressions || 0), 0)
    const totalClicks = data.reduce((sum, d) => sum + (d.clicks || 0), 0)
    const totalConversions = data.reduce((sum, d) => sum + (d.conversions || 0), 0)
    const totalSpend = data.reduce((sum, d) => sum + (d.spend || 0), 0)
    const totalRevenue = data.reduce((sum, d) => sum + (d.revenue || 0), 0)
    const conversionRate = totalClicks > 0 ? totalConversions / totalClicks : 0
    const roas = totalSpend > 0 ? totalRevenue / totalSpend : 0

    return {
      totalImpressions,
      totalClicks,
      totalConversions,
      totalSpend,
      totalRevenue,
      conversionRate,
      roas,
    }
  }

  const metrics = calculateMetrics(performanceData)

  if (isLoading) {
    return (
      <DashboardLayout
        title="Analytics"
        description="Deep insights into your campaign performance"
      >
        <AnalyticsSkeleton />
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout
      title="Analytics"
      description="Deep insights into your campaign performance"
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Time Range Selector */}
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-40">
                <Calendar className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="yesterday">Yesterday</SelectItem>
                <SelectItem value="last_7_days">Last 7 days</SelectItem>
                <SelectItem value="last_30_days">Last 30 days</SelectItem>
                <SelectItem value="last_90_days">Last 90 days</SelectItem>
                <SelectItem value="custom">Custom range</SelectItem>
              </SelectContent>
            </Select>

            {timeRange === "custom" && (
              <div className="flex items-center gap-2">
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-40"
                />
                <span>to</span>
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-40"
                />
              </div>
            )}

            {/* Filters */}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  Filters
                  {(selectedCampaigns.length > 0 || selectedMetrics.length < 5) && (
                    <Badge variant="secondary" className="ml-2">
                      {selectedCampaigns.length + (selectedMetrics.length < 5 ? 1 : 0)}
                    </Badge>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80" align="start">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Campaigns</Label>
                    <div className="mt-2 space-y-2 max-h-32 overflow-y-auto">
                      {campaigns.slice(0, 10).map((campaign) => (
                        <div key={campaign.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={campaign.id}
                            checked={selectedCampaigns.includes(campaign.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedCampaigns([...selectedCampaigns, campaign.id])
                              } else {
                                setSelectedCampaigns(selectedCampaigns.filter(id => id !== campaign.id))
                              }
                            }}
                          />
                          <Label htmlFor={campaign.id} className="text-sm truncate">
                            {campaign.name}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Metrics</Label>
                    <div className="mt-2 space-y-2">
                      {["impressions", "clicks", "conversions", "cost", "revenue"].map((metric) => (
                        <div key={metric} className="flex items-center space-x-2">
                          <Checkbox
                            id={metric}
                            checked={selectedMetrics.includes(metric)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedMetrics([...selectedMetrics, metric])
                              } else {
                                setSelectedMetrics(selectedMetrics.filter(m => m !== metric))
                              }
                            }}
                          />
                          <Label htmlFor={metric} className="text-sm capitalize">
                            {metric}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            <Button
              variant="outline"
              size="sm"
              onClick={() => refresh()}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={handleExportReport}
              disabled={isExporting}
            >
              <Download className="mr-2 h-4 w-4" />
              {isExporting ? "Exporting..." : "Export Report"}
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Impressions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(metrics.totalImpressions)}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +12.3% vs previous period
                </span>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(metrics.totalClicks)}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +8.7% vs previous period
                </span>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatPercentage(metrics.conversionRate)}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-red-600 flex items-center gap-1">
                  <TrendingDown className="h-3 w-3" />
                  -2.1% vs previous period
                </span>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total ROAS</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {metrics.roas.toFixed(1)}x
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +5.4% vs previous period
                </span>
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Performance Over Time */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Performance Trends
            </CardTitle>
            <CardDescription>
              Performance metrics over the selected time period
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="impressions">Impressions</TabsTrigger>
                <TabsTrigger value="conversions">Conversions</TabsTrigger>
                <TabsTrigger value="revenue">Revenue</TabsTrigger>
              </TabsList>

              <TabsContent value="overview">
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip 
                      formatter={(value, name) => [
                        name === 'spend' || name === 'revenue' ? formatCurrency(value as number) : formatNumber(value as number),
                        name.charAt(0).toUpperCase() + name.slice(1)
                      ]}
                    />
                    <Legend />
                    <Area type="monotone" dataKey="revenue" stackId="1" stroke="#10b981" fill="#10b981" fillOpacity={0.6} />
                    <Area type="monotone" dataKey="spend" stackId="1" stroke="#ef4444" fill="#ef4444" fillOpacity={0.6} />
                  </AreaChart>
                </ResponsiveContainer>
              </TabsContent>

              <TabsContent value="impressions">
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value) => [formatNumber(value as number), "Value"]} />
                    <Legend />
                    <Line type="monotone" dataKey="impressions" stroke="#3b82f6" strokeWidth={2} />
                    <Line type="monotone" dataKey="clicks" stroke="#10b981" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </TabsContent>

              <TabsContent value="conversions">
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value) => [formatNumber(value as number), "Conversions"]} />
                    <Legend />
                    <Bar dataKey="conversions" fill="#8b5cf6" />
                  </BarChart>
                </ResponsiveContainer>
              </TabsContent>

              <TabsContent value="revenue">
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value) => [formatCurrency(value as number), "Revenue"]} />
                    <Legend />
                    <Line type="monotone" dataKey="revenue" stroke="#10b981" strokeWidth={3} />
                  </LineChart>
                </ResponsiveContainer>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Campaign Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Campaign Performance</CardTitle>
              <CardDescription>
                ROI and conversion metrics by campaign
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={campaignPerformance} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" width={100} />
                  <Tooltip 
                    formatter={(value, name) => [
                      name === 'spend' ? formatCurrency(value as number) : 
                      name === 'roi' ? `${value}x` : formatNumber(value as number),
                      name.toUpperCase()
                    ]}
                  />
                  <Legend />
                  <Bar dataKey="conversions" fill="#3b82f6" />
                  <Bar dataKey="roi" fill="#10b981" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Device Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChartIcon className="h-4 w-4" />
                Traffic by Device
              </CardTitle>
              <CardDescription>
                Click distribution across device types
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={deviceBreakdown}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {deviceBreakdown.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
              <div className="flex justify-center gap-4 mt-4">
                {deviceBreakdown.map((device) => (
                  <div key={device.name} className="flex items-center gap-2">
                    <div 
                      className="h-3 w-3 rounded-full" 
                      style={{ backgroundColor: device.color }}
                    />
                    <span className="text-sm text-muted-foreground">
                      {device.name}: {device.value}%
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* AI-Generated Insights */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-4 w-4" />
              AI-Generated Insights
            </CardTitle>
            <CardDescription>
              Key findings and recommendations from your data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  type: "improvement",
                  title: "Peak Performance Hours Identified",
                  description: "Your campaigns perform 32% better between 2-4 PM. Consider increasing bids during these hours.",
                  impact: "High",
                  icon: <TrendingUp className="h-4 w-4" />,
                },
                {
                  type: "opportunity",
                  title: "Mobile Conversion Gap",
                  description: "Mobile traffic has high engagement but 15% lower conversion rate. Landing page optimization recommended.",
                  impact: "Medium",
                  icon: <Target className="h-4 w-4" />,
                },
                {
                  type: "alert",
                  title: "Budget Underutilization",
                  description: "Some campaigns are spending only 30% of daily budget. Consider expanding keywords or increasing bids.",
                  impact: "Medium",
                  icon: <AlertTriangle className="h-4 w-4" />,
                },
                {
                  type: "success",
                  title: "Top Performing Campaign",
                  description: "Your best campaign achieved 4.1x ROI, 23% above average. Consider applying similar strategies.",
                  impact: "High",
                  icon: <CheckCircle className="h-4 w-4" />,
                },
              ].map((insight, index) => (
                <div key={index} className="rounded-lg border p-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        {insight.icon}
                        <h4 className="font-medium">{insight.title}</h4>
                        <Badge 
                          variant={
                            insight.impact === "High" ? "destructive" : 
                            insight.impact === "Medium" ? "secondary" : "outline"
                          }
                        >
                          {insight.impact} Impact
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {insight.description}
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      Apply
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}