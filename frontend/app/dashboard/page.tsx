"use client"

import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { DashboardSkeleton } from "@/components/dashboard/dashboard-skeleton"
import { useDashboardData } from "@/hooks/use-dashboard-data"
import { useToast } from "@/hooks/use-toast"
import { formatCurrency, formatNumber } from "@/lib/utils"
import {
  BarChart3,
  Bot,
  Target,
  DollarSign,
  TrendingDown,
  TrendingUp,
  Users,
  Zap,
  RefreshCw,
  AlertTriangle,
  Info,
  XCircle,
} from "lucide-react"
import { useEffect } from "react"

export default function DashboardPage() {
  const { data, isLoading, isError, error, refresh } = useDashboardData()
  const { toast } = useToast()

  useEffect(() => {
    if (isError) {
      toast({
        title: "Error loading dashboard data",
        description: "Failed to load dashboard data. Please try refreshing the page.",
        variant: "destructive",
      })
    }
  }, [isError, toast])

  if (isLoading) {
    return (
      <DashboardLayout
        title="Dashboard"
        description="Welcome back! Here's what's happening with your campaigns."
      >
        <DashboardSkeleton />
      </DashboardLayout>
    )
  }

  if (isError || !data) {
    return (
      <DashboardLayout
        title="Dashboard"
        description="Welcome back! Here's what's happening with your campaigns."
      >
        <div className="flex flex-col items-center justify-center py-12">
          <XCircle className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Unable to load dashboard</h3>
          <p className="text-muted-foreground mb-4">
            There was an error loading your dashboard data.
          </p>
          <Button onClick={() => refresh()} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Try again
          </Button>
        </div>
      </DashboardLayout>
    )
  }

  const { summary, recentCampaigns, agents, alerts } = data

  const getAlertIcon = (type: string) => {
    switch (type) {
      case "error":
        return <XCircle className="h-4 w-4 mt-0.5 text-red-500" />
      case "warning":
        return <AlertTriangle className="h-4 w-4 mt-0.5 text-yellow-500" />
      default:
        return <Info className="h-4 w-4 mt-0.5 text-blue-500" />
    }
  }

  const getBadgeVariant = (status: string) => {
    switch (status) {
      case "active":
        return "default"
      case "paused":
        return "secondary"
      default:
        return "outline"
    }
  }

  return (
    <DashboardLayout
      title="Dashboard"
      description="Welcome back! Here's what's happening with your campaigns."
    >
      <div className="space-y-6">
        {/* Refresh Button */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={() => refresh()}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(summary.totalRevenue)}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +20.1% from last month
                </span>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.activeCampaigns}</div>
              <p className="text-xs text-muted-foreground">
                {summary.totalCampaigns} total campaigns
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Conversions</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(summary.totalConversions)}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +15.3% from last month
                </span>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average ROI</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {summary.roi > 0 ? `${summary.roi}x` : "N/A"}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-red-600 flex items-center gap-1">
                  <TrendingDown className="h-3 w-3" />
                  -5.2% from last month
                </span>
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Campaigns */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Campaigns</CardTitle>
              <CardDescription>
                Your most recent campaign performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentCampaigns.length > 0 ? (
                <div className="space-y-4">
                  {recentCampaigns.map((campaign) => (
                    <div key={campaign.id} className="flex items-center justify-between">
                      <div className="space-y-1">
                        <p className="text-sm font-medium">{campaign.name}</p>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={getBadgeVariant(campaign.status)}
                            className="text-xs"
                          >
                            {campaign.status}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {formatCurrency(campaign.spend)} spent
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {campaign.conversions} conversions
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {campaign.roi > 0 ? `${campaign.roi}x ROI` : "No ROI data"}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No campaigns found</p>
                  <p className="text-xs">Create your first campaign to get started</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* AI Agent Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bot className="h-4 w-4" />
                AI Agent Status
              </CardTitle>
              <CardDescription>
                Current status of your AI optimization agents
              </CardDescription>
            </CardHeader>
            <CardContent>
              {agents.length > 0 ? (
                <div className="space-y-3">
                  {agents.map((agent, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div
                          className={`h-2 w-2 rounded-full ${
                            agent.status === "active" ? "bg-green-500" : "bg-gray-400"
                          }`}
                        />
                        <span className="text-sm font-medium">{agent.name}</span>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {agent.tasksCompleted} tasks completed
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  <Bot className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No agents configured</p>
                  <p className="text-xs">Set up AI agents to start optimization</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* System Alerts */}
        <Card>
          <CardHeader>
            <CardTitle>System Alerts</CardTitle>
            <CardDescription>
              Important notifications and system updates
            </CardDescription>
          </CardHeader>
          <CardContent>
            {alerts.length > 0 ? (
              <div className="space-y-3">
                {alerts.map((alert, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 rounded-lg border p-3"
                  >
                    {getAlertIcon(alert.type)}
                    <div className="flex-1">
                      <p className="text-sm">{alert.message}</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      View
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6 text-muted-foreground">
                <Zap className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No alerts</p>
                <p className="text-xs">All systems are running smoothly</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}