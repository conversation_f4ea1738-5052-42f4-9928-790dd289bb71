import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"

import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { AuthProvider } from "@/contexts/auth-context"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "AiLex - AI-Powered Google Ads Management",
  description: "Automated Google Ads campaign management with AI agents",
  keywords: ["Google Ads", "AI", "Marketing", "Automation", "Campaign Management"],
  authors: [{ name: "AiLex Team" }],
  creator: "<PERSON><PERSON><PERSON>",
  publisher: "AiLex",
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://ailex.app",
    title: "AiLex - AI-Powered Google Ads Management",
    description: "Automated Google Ads campaign management with AI agents",
    siteName: "AiLex",
  },
  twitter: {
    card: "summary_large_image",
    title: "AiLex - AI-Powered Google Ads Management",
    description: "Automated Google Ads campaign management with AI agents",
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <AuthProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            {children}
            <Toaster />
          </ThemeProvider>
        </AuthProvider>
      </body>
    </html>
  )
}