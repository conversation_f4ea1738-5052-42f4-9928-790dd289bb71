export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Welcome to AiLex
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            AI-Powered Google Ads Management & Optimization Platform
          </p>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-4xl mx-auto">
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mb-4">
              🚀 Development Server Ready
            </h2>
            <div className="grid md:grid-cols-2 gap-6 text-left">
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-700 dark:text-gray-300">Backend API</h3>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <li>✅ FastAPI server running on :8000</li>
                  <li>✅ Health checks operational</li>
                  <li>✅ API documentation available</li>
                  <li>✅ CORS configured for frontend</li>
                </ul>
                <a 
                  href="http://localhost:8000/docs" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
                >
                  View API Docs
                </a>
              </div>
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-700 dark:text-gray-300">Frontend</h3>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <li>✅ Next.js 14 running on :3000</li>
                  <li>✅ TypeScript strict mode enabled</li>
                  <li>✅ Tailwind CSS configured</li>
                  <li>✅ Theme provider ready</li>
                </ul>
                <div className="text-sm text-green-600 dark:text-green-400">
                  🎉 You're viewing this page successfully!
                </div>
              </div>
            </div>
            <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                <strong>Next Steps:</strong> Authentication (Clerk) temporarily disabled for development. 
                Ready to start building your Google Ads AI Agent features!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}