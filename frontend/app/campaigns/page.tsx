"use client"

import { useState, useEffect } from "react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { CampaignDialog } from "@/components/campaigns/campaign-dialog"
import { CampaignsSkeleton } from "@/components/campaigns/campaigns-skeleton"
import { useCampaigns } from "@/hooks/use-campaigns"
import { useToast } from "@/hooks/use-toast"
import { formatCurrency, formatPercentage, formatDate } from "../../lib/utils"
import type { Campaign } from "@/lib/api"
import {
  Eye,
  MoreHorizontal,
  Pause,
  Play,
  Plus,
  Search,
  Settings,
  TrendingDown,
  TrendingUp,
  Trash2,
  Zap,
  RefreshCw,
  Target,
} from "lucide-react"

function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "active":
      return "default"
    case "paused":
      return "secondary"
    case "draft":
      return "outline"
    default:
      return "outline"
  }
}

export default function CampaignsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null)
  const { toast } = useToast()

  // Get campaigns with filters
  const filters = {
    status: activeTab === "all" ? undefined : activeTab,
    search: searchQuery || undefined,
    limit: 50,
  }

  const {
    campaigns,
    isLoading,
    isError,
    isDeleting,
    refresh,
    deleteCampaign,
    startCampaign,
    pauseCampaign,
    optimizeCampaign,
  } = useCampaigns(filters)

  useEffect(() => {
    if (isError) {
      toast({
        title: "Error loading campaigns",
        description: "Failed to load campaigns. Please try refreshing the page.",
        variant: "destructive",
      })
    }
  }, [isError, toast])

  const handleEdit = (campaign: Campaign) => {
    setSelectedCampaign(campaign)
    setShowEditDialog(true)
  }

  const handleDelete = (campaign: Campaign) => {
    setSelectedCampaign(campaign)
    setShowDeleteDialog(true)
  }

  const handleDeleteConfirm = async () => {
    if (selectedCampaign) {
      try {
        await deleteCampaign(selectedCampaign.id, selectedCampaign.name)
        setShowDeleteDialog(false)
        setSelectedCampaign(null)
      } catch (error) {
        console.error("Error deleting campaign:", error)
      }
    }
  }

  const handleToggleStatus = async (campaign: Campaign) => {
    try {
      if (campaign.status === "active") {
        await pauseCampaign(campaign.id, campaign.name)
      } else {
        await startCampaign(campaign.id, campaign.name)
      }
    } catch (error) {
      console.error("Error toggling campaign status:", error)
    }
  }

  const handleOptimize = async (campaign: Campaign) => {
    try {
      await optimizeCampaign(campaign.id, campaign.name)
    } catch (error) {
      console.error("Error optimizing campaign:", error)
    }
  }

  const filteredCampaigns = campaigns.filter(campaign => {
    if (activeTab !== "all" && campaign.status !== activeTab) return false
    if (searchQuery && !campaign.name.toLowerCase().includes(searchQuery.toLowerCase())) return false
    return true
  })

  const campaignStats = {
    total: campaigns.length,
    active: campaigns.filter(c => c.status === "active").length,
    paused: campaigns.filter(c => c.status === "paused").length,
    draft: campaigns.filter(c => c.status === "draft").length,
    totalSpend: campaigns.reduce((sum, c) => sum + (c.budget_amount || 0), 0),
    // These would come from actual metrics in a real implementation
    totalConversions: Math.floor(Math.random() * 500),
    averageROI: 2.8,
  }

  if (isLoading) {
    return (
      <DashboardLayout
        title="Campaigns"
        description="Manage and monitor your Google Ads campaigns"
      >
        <CampaignsSkeleton />
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout
      title="Campaigns"
      description="Manage and monitor your Google Ads campaigns"
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search campaigns..."
                className="w-64 pl-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => refresh()}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            New Campaign
          </Button>
        </div>

        {/* Campaign Stats Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{campaignStats.total}</div>
              <p className="text-xs text-muted-foreground">
                {campaignStats.active} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(campaignStats.totalSpend)}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +12.5% vs last month
                </span>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Conversions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {campaignStats.totalConversions}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +18.2% vs last month
                </span>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Average ROI</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {campaignStats.averageROI}x
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-red-600 flex items-center gap-1">
                  <TrendingDown className="h-3 w-3" />
                  -3.1% vs last month
                </span>
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Campaigns Table */}
        <Card>
          <CardHeader>
            <CardTitle>All Campaigns</CardTitle>
            <CardDescription>
              Monitor performance and manage your campaigns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList>
                <TabsTrigger value="all">All Campaigns ({campaignStats.total})</TabsTrigger>
                <TabsTrigger value="active">Active ({campaignStats.active})</TabsTrigger>
                <TabsTrigger value="paused">Paused ({campaignStats.paused})</TabsTrigger>
                <TabsTrigger value="draft">Draft ({campaignStats.draft})</TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="space-y-4">
                {filteredCampaigns.length > 0 ? (
                  <div className="rounded-md border">
                    <div className="grid grid-cols-12 gap-4 p-4 text-sm font-medium border-b bg-muted/50">
                      <div className="col-span-3">Campaign</div>
                      <div className="col-span-1">Status</div>
                      <div className="col-span-1 text-right">Budget</div>
                      <div className="col-span-1 text-right">Type</div>
                      <div className="col-span-2 text-right">Locations</div>
                      <div className="col-span-2 text-right">Languages</div>
                      <div className="col-span-1 text-right">Created</div>
                      <div className="col-span-1"></div>
                    </div>

                    {filteredCampaigns.map((campaign) => (
                      <div
                        key={campaign.id}
                        className="grid grid-cols-12 gap-4 p-4 text-sm border-b last:border-b-0 hover:bg-muted/25"
                      >
                        <div className="col-span-3">
                          <div>
                            <p className="font-medium">{campaign.name}</p>
                            <p className="text-xs text-muted-foreground line-clamp-1">
                              {campaign.description || "No description"}
                            </p>
                          </div>
                        </div>
                        <div className="col-span-1">
                          <Badge variant={getStatusBadgeVariant(campaign.status) as any}>
                            {campaign.status}
                          </Badge>
                        </div>
                        <div className="col-span-1 text-right">
                          {formatCurrency(campaign.budget_amount)}
                        </div>
                        <div className="col-span-1 text-right capitalize">
                          {campaign.type}
                        </div>
                        <div className="col-span-2 text-right text-xs">
                          {campaign.target_locations.slice(0, 2).join(", ")}
                          {campaign.target_locations.length > 2 && ` +${campaign.target_locations.length - 2}`}
                        </div>
                        <div className="col-span-2 text-right text-xs">
                          {campaign.target_languages.slice(0, 2).join(", ")}
                          {campaign.target_languages.length > 2 && ` +${campaign.target_languages.length - 2}`}
                        </div>
                        <div className="col-span-1 text-right text-xs">
                          {campaign.created_at ? formatDate(campaign.created_at) : "N/A"}
                        </div>
                        <div className="col-span-1 flex justify-end">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleEdit(campaign)}>
                                <Settings className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleToggleStatus(campaign)}>
                                {campaign.status === "active" ? (
                                  <>
                                    <Pause className="mr-2 h-4 w-4" />
                                    Pause
                                  </>
                                ) : (
                                  <>
                                    <Play className="mr-2 h-4 w-4" />
                                    Start
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleOptimize(campaign)}>
                                <Zap className="mr-2 h-4 w-4" />
                                Optimize
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDelete(campaign)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Target className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                    <h3 className="text-lg font-semibold mb-2">No campaigns found</h3>
                    <p className="text-muted-foreground mb-4">
                      {searchQuery
                        ? `No campaigns match "${searchQuery}"`
                        : activeTab === "all"
                        ? "You haven't created any campaigns yet."
                        : `No ${activeTab} campaigns found.`}
                    </p>
                    {!searchQuery && activeTab === "all" && (
                      <Button onClick={() => setShowCreateDialog(true)}>
                        <Plus className="mr-2 h-4 w-4" />
                        Create your first campaign
                      </Button>
                    )}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Create Campaign Dialog */}
        <CampaignDialog
          open={showCreateDialog}
          onOpenChange={setShowCreateDialog}
          mode="create"
        />

        {/* Edit Campaign Dialog */}
        <CampaignDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          campaign={selectedCampaign || undefined}
          mode="edit"
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the campaign{" "}
                <strong>"{selectedCampaign?.name}"</strong> and remove all associated data.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteConfirm}
                className="bg-red-600 hover:bg-red-700"
                disabled={isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete Campaign"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </DashboardLayout>
  )
}