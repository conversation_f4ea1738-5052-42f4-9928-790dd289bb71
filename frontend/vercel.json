{"buildCommand": "pnpm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "pnpm install", "devCommand": "pnpm run dev", "cleanUrls": true, "trailingSlash": false, "regions": ["iad1"], "functions": {"app/api/**/*.js": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "X-Requested-With, Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/backend/(.*)", "destination": "https://ailex-ad-agent-backend.fly.dev/api/v1/$1"}, {"source": "/health", "destination": "/api/health"}], "redirects": [{"source": "/login", "destination": "/sign-in", "permanent": false}, {"source": "/register", "destination": "/sign-up", "permanent": false}, {"source": "/dashboard", "destination": "/dashboard", "permanent": false}], "env": {"NEXT_PUBLIC_APP_NAME": "AiLex Ad Agent System", "NEXT_PUBLIC_APP_VERSION": "1.0.0"}, "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1"}}}