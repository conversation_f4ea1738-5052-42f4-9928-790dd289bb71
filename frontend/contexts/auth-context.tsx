'use client'

/**
 * Authentication context provider for the AiLex Ad Agent System.
 * Manages user authentication state using Supabase Auth.
 */

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'

interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  signUp: (email: string, password: string, metadata?: Record<string, any>) => Promise<{ user: User | null; error: AuthError | null }>
  signIn: (email: string, password: string) => Promise<{ user: User | null; error: AuthError | null }>
  signOut: () => Promise<{ error: AuthError | null }>
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>
  updatePassword: (password: string) => Promise<{ error: AuthError | null }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        console.error('Error getting initial session:', error)
      } else {
        setSession(session)
        setUser(session?.user ?? null)
      }
      
      setLoading(false)
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session)
        
        setSession(session)
        setUser(session?.user ?? null)
        setLoading(false)

        // Handle specific auth events
        if (event === 'SIGNED_IN') {
          // Redirect to dashboard or intended page
          console.log('User signed in:', session?.user?.email)
        } else if (event === 'SIGNED_OUT') {
          // Clear any cached data
          console.log('User signed out')
        } else if (event === 'PASSWORD_RECOVERY') {
          // Handle password recovery
          console.log('Password recovery initiated')
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signUp = async (
    email: string, 
    password: string, 
    metadata?: Record<string, any>
  ) => {
    try {
      setLoading(true)
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata || {},
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) {
        console.error('Sign up error:', error)
        return { user: null, error }
      }

      console.log('Sign up successful:', data.user?.email)
      return { user: data.user, error: null }
    } catch (error) {
      console.error('Unexpected sign up error:', error)
      return { 
        user: null, 
        error: { 
          message: 'An unexpected error occurred during sign up',
          name: 'UnexpectedError',
          status: 500
        } as AuthError 
      }
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true)
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error('Sign in error:', error)
        return { user: null, error }
      }

      console.log('Sign in successful:', data.user?.email)
      return { user: data.user, error: null }
    } catch (error) {
      console.error('Unexpected sign in error:', error)
      return { 
        user: null, 
        error: { 
          message: 'An unexpected error occurred during sign in',
          name: 'UnexpectedError',
          status: 500
        } as AuthError 
      }
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    try {
      setLoading(true)
      
      const { error } = await supabase.auth.signOut()

      if (error) {
        console.error('Sign out error:', error)
        return { error }
      }

      console.log('Sign out successful')
      return { error: null }
    } catch (error) {
      console.error('Unexpected sign out error:', error)
      return { 
        error: { 
          message: 'An unexpected error occurred during sign out',
          name: 'UnexpectedError',
          status: 500
        } as AuthError 
      }
    } finally {
      setLoading(false)
    }
  }

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })

      if (error) {
        console.error('Password reset error:', error)
        return { error }
      }

      console.log('Password reset email sent to:', email)
      return { error: null }
    } catch (error) {
      console.error('Unexpected password reset error:', error)
      return { 
        error: { 
          message: 'An unexpected error occurred during password reset',
          name: 'UnexpectedError',
          status: 500
        } as AuthError 
      }
    }
  }

  const updatePassword = async (password: string) => {
    try {
      setLoading(true)
      
      const { error } = await supabase.auth.updateUser({
        password,
      })

      if (error) {
        console.error('Password update error:', error)
        return { error }
      }

      console.log('Password updated successfully')
      return { error: null }
    } catch (error) {
      console.error('Unexpected password update error:', error)
      return { 
        error: { 
          message: 'An unexpected error occurred during password update',
          name: 'UnexpectedError',
          status: 500
        } as AuthError 
      }
    } finally {
      setLoading(false)
    }
  }

  const value: AuthContextType = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updatePassword,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Hook for checking if user is authenticated
export const useAuthGuard = () => {
  const { user, loading } = useAuth()
  
  return {
    isAuthenticated: !!user,
    isLoading: loading,
    user,
  }
}

// Hook for requiring authentication (redirects if not authenticated)
export const useRequireAuth = () => {
  const { user, loading } = useAuth()
  
  useEffect(() => {
    if (!loading && !user) {
      // Redirect to sign in page
      window.location.href = '/auth/sign-in'
    }
  }, [user, loading])

  return {
    isAuthenticated: !!user,
    isLoading: loading,
    user,
  }
}