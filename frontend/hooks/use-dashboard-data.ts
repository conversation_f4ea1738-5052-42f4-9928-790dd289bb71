"use client"

import useS<PERSON> from "swr"
import { apiClient } from "@/lib/api"

interface DashboardData {
  summary: {
    totalCampaigns: number
    activeCampaigns: number
    totalSpend: number
    totalConversions: number
    averageCAC: number
    totalRevenue: number
    roi: number
  }
  recentCampaigns: Array<{
    id: string
    name: string
    status: string
    spend: number
    conversions: number
    roi: number
  }>
  agents: Array<{
    name: string
    status: string
    tasksCompleted: number
  }>
  alerts: Array<{
    type: string
    message: string
  }>
}

const fetcher = async (): Promise<DashboardData> => {
  try {
    const [dashboardResponse, campaignResponse, agentResponse] = await Promise.all([
      apiClient.getDashboardData({ time_range: "last_30_days" }),
      apiClient.getCampaigns({ limit: 3, status: "active" }),
      apiClient.getAgents({ limit: 8 })
    ])

    // Transform the API data to match our interface
    const campaigns = campaignResponse.data || []
    const agents = agentResponse.data || []
    
    // Calculate summary metrics from dashboard data
    const summary = dashboardResponse.data?.summary || {
      totalCampaigns: campaigns.length,
      activeCampaigns: campaigns.filter(c => c.status === 'active').length,
      totalSpend: 0,
      totalConversions: 0,
      averageCAC: 0,
      totalRevenue: 0,
      roi: 0,
    }

    // Transform campaigns for display
    const recentCampaigns = campaigns.map(campaign => ({
      id: campaign.id,
      name: campaign.name,
      status: campaign.status,
      spend: 0, // This would come from campaign metrics
      conversions: 0, // This would come from campaign metrics
      roi: 0, // This would come from campaign metrics
    }))

    // Transform agents for display
    const transformedAgents = agents.map(agent => ({
      name: agent.name,
      status: agent.status,
      tasksCompleted: Math.floor(Math.random() * 50), // Placeholder until we have real metrics
    }))

    // Mock alerts for now
    const alerts = [
      {
        type: "warning",
        message: "Campaign approaching daily budget limit",
      },
      {
        type: "info", 
        message: "New optimization suggestions available",
      },
    ]

    return {
      summary,
      recentCampaigns,
      agents: transformedAgents,
      alerts,
    }
  } catch (error) {
    console.error("Error fetching dashboard data:", error)
    // Return fallback data in case of error
    return {
      summary: {
        totalCampaigns: 0,
        activeCampaigns: 0,
        totalSpend: 0,
        totalConversions: 0,
        averageCAC: 0,
        totalRevenue: 0,
        roi: 0,
      },
      recentCampaigns: [],
      agents: [],
      alerts: [],
    }
  }
}

export function useDashboardData() {
  const { data, error, isLoading, mutate } = useSWR<DashboardData>(
    "/dashboard",
    fetcher,
    {
      refreshInterval: 30000, // Refresh every 30 seconds
      revalidateOnFocus: true,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  )

  return {
    data,
    isLoading,
    isError: !!error,
    error,
    refresh: mutate,
  }
}