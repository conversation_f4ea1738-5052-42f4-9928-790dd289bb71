"use client"

import use<PERSON><PERSON> from "swr"
import { apiClient, type Campaign } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import { useState } from "react"

interface CampaignFilters {
  status?: string
  campaign_type?: string
  search?: string
  skip?: number
  limit?: number
}

export function useCampaigns(filters: CampaignFilters = {}) {
  const { toast } = useToast()
  const [isCreating, setIsCreating] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const { data, error, isLoading, mutate } = useSWR(
    ["/campaigns", filters],
    () => apiClient.getCampaigns(filters),
    {
      refreshInterval: 30000,
      revalidateOnFocus: true,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  )

  const createCampaign = async (campaignData: Omit<Campaign, "id">) => {
    setIsCreating(true)
    try {
      const response = await apiClient.createCampaign(campaignData)
      if (response.success) {
        toast({
          title: "Campaign created",
          description: `Campaign "${campaignData.name}" has been created successfully.`,
        })
        await mutate() // Refresh the campaigns list
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to create campaign"
      toast({
        title: "Error creating campaign",
        description: message,
        variant: "destructive",
      })
      throw error
    } finally {
      setIsCreating(false)
    }
  }

  const updateCampaign = async (id: string, updates: Partial<Campaign>) => {
    setIsUpdating(true)
    try {
      const response = await apiClient.updateCampaign(id, updates)
      if (response.success) {
        toast({
          title: "Campaign updated",
          description: "Campaign has been updated successfully.",
        })
        await mutate() // Refresh the campaigns list
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to update campaign"
      toast({
        title: "Error updating campaign",
        description: message,
        variant: "destructive",
      })
      throw error
    } finally {
      setIsUpdating(false)
    }
  }

  const deleteCampaign = async (id: string, name: string) => {
    setIsDeleting(true)
    try {
      await apiClient.deleteCampaign(id)
      toast({
        title: "Campaign deleted",
        description: `Campaign "${name}" has been deleted successfully.`,
      })
      await mutate() // Refresh the campaigns list
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to delete campaign"
      toast({
        title: "Error deleting campaign",
        description: message,
        variant: "destructive",
      })
      throw error
    } finally {
      setIsDeleting(false)
    }
  }

  const startCampaign = async (id: string, name: string) => {
    try {
      const response = await apiClient.startCampaign(id)
      if (response.success) {
        toast({
          title: "Campaign started",
          description: `Campaign "${name}" has been started successfully.`,
        })
        await mutate() // Refresh the campaigns list
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to start campaign"
      toast({
        title: "Error starting campaign",
        description: message,
        variant: "destructive",
      })
      throw error
    }
  }

  const pauseCampaign = async (id: string, name: string) => {
    try {
      const response = await apiClient.pauseCampaign(id)
      if (response.success) {
        toast({
          title: "Campaign paused",
          description: `Campaign "${name}" has been paused successfully.`,
        })
        await mutate() // Refresh the campaigns list
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to pause campaign"
      toast({
        title: "Error pausing campaign",
        description: message,
        variant: "destructive",
      })
      throw error
    }
  }

  const optimizeCampaign = async (id: string, name: string) => {
    try {
      const response = await apiClient.optimizeCampaign(id)
      if (response.success) {
        toast({
          title: "Campaign optimization started",
          description: `Optimization has been started for campaign "${name}".`,
        })
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to optimize campaign"
      toast({
        title: "Error optimizing campaign",
        description: message,
        variant: "destructive",
      })
      throw error
    }
  }

  return {
    campaigns: data?.data || [],
    pagination: data?.pagination,
    isLoading,
    isError: !!error,
    error,
    isCreating,
    isUpdating,
    isDeleting,
    refresh: mutate,
    createCampaign,
    updateCampaign,
    deleteCampaign,
    startCampaign,
    pauseCampaign,
    optimizeCampaign,
  }
}

export function useCampaign(id: string) {
  const { toast } = useToast()

  const { data, error, isLoading, mutate } = useSWR(
    id ? `/campaigns/${id}` : null,
    () => apiClient.getCampaign(id),
    {
      revalidateOnFocus: true,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  )

  return {
    campaign: data?.data,
    isLoading,
    isError: !!error,
    error,
    refresh: mutate,
  }
}