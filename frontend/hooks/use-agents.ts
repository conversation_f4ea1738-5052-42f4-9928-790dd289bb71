"use client"

import use<PERSON><PERSON> from "swr"
import { apiClient, type Agent } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import { useState } from "react"

interface AgentFilters {
  status?: string
  agent_type?: string
  campaign_id?: string
  skip?: number
  limit?: number
}

export function useAgents(filters: AgentFilters = {}) {
  const { toast } = useToast()
  const [isCreating, setIsCreating] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const { data, error, isLoading, mutate } = useSWR(
    ["/agents", filters],
    () => apiClient.getAgents(filters),
    {
      refreshInterval: 10000, // Refresh every 10 seconds for real-time monitoring
      revalidateOnFocus: true,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  )

  const createAgent = async (agentData: Omit<Agent, "id">) => {
    setIsCreating(true)
    try {
      const response = await apiClient.createAgent(agentData)
      if (response.success) {
        toast({
          title: "Agent created",
          description: `Agent "${agentData.name}" has been created successfully.`,
        })
        await mutate() // Refresh the agents list
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to create agent"
      toast({
        title: "Error creating agent",
        description: message,
        variant: "destructive",
      })
      throw error
    } finally {
      setIsCreating(false)
    }
  }

  const updateAgent = async (id: string, updates: Partial<Agent>) => {
    setIsUpdating(true)
    try {
      const response = await apiClient.updateAgent(id, updates)
      if (response.success) {
        toast({
          title: "Agent updated",
          description: "Agent has been updated successfully.",
        })
        await mutate() // Refresh the agents list
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to update agent"
      toast({
        title: "Error updating agent",
        description: message,
        variant: "destructive",
      })
      throw error
    } finally {
      setIsUpdating(false)
    }
  }

  const deleteAgent = async (id: string, name: string) => {
    setIsDeleting(true)
    try {
      await apiClient.deleteAgent(id)
      toast({
        title: "Agent deleted",
        description: `Agent "${name}" has been deleted successfully.`,
      })
      await mutate() // Refresh the agents list
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to delete agent"
      toast({
        title: "Error deleting agent",
        description: message,
        variant: "destructive",
      })
      throw error
    } finally {
      setIsDeleting(false)
    }
  }

  const startAgent = async (id: string, name: string) => {
    try {
      const response = await apiClient.startAgent(id)
      if (response.success) {
        toast({
          title: "Agent started",
          description: `Agent "${name}" has been started successfully.`,
        })
        await mutate() // Refresh the agents list
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to start agent"
      toast({
        title: "Error starting agent",
        description: message,
        variant: "destructive",
      })
      throw error
    }
  }

  const stopAgent = async (id: string, name: string) => {
    try {
      const response = await apiClient.stopAgent(id)
      if (response.success) {
        toast({
          title: "Agent stopped",
          description: `Agent "${name}" has been stopped successfully.`,
        })
        await mutate() // Refresh the agents list
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to stop agent"
      toast({
        title: "Error stopping agent",
        description: message,
        variant: "destructive",
      })
      throw error
    }
  }

  const assignTask = async (id: string, name: string, taskData: any) => {
    try {
      const response = await apiClient.assignTask(id, taskData)
      if (response.success) {
        toast({
          title: "Task assigned",
          description: `Task has been assigned to agent "${name}".`,
        })
        await mutate() // Refresh the agents list
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to assign task"
      toast({
        title: "Error assigning task",
        description: message,
        variant: "destructive",
      })
      throw error
    }
  }

  return {
    agents: data?.data || [],
    pagination: data?.pagination,
    isLoading,
    isError: !!error,
    error,
    isCreating,
    isUpdating,
    isDeleting,
    refresh: mutate,
    createAgent,
    updateAgent,
    deleteAgent,
    startAgent,
    stopAgent,
    assignTask,
  }
}

export function useAgent(id: string) {
  const { toast } = useToast()

  const { data, error, isLoading, mutate } = useSWR(
    id ? `/agents/${id}` : null,
    () => apiClient.getAgent(id),
    {
      refreshInterval: 5000, // Refresh every 5 seconds for detailed monitoring
      revalidateOnFocus: true,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  )

  return {
    agent: data?.data,
    isLoading,
    isError: !!error,
    error,
    refresh: mutate,
  }
}

export function useAgentTasks(id: string, params?: { skip?: number; limit?: number }) {
  const { data, error, isLoading, mutate } = useSWR(
    id ? [`/agents/${id}/tasks`, params] : null,
    () => apiClient.getAgentTasks(id, params),
    {
      refreshInterval: 5000,
      revalidateOnFocus: true,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  )

  return {
    tasks: data?.data || [],
    pagination: data?.pagination,
    isLoading,
    isError: !!error,
    error,
    refresh: mutate,
  }
}

export function useAgentMetrics(id: string) {
  const { data, error, isLoading, mutate } = useSWR(
    id ? `/agents/${id}/metrics` : null,
    () => apiClient.getAgentMetrics(id),
    {
      refreshInterval: 10000, // Refresh every 10 seconds
      revalidateOnFocus: true,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  )

  return {
    metrics: data?.data,
    isLoading,
    isError: !!error,
    error,
    refresh: mutate,
  }
}