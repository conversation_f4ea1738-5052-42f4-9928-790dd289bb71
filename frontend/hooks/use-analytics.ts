"use client"

import use<PERSON><PERSON> from "swr"
import { apiClient } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import { useState } from "react"

interface AnalyticsFilters {
  campaign_id?: string
  start_date?: string
  end_date?: string
  time_range?: string
  metrics?: string[]
  granularity?: string
}

export function useAnalyticsReport(reportType: string, params?: AnalyticsFilters) {
  const { toast } = useToast()

  const { data, error, isLoading, mutate } = useSWR(
    ["/analytics/reports", reportType, params],
    () => apiClient.generateReport(reportType, params),
    {
      refreshInterval: 60000, // Refresh every minute
      revalidateOnFocus: true,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  )

  return {
    report: data?.data,
    isLoading,
    isError: !!error,
    error,
    refresh: mutate,
  }
}

export function useCampaignMetrics(campaignId: string, params?: {
  start_date?: string
  end_date?: string
  time_range?: string
}) {
  const { data, error, isLoading, mutate } = useSWR(
    campaignId ? ["/analytics/campaigns", campaignId, "metrics", params] : null,
    () => apiClient.getCampaignMetrics(campaignId, params),
    {
      refreshInterval: 60000,
      revalidateOnFocus: true,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  )

  return {
    metrics: data?.data,
    isLoading,
    isError: !!error,
    error,
    refresh: mutate,
  }
}

export function useCampaignInsights(campaignId: string, insightTypes?: string[]) {
  const { data, error, isLoading, mutate } = useSWR(
    campaignId ? ["/analytics/campaigns", campaignId, "insights", insightTypes] : null,
    () => apiClient.getCampaignInsights(campaignId, insightTypes),
    {
      refreshInterval: 300000, // Refresh every 5 minutes
      revalidateOnFocus: true,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  )

  return {
    insights: data?.data,
    isLoading,
    isError: !!error,
    error,
    refresh: mutate,
  }
}

export function useOptimizationSuggestions(campaignId: string, params?: {
  priority?: string
  category?: string
}) {
  const { data, error, isLoading, mutate } = useSWR(
    campaignId ? ["/analytics/campaigns", campaignId, "optimization", params] : null,
    () => apiClient.getOptimizationSuggestions(campaignId, params),
    {
      refreshInterval: 300000, // Refresh every 5 minutes
      revalidateOnFocus: true,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  )

  return {
    suggestions: data?.data,
    isLoading,
    isError: !!error,
    error,
    refresh: mutate,
  }
}

export function useAnalyticsDashboard(params?: {
  time_range?: string
  campaign_ids?: string[]
}) {
  const { data, error, isLoading, mutate } = useSWR(
    ["/analytics/dashboard", params],
    () => apiClient.getDashboardData(params),
    {
      refreshInterval: 60000, // Refresh every minute
      revalidateOnFocus: true,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  )

  return {
    dashboardData: data?.data,
    isLoading,
    isError: !!error,
    error,
    refresh: mutate,
  }
}

export function useReportExport() {
  const [isExporting, setIsExporting] = useState(false)
  const { toast } = useToast()

  const exportReport = async (reportId: string, format: string = "pdf") => {
    setIsExporting(true)
    try {
      const response = await apiClient.exportReport(reportId, format)
      if (response.success) {
        toast({
          title: "Report exported",
          description: `Report has been exported as ${format.toUpperCase()}.`,
        })
        
        // In a real implementation, you would handle the file download here
        // For now, we'll just show a success message
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to export report"
      toast({
        title: "Export failed",
        description: message,
        variant: "destructive",
      })
      throw error
    } finally {
      setIsExporting(false)
    }
  }

  return {
    exportReport,
    isExporting,
  }
}

// Hook for managing date ranges and filters
export function useAnalyticsFilters() {
  const [timeRange, setTimeRange] = useState<string>("last_7_days")
  const [startDate, setStartDate] = useState<string>("")
  const [endDate, setEndDate] = useState<string>("")
  const [selectedCampaigns, setSelectedCampaigns] = useState<string[]>([])
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([
    "impressions",
    "clicks",
    "conversions",
    "cost",
    "revenue"
  ])

  const getDateRange = () => {
    const now = new Date()
    let start = new Date()
    
    switch (timeRange) {
      case "today":
        start = new Date(now)
        break
      case "yesterday":
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case "last_7_days":
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case "last_30_days":
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case "last_90_days":
        start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case "custom":
        return {
          start_date: startDate,
          end_date: endDate,
        }
      default:
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    }
    
    return {
      start_date: start.toISOString().split('T')[0],
      end_date: now.toISOString().split('T')[0],
    }
  }

  const getFilters = (): AnalyticsFilters => {
    const dateRange = getDateRange()
    return {
      ...dateRange,
      time_range: timeRange !== "custom" ? timeRange : undefined,
      metrics: selectedMetrics,
    }
  }

  return {
    timeRange,
    setTimeRange,
    startDate,
    setStartDate,
    endDate,
    setEndDate,
    selectedCampaigns,
    setSelectedCampaigns,
    selectedMetrics,
    setSelectedMetrics,
    getDateRange,
    getFilters,
  }
}