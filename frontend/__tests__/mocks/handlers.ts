import { http, HttpResponse } from 'msw'
import { createMockCampaign, createMockAgent, createMockApiResponse, createMockPaginatedResponse } from '../utils/test-utils'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'

export const handlers = [
  // Health check
  http.get(`${API_BASE_URL}/health/liveness`, () => {
    return HttpResponse.json({ status: 'healthy' })
  }),

  // Campaigns endpoints
  http.get(`${API_BASE_URL}/campaigns`, ({ request }) => {
    const url = new URL(request.url)
    const search = url.searchParams.get('search')
    const status = url.searchParams.get('status')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const skip = parseInt(url.searchParams.get('skip') || '0')

    let campaigns = [
      createMockCampaign({ id: '1', name: 'Campaign 1', status: 'active' }),
      createMockCampaign({ id: '2', name: 'Campaign 2', status: 'paused' }),
      createMockCampaign({ id: '3', name: 'Campaign 3', status: 'active' }),
    ]

    // Apply filters
    if (search) {
      campaigns = campaigns.filter(c => 
        c.name.toLowerCase().includes(search.toLowerCase())
      )
    }
    if (status) {
      campaigns = campaigns.filter(c => c.status === status)
    }

    // Apply pagination
    const paginatedCampaigns = campaigns.slice(skip, skip + limit)

    return HttpResponse.json(
      createMockPaginatedResponse(paginatedCampaigns, {
        pagination: {
          page: Math.floor(skip / limit) + 1,
          limit,
          total: campaigns.length,
          pages: Math.ceil(campaigns.length / limit),
        },
      })
    )
  }),

  http.get(`${API_BASE_URL}/campaigns/:id`, ({ params }) => {
    const { id } = params
    const campaign = createMockCampaign({ id: id as string })
    return HttpResponse.json(createMockApiResponse(campaign))
  }),

  http.post(`${API_BASE_URL}/campaigns`, async ({ request }) => {
    const body = await request.json() as any
    const newCampaign = createMockCampaign({ 
      id: Date.now().toString(), 
      ...body 
    })
    return HttpResponse.json(createMockApiResponse(newCampaign), { status: 201 })
  }),

  http.patch(`${API_BASE_URL}/campaigns/:id`, async ({ params, request }) => {
    const { id } = params
    const body = await request.json() as any
    const updatedCampaign = createMockCampaign({ 
      id: id as string, 
      ...body 
    })
    return HttpResponse.json(createMockApiResponse(updatedCampaign))
  }),

  http.delete(`${API_BASE_URL}/campaigns/:id`, ({ params }) => {
    const { id } = params
    return HttpResponse.json(createMockApiResponse({ id }))
  }),

  http.post(`${API_BASE_URL}/campaigns/:id/start`, ({ params }) => {
    const { id } = params
    const campaign = createMockCampaign({ id: id as string, status: 'active' })
    return HttpResponse.json(createMockApiResponse(campaign))
  }),

  http.post(`${API_BASE_URL}/campaigns/:id/pause`, ({ params }) => {
    const { id } = params
    const campaign = createMockCampaign({ id: id as string, status: 'paused' })
    return HttpResponse.json(createMockApiResponse(campaign))
  }),

  http.post(`${API_BASE_URL}/campaigns/:id/optimize`, ({ params }) => {
    const { id } = params
    return HttpResponse.json(createMockApiResponse({ 
      id: id as string, 
      optimization_started: true 
    }))
  }),

  // Agents endpoints
  http.get(`${API_BASE_URL}/agents`, ({ request }) => {
    const url = new URL(request.url)
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const skip = parseInt(url.searchParams.get('skip') || '0')

    const agents = [
      createMockAgent({ id: '1', name: 'Keyword Research Agent', type: 'keyword_research' }),
      createMockAgent({ id: '2', name: 'Ad Copy Agent', type: 'ad_copy_generation' }),
      createMockAgent({ id: '3', name: 'Bid Management Agent', type: 'bid_optimization' }),
    ]

    const paginatedAgents = agents.slice(skip, skip + limit)

    return HttpResponse.json(
      createMockPaginatedResponse(paginatedAgents, {
        pagination: {
          page: Math.floor(skip / limit) + 1,
          limit,
          total: agents.length,
          pages: Math.ceil(agents.length / limit),
        },
      })
    )
  }),

  http.get(`${API_BASE_URL}/agents/:id`, ({ params }) => {
    const { id } = params
    const agent = createMockAgent({ id: id as string })
    return HttpResponse.json(createMockApiResponse(agent))
  }),

  http.post(`${API_BASE_URL}/agents`, async ({ request }) => {
    const body = await request.json() as any
    const newAgent = createMockAgent({ 
      id: Date.now().toString(), 
      ...body 
    })
    return HttpResponse.json(createMockApiResponse(newAgent), { status: 201 })
  }),

  // Analytics endpoints
  http.get(`${API_BASE_URL}/analytics/dashboard`, () => {
    return HttpResponse.json(createMockApiResponse({
      totalSpend: 15430.25,
      totalImpressions: 125000,
      totalClicks: 8750,
      averageCtr: 0.07,
      averageCpc: 1.76,
      conversionRate: 0.035,
      totalConversions: 306,
      roas: 4.2,
    }))
  }),

  http.get(`${API_BASE_URL}/analytics/performance`, ({ request }) => {
    const url = new URL(request.url)
    const period = url.searchParams.get('period') || '30d'
    
    // Generate mock performance data based on period
    const dataPoints = period === '7d' ? 7 : period === '30d' ? 30 : 90
    const performanceData = Array.from({ length: dataPoints }, (_, i) => ({
      date: new Date(Date.now() - (dataPoints - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      impressions: Math.floor(Math.random() * 5000) + 1000,
      clicks: Math.floor(Math.random() * 350) + 50,
      spend: Math.floor(Math.random() * 500) + 100,
      conversions: Math.floor(Math.random() * 15) + 2,
    }))

    return HttpResponse.json(createMockApiResponse(performanceData))
  }),

  // Error handlers for testing error states
  http.get(`${API_BASE_URL}/error/500`, () => {
    return HttpResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }),

  http.get(`${API_BASE_URL}/error/404`, () => {
    return HttpResponse.json(
      { success: false, message: 'Not found' },
      { status: 404 }
    )
  }),

  http.get(`${API_BASE_URL}/error/401`, () => {
    return HttpResponse.json(
      { success: false, message: 'Unauthorized' },
      { status: 401 }
    )
  }),

  // Slow response for testing loading states
  http.get(`${API_BASE_URL}/slow`, async () => {
    await new Promise(resolve => setTimeout(resolve, 2000))
    return HttpResponse.json(createMockApiResponse({ data: 'slow response' }))
  }),
]