import { renderHook, waitFor } from '@testing-library/react'
import { act } from 'react'
import { SWRConfig } from 'swr'
import { ReactNode } from 'react'
import { useCampaigns, useCampaign } from '@/hooks/use-campaigns'
import { server } from '../mocks/server'
import { http, HttpResponse } from 'msw'
import { createMockApiResponse, createMockCampaign, createMockPaginatedResponse } from '../utils/test-utils'

// Wrapper to provide SWR context
const createWrapper = () => {
  return ({ children }: { children: ReactNode }) => (
    <SWRConfig value={{ provider: () => new Map(), dedupingInterval: 0 }}>
      {children}
    </SWRConfig>
  )
}

// Mock the toast hook
const mockToast = jest.fn()
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: mockToast }),
}))

describe('useCampaigns', () => {
  beforeEach(() => {
    mockToast.mockClear()
  })

  describe('data fetching', () => {
    it('fetches campaigns successfully', async () => {
      const { result } = renderHook(() => useCampaigns(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.campaigns).toHaveLength(3)
      expect(result.current.campaigns[0]).toMatchObject({
        id: '1',
        name: 'Campaign 1',
        status: 'active',
      })
    })

    it('applies filters correctly', async () => {
      const { result } = renderHook(
        () => useCampaigns({ status: 'active' }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.campaigns).toHaveLength(2)
      expect(result.current.campaigns.every(c => c.status === 'active')).toBe(true)
    })

    it('handles search filter', async () => {
      const { result } = renderHook(
        () => useCampaigns({ search: 'Campaign 1' }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.campaigns).toHaveLength(1)
      expect(result.current.campaigns[0].name).toBe('Campaign 1')
    })

    it('handles pagination', async () => {
      const { result } = renderHook(
        () => useCampaigns({ limit: 2, skip: 0 }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.pagination).toMatchObject({
        page: 1,
        limit: 2,
        total: 3,
        pages: 2,
      })
    })

    it('handles API errors', async () => {
      server.use(
        http.get('*/campaigns', () => {
          return HttpResponse.json(
            { success: false, message: 'Server error' },
            { status: 500 }
          )
        })
      )

      const { result } = renderHook(() => useCampaigns(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBeDefined()
    })
  })

  describe('createCampaign', () => {
    it('creates campaign successfully', async () => {
      const { result } = renderHook(() => useCampaigns(), {
        wrapper: createWrapper(),
      })

      const newCampaignData = {
        name: 'New Campaign',
        status: 'active',
        campaign_type: 'search',
        budget: 1000,
        target_cpa: 50,
        start_date: '2024-01-01',
        end_date: '2024-12-31',
      }

      await act(async () => {
        const createdCampaign = await result.current.createCampaign(newCampaignData as any)
        expect(createdCampaign.name).toBe('New Campaign')
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Campaign created',
        description: 'Campaign "New Campaign" has been created successfully.',
      })
      expect(result.current.isCreating).toBe(false)
    })

    it('handles creation errors', async () => {
      server.use(
        http.post('*/campaigns', () => {
          return HttpResponse.json(
            { success: false, message: 'Validation error' },
            { status: 400 }
          )
        })
      )

      const { result } = renderHook(() => useCampaigns(), {
        wrapper: createWrapper(),
      })

      await act(async () => {
        try {
          await result.current.createCampaign({} as any)
        } catch (error) {
          expect(error).toBeDefined()
        }
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Error creating campaign',
        description: expect.stringContaining('error'),
        variant: 'destructive',
      })
    })

    it('sets loading state during creation', async () => {
      const { result } = renderHook(() => useCampaigns(), {
        wrapper: createWrapper(),
      })

      act(() => {
        result.current.createCampaign({} as any).catch(() => {})
      })

      expect(result.current.isCreating).toBe(true)

      await waitFor(() => {
        expect(result.current.isCreating).toBe(false)
      })
    })
  })

  describe('updateCampaign', () => {
    it('updates campaign successfully', async () => {
      const { result } = renderHook(() => useCampaigns(), {
        wrapper: createWrapper(),
      })

      await act(async () => {
        const updatedCampaign = await result.current.updateCampaign('1', {
          name: 'Updated Campaign',
        })
        expect(updatedCampaign.name).toBe('Updated Campaign')
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Campaign updated',
        description: 'Campaign has been updated successfully.',
      })
    })

    it('handles update errors', async () => {
      server.use(
        http.patch('*/campaigns/1', () => {
          return HttpResponse.json(
            { success: false, message: 'Not found' },
            { status: 404 }
          )
        })
      )

      const { result } = renderHook(() => useCampaigns(), {
        wrapper: createWrapper(),
      })

      await act(async () => {
        try {
          await result.current.updateCampaign('1', { name: 'Updated' })
        } catch (error) {
          expect(error).toBeDefined()
        }
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Error updating campaign',
        description: expect.stringContaining('error'),
        variant: 'destructive',
      })
    })
  })

  describe('deleteCampaign', () => {
    it('deletes campaign successfully', async () => {
      const { result } = renderHook(() => useCampaigns(), {
        wrapper: createWrapper(),
      })

      await act(async () => {
        await result.current.deleteCampaign('1', 'Test Campaign')
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Campaign deleted',
        description: 'Campaign "Test Campaign" has been deleted successfully.',
      })
    })

    it('handles deletion errors', async () => {
      server.use(
        http.delete('*/campaigns/1', () => {
          return HttpResponse.json(
            { success: false, message: 'Cannot delete active campaign' },
            { status: 400 }
          )
        })
      )

      const { result } = renderHook(() => useCampaigns(), {
        wrapper: createWrapper(),
      })

      await act(async () => {
        try {
          await result.current.deleteCampaign('1', 'Test Campaign')
        } catch (error) {
          expect(error).toBeDefined()
        }
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Error deleting campaign',
        description: expect.stringContaining('error'),
        variant: 'destructive',
      })
    })
  })

  describe('campaign actions', () => {
    it('starts campaign successfully', async () => {
      const { result } = renderHook(() => useCampaigns(), {
        wrapper: createWrapper(),
      })

      await act(async () => {
        const startedCampaign = await result.current.startCampaign('1', 'Test Campaign')
        expect(startedCampaign.status).toBe('active')
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Campaign started',
        description: 'Campaign "Test Campaign" has been started successfully.',
      })
    })

    it('pauses campaign successfully', async () => {
      const { result } = renderHook(() => useCampaigns(), {
        wrapper: createWrapper(),
      })

      await act(async () => {
        const pausedCampaign = await result.current.pauseCampaign('1', 'Test Campaign')
        expect(pausedCampaign.status).toBe('paused')
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Campaign paused',
        description: 'Campaign "Test Campaign" has been paused successfully.',
      })
    })

    it('optimizes campaign successfully', async () => {
      const { result } = renderHook(() => useCampaigns(), {
        wrapper: createWrapper(),
      })

      await act(async () => {
        const result_data = await result.current.optimizeCampaign('1', 'Test Campaign')
        expect(result_data.optimization_started).toBe(true)
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Campaign optimization started',
        description: 'Optimization has been started for campaign "Test Campaign".',
      })
    })
  })
})

describe('useCampaign', () => {
  beforeEach(() => {
    mockToast.mockClear()
  })

  it('fetches single campaign successfully', async () => {
    const { result } = renderHook(() => useCampaign('1'), {
      wrapper: createWrapper(),
    })

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.campaign).toMatchObject({
      id: '1',
      name: expect.any(String),
      status: expect.any(String),
    })
  })

  it('does not fetch when id is empty', () => {
    const { result } = renderHook(() => useCampaign(''), {
      wrapper: createWrapper(),
    })

    expect(result.current.campaign).toBeUndefined()
    expect(result.current.isLoading).toBe(false)
  })

  it('handles single campaign fetch errors', async () => {
    server.use(
      http.get('*/campaigns/999', () => {
        return HttpResponse.json(
          { success: false, message: 'Not found' },
          { status: 404 }
        )
      })
    )

    const { result } = renderHook(() => useCampaign('999'), {
      wrapper: createWrapper(),
    })

    await waitFor(() => {
      expect(result.current.isError).toBe(true)
    })

    expect(result.current.error).toBeDefined()
  })
})