import React, { type ReactElement, type ReactNode } from 'react'
import { render, type RenderOptions } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ThemeProvider } from 'next-themes'
import { ClerkProvider } from '@clerk/nextjs'
import { SWRConfig } from 'swr'

// Custom render function that includes all providers
const AllTheProviders = ({ children }: { children: ReactNode }) => {
  return (
    <ClerkProvider publishableKey="test-key">
      <ThemeProvider
        attribute="class"
        defaultTheme="light"
        enableSystem={false}
        disableTransitionOnChange
      >
        <SWRConfig value={{ dedupingInterval: 0, provider: () => new Map() }}>
          {children}
        </SWRConfig>
      </ThemeProvider>
    </ClerkProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => {
  return {
    user: userEvent.setup(),
    ...render(ui, { wrapper: AllTheProviders, ...options }),
  }
}

// Test data factories
export const createMockCampaign = (overrides = {}) => ({
  id: '1',
  name: 'Test Campaign',
  status: 'active',
  campaign_type: 'search',
  budget: 1000,
  target_cpa: 50,
  start_date: '2024-01-01',
  end_date: '2024-12-31',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  ...overrides,
})

export const createMockAgent = (overrides = {}) => ({
  id: '1',
  name: 'Test Agent',
  type: 'keyword_research',
  status: 'active',
  configuration: {},
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  ...overrides,
})

export const createMockApiResponse = <T,>(data: T, overrides = {}) => ({
  success: true,
  data,
  message: 'Success',
  ...overrides,
})

export const createMockPaginatedResponse = <T,>(items: T[], overrides = {}) => ({
  success: true,
  data: items,
  pagination: {
    page: 1,
    limit: 10,
    total: items.length,
    pages: 1,
    ...overrides.pagination,
  },
  message: 'Success',
  ...overrides,
})

// Custom matchers and utilities
export const waitForLoadingToFinish = async () => {
  const { waitForElementToBeRemoved } = await import('@testing-library/react')
  await waitForElementToBeRemoved(() => document.querySelector('[data-testid="loading"]'))
}

// Mock fetch response helper
export const createMockResponse = <T,>(data: T, status = 200) => {
  return Promise.resolve({
    ok: status >= 200 && status < 300,
    status,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
  } as Response)
}

// Error boundary for testing
export class TestErrorBoundary extends React.Component<
  { children: ReactNode; onError?: (error: Error) => void },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode; onError?: (error: Error) => void }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error) {
    this.props.onError?.(error)
  }

  render() {
    if (this.state.hasError) {
      return <div data-testid="error-boundary">Something went wrong.</div>
    }

    return this.props.children
  }
}

// Common assertions
export const expectElementToBeInDocument = (element: HTMLElement | null) => {
  expect(element).toBeInTheDocument()
}

export const expectElementNotToBeInDocument = (element: HTMLElement | null) => {
  expect(element).not.toBeInTheDocument()
}

// Async test helpers
export const flushPromises = () => new Promise(resolve => setTimeout(resolve, 0))

// Re-export everything from RTL
export * from '@testing-library/react'

// Override the default render export
export { customRender as render }