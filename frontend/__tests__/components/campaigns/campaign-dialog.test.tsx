import { render, screen, waitFor } from '../../utils/test-utils'
import { CampaignDialog } from '@/components/campaigns/campaign-dialog'
import { createMockCampaign } from '../../utils/test-utils'

// Mock the hook
const mockCreateCampaign = jest.fn()
const mockUpdateCampaign = jest.fn()

jest.mock('@/hooks/use-campaigns', () => ({
  useCampaigns: () => ({
    createCampaign: mockCreateCampaign,
    updateCampaign: mockUpdateCampaign,
    isCreating: false,
    isUpdating: false,
  }),
}))

describe('CampaignDialog', () => {
  const mockOnOpenChange = jest.fn()

  beforeEach(() => {
    mockCreateCampaign.mockClear()
    mockUpdateCampaign.mockClear()
    mockOnOpenChange.mockClear()
  })

  describe('rendering', () => {
    it('renders create dialog correctly', () => {
      render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      expect(screen.getByText('Create New Campaign')).toBeInTheDocument()
      expect(screen.getByText('Create a new Google Ads campaign with AI optimization.')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /create campaign/i })).toBeInTheDocument()
    })

    it('renders edit dialog correctly', () => {
      const mockCampaign = createMockCampaign({
        name: 'Test Campaign',
        type: 'search',
        budget_amount: 500,
      })

      render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="edit"
          campaign={mockCampaign}
        />
      )

      expect(screen.getByText('Edit Campaign')).toBeInTheDocument()
      expect(screen.getByText('Update your campaign settings and configuration.')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /update campaign/i })).toBeInTheDocument()
      expect(screen.getByDisplayValue('Test Campaign')).toBeInTheDocument()
    })

    it('does not render when closed', () => {
      render(
        <CampaignDialog
          open={false}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      expect(screen.queryByText('Create New Campaign')).not.toBeInTheDocument()
    })
  })

  describe('form validation', () => {
    it('shows validation errors for required fields', async () => {
      const { user } = render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      const submitButton = screen.getByRole('button', { name: /create campaign/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Campaign name is required')).toBeInTheDocument()
      })
    })

    it('validates budget amount', async () => {
      const { user } = render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      const budgetInput = screen.getByLabelText(/daily budget/i)
      await user.clear(budgetInput)
      await user.type(budgetInput, '0')

      const submitButton = screen.getByRole('button', { name: /create campaign/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Budget must be at least $1')).toBeInTheDocument()
      })
    })

    it('validates name length', async () => {
      const { user } = render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      const nameInput = screen.getByLabelText(/campaign name/i)
      await user.type(nameInput, 'a'.repeat(101))

      const submitButton = screen.getByRole('button', { name: /create campaign/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Name must be less than 100 characters')).toBeInTheDocument()
      })
    })
  })

  describe('form interaction', () => {
    it('allows adding and removing locations', async () => {
      const { user } = render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      const locationInput = screen.getByPlaceholderText(/enter location/i)
      const addLocationButton = screen.getByRole('button', { name: /add/i })

      // Add a location
      await user.type(locationInput, 'United States')
      await user.click(addLocationButton)

      await waitFor(() => {
        expect(screen.getByText('United States')).toBeInTheDocument()
      })

      // Remove the location
      const removeButton = screen.getByRole('button', { name: '' })
      await user.click(removeButton)

      await waitFor(() => {
        expect(screen.queryByText('United States')).not.toBeInTheDocument()
      })
    })

    it('allows adding locations with Enter key', async () => {
      const { user } = render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      const locationInput = screen.getByPlaceholderText(/enter location/i)
      await user.type(locationInput, 'Canada')
      await user.keyboard('{Enter}')

      await waitFor(() => {
        expect(screen.getByText('Canada')).toBeInTheDocument()
      })
    })

    it('allows adding and removing languages', async () => {
      const { user } = render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      const languageInput = screen.getByPlaceholderText(/enter language/i)
      const addButtons = screen.getAllByRole('button', { name: /add/i })
      const addLanguageButton = addButtons.find(button => 
        button.closest('div')?.querySelector('input')?.placeholder.includes('language')
      )

      await user.type(languageInput, 'English')
      if (addLanguageButton) {
        await user.click(addLanguageButton)
      }

      await waitFor(() => {
        expect(screen.getByText('English')).toBeInTheDocument()
      })
    })

    it('shows keywords section only for search campaigns', async () => {
      const { user } = render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      // Keywords should be visible for search campaigns (default)
      expect(screen.getByText('Keywords')).toBeInTheDocument()

      // Change to display campaign
      const typeSelect = screen.getByRole('combobox')
      await user.click(typeSelect)
      
      const displayOption = await screen.findByText('Display')
      await user.click(displayOption)

      await waitFor(() => {
        expect(screen.queryByText('Keywords')).not.toBeInTheDocument()
      })
    })

    it('allows adding and removing keywords for search campaigns', async () => {
      const { user } = render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      const keywordInput = screen.getByPlaceholderText(/enter keyword/i)
      const addButtons = screen.getAllByRole('button', { name: /add/i })
      const addKeywordButton = addButtons.find(button => 
        button.closest('div')?.querySelector('input')?.placeholder.includes('keyword')
      )

      await user.type(keywordInput, 'test keyword')
      if (addKeywordButton) {
        await user.click(addKeywordButton)
      }

      await waitFor(() => {
        expect(screen.getByText('test keyword')).toBeInTheDocument()
      })
    })
  })

  describe('form submission', () => {
    it('creates campaign successfully', async () => {
      mockCreateCampaign.mockResolvedValue({ id: '1', name: 'Test Campaign' })

      const { user } = render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      // Fill out required fields
      await user.type(screen.getByLabelText(/campaign name/i), 'Test Campaign')
      await user.type(screen.getByPlaceholderText(/enter location/i), 'United States')
      await user.click(screen.getAllByRole('button', { name: /add/i })[0])
      await user.type(screen.getByPlaceholderText(/enter language/i), 'English')
      await user.click(screen.getAllByRole('button', { name: /add/i })[1])

      const submitButton = screen.getByRole('button', { name: /create campaign/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockCreateCampaign).toHaveBeenCalledWith({
          name: 'Test Campaign',
          description: '',
          type: 'search',
          budget_amount: 100,
          target_locations: ['United States'],
          target_languages: ['English'],
          keywords: [],
        })
      })
    })

    it('updates campaign successfully', async () => {
      const mockCampaign = createMockCampaign({
        id: '1',
        name: 'Original Campaign',
        type: 'search',
        budget_amount: 200,
        target_locations: ['Canada'],
        target_languages: ['French'],
      })

      mockUpdateCampaign.mockResolvedValue({ ...mockCampaign, name: 'Updated Campaign' })

      const { user } = render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="edit"
          campaign={mockCampaign}
        />
      )

      const nameInput = screen.getByDisplayValue('Original Campaign')
      await user.clear(nameInput)
      await user.type(nameInput, 'Updated Campaign')

      const submitButton = screen.getByRole('button', { name: /update campaign/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockUpdateCampaign).toHaveBeenCalledWith('1', expect.objectContaining({
          name: 'Updated Campaign',
        }))
      })
    })

    it('closes dialog on successful submission', async () => {
      mockCreateCampaign.mockResolvedValue({ id: '1', name: 'Test Campaign' })

      const { user } = render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      // Fill required fields and submit
      await user.type(screen.getByLabelText(/campaign name/i), 'Test Campaign')
      await user.type(screen.getByPlaceholderText(/enter location/i), 'United States')
      await user.click(screen.getAllByRole('button', { name: /add/i })[0])
      await user.type(screen.getByPlaceholderText(/enter language/i), 'English')
      await user.click(screen.getAllByRole('button', { name: /add/i })[1])

      const submitButton = screen.getByRole('button', { name: /create campaign/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockOnOpenChange).toHaveBeenCalledWith(false)
      })
    })

    it('handles submission errors gracefully', async () => {
      mockCreateCampaign.mockRejectedValue(new Error('API Error'))

      const { user } = render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      // Fill required fields and submit
      await user.type(screen.getByLabelText(/campaign name/i), 'Test Campaign')
      await user.type(screen.getByPlaceholderText(/enter location/i), 'United States')
      await user.click(screen.getAllByRole('button', { name: /add/i })[0])
      await user.type(screen.getByPlaceholderText(/enter language/i), 'English')
      await user.click(screen.getAllByRole('button', { name: /add/i })[1])

      const submitButton = screen.getByRole('button', { name: /create campaign/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockCreateCampaign).toHaveBeenCalled()
      })

      // Dialog should remain open on error
      expect(mockOnOpenChange).not.toHaveBeenCalledWith(false)
    })
  })

  describe('cancel functionality', () => {
    it('closes dialog when cancel button is clicked', async () => {
      const { user } = render(
        <CampaignDialog
          open={true}
          onOpenChange={mockOnOpenChange}
          mode="create"
        />
      )

      const cancelButton = screen.getByRole('button', { name: /cancel/i })
      await user.click(cancelButton)

      expect(mockOnOpenChange).toHaveBeenCalledWith(false)
    })
  })
})