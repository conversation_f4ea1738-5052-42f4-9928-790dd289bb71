import { render, screen } from '../../utils/test-utils'
import { Button } from '@/components/ui/button'

describe('Button', () => {
  describe('rendering', () => {
    it('renders button with default props', () => {
      render(<Button>Click me</Button>)
      
      const button = screen.getByRole('button', { name: /click me/i })
      expect(button).toBeInTheDocument()
      expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center')
    })

    it('renders button with custom className', () => {
      render(<Button className="custom-class">Click me</Button>)
      
      const button = screen.getByRole('button', { name: /click me/i })
      expect(button).toHaveClass('custom-class')
    })

    it('renders button as child component when asChild is true', () => {
      render(
        <Button asChild>
          <a href="/test">Link Button</a>
        </Button>
      )
      
      const link = screen.getByRole('link', { name: /link button/i })
      expect(link).toBeInTheDocument()
      expect(link).toHaveAttribute('href', '/test')
    })
  })

  describe('variants', () => {
    it('renders default variant', () => {
      render(<Button>Default</Button>)
      
      const button = screen.getByRole('button', { name: /default/i })
      expect(button).toHaveClass('bg-primary', 'text-primary-foreground')
    })

    it('renders destructive variant', () => {
      render(<Button variant="destructive">Delete</Button>)
      
      const button = screen.getByRole('button', { name: /delete/i })
      expect(button).toHaveClass('bg-destructive', 'text-destructive-foreground')
    })

    it('renders outline variant', () => {
      render(<Button variant="outline">Outline</Button>)
      
      const button = screen.getByRole('button', { name: /outline/i })
      expect(button).toHaveClass('border', 'border-input', 'bg-background')
    })

    it('renders secondary variant', () => {
      render(<Button variant="secondary">Secondary</Button>)
      
      const button = screen.getByRole('button', { name: /secondary/i })
      expect(button).toHaveClass('bg-secondary', 'text-secondary-foreground')
    })

    it('renders ghost variant', () => {
      render(<Button variant="ghost">Ghost</Button>)
      
      const button = screen.getByRole('button', { name: /ghost/i })
      expect(button).toHaveClass('hover:bg-accent')
    })

    it('renders link variant', () => {
      render(<Button variant="link">Link</Button>)
      
      const button = screen.getByRole('button', { name: /link/i })
      expect(button).toHaveClass('text-primary', 'underline-offset-4')
    })
  })

  describe('sizes', () => {
    it('renders default size', () => {
      render(<Button>Default Size</Button>)
      
      const button = screen.getByRole('button', { name: /default size/i })
      expect(button).toHaveClass('h-9', 'px-4', 'py-2')
    })

    it('renders small size', () => {
      render(<Button size="sm">Small</Button>)
      
      const button = screen.getByRole('button', { name: /small/i })
      expect(button).toHaveClass('h-8', 'px-3', 'text-xs')
    })

    it('renders large size', () => {
      render(<Button size="lg">Large</Button>)
      
      const button = screen.getByRole('button', { name: /large/i })
      expect(button).toHaveClass('h-10', 'px-8')
    })

    it('renders icon size', () => {
      render(<Button size="icon">🔥</Button>)
      
      const button = screen.getByRole('button', { name: /🔥/i })
      expect(button).toHaveClass('h-9', 'w-9')
    })
  })

  describe('interaction', () => {
    it('calls onClick when clicked', async () => {
      const handleClick = jest.fn()
      const { user } = render(<Button onClick={handleClick}>Click me</Button>)
      
      const button = screen.getByRole('button', { name: /click me/i })
      await user.click(button)
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('does not call onClick when disabled', async () => {
      const handleClick = jest.fn()
      const { user } = render(
        <Button onClick={handleClick} disabled>
          Disabled Button
        </Button>
      )
      
      const button = screen.getByRole('button', { name: /disabled button/i })
      await user.click(button)
      
      expect(handleClick).not.toHaveBeenCalled()
      expect(button).toBeDisabled()
    })

    it('supports keyboard navigation', async () => {
      const handleClick = jest.fn()
      const { user } = render(<Button onClick={handleClick}>Keyboard</Button>)
      
      const button = screen.getByRole('button', { name: /keyboard/i })
      button.focus()
      await user.keyboard('{Enter}')
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('supports space key activation', async () => {
      const handleClick = jest.fn()
      const { user } = render(<Button onClick={handleClick}>Space</Button>)
      
      const button = screen.getByRole('button', { name: /space/i })
      button.focus()
      await user.keyboard(' ')
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })
  })

  describe('accessibility', () => {
    it('has proper focus styles', () => {
      render(<Button>Focus me</Button>)
      
      const button = screen.getByRole('button', { name: /focus me/i })
      expect(button).toHaveClass('focus-visible:outline-none', 'focus-visible:ring-1')
    })

    it('renders with proper button role', () => {
      render(<Button>Accessible</Button>)
      
      const button = screen.getByRole('button', { name: /accessible/i })
      expect(button).toBeInTheDocument()
    })

    it('supports aria-label', () => {
      render(<Button aria-label="Custom label">🔥</Button>)
      
      const button = screen.getByRole('button', { name: /custom label/i })
      expect(button).toBeInTheDocument()
    })

    it('supports aria-disabled when disabled', () => {
      render(<Button disabled>Disabled</Button>)
      
      const button = screen.getByRole('button', { name: /disabled/i })
      expect(button).toHaveAttribute('aria-disabled', 'true')
    })
  })

  describe('forwarding ref', () => {
    it('forwards ref to button element', () => {
      const ref = { current: null } as React.RefObject<HTMLButtonElement>
      render(<Button ref={ref}>Ref Button</Button>)
      
      expect(ref.current).toBeInstanceOf(HTMLButtonElement)
      expect(ref.current?.textContent).toBe('Ref Button')
    })
  })
})