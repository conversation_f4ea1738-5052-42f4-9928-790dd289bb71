/**
 * Test script to diagnose sign-up issues
 * Run with: node test-signup.js
 */

const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

console.log('Testing Supabase Sign-up Functionality')
console.log('=====================================')
console.log('Supabase URL:', supabaseUrl)
console.log('Anon Key:', supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'NOT SET')
console.log('')

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Please check NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in .env.local')
  process.exit(1)
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: false, // Don't persist for testing
    detectSessionInUrl: false,
    flowType: 'pkce',
  },
})

async function testSignUp() {
  try {
    console.log('🧪 Testing sign-up with test email...')
    
    const testEmail = `test-${Date.now()}@example.com`
    const testPassword = 'TestPassword123!'
    
    console.log(`📧 Email: ${testEmail}`)
    console.log(`🔒 Password: ${testPassword}`)
    console.log('')
    
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          first_name: 'Test',
          last_name: 'User',
        },
        emailRedirectTo: 'http://localhost:3000/auth/callback',
      },
    })
    
    if (error) {
      console.error('❌ Sign-up failed with error:')
      console.error('Error code:', error.status)
      console.error('Error message:', error.message)
      console.error('Full error:', JSON.stringify(error, null, 2))
      return false
    }
    
    if (data.user) {
      console.log('✅ Sign-up successful!')
      console.log('User ID:', data.user.id)
      console.log('Email:', data.user.email)
      console.log('Email confirmed:', data.user.email_confirmed_at ? 'Yes' : 'No')
      console.log('Created at:', data.user.created_at)
      console.log('User metadata:', JSON.stringify(data.user.user_metadata, null, 2))
      
      if (data.session) {
        console.log('🎫 Session created automatically')
        console.log('Access token:', data.session.access_token ? 'Present' : 'Missing')
      } else {
        console.log('📧 Email confirmation required - no session created')
      }
      
      return true
    } else {
      console.error('❌ No user data returned from sign-up')
      return false
    }
    
  } catch (error) {
    console.error('❌ Unexpected error during sign-up:')
    console.error(error)
    return false
  }
}

async function testConnection() {
  try {
    console.log('🔗 Testing Supabase connection...')
    
    // Test basic connection by getting the current session
    const { data, error } = await supabase.auth.getSession()
    
    if (error) {
      console.error('❌ Connection test failed:', error.message)
      return false
    }
    
    console.log('✅ Connection successful')
    console.log('Current session:', data.session ? 'Active' : 'None')
    return true
    
  } catch (error) {
    console.error('❌ Connection test failed with exception:', error.message)
    return false
  }
}

async function main() {
  console.log('Starting diagnostic tests...\n')
  
  // Test 1: Connection
  const connectionOk = await testConnection()
  console.log('')
  
  if (!connectionOk) {
    console.error('❌ Cannot proceed - connection test failed')
    process.exit(1)
  }
  
  // Test 2: Sign-up
  const signUpOk = await testSignUp()
  console.log('')
  
  // Summary
  console.log('Test Summary:')
  console.log('=============')
  console.log('Connection:', connectionOk ? '✅ OK' : '❌ FAILED')
  console.log('Sign-up:', signUpOk ? '✅ OK' : '❌ FAILED')
  
  if (connectionOk && signUpOk) {
    console.log('\n🎉 All tests passed! Sign-up functionality appears to be working.')
  } else {
    console.log('\n⚠️  Some tests failed. Check the errors above for details.')
  }
}

main().catch(console.error)
