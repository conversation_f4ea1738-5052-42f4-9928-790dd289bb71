{"name": "ailex-ad-agent-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --watchAll=false", "test:coverage": "jest --coverage", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "analyze": "ANALYZE=true next build"}, "dependencies": {"@supabase/supabase-js": "^2.46.1", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.460.0", "next": "14.2.31", "next-themes": "^0.4.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.61.1", "recharts": "^2.13.3", "swr": "^2.2.5", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.13"}, "devDependencies": {"@next/bundle-analyzer": "^14.2.25", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/node": "^22", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "14.2.31", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-security": "^1.7.1", "eslint-plugin-testing-library": "^6.4.0", "eslint-plugin-unused-imports": "^4.1.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.6.5", "postcss": "^8.5.0", "tailwindcss": "^3.4.15", "ts-jest": "^29.2.5", "typescript": "^5.7.3"}}