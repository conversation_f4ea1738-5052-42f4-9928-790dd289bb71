{"extends": ["next/core-web-vitals", "@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:jsx-a11y/recommended", "plugin:testing-library/react"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "react", "react-hooks", "jsx-a11y", "testing-library", "unused-imports"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "react/jsx-uses-react": "off", "@typescript-eslint/no-unused-vars": "off", "unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/explicit-module-boundary-types": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/prefer-const": "error", "@typescript-eslint/no-var-requires": "error", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "jsx-a11y/alt-text": "error", "jsx-a11y/aria-props": "error", "jsx-a11y/aria-proptypes": "error", "jsx-a11y/aria-unsupported-elements": "error", "jsx-a11y/role-has-required-aria-props": "error", "jsx-a11y/role-supports-aria-props": "error", "jsx-a11y/click-events-have-key-events": "warn", "jsx-a11y/no-static-element-interactions": "warn", "testing-library/await-async-queries": "error", "testing-library/no-await-sync-queries": "error", "testing-library/no-debugging-utils": "warn", "testing-library/no-dom-import": "error"}, "settings": {"react": {"version": "detect"}}, "overrides": [{"files": ["**/__tests__/**/*", "**/*.{test,spec}.*"], "extends": ["plugin:testing-library/react"], "rules": {"@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off"}}]}