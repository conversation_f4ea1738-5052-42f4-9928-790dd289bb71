'use client'

/**
 * Reset Password Form Component
 * Allows users to set a new password after receiving a reset link
 */

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/contexts/auth-context'
import { supabase } from '@/lib/supabase'

const resetPasswordSchema = z.object({
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
})

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>

export function ResetPasswordForm() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isValidSession, setIsValidSession] = useState<boolean | null>(null)
  
  const router = useRouter()
  const searchParams = useSearchParams()
  const { updatePassword, user } = useAuth()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
  })

  useEffect(() => {
    const handlePasswordReset = async () => {
      try {
        // Check if we have a code parameter (Supabase auth flow)
        const code = searchParams.get('code')
        const error_param = searchParams.get('error')
        const error_description = searchParams.get('error_description')

        if (error_param) {
          console.error('Auth error from URL:', error_param, error_description)
          setIsValidSession(false)
          setError(error_description || 'Authentication error occurred.')
          return
        }

        if (code) {
          // Exchange the code for a session
          console.log('Exchanging code for session...')
          const { data, error } = await supabase.auth.exchangeCodeForSession(window.location.href)

          if (error) {
            console.error('Code exchange error:', error)
            setIsValidSession(false)
            setError('Invalid or expired password reset link. Please request a new one.')
            return
          }

          if (data?.session) {
            console.log('Password reset session established for:', data.user?.email)
            setIsValidSession(true)
            // Clean up the URL by removing the code parameter
            const url = new URL(window.location.href)
            url.searchParams.delete('code')
            window.history.replaceState({}, '', url.toString())
            return
          }
        }

        // Check if we already have an active session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.error('Session check error:', sessionError)
          setIsValidSession(false)
          setError('Invalid or expired password reset link. Please request a new one.')
          return
        }

        if (session?.user) {
          // User is authenticated and can reset password
          setIsValidSession(true)
          console.log('Existing valid session for user:', session.user.email)
        } else {
          // No session and no code - invalid link
          setIsValidSession(false)
          setError('Invalid or expired password reset link. Please request a new one.')
        }
      } catch (error) {
        console.error('Unexpected password reset error:', error)
        setIsValidSession(false)
        setError('An error occurred while verifying your reset link.')
      }
    }

    handlePasswordReset()
  }, [searchParams])

  const onSubmit = async (data: ResetPasswordFormData) => {
    if (!isValidSession) {
      setError('Invalid session. Please request a new password reset link.')
      return
    }

    try {
      setIsLoading(true)
      setError(null)
      setSuccess(null)

      const { error } = await updatePassword(data.password)

      if (error) {
        setError(error.message)
        return
      }

      setSuccess('Password updated successfully! Redirecting to dashboard...')
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push('/dashboard')
      }, 2000)
    } catch (error) {
      console.error('Password update error:', error)
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  // Show loading while checking session validity
  if (isValidSession === null) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="p-8 text-center">
          <div className="text-gray-600">Verifying reset link...</div>
        </CardContent>
      </Card>
    )
  }

  // Show error if invalid session
  if (!isValidSession) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Invalid Reset Link</CardTitle>
          <CardDescription>
            This password reset link is invalid or has expired
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            {error}
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <Link
            href="/auth/forgot-password"
            className="w-full"
          >
            <Button className="w-full">
              Request New Reset Link
            </Button>
          </Link>
          <Link
            href="/auth/sign-in"
            className="text-sm text-blue-600 hover:text-blue-500 font-medium"
          >
            Back to Sign In
          </Link>
        </CardFooter>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl">Reset Password</CardTitle>
        <CardDescription>
          Enter your new password below
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit(onSubmit)}>
        <CardContent className="space-y-4">
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}
          
          {success && (
            <div className="p-3 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md">
              {success}
            </div>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="password">New Password</Label>
            <Input
              id="password"
              type="password"
              placeholder="Enter your new password"
              disabled={isLoading || !!success}
              {...register('password')}
            />
            {errors.password && (
              <p className="text-sm text-red-600">{errors.password.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirm New Password</Label>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="Confirm your new password"
              disabled={isLoading || !!success}
              {...register('confirmPassword')}
            />
            {errors.confirmPassword && (
              <p className="text-sm text-red-600">{errors.confirmPassword.message}</p>
            )}
          </div>
        </CardContent>
        
        <CardFooter className="flex flex-col space-y-4">
          <Button
            type="submit"
            className="w-full"
            disabled={isLoading || !!success}
          >
            {isLoading ? 'Updating Password...' : 'Update Password'}
          </Button>
          
          {!success && (
            <Link
              href="/auth/sign-in"
              className="text-sm text-center text-blue-600 hover:text-blue-500 font-medium"
            >
              Back to Sign In
            </Link>
          )}
        </CardFooter>
      </form>
    </Card>
  )
}