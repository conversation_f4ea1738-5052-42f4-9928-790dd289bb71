'use client'

/**
 * Protected Route Component
 * Wrapper component that requires authentication to access child components
 */

import { useEffect, ReactNode } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'

interface ProtectedRouteProps {
  children: ReactNode
  redirectTo?: string
  requireRole?: string
  fallback?: ReactNode
}

export function ProtectedRoute({ 
  children, 
  redirectTo = '/auth/sign-in',
  requireRole,
  fallback = <AuthLoadingSpinner />
}: ProtectedRouteProps) {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading) {
      if (!user) {
        // Redirect to sign in if not authenticated
        router.push(redirectTo)
        return
      }

      // Check role requirement if specified
      if (requireRole) {
        const userRole = user.user_metadata?.role || user.app_metadata?.role || 'user'
        if (userRole !== requireRole) {
          // Redirect to unauthorized page or dashboard
          router.push('/unauthorized')
          return
        }
      }
    }
  }, [user, loading, router, redirectTo, requireRole])

  // Show loading state while checking authentication
  if (loading) {
    return fallback
  }

  // Show nothing while redirecting
  if (!user) {
    return null
  }

  // Check role requirement
  if (requireRole) {
    const userRole = user.user_metadata?.role || user.app_metadata?.role || 'user'
    if (userRole !== requireRole) {
      return null
    }
  }

  // Render children if authenticated and authorized
  return <>{children}</>
}

/**
 * Auth Loading Spinner Component
 */
function AuthLoadingSpinner() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p className="text-gray-600">Checking authentication...</p>
      </div>
    </div>
  )
}

/**
 * Hook for conditional rendering based on authentication
 */
export function useAuthRedirect(redirectTo: string = '/auth/sign-in') {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push(redirectTo)
    }
  }, [user, loading, router, redirectTo])

  return {
    isAuthenticated: !!user,
    isLoading: loading,
    user,
  }
}

/**
 * Higher-order component for protecting pages
 */
export function withAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options?: {
    redirectTo?: string
    requireRole?: string
    fallback?: ReactNode
  }
) {
  const displayName = WrappedComponent.displayName || WrappedComponent.name || 'Component'
  
  const AuthenticatedComponent = (props: P) => {
    return (
      <ProtectedRoute
        redirectTo={options?.redirectTo}
        requireRole={options?.requireRole}
        fallback={options?.fallback}
      >
        <WrappedComponent {...props} />
      </ProtectedRoute>
    )
  }

  AuthenticatedComponent.displayName = `withAuth(${displayName})`
  
  return AuthenticatedComponent
}

/**
 * Role-based access control component
 */
interface RoleGuardProps {
  children: ReactNode
  allowedRoles: string[]
  fallback?: ReactNode
}

export function RoleGuard({ children, allowedRoles, fallback = null }: RoleGuardProps) {
  const { user, loading } = useAuth()

  if (loading) {
    return <AuthLoadingSpinner />
  }

  if (!user) {
    return fallback
  }

  const userRole = user.user_metadata?.role || user.app_metadata?.role || 'user'
  
  if (!allowedRoles.includes(userRole)) {
    return fallback
  }

  return <>{children}</>
}

/**
 * Admin-only access component
 */
interface AdminOnlyProps {
  children: ReactNode
  fallback?: ReactNode
}

export function AdminOnly({ children, fallback = null }: AdminOnlyProps) {
  return (
    <RoleGuard allowedRoles={['admin']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}