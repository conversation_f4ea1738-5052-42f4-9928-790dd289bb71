"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { useCampaigns } from "@/hooks/use-campaigns"
import type { Campaign } from "@/lib/api"
import { X } from "lucide-react"

const campaignSchema = z.object({
  name: z.string().min(1, "Campaign name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().optional(),
  type: z.enum(["search", "display", "shopping", "video", "performance_max"]),
  budget_amount: z.number().min(1, "Budget must be at least $1").max(100000, "Budget cannot exceed $100,000"),
  target_locations: z.array(z.string()).min(1, "At least one location is required"),
  target_languages: z.array(z.string()).min(1, "At least one language is required"),
  keywords: z.array(z.string()).optional(),
})

type CampaignFormData = z.infer<typeof campaignSchema>

interface CampaignDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  campaign?: Campaign
  mode: "create" | "edit"
}

export function CampaignDialog({ open, onOpenChange, campaign, mode }: CampaignDialogProps) {
  const { createCampaign, updateCampaign, isCreating, isUpdating } = useCampaigns()
  const [keywordInput, setKeywordInput] = useState("")
  const [locationInput, setLocationInput] = useState("")
  const [languageInput, setLanguageInput] = useState("")

  const form = useForm<CampaignFormData>({
    resolver: zodResolver(campaignSchema),
    defaultValues: {
      name: campaign?.name || "",
      description: campaign?.description || "",
      type: campaign?.type as any || "search",
      budget_amount: campaign?.budget_amount || 100,
      target_locations: campaign?.target_locations || [],
      target_languages: campaign?.target_languages || [],
      keywords: campaign?.keywords || [],
    },
  })

  const onSubmit = async (data: CampaignFormData) => {
    try {
      if (mode === "create") {
        await createCampaign(data)
      } else if (campaign) {
        await updateCampaign(campaign.id, data)
      }
      onOpenChange(false)
      form.reset()
    } catch (error) {
      console.error("Error saving campaign:", error)
    }
  }

  const addKeyword = () => {
    if (keywordInput.trim()) {
      const currentKeywords = form.getValues("keywords") || []
      form.setValue("keywords", [...currentKeywords, keywordInput.trim()])
      setKeywordInput("")
    }
  }

  const removeKeyword = (index: number) => {
    const currentKeywords = form.getValues("keywords") || []
    form.setValue("keywords", currentKeywords.filter((_, i) => i !== index))
  }

  const addLocation = () => {
    if (locationInput.trim()) {
      const currentLocations = form.getValues("target_locations")
      form.setValue("target_locations", [...currentLocations, locationInput.trim()])
      setLocationInput("")
    }
  }

  const removeLocation = (index: number) => {
    const currentLocations = form.getValues("target_locations")
    form.setValue("target_locations", currentLocations.filter((_, i) => i !== index))
  }

  const addLanguage = () => {
    if (languageInput.trim()) {
      const currentLanguages = form.getValues("target_languages")
      form.setValue("target_languages", [...currentLanguages, languageInput.trim()])
      setLanguageInput("")
    }
  }

  const removeLanguage = (index: number) => {
    const currentLanguages = form.getValues("target_languages")
    form.setValue("target_languages", currentLanguages.filter((_, i) => i !== index))
  }

  const isLoading = isCreating || isUpdating

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === "create" ? "Create New Campaign" : "Edit Campaign"}
          </DialogTitle>
          <DialogDescription>
            {mode === "create"
              ? "Create a new Google Ads campaign with AI optimization."
              : "Update your campaign settings and configuration."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Campaign Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter campaign name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Campaign Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select campaign type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="search">Search</SelectItem>
                        <SelectItem value="display">Display</SelectItem>
                        <SelectItem value="shopping">Shopping</SelectItem>
                        <SelectItem value="video">Video</SelectItem>
                        <SelectItem value="performance_max">Performance Max</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter campaign description (optional)"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="budget_amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Daily Budget ($)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="100"
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormDescription>
                    Set your daily budget for this campaign
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Target Locations */}
            <div className="space-y-2">
              <Label>Target Locations</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Enter location (e.g., United States, New York)"
                  value={locationInput}
                  onChange={(e) => setLocationInput(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addLocation())}
                />
                <Button type="button" onClick={addLocation} variant="outline">
                  Add
                </Button>
              </div>
              {form.watch("target_locations").length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {form.watch("target_locations").map((location, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {location}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeLocation(index)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Target Languages */}
            <div className="space-y-2">
              <Label>Target Languages</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Enter language (e.g., English, Spanish)"
                  value={languageInput}
                  onChange={(e) => setLanguageInput(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addLanguage())}
                />
                <Button type="button" onClick={addLanguage} variant="outline">
                  Add
                </Button>
              </div>
              {form.watch("target_languages").length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {form.watch("target_languages").map((language, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {language}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeLanguage(index)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Keywords (for search campaigns) */}
            {form.watch("type") === "search" && (
              <div className="space-y-2">
                <Label>Keywords</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Enter keyword"
                    value={keywordInput}
                    onChange={(e) => setKeywordInput(e.target.value)}
                    onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addKeyword())}
                  />
                  <Button type="button" onClick={addKeyword} variant="outline">
                    Add
                  </Button>
                </div>
                {(form.watch("keywords")?.length || 0) > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {form.watch("keywords")?.map((keyword, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {keyword}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => removeKeyword(index)}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading
                  ? mode === "create"
                    ? "Creating..."
                    : "Updating..."
                  : mode === "create"
                  ? "Create Campaign"
                  : "Update Campaign"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}