"use client"

import * as React from "react"
import { Sidebar } from "./sidebar"
import { Head<PERSON> } from "./header"
import { cn } from "@/lib/utils"

interface DashboardLayoutProps {
  children: React.ReactNode
  title?: string
  description?: string
  className?: string
}

export function DashboardLayout({
  children,
  title,
  description,
  className,
}: DashboardLayoutProps) {
  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content */}
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Header */}
        <Header title={title} description={description} />

        {/* Page Content */}
        <main className={cn("flex-1 overflow-y-auto p-6", className)}>
          {children}
        </main>
      </div>
    </div>
  )
}