"use client"

import * as React from "react"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { MainNav } from "@/components/navigation/main-nav"
import { ThemeToggle } from "@/components/theme-toggle"
import { But<PERSON> } from "@/components/ui/button"
import { Zap, Menu, X } from "lucide-react"

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const [collapsed, setCollapsed] = React.useState(false)

  return (
    <div
      className={cn(
        "flex h-full flex-col border-r bg-background",
        collapsed ? "w-16" : "w-64",
        className
      )}
    >
      {/* Header */}
      <div className="flex h-16 items-center border-b px-6">
        <Link href="/dashboard" className="flex items-center gap-2 font-semibold">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <Zap className="h-4 w-4" />
          </div>
          {!collapsed && <span className="text-lg">AiLex</span>}
        </Link>
        <Button
          variant="ghost"
          size="icon"
          className="ml-auto h-8 w-8"
          onClick={() => setCollapsed(!collapsed)}
        >
          {collapsed ? (
            <Menu className="h-4 w-4" />
          ) : (
            <X className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto p-4">
        <MainNav className={collapsed ? "items-center" : ""} />
      </div>

      {/* Footer */}
      <div className="border-t p-4">
        <div className="flex items-center gap-2">
          <ThemeToggle />
          {!collapsed && <span className="text-sm text-muted-foreground">Theme</span>}
        </div>
      </div>
    </div>
  )
}