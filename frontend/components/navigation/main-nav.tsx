"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  BarChart3,
  Bot,
  Target,
  Home,
  Settings,
  TrendingUp,
  Users,
  Zap,
} from "lucide-react"

const navigation = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: Home,
    description: "Overview and key metrics",
  },
  {
    name: "Campaigns",
    href: "/campaigns",
    icon: Target,
    description: "Manage Google Ads campaigns",
  },
  {
    name: "Agents",
    href: "/agents",
    icon: Bo<PERSON>,
    description: "AI agent monitoring",
  },
  {
    name: "Analytics",
    href: "/analytics",
    icon: BarChart3,
    description: "Performance insights",
  },
  {
    name: "Optimization",
    href: "/optimization",
    icon: TrendingUp,
    description: "AI-powered suggestions",
  },
  {
    name: "Settings",
    href: "/settings",
    icon: Settings,
    description: "Account and preferences",
  },
]

interface MainNavProps {
  className?: string
}

export function MainNav({ className }: MainNavProps) {
  const pathname = usePathname()

  return (
    <nav className={cn("flex flex-col space-y-2", className)}>
      {navigation.map((item) => {
        const isActive = pathname === item.href || pathname?.startsWith(`${item.href}/`)
        const Icon = item.icon

        return (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground",
              isActive
                ? "bg-accent text-accent-foreground"
                : "text-muted-foreground"
            )}
          >
            <Icon className="h-4 w-4" />
            <span>{item.name}</span>
          </Link>
        )
      })}
    </nav>
  )
}