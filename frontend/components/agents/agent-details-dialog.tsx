"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { useAgent, useAgentTasks, useAgentMetrics, useAgents } from "@/hooks/use-agents"
import { useCampaigns } from "@/hooks/use-campaigns"
import { formatDateTime } from "@/lib/utils"
import type { Agent } from "@/lib/api"
import {
  Activity,
  Bot,
  CheckCircle,
  Clock,
  Play,
  Pause,
  Settings,
  Zap,
  TrendingUp,
  AlertCircle,
  Info,
  Loader2,
} from "lucide-react"

const taskSchema = z.object({
  type: z.enum(["optimize_campaign", "analyze_performance", "generate_content", "audit_compliance", "research_audience"]),
  campaign_id: z.string().optional(),
  priority: z.enum(["low", "medium", "high", "urgent"]).default("medium"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  parameters: z.record(z.any()).default({}),
})

type TaskFormData = z.infer<typeof taskSchema>

interface AgentDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  agentId: string
}

export function AgentDetailsDialog({ open, onOpenChange, agentId }: AgentDetailsDialogProps) {
  const [activeTab, setActiveTab] = useState("overview")
  const { agent, isLoading: agentLoading } = useAgent(agentId)
  const { tasks, isLoading: tasksLoading } = useAgentTasks(agentId, { limit: 10 })
  const { metrics, isLoading: metricsLoading } = useAgentMetrics(agentId)
  const { campaigns } = useCampaigns()
  const { assignTask, startAgent, stopAgent } = useAgents()

  const form = useForm<TaskFormData>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      type: "optimize_campaign",
      priority: "medium",
      description: "",
      parameters: {},
    },
  })

  const onAssignTask = async (data: TaskFormData) => {
    if (!agent) return
    
    try {
      await assignTask(agent.id, agent.name, data)
      form.reset()
    } catch (error) {
      console.error("Error assigning task:", error)
    }
  }

  const handleToggleStatus = async () => {
    if (!agent) return
    
    try {
      if (agent.status === "active") {
        await stopAgent(agent.id, agent.name)
      } else {
        await startAgent(agent.id, agent.name)
      }
    } catch (error) {
      console.error("Error toggling agent status:", error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500"
      case "idle":
        return "bg-yellow-500"
      case "error":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "active":
        return "default"
      case "idle":
        return "secondary"
      case "error":
        return "destructive"
      default:
        return "outline"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "text-red-600"
      case "high":
        return "text-orange-600"
      case "medium":
        return "text-blue-600"
      case "low":
        return "text-gray-600"
      default:
        return "text-gray-600"
    }
  }

  if (agentLoading || !agent) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <div className={`h-3 w-3 rounded-full ${getStatusColor(agent.status)}`} />
              <DialogTitle className="text-xl">{agent.name}</DialogTitle>
            </div>
            <Badge variant={getStatusBadgeVariant(agent.status) as any}>
              {agent.status}
            </Badge>
          </div>
          <DialogDescription className="text-base">
            {agent.description}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="tasks">Tasks</TabsTrigger>
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
            <TabsTrigger value="assign">Assign Task</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Agent Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Agent Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Status</span>
                    <Badge variant={getStatusBadgeVariant(agent.status) as any}>
                      {agent.status}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Type</span>
                    <span className="text-sm font-medium capitalize">{agent.type}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Campaign</span>
                    <span className="text-sm font-medium">
                      {agent.campaign_id ? "Assigned" : "Unassigned"}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Last Activity</span>
                    <span className="text-sm font-medium">
                      {agent.last_activity ? formatDateTime(agent.last_activity) : "Never"}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    onClick={handleToggleStatus}
                    className="w-full"
                    variant={agent.status === "active" ? "destructive" : "default"}
                  >
                    {agent.status === "active" ? (
                      <>
                        <Pause className="mr-2 h-4 w-4" />
                        Stop Agent
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        Start Agent
                      </>
                    )}
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Settings className="mr-2 h-4 w-4" />
                    Configure Agent
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Zap className="mr-2 h-4 w-4" />
                    Restart Agent
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Configuration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-2 text-sm">
                  {Object.entries(agent.config || {}).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                      <span className="capitalize">{key.replace(/_/g, " ")}</span>
                      <span className="font-medium">{String(value)}</span>
                    </div>
                  ))}
                  {Object.keys(agent.config || {}).length === 0 && (
                    <p className="text-muted-foreground">No configuration parameters set</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tasks" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Recent Tasks</CardTitle>
                <CardDescription>
                  Latest tasks assigned to this agent
                </CardDescription>
              </CardHeader>
              <CardContent>
                {tasksLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : tasks.length > 0 ? (
                  <div className="space-y-3">
                    {tasks.map((task: any, index: number) => (
                      <div key={index} className="flex items-start gap-3 rounded-lg border p-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium capitalize">
                              {task.type?.replace(/_/g, " ") || "Unknown Task"}
                            </span>
                            <Badge
                              variant="outline"
                              className={getPriorityColor(task.priority || "medium")}
                            >
                              {task.priority || "medium"}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {task.description || "No description available"}
                          </p>
                          <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            {task.created_at ? formatDateTime(task.created_at) : "Unknown time"}
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          {task.status === "completed" ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : task.status === "failed" ? (
                            <AlertCircle className="h-4 w-4 text-red-500" />
                          ) : (
                            <Activity className="h-4 w-4 text-blue-500" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Bot className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No tasks assigned yet</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="metrics" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Performance Metrics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {metricsLoading ? (
                    <div className="flex items-center justify-center py-4">
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </div>
                  ) : (
                    <>
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm">Success Rate</span>
                          <span className="text-sm font-medium">
                            {metrics?.success_rate ? `${(metrics.success_rate * 100).toFixed(1)}%` : "N/A"}
                          </span>
                        </div>
                        <Progress value={(metrics?.success_rate || 0) * 100} />
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm">Avg Response Time</span>
                          <span className="text-sm font-medium">
                            {metrics?.avg_response_time || "N/A"}
                          </span>
                        </div>
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm">Tasks Processed</span>
                          <span className="text-sm font-medium">
                            {metrics?.tasks_processed || 0}
                          </span>
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Resource Usage</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm">CPU Usage</span>
                      <span className="text-sm font-medium">
                        {metrics?.cpu_usage ? `${metrics.cpu_usage.toFixed(1)}%` : "N/A"}
                      </span>
                    </div>
                    <Progress value={metrics?.cpu_usage || 0} />
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm">Memory Usage</span>
                      <span className="text-sm font-medium">
                        {metrics?.memory_usage ? `${metrics.memory_usage.toFixed(1)}MB` : "N/A"}
                      </span>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm">Uptime</span>
                      <span className="text-sm font-medium">
                        {metrics?.uptime || "N/A"}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="assign" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Assign New Task</CardTitle>
                <CardDescription>
                  Create and assign a new task to this agent
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onAssignTask)} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Task Type</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select task type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="optimize_campaign">Optimize Campaign</SelectItem>
                                <SelectItem value="analyze_performance">Analyze Performance</SelectItem>
                                <SelectItem value="generate_content">Generate Content</SelectItem>
                                <SelectItem value="audit_compliance">Audit Compliance</SelectItem>
                                <SelectItem value="research_audience">Research Audience</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="priority"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Priority</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select priority" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="low">Low</SelectItem>
                                <SelectItem value="medium">Medium</SelectItem>
                                <SelectItem value="high">High</SelectItem>
                                <SelectItem value="urgent">Urgent</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="campaign_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Campaign (Optional)</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select campaign (optional)" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {campaigns.map((campaign) => (
                                <SelectItem key={campaign.id} value={campaign.id}>
                                  {campaign.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Task Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Describe what the agent should do..."
                              className="resize-none"
                              rows={3}
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Provide clear instructions for the agent
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-end gap-2">
                      <Button type="button" variant="outline" onClick={() => form.reset()}>
                        Clear
                      </Button>
                      <Button type="submit">
                        <Zap className="mr-2 h-4 w-4" />
                        Assign Task
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}