#!/bin/bash

# AiLex Ad Agent System - Split Architecture Deployment
# Deploys API and Worker services separately for optimal performance

set -e

# Configuration
API_APP="ailex-ad-agent-api"
WORKER_APP="ailex-ad-agent-worker"
POSTGRES_APP="ailex-postgres"
REDIS_APP="ailex-redis"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${BLUE}==>${NC} $1"
}

print_success() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

check_dependencies() {
    print_step "Checking dependencies..."
    
    if ! command -v flyctl &> /dev/null; then
        print_error "flyctl is not installed. Please install it first."
        exit 1
    fi
    
    if ! flyctl auth whoami &> /dev/null; then
        print_error "Not logged in to Fly.io. Please run 'flyctl auth login'"
        exit 1
    fi
    
    print_success "Dependencies checked"
}

setup_shared_resources() {
    print_step "Setting up shared resources..."
    
    # Create PostgreSQL if it doesn't exist
    if ! flyctl apps list | grep -q "$POSTGRES_APP"; then
        print_step "Creating PostgreSQL database..."
        flyctl postgres create --name $POSTGRES_APP --region iad --vm-size shared-cpu-1x --volume-size 10
    else
        print_success "PostgreSQL database already exists"
    fi
    
    # Create Redis if it doesn't exist
    if ! flyctl apps list | grep -q "$REDIS_APP"; then
        print_step "Creating Redis instance..."
        flyctl redis create --name $REDIS_APP --region iad
    else
        print_success "Redis instance already exists"
    fi
    
    print_success "Shared resources ready"
}

deploy_api_service() {
    print_step "Deploying API service (lightweight)..."
    
    cd backend
    
    # Create API app if it doesn't exist
    if ! flyctl apps list | grep -q "$API_APP"; then
        print_step "Creating API application..."
        flyctl apps create $API_APP --org personal
    fi
    
    # Attach shared resources
    print_step "Attaching shared resources to API..."
    flyctl postgres attach $POSTGRES_APP --app $API_APP || true
    flyctl redis attach $REDIS_APP --app $API_APP || true
    
    # Set API-specific secrets
    print_step "Setting API secrets..."
    flyctl secrets set \
        SERVICE_TYPE=api \
        ENVIRONMENT=production \
        LOG_LEVEL=INFO \
        --app $API_APP
    
    # Deploy API service
    print_step "Deploying API service..."
    flyctl deploy --config fly.api.toml --remote-only --app $API_APP
    
    # Check deployment status
    if flyctl status --app $API_APP | grep -q "running"; then
        print_success "API service deployed successfully"
        API_URL="https://${API_APP}.fly.dev"
        echo "API URL: $API_URL"
    else
        print_error "API service deployment failed"
        exit 1
    fi
    
    cd ..
}

deploy_worker_service() {
    print_step "Deploying Worker service (with CrewAI)..."
    
    cd backend
    
    # Create Worker app if it doesn't exist
    if ! flyctl apps list | grep -q "$WORKER_APP"; then
        print_step "Creating Worker application..."
        flyctl apps create $WORKER_APP --org personal
    fi
    
    # Attach shared resources
    print_step "Attaching shared resources to Worker..."
    flyctl postgres attach $POSTGRES_APP --app $WORKER_APP || true
    flyctl redis attach $REDIS_APP --app $WORKER_APP || true
    
    # Set Worker-specific secrets
    print_step "Setting Worker secrets..."
    flyctl secrets set \
        SERVICE_TYPE=worker \
        ENVIRONMENT=production \
        LOG_LEVEL=INFO \
        CELERY_WORKER=true \
        --app $WORKER_APP
    
    # Deploy Worker service
    print_step "Deploying Worker service (this may take longer due to heavy dependencies)..."
    flyctl deploy --config fly.worker.toml --remote-only --app $WORKER_APP
    
    # Check deployment status
    if flyctl status --app $WORKER_APP | grep -q "running"; then
        print_success "Worker service deployed successfully"
        WORKER_URL="https://${WORKER_APP}.fly.dev"
        echo "Worker URL: $WORKER_URL"
    else
        print_error "Worker service deployment failed"
        exit 1
    fi
    
    cd ..
}

run_health_checks() {
    print_step "Running health checks..."
    
    # Wait for services to be ready
    sleep 30
    
    # Check API health
    print_step "Checking API health..."
    if curl -f "https://${API_APP}.fly.dev/api/v1/health" > /dev/null 2>&1; then
        print_success "API service is healthy"
    else
        print_warning "API service health check failed"
    fi
    
    # Check Worker health
    print_step "Checking Worker health..."
    if curl -f "https://${WORKER_APP}.fly.dev/worker/health" > /dev/null 2>&1; then
        print_success "Worker service is healthy"
    else
        print_warning "Worker service health check failed"
    fi
}

show_deployment_info() {
    echo ""
    echo "=========================================="
    print_success "Split Architecture Deployment Complete!"
    echo "=========================================="
    echo ""
    echo "🚀 Services Deployed:"
    echo "   API Service (Lightweight):  https://${API_APP}.fly.dev"
    echo "   Worker Service (CrewAI):     https://${WORKER_APP}.fly.dev"
    echo ""
    echo "📊 Architecture Benefits:"
    echo "   ✓ Fast API deployments (~2-3 minutes)"
    echo "   ✓ Reduced API container size (~200MB vs ~1GB)"
    echo "   ✓ Independent scaling of API and workers"
    echo "   ✓ No more deployment timeouts"
    echo ""
    echo "🔧 Resource Usage:"
    echo "   API:    512MB RAM, 1 CPU (lightweight)"
    echo "   Worker: 2GB RAM, 2 CPU (heavy processing)"
    echo ""
    echo "📝 Next Steps:"
    echo "   1. Update frontend to use API URL: https://${API_APP}.fly.dev"
    echo "   2. Monitor both services via Fly.io dashboard"
    echo "   3. Scale services independently as needed"
    echo ""
}

main() {
    echo "=========================================="
    echo "AiLex Split Architecture Deployment"
    echo "=========================================="
    echo ""
    
    check_dependencies
    setup_shared_resources
    deploy_api_service
    deploy_worker_service
    run_health_checks
    show_deployment_info
}

main "$@"
