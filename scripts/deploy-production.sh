#!/bin/bash

# AiLex Ad Agent System - Production Deployment Script
# This script handles the complete production deployment process

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_APP="ailex-ad-agent-backend"
FRONTEND_PROJECT="ailex-frontend"
REGION="iad"

# Functions
print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    print_step "Checking required dependencies..."
    
    # Check if fly CLI is installed
    if ! command -v flyctl &> /dev/null; then
        print_error "Fly CLI is not installed. Please install it from https://fly.io/docs/hands-on/install-flyctl/"
        exit 1
    fi
    
    # Check if Vercel CLI is installed
    if ! command -v vercel &> /dev/null; then
        print_error "Vercel CLI is not installed. Please run: npm i -g vercel"
        exit 1
    fi
    
    # Check if authenticated with Fly.io
    if ! flyctl auth whoami &> /dev/null; then
        print_error "Not authenticated with Fly.io. Please run: flyctl auth login"
        exit 1
    fi
    
    # Check if authenticated with Vercel
    if ! vercel whoami &> /dev/null; then
        print_error "Not authenticated with Vercel. Please run: vercel login"
        exit 1
    fi
    
    print_success "All dependencies are available"
}

setup_fly_resources() {
    print_step "Setting up Fly.io resources..."
    
    # Create PostgreSQL database if it doesn't exist
    if ! flyctl postgres list | grep -q "ailex-postgres"; then
        print_step "Creating PostgreSQL database..."
        flyctl postgres create --name ailex-postgres --region $REGION --vm-size shared-cpu-1x --volume-size 10
        print_success "PostgreSQL database created"
    else
        print_warning "PostgreSQL database already exists"
    fi
    
    # Create Redis instance if it doesn't exist
    if ! flyctl redis list | grep -q "ailex-redis"; then
        print_step "Creating Redis instance..."
        flyctl redis create --name ailex-redis --region $REGION
        print_success "Redis instance created"
    else
        print_warning "Redis instance already exists"
    fi
    
    # Create app if it doesn't exist
    if ! flyctl apps list | grep -q "$BACKEND_APP"; then
        print_step "Creating Fly.io app..."
        cd backend
        flyctl apps create $BACKEND_APP --org personal
        cd ..
        print_success "Fly.io app created"
    else
        print_warning "Fly.io app already exists"
    fi
}

set_fly_secrets() {
    print_step "Setting up Fly.io secrets..."
    
    # Check if secrets file exists
    if [ ! -f "backend/.env.production" ]; then
        print_error "Production environment file not found: backend/.env.production"
        exit 1
    fi
    
    print_warning "Please set the following secrets manually using 'flyctl secrets set':"
    echo "  - OPENAI_API_KEY"
    echo "  - GOOGLE_ADS_DEVELOPER_TOKEN"
    echo "  - GOOGLE_ADS_CLIENT_ID" 
    echo "  - GOOGLE_ADS_CLIENT_SECRET"
    echo "  - GOOGLE_ADS_REFRESH_TOKEN"
    echo "  - GOOGLE_ADS_CUSTOMER_ID"
    echo "  - SENTRY_DSN"
    echo "  - CORS_ORIGINS"
    echo ""
    read -p "Press Enter after setting all secrets..."
}

attach_resources() {
    print_step "Attaching resources to app..."
    
    cd backend
    
    # Attach PostgreSQL
    if ! flyctl postgres attach ailex-postgres --app $BACKEND_APP; then
        print_warning "Failed to attach PostgreSQL (might already be attached)"
    fi
    
    # Attach Redis
    if ! flyctl redis attach ailex-redis --app $BACKEND_APP; then
        print_warning "Failed to attach Redis (might already be attached)"
    fi
    
    cd ..
    print_success "Resources attached"
}

deploy_backend() {
    print_step "Deploying backend to Fly.io..."
    
    cd backend
    
    # Deploy the application
    flyctl deploy --remote-only --app $BACKEND_APP
    
    # Check if deployment was successful
    if flyctl status --app $BACKEND_APP | grep -q "running"; then
        print_success "Backend deployed successfully"
    else
        print_error "Backend deployment failed"
        exit 1
    fi
    
    cd ..
}

deploy_frontend() {
    print_step "Deploying frontend to Vercel..."
    
    cd frontend
    
    # Deploy to Vercel
    vercel --prod --yes
    
    print_success "Frontend deployed successfully"
    
    cd ..
}

run_health_checks() {
    print_step "Running health checks..."
    
    # Wait for backend to be ready
    sleep 30
    
    # Check backend health
    BACKEND_URL="https://${BACKEND_APP}.fly.dev"
    if curl -f "$BACKEND_URL/api/v1/health/liveness" > /dev/null 2>&1; then
        print_success "Backend health check passed"
    else
        print_error "Backend health check failed"
        exit 1
    fi
    
    # Check if readiness endpoint is accessible
    if curl -f "$BACKEND_URL/api/v1/health/readiness" > /dev/null 2>&1; then
        print_success "Backend readiness check passed"
    else
        print_warning "Backend readiness check failed (services might not be fully ready)"
    fi
}

main() {
    echo "=========================================="
    echo "AiLex Ad Agent System Production Deployment"
    echo "=========================================="
    echo ""
    
    check_dependencies
    setup_fly_resources
    set_fly_secrets
    attach_resources
    deploy_backend
    deploy_frontend
    run_health_checks
    
    echo ""
    echo "=========================================="
    print_success "Deployment completed successfully!"
    echo "=========================================="
    echo ""
    echo "Backend URL: https://${BACKEND_APP}.fly.dev"
    echo "Frontend URL: Check Vercel deployment output above"
    echo ""
    echo "Next steps:"
    echo "1. Verify all services are working correctly"
    echo "2. Run integration tests"
    echo "3. Monitor logs and metrics"
    echo "4. Set up monitoring alerts"
}

# Run main function
main "$@"