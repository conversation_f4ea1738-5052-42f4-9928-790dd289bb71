#!/bin/bash

# AiLex Ad Agent System - Staging Deployment Script
# This script handles staging environment deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_APP="ailex-ad-agent-backend-staging"
REGION="iad"

# Functions
print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    print_step "Checking required dependencies..."
    
    if ! command -v flyctl &> /dev/null; then
        print_error "Fly CLI is not installed. Please install it from https://fly.io/docs/hands-on/install-flyctl/"
        exit 1
    fi
    
    if ! command -v vercel &> /dev/null; then
        print_error "Vercel CLI is not installed. Please run: npm i -g vercel"
        exit 1
    fi
    
    if ! flyctl auth whoami &> /dev/null; then
        print_error "Not authenticated with Fly.io. Please run: flyctl auth login"
        exit 1
    fi
    
    if ! vercel whoami &> /dev/null; then
        print_error "Not authenticated with Vercel. Please run: vercel login"
        exit 1
    fi
    
    print_success "All dependencies are available"
}

setup_staging_resources() {
    print_step "Setting up staging Fly.io resources..."
    
    # Create staging PostgreSQL database
    if ! flyctl postgres list | grep -q "ailex-postgres-staging"; then
        print_step "Creating staging PostgreSQL database..."
        flyctl postgres create --name ailex-postgres-staging --region $REGION --vm-size shared-cpu-1x --volume-size 5
        print_success "Staging PostgreSQL database created"
    else
        print_warning "Staging PostgreSQL database already exists"
    fi
    
    # Create staging Redis instance
    if ! flyctl redis list | grep -q "ailex-redis-staging"; then
        print_step "Creating staging Redis instance..."
        flyctl redis create --name ailex-redis-staging --region $REGION
        print_success "Staging Redis instance created"
    else
        print_warning "Staging Redis instance already exists"
    fi
    
    # Create staging app
    if ! flyctl apps list | grep -q "$BACKEND_APP"; then
        print_step "Creating staging Fly.io app..."
        cd backend
        flyctl apps create $BACKEND_APP --org personal
        cd ..
        print_success "Staging Fly.io app created"
    else
        print_warning "Staging Fly.io app already exists"
    fi
}

deploy_staging_backend() {
    print_step "Deploying backend to staging..."
    
    cd backend
    
    # Create staging fly.toml if it doesn't exist
    if [ ! -f "fly.staging.toml" ]; then
        cp fly.toml fly.staging.toml
        sed -i.bak "s/ailex-ad-agent-backend/ailex-ad-agent-backend-staging/g" fly.staging.toml
        rm fly.staging.toml.bak
    fi
    
    # Attach resources
    flyctl postgres attach ailex-postgres-staging --app $BACKEND_APP || true
    flyctl redis attach ailex-redis-staging --app $BACKEND_APP || true
    
    # Set staging-specific secrets
    flyctl secrets set ENVIRONMENT=staging --app $BACKEND_APP
    flyctl secrets set LOG_LEVEL=DEBUG --app $BACKEND_APP
    
    # Deploy
    flyctl deploy --config fly.staging.toml --remote-only --app $BACKEND_APP
    
    cd ..
    
    print_success "Staging backend deployed"
}

deploy_staging_frontend() {
    print_step "Deploying frontend to staging..."
    
    cd frontend
    
    # Deploy to Vercel staging (preview deployment)
    vercel --yes
    
    print_success "Staging frontend deployed"
    
    cd ..
}

run_staging_tests() {
    print_step "Running staging smoke tests..."
    
    sleep 20
    
    STAGING_URL="https://${BACKEND_APP}.fly.dev"
    
    # Basic health check
    if curl -f "$STAGING_URL/api/v1/health/liveness" > /dev/null 2>&1; then
        print_success "Staging health check passed"
    else
        print_error "Staging health check failed"
        exit 1
    fi
    
    # Check API endpoints
    if curl -f "$STAGING_URL/api/v1/health" > /dev/null 2>&1; then
        print_success "Staging API check passed"
    else
        print_warning "Staging API check failed"
    fi
}

main() {
    echo "========================================"
    echo "AiLex Ad Agent System Staging Deployment"
    echo "========================================"
    echo ""
    
    check_dependencies
    setup_staging_resources
    deploy_staging_backend
    deploy_staging_frontend
    run_staging_tests
    
    echo ""
    echo "========================================"
    print_success "Staging deployment completed!"
    echo "========================================"
    echo ""
    echo "Staging Backend URL: https://${BACKEND_APP}.fly.dev"
    echo "Staging Frontend URL: Check Vercel deployment output above"
    echo ""
    echo "Ready for testing and validation!"
}

main "$@"