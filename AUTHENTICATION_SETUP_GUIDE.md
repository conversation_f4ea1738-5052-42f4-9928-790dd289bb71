# Authentication Setup Guide 🔐

This guide covers the complete setup of Supa<PERSON> Auth with Resend email service for the AiLex Ad Agent System.

## Overview

The authentication system includes:
- **Supabase Auth**: User authentication, password management, email verification
- **Resend**: Professional email service for transactional emails
- **JWT-based sessions**: Secure token-based authentication
- **Role-based access control**: Admin, manager, and user roles
- **Frontend components**: Complete auth UI with Next.js

## 🏗️ Architecture

```
Frontend (Next.js + Supabase Client)
    ↓ (JWT tokens)
Backend (FastAPI + Supabase Auth Service)
    ↓ (Auth verification)
Supabase Auth + PostgreSQL
    ↓ (Email triggers)
Resend Email Service
```

## 📋 Prerequisites

1. **Supabase Project**: Create a project at [supabase.com](https://supabase.com)
2. **Resend Account**: Sign up at [resend.com](https://resend.com)
3. **Custom Domain** (recommended): For production email sending

## 🚀 Setup Instructions

### 1. Supabase Configuration

#### 1.1 Create Supabase Project
```bash
# Go to https://supabase.com
# Create a new project
# Note down your project URL and keys
```

#### 1.2 Configure Auth Settings
In your Supabase dashboard:

1. **Authentication > Settings**:
   - Enable email confirmations
   - Set site URL: `https://your-domain.com`
   - Add redirect URLs:
     - `http://localhost:3000/auth/callback` (development)
     - `https://your-domain.com/auth/callback` (production)

2. **Authentication > Email Templates**:
   - Customize email templates (optional)
   - Set sender name: "AiLex Ad Agent System"

#### 1.3 Database Setup
Run these SQL commands in Supabase SQL Editor:

```sql
-- Enable Row Level Security
ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

-- Create profiles table for additional user data
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE,
    email TEXT,
    first_name TEXT,
    last_name TEXT,
    role TEXT DEFAULT 'user',
    organization_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    PRIMARY KEY (id)
);

-- Enable RLS on profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create policy for users to see their own profile
CREATE POLICY "Users can view their own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

-- Create policy for users to update their own profile
CREATE POLICY "Users can update their own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, first_name, last_name, role)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'first_name',
        NEW.raw_user_meta_data->>'last_name',
        COALESCE(NEW.raw_user_meta_data->>'role', 'user')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on user creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
```

### 2. Resend Email Service

#### 2.1 Setup Resend
1. Go to [resend.com](https://resend.com) and create an account
2. Verify your domain (recommended for production)
3. Generate an API key
4. Note your API key (starts with `re_`)

#### 2.2 Domain Configuration (Production)
```bash
# Add these DNS records for your domain:
# TXT: resend._domainkey.yourdomain.com
# Value: [provided by Resend]

# MX record (if using Resend for all emails):
# Priority: 10, Value: feedback-smtp.resend.com
```

### 3. Environment Variables

#### 3.1 Backend (.env)
```bash
# Supabase Configuration
SUPABASE_URL="https://your-project-ref.supabase.co"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Legacy support (optional)
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
DATABASE_KEY="your-service-role-key"

# Security
SECRET_KEY="your-secret-key-for-sessions"

# Email Service
RESEND_API_KEY="re_your_resend_api_key"
FROM_EMAIL="<EMAIL>"
FROM_NAME="AiLex Ad Agent System"

# CORS (adjust for your domain)
CORS_ORIGINS="http://localhost:3000,https://your-domain.com"
```

#### 3.2 Frontend (.env.local)
```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://your-project-ref.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"

# API Configuration
NEXT_PUBLIC_API_BASE_URL="http://localhost:8000/api/v1"
NEXT_PUBLIC_BACKEND_URL="http://localhost:8000"
```

### 4. Production Deployment

#### 4.1 Fly.io Backend Secrets
```bash
# Set Supabase secrets
fly secrets set SUPABASE_URL="https://your-project-ref.supabase.co"
fly secrets set SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
fly secrets set SUPABASE_ANON_KEY="your-anon-key"

# Set email secrets
fly secrets set RESEND_API_KEY="re_your_resend_api_key"
fly secrets set FROM_EMAIL="<EMAIL>"

# Set security secrets
fly secrets set SECRET_KEY="your-production-secret-key"

# Set CORS
fly secrets set CORS_ORIGINS="https://your-frontend-domain.com"
```

#### 4.2 Vercel Frontend Environment Variables
```bash
# In Vercel dashboard, add these environment variables:
NEXT_PUBLIC_SUPABASE_URL="https://your-project-ref.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
NEXT_PUBLIC_API_BASE_URL="https://your-backend-domain.fly.dev/api/v1"
NEXT_PUBLIC_BACKEND_URL="https://your-backend-domain.fly.dev"
```

## 🧪 Testing

### 1. Run Authentication Tests
```bash
# Backend testing
cd backend
python test_auth_setup.py
```

### 2. Manual Testing
1. **Registration**: Create a new account at `/auth/sign-up`
2. **Email Verification**: Check email and click verification link
3. **Login**: Sign in at `/auth/sign-in`
4. **Password Reset**: Test at `/auth/forgot-password`
5. **Protected Routes**: Access `/dashboard` (requires auth)

### 3. API Testing
```bash
# Test backend endpoints
curl -X POST http://localhost:8000/api/v1/auth/sign-up \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123",
    "first_name": "Test",
    "last_name": "User"
  }'
```

## 🔧 Configuration Options

### Supabase Auth Settings
- **Email confirmation**: Required for new users
- **Password requirements**: Minimum 8 characters
- **Session timeout**: 1 hour (refreshable)
- **Email templates**: Customizable HTML templates

### Resend Configuration
- **From address**: Must be verified domain
- **Rate limits**: 100 emails/day (free), upgrade for more
- **Templates**: HTML email templates for welcome, reset, etc.

### Security Features
- **JWT tokens**: Secure, stateless authentication
- **Rate limiting**: Prevents brute force attacks
- **CORS protection**: Restricts frontend origins
- **Input validation**: Prevents malicious inputs
- **Password hashing**: Secure bcrypt hashing

## 🎯 User Roles

| Role | Permissions | Description |
|------|-------------|-------------|
| **user** | `campaigns:read`, `analytics:read` | Standard user |
| **manager** | `campaigns:write`, `analytics:write` | Campaign manager |
| **admin** | `system:admin`, `users:manage` | Full system access |

### Role Assignment
```sql
-- Update user role in Supabase
UPDATE public.profiles 
SET role = 'admin' 
WHERE id = 'user-uuid-here';
```

## 🚨 Troubleshooting

### Common Issues

1. **Email not sending**:
   - Check Resend API key
   - Verify domain configuration
   - Check email templates

2. **Auth callback errors**:
   - Verify redirect URLs in Supabase
   - Check CORS settings
   - Ensure callback page exists

3. **JWT token errors**:
   - Check Supabase service role key
   - Verify JWT secret configuration
   - Check token expiration

4. **Database connection issues**:
   - Verify Supabase URL and keys
   - Check network connectivity
   - Review database policies

### Debug Mode
```bash
# Enable debug logging
DEBUG=true
LOG_LEVEL="DEBUG"
```

### Health Checks
```bash
# Check auth service health
curl http://localhost:8000/api/v1/auth/health

# Check main application health
curl http://localhost:8000/api/v1/health/liveness
```

## 📚 API Reference

### Authentication Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/v1/auth/sign-up` | User registration |
| `POST` | `/api/v1/auth/sign-in` | User login |
| `POST` | `/api/v1/auth/sign-out` | User logout |
| `POST` | `/api/v1/auth/reset-password` | Password reset request |
| `POST` | `/api/v1/auth/update-password` | Password update |
| `POST` | `/api/v1/auth/refresh` | Token refresh |
| `GET` | `/api/v1/auth/user` | Current user info |
| `GET` | `/api/v1/auth/health` | Service health |

### Frontend Pages

| Route | Description | Access |
|-------|-------------|---------|
| `/auth/sign-in` | Login page | Public |
| `/auth/sign-up` | Registration page | Public |
| `/auth/forgot-password` | Password reset | Public |
| `/auth/reset-password` | Set new password | Public (with token) |
| `/auth/callback` | Auth callback handler | Public |
| `/dashboard` | Main dashboard | Protected |

## 🎉 Next Steps

After completing the authentication setup:

1. **Customize Email Templates**: Brand your auth emails
2. **Add Social Login**: Configure OAuth providers in Supabase
3. **Implement MFA**: Add multi-factor authentication
4. **Set up Monitoring**: Configure error tracking and analytics
5. **Add Audit Logs**: Track authentication events

## 📞 Support

- **Supabase Docs**: [https://supabase.com/docs](https://supabase.com/docs)
- **Resend Docs**: [https://resend.com/docs](https://resend.com/docs)
- **Next.js Auth**: [https://nextjs.org/docs/authentication](https://nextjs.org/docs/authentication)

---

🎉 **Congratulations!** Your authentication system is now ready for production use with Supabase Auth and Resend email service.