# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
# Python lib directories only (not frontend/lib)
backend/lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# macOS
.DS_Store

# Logs
*.log
logs/

# Database
*.db
*.sqlite

# Cache
.cache/
.parcel-cache/

# Testing
coverage/
.nyc_output/
.pytest_cache/

# API Keys and secrets
google_ads_config.yaml
service_account.json
*.pem
*.key

# Redis
dump.rdb

# Celery
celerybeat-schedule
celerybeat.pid

# Temporary files
*.tmp
*.temp