# Pre-commit configuration for security and code quality
# Install with: pre-commit install
# Run manually with: pre-commit run --all-files

repos:
  # General code formatting and linting
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-merge-conflict
      - id: check-added-large-files
        args: ['--maxkb=1024']  # Max 1MB files
      - id: check-case-conflict
      - id: check-docstring-first
      - id: debug-statements
      - id: name-tests-test
        args: ['--pytest-test-first']

  # Security: Secret detection
  - repo: https://github.com/trufflesecurity/trufflehog
    rev: v3.63.2
    hooks:
      - id: trufflehog
        name: TruffleHog Secret Detection
        description: Detect secrets in your code
        entry: bash -c 'trufflehog git file://. --since-commit HEAD --only-verified --fail --no-update'
        language: system
        stages: [commit, push]

  # Security: GitLeaks secret detection (alternative)
  - repo: https://github.com/zricethezav/gitleaks
    rev: v8.18.0
    hooks:
      - id: gitleaks
        name: GitLeaks Secret Detection
        description: Detect hardcoded secrets using GitLeaks
        entry: gitleaks detect --source . -v
        language: system
        stages: [commit]

  # Python security and code quality
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        name: Black Python formatter
        files: ^backend/.*\.py$
        args: ["--line-length=88"]

  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        name: isort import sorting
        files: ^backend/.*\.py$
        args: ["--profile", "black", "--line-length", "88"]

  - repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
        name: Flake8 Python linting
        files: ^backend/.*\.py$
        args: ["--max-line-length=88", "--extend-ignore=E203,W503"]

  # Python security: Bandit
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        name: Bandit Security Linter
        files: ^backend/.*\.py$
        args: ["-ll", "-x", "tests/"]
        exclude: ^backend/tests/

  # Python security: Safety (dependency vulnerability check)
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        files: ^backend/requirements.*\.txt$

  # Python type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        name: MyPy Type Checking
        files: ^backend/.*\.py$
        additional_dependencies:
          - types-requests
          - types-redis
        args: ["--config-file", "backend/mypy.ini"]

  # Frontend security and linting
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.56.0
    hooks:
      - id: eslint
        name: ESLint JavaScript/TypeScript linting
        files: ^frontend/.*\.(js|jsx|ts|tsx)$
        additional_dependencies:
          - eslint@8.56.0
          - "@next/eslint-config-next@14.0.4"
          - "@typescript-eslint/eslint-plugin@6.18.1"
        args: ["--fix"]

  # Frontend dependency audit
  - repo: local
    hooks:
      - id: npm-audit
        name: NPM Audit
        entry: bash -c 'cd frontend && npm audit --audit-level=moderate'
        language: system
        files: ^frontend/package.*\.json$
        pass_filenames: false

  # Docker security
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        name: Hadolint Dockerfile linter
        files: Dockerfile.*

  # Infrastructure as Code security
  - repo: https://github.com/bridgecrewio/checkov
    rev: 3.1.34
    hooks:
      - id: checkov
        name: Checkov IaC Security
        args: ["--framework", "dockerfile,github_actions", "--quiet"]

  # YAML/JSON security
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        name: Detect Secrets
        args: ['--baseline', '.secrets.baseline']
        exclude: ^(\.secrets\.baseline|backend/venv/|frontend/node_modules/)

  # Commit message security
  - repo: https://github.com/compilerla/conventional-pre-commit
    rev: v3.0.0
    hooks:
      - id: conventional-pre-commit
        name: Conventional Commit Format
        stages: [commit-msg]

  # Additional security checks
  - repo: local
    hooks:
      # Environment file security check
      - id: env-file-check
        name: Environment File Security Check
        entry: bash -c 'if find . -name "*.env" -not -path "./backend/.env.example" -not -path "./.github/*" | grep -q .; then echo "ERROR: .env files found in repository!"; exit 1; fi'
        language: system
        pass_filenames: false

      # API key pattern check
      - id: api-key-check
        name: API Key Pattern Check
        entry: bash -c 'if grep -r -E "(sk-[a-zA-Z0-9]{48}|pk_[a-zA-Z0-9]{64})" --exclude-dir=venv --exclude-dir=node_modules --exclude="*.md" .; then echo "ERROR: Potential API keys found!"; exit 1; fi'
        language: system
        pass_filenames: false

      # Database URL check
      - id: database-url-check
        name: Database URL Check
        entry: bash -c 'if grep -r -E "postgres://.*:.*@" --exclude-dir=venv --exclude-dir=node_modules --exclude="*.md" .; then echo "ERROR: Database URLs with credentials found!"; exit 1; fi'
        language: system
        pass_filenames: false

      # JWT secret check
      - id: jwt-secret-check
        name: JWT Secret Check
        entry: bash -c 'if grep -r -E "(eyJ[A-Za-z0-9_/+=]*\.eyJ[A-Za-z0-9_/+=]*\.[A-Za-z0-9_/+=]*)" --exclude-dir=venv --exclude-dir=node_modules --exclude="*.md" .; then echo "ERROR: JWT tokens found!"; exit 1; fi'
        language: system
        pass_filenames: false

# Global configuration
default_install_hook_types: [pre-commit, pre-push, commit-msg]
default_stages: [pre-commit]

# CI configuration
ci:
  autoupdate_schedule: weekly
  autoupdate_commit_msg: 'chore: update pre-commit hooks'