# AiLex Ad Agent System 🚀

AI-powered advertising automation platform that plans, launches, and optimizes Google Ads campaigns using CrewAI agents with minimal human intervention.

## 📊 Current Status: **Backend LIVE in Production** 🚀

**Phase 3 Complete + Backend Deployed** - Backend live on Fly.io, frontend deployment in progress.

### 🌐 **Live Production URLs**
- **Backend API**: https://ailex-ad-agent-backend.fly.dev ✅ **LIVE**
- **Health Check**: https://ailex-ad-agent-backend.fly.dev/api/v1/health/liveness ✅ **PASSING**
- **Frontend**: Vercel deployment configured (deployment in progress)

## Overview

This system delivers lower CAC and higher ROI by automating campaign management across multiple regions (USA and Belgium) with full EU regulatory compliance including GDPR, EU AI Act, and local advertising regulations.

## 🔍 **Enterprise-Grade Type Safety**

The system implements **ultra-strict type checking** across the entire stack:

### **Backend Type Safety (Python)**
- **MyPy Ultra-Strict**: `disallow_untyped_defs`, `strict_equality`, `warn_unreachable`
- **90% Type Coverage**: Enforced threshold with automated monitoring
- **Pydantic Models**: Runtime type validation for all API requests/responses
- **SQLAlchemy Types**: Database schema type safety with proper annotations

### **Frontend Type Safety (TypeScript)**
- **Strict Mode**: All TypeScript strict checks enabled
- **Advanced Safety**: `noUncheckedIndexedAccess`, `exactOptionalPropertyTypes`
- **React Types**: Strict component prop and state typing
- **API Types**: Type-safe HTTP client with generated schemas

### **CI/CD Type Enforcement**
- **Quality Gates**: Builds fail on type errors or coverage below 90%
- **Automated Reports**: Type coverage monitoring with PR comments
- **Fast Feedback**: Type checking in 2-3 minutes with modern tooling
- **Matrix Testing**: Type safety across Python 3.10/3.11 and Node.js 16/18/20

📖 **[View Complete Type System Documentation](./STRICT_TYPE_CHECKING_SUMMARY.md)**

## Core Features

- **Campaign Planning**: AI agent research for competitors, keywords, and audience demographics
- **Ad Asset Generation**: Automated creation of headlines, descriptions, sitelinks, and images
- **Campaign Management**: Google Ads API integration for Search & Performance Max campaigns
- **Real-Time Optimization**: Continuous bid, budget, and asset adjustments
- **A/B Testing**: Automated variant testing with statistical significance
- **Compliance**: GDPR, EU AI Act, and regional advertising regulation adherence
- **Reporting**: Performance dashboards with CAC/ROI metrics and decision logs

## Project Structure

```
├── backend/          # FastAPI backend with CrewAI agents
│   ├── agents/       # CrewAI agent implementations
│   ├── api/          # FastAPI endpoints
│   ├── models/       # Database models
│   ├── services/     # Business logic services
│   ├── utils/        # Utility functions
│   └── tests/        # Backend tests
├── frontend/         # Next.js frontend
│   ├── components/   # React components
│   ├── pages/        # Next.js pages
│   ├── hooks/        # Custom React hooks
│   ├── utils/        # Frontend utilities
│   └── types/        # TypeScript type definitions
├── docs/             # Documentation
├── scripts/          # Deployment and utility scripts
└── .github/          # GitHub Actions workflows
```

## 🛠️ Tech Stack

### Backend ✅ **FULLY IMPLEMENTED**
- **Runtime**: FastAPI (production-ready with comprehensive API)
- **Database**: Supabase PostgreSQL (CMS project - all tables created)
- **Authentication**: Supabase Auth + JWT tokens
- **Package Management**: uv (modern Python package manager)
- **Type Safety**: MyPy ultra-strict (90% coverage enforced) ✅ **ENTERPRISE-GRADE**
- **Agent Orchestration**: CrewAI Flows + Phoenix for tracing ✅ **COMPLETE**
- **LLMs**: GPT-4o + Gemini 2.5 Pro ✅ **INTEGRATED**
- **Vector DB**: Pinecone ✅ **CONFIGURED**
- **Scheduler**: Celery with Redis ✅ **IMPLEMENTED**
- **APIs**: Google Ads API, GA4 API ✅ **FULLY INTEGRATED**

### Frontend ✅ **IMPLEMENTED**
- **Framework**: Next.js 14 with TypeScript (complete page structure)
- **UI**: shadcn/ui + Tailwind CSS (component library ready)
- **Package Management**: pnpm (modern Node.js package manager)
- **Type Safety**: TypeScript ultra-strict mode ✅ **ENTERPRISE-GRADE**
- **State Management**: SWR for data fetching (configured)
- **Charts**: Recharts (configured)
- **Auth**: Supabase Auth (integrated)
- **Theme**: Dark/light mode support

### Infrastructure ✅ **FULLY IMPLEMENTED**
- **Backend Hosting**: Fly.io ✅ **PRODUCTION READY**
- **Frontend Hosting**: Vercel ✅ **PRODUCTION READY**
- **Database**: Supabase CMS project ✅ **ACTIVE & HEALTHY**
- **CI/CD**: GitHub Actions ✅ **7 WORKFLOWS IMPLEMENTED**
- **Multi-Container**: Docker Compose ✅ **3 ARCHITECTURES**
- **Monitoring**: Prometheus/Grafana ✅ **FULL OBSERVABILITY**
- **Type Safety**: Ultra-Strict TypeScript + MyPy ✅ **90% COVERAGE ENFORCED**

## Getting Started

### Prerequisites ✅ **CONFIGURED**
- Node.js 18+ ✅
- Python 3.11+ ✅
- uv (Python package manager) ✅
- pnpm (Node.js package manager) ✅
- Google Ads API credentials (ready for setup)
- Supabase account ✅ (CMS project active)
- OpenAI/Gemini API keys (configured in .env)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/Jpkay/googleads.git
cd googleads
```

2. Install backend dependencies:
```bash
cd backend
pip install -r requirements.txt
```

3. Install frontend dependencies:
```bash
cd frontend
npm install
```

4. Set up environment variables:
```bash
cp .env.example .env
# Fill in your API keys and configuration
```

5. Run the development servers:
```bash
# Backend
cd backend && uvicorn main:app --reload

# Frontend
cd frontend && npm run dev
```

## Development Roadmap

- [ ] CrewAI flow POC
- [ ] Google Ads integration (Search + P-Max)
- [ ] Supabase schema + reporting setup
- [ ] Frontend dashboard (Next.js)
- [ ] A/B testing & guardrails implementation
- [ ] Alerts + Live Optimization
- [ ] EU Compliance Integration

## Contributing

Please read our contributing guidelines and ensure all code follows our standards for compliance and security.

## License

MIT License - see LICENSE file for details.