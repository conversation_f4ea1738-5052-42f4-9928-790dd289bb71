# Docker Compose for split API/Worker architecture
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ailex-postgres
    environment:
      POSTGRES_DB: ailex_db
      POSTGRES_USER: ailex_user
      POSTGRES_PASSWORD: ailex_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ailex_user -d ailex_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis Cache and Message Broker
  redis:
    image: redis:7-alpine
    container_name: ailex-redis
    command: redis-server --requirepass redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # API Service (Lightweight)
  api:
    build: 
      context: .
      dockerfile: Dockerfile.api
    container_name: ailex-api
    ports:
      - "8000:8000"
    environment:
      - SERVICE_TYPE=api
      - ENVIRONMENT=development
      - DATABASE_URL=****************************************************/ailex_db
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - CELERY_BROKER_URL=redis://:redis_password@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:redis_password@redis:6379/2
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=true
      - LOG_LEVEL=INFO
    volumes:
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health/liveness"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Worker Service (Heavy Dependencies)
  worker:
    build: 
      context: .
      dockerfile: Dockerfile.worker
    container_name: ailex-worker
    ports:
      - "8001:8001"
    environment:
      - SERVICE_TYPE=worker
      - ENVIRONMENT=development
      - DATABASE_URL=****************************************************/ailex_db
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - CELERY_BROKER_URL=redis://:redis_password@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:redis_password@redis:6379/2
      - CELERY_WORKER=true
      - LOG_LEVEL=INFO
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/worker/health"]
      interval: 60s
      timeout: 30s
      retries: 3

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: ailex-network
