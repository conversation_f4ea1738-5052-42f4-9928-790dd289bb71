# AiLex Ad Agent System - Backend Makefile

.PHONY: help install dev test clean docker-build docker-run setup check

# Default target
.DEFAULT_GOAL := help

help: ## Show this help message
	@echo "AiLex Ad Agent System - Backend"
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}'

setup: ## Initial setup - copy env file and install dependencies
	@echo "Setting up AiLex backend..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "✓ Created .env file from .env.example"; \
		echo "⚠️  Please edit .env with your actual credentials"; \
	else \
		echo "✓ .env file already exists"; \
	fi
	@echo "Installing dependencies..."
	@pip install -r requirements.txt
	@echo "✓ Dependencies installed"
	@echo "Testing setup..."
	@python test_setup.py

install: ## Install dependencies
	pip install -r requirements.txt

dev: ## Run development server with auto-reload
	@echo "Starting development server..."
	@echo "API Documentation: http://localhost:8000/docs"
	@echo "Health Check: http://localhost:8000/api/v1/health/"
	python main.py

test: ## Run tests
	pytest

test-verbose: ## Run tests with verbose output
	pytest -v

test-coverage: ## Run tests with coverage report
	pytest --cov=. --cov-report=html --cov-report=term

typecheck: ## Run type checking with MyPy
	@echo "Running type checks with MyPy..."
	@if command -v mypy >/dev/null 2>&1; then \
		mypy .; \
	else \
		echo "⚠️  MyPy not installed. Install with: pip install mypy"; \
	fi

typecheck-report: ## Generate detailed type coverage report
	@echo "Generating type coverage report..."
	@if command -v mypy >/dev/null 2>&1; then \
		mypy --html-report mypy-report --txt-report mypy-report .; \
		echo "✓ Type coverage report generated in mypy-report/"; \
	else \
		echo "⚠️  MyPy not installed. Install with: pip install mypy"; \
	fi

typecheck-strict: ## Run strict type checking
	@echo "Running strict type checking..."
	@if command -v mypy >/dev/null 2>&1; then \
		mypy --strict .; \
	else \
		echo "⚠️  MyPy not installed. Install with: pip install mypy"; \
	fi

check: ## Check code setup and configuration
	python test_setup.py

lint: ## Run code linting with type checking
	@echo "Checking code style and types..."
	@if command -v black >/dev/null 2>&1; then \
		black --check .; \
	else \
		echo "⚠️  black not installed. Install with: pip install black"; \
	fi
	@if command -v isort >/dev/null 2>&1; then \
		isort --check-only .; \
	else \
		echo "⚠️  isort not installed. Install with: pip install isort"; \
	fi
	@make typecheck

format: ## Format code
	@echo "Formatting code..."
	@if command -v black >/dev/null 2>&1; then \
		black .; \
	else \
		echo "⚠️  black not installed. Install with: pip install black"; \
	fi
	@if command -v isort >/dev/null 2>&1; then \
		isort .; \
	else \
		echo "⚠️  isort not installed. Install with: pip install isort"; \
	fi

clean: ## Clean up cache files and temporary files
	@echo "Cleaning up..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type f -name ".coverage" -delete
	find . -type d -name "htmlcov" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	find . -type d -name "mypy-report" -exec rm -rf {} +
	@echo "✓ Cleanup complete"

# Docker commands
docker-build: ## Build Docker image
	docker build -t ailex-backend:latest .

docker-run: ## Run Docker container
	docker run -p 8000:8000 --env-file .env ailex-backend:latest

docker-dev: ## Run Docker Compose for development
	docker-compose up -d

docker-logs: ## Show Docker Compose logs
	docker-compose logs -f

docker-stop: ## Stop Docker Compose services
	docker-compose down

docker-clean: ## Clean up Docker resources
	docker-compose down -v
	docker system prune -f

# Database commands
db-migrate: ## Run database migrations (placeholder)
	@echo "Database migrations would be run here"
	@echo "Consider using Alembic for SQLAlchemy or Supabase migrations"

# Production commands
prod-build: ## Build production Docker image
	docker build -t ailex-backend:prod -f Dockerfile.prod .

prod-deploy: ## Deploy to production (placeholder)
	@echo "Production deployment would be handled here"
	@echo "Configure your deployment pipeline (Fly.io, AWS, etc.)"

# Monitoring commands
health: ## Check application health
	@echo "Checking application health..."
	@curl -s http://localhost:8000/api/v1/health/ | python -m json.tool || echo "❌ Application not running"

logs: ## Show application logs (for Docker Compose)
	docker-compose logs -f backend

# Development utilities
shell: ## Open Python shell with app context
	python -c "from main import app; import code; code.interact(local=locals())"

routes: ## Show all API routes
	python -c "from main import app; from fastapi.routing import APIRoute; [print(f'{route.methods} {route.path}') for route in app.routes if isinstance(route, APIRoute)]"

env-check: ## Check environment variables
	@echo "Checking environment configuration..."
	@python -c "from utils.config import settings; print(f'Environment: {settings.ENVIRONMENT}'); print(f'Debug: {settings.DEBUG}'); print(f'Host: {settings.HOST}:{settings.PORT}')"

# Requirements management
freeze: ## Generate requirements.txt from current environment
	pip freeze > requirements-freeze.txt
	@echo "✓ Current environment frozen to requirements-freeze.txt"

update: ## Update dependencies (be careful in production)
	pip install --upgrade -r requirements.txt

# Git hooks (if using)
pre-commit: ## Install pre-commit hooks
	@if command -v pre-commit >/dev/null 2>&1; then \
		pre-commit install; \
		echo "✓ Pre-commit hooks installed"; \
	else \
		echo "⚠️  pre-commit not installed. Install with: pip install pre-commit"; \
	fi

# Documentation
docs: ## Generate API documentation
	@echo "API documentation available at:"
	@echo "  Swagger UI: http://localhost:8000/docs"
	@echo "  ReDoc: http://localhost:8000/redoc"
	@echo "  OpenAPI JSON: http://localhost:8000/openapi.json"
	@echo ""
	@echo "Start the dev server and visit the URLs above"