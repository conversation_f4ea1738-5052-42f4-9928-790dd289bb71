# AiLex Ad Agent System - Backend Configuration
# Modern Python project configuration with uv package management

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "ailex-ad-agent-backend"
version = "1.0.0"
description = "AI-powered Google Ads campaign management system backend"
authors = [
    {name = "AiLex Development Team", email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.11"
keywords = ["ai", "google-ads", "automation", "fastapi", "agents"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
]

# Package configuration for hatchling
[tool.hatch.build.targets.wheel]
packages = ["."]

dependencies = [
    # FastAPI and web framework
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "pydantic>=2.6.1",
    "pydantic-settings>=2.1.0",

    # AI Agents and Tracing
    "crewai>=0.28.8",
    "crewai-tools>=0.1.6",

    # Google APIs
    "google-ads>=22.0.0",
    "google-analytics-data>=0.18.7",

    # AI/ML Models
    "openai>=1.13.3",
    "google-generativeai>=0.3.2",

    # Database
    "asyncpg>=0.29.0",
    "sqlalchemy[asyncio]>=2.0.25",
    "supabase>=2.3.4",
    "alembic>=1.13.1",

    # Background Tasks
    "celery[redis]>=5.3.4",
    "redis>=5.0.1",

    # HTTP and API
    "httpx>=0.26.0",
    "aiohttp>=3.9.1",
    "requests>=2.31.0",

    # Data Processing
    "pandas>=2.1.4",
    "numpy>=1.26.2",
    "python-dateutil>=2.8.2",

    # Utilities
    "structlog>=23.2.0",
    "python-dotenv>=1.0.0",
    "pyjwt>=2.8.0",
    "passlib[bcrypt]>=1.7.4",
    "python-jose[cryptography]>=3.3.0",

    # Monitoring and Observability
    "sentry-sdk[fastapi]>=1.40.0",
    "prometheus-client>=0.19.0",

    # Security
    "cryptography>=41.0.8",
    "bcrypt>=4.1.2",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.3",
    "pytest-asyncio>=0.23.2",
    "pytest-cov>=4.1.0",
    "pytest-xdist>=3.5.0",
    "pytest-timeout>=2.2.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.26.0",  # For testing async clients

    # Code Quality (using ruff for faster linting)
    "ruff>=0.1.9",
    "mypy>=1.8.0",
    "bandit>=1.7.5",
    "safety>=2.3.5",

    # Development Tools
    "pre-commit>=3.6.0",
    "ipython>=8.18.1",
    "rich>=13.7.0",
]

production = [
    # Production-only dependencies
    "gunicorn>=21.2.0",
]

[project.urls]
Homepage = "https://github.com/Jpkay/googleads"
Repository = "https://github.com/Jpkay/googleads.git"
Issues = "https://github.com/Jpkay/googleads/issues"

[project.scripts]
ailex-backend = "main:app"

# Tool configurations
[tool.uv]
dev-dependencies = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.23.2",
    "pytest-cov>=4.1.0",
    "pytest-xdist>=3.5.0",
    "pytest-timeout>=2.2.0",
    "pytest-mock>=3.12.0",
    "ruff>=0.1.9",
    "mypy>=1.8.0",
    "bandit>=1.7.5",
    "safety>=2.3.5",
    "pre-commit>=3.6.0",
    "ipython>=8.18.1",
    "rich>=13.7.0",
]



[tool.bandit]
exclude_dirs = ["tests", "venv", ".venv", "__pycache__", ".git"]
skips = ["B101", "B601"]  # Allow assert in tests, shell injection handled by middleware
severity = "medium"
confidence = "medium"

[tool.bandit.assert_used]
skips = ["*/tests/*", "*/*test*.py", "*/conftest.py"]

[tool.ruff]
# Ruff configuration (replaces black, isort, flake8)
line-length = 88
target-version = "py311"
extend-exclude = [
    ".eggs",
    ".git",
    ".mypy_cache",
    ".tox",
    ".venv",
    "venv",
    "_build",
    "buck-out",
    "build",
    "dist",
    "migrations",
    "__pycache__",
]

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "S",   # bandit (security)
]
ignore = [
    "E501",  # line too long (handled by formatter)
    "B008",  # do not perform function calls in argument defaults
    "S101",  # use of assert detected (allow in tests)
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["S101", "S106", "S311"]  # Allow assert, hardcoded passwords, and random in tests
"__init__.py" = ["F401"]  # Allow unused imports in __init__.py

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow tests",
    "security: Security-related tests",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

[tool.coverage.run]
source = ["."]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/.venv/*",
    "*/migrations/*",
    "manage.py",
    "*/settings/*",
    "*/node_modules/*",
    "*/__pycache__/*",
    "*/type-coverage-report/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "venv",
    ".venv",
    "migrations",
    "node_modules",
]
per-file-ignores = [
    "__init__.py:F401",
    "settings.py:E501",
]

[tool.safety]
# Safety configuration for vulnerability checking
ignore = []  # Add CVE IDs to ignore if false positives

[tool.semgrep]
# Semgrep SAST configuration
config = ["auto", "security-audit", "owasp-top-ten"]
exclude = ["tests/", "venv/", ".venv/", "node_modules/"]
severity = ["ERROR", "WARNING"]