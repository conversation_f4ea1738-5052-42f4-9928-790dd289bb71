"""
Worker service for CrewAI agents and heavy background processing.
Handles AI agent orchestration, data processing, and ML workloads.
"""

import os
import time
import asyncio
import signal
import threading
from typing import Dict, Any

from fastapi import FastAPI
from celery import Celery
import uvicorn

from utils.config import settings
from utils.logging import configure_logging, get_logger
from agents.orchestrator import campaign_orchestrator
from services.database import database_service
from services.redis_service import redis_service


# Configure logging
configure_logging(settings.LOG_LEVEL, settings.ENVIRONMENT)
logger = get_logger(__name__)

# Track worker start time
_start_time = time.time()

# Import shared Celery configuration
from celery_config import celery_app

# Create FastAPI app for worker health checks and monitoring
health_app = FastAPI(
    title="AiLex Worker Service",
    description="Background worker for AI agents and heavy processing",
    version=settings.VERSION,
    docs_url="/worker/docs",
    redoc_url="/worker/redoc",
)


@health_app.get("/worker/health")
async def worker_health() -> Dict[str, Any]:
    """
    Worker health check endpoint.
    
    Returns:
        dict: Worker health status
    """
    uptime_seconds = time.time() - _start_time
    
    # Check critical services
    checks = {}
    
    # Database check
    try:
        if database_service:
            db_health = await database_service.health_check()
            checks["database"] = db_health.get("status") == "healthy"
        else:
            checks["database"] = False
    except Exception:
        checks["database"] = False
    
    # Redis check
    try:
        if redis_service:
            redis_health = await redis_service.health_check()
            checks["redis"] = redis_health.get("status") == "healthy"
        else:
            checks["redis"] = False
    except Exception:
        checks["redis"] = False
    
    # Celery check
    try:
        celery_inspect = celery_app.control.inspect()
        active_tasks = celery_inspect.active()
        checks["celery"] = active_tasks is not None
    except Exception:
        checks["celery"] = False
    
    all_healthy = all(checks.values())
    
    return {
        "status": "healthy" if all_healthy else "unhealthy",
        "service_type": "worker",
        "uptime_seconds": round(uptime_seconds, 2),
        "timestamp": time.time(),
        "checks": checks,
        "capabilities": [
            "crewai_agents",
            "campaign_orchestration",
            "data_processing",
            "ml_workloads",
            "background_tasks"
        ],
        "heavy_dependencies": [
            "crewai",
            "pandas", 
            "numpy",
            "google-ads",
            "pinecone"
        ]
    }


@health_app.get("/worker/stats")
async def worker_stats() -> Dict[str, Any]:
    """
    Worker statistics endpoint.
    
    Returns:
        dict: Worker performance statistics
    """
    try:
        celery_inspect = celery_app.control.inspect()
        
        # Get worker stats
        stats = celery_inspect.stats()
        active_tasks = celery_inspect.active()
        scheduled_tasks = celery_inspect.scheduled()
        
        return {
            "worker_stats": stats,
            "active_tasks": len(active_tasks.get(list(active_tasks.keys())[0], [])) if active_tasks else 0,
            "scheduled_tasks": len(scheduled_tasks.get(list(scheduled_tasks.keys())[0], [])) if scheduled_tasks else 0,
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error("Failed to get worker stats", error=str(e))
        return {
            "error": "Failed to get worker stats",
            "timestamp": time.time()
        }


class WorkerService:
    """Main worker service class."""
    
    def __init__(self):
        self.celery_worker = None
        self.health_server = None
        self.running = False
    
    async def start_health_server(self):
        """Start the health check server."""
        config = uvicorn.Config(
            health_app,
            host="0.0.0.0",
            port=8001,
            log_level="info"
        )
        self.health_server = uvicorn.Server(config)
        await self.health_server.serve()
    
    def start_celery_worker(self):
        """Start the Celery worker."""
        logger.info("Starting Celery worker")
        
        # Start Celery worker
        celery_app.worker_main([
            "worker",
            "--loglevel=info",
            "--concurrency=2",
            "--max-tasks-per-child=1000",
            "--time-limit=1800",  # 30 minutes
            "--soft-time-limit=1500",  # 25 minutes
        ])
    
    async def start(self):
        """Start the worker service."""
        logger.info("Starting AiLex Worker Service")
        self.running = True
        
        # Initialize services
        try:
            if database_service:
                await database_service.initialize()
                logger.info("Database service initialized")
            
            if redis_service:
                await redis_service.initialize()
                logger.info("Redis service initialized")
                
        except Exception as e:
            logger.error("Failed to initialize services", error=str(e))
        
        # Start Celery worker in a separate thread
        celery_thread = threading.Thread(
            target=self.start_celery_worker,
            daemon=True
        )
        celery_thread.start()
        logger.info("Celery worker thread started")
        
        # Start health server (this will block)
        await self.start_health_server()
    
    async def stop(self):
        """Stop the worker service."""
        logger.info("Stopping AiLex Worker Service")
        self.running = False
        
        if self.health_server:
            self.health_server.should_exit = True
        
        # Stop Celery worker
        celery_app.control.shutdown()


# Global worker service instance
worker_service = WorkerService()


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, shutting down worker")
    asyncio.create_task(worker_service.stop())


async def main():
    """Main entry point."""
    # Register signal handlers
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        await worker_service.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error("Worker service error", error=str(e))
    finally:
        await worker_service.stop()


if __name__ == "__main__":
    asyncio.run(main())
