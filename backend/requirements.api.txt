# API-only requirements - Lightweight for fast deployment
# Excludes CrewAI, heavy ML libraries, and worker-only dependencies

# FastAPI and web framework
fastapi>=0.100.0
uvicorn>=0.20.0
python-multipart>=0.0.6
pydantic>=2.6.1
pydantic-settings>=2.1.0

# Database and Storage (required for API)
supabase>=2.18.0
asyncpg>=0.28.0
sqlalchemy[asyncio]>=2.0.0
alembic>=1.13.0
psycopg2-binary>=2.9.0

# Authentication and Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
cryptography>=41.0.0
itsdangerous>=2.0.0

# HTTP and Requests
requests>=2.31.0
httpx>=0.26.0

# Environment and Configuration
python-dotenv>=1.0.0

# Logging and Monitoring
structlog>=23.0.0
sentry-sdk[fastapi]>=1.38.0

# Email Service
resend>=0.8.0
email-validator>=2.0.0

# Rate Limiting and Protection
slowapi>=0.1.9
limits>=3.8.0

# Additional utilities
aiofiles>=23.0.0

# Task Queue Client (for triggering workers)
celery>=5.3.0
redis>=5.0.0

# Minimal data processing (for API responses only)
# Note: No pandas, numpy, scipy - these are worker-only
