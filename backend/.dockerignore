# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs and databases
*.log
*.sqlite
*.sqlite3
*.db

# Test files and coverage
test_*.py
*_test.py
tests/
.coverage
.pytest_cache/
.tox/
htmlcov/

# Documentation and markdown files
*.md
docs/

# Configuration files (secrets handled via Fly.io secrets)
.env
.env.*

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Development scripts
fix_*.py
test_*.py
demo_*.py
quick_deploy.sh

# Type checking
.mypy_cache/
mypy-report.txt

# Git
.git/
.gitignore

# Fly.io
.dockerignore