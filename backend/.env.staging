# AiLex Ad Agent System - Staging Environment Configuration

# Application Settings
APP_NAME="AiLex Ad Agent System (Staging)"
VERSION="1.0.0"
ENVIRONMENT="staging"
DEBUG=false
HOST="0.0.0.0"
PORT=8000

# CORS Settings
CORS_ORIGINS="https://ailex-staging.vercel.app,https://ailex-frontend-staging.vercel.app"

# Trusted Hosts
TRUSTED_HOSTS="ailex-ad-agent-backend-staging.fly.dev"

# Database Settings (Fly.io PostgreSQL - Staging)
DATABASE_URL=""  # Set automatically by Fly.io PostgreSQL attachment

# Redis Settings (Fly.io Redis - Staging)
REDIS_URL=""  # Set automatically by Fly.io Redis attachment

# Google Ads API Settings (Staging/Sandbox)
GOOGLE_ADS_DEVELOPER_TOKEN=""  # Set in Fly.io secrets
GOOGLE_ADS_CLIENT_ID=""  # Set in Fly.io secrets
GOOGLE_ADS_CLIENT_SECRET=""  # Set in Fly.io secrets
GOOGLE_ADS_REFRESH_TOKEN=""  # Set in Fly.io secrets
GOOGLE_ADS_CUSTOMER_ID=""  # Use test customer ID

# Google Analytics Settings (Staging)
GOOGLE_ANALYTICS_PROPERTY_ID=""  # Set in Fly.io secrets
GOOGLE_ANALYTICS_CREDENTIALS_PATH="/app/credentials/google-analytics-staging.json"

# OpenAI Settings
OPENAI_API_KEY=""  # Set in Fly.io secrets
OPENAI_MODEL="gpt-4o"

# Google Gemini Settings
GEMINI_API_KEY=""  # Set in Fly.io secrets

# Pinecone Settings (Staging)
PINECONE_API_KEY=""  # Set in Fly.io secrets
PINECONE_ENVIRONMENT=""  # Set in Fly.io secrets
PINECONE_INDEX_NAME="ailex-memory-staging"

# Celery Settings
CELERY_BROKER_URL=""  # Set automatically by Redis attachment
CELERY_RESULT_BACKEND=""  # Set automatically by Redis attachment

# Logging Settings
LOG_LEVEL="DEBUG"

# Sentry Settings (Staging Environment)
SENTRY_DSN=""  # Set in Fly.io secrets
SENTRY_TRACES_SAMPLE_RATE=0.3
SENTRY_PROFILES_SAMPLE_RATE=0.3

# Phoenix Tracing Settings
PHOENIX_COLLECTOR_ENDPOINT=""  # Set in Fly.io secrets if using
PHOENIX_PROJECT_NAME="ailex-ad-agents-staging"

# Rate Limiting (More lenient for testing)
RATE_LIMIT_REQUESTS_PER_MINUTE=500

# Campaign Optimization Settings (More conservative for testing)
MAX_BID_ADJUSTMENT_PERCENT=10.0
MAX_BUDGET_ADJUSTMENT_PERCENT_PER_HOUR=2.0
OPTIMIZATION_INTERVAL_MINUTES=5

# A/B Testing Settings
MIN_SAMPLE_SIZE=50
STATISTICAL_SIGNIFICANCE_THRESHOLD=0.05

# GDPR and Compliance Settings
GDPR_ENABLED=true
DATA_RETENTION_DAYS=90

# Webhooks and Notifications
SLACK_WEBHOOK_URL=""  # Set in Fly.io secrets if using