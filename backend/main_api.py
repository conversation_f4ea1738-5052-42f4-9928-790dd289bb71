"""
API-only FastAPI application for lightweight deployment.
Excludes CrewAI agents and heavy dependencies for fast startup.
"""

import os
import time
import sentry_sdk
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.starlette import StarletteIntegration

# Import only lightweight API endpoints
from api.health import router as health_router
from api.auth import router as auth_router
from api.campaigns import router as campaigns_router
from api.analytics import router as analytics_router
from api.google_ads import router as google_ads_router

from utils.config import settings
from utils.logging import configure_logging
from utils.exceptions import (
    CustomException,
    custom_exception_handler,
    validation_exception_handler,
)
from middleware.integration import middleware_manager, setup_exception_handlers


# Track application start time
_start_time = time.time()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown events.
    """
    # Startup
    configure_logging(settings.LOG_LEVEL, settings.ENVIRONMENT)
    print("Starting AiLex API-only service")
    
    # Initialize lightweight services only
    try:
        # Initialize middleware manager with API-only configuration
        await middleware_manager.initialize(
            app=app,
            redis_service=None,  # Optional for API-only
            enable_auth=True,
            enable_rate_limiting=True,
            enable_metrics=True,
        )
        logger.info("API-only middleware initialized successfully")
    except Exception as e:
        print(f"Failed to initialize middleware: {e}")
        # Continue anyway for API-only mode
    
    yield
    
    # Shutdown
    print("Shutting down AiLex API-only service")
    await middleware_manager.cleanup()


def create_app() -> FastAPI:
    """
    Create and configure the API-only FastAPI application.

    Returns:
        FastAPI: Configured FastAPI application instance
    """
    # Configure logging
    configure_logging(settings.LOG_LEVEL, settings.ENVIRONMENT)

    import structlog
    logger = structlog.get_logger(__name__)
    
    # Initialize Sentry for error tracking
    if settings.SENTRY_DSN:
        sentry_sdk.init(
            dsn=settings.SENTRY_DSN,
            integrations=[
                FastApiIntegration(auto_enabling=True),
                StarletteIntegration(auto_enabling=True),
            ],
            environment=settings.ENVIRONMENT,
            traces_sample_rate=settings.SENTRY_TRACES_SAMPLE_RATE,
            profiles_sample_rate=settings.SENTRY_PROFILES_SAMPLE_RATE,
        )

    # Create FastAPI application with lifespan
    app = FastAPI(
        title="AiLex Ad Agent System - API",
        description="Lightweight API service for Google Ads campaign management",
        version=settings.VERSION,
        docs_url="/docs" if settings.ENVIRONMENT != "production" else None,
        redoc_url="/redoc" if settings.ENVIRONMENT != "production" else None,
        openapi_url="/openapi.json" if settings.ENVIRONMENT != "production" else None,
        lifespan=lifespan,
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
        allow_headers=["*"],
    )

    # Exception handlers
    setup_exception_handlers(app)
    app.add_exception_handler(CustomException, custom_exception_handler)
    app.add_exception_handler(422, validation_exception_handler)
    
    # Include API-only routers
    app.include_router(
        health_router,
        prefix="/api/v1/health",
        tags=["health"],
    )
    
    app.include_router(
        auth_router,
        prefix="/api/v1/auth",
        tags=["authentication"],
    )
    
    app.include_router(
        campaigns_router,
        prefix="/api/v1/campaigns",
        tags=["campaigns"],
    )
    
    app.include_router(
        analytics_router,
        prefix="/api/v1/analytics",
        tags=["analytics"],
    )
    
    app.include_router(
        google_ads_router,
        prefix="/api/v1/google-ads",
        tags=["google-ads"],
    )
    
    # Note: Agents router excluded - handled by worker service
    
    return app


# Create the application instance
app = create_app()


@app.get("/", include_in_schema=False)
async def root() -> Dict[str, Any]:
    """
    Root endpoint returning API information.
    
    Returns:
        dict: API information and status
    """
    uptime_seconds = time.time() - _start_time
    
    return {
        "name": "AiLex Ad Agent System - API",
        "version": settings.VERSION,
        "status": "operational",
        "service_type": "api",
        "environment": settings.ENVIRONMENT,
        "uptime_seconds": round(uptime_seconds, 2),
        "docs": "/docs" if settings.ENVIRONMENT != "production" else "disabled",
        "worker_service": "separate",
        "features": [
            "authentication",
            "campaign_management", 
            "analytics",
            "google_ads_integration"
        ],
        "excluded_features": [
            "ai_agents",
            "crewai_orchestration",
            "heavy_data_processing"
        ]
    }


@app.get("/api/v1/service-info", include_in_schema=False)
async def service_info() -> Dict[str, Any]:
    """
    Service information endpoint for monitoring.
    
    Returns:
        dict: Service configuration and capabilities
    """
    return {
        "service_type": "api",
        "lightweight": True,
        "dependencies_excluded": [
            "crewai",
            "crewai-tools", 
            "pandas",
            "numpy",
            "scipy",
            "pinecone-client"
        ],
        "estimated_size_reduction": "~850MB",
        "deployment_speed": "fast",
        "worker_communication": "via_celery_redis"
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
