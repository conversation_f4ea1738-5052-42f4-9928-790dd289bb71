# Fly.io configuration for Worker service (CrewAI and heavy processing)
app = "ailex-ad-agent-worker"
primary_region = "iad"

[build]
  dockerfile = "Dockerfile.worker"

[env]
  SERVICE_TYPE = "worker"
  ENVIRONMENT = "production"
  LOG_LEVEL = "INFO"
  CELERY_WORKER = "true"
  PORT = "8001"

[http_service]
  internal_port = 8001
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  max_machines_running = 3
  processes = ["app"]
  
  [http_service.concurrency]
    type = "requests"
    hard_limit = 100
    soft_limit = 80

  [[http_service.checks]]
    grace_period = "30s"  # Longer grace period for worker startup
    interval = "60s"
    method = "GET"
    timeout = "10s"
    path = "/worker/health"

[[vm]]
  memory = "2gb"  # More memory for CrewAI and ML workloads
  cpu_kind = "shared"
  cpus = 2

# Deploy configuration
[deploy]
  strategy = "rolling"
  max_unavailable = 0.5

# Metrics
[metrics]
  port = 9091
  path = "/worker/metrics"
