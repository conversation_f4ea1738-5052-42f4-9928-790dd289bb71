#!/usr/bin/env python3
"""
Test script to verify the backend setup is working correctly.
Run this after installing dependencies to check if everything is configured properly.
"""

import sys
import os
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        from fastapi import FastAPI
        print("✓ FastAPI")
    except ImportError as e:
        print(f"✗ FastAPI: {e}")
        return False
    
    try:
        import structlog
        print("✓ structlog")
    except ImportError as e:
        print(f"✗ structlog: {e}")
        return False
    
    try:
        from pydantic_settings import BaseSettings
        print("✓ pydantic-settings")
    except ImportError as e:
        print(f"✗ pydantic-settings: {e}")
        return False
    
    try:
        import sentry_sdk
        print("✓ sentry_sdk")
    except ImportError as e:
        print(f"✗ sentry_sdk: {e}")
        return False
    
    return True

def test_local_modules():
    """Test that local modules can be imported."""
    print("\nTesting local modules...")
    
    try:
        from utils.config import settings
        print("✓ utils.config")
    except ImportError as e:
        print(f"✗ utils.config: {e}")
        return False
    
    try:
        from utils.logging import configure_logging
        print("✓ utils.logging")
    except ImportError as e:
        print(f"✗ utils.logging: {e}")
        return False
    
    try:
        from utils.exceptions import CustomException
        print("✓ utils.exceptions")
    except ImportError as e:
        print(f"✗ utils.exceptions: {e}")
        return False
    
    try:
        from models.common import BaseResponse
        print("✓ models.common")
    except ImportError as e:
        print(f"✗ models.common: {e}")
        return False
    
    try:
        from models.campaigns import Campaign
        print("✓ models.campaigns")
    except ImportError as e:
        print(f"✗ models.campaigns: {e}")
        return False
    
    try:
        from api.health import router
        print("✓ api.health")
    except ImportError as e:
        print(f"✗ api.health: {e}")
        return False
    
    return True

def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        from utils.config import settings
        print(f"✓ App name: {settings.APP_NAME}")
        print(f"✓ Environment: {settings.ENVIRONMENT}")
        print(f"✓ Version: {settings.VERSION}")
        print(f"✓ Host: {settings.HOST}")
        print(f"✓ Port: {settings.PORT}")
        return True
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

def test_fastapi_app():
    """Test FastAPI app creation."""
    print("\nTesting FastAPI app creation...")
    
    try:
        # Import without creating the app to avoid dependency issues
        from main import create_app
        print("✓ create_app function available")
        
        # Note: We can't actually create the app without all dependencies
        # but we can verify the function exists
        return True
    except ImportError as e:
        print(f"✗ Cannot import create_app: {e}")
        return False
    except Exception as e:
        print(f"✗ App creation error: {e}")
        return False

def main():
    """Run all tests."""
    print("AiLex Ad Agent System - Backend Setup Test")
    print("=" * 50)
    
    # Test environment
    print(f"Python version: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    print(f"Backend directory: {backend_dir}")
    
    # Run tests
    tests = [
        ("Imports", test_imports),
        ("Local Modules", test_local_modules),
        ("Configuration", test_configuration),
        ("FastAPI App", test_fastapi_app),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Backend setup is ready.")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Copy .env.example to .env and configure")
        print("3. Start Redis: redis-server")
        print("4. Run the app: python main.py")
        return True
    else:
        print(f"\n❌ {total - passed} tests failed. Please check the setup.")
        print("\nCommon issues:")
        print("- Missing dependencies: pip install -r requirements.txt")
        print("- Missing environment file: cp .env.example .env")
        print("- Check Python path and module imports")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)