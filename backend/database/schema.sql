-- AiLex Ad Agent System Database Schema
-- Production-ready PostgreSQL schema for Supabase
-- Includes proper indexes, constraints, and GDPR compliance

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Enable Row Level Security for all tables
SET ROLE postgres;

-- ================================================================
-- CAMPAIGNS TABLE
-- ================================================================
CREATE TABLE IF NOT EXISTS campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL CHECK (type IN (
        'search', 'display', 'shopping', 'video', 
        'performance_max', 'discovery', 'local', 'smart'
    )),
    status VARCHAR(50) NOT NULL DEFAULT 'draft' CHECK (status IN (
        'draft', 'active', 'paused', 'removed', 'ended', 'deleted'
    )),
    
    -- Budget configuration
    budget_amount DECIMAL(12,2) NOT NULL CHECK (budget_amount > 0),
    budget_currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    budget_delivery_method VARCHAR(20) DEFAULT 'standard',
    total_budget_limit DECIMAL(12,2),
    
    -- Bidding configuration
    bidding_strategy VARCHAR(50) NOT NULL DEFAULT 'manual_cpc' CHECK (bidding_strategy IN (
        'manual_cpc', 'enhanced_cpc', 'target_cpa', 'target_roas',
        'maximize_clicks', 'maximize_conversions', 'maximize_conversion_value'
    )),
    target_cpa DECIMAL(10,2),
    target_roas DECIMAL(10,4),
    max_cpc DECIMAL(10,2),
    
    -- Targeting configuration
    targeting JSONB NOT NULL DEFAULT '{}',
    target_locations TEXT[] DEFAULT '{}',
    target_languages TEXT[] DEFAULT '{}',
    keywords TEXT[] DEFAULT '{}',
    negative_keywords TEXT[] DEFAULT '{}',
    
    -- Scheduling
    start_date TIMESTAMPTZ,
    end_date TIMESTAMPTZ,
    ad_schedule JSONB,
    
    -- Google Ads integration
    google_ads_id VARCHAR(255),
    customer_id VARCHAR(255),
    
    -- AI optimization
    auto_optimization_enabled BOOLEAN DEFAULT true,
    optimization_score DECIMAL(3,2) CHECK (optimization_score BETWEEN 0 AND 1),
    last_optimized TIMESTAMPTZ,
    
    -- Metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    created_by UUID,
    updated_by UUID,
    
    -- GDPR Compliance
    data_processing_consent BOOLEAN DEFAULT false,
    data_retention_expires_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT campaigns_end_after_start CHECK (end_date IS NULL OR end_date > start_date),
    CONSTRAINT campaigns_valid_budget CHECK (total_budget_limit IS NULL OR total_budget_limit >= budget_amount)
);

-- ================================================================
-- AD_GROUPS TABLE
-- ================================================================
CREATE TABLE IF NOT EXISTS ad_groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'enabled' CHECK (status IN (
        'enabled', 'paused', 'removed'
    )),
    
    -- Bidding configuration
    bid_strategy VARCHAR(50) NOT NULL DEFAULT 'manual_cpc',
    max_cpc DECIMAL(10,2),
    target_cpa DECIMAL(10,2),
    
    -- Keywords and targeting
    keywords JSONB NOT NULL DEFAULT '[]',
    negative_keywords TEXT[] DEFAULT '{}',
    targeting JSONB DEFAULT '{}',
    
    -- Google Ads integration
    google_ads_id VARCHAR(255),
    
    -- Metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    created_by UUID,
    updated_by UUID,
    
    -- Constraints
    UNIQUE(campaign_id, name, deleted_at)
);

-- ================================================================
-- ADS TABLE
-- ================================================================
CREATE TABLE IF NOT EXISTS ads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ad_group_id UUID NOT NULL REFERENCES ad_groups(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL CHECK (type IN (
        'text_ad', 'expanded_text_ad', 'responsive_search_ad',
        'display_ad', 'image_ad', 'video_ad', 'shopping_ad', 'call_ad'
    )),
    status VARCHAR(50) NOT NULL DEFAULT 'enabled' CHECK (status IN (
        'enabled', 'paused', 'removed', 'pending_review',
        'under_review', 'approved', 'disapproved'
    )),
    
    -- Ad content
    headlines JSONB NOT NULL DEFAULT '[]',
    descriptions JSONB NOT NULL DEFAULT '[]',
    display_url VARCHAR(255),
    final_urls TEXT[] DEFAULT '{}',
    
    -- Assets
    assets JSONB DEFAULT '{}',
    image_assets JSONB DEFAULT '[]',
    video_assets JSONB DEFAULT '[]',
    
    -- Performance tracking
    performance JSONB DEFAULT '{}',
    approval_status VARCHAR(50),
    policy_summary TEXT,
    
    -- Google Ads integration
    google_ads_id VARCHAR(255),
    
    -- Metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    created_by UUID,
    updated_by UUID
);

-- ================================================================
-- AGENTS TABLE
-- ================================================================
CREATE TABLE IF NOT EXISTS agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN (
        'campaign_planning', 'ad_asset_generation', 'keyword_research',
        'bid_optimization', 'budget_management', 'performance_analysis',
        'audience_targeting', 'competitor_analysis', 'content_optimization',
        'quality_assurance'
    )),
    status VARCHAR(50) NOT NULL DEFAULT 'created' CHECK (status IN (
        'created', 'initializing', 'active', 'busy', 'idle',
        'paused', 'stopped', 'error', 'maintenance'
    )),
    
    -- Configuration
    config JSONB NOT NULL DEFAULT '{}',
    capabilities JSONB DEFAULT '[]',
    
    -- Assignment
    campaign_id UUID REFERENCES campaigns(id) ON DELETE SET NULL,
    user_id UUID,
    team_id UUID,
    
    -- Performance metrics
    tasks_completed INTEGER DEFAULT 0,
    tasks_failed INTEGER DEFAULT 0,
    average_execution_time DECIMAL(10,2),
    success_rate DECIMAL(5,4) CHECK (success_rate BETWEEN 0 AND 1),
    
    -- Status tracking
    last_activity TIMESTAMPTZ,
    last_error TEXT,
    uptime_hours DECIMAL(10,2),
    
    -- Resource usage
    memory_usage_mb DECIMAL(10,2),
    cpu_usage_percent DECIMAL(5,2) CHECK (cpu_usage_percent BETWEEN 0 AND 100),
    
    -- Version control
    version VARCHAR(50) NOT NULL DEFAULT '1.0.0',
    
    -- Metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    created_by UUID,
    updated_by UUID,
    
    -- Constraints
    UNIQUE(name, deleted_at)
);

-- ================================================================
-- AGENT_TASKS TABLE
-- ================================================================
CREATE TABLE IF NOT EXISTS agent_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
    campaign_id UUID REFERENCES campaigns(id) ON DELETE SET NULL,
    
    -- Task details
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    type VARCHAR(100) NOT NULL,
    priority VARCHAR(20) NOT NULL DEFAULT 'normal' CHECK (priority IN (
        'low', 'normal', 'high', 'urgent'
    )),
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending', 'queued', 'running', 'completed', 'failed', 'cancelled', 'timeout'
    )),
    
    -- Task data
    input_data JSONB NOT NULL DEFAULT '{}',
    output_data JSONB,
    context JSONB DEFAULT '{}',
    
    -- Execution details
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    execution_time_seconds DECIMAL(10,3),
    retry_count INTEGER DEFAULT 0,
    
    -- Results
    result TEXT,
    error_message TEXT,
    logs JSONB DEFAULT '[]',
    
    -- Dependencies
    parent_task_id UUID REFERENCES agent_tasks(id) ON DELETE SET NULL,
    dependent_task_ids UUID[] DEFAULT '{}',
    
    -- Scheduling
    scheduled_at TIMESTAMPTZ,
    deadline TIMESTAMPTZ,
    
    -- Metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_by UUID,
    
    -- Constraints
    CONSTRAINT agent_tasks_valid_execution_time CHECK (
        (started_at IS NULL AND completed_at IS NULL) OR
        (started_at IS NOT NULL AND (completed_at IS NULL OR completed_at >= started_at))
    )
);

-- ================================================================
-- PERFORMANCE_METRICS TABLE
-- ================================================================
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    ad_group_id UUID REFERENCES ad_groups(id) ON DELETE CASCADE,
    ad_id UUID REFERENCES ads(id) ON DELETE CASCADE,
    
    -- Date dimension
    date DATE NOT NULL,
    hour INTEGER CHECK (hour BETWEEN 0 AND 23),
    
    -- Core metrics
    impressions BIGINT DEFAULT 0 CHECK (impressions >= 0),
    clicks BIGINT DEFAULT 0 CHECK (clicks >= 0),
    conversions DECIMAL(10,3) DEFAULT 0 CHECK (conversions >= 0),
    cost DECIMAL(12,2) DEFAULT 0 CHECK (cost >= 0),
    revenue DECIMAL(12,2) CHECK (revenue >= 0),
    
    -- Calculated metrics (stored for performance)
    ctr DECIMAL(8,6) CHECK (ctr BETWEEN 0 AND 1),
    cpc DECIMAL(10,2) CHECK (cpc >= 0),
    cpm DECIMAL(10,2) CHECK (cpm >= 0),
    conversion_rate DECIMAL(8,6) CHECK (conversion_rate BETWEEN 0 AND 1),
    cost_per_conversion DECIMAL(10,2) CHECK (cost_per_conversion >= 0),
    roas DECIMAL(10,4) CHECK (roas >= 0),
    roi DECIMAL(10,4),
    
    -- Quality metrics
    quality_score DECIMAL(3,1) CHECK (quality_score BETWEEN 1 AND 10),
    
    -- Additional metrics
    view_through_conversions DECIMAL(10,3) DEFAULT 0,
    interaction_rate DECIMAL(8,6),
    average_position DECIMAL(4,2),
    
    -- Metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(campaign_id, ad_group_id, ad_id, date, hour),
    CONSTRAINT performance_metrics_valid_clicks CHECK (clicks <= impressions),
    CONSTRAINT performance_metrics_valid_conversions CHECK (conversions <= clicks)
);

-- ================================================================
-- COMPLIANCE_LOGS TABLE
-- ================================================================
CREATE TABLE IF NOT EXISTS compliance_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entity_type VARCHAR(50) NOT NULL CHECK (entity_type IN (
        'campaign', 'ad_group', 'ad', 'agent', 'task', 'user'
    )),
    entity_id UUID NOT NULL,
    
    -- Compliance check details
    check_type VARCHAR(100) NOT NULL CHECK (check_type IN (
        'gdpr_consent', 'data_retention', 'policy_compliance',
        'ad_approval', 'keyword_policy', 'targeting_policy',
        'budget_compliance', 'bid_compliance', 'performance_threshold'
    )),
    result VARCHAR(20) NOT NULL CHECK (result IN (
        'passed', 'failed', 'warning', 'pending'
    )),
    
    -- Details and recommendations
    details JSONB NOT NULL DEFAULT '{}',
    recommendations JSONB DEFAULT '[]',
    severity VARCHAR(20) DEFAULT 'low' CHECK (severity IN (
        'low', 'medium', 'high', 'critical'
    )),
    
    -- Resolution tracking
    resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMPTZ,
    resolved_by UUID,
    resolution_notes TEXT,
    
    -- Metadata
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_by UUID,
    
    -- Indexes will be created separately
    INDEX idx_compliance_logs_entity ON compliance_logs(entity_type, entity_id),
    INDEX idx_compliance_logs_timestamp ON compliance_logs(timestamp),
    INDEX idx_compliance_logs_unresolved ON compliance_logs(resolved, severity) WHERE NOT resolved
);

-- ================================================================
-- ADDITIONAL UTILITY TABLES
-- ================================================================

-- Keywords table for better keyword management
CREATE TABLE IF NOT EXISTS keywords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ad_group_id UUID NOT NULL REFERENCES ad_groups(id) ON DELETE CASCADE,
    text VARCHAR(255) NOT NULL,
    match_type VARCHAR(20) NOT NULL CHECK (match_type IN (
        'exact', 'phrase', 'broad', 'broad_modified'
    )),
    status VARCHAR(20) NOT NULL DEFAULT 'enabled' CHECK (status IN (
        'enabled', 'paused', 'removed'
    )),
    negative BOOLEAN DEFAULT false,
    
    -- Bidding
    max_cpc DECIMAL(10,2),
    
    -- Performance
    quality_score INTEGER CHECK (quality_score BETWEEN 1 AND 10),
    
    -- Google Ads integration
    google_ads_id VARCHAR(255),
    
    -- Metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(ad_group_id, text, match_type, negative)
);

-- Budget history for tracking changes
CREATE TABLE IF NOT EXISTS budget_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    
    -- Budget change details
    old_amount DECIMAL(12,2),
    new_amount DECIMAL(12,2) NOT NULL,
    change_reason VARCHAR(255),
    changed_by_agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
    
    -- Metadata
    changed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    changed_by UUID
);

-- Optimization history
CREATE TABLE IF NOT EXISTS optimization_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
    ad_group_id UUID REFERENCES ad_groups(id) ON DELETE CASCADE,
    ad_id UUID REFERENCES ads(id) ON DELETE CASCADE,
    agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
    
    -- Optimization details
    optimization_type VARCHAR(100) NOT NULL,
    changes_made JSONB NOT NULL DEFAULT '{}',
    expected_impact JSONB DEFAULT '{}',
    actual_impact JSONB DEFAULT '{}',
    
    -- Performance
    confidence_score DECIMAL(3,2) CHECK (confidence_score BETWEEN 0 AND 1),
    
    -- Metadata
    applied_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    measured_at TIMESTAMPTZ
);

-- ================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ================================================================

-- Update updated_at timestamp automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for all main tables
CREATE TRIGGER update_campaigns_updated_at BEFORE UPDATE ON campaigns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ad_groups_updated_at BEFORE UPDATE ON ad_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ads_updated_at BEFORE UPDATE ON ads FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_agent_tasks_updated_at BEFORE UPDATE ON agent_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_performance_metrics_updated_at BEFORE UPDATE ON performance_metrics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ================================================================
-- ROW LEVEL SECURITY POLICIES
-- ================================================================

-- Enable RLS on all tables
ALTER TABLE campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE ads ENABLE ROW LEVEL SECURITY;
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE keywords ENABLE ROW LEVEL SECURITY;
ALTER TABLE budget_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE optimization_history ENABLE ROW LEVEL SECURITY;

-- Basic policies (can be customized based on requirements)
-- These policies allow authenticated users to access their own data

-- Campaigns policies
CREATE POLICY "Users can view their campaigns" ON campaigns FOR SELECT USING (auth.uid() = created_by);
CREATE POLICY "Users can create campaigns" ON campaigns FOR INSERT WITH CHECK (auth.uid() = created_by);
CREATE POLICY "Users can update their campaigns" ON campaigns FOR UPDATE USING (auth.uid() = created_by);
CREATE POLICY "Users can delete their campaigns" ON campaigns FOR DELETE USING (auth.uid() = created_by);

-- Similar policies for other tables can be added based on business requirements

-- Service role policies (for backend operations)
CREATE POLICY "Service role full access campaigns" ON campaigns FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access ad_groups" ON ad_groups FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access ads" ON ads FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access agents" ON agents FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access agent_tasks" ON agent_tasks FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access performance_metrics" ON performance_metrics FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access compliance_logs" ON compliance_logs FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- ================================================================
-- COMMENTS FOR DOCUMENTATION
-- ================================================================

COMMENT ON TABLE campaigns IS 'Google Ads campaigns with AI optimization capabilities';
COMMENT ON TABLE ad_groups IS 'Ad groups within campaigns containing related ads and keywords';
COMMENT ON TABLE ads IS 'Individual advertisements with various formats and assets';
COMMENT ON TABLE agents IS 'AI agents responsible for different aspects of campaign management';
COMMENT ON TABLE agent_tasks IS 'Tasks assigned to AI agents with execution tracking';
COMMENT ON TABLE performance_metrics IS 'Time-series performance data for campaigns, ad groups, and ads';
COMMENT ON TABLE compliance_logs IS 'Audit trail for compliance checks and policy enforcement';
COMMENT ON TABLE keywords IS 'Keywords associated with ad groups for targeting';
COMMENT ON TABLE budget_history IS 'Historical record of budget changes and optimizations';
COMMENT ON TABLE optimization_history IS 'Record of AI-driven optimizations and their impact';

-- Column comments for key fields
COMMENT ON COLUMN campaigns.targeting IS 'JSON object containing detailed targeting criteria';
COMMENT ON COLUMN campaigns.auto_optimization_enabled IS 'Enable AI-driven automatic optimizations';
COMMENT ON COLUMN campaigns.data_retention_expires_at IS 'GDPR compliance: when to delete campaign data';
COMMENT ON COLUMN agents.config IS 'JSON configuration for agent behavior and capabilities';
COMMENT ON COLUMN agent_tasks.input_data IS 'JSON input parameters for task execution';
COMMENT ON COLUMN agent_tasks.output_data IS 'JSON results and outputs from task execution';
COMMENT ON COLUMN performance_metrics.date IS 'Date for which metrics are recorded (UTC)';
COMMENT ON COLUMN compliance_logs.details IS 'JSON object with detailed compliance check results';