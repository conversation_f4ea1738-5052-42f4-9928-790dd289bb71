-- AiLex Ad Agent System Database Indexes
-- Optimized indexes for production performance

-- ================================================================
-- CAMPAIGNS TABLE INDEXES
-- ================================================================

-- Primary lookup indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_status ON campaigns(status) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_type ON campaigns(type) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_created_by ON campaigns(created_by) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_google_ads_id ON campaigns(google_ads_id) WHERE google_ads_id IS NOT NULL;

-- Performance-critical indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_active_optimization ON campaigns(auto_optimization_enabled, last_optimized) 
    WHERE status = 'active' AND deleted_at IS NULL;

-- Time-based indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_created_at ON campaigns(created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_updated_at ON campaigns(updated_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_end_date ON campaigns(end_date) WHERE end_date IS NOT NULL;

-- GDPR compliance indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_data_retention ON campaigns(data_retention_expires_at) 
    WHERE data_retention_expires_at IS NOT NULL;

-- Full-text search on campaign names and descriptions
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_search ON campaigns 
    USING gin(to_tsvector('english', name || ' ' || COALESCE(description, ''))) 
    WHERE deleted_at IS NULL;

-- Targeting arrays indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_target_locations ON campaigns 
    USING gin(target_locations) WHERE array_length(target_locations, 1) > 0;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_target_languages ON campaigns 
    USING gin(target_languages) WHERE array_length(target_languages, 1) > 0;

-- ================================================================
-- AD_GROUPS TABLE INDEXES
-- ================================================================

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ad_groups_campaign_id ON ad_groups(campaign_id) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ad_groups_status ON ad_groups(status) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ad_groups_google_ads_id ON ad_groups(google_ads_id) WHERE google_ads_id IS NOT NULL;

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ad_groups_campaign_status ON ad_groups(campaign_id, status) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ad_groups_created_at ON ad_groups(created_at);

-- Keywords JSONB index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ad_groups_keywords ON ad_groups USING gin(keywords);

-- ================================================================
-- ADS TABLE INDEXES
-- ================================================================

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ads_ad_group_id ON ads(ad_group_id) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ads_status ON ads(status) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ads_type ON ads(type) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ads_google_ads_id ON ads(google_ads_id) WHERE google_ads_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ads_approval_status ON ads(approval_status) WHERE approval_status IS NOT NULL;

-- Composite indexes for filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ads_group_status ON ads(ad_group_id, status) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ads_type_status ON ads(type, status) WHERE deleted_at IS NULL;

-- Performance tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ads_created_at ON ads(created_at);

-- JSONB indexes for ad content
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ads_headlines ON ads USING gin(headlines);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ads_descriptions ON ads USING gin(descriptions);

-- ================================================================
-- AGENTS TABLE INDEXES
-- ================================================================

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agents_type ON agents(type) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agents_status ON agents(status) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agents_campaign_id ON agents(campaign_id) WHERE campaign_id IS NOT NULL AND deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agents_user_id ON agents(user_id) WHERE user_id IS NOT NULL;

-- Performance tracking indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agents_success_rate ON agents(success_rate) WHERE success_rate IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agents_last_activity ON agents(last_activity) WHERE last_activity IS NOT NULL;

-- Resource usage indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agents_resource_usage ON agents(memory_usage_mb, cpu_usage_percent) 
    WHERE memory_usage_mb IS NOT NULL OR cpu_usage_percent IS NOT NULL;

-- Active agents optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agents_active ON agents(status, last_activity) 
    WHERE status IN ('active', 'busy', 'idle');

-- Config JSONB index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agents_config ON agents USING gin(config);

-- ================================================================
-- AGENT_TASKS TABLE INDEXES
-- ================================================================

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_agent_id ON agent_tasks(agent_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_status ON agent_tasks(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_priority ON agent_tasks(priority);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_campaign_id ON agent_tasks(campaign_id) WHERE campaign_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_type ON agent_tasks(type);

-- Task scheduling and execution
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_scheduled ON agent_tasks(scheduled_at) WHERE scheduled_at IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_deadline ON agent_tasks(deadline) WHERE deadline IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_execution_time ON agent_tasks(execution_time_seconds) WHERE execution_time_seconds IS NOT NULL;

-- Task queue optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_queue ON agent_tasks(agent_id, status, priority, created_at) 
    WHERE status IN ('pending', 'queued');

-- Failed tasks analysis
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_failed ON agent_tasks(agent_id, status, retry_count) 
    WHERE status = 'failed';

-- Dependencies tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_parent ON agent_tasks(parent_task_id) WHERE parent_task_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_dependencies ON agent_tasks USING gin(dependent_task_ids) 
    WHERE array_length(dependent_task_ids, 1) > 0;

-- JSONB indexes for task data
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_input_data ON agent_tasks USING gin(input_data);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_output_data ON agent_tasks USING gin(output_data) WHERE output_data IS NOT NULL;

-- Time-based reporting
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_completed_at ON agent_tasks(completed_at) WHERE completed_at IS NOT NULL;

-- ================================================================
-- PERFORMANCE_METRICS TABLE INDEXES
-- ================================================================

-- Time-series optimization - most critical indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_campaign_date ON performance_metrics(campaign_id, date DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_date_campaign ON performance_metrics(date DESC, campaign_id);

-- Hierarchical queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_ad_group_date ON performance_metrics(ad_group_id, date DESC) 
    WHERE ad_group_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_ad_date ON performance_metrics(ad_id, date DESC) 
    WHERE ad_id IS NOT NULL;

-- Hourly granularity
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_hourly ON performance_metrics(campaign_id, date, hour) 
    WHERE hour IS NOT NULL;

-- Performance analysis indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_ctr ON performance_metrics(ctr) WHERE ctr IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_roas ON performance_metrics(roas) WHERE roas IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_cost ON performance_metrics(cost) WHERE cost > 0;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_conversions ON performance_metrics(conversions) WHERE conversions > 0;

-- Composite performance indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_cost_conversions ON performance_metrics(cost, conversions, date DESC) 
    WHERE cost > 0 AND conversions > 0;

-- Quality score analysis
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_quality_score ON performance_metrics(quality_score, campaign_id) 
    WHERE quality_score IS NOT NULL;

-- ================================================================
-- COMPLIANCE_LOGS TABLE INDEXES
-- ================================================================

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compliance_logs_entity ON compliance_logs(entity_type, entity_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compliance_logs_timestamp ON compliance_logs(timestamp DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compliance_logs_check_type ON compliance_logs(check_type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compliance_logs_result ON compliance_logs(result);

-- Unresolved issues tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compliance_logs_unresolved ON compliance_logs(resolved, severity, timestamp DESC) 
    WHERE NOT resolved;

-- GDPR compliance tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compliance_logs_gdpr ON compliance_logs(check_type, result, timestamp DESC) 
    WHERE check_type IN ('gdpr_consent', 'data_retention');

-- Entity-specific compliance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compliance_logs_entity_unresolved ON compliance_logs(entity_type, entity_id, resolved) 
    WHERE NOT resolved;

-- JSONB index for details
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_compliance_logs_details ON compliance_logs USING gin(details);

-- ================================================================
-- KEYWORDS TABLE INDEXES
-- ================================================================

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_keywords_ad_group_id ON keywords(ad_group_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_keywords_status ON keywords(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_keywords_match_type ON keywords(match_type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_keywords_negative ON keywords(negative);

-- Text search on keyword text
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_keywords_text ON keywords USING gin(text gin_trgm_ops);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_keywords_text_exact ON keywords(text);

-- Performance analysis
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_keywords_quality_score ON keywords(quality_score) WHERE quality_score IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_keywords_max_cpc ON keywords(max_cpc) WHERE max_cpc IS NOT NULL;

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_keywords_group_status ON keywords(ad_group_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_keywords_group_match_type ON keywords(ad_group_id, match_type);

-- ================================================================
-- BUDGET_HISTORY TABLE INDEXES
-- ================================================================

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_budget_history_campaign_id ON budget_history(campaign_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_budget_history_changed_at ON budget_history(changed_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_budget_history_agent_id ON budget_history(changed_by_agent_id) WHERE changed_by_agent_id IS NOT NULL;

-- Budget analysis
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_budget_history_campaign_date ON budget_history(campaign_id, changed_at DESC);

-- ================================================================
-- OPTIMIZATION_HISTORY TABLE INDEXES
-- ================================================================

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_optimization_history_campaign_id ON optimization_history(campaign_id) WHERE campaign_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_optimization_history_ad_group_id ON optimization_history(ad_group_id) WHERE ad_group_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_optimization_history_ad_id ON optimization_history(ad_id) WHERE ad_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_optimization_history_agent_id ON optimization_history(agent_id);

-- Optimization type and performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_optimization_history_type ON optimization_history(optimization_type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_optimization_history_applied_at ON optimization_history(applied_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_optimization_history_confidence ON optimization_history(confidence_score) WHERE confidence_score IS NOT NULL;

-- JSONB indexes for optimization data
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_optimization_history_changes ON optimization_history USING gin(changes_made);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_optimization_history_impact ON optimization_history USING gin(expected_impact);

-- ================================================================
-- MATERIALIZED VIEWS FOR PERFORMANCE
-- ================================================================

-- Daily campaign performance summary
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_campaign_performance AS
SELECT 
    campaign_id,
    date,
    SUM(impressions) as total_impressions,
    SUM(clicks) as total_clicks,
    SUM(conversions) as total_conversions,
    SUM(cost) as total_cost,
    SUM(revenue) as total_revenue,
    CASE 
        WHEN SUM(impressions) > 0 THEN SUM(clicks)::decimal / SUM(impressions) 
        ELSE 0 
    END as avg_ctr,
    CASE 
        WHEN SUM(clicks) > 0 THEN SUM(cost) / SUM(clicks) 
        ELSE 0 
    END as avg_cpc,
    CASE 
        WHEN SUM(cost) > 0 THEN SUM(revenue) / SUM(cost) 
        ELSE 0 
    END as avg_roas,
    MAX(created_at) as last_updated
FROM performance_metrics 
WHERE campaign_id IS NOT NULL
GROUP BY campaign_id, date;

-- Create index on materialized view
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_daily_campaign_performance_campaign_date 
    ON daily_campaign_performance(campaign_id, date DESC);

-- Agent performance summary
CREATE MATERIALIZED VIEW IF NOT EXISTS agent_performance_summary AS
SELECT 
    agent_id,
    DATE(created_at) as date,
    COUNT(*) as total_tasks,
    COUNT(*) FILTER (WHERE status = 'completed') as completed_tasks,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_tasks,
    AVG(execution_time_seconds) FILTER (WHERE execution_time_seconds IS NOT NULL) as avg_execution_time,
    CASE 
        WHEN COUNT(*) > 0 THEN COUNT(*) FILTER (WHERE status = 'completed')::decimal / COUNT(*) 
        ELSE 0 
    END as success_rate
FROM agent_tasks 
GROUP BY agent_id, DATE(created_at);

-- Create index on agent performance view
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_performance_summary_agent_date 
    ON agent_performance_summary(agent_id, date DESC);

-- ================================================================
-- PARTIAL INDEXES FOR SPECIFIC USE CASES
-- ================================================================

-- Active campaigns only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_active_only ON campaigns(name, created_at) 
    WHERE status = 'active' AND deleted_at IS NULL;

-- Running tasks only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_tasks_running ON agent_tasks(agent_id, started_at) 
    WHERE status = 'running';

-- Recent performance metrics (last 30 days)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_recent ON performance_metrics(campaign_id, date DESC, cost) 
    WHERE date >= CURRENT_DATE - INTERVAL '30 days';

-- High-spend campaigns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_high_spend ON campaigns(budget_amount, status) 
    WHERE budget_amount > 100 AND status = 'active';

-- ================================================================
-- STATISTICS UPDATE
-- ================================================================

-- Analyze all tables to update statistics for the query planner
ANALYZE campaigns;
ANALYZE ad_groups;
ANALYZE ads;
ANALYZE agents;
ANALYZE agent_tasks;
ANALYZE performance_metrics;
ANALYZE compliance_logs;
ANALYZE keywords;
ANALYZE budget_history;
ANALYZE optimization_history;