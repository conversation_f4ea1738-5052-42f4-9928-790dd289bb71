-- Migration: Add Google Ads OAuth2 credentials table
-- Description: Create table to store encrypted OAuth2 credentials for multi-account support
-- Version: 002
-- Date: 2025-08-06

-- Create google_ads_credentials table for OAuth2 token management
CREATE TABLE IF NOT EXISTS google_ads_credentials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id VARCHAR(50) NOT NULL UNIQUE,
    credentials_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    
    -- Indexes for performance
    CONSTRAINT google_ads_credentials_customer_id_key UNIQUE (customer_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_google_ads_credentials_customer_id 
ON google_ads_credentials (customer_id);

CREATE INDEX IF NOT EXISTS idx_google_ads_credentials_created_at 
ON google_ads_credentials (created_at);

-- Add comment
COMMENT ON TABLE google_ads_credentials IS 'Stores OAuth2 credentials for Google Ads API access';
COMMENT ON COLUMN google_ads_credentials.customer_id IS 'Google Ads customer account ID';
COMMENT ON COLUMN google_ads_credentials.credentials_data IS 'Encrypted OAuth2 token data (JSON)';

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_google_ads_credentials_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS trigger_update_google_ads_credentials_updated_at ON google_ads_credentials;
CREATE TRIGGER trigger_update_google_ads_credentials_updated_at
    BEFORE UPDATE ON google_ads_credentials
    FOR EACH ROW EXECUTE FUNCTION update_google_ads_credentials_updated_at();