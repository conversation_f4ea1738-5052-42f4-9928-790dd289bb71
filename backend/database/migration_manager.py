"""
Database migration management system.
Handles applying and tracking database schema changes.
"""

import asyncio
import os
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime

import structlog
from services.database import database_service
from utils.exceptions import DatabaseException


logger = structlog.get_logger(__name__)


class MigrationManager:
    """
    Manages database migrations for the AiLex Ad Agent System.
    """
    
    def __init__(self):
        self.migrations_dir = Path(__file__).parent / "migrations"
        self.migrations_table = "schema_migrations"
    
    async def initialize(self) -> None:
        """Initialize the migration system."""
        await self._ensure_migrations_table()
    
    async def _ensure_migrations_table(self) -> None:
        """Create the schema_migrations table if it doesn't exist."""
        create_migrations_table_sql = """
            CREATE TABLE IF NOT EXISTS schema_migrations (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                version VARCHAR(255) NOT NULL UNIQUE,
                description TEXT,
                applied_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                checksum VARCHAR(64) NOT NULL,
                execution_time_seconds DECIMAL(10,3),
                success BOOLEAN NOT NULL DEFAULT true,
                error_message TEXT,
                
                CONSTRAINT schema_migrations_version_format CHECK (
                    version ~ '^[0-9]{3}_[a-zA-Z0-9_]+$'
                )
            );
            
            CREATE INDEX IF NOT EXISTS idx_schema_migrations_version 
            ON schema_migrations(version);
            
            CREATE INDEX IF NOT EXISTS idx_schema_migrations_applied_at 
            ON schema_migrations(applied_at);
            
            COMMENT ON TABLE schema_migrations IS 'Tracks applied database migrations';
        """
        
        try:
            await database_service.execute_raw_command(create_migrations_table_sql)
            logger.info("Migration tracking table ensured")
        except Exception as e:
            logger.error("Failed to create migrations table", error=str(e))
            raise DatabaseException(f"Failed to initialize migration system: {str(e)}")
    
    def _get_migration_files(self) -> List[Path]:
        """Get all migration files sorted by version."""
        if not self.migrations_dir.exists():
            logger.warning("Migrations directory not found", path=str(self.migrations_dir))
            return []
        
        migration_files = []
        for file_path in self.migrations_dir.glob("*.sql"):
            if file_path.name.startswith((".","__")):
                continue
            migration_files.append(file_path)
        
        # Sort by version (filename)
        migration_files.sort(key=lambda p: p.name)
        return migration_files
    
    def _extract_version_from_filename(self, filename: str) -> str:
        """Extract migration version from filename."""
        return filename.replace(".sql", "")
    
    def _calculate_checksum(self, content: str) -> str:
        """Calculate SHA-256 checksum of migration content."""
        import hashlib
        return hashlib.sha256(content.encode()).hexdigest()
    
    async def _is_migration_applied(self, version: str) -> bool:
        """Check if a migration has already been applied."""
        query = """
            SELECT EXISTS (
                SELECT 1 FROM schema_migrations 
                WHERE version = $1 AND success = true
            );
        """
        
        try:
            result = await database_service.execute_raw_query(query, {"version": version})
            return result[0]["exists"] if result else False
        except Exception as e:
            logger.error("Failed to check migration status", version=version, error=str(e))
            return False
    
    async def _record_migration(
        self, 
        version: str, 
        description: str,
        checksum: str,
        execution_time: float,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> None:
        """Record migration execution in the database."""
        insert_sql = """
            INSERT INTO schema_migrations 
            (version, description, checksum, execution_time_seconds, success, error_message)
            VALUES ($1, $2, $3, $4, $5, $6);
        """
        
        try:
            await database_service.execute_raw_command(
                insert_sql,
                {
                    "version": version,
                    "description": description,
                    "checksum": checksum,
                    "execution_time_seconds": execution_time,
                    "success": success,
                    "error_message": error_message
                }
            )
        except Exception as e:
            logger.error("Failed to record migration", version=version, error=str(e))
            raise DatabaseException(f"Failed to record migration {version}: {str(e)}")
    
    async def _apply_migration(self, migration_file: Path) -> bool:
        """Apply a single migration file."""
        version = self._extract_version_from_filename(migration_file.name)
        
        try:
            # Read migration content
            content = migration_file.read_text(encoding='utf-8')
            checksum = self._calculate_checksum(content)
            
            # Extract description from first comment line
            lines = content.strip().split('\n')
            description = ""
            for line in lines:
                if line.strip().startswith('-- Description:'):
                    description = line.replace('-- Description:', '').strip()
                    break
            
            logger.info(
                "Applying migration",
                version=version,
                description=description,
                file=str(migration_file)
            )
            
            start_time = datetime.utcnow()
            
            # Execute migration
            await database_service.execute_raw_command(content)
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Record successful migration
            await self._record_migration(
                version=version,
                description=description,
                checksum=checksum,
                execution_time=execution_time,
                success=True
            )
            
            logger.info(
                "Migration applied successfully",
                version=version,
                execution_time=execution_time
            )
            
            return True
            
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds() if 'start_time' in locals() else 0
            error_message = str(e)
            
            logger.error(
                "Migration failed",
                version=version,
                error=error_message,
                execution_time=execution_time
            )
            
            # Record failed migration
            try:
                await self._record_migration(
                    version=version,
                    description=description if 'description' in locals() else "",
                    checksum=checksum if 'checksum' in locals() else "",
                    execution_time=execution_time,
                    success=False,
                    error_message=error_message
                )
            except Exception:
                pass  # Don't fail if we can't record the failure
            
            return False
    
    async def migrate(self, target_version: Optional[str] = None) -> Dict[str, any]:
        """
        Apply pending migrations up to target version.
        
        Args:
            target_version: Apply migrations up to this version (optional)
            
        Returns:
            Dict with migration results
        """
        await self.initialize()
        
        migration_files = self._get_migration_files()
        if not migration_files:
            logger.info("No migration files found")
            return {"applied": [], "skipped": [], "total": 0}
        
        applied = []
        skipped = []
        failed = []
        
        for migration_file in migration_files:
            version = self._extract_version_from_filename(migration_file.name)
            
            # Check if we've reached target version
            if target_version and version > target_version:
                break
            
            # Skip if already applied
            if await self._is_migration_applied(version):
                skipped.append(version)
                logger.info("Migration already applied, skipping", version=version)
                continue
            
            # Apply migration
            success = await self._apply_migration(migration_file)
            if success:
                applied.append(version)
            else:
                failed.append(version)
                break  # Stop on first failure
        
        result = {
            "applied": applied,
            "skipped": skipped,
            "failed": failed,
            "total": len(migration_files)
        }
        
        logger.info(
            "Migration run completed",
            applied_count=len(applied),
            skipped_count=len(skipped),
            failed_count=len(failed)
        )
        
        return result
    
    async def get_migration_status(self) -> Dict[str, any]:
        """Get current migration status."""
        await self.initialize()
        
        # Get all migration files
        migration_files = self._get_migration_files()
        file_versions = [self._extract_version_from_filename(f.name) for f in migration_files]
        
        # Get applied migrations
        query = """
            SELECT version, applied_at, success, error_message
            FROM schema_migrations
            ORDER BY version;
        """
        
        try:
            applied_migrations = await database_service.execute_raw_query(query)
        except Exception as e:
            logger.error("Failed to get migration status", error=str(e))
            applied_migrations = []
        
        applied_versions = [m["version"] for m in applied_migrations if m["success"]]
        failed_versions = [m["version"] for m in applied_migrations if not m["success"]]
        
        pending = [v for v in file_versions if v not in applied_versions]
        
        return {
            "total_migrations": len(file_versions),
            "applied": applied_versions,
            "pending": pending,
            "failed": failed_versions,
            "last_migration": applied_versions[-1] if applied_versions else None,
            "migrations": applied_migrations
        }
    
    async def rollback(self, target_version: str) -> bool:
        """
        Rollback to a specific migration version.
        Note: This is a destructive operation and should be used with caution.
        """
        logger.warning(
            "Rollback requested - this is a destructive operation",
            target_version=target_version
        )
        
        # For now, we don't implement automatic rollbacks as they can be complex
        # In a production system, you would need rollback scripts for each migration
        raise NotImplementedError(
            "Automatic rollbacks are not implemented. "
            "Please create manual rollback scripts for data safety."
        )


# Global migration manager instance
migration_manager = MigrationManager()