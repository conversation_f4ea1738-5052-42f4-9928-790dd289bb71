#!/usr/bin/env python3
"""
End-to-End Authentication Flow Test
Tests user signup, login, and session management.
"""

import asyncio
import os
import sys
import random
import string
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.auth import auth_service
from utils.config import settings


def generate_test_email():
    """Generate a unique test email."""
    random_string = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    return f"test_{random_string}@example.com"


async def test_signup_flow():
    """Test user signup flow."""
    print("\n📝 Testing User Signup Flow")
    print("=" * 40)
    
    test_email = generate_test_email()
    test_password = "TestPassword123!"
    
    print(f"Testing with email: {test_email}")
    
    try:
        # Attempt signup
        result = await auth_service.sign_up(
            email=test_email,
            password=test_password,
            user_metadata={
                "full_name": "Test User",
                "company": "Test Company",
                "created_via": "test_script"
            }
        )
        
        if result.get("success"):
            user = result.get("user")
            print(f"✅ Signup successful!")
            print(f"   User ID: {user.get('id')}")
            print(f"   Email: {user.get('email')}")
            print(f"   Created: {user.get('created_at')}")
            
            # Note about email confirmation
            if user.get('email_confirmed_at') is None:
                print("⚠️  Email not confirmed (expected in test environment)")
            
            return test_email, test_password, user
        else:
            error = result.get("error", "Unknown error")
            print(f"❌ Signup failed: {error}")
            return None, None, None
            
    except Exception as e:
        print(f"❌ Signup error: {str(e)}")
        return None, None, None


async def test_login_flow(email, password):
    """Test user login flow."""
    print("\n🔐 Testing User Login Flow")
    print("=" * 40)
    
    if not email or not password:
        print("⚠️  Skipping login test - no valid credentials")
        return None
    
    print(f"Attempting login with: {email}")
    
    try:
        # Attempt login
        result = await auth_service.sign_in(
            email=email,
            password=password
        )
        
        if result.get("success"):
            session = result.get("session")
            user = result.get("user")
            
            print(f"✅ Login successful!")
            print(f"   User ID: {user.get('id')}")
            print(f"   Session expires: {session.get('expires_at') if session else 'N/A'}")
            
            # Test session token
            if session and session.get('access_token'):
                print(f"   Access token: {session['access_token'][:20]}...")
                return session
            else:
                print("⚠️  No access token in session")
                return None
        else:
            error = result.get("error", "Unknown error")
            print(f"❌ Login failed: {error}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return None


async def test_session_verification(session):
    """Test session verification."""
    print("\n🔍 Testing Session Verification")
    print("=" * 40)
    
    if not session or not session.get('access_token'):
        print("⚠️  Skipping session test - no valid session")
        return False
    
    try:
        # Verify session
        result = await auth_service.verify_session(session['access_token'])
        
        if result.get("success"):
            user = result.get("user")
            print(f"✅ Session valid!")
            print(f"   User ID: {user.get('id')}")
            print(f"   Email: {user.get('email')}")
            return True
        else:
            error = result.get("error", "Unknown error")
            print(f"❌ Session verification failed: {error}")
            return False
            
    except Exception as e:
        print(f"❌ Session verification error: {str(e)}")
        return False


async def test_logout_flow(session):
    """Test user logout flow."""
    print("\n🚪 Testing User Logout Flow")
    print("=" * 40)
    
    if not session or not session.get('access_token'):
        print("⚠️  Skipping logout test - no valid session")
        return False
    
    try:
        # Attempt logout
        result = await auth_service.sign_out(session['access_token'])
        
        if result.get("success"):
            print(f"✅ Logout successful!")
            return True
        else:
            error = result.get("error", "Unknown error")
            print(f"❌ Logout failed: {error}")
            return False
            
    except Exception as e:
        print(f"❌ Logout error: {str(e)}")
        return False


async def test_password_reset_flow():
    """Test password reset flow."""
    print("\n🔑 Testing Password Reset Flow")
    print("=" * 40)
    
    test_email = "<EMAIL>"
    
    try:
        # Request password reset
        result = await auth_service.reset_password_request(test_email)
        
        if result.get("success"):
            print(f"✅ Password reset email sent to: {test_email}")
            print("   (Check email for reset link)")
            return True
        else:
            error = result.get("error", "Unknown error")
            print(f"⚠️  Password reset request: {error}")
            print("   (This is expected if email doesn't exist)")
            return False
            
    except Exception as e:
        print(f"❌ Password reset error: {str(e)}")
        return False


async def run_authentication_tests():
    """Run complete authentication flow tests."""
    print("🚀 Starting End-to-End Authentication Tests")
    print("=" * 60)
    print(f"Environment: {settings.ENVIRONMENT}")
    print(f"Supabase URL: {settings.database_url[:30]}...")
    print("=" * 60)
    
    # Initialize auth service
    try:
        await auth_service.authenticate()
        print("✅ Auth service initialized\n")
    except Exception as e:
        print(f"❌ Failed to initialize auth service: {e}")
        return
    
    # Track test results
    results = {
        "signup": False,
        "login": False,
        "session": False,
        "logout": False,
        "password_reset": False
    }
    
    # Test signup
    email, password, user = await test_signup_flow()
    results["signup"] = user is not None
    
    # Test login
    session = None
    if email and password:
        session = await test_login_flow(email, password)
        results["login"] = session is not None
    
    # Test session verification
    if session:
        results["session"] = await test_session_verification(session)
    
    # Test logout
    if session:
        results["logout"] = await test_logout_flow(session)
    
    # Test password reset
    results["password_reset"] = await test_password_reset_flow()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name.replace('_', ' ').title()}")
    
    passed_count = sum(1 for passed in results.values() if passed)
    total_count = len(results)
    
    print(f"\nTotal: {passed_count}/{total_count} tests passed")
    
    if passed_count == total_count:
        print("🎉 All authentication tests passed!")
    elif passed_count >= 3:
        print("✅ Core authentication features working!")
    else:
        print("⚠️  Some authentication features need attention")
    
    # Additional notes
    print("\n📝 Notes:")
    if not settings.RESEND_API_KEY:
        print("• Email service not configured (Resend API key missing)")
        print("  Set RESEND_API_KEY to enable email notifications")
    
    if settings.ENVIRONMENT == "development":
        print("• Running in development mode")
        print("  Email confirmation may be disabled")
    
    print("\n🔗 Next Steps:")
    print("1. Configure Resend API for email notifications")
    print("2. Test with frontend authentication UI")
    print("3. Implement role-based access control")
    print("4. Add OAuth providers (Google, GitHub)")


if __name__ == "__main__":
    asyncio.run(run_authentication_tests())