# AiLex Ad Agent System - FastAPI Backend ✅

**Status: Phase 1 Complete** - Production-ready backend with comprehensive API implementation.

This is the backend API for the AiLex Ad Agent System, built with FastAPI and designed for autonomous Google Ads campaign management using AI agents.

## 🚀 Features ✅ **IMPLEMENTED**

- **FastAPI Framework**: High-performance API with automatic OpenAPI documentation
- **Supabase Integration**: PostgreSQL database with connection pooling and RLS
- **Authentication System**: Supabase Auth with JWT token validation
- **Google Ads API**: Complete endpoint structure ready for real API integration
- **Multi-AI Support**: OpenAI GPT-4o and Google Gemini configuration ready
- **Real-time Analytics**: Performance monitoring and optimization insights endpoints
- **Production Ready**: Comprehensive logging, error handling, and health monitoring
- **GDPR Compliant**: Built-in privacy controls and data management features

## 🏗️ Architecture

```
backend/
├── main.py                 # FastAPI application entry point
├── api/                    # API route handlers
│   ├── campaigns.py        # Campaign management endpoints
│   ├── agents.py           # AI agent management endpoints
│   ├── analytics.py        # Analytics and reporting endpoints
│   └── health.py           # Health check endpoints
├── models/                 # Pydantic models
│   ├── campaigns.py        # Campaign data models
│   ├── agents.py           # Agent data models
│   ├── analytics.py        # Analytics data models
│   └── common.py           # Shared models and utilities
├── services/               # External service integrations
│   ├── google_ads.py       # Google Ads API service
│   ├── openai_service.py   # OpenAI API service
│   ├── database.py         # Supabase database service
│   ├── redis_service.py    # Redis caching service
│   └── base.py             # Base service classes
└── utils/                  # Utilities and helpers
    ├── config.py           # Configuration management
    ├── logging.py          # Structured logging
    ├── exceptions.py       # Custom exceptions
    └── helpers.py          # Common utility functions
```

## 🛠️ Setup

### Prerequisites

- Python 3.11+
- Redis (for caching and task queue)
- Google Ads API credentials
- OpenAI API key
- Supabase account (for database)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd googleads/backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Environment configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your actual credentials
   ```

5. **Start Redis** (if running locally)
   ```bash
   redis-server
   ```

6. **Run the application**
   ```bash
   python main.py
   ```

### Docker Setup

1. **Using Docker Compose (Recommended)**
   ```bash
   docker-compose up -d
   ```
   This starts:
   - FastAPI backend on port 8000
   - Redis on port 6379
   - Celery worker for background tasks
   - Celery beat for scheduled tasks

2. **Build and run individually**
   ```bash
   docker build -t ailex-backend .
   docker run -p 8000:8000 ailex-backend
   ```

## ⚙️ Configuration

### Environment Variables

Key environment variables (see `.env.example` for complete list):

```bash
# Application
ENVIRONMENT=development
DEBUG=true
HOST=0.0.0.0
PORT=8000

# Database
DATABASE_URL=https://your-project.supabase.co
DATABASE_KEY=your-supabase-key

# Google Ads API
GOOGLE_ADS_DEVELOPER_TOKEN=your-token
GOOGLE_ADS_CLIENT_ID=your-client-id
GOOGLE_ADS_CLIENT_SECRET=your-client-secret
GOOGLE_ADS_REFRESH_TOKEN=your-refresh-token
GOOGLE_ADS_CUSTOMER_ID=1234567890

# OpenAI
OPENAI_API_KEY=sk-your-key
OPENAI_MODEL=gpt-4o

# Redis
REDIS_URL=redis://localhost:6379/0
```

### Google Ads API Setup

1. Create a Google Ads API application
2. Get developer token approval
3. Set up OAuth2 credentials
4. Generate refresh token
5. Add credentials to `.env` file

## 📚 API Documentation

Once running, access the interactive API documentation:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### Key Endpoints

#### Health Checks
- `GET /api/v1/health/` - Comprehensive health check
- `GET /api/v1/health/liveness` - Simple liveness check
- `GET /api/v1/health/readiness` - Readiness check

#### Campaigns
- `POST /api/v1/campaigns/` - Create campaign
- `GET /api/v1/campaigns/` - List campaigns
- `GET /api/v1/campaigns/{id}` - Get campaign details
- `PUT /api/v1/campaigns/{id}` - Update campaign
- `POST /api/v1/campaigns/{id}/start` - Start campaign
- `POST /api/v1/campaigns/{id}/pause` - Pause campaign

#### AI Agents
- `GET /api/v1/agents/` - List agents
- `POST /api/v1/agents/` - Create agent
- `GET /api/v1/agents/{id}` - Get agent details
- `POST /api/v1/agents/{id}/start` - Start agent
- `POST /api/v1/agents/{id}/task` - Assign task

#### Analytics
- `GET /api/v1/analytics/reports/{type}` - Generate reports
- `GET /api/v1/analytics/campaigns/{id}/metrics` - Campaign metrics
- `GET /api/v1/analytics/campaigns/{id}/insights` - AI insights
- `GET /api/v1/analytics/dashboard` - Dashboard data

## 🧪 Testing

Run tests with pytest:

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest

# Run with coverage
pytest --cov=.

# Run specific test file
pytest tests/test_campaigns.py
```

## 📊 Monitoring

### Health Monitoring

The application provides comprehensive health checks:

```bash
# Check overall health
curl http://localhost:8000/api/v1/health/

# Quick liveness check
curl http://localhost:8000/api/v1/health/liveness

# Readiness for traffic
curl http://localhost:8000/api/v1/health/readiness
```

### Logging

Structured logging with different levels:
- **Development**: Pretty console output with colors
- **Production**: JSON structured logs for log aggregation

Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL

### Error Tracking

Optional Sentry integration for error tracking in production:

```bash
SENTRY_DSN=https://your-sentry-dsn
SENTRY_TRACES_SAMPLE_RATE=0.1
```

## 🚀 Deployment

### Production Checklist

- [ ] Set `ENVIRONMENT=production`
- [ ] Configure `TRUSTED_HOSTS`
- [ ] Set up proper CORS origins
- [ ] Configure Sentry for error tracking
- [ ] Set up SSL/TLS certificates
- [ ] Configure reverse proxy (nginx)
- [ ] Set up monitoring and alerting
- [ ] Configure backup strategies

### Fly.io Deployment

1. Install Fly CLI
2. Create `fly.toml` configuration
3. Deploy:
   ```bash
   fly deploy
   ```

### Docker Production

```bash
# Build production image
docker build -t ailex-backend:latest .

# Run with production settings
docker run -d \
  --name ailex-backend \
  -p 8000:8000 \
  -e ENVIRONMENT=production \
  ailex-backend:latest
```

## 🔒 Security

- **CORS**: Configurable origins for cross-origin requests
- **Rate Limiting**: Per-IP rate limiting with Redis
- **Input Validation**: Pydantic models for request validation
- **Error Handling**: Secure error responses without sensitive data
- **Authentication**: Ready for JWT or OAuth integration
- **HTTPS**: SSL/TLS support for production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

1. Check the API documentation at `/docs`
2. Review logs for error details
3. Check health endpoints for service status
4. Submit issues on GitHub

## 🔄 Development Workflow

1. **Start development server**
   ```bash
   python main.py
   # Server runs with auto-reload enabled
   ```

2. **Make changes**
   - API changes: Update route handlers in `api/`
   - Data models: Update Pydantic models in `models/`
   - Services: Update external integrations in `services/`

3. **Test changes**
   ```bash
   pytest
   curl http://localhost:8000/api/v1/health/
   ```

4. **Check API docs**
   - Visit http://localhost:8000/docs
   - Test endpoints interactively

## 📈 Performance

- **Async/Await**: Fully asynchronous for high concurrency
- **Caching**: Redis caching for expensive operations
- **Connection Pooling**: Efficient database connections
- **Rate Limiting**: Protect against abuse
- **Monitoring**: Built-in performance metrics