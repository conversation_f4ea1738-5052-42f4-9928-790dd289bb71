[bandit]
# Bandit configuration for Python security scanning

# Exclude paths
exclude_dirs = venv,node_modules,.git,__pycache__,tests,.pytest_cache,type-coverage-report

# Include specific tests
tests = B101,B102,B103,B104,B105,B106,B107,B108,B110,B112,B201,B301,B302,B303,B304,B305,B306,B307,B308,B310,B311,B312,B313,B314,B315,B316,B317,B318,B319,B320,B321,B322,B323,B324,B325,B401,B402,B403,B404,B405,B406,B407,B408,B409,B410,B411,B412,B413,B501,B502,B503,B504,B505,B506,B507,B601,B602,B603,B604,B605,B606,B607,B608,B609,B610,B611,B701,B702,B703

# Skip specific tests (with good reasons)
skips = 
    # B101: Test for use of assert (we use assert in tests)
    # B601: Test for shell injection (we handle this in our security middleware)

# Confidence levels: HIGH, MEDIUM, LOW
confidence = HIGH,MEDIUM,LOW

# Severity levels: HIGH, MEDIUM, LOW
level = LOW

# Report format
format = json

# Include line numbers
include_paths = .

# Custom patterns for sensitive data
[bandit.plugins]

# Additional security patterns
assert_used = B101
exec_used = B102
set_bad_file_permissions = B103
hardcoded_bind_all_interfaces = B104
hardcoded_password_string = B105
hardcoded_password_funcarg = B106
hardcoded_password_default = B107
hardcoded_tmp_directory = B108
password_config_option_not_marked_secret = B109
try_except_pass = B110
try_except_continue = B112
request_without_timeout = B113

# Injection attacks
flask_debug_true = B201
tarfile_unsafe_members = B202
os_system = B301
urllib_urlopen = B302
input_function = B303
pickle = B301,B302,B303,B304,B305,B306,B307,B308,B313,B401,B403
yaml_load = B301,B302,B303,B304,B305,B306,B307,B308,B506,B701

# SSL/TLS issues
ssl_with_bad_version = B301,B302,B303,B304,B305,B323,B324,B325,B501,B502,B503,B504,B505
ssl_with_bad_defaults = B301,B302,B303,B304,B305,B323,B324,B325,B501,B502,B503,B504,B505
ssl_with_no_version = B301,B302,B303,B304,B305,B323,B324,B325,B501,B502,B503,B504,B505

# Cryptographic issues
weak_cryptographic_key = B301,B302,B303,B304,B305,B323,B324,B325,B501,B502,B503,B504,B505,B506,B507

# Path traversal
path_traversal = B401,B402,B403