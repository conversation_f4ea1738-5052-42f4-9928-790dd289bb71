"""
Analytics and reporting API endpoints.
Handles campaign performance metrics, insights, and reporting.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, date
from enum import Enum

from fastapi import APIRouter, Depends, Query, Path, status
from fastapi.responses import JSONResponse

from models.analytics import (
    AnalyticsReport,
    AnalyticsReportResponse,
    MetricType,
    TimeRange,
    CampaignMetrics,
    PerformanceInsight,
    OptimizationSuggestion,
)
from services import database_service
from utils.logging import get_logger
from utils.exceptions import NotFoundException, ValidationException
from utils.helpers import utc_now


logger = get_logger(__name__)

router = APIRouter()


class ReportType(str, Enum):
    """Available report types."""
    CAMPAIGN_PERFORMANCE = "campaign_performance"
    KEYWORD_PERFORMANCE = "keyword_performance"
    AD_PERFORMANCE = "ad_performance"
    AUDIENCE_INSIGHTS = "audience_insights"
    COST_ANALYSIS = "cost_analysis"
    CONVERSION_TRACKING = "conversion_tracking"


@router.get("/reports/{report_type}", response_model=AnalyticsReportResponse)
async def generate_report(
    report_type: ReportType = Path(..., description="Type of report to generate"),
    campaign_id: Optional[str] = Query(None, description="Filter by specific campaign"),
    start_date: Optional[date] = Query(None, description="Report start date (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="Report end date (YYYY-MM-DD)"),
    time_range: Optional[TimeRange] = Query(None, description="Predefined time range"),
    metrics: Optional[List[MetricType]] = Query(None, description="Specific metrics to include"),
    granularity: Optional[str] = Query("daily", description="Data granularity (hourly, daily, weekly, monthly)"),
) -> AnalyticsReportResponse:
    """
    Generate analytics report based on specified parameters.
    
    Args:
        report_type: Type of report to generate
        campaign_id: Filter by specific campaign ID
        start_date: Report start date
        end_date: Report end date
        time_range: Predefined time range (overrides start/end dates)
        metrics: Specific metrics to include in the report
        granularity: Data granularity level
        
    Returns:
        AnalyticsReportResponse: Generated analytics report
        
    Raises:
        ValidationException: If report parameters are invalid
    """
    logger.info(
        "Generating analytics report",
        report_type=report_type,
        campaign_id=campaign_id,
        start_date=start_date,
        end_date=end_date,
        time_range=time_range,
    )
    
    try:
        # TODO: Implement report generation logic
        # 1. Validate report parameters
        # 2. Fetch data from Google Ads API
        # 3. Fetch data from Google Analytics
        # 4. Process and aggregate data
        # 5. Generate insights and recommendations
        # 6. Return formatted report
        
        # Placeholder report data
        report = AnalyticsReport(
            id="report-12345",
            type=report_type,
            title=f"{report_type.value.replace('_', ' ').title()} Report",
            description=f"Analytics report for {report_type.value}",
            generated_at=utc_now(),
            date_range={
                "start_date": start_date or date.today(),
                "end_date": end_date or date.today(),
            },
            metrics={
                "impressions": 10000,
                "clicks": 500,
                "conversions": 25,
                "cost": 250.00,
                "ctr": 0.05,
                "cpc": 0.50,
                "conversion_rate": 0.05,
                "cost_per_conversion": 10.00,
            },
            data=[],  # Placeholder for actual data points
        )
        
        logger.info("Analytics report generated successfully", report_id=report.id)
        
        return AnalyticsReportResponse(
            success=True,
            message="Analytics report generated successfully",
            data=report,
        )
        
    except Exception as e:
        logger.error("Failed to generate analytics report", error=str(e))
        raise ValidationException(f"Failed to generate analytics report: {str(e)}")


@router.get("/campaigns/{campaign_id}/metrics", response_model=Dict[str, Any])
async def get_campaign_metrics(
    campaign_id: str = Path(..., description="Campaign ID"),
    start_date: Optional[date] = Query(None, description="Metrics start date"),
    end_date: Optional[date] = Query(None, description="Metrics end date"),
    time_range: Optional[TimeRange] = Query(TimeRange.LAST_7_DAYS, description="Predefined time range"),
) -> Dict[str, Any]:
    """
    Get detailed metrics for a specific campaign.
    
    Args:
        campaign_id: Campaign identifier
        start_date: Metrics start date
        end_date: Metrics end date
        time_range: Predefined time range
        
    Returns:
        Dict[str, Any]: Campaign performance metrics
        
    Raises:
        NotFoundException: If campaign is not found
    """
    logger.info(
        "Getting campaign metrics",
        campaign_id=campaign_id,
        start_date=start_date,
        end_date=end_date,
        time_range=time_range,
    )
    
    try:
        # Validate campaign exists
        existing_campaign = await database_service.get_campaign(campaign_id)
        if not existing_campaign:
            raise NotFoundException("Campaign", campaign_id)
        
        # Convert time_range to dates if provided
        if time_range and time_range != TimeRange.CUSTOM:
            from datetime import timedelta
            today = date.today()
            
            if time_range == TimeRange.TODAY:
                start_date = end_date = today.isoformat()
            elif time_range == TimeRange.YESTERDAY:
                yesterday = today - timedelta(days=1)
                start_date = end_date = yesterday.isoformat()
            elif time_range == TimeRange.LAST_7_DAYS:
                start_date = (today - timedelta(days=7)).isoformat()
                end_date = today.isoformat()
            elif time_range == TimeRange.LAST_30_DAYS:
                start_date = (today - timedelta(days=30)).isoformat()
                end_date = today.isoformat()
            elif time_range == TimeRange.LAST_90_DAYS:
                start_date = (today - timedelta(days=90)).isoformat()
                end_date = today.isoformat()
            # Add more time_range conversions as needed
        
        # Get metrics from database
        metrics_list = await database_service.get_campaign_metrics(
            campaign_id=campaign_id,
            start_date=start_date,
            end_date=end_date,
        )
        
        # If no database metrics, try to get from database table 'campaign_metrics' directly
        if not metrics_list:
            # Try to get any available metrics for this campaign
            metrics_list = await database_service.get_campaign_metrics(
                campaign_id=campaign_id,
                start_date=None,
                end_date=None,
            )
        
        # Calculate aggregated performance metrics
        if metrics_list:
            total_performance = {
                "impressions": 0,
                "clicks": 0,
                "conversions": 0.0,
                "cost": 0.0,
                "revenue": 0.0,
            }
            
            # Aggregate metrics from all records
            for metric_record in metrics_list:
                metrics_data = metric_record.get("metrics", {})
                total_performance["impressions"] += metrics_data.get("impressions", 0)
                total_performance["clicks"] += metrics_data.get("clicks", 0)
                total_performance["conversions"] += metrics_data.get("conversions", 0.0)
                total_performance["cost"] += metrics_data.get("cost", 0.0)
                total_performance["revenue"] += metrics_data.get("revenue", 0.0)
        else:
            # No metrics found, return zeros
            total_performance = {
                "impressions": 0,
                "clicks": 0,
                "conversions": 0.0,
                "cost": 0.0,
                "revenue": 0.0,
            }
        
        # Calculate derived metrics
        derived_metrics = {}
        if total_performance["impressions"] > 0:
            derived_metrics["ctr"] = total_performance["clicks"] / total_performance["impressions"]
            derived_metrics["cpm"] = (total_performance["cost"] / total_performance["impressions"]) * 1000
        else:
            derived_metrics["ctr"] = 0.0
            derived_metrics["cpm"] = 0.0
            
        if total_performance["clicks"] > 0:
            derived_metrics["cpc"] = total_performance["cost"] / total_performance["clicks"]
            derived_metrics["conversion_rate"] = total_performance["conversions"] / total_performance["clicks"]
        else:
            derived_metrics["cpc"] = 0.0
            derived_metrics["conversion_rate"] = 0.0
            
        if total_performance["conversions"] > 0:
            derived_metrics["cost_per_conversion"] = total_performance["cost"] / total_performance["conversions"]
        else:
            derived_metrics["cost_per_conversion"] = 0.0
            
        if total_performance["cost"] > 0 and total_performance["revenue"] > 0:
            derived_metrics["roas"] = total_performance["revenue"] / total_performance["cost"]
            derived_metrics["roi"] = (total_performance["revenue"] - total_performance["cost"]) / total_performance["cost"]
        else:
            derived_metrics["roas"] = 0.0
            derived_metrics["roi"] = 0.0
        
        # TODO: Calculate trends by comparing with previous period
        trends = {
            "impressions_change": 0.0,
            "clicks_change": 0.0,
            "conversions_change": 0.0,
            "cost_change": 0.0,
        }
        
        # Create metrics object
        metrics = CampaignMetrics(
            campaign_id=campaign_id,
            date_range={
                "start_date": start_date or date.today(),
                "end_date": end_date or date.today(),
            },
            performance=total_performance,
            derived_metrics=derived_metrics,
            trends=trends,
        )
        
        logger.info("Campaign metrics retrieved successfully", campaign_id=campaign_id, metrics_records=len(metrics_list))
        
        return {
            "success": True,
            "message": "Campaign metrics retrieved successfully",
            "data": metrics.dict(),
        }
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to get campaign metrics", campaign_id=campaign_id, error=str(e))
        raise ValidationException(f"Failed to get campaign metrics: {str(e)}")


@router.get("/campaigns/{campaign_id}/insights", response_model=Dict[str, Any])
async def get_campaign_insights(
    campaign_id: str = Path(..., description="Campaign ID"),
    insight_types: Optional[List[str]] = Query(None, description="Types of insights to generate"),
) -> Dict[str, Any]:
    """
    Get AI-generated insights for a campaign.
    
    Args:
        campaign_id: Campaign identifier
        insight_types: Specific types of insights to generate
        
    Returns:
        Dict[str, Any]: Campaign performance insights
        
    Raises:
        NotFoundException: If campaign is not found
    """
    logger.info("Getting campaign insights", campaign_id=campaign_id, insight_types=insight_types)
    
    try:
        # TODO: Implement campaign insights generation
        # 1. Validate campaign exists
        # 2. Fetch performance data
        # 3. Run AI analysis for insights
        # 4. Generate actionable recommendations
        # 5. Return insights with confidence scores
        
        # Placeholder check
        if campaign_id == "nonexistent":
            raise NotFoundException("Campaign", campaign_id)
        
        # Placeholder insights
        insights = [
            PerformanceInsight(
                type="performance_anomaly",
                title="CTR Drop Detected",
                description="Click-through rate has decreased by 15% over the last 3 days",
                confidence=0.87,
                impact="medium",
                recommendation="Review ad copy relevance and consider A/B testing new headlines",
            ),
            PerformanceInsight(
                type="optimization_opportunity",
                title="Budget Reallocation Opportunity",
                description="High-performing keywords are being limited by budget constraints",
                confidence=0.92,
                impact="high",
                recommendation="Increase daily budget by 20% or redistribute from underperforming campaigns",
            ),
            PerformanceInsight(
                type="audience_behavior",
                title="Mobile Traffic Increase",
                description="Mobile traffic has increased 25% but conversion rate is 12% lower",
                confidence=0.78,
                impact="medium",
                recommendation="Optimize landing pages for mobile experience and adjust mobile bid modifiers",
            ),
        ]
        
        logger.info("Campaign insights generated successfully", campaign_id=campaign_id, insight_count=len(insights))
        
        return {
            "success": True,
            "message": "Campaign insights generated successfully",
            "data": {
                "campaign_id": campaign_id,
                "generated_at": utc_now().isoformat(),
                "insights": [insight.dict() for insight in insights],
            },
        }
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to get campaign insights", campaign_id=campaign_id, error=str(e))
        raise ValidationException(f"Failed to get campaign insights: {str(e)}")


@router.get("/campaigns/{campaign_id}/optimization-suggestions", response_model=Dict[str, Any])
async def get_optimization_suggestions(
    campaign_id: str = Path(..., description="Campaign ID"),
    priority: Optional[str] = Query(None, description="Filter by priority (high, medium, low)"),
    category: Optional[str] = Query(None, description="Filter by category (bidding, keywords, ads, targeting)"),
) -> Dict[str, Any]:
    """
    Get AI-generated optimization suggestions for a campaign.
    
    Args:
        campaign_id: Campaign identifier
        priority: Filter suggestions by priority level
        category: Filter suggestions by category
        
    Returns:
        Dict[str, Any]: Optimization suggestions
        
    Raises:
        NotFoundException: If campaign is not found
    """
    logger.info(
        "Getting optimization suggestions",
        campaign_id=campaign_id,
        priority=priority,
        category=category,
    )
    
    try:
        # TODO: Implement optimization suggestions generation
        # 1. Validate campaign exists
        # 2. Analyze current performance
        # 3. Generate AI-powered suggestions
        # 4. Prioritize suggestions by potential impact
        # 5. Return actionable recommendations
        
        # Placeholder check
        if campaign_id == "nonexistent":
            raise NotFoundException("Campaign", campaign_id)
        
        # Placeholder suggestions
        suggestions = [
            OptimizationSuggestion(
                id="opt-001",
                type="bid_adjustment",
                category="bidding",
                priority="high",
                title="Increase Bids for High-Converting Keywords",
                description="Keywords 'legal services' and 'lawyer consultation' have 25% higher conversion rates but limited by low bids",
                estimated_impact={"conversion_increase": 0.18, "cost_increase": 0.12},
                action_required="Increase bids by 15-20% for specified keywords",
                confidence=0.91,
            ),
            OptimizationSuggestion(
                id="opt-002",
                type="negative_keywords",
                category="keywords",
                priority="medium",
                title="Add Negative Keywords",
                description="Search terms 'free legal advice' and 'pro bono' are generating clicks but no conversions",
                estimated_impact={"cost_reduction": 0.08, "quality_improvement": 0.15},
                action_required="Add identified terms as negative keywords",
                confidence=0.84,
            ),
            OptimizationSuggestion(
                id="opt-003",
                type="ad_copy_optimization",
                category="ads",
                priority="medium",
                title="Update Ad Headlines",
                description="Current headlines have lower CTR compared to industry benchmarks",
                estimated_impact={"ctr_improvement": 0.22, "conversion_improvement": 0.12},
                action_required="Test new headlines focusing on unique value propositions",
                confidence=0.76,
            ),
        ]
        
        # Apply filters
        if priority:
            suggestions = [s for s in suggestions if s.priority == priority]
        if category:
            suggestions = [s for s in suggestions if s.category == category]
        
        logger.info(
            "Optimization suggestions generated successfully",
            campaign_id=campaign_id,
            suggestion_count=len(suggestions),
        )
        
        return {
            "success": True,
            "message": "Optimization suggestions generated successfully",
            "data": {
                "campaign_id": campaign_id,
                "generated_at": utc_now().isoformat(),
                "suggestions": [suggestion.dict() for suggestion in suggestions],
            },
        }
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to get optimization suggestions", campaign_id=campaign_id, error=str(e))
        raise ValidationException(f"Failed to get optimization suggestions: {str(e)}")


@router.get("/dashboard", response_model=Dict[str, Any])
async def get_dashboard_data(
    time_range: TimeRange = Query(TimeRange.LAST_7_DAYS, description="Time range for dashboard data"),
    campaign_ids: Optional[List[str]] = Query(None, description="Filter by specific campaigns"),
) -> Dict[str, Any]:
    """
    Get dashboard data with key metrics and insights.
    
    Args:
        time_range: Time range for dashboard data
        campaign_ids: Filter by specific campaign IDs
        
    Returns:
        Dict[str, Any]: Dashboard data with metrics and insights
    """
    logger.info("Getting dashboard data", time_range=time_range, campaign_count=len(campaign_ids or []))
    
    try:
        # Get campaigns (filtered or all)
        filters = {}
        if campaign_ids:
            # For now, we'll filter after retrieving due to database limitations
            pass
            
        campaigns_data = await database_service.list_campaigns(
            filters=filters,
            limit=1000,  # Get all campaigns for dashboard
            offset=0,
            order_by="created_at",
            order_direction="desc",
        )
        
        # Filter by campaign_ids if provided
        if campaign_ids:
            campaigns_data = [c for c in campaigns_data if c.get("id") in campaign_ids]
        
        # Calculate summary metrics
        total_campaigns = len(campaigns_data)
        active_campaigns = len([c for c in campaigns_data if c.get("status") == "active"])
        
        # Get metrics for all campaigns
        total_spend = 0.0
        total_conversions = 0.0
        total_revenue = 0.0
        top_campaigns = []
        
        for campaign_data in campaigns_data:
            campaign_id = campaign_data.get("id")
            
            # Get recent metrics for each campaign
            try:
                metrics_list = await database_service.get_campaign_metrics(
                    campaign_id=campaign_id,
                    start_date=None,  # Get all available metrics
                    end_date=None,
                )
                
                # Aggregate metrics for this campaign
                campaign_spend = 0.0
                campaign_conversions = 0.0
                campaign_revenue = 0.0
                
                for metric_record in metrics_list:
                    metrics_data = metric_record.get("metrics", {})
                    campaign_spend += metrics_data.get("cost", 0.0)
                    campaign_conversions += metrics_data.get("conversions", 0.0)
                    campaign_revenue += metrics_data.get("revenue", 0.0)
                
                total_spend += campaign_spend
                total_conversions += campaign_conversions
                total_revenue += campaign_revenue
                
                # Calculate ROI for this campaign
                campaign_roi = 0.0
                if campaign_spend > 0 and campaign_revenue > 0:
                    campaign_roi = (campaign_revenue - campaign_spend) / campaign_spend
                
                # Add to top campaigns if it has performance data
                if campaign_spend > 0 or campaign_conversions > 0:
                    top_campaigns.append({
                        "id": campaign_id,
                        "name": campaign_data.get("name", "Unknown"),
                        "spend": round(campaign_spend, 2),
                        "conversions": int(campaign_conversions),
                        "roi": round(campaign_roi, 2),
                    })
                    
            except Exception as e:
                logger.warning("Failed to get metrics for campaign", campaign_id=campaign_id, error=str(e))
        
        # Sort top campaigns by ROI and take top 5
        top_campaigns.sort(key=lambda x: x["roi"], reverse=True)
        top_campaigns = top_campaigns[:5]
        
        # Calculate overall metrics
        average_cac = total_spend / total_conversions if total_conversions > 0 else 0.0
        overall_roi = (total_revenue - total_spend) / total_spend if total_spend > 0 else 0.0
        
        # Generate placeholder trends (in a real implementation, this would use historical data)
        trends = {
            "spend_trend": [round(total_spend * 0.8, 1), round(total_spend * 0.9, 1), round(total_spend, 1)],
            "conversion_trend": [int(total_conversions * 0.7), int(total_conversions * 0.85), int(total_conversions)],
            "roi_trend": [round(overall_roi * 0.9, 2), round(overall_roi * 0.95, 2), round(overall_roi, 2)],
        }
        
        # Generate basic alerts
        alerts = []
        
        # Check for campaigns with low performance
        for campaign in top_campaigns:
            if campaign["roi"] < 0.5:
                alerts.append({
                    "type": "performance_alert",
                    "message": f"Campaign '{campaign['name']}' has low ROI ({campaign['roi']:.2f})",
                    "severity": "medium",
                })
        
        # Check for high spending campaigns
        avg_spend_per_campaign = total_spend / total_campaigns if total_campaigns > 0 else 0
        for campaign in top_campaigns:
            if campaign["spend"] > avg_spend_per_campaign * 2:
                alerts.append({
                    "type": "budget_alert",
                    "message": f"Campaign '{campaign['name']}' has high spend (${campaign['spend']:.2f})",
                    "severity": "info",
                })
        
        dashboard_data = {
            "summary": {
                "total_campaigns": total_campaigns,
                "active_campaigns": active_campaigns,
                "total_spend": round(total_spend, 2),
                "total_conversions": int(total_conversions),
                "average_cac": round(average_cac, 2),
                "total_revenue": round(total_revenue, 2),
                "roi": round(overall_roi, 2),
            },
            "trends": trends,
            "top_campaigns": top_campaigns,
            "alerts": alerts[:5],  # Limit to 5 most important alerts
            "generated_at": utc_now().isoformat(),
        }
        
        logger.info(
            "Dashboard data retrieved successfully", 
            total_campaigns=total_campaigns,
            active_campaigns=active_campaigns,
            total_spend=total_spend
        )
        
        return {
            "success": True,
            "message": "Dashboard data retrieved successfully",
            "data": dashboard_data,
        }
        
    except Exception as e:
        logger.error("Failed to get dashboard data", error=str(e))
        raise ValidationException(f"Failed to get dashboard data: {str(e)}")


@router.post("/reports/{report_id}/export")
async def export_report(
    report_id: str = Path(..., description="Report ID to export"),
    format: str = Query("pdf", description="Export format (pdf, csv, xlsx)"),
) -> JSONResponse:
    """
    Export a generated report in the specified format.
    
    Args:
        report_id: Report identifier
        format: Export format (pdf, csv, xlsx)
        
    Returns:
        JSONResponse: Export status and download URL
        
    Raises:
        NotFoundException: If report is not found
    """
    logger.info("Exporting report", report_id=report_id, format=format)
    
    try:
        # TODO: Implement report export logic
        # 1. Validate report exists
        # 2. Generate report in requested format
        # 3. Store in temporary location
        # 4. Return download URL
        
        # Placeholder check
        if report_id == "nonexistent":
            raise NotFoundException("Report", report_id)
        
        export_url = f"/api/v1/analytics/downloads/{report_id}.{format}"
        
        logger.info("Report export initiated", report_id=report_id, export_url=export_url)
        
        return JSONResponse(
            status_code=status.HTTP_202_ACCEPTED,
            content={
                "success": True,
                "message": "Report export initiated",
                "data": {
                    "report_id": report_id,
                    "format": format,
                    "export_url": export_url,
                    "expires_at": (utc_now().replace(hour=23, minute=59, second=59)).isoformat(),
                },
            },
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to export report", report_id=report_id, error=str(e))
        raise ValidationException(f"Failed to export report: {str(e)}")