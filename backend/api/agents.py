"""
AI Agent management API endpoints.
Handles CrewAI agent lifecycle, monitoring, and control.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, Depends, Query, Path, Body, status, HTTPException
from fastapi.responses import JSONResponse

from models.agents import (
    Agent,
    AgentCreate,
    AgentUpdate,
    AgentResponse,
    AgentListResponse,
    AgentStatus,
    AgentType,
    AgentTask,
    AgentTaskResponse,
    AgentMetrics,
    TaskCreate,
    AgentCommand
)
from services.database import database_service
from utils.logging import get_logger
from utils.exceptions import NotFoundException, ValidationException, AgentException
from agents.orchestrator import campaign_orchestrator, CampaignRequest


logger = get_logger(__name__)

router = APIRouter()


@router.get("/", response_model=AgentListResponse)
async def list_agents(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    status: Optional[AgentStatus] = Query(None, description="Filter by agent status"),
    agent_type: Optional[AgentType] = Query(None, description="Filter by agent type"),
    campaign_id: Optional[str] = Query(None, description="Filter by campaign ID"),
) -> AgentListResponse:
    """
    List AI agents with optional filtering and pagination.
    
    Args:
        skip: Number of records to skip for pagination
        limit: Maximum number of records to return
        status: Filter by agent status
        agent_type: Filter by agent type
        campaign_id: Filter by campaign ID
        
    Returns:
        AgentListResponse: List of agents with pagination info
    """
    logger.info(
        "Listing agents",
        skip=skip,
        limit=limit,
        status=status,
        agent_type=agent_type,
        campaign_id=campaign_id,
    )
    
    try:
        # Build filters
        filters = {}
        if status:
            filters["status"] = status.value
        if agent_type:
            filters["type"] = agent_type.value
        if campaign_id:
            filters["campaign_id"] = campaign_id
        
        # Get agents from database
        agent_data_list = await database_service.list_agents(
            filters=filters,
            limit=limit,
            offset=skip,
            order_by="created_at",
            order_direction="desc",
        )
        
        # Get total count for pagination
        total = await database_service.get_count("agents", filters)
        
        # Convert to Agent models
        agents = []
        for agent_data in agent_data_list:
            try:
                agent = Agent(
                    id=agent_data["id"],
                    name=agent_data["name"],
                    description=agent_data["description"],
                    type=AgentType(agent_data["type"]),
                    status=AgentStatus(agent_data.get("status", "created")),
                    config=agent_data.get("config", {}),
                    campaign_id=agent_data.get("campaign_id"),
                    user_id=agent_data.get("user_id"),
                    tasks_completed=agent_data.get("tasks_completed", 0),
                    tasks_failed=agent_data.get("tasks_failed", 0),
                    success_rate=agent_data.get("success_rate"),
                    last_activity=agent_data.get("last_activity"),
                    created_at=agent_data.get("created_at"),
                    updated_at=agent_data.get("updated_at"),
                )
                agents.append(agent)
            except Exception as e:
                logger.warning(
                    "Skipping invalid agent data",
                    agent_id=agent_data.get("id"),
                    error=str(e),
                )
        
        return AgentListResponse(
            success=True,
            message="Agents retrieved successfully",
            data=agents,
            pagination={
                "skip": skip,
                "limit": limit,
                "total": total,
                "has_more": skip + limit < total,
            },
        )
        
    except Exception as e:
        logger.error("Failed to list agents", error=str(e))
        raise ValidationException(f"Failed to list agents: {str(e)}")


@router.get("/{agent_id}", response_model=AgentResponse)
async def get_agent(
    agent_id: str = Path(..., description="Agent ID"),
) -> AgentResponse:
    """
    Get agent details by ID.
    
    Args:
        agent_id: Agent identifier
        
    Returns:
        AgentResponse: Agent details
        
    Raises:
        NotFoundException: If agent is not found
    """
    logger.info("Getting agent details", agent_id=agent_id)
    
    try:
        # Get agent from database
        agent_data = await database_service.get_agent(agent_id)
        
        if not agent_data:
            raise NotFoundException("Agent", agent_id)
        
        # Convert to Agent model
        agent = Agent(
            id=agent_data["id"],
            name=agent_data["name"],
            description=agent_data["description"],
            type=AgentType(agent_data["type"]),
            status=AgentStatus(agent_data.get("status", "created")),
            config=agent_data.get("config", {}),
            campaign_id=agent_data.get("campaign_id"),
            user_id=agent_data.get("user_id"),
            tasks_completed=agent_data.get("tasks_completed", 0),
            tasks_failed=agent_data.get("tasks_failed", 0),
            success_rate=agent_data.get("success_rate"),
            last_activity=agent_data.get("last_activity"),
            last_error=agent_data.get("last_error"),
            memory_usage_mb=agent_data.get("memory_usage_mb"),
            cpu_usage_percent=agent_data.get("cpu_usage_percent"),
            uptime_hours=agent_data.get("uptime_hours"),
            version=agent_data.get("version", "1.0.0"),
            created_at=agent_data.get("created_at"),
            updated_at=agent_data.get("updated_at"),
        )
        
        return AgentResponse(
            success=True,
            message="Agent retrieved successfully",
            data=agent,
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to get agent", agent_id=agent_id, error=str(e))
        raise AgentException(f"Failed to get agent: {str(e)}", agent_name=agent_id)


@router.post("/", response_model=AgentResponse, status_code=status.HTTP_201_CREATED)
async def create_agent(
    agent_data: AgentCreate,
) -> AgentResponse:
    """
    Create a new AI agent.
    
    Args:
        agent_data: Agent creation data
        
    Returns:
        AgentResponse: Created agent details
        
    Raises:
        ValidationException: If agent data is invalid
    """
    logger.info("Creating new agent", agent_name=agent_data.name, agent_type=agent_data.type)
    
    try:
        # Prepare agent data for database
        db_agent_data = {
            "name": agent_data.name,
            "description": agent_data.description,
            "type": agent_data.type.value,
            "status": AgentStatus.CREATED.value,
            "config": agent_data.config.dict(),
            "campaign_id": agent_data.campaign_id,
            "tasks_completed": 0,
            "tasks_failed": 0,
            "success_rate": None,
            "version": "1.0.0",
        }
        
        # Create agent in database
        agent_id = await database_service.create_agent(db_agent_data)
        
        # Retrieve created agent data
        created_agent_data = await database_service.get_agent(agent_id)
        
        # Convert to Agent model
        agent = Agent(
            id=created_agent_data["id"],
            name=created_agent_data["name"],
            description=created_agent_data["description"],
            type=AgentType(created_agent_data["type"]),
            status=AgentStatus(created_agent_data["status"]),
            config=created_agent_data["config"],
            campaign_id=created_agent_data.get("campaign_id"),
            tasks_completed=created_agent_data.get("tasks_completed", 0),
            tasks_failed=created_agent_data.get("tasks_failed", 0),
            success_rate=created_agent_data.get("success_rate"),
            version=created_agent_data.get("version", "1.0.0"),
            created_at=created_agent_data.get("created_at"),
            updated_at=created_agent_data.get("updated_at"),
        )
        
        logger.info("Agent created successfully", agent_id=agent.id)
        
        return AgentResponse(
            success=True,
            message="Agent created successfully",
            data=agent,
        )
        
    except Exception as e:
        logger.error("Failed to create agent", error=str(e))
        raise AgentException(f"Failed to create agent: {str(e)}", agent_name=agent_data.name)


@router.put("/{agent_id}/status", response_model=AgentResponse)
async def update_agent_status(
    agent_id: str = Path(..., description="Agent ID"),
    status: AgentStatus = Body(..., description="New agent status"),
) -> AgentResponse:
    """
    Update agent status.
    
    Args:
        agent_id: Agent identifier
        status: New agent status
        
    Returns:
        AgentResponse: Updated agent details
        
    Raises:
        NotFoundException: If agent is not found
        ValidationException: If status update is invalid
    """
    logger.info("Updating agent status", agent_id=agent_id, new_status=status)
    
    try:
        # Validate agent exists
        existing_agent = await database_service.get_agent(agent_id)
        if not existing_agent:
            raise NotFoundException("Agent", agent_id)
        
        # Update agent status in database
        update_data = {
            "status": status.value,
            "last_activity": datetime.utcnow().isoformat(),
        }
        
        await database_service.update_agent(agent_id, update_data)
        
        # Retrieve updated agent
        updated_agent_data = await database_service.get_agent(agent_id)
        
        # Convert to Agent model
        agent = Agent(
            id=updated_agent_data["id"],
            name=updated_agent_data["name"],
            description=updated_agent_data["description"],
            type=AgentType(updated_agent_data["type"]),
            status=AgentStatus(updated_agent_data["status"]),
            config=updated_agent_data["config"],
            campaign_id=updated_agent_data.get("campaign_id"),
            tasks_completed=updated_agent_data.get("tasks_completed", 0),
            tasks_failed=updated_agent_data.get("tasks_failed", 0),
            success_rate=updated_agent_data.get("success_rate"),
            last_activity=updated_agent_data.get("last_activity"),
            created_at=updated_agent_data.get("created_at"),
            updated_at=updated_agent_data.get("updated_at"),
        )
        
        logger.info("Agent status updated successfully", agent_id=agent_id, new_status=status)
        
        return AgentResponse(
            success=True,
            message="Agent status updated successfully",
            data=agent,
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to update agent status", agent_id=agent_id, error=str(e))
        raise AgentException(f"Failed to update agent status: {str(e)}", agent_name=agent_id)


@router.put("/{agent_id}", response_model=AgentResponse)
async def update_agent(
    agent_id: str = Path(..., description="Agent ID"),
    agent_data: AgentUpdate = Body(...),
) -> AgentResponse:
    """
    Update agent configuration.
    
    Args:
        agent_id: Agent identifier
        agent_data: Agent update data
        
    Returns:
        AgentResponse: Updated agent details
        
    Raises:
        NotFoundException: If agent is not found
        ValidationException: If update data is invalid
    """
    logger.info("Updating agent", agent_id=agent_id)
    
    try:
        # Validate agent exists
        existing_agent = await database_service.get_agent(agent_id)
        if not existing_agent:
            raise NotFoundException("Agent", agent_id)
        
        # Prepare update data
        update_data = {}
        
        if agent_data.name is not None:
            update_data["name"] = agent_data.name
        if agent_data.description is not None:
            update_data["description"] = agent_data.description
        if agent_data.status is not None:
            update_data["status"] = agent_data.status.value
            update_data["last_activity"] = datetime.utcnow().isoformat()
        if agent_data.campaign_id is not None:
            update_data["campaign_id"] = agent_data.campaign_id
        if agent_data.config is not None:
            update_data["config"] = agent_data.config.dict()
        
        # Update agent in database
        await database_service.update_agent(agent_id, update_data)
        
        # Retrieve updated agent
        updated_agent_data = await database_service.get_agent(agent_id)
        
        # Convert to Agent model
        agent = Agent(
            id=updated_agent_data["id"],
            name=updated_agent_data["name"],
            description=updated_agent_data["description"],
            type=AgentType(updated_agent_data["type"]),
            status=AgentStatus(updated_agent_data["status"]),
            config=updated_agent_data["config"],
            campaign_id=updated_agent_data.get("campaign_id"),
            tasks_completed=updated_agent_data.get("tasks_completed", 0),
            tasks_failed=updated_agent_data.get("tasks_failed", 0),
            success_rate=updated_agent_data.get("success_rate"),
            last_activity=updated_agent_data.get("last_activity"),
            created_at=updated_agent_data.get("created_at"),
            updated_at=updated_agent_data.get("updated_at"),
        )
        
        logger.info("Agent updated successfully", agent_id=agent_id)
        
        return AgentResponse(
            success=True,
            message="Agent updated successfully",
            data=agent,
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to update agent", agent_id=agent_id, error=str(e))
        raise AgentException(f"Failed to update agent: {str(e)}", agent_name=agent_id)


@router.delete("/{agent_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_agent(
    agent_id: str = Path(..., description="Agent ID"),
) -> None:
    """
    Delete an agent.
    
    Args:
        agent_id: Agent identifier
        
    Raises:
        NotFoundException: If agent is not found
    """
    logger.info("Deleting agent", agent_id=agent_id)
    
    try:
        # Validate agent exists
        existing_agent = await database_service.get_agent(agent_id)
        if not existing_agent:
            raise NotFoundException("Agent", agent_id)
        
        # Soft delete agent (update status to deleted)
        await database_service.delete_agent(agent_id)
        
        logger.info("Agent deleted successfully", agent_id=agent_id)
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to delete agent", agent_id=agent_id, error=str(e))
        raise AgentException(f"Failed to delete agent: {str(e)}", agent_name=agent_id)




@router.get("/{agent_id}/tasks", response_model=AgentTaskResponse)
async def get_agent_tasks(
    agent_id: str = Path(..., description="Agent ID"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(50, ge=1, le=500, description="Maximum number of records to return"),
) -> AgentTaskResponse:
    """
    Get agent task history.
    
    Args:
        agent_id: Agent identifier
        skip: Number of records to skip for pagination
        limit: Maximum number of records to return
        
    Returns:
        AgentTaskResponse: List of agent tasks
        
    Raises:
        NotFoundException: If agent is not found
    """
    logger.info("Getting agent tasks", agent_id=agent_id, skip=skip, limit=limit)
    
    try:
        # Validate agent exists
        existing_agent = await database_service.get_agent(agent_id)
        if not existing_agent:
            raise NotFoundException("Agent", agent_id)
        
        # Get agent tasks from database
        task_data_list = await database_service.list_agent_tasks(
            agent_id=agent_id,
            limit=limit,
            offset=skip,
            order_by="created_at",
            order_direction="desc",
        )
        
        # Get total count for pagination
        total = await database_service.get_count("agent_tasks", {"agent_id": agent_id})
        
        # Convert to AgentTask models
        tasks = []
        for task_data in task_data_list:
            try:
                from models.agents import TaskStatus, TaskPriority
                task = AgentTask(
                    id=task_data["id"],
                    agent_id=task_data["agent_id"],
                    campaign_id=task_data.get("campaign_id"),
                    name=task_data["name"],
                    description=task_data["description"],
                    type=task_data["type"],
                    priority=TaskPriority(task_data.get("priority", "normal")),
                    status=TaskStatus(task_data.get("status", "pending")),
                    input_data=task_data.get("input_data", {}),
                    output_data=task_data.get("output_data"),
                    context=task_data.get("context", {}),
                    started_at=task_data.get("started_at"),
                    completed_at=task_data.get("completed_at"),
                    execution_time_seconds=task_data.get("execution_time_seconds"),
                    retry_count=task_data.get("retry_count", 0),
                    result=task_data.get("result"),
                    error_message=task_data.get("error_message"),
                    logs=task_data.get("logs", []),
                    created_at=task_data.get("created_at"),
                    updated_at=task_data.get("updated_at"),
                )
                tasks.append(task)
            except Exception as e:
                logger.warning(
                    "Skipping invalid task data",
                    task_id=task_data.get("id"),
                    error=str(e),
                )
        
        return AgentTaskResponse(
            success=True,
            message="Agent tasks retrieved successfully",
            data=tasks,
            pagination={
                "skip": skip,
                "limit": limit,
                "total": total,
                "has_more": skip + limit < total,
            },
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to get agent tasks", agent_id=agent_id, error=str(e))
        raise AgentException(f"Failed to get agent tasks: {str(e)}", agent_name=agent_id)


@router.get("/{agent_id}/metrics", response_model=Dict[str, Any])
async def get_agent_metrics(
    agent_id: str = Path(..., description="Agent ID"),
) -> Dict[str, Any]:
    """
    Get agent performance metrics.
    
    Args:
        agent_id: Agent identifier
        
    Returns:
        Dict[str, Any]: Agent performance metrics
        
    Raises:
        NotFoundException: If agent is not found
    """
    logger.info("Getting agent metrics", agent_id=agent_id)
    
    try:
        # Validate agent exists
        existing_agent = await database_service.get_agent(agent_id)
        if not existing_agent:
            raise NotFoundException("Agent", agent_id)
        
        # Get agent task statistics
        from models.agents import TaskStatus
        completed_tasks = await database_service.get_count(
            "agent_tasks", 
            {"agent_id": agent_id, "status": TaskStatus.COMPLETED.value}
        )
        failed_tasks = await database_service.get_count(
            "agent_tasks", 
            {"agent_id": agent_id, "status": TaskStatus.FAILED.value}
        )
        total_tasks = await database_service.get_count(
            "agent_tasks", 
            {"agent_id": agent_id}
        )
        
        # Calculate success rate
        success_rate = completed_tasks / total_tasks if total_tasks > 0 else 0.0
        
        # Calculate average execution time from completed tasks
        completed_task_data = await database_service.list_agent_tasks(
            agent_id=agent_id,
            filters={"status": TaskStatus.COMPLETED.value},
            limit=100,  # Last 100 completed tasks
        )
        
        execution_times = [
            task.get("execution_time_seconds", 0) 
            for task in completed_task_data 
            if task.get("execution_time_seconds") is not None
        ]
        average_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0.0
        
        # Compile metrics
        metrics = {
            "agent_id": agent_id,
            "agent_name": existing_agent.get("name"),
            "agent_type": existing_agent.get("type"),
            "agent_status": existing_agent.get("status"),
            "tasks_completed": completed_tasks,
            "tasks_failed": failed_tasks,
            "total_tasks": total_tasks,
            "success_rate": round(success_rate, 3),
            "average_execution_time_seconds": round(average_execution_time, 2),
            "memory_usage_mb": existing_agent.get("memory_usage_mb"),
            "cpu_usage_percent": existing_agent.get("cpu_usage_percent"),
            "uptime_hours": existing_agent.get("uptime_hours"),
            "last_activity": existing_agent.get("last_activity"),
            "created_at": existing_agent.get("created_at"),
        }
        
        logger.info("Agent metrics retrieved successfully", agent_id=agent_id, tasks_completed=completed_tasks)
        
        return {
            "success": True,
            "message": "Agent metrics retrieved successfully",
            "data": metrics,
        }
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to get agent metrics", agent_id=agent_id, error=str(e))
        raise AgentException(f"Failed to get agent metrics: {str(e)}", agent_name=agent_id)


@router.post("/{agent_id}/tasks", status_code=status.HTTP_201_CREATED)
async def assign_task(
    agent_id: str = Path(..., description="Agent ID"),
    task_data: TaskCreate = Body(...),
) -> JSONResponse:
    """
    Assign a task to an agent.
    
    Args:
        agent_id: Agent identifier
        task_data: Task details and parameters
        
    Returns:
        JSONResponse: Task assignment confirmation
        
    Raises:
        NotFoundException: If agent is not found
    """
    logger.info("Assigning task to agent", agent_id=agent_id, task_type=task_data.type)
    
    try:
        # Validate agent exists and is active
        existing_agent = await database_service.get_agent(agent_id)
        if not existing_agent:
            raise NotFoundException("Agent", agent_id)
        
        agent_status = existing_agent.get("status", "created")
        if agent_status not in ["active", "idle"]:
            raise ValidationException(f"Agent must be active or idle to accept tasks. Current status: {agent_status}")
        
        # Prepare task data for database
        from models.agents import TaskStatus, TaskPriority
        db_task_data = {
            "agent_id": agent_id,
            "campaign_id": task_data.campaign_id,
            "name": task_data.name,
            "description": task_data.description,
            "type": task_data.type,
            "priority": task_data.priority.value,
            "status": TaskStatus.PENDING.value,
            "input_data": task_data.input_data,
            "context": task_data.context,
            "retry_count": 0,
        }
        
        if task_data.scheduled_at:
            db_task_data["scheduled_at"] = task_data.scheduled_at.isoformat()
        if task_data.deadline:
            db_task_data["deadline"] = task_data.deadline.isoformat()
        
        # Create task in database
        task_id = await database_service.create_agent_task(db_task_data)
        
        # Update agent status to busy if not already
        if agent_status == "idle":
            await database_service.update_agent(agent_id, {
                "status": "busy",
                "last_activity": datetime.utcnow().isoformat(),
            })
        
        logger.info("Task assigned successfully", agent_id=agent_id, task_id=task_id, task_type=task_data.type)
        
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={
                "success": True,
                "message": "Task assigned successfully",
                "data": {
                    "task_id": task_id,
                    "agent_id": agent_id,
                    "task_name": task_data.name,
                    "task_type": task_data.type,
                    "priority": task_data.priority.value,
                    "status": "pending",
                    "scheduled_at": task_data.scheduled_at.isoformat() if task_data.scheduled_at else None,
                    "deadline": task_data.deadline.isoformat() if task_data.deadline else None,
                },
            },
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to assign task", agent_id=agent_id, error=str(e))
        raise AgentException(f"Failed to assign task: {str(e)}", agent_name=agent_id)


# Campaign Orchestration Endpoints

@router.get("/orchestrator/status")
async def get_orchestrator_status() -> Dict[str, Any]:
    """
    Get campaign orchestrator status.
    
    Returns:
        Dict[str, Any]: Orchestrator status and metrics
    """
    logger.info("Getting orchestrator status")
    
    try:
        if not campaign_orchestrator.initialized:
            await campaign_orchestrator.initialize()
        
        status_data = await campaign_orchestrator.get_orchestrator_status()
        
        return {
            "success": True,
            "message": "Orchestrator status retrieved successfully",
            "data": status_data,
        }
        
    except Exception as e:
        logger.error("Failed to get orchestrator status", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get orchestrator status: {str(e)}"
        )


@router.post("/orchestrator/workflows", status_code=status.HTTP_201_CREATED)
async def create_campaign_workflow(
    campaign_request: CampaignRequest,
) -> Dict[str, Any]:
    """
    Create a new campaign workflow using the orchestrator.
    
    Args:
        campaign_request: Campaign creation request data
        
    Returns:
        Dict[str, Any]: Created workflow details
    """
    logger.info("Creating campaign workflow", industry=campaign_request.industry)
    
    try:
        # Ensure orchestrator is initialized
        if not campaign_orchestrator.initialized:
            await campaign_orchestrator.initialize()
        
        # Create workflow
        workflow = await campaign_orchestrator.create_campaign_workflow(campaign_request)
        
        return {
            "success": True,
            "message": "Campaign workflow created successfully",
            "data": {
                "workflow_id": workflow.workflow_id,
                "name": workflow.name,
                "description": workflow.description,
                "status": workflow.status.value,
                "tasks_count": len(workflow.tasks),
                "tasks": [
                    {
                        "task_id": task.task_id,
                        "name": task.name,
                        "agent_type": task.agent_type.value,
                        "status": task.status.value,
                        "dependencies": task.dependencies
                    }
                    for task in workflow.tasks
                ],
                "created_at": workflow.created_at.isoformat(),
            },
        }
        
    except Exception as e:
        logger.error("Failed to create campaign workflow", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create campaign workflow: {str(e)}"
        )


@router.post("/orchestrator/workflows/{workflow_id}/execute")
async def execute_workflow(
    workflow_id: str = Path(..., description="Workflow ID"),
) -> Dict[str, Any]:
    """
    Execute a campaign workflow.
    
    Args:
        workflow_id: ID of the workflow to execute
        
    Returns:
        Dict[str, Any]: Workflow execution results
    """
    logger.info("Executing workflow", workflow_id=workflow_id)
    
    try:
        # Execute workflow
        completed_workflow = await campaign_orchestrator.execute_workflow(workflow_id)
        
        return {
            "success": True,
            "message": "Workflow executed successfully",
            "data": {
                "workflow_id": completed_workflow.workflow_id,
                "name": completed_workflow.name,
                "status": completed_workflow.status.value,
                "started_at": completed_workflow.started_at.isoformat() if completed_workflow.started_at else None,
                "completed_at": completed_workflow.completed_at.isoformat() if completed_workflow.completed_at else None,
                "total_execution_time": completed_workflow.total_execution_time,
                "tasks_summary": [
                    {
                        "task_id": task.task_id,
                        "name": task.name,
                        "agent_type": task.agent_type.value,
                        "status": task.status.value,
                        "execution_time": task.execution_time,
                        "error_message": task.error_message
                    }
                    for task in completed_workflow.tasks
                ],
                "results_summary": {
                    key: {"status": "completed", "data_size": len(str(value))}
                    for key, value in completed_workflow.results.items()
                }
            },
        }
        
    except Exception as e:
        logger.error("Failed to execute workflow", workflow_id=workflow_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute workflow: {str(e)}"
        )


@router.get("/orchestrator/workflows/{workflow_id}")
async def get_workflow_status(
    workflow_id: str = Path(..., description="Workflow ID"),
) -> Dict[str, Any]:
    """
    Get workflow status and details.
    
    Args:
        workflow_id: ID of the workflow
        
    Returns:
        Dict[str, Any]: Workflow status and details
    """
    logger.info("Getting workflow status", workflow_id=workflow_id)
    
    try:
        workflow = await campaign_orchestrator.get_workflow_status(workflow_id)
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Workflow {workflow_id} not found"
            )
        
        return {
            "success": True,
            "message": "Workflow status retrieved successfully",
            "data": {
                "workflow_id": workflow.workflow_id,
                "name": workflow.name,
                "description": workflow.description,
                "status": workflow.status.value,
                "user_id": workflow.user_id,
                "created_at": workflow.created_at.isoformat(),
                "started_at": workflow.started_at.isoformat() if workflow.started_at else None,
                "completed_at": workflow.completed_at.isoformat() if workflow.completed_at else None,
                "total_execution_time": workflow.total_execution_time,
                "tasks": [
                    {
                        "task_id": task.task_id,
                        "name": task.name,
                        "agent_type": task.agent_type.value,
                        "status": task.status.value,
                        "dependencies": task.dependencies,
                        "created_at": task.created_at.isoformat(),
                        "started_at": task.started_at.isoformat() if task.started_at else None,
                        "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                        "execution_time": task.execution_time,
                        "error_message": task.error_message,
                        "outputs_available": bool(task.outputs)
                    }
                    for task in workflow.tasks
                ],
                "results_available": len(workflow.results)
            },
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get workflow status", workflow_id=workflow_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get workflow status: {str(e)}"
        )


@router.get("/orchestrator/workflows")
async def list_active_workflows() -> Dict[str, Any]:
    """
    List all active workflows.
    
    Returns:
        Dict[str, Any]: List of active workflows
    """
    logger.info("Listing active workflows")
    
    try:
        workflows = await campaign_orchestrator.list_active_workflows()
        
        return {
            "success": True,
            "message": "Active workflows retrieved successfully",
            "data": [
                {
                    "workflow_id": workflow.workflow_id,
                    "name": workflow.name,
                    "status": workflow.status.value,
                    "user_id": workflow.user_id,
                    "tasks_count": len(workflow.tasks),
                    "completed_tasks": len([t for t in workflow.tasks if t.status.value == "completed"]),
                    "created_at": workflow.created_at.isoformat(),
                    "started_at": workflow.started_at.isoformat() if workflow.started_at else None,
                }
                for workflow in workflows
            ],
            "total": len(workflows)
        }
        
    except Exception as e:
        logger.error("Failed to list active workflows", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list active workflows: {str(e)}"
        )


@router.delete("/orchestrator/workflows/{workflow_id}")
async def cancel_workflow(
    workflow_id: str = Path(..., description="Workflow ID"),
) -> Dict[str, Any]:
    """
    Cancel an active workflow.
    
    Args:
        workflow_id: ID of the workflow to cancel
        
    Returns:
        Dict[str, Any]: Cancellation confirmation
    """
    logger.info("Cancelling workflow", workflow_id=workflow_id)
    
    try:
        success = await campaign_orchestrator.cancel_workflow(workflow_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Workflow {workflow_id} not found or already completed"
            )
        
        return {
            "success": True,
            "message": "Workflow cancelled successfully",
            "data": {"workflow_id": workflow_id, "status": "cancelled"}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to cancel workflow", workflow_id=workflow_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel workflow: {str(e)}"
        )