"""
Health check endpoints for monitoring system status.
Provides comprehensive health monitoring for all system components.
"""

import asyncio
import time
from typing import Dict, Any
from datetime import datetime

from fastapi import APIRouter, Depends, status
from pydantic import BaseModel

from services import database_service, google_ads_service, redis_service
from utils.config import settings
from utils.logging import get_logger
from utils.helpers import utc_now


logger = get_logger(__name__)

router = APIRouter()


class HealthStatus(BaseModel):
    """Health status response model."""
    status: str
    timestamp: datetime
    version: str
    environment: str
    uptime_seconds: float
    checks: Dict[str, Dict[str, Any]]


class ServiceCheck(BaseModel):
    """Individual service check result."""
    healthy: bool
    response_time_ms: float
    message: str
    details: Dict[str, Any] = {}


# Track application start time
_start_time = time.time()


async def check_database() -> ServiceCheck:
    """
    Check database connectivity.
    
    Returns:
        ServiceCheck: Database health status
    """
    start_time = time.time()
    
    # Check if the service is available
    if database_service is None:
        response_time = (time.time() - start_time) * 1000
        return ServiceCheck(
            healthy=False,
            response_time_ms=response_time,
            message="Database service not available (import failed)",
            details={"error": "Service not imported due to missing dependencies"},
        )
    
    try:
        # Use actual database service health check
        health_result = await database_service.health_check()
        response_time = (time.time() - start_time) * 1000
        
        is_healthy = health_result.get("status") == "healthy"
        
        return ServiceCheck(
            healthy=is_healthy,
            response_time_ms=response_time,
            message=health_result.get("note", "Database connection successful" if is_healthy else "Database connection failed"),
            details={
                "url": settings.database_url[:50] + "..." if settings.database_url else "Not configured",
                "authenticated": health_result.get("authenticated", False),
                "last_check": health_result.get("last_check"),
            },
        )
    
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Database health check failed", error=str(e))
        
        return ServiceCheck(
            healthy=False,
            response_time_ms=response_time,
            message=f"Database connection failed: {str(e)}",
            details={"error": str(e)},
        )


async def check_redis() -> ServiceCheck:
    """
    Check Redis connectivity.
    
    Returns:
        ServiceCheck: Redis health status
    """
    start_time = time.time()
    
    # Check if the service is available
    if redis_service is None:
        response_time = (time.time() - start_time) * 1000
        return ServiceCheck(
            healthy=False,
            response_time_ms=response_time,
            message="Redis service not available (import failed)",
            details={"error": "Service not imported due to missing dependencies"},
        )
    
    try:
        # Use actual Redis service health check
        health_result = await redis_service.health_check()
        response_time = (time.time() - start_time) * 1000
        
        is_healthy = health_result.get("status") == "healthy"
        
        return ServiceCheck(
            healthy=is_healthy,
            response_time_ms=response_time,
            message="Redis connection successful" if is_healthy else "Redis connection failed",
            details={
                "url": settings.REDIS_URL,
                "operation_time_seconds": health_result.get("operation_time_seconds"),
                "connected_clients": health_result.get("connected_clients"),
                "used_memory_human": health_result.get("used_memory_human"),
                "redis_version": health_result.get("redis_version"),
                "last_check": health_result.get("last_check"),
            },
        )
    
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Redis health check failed", error=str(e))
        
        return ServiceCheck(
            healthy=False,
            response_time_ms=response_time,
            message=f"Redis connection failed: {str(e)}",
            details={"error": str(e)},
        )


async def check_google_ads_api() -> ServiceCheck:
    """
    Check Google Ads API connectivity.
    
    Returns:
        ServiceCheck: Google Ads API health status
    """
    start_time = time.time()
    
    # Check if the service is available
    if google_ads_service is None:
        response_time = (time.time() - start_time) * 1000
        return ServiceCheck(
            healthy=False,
            response_time_ms=response_time,
            message="Google Ads service not available (import failed)",
            details={"error": "Service not imported due to missing dependencies"},
        )
    
    try:
        # Use actual Google Ads service health check
        health_result = await google_ads_service.health_check()
        response_time = (time.time() - start_time) * 1000
        
        is_healthy = health_result.get("status") == "healthy"
        
        return ServiceCheck(
            healthy=is_healthy,
            response_time_ms=response_time,
            message="Google Ads API connection successful" if is_healthy else "Google Ads API connection failed",
            details={
                "customer_id": health_result.get("customer_id"),
                "authenticated": health_result.get("authenticated", False),
                "last_check": health_result.get("last_check"),
                "error": health_result.get("error") if not is_healthy else None,
            },
        )
    
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Google Ads API health check failed", error=str(e))
        
        return ServiceCheck(
            healthy=False,
            response_time_ms=response_time,
            message=f"Google Ads API check failed: {str(e)}",
            details={"error": str(e)},
        )


async def check_openai_api() -> ServiceCheck:
    """
    Check OpenAI API connectivity.
    
    Returns:
        ServiceCheck: OpenAI API health status
    """
    start_time = time.time()
    
    try:
        # TODO: Implement actual OpenAI API connectivity check
        # For now, check if API key is configured
        await asyncio.sleep(0.015)  # Simulate API call
        
        response_time = (time.time() - start_time) * 1000
        
        if settings.OPENAI_API_KEY:
            return ServiceCheck(
                healthy=True,
                response_time_ms=response_time,
                message="OpenAI API key configured",
                details={"model": settings.OPENAI_MODEL},
            )
        else:
            return ServiceCheck(
                healthy=False,
                response_time_ms=response_time,
                message="OpenAI API key not configured",
                details={"missing_api_key": True},
            )
    
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("OpenAI API health check failed", error=str(e))
        
        return ServiceCheck(
            healthy=False,
            response_time_ms=response_time,
            message=f"OpenAI API check failed: {str(e)}",
            details={"error": str(e)},
        )


async def check_pinecone() -> ServiceCheck:
    """
    Check Pinecone connectivity.
    
    Returns:
        ServiceCheck: Pinecone health status
    """
    start_time = time.time()
    
    try:
        # TODO: Implement actual Pinecone connectivity check
        # For now, check if credentials are configured
        await asyncio.sleep(0.01)  # Simulate API call
        
        response_time = (time.time() - start_time) * 1000
        
        has_credentials = all([
            settings.PINECONE_API_KEY,
            settings.PINECONE_ENVIRONMENT,
        ])
        
        if has_credentials:
            return ServiceCheck(
                healthy=True,
                response_time_ms=response_time,
                message="Pinecone credentials configured",
                details={
                    "environment": settings.PINECONE_ENVIRONMENT,
                    "index_name": settings.PINECONE_INDEX_NAME,
                },
            )
        else:
            return ServiceCheck(
                healthy=False,
                response_time_ms=response_time,
                message="Pinecone credentials not configured",
                details={"missing_credentials": True},
            )
    
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Pinecone health check failed", error=str(e))
        
        return ServiceCheck(
            healthy=False,
            response_time_ms=response_time,
            message=f"Pinecone check failed: {str(e)}",
            details={"error": str(e)},
        )


@router.get("/", response_model=HealthStatus)
async def health_check() -> HealthStatus:
    """
    Comprehensive health check endpoint.
    
    Returns:
        HealthStatus: Overall system health status
    """
    logger.info("Health check requested")
    
    # Calculate uptime
    uptime_seconds = time.time() - _start_time
    
    # Run all health checks in parallel
    database_check, redis_check, google_ads_check, openai_check, pinecone_check = await asyncio.gather(
        check_database(),
        check_redis(),
        check_google_ads_api(),
        check_openai_api(),
        check_pinecone(),
        return_exceptions=True,
    )
    
    # Collect check results
    checks = {
        "database": database_check.dict() if isinstance(database_check, ServiceCheck) else {
            "healthy": False,
            "response_time_ms": 0,
            "message": f"Health check failed: {str(database_check)}",
            "details": {"error": str(database_check)},
        },
        "redis": redis_check.dict() if isinstance(redis_check, ServiceCheck) else {
            "healthy": False,
            "response_time_ms": 0,
            "message": f"Health check failed: {str(redis_check)}",
            "details": {"error": str(redis_check)},
        },
        "google_ads_api": google_ads_check.dict() if isinstance(google_ads_check, ServiceCheck) else {
            "healthy": False,
            "response_time_ms": 0,
            "message": f"Health check failed: {str(google_ads_check)}",
            "details": {"error": str(google_ads_check)},
        },
        "openai_api": openai_check.dict() if isinstance(openai_check, ServiceCheck) else {
            "healthy": False,
            "response_time_ms": 0,
            "message": f"Health check failed: {str(openai_check)}",
            "details": {"error": str(openai_check)},
        },
        "pinecone": pinecone_check.dict() if isinstance(pinecone_check, ServiceCheck) else {
            "healthy": False,
            "response_time_ms": 0,
            "message": f"Health check failed: {str(pinecone_check)}",
            "details": {"error": str(pinecone_check)},
        },
    }
    
    # Determine overall status
    all_healthy = all(check.get("healthy", False) for check in checks.values())
    overall_status = "healthy" if all_healthy else "unhealthy"
    
    logger.info(
        "Health check completed",
        status=overall_status,
        checks_passed=sum(1 for check in checks.values() if check.get("healthy", False)),
        total_checks=len(checks),
    )
    
    return HealthStatus(
        status=overall_status,
        timestamp=utc_now(),
        version=settings.VERSION,
        environment=settings.ENVIRONMENT,
        uptime_seconds=round(uptime_seconds, 2),
        checks=checks,
    )


@router.get("/liveness")
async def liveness_check() -> Dict[str, str]:
    """
    Simple liveness check endpoint.
    Used by container orchestrators to determine if the application is running.
    
    Returns:
        Dict[str, str]: Liveness status
    """
    return {"status": "alive", "timestamp": utc_now().isoformat()}


@router.get("/readiness")
async def readiness_check() -> Dict[str, Any]:
    """
    Readiness check endpoint.
    Used by container orchestrators to determine if the application is ready to serve traffic.
    
    Returns:
        Dict[str, Any]: Readiness status
    """
    # Check critical services for readiness
    database_check = await check_database()
    redis_check = await check_redis()
    
    ready = database_check.healthy and redis_check.healthy
    
    return {
        "status": "ready" if ready else "not_ready",
        "timestamp": utc_now().isoformat(),
        "checks": {
            "database": database_check.healthy,
            "redis": redis_check.healthy,
        },
    }