"""
Google Ads API integration endpoints.
Handles Google Ads API operations, synchronization, and account management.
"""

from typing import List, Optional, Dict, Any
from datetime import date, datetime, timedelta
from uuid import UUID

from fastapi import APIRouter, Depends, Query, Path, Body, status, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from models.campaigns import CampaignType, CampaignStatus, BiddingStrategy, KeywordMatchType, AdStatus
from models.common import BaseResponse, PaginatedResponse
from services import google_ads_service, google_ads_auth_service, database_service
from services.google_ads_auth import GoogleAdsCredentials, GoogleAdsAccount
from utils.logging import get_logger
from utils.exceptions import GoogleAdsException as CustomGoogleAdsException, NotFoundException, ValidationException


logger = get_logger(__name__)

router = APIRouter()


# Request/Response Models for Google Ads API

class GoogleAdsAccountResponse(BaseModel):
    """Google Ads account information response."""
    id: str = Field(..., description="Customer account ID")
    descriptive_name: str = Field(..., description="Account descriptive name")
    currency_code: str = Field(..., description="Account currency code")
    time_zone: str = Field(..., description="Account time zone")
    manager: bool = Field(..., description="Whether this is a manager account")


class GoogleAdsSyncRequest(BaseModel):
    """Request model for Google Ads synchronization."""
    start_date: Optional[date] = Field(None, description="Start date for sync (defaults to 30 days ago)")
    end_date: Optional[date] = Field(None, description="End date for sync (defaults to today)")
    campaign_ids: Optional[List[str]] = Field(None, description="Specific campaign IDs to sync (optional)")


class AdGroupCreateRequest(BaseModel):
    """Request model for ad group creation."""
    campaign_id: str = Field(..., description="Parent campaign ID")
    name: str = Field(..., min_length=1, max_length=255, description="Ad group name")
    max_cpc: Optional[float] = Field(None, gt=0, description="Maximum cost per click")


class KeywordCreateRequest(BaseModel):
    """Request model for keyword creation."""
    text: str = Field(..., min_length=1, max_length=80, description="Keyword text")
    match_type: KeywordMatchType = Field(..., description="Keyword match type")
    max_cpc: Optional[float] = Field(None, gt=0, description="Maximum cost per click")


class AdGroupKeywordsRequest(BaseModel):
    """Request model for adding keywords to ad group."""
    ad_group_id: str = Field(..., description="Target ad group ID")
    keywords: List[KeywordCreateRequest] = Field(..., min_items=1, description="Keywords to add")


class ResponsiveSearchAdRequest(BaseModel):
    """Request model for responsive search ad creation."""
    ad_group_id: str = Field(..., description="Target ad group ID")
    headlines: List[str] = Field(..., min_items=3, max_items=15, description="Ad headlines")
    descriptions: List[str] = Field(..., min_items=2, max_items=4, description="Ad descriptions")
    final_urls: List[str] = Field(..., min_items=1, description="Final landing page URLs")


class BudgetUpdateRequest(BaseModel):
    """Request model for budget updates."""
    campaign_id: str = Field(..., description="Campaign ID to update")
    new_budget_amount: float = Field(..., gt=0, description="New daily budget amount")


class BidUpdateRequest(BaseModel):
    """Request model for bid updates."""
    max_cpc: float = Field(..., gt=0, description="New maximum cost per click")


class OAuth2AuthRequest(BaseModel):
    """Request model for OAuth2 authorization."""
    redirect_uri: str = Field(..., description="OAuth2 redirect URI")
    state: Optional[str] = Field(None, description="CSRF protection state parameter")


class OAuth2TokenRequest(BaseModel):
    """Request model for OAuth2 token exchange."""
    authorization_code: str = Field(..., description="Authorization code from OAuth2 callback")
    redirect_uri: str = Field(..., description="Redirect URI used in authorization")
    customer_id: Optional[str] = Field(None, description="Google Ads customer ID")


# OAuth2 Authentication Endpoints

@router.post("/auth/url", response_model=Dict[str, str])
async def generate_oauth2_url(
    auth_request: OAuth2AuthRequest = Body(...),
) -> Dict[str, str]:
    """
    Generate OAuth2 authorization URL for Google Ads API.
    
    Args:
        auth_request: OAuth2 authorization request
        
    Returns:
        Dict[str, str]: Authorization URL and state parameter
        
    Raises:
        HTTPException: If URL generation fails
    """
    logger.info("Generating OAuth2 authorization URL", redirect_uri=auth_request.redirect_uri)
    
    try:
        auth_data = google_ads_auth_service.generate_auth_url(
            redirect_uri=auth_request.redirect_uri,
            state=auth_request.state,
        )
        
        logger.info("OAuth2 URL generated successfully", state=auth_data["state"])
        
        return {
            "success": True,
            "message": "OAuth2 authorization URL generated successfully",
            "auth_url": auth_data["auth_url"],
            "state": auth_data["state"],
        }
        
    except Exception as e:
        logger.error("Failed to generate OAuth2 URL", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.post("/auth/token", response_model=None, status_code=status.HTTP_201_CREATED)
async def exchange_oauth2_token(
    token_request: OAuth2TokenRequest = Body(...),
) -> JSONResponse:
    """
    Exchange OAuth2 authorization code for access tokens.
    
    Args:
        token_request: Token exchange request
        
    Returns:
        JSONResponse: Token exchange status and credentials info
        
    Raises:
        HTTPException: If token exchange fails
    """
    logger.info("Exchanging OAuth2 authorization code for tokens")
    
    try:
        credentials = await google_ads_auth_service.exchange_code_for_tokens(
            authorization_code=token_request.authorization_code,
            redirect_uri=token_request.redirect_uri,
            customer_id=token_request.customer_id,
        )
        
        logger.info("OAuth2 token exchange successful", customer_id=credentials.customer_id)
        
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={
                "success": True,
                "message": "OAuth2 tokens obtained successfully",
                "data": {
                    "customer_id": credentials.customer_id,
                    "expires_at": credentials.expires_at.isoformat(),
                    "authenticated": True,
                }
            }
        )
        
    except CustomGoogleAdsException as e:
        logger.error("OAuth2 token exchange failed", error=str(e))
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error during token exchange", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.post("/auth/refresh/{customer_id}", response_model=None)
async def refresh_oauth2_token(
    customer_id: str = Path(..., description="Google Ads customer ID"),
) -> JSONResponse:
    """
    Refresh OAuth2 access token for a customer account.
    
    Args:
        customer_id: Google Ads customer identifier
        
    Returns:
        JSONResponse: Token refresh status
        
    Raises:
        HTTPException: If token refresh fails
    """
    logger.info("Refreshing OAuth2 access token", customer_id=customer_id)
    
    try:
        # Get existing credentials
        existing_credentials = await google_ads_auth_service.get_credentials(customer_id, auto_refresh=False)
        if not existing_credentials:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No credentials found for customer")
        
        # Refresh token
        credentials = await google_ads_auth_service.refresh_access_token(
            refresh_token=existing_credentials.refresh_token,
            customer_id=customer_id,
        )
        
        logger.info("OAuth2 token refreshed successfully", customer_id=customer_id)
        
        return JSONResponse(
            content={
                "success": True,
                "message": "OAuth2 token refreshed successfully",
                "data": {
                    "customer_id": credentials.customer_id,
                    "expires_at": credentials.expires_at.isoformat(),
                    "authenticated": True,
                }
            }
        )
        
    except CustomGoogleAdsException as e:
        logger.error("OAuth2 token refresh failed", error=str(e))
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error during token refresh", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.delete("/auth/{customer_id}", response_model=None)
async def revoke_oauth2_credentials(
    customer_id: str = Path(..., description="Google Ads customer ID"),
) -> JSONResponse:
    """
    Revoke OAuth2 credentials for a customer account.
    
    Args:
        customer_id: Google Ads customer identifier
        
    Returns:
        JSONResponse: Revocation status
        
    Raises:
        HTTPException: If credential revocation fails
    """
    logger.info("Revoking OAuth2 credentials", customer_id=customer_id)
    
    try:
        success = await google_ads_auth_service.revoke_credentials(customer_id)
        
        if success:
            logger.info("OAuth2 credentials revoked successfully", customer_id=customer_id)
            
            return JSONResponse(
                content={
                    "success": True,
                    "message": "OAuth2 credentials revoked successfully",
                    "data": {
                        "customer_id": customer_id,
                        "revoked": True,
                        "timestamp": datetime.utcnow().isoformat(),
                    }
                }
            )
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to revoke credentials")
        
    except CustomGoogleAdsException as e:
        logger.error("OAuth2 credential revocation failed", error=str(e))
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error during credential revocation", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/auth/accounts", response_model=List[GoogleAdsAccount])
async def list_authenticated_accounts() -> List[GoogleAdsAccount]:
    """
    List all authenticated Google Ads accounts.
    
    Returns:
        List[GoogleAdsAccount]: List of authenticated accounts with credentials
        
    Raises:
        HTTPException: If account listing fails
    """
    logger.info("Listing authenticated Google Ads accounts")
    
    try:
        accounts = await google_ads_auth_service.list_authenticated_accounts()
        
        logger.info(f"Retrieved {len(accounts)} authenticated accounts")
        return accounts
        
    except CustomGoogleAdsException as e:
        logger.error("Failed to list authenticated accounts", error=str(e))
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error listing authenticated accounts", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


# Account Management Endpoints

@router.get("/accounts", response_model=List[GoogleAdsAccountResponse])
async def list_accessible_accounts() -> List[GoogleAdsAccountResponse]:
    """
    List accessible Google Ads customer accounts.
    
    Returns:
        List[GoogleAdsAccountResponse]: List of accessible customer accounts
        
    Raises:
        HTTPException: If account listing fails
    """
    logger.info("Listing accessible Google Ads accounts")
    
    try:
        accounts = await google_ads_service.list_accessible_customers()
        
        response_accounts = [
            GoogleAdsAccountResponse(
                id=account["id"],
                descriptive_name=account["descriptive_name"],
                currency_code=account["currency_code"],
                time_zone=account["time_zone"],
                manager=account["manager"],
            )
            for account in accounts
        ]
        
        logger.info(f"Retrieved {len(response_accounts)} accessible accounts")
        return response_accounts
        
    except CustomGoogleAdsException as e:
        logger.error("Failed to list accessible accounts", error=str(e))
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error listing accounts", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/health", response_model=Dict[str, Any])
async def google_ads_health_check() -> Dict[str, Any]:
    """
    Check Google Ads API service health.
    
    Returns:
        Dict[str, Any]: Health check results
    """
    logger.info("Performing Google Ads API health check")
    
    try:
        health_status = await google_ads_service.health_check()
        
        if health_status["status"] == "healthy":
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=health_status
            )
        else:
            return JSONResponse(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                content=health_status
            )
            
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "error": str(e),
                "authenticated": False,
                "last_check": datetime.utcnow().isoformat(),
            }
        )


# Campaign Synchronization Endpoints

@router.post("/sync/campaigns", response_model=None, status_code=status.HTTP_202_ACCEPTED)
async def sync_campaigns(
    sync_request: GoogleAdsSyncRequest = Body(...),
) -> JSONResponse:
    """
    Sync campaigns from Google Ads to local database.
    
    Args:
        sync_request: Synchronization request parameters
        
    Returns:
        JSONResponse: Sync status and summary
        
    Raises:
        HTTPException: If synchronization fails
    """
    logger.info("Starting campaign sync from Google Ads", request=sync_request.dict())
    
    try:
        # Get campaigns from Google Ads
        campaigns = await google_ads_service.list_campaigns(limit=1000)
        
        # Filter by specific campaign IDs if provided
        if sync_request.campaign_ids:
            campaigns = [c for c in campaigns if c["id"] in sync_request.campaign_ids]
        
        synced_campaigns = []
        failed_campaigns = []
        
        for campaign in campaigns:
            try:
                # Check if campaign exists in database
                existing_campaign = None
                try:
                    # Try to find by Google Ads ID
                    db_campaigns = await database_service.list_campaigns(
                        filters={"google_ads_id": campaign["id"]},
                        limit=1
                    )
                    if db_campaigns:
                        existing_campaign = db_campaigns[0]
                except Exception:
                    pass
                
                campaign_data = {
                    "name": campaign["name"],
                    "type": campaign["type"].lower(),
                    "status": campaign["status"].lower().replace("enabled", "active"),
                    "budget_amount": campaign["budget_amount"],
                    "google_ads_id": campaign["id"],
                    "bidding_strategy": "manual_cpc",  # Default, could be enhanced
                }
                
                if existing_campaign:
                    # Update existing campaign
                    await database_service.update_campaign(
                        existing_campaign["id"],
                        campaign_data
                    )
                    synced_campaigns.append({
                        "id": existing_campaign["id"],
                        "google_ads_id": campaign["id"],
                        "name": campaign["name"],
                        "action": "updated"
                    })
                else:
                    # Create new campaign
                    new_campaign_id = await database_service.create_campaign(campaign_data)
                    synced_campaigns.append({
                        "id": new_campaign_id,
                        "google_ads_id": campaign["id"],
                        "name": campaign["name"],
                        "action": "created"
                    })
                
            except Exception as e:
                logger.warning(
                    "Failed to sync campaign",
                    campaign_id=campaign["id"],
                    error=str(e)
                )
                failed_campaigns.append({
                    "google_ads_id": campaign["id"],
                    "name": campaign["name"],
                    "error": str(e)
                })
        
        logger.info(
            "Campaign sync completed",
            total_campaigns=len(campaigns),
            synced=len(synced_campaigns),
            failed=len(failed_campaigns)
        )
        
        return JSONResponse(
            status_code=status.HTTP_202_ACCEPTED,
            content={
                "success": True,
                "message": "Campaign sync completed",
                "data": {
                    "total_campaigns": len(campaigns),
                    "synced_campaigns": len(synced_campaigns),
                    "failed_campaigns": len(failed_campaigns),
                    "synced": synced_campaigns,
                    "failed": failed_campaigns,
                }
            }
        )
        
    except CustomGoogleAdsException as e:
        logger.error("Failed to sync campaigns", error=str(e))
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error syncing campaigns", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.post("/sync/performance", response_model=None, status_code=status.HTTP_202_ACCEPTED)
async def sync_performance_data(
    sync_request: GoogleAdsSyncRequest = Body(...),
) -> JSONResponse:
    """
    Sync performance data from Google Ads.
    
    Args:
        sync_request: Synchronization request parameters
        
    Returns:
        JSONResponse: Sync status and performance data summary
        
    Raises:
        HTTPException: If synchronization fails
    """
    logger.info("Starting performance data sync from Google Ads")
    
    try:
        # Set default date range if not provided
        end_date = sync_request.end_date or date.today()
        start_date = sync_request.start_date or (end_date - timedelta(days=30))
        
        if sync_request.campaign_ids:
            # Sync specific campaigns
            performance_data = []
            for campaign_id in sync_request.campaign_ids:
                try:
                    campaign_performance = await google_ads_service.sync_campaign_performance(
                        campaign_id=campaign_id,
                        start_date=start_date,
                        end_date=end_date,
                    )
                    performance_data.append(campaign_performance)
                except Exception as e:
                    logger.warning(
                        "Failed to sync performance for campaign",
                        campaign_id=campaign_id,
                        error=str(e)
                    )
        else:
            # Sync all campaigns
            performance_data = await google_ads_service.sync_all_campaigns_performance(
                start_date=start_date,
                end_date=end_date,
            )
        
        # Store performance data in database
        stored_records = 0
        for campaign_data in performance_data:
            try:
                # Find local campaign by Google Ads ID
                db_campaigns = await database_service.list_campaigns(
                    filters={"google_ads_id": campaign_data["campaign_id"]},
                    limit=1
                )
                
                if db_campaigns:
                    await database_service.save_campaign_metrics(
                        campaign_id=db_campaigns[0]["id"],
                        metrics_data=campaign_data["metrics"],
                        date=end_date,
                    )
                    stored_records += 1
                    
            except Exception as e:
                logger.warning(
                    "Failed to store performance data",
                    campaign_id=campaign_data.get("campaign_id"),
                    error=str(e)
                )
        
        logger.info(
            "Performance data sync completed",
            campaigns_synced=len(performance_data),
            records_stored=stored_records,
            date_range=f"{start_date} to {end_date}"
        )
        
        return JSONResponse(
            status_code=status.HTTP_202_ACCEPTED,
            content={
                "success": True,
                "message": "Performance data sync completed",
                "data": {
                    "campaigns_synced": len(performance_data),
                    "records_stored": stored_records,
                    "date_range": {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                    },
                    "performance_summary": performance_data,
                }
            }
        )
        
    except CustomGoogleAdsException as e:
        logger.error("Failed to sync performance data", error=str(e))
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error syncing performance data", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


# Campaign Management Endpoints

@router.post("/campaigns/{campaign_id}/update", response_model=None)
async def push_campaign_changes(
    campaign_id: str = Path(..., description="Local campaign ID"),
) -> JSONResponse:
    """
    Push local campaign changes to Google Ads.
    
    Args:
        campaign_id: Local campaign identifier
        
    Returns:
        JSONResponse: Update status
        
    Raises:
        HTTPException: If update fails
    """
    logger.info("Pushing campaign changes to Google Ads", campaign_id=campaign_id)
    
    try:
        # Get local campaign data
        campaign_data = await database_service.get_campaign(campaign_id)
        if not campaign_data:
            raise NotFoundException("Campaign", campaign_id)
        
        google_ads_id = campaign_data.get("google_ads_id")
        if not google_ads_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Campaign is not linked to Google Ads"
            )
        
        # Update campaign status if needed
        current_google_campaign = await google_ads_service.get_campaign(google_ads_id)
        local_status = campaign_data["status"]
        
        # Map local status to Google Ads status
        status_mapping = {
            "active": CampaignStatus.ACTIVE,
            "paused": CampaignStatus.PAUSED,
            "draft": CampaignStatus.PAUSED,
        }
        
        target_status = status_mapping.get(local_status)
        if target_status and current_google_campaign["status"].lower() != local_status:
            await google_ads_service.update_campaign_status(google_ads_id, target_status)
        
        # Update budget if different
        if abs(current_google_campaign["budget_amount"] - campaign_data["budget_amount"]) > 0.01:
            await google_ads_service.update_campaign_budget(
                google_ads_id,
                campaign_data["budget_amount"]
            )
        
        logger.info("Campaign changes pushed successfully", campaign_id=campaign_id)
        
        return JSONResponse(
            content={
                "success": True,
                "message": "Campaign changes pushed to Google Ads successfully",
                "data": {
                    "campaign_id": campaign_id,
                    "google_ads_id": google_ads_id,
                    "updated_fields": ["status", "budget"],
                    "timestamp": datetime.utcnow().isoformat(),
                }
            }
        )
        
    except NotFoundException:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Campaign not found")
    except CustomGoogleAdsException as e:
        logger.error("Failed to push campaign changes", error=str(e))
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error pushing campaign changes", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


# Ad Group Management Endpoints

@router.post("/ad-groups", response_model=None, status_code=status.HTTP_201_CREATED)
async def create_ad_group(
    ad_group_data: AdGroupCreateRequest = Body(...),
) -> JSONResponse:
    """
    Create a new ad group in Google Ads.
    
    Args:
        ad_group_data: Ad group creation data
        
    Returns:
        JSONResponse: Created ad group details
        
    Raises:
        HTTPException: If creation fails
    """
    logger.info("Creating ad group", campaign_id=ad_group_data.campaign_id, name=ad_group_data.name)
    
    try:
        # Get campaign data to find Google Ads campaign ID
        campaign = await database_service.get_campaign(ad_group_data.campaign_id)
        if not campaign:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Campaign not found")
        
        google_ads_campaign_id = campaign.get("google_ads_id")
        if not google_ads_campaign_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Campaign is not linked to Google Ads"
            )
        
        # Create ad group in Google Ads
        ad_group_id = await google_ads_service.create_ad_group(
            campaign_id=google_ads_campaign_id,
            name=ad_group_data.name,
            max_cpc=ad_group_data.max_cpc,
        )
        
        logger.info("Ad group created successfully", ad_group_id=ad_group_id)
        
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={
                "success": True,
                "message": "Ad group created successfully",
                "data": {
                    "ad_group_id": ad_group_id,
                    "campaign_id": ad_group_data.campaign_id,
                    "google_ads_campaign_id": google_ads_campaign_id,
                    "name": ad_group_data.name,
                    "max_cpc": ad_group_data.max_cpc,
                }
            }
        )
        
    except CustomGoogleAdsException as e:
        logger.error("Failed to create ad group", error=str(e))
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error creating ad group", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/campaigns/{campaign_id}/ad-groups", response_model=None)
async def list_ad_groups(
    campaign_id: str = Path(..., description="Campaign ID"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of ad groups to return"),
) -> JSONResponse:
    """
    List ad groups for a campaign.
    
    Args:
        campaign_id: Campaign identifier
        limit: Maximum number of ad groups to return
        
    Returns:
        JSONResponse: List of ad groups
        
    Raises:
        HTTPException: If listing fails
    """
    logger.info("Listing ad groups", campaign_id=campaign_id, limit=limit)
    
    try:
        # Get campaign data to find Google Ads campaign ID
        campaign = await database_service.get_campaign(campaign_id)
        if not campaign:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Campaign not found")
        
        google_ads_campaign_id = campaign.get("google_ads_id")
        if not google_ads_campaign_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Campaign is not linked to Google Ads"
            )
        
        # Get ad groups from Google Ads
        ad_groups = await google_ads_service.list_ad_groups(
            campaign_id=google_ads_campaign_id,
            limit=limit,
        )
        
        logger.info(f"Retrieved {len(ad_groups)} ad groups", campaign_id=campaign_id)
        
        return JSONResponse(
            content={
                "success": True,
                "message": "Ad groups retrieved successfully",
                "data": {
                    "campaign_id": campaign_id,
                    "google_ads_campaign_id": google_ads_campaign_id,
                    "ad_groups": ad_groups,
                    "total_count": len(ad_groups),
                }
            }
        )
        
    except CustomGoogleAdsException as e:
        logger.error("Failed to list ad groups", error=str(e))
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error listing ad groups", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.put("/ad-groups/{ad_group_id}/cpc", response_model=None)
async def update_ad_group_cpc(
    ad_group_id: str = Path(..., description="Ad group ID"),
    bid_data: BidUpdateRequest = Body(...),
) -> JSONResponse:
    """
    Update ad group maximum CPC bid.
    
    Args:
        ad_group_id: Ad group identifier
        bid_data: New bid data
        
    Returns:
        JSONResponse: Update status
        
    Raises:
        HTTPException: If update fails
    """
    logger.info("Updating ad group CPC", ad_group_id=ad_group_id, new_cpc=bid_data.max_cpc)
    
    try:
        # Update ad group CPC in Google Ads
        success = await google_ads_service.update_ad_group_cpc(
            ad_group_id=ad_group_id,
            max_cpc=bid_data.max_cpc,
        )
        
        if success:
            logger.info("Ad group CPC updated successfully", ad_group_id=ad_group_id)
            
            return JSONResponse(
                content={
                    "success": True,
                    "message": "Ad group CPC updated successfully",
                    "data": {
                        "ad_group_id": ad_group_id,
                        "new_max_cpc": bid_data.max_cpc,
                        "timestamp": datetime.utcnow().isoformat(),
                    }
                }
            )
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to update ad group CPC")
        
    except CustomGoogleAdsException as e:
        logger.error("Failed to update ad group CPC", error=str(e))
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error updating ad group CPC", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


# Keyword Management Endpoints

@router.post("/keywords", response_model=None, status_code=status.HTTP_201_CREATED)
async def add_keywords(
    keyword_data: AdGroupKeywordsRequest = Body(...),
) -> JSONResponse:
    """
    Add keywords to an ad group.
    
    Args:
        keyword_data: Keywords to add
        
    Returns:
        JSONResponse: Created keywords status
        
    Raises:
        HTTPException: If creation fails
    """
    logger.info("Adding keywords to ad group", ad_group_id=keyword_data.ad_group_id, count=len(keyword_data.keywords))
    
    try:
        # Convert keyword requests to dictionary format
        keywords_dict = [
            {
                "text": kw.text,
                "match_type": kw.match_type.value,
                "max_cpc": kw.max_cpc,
            }
            for kw in keyword_data.keywords
        ]
        
        # Add keywords to Google Ads
        keyword_ids = await google_ads_service.add_keywords(
            ad_group_id=keyword_data.ad_group_id,
            keywords=keywords_dict,
        )
        
        logger.info("Keywords added successfully", ad_group_id=keyword_data.ad_group_id, keyword_ids=keyword_ids)
        
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={
                "success": True,
                "message": "Keywords added successfully",
                "data": {
                    "ad_group_id": keyword_data.ad_group_id,
                    "keywords_added": len(keyword_ids),
                    "keyword_ids": keyword_ids,
                    "keywords": keywords_dict,
                }
            }
        )
        
    except CustomGoogleAdsException as e:
        logger.error("Failed to add keywords", error=str(e))
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error adding keywords", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.put("/keywords/{keyword_id}/bid", response_model=None)
async def update_keyword_bid(
    keyword_id: str = Path(..., description="Keyword ID"),
    bid_data: BidUpdateRequest = Body(...),
) -> JSONResponse:
    """
    Update keyword bid.
    
    Args:
        keyword_id: Keyword identifier
        bid_data: New bid data
        
    Returns:
        JSONResponse: Update status
        
    Raises:
        HTTPException: If update fails
    """
    logger.info("Updating keyword bid", keyword_id=keyword_id, new_bid=bid_data.max_cpc)
    
    try:
        # Update keyword bid in Google Ads
        success = await google_ads_service.update_keyword_bid(
            ad_group_criterion_id=keyword_id,
            max_cpc=bid_data.max_cpc,
        )
        
        if success:
            logger.info("Keyword bid updated successfully", keyword_id=keyword_id)
            
            return JSONResponse(
                content={
                    "success": True,
                    "message": "Keyword bid updated successfully",
                    "data": {
                        "keyword_id": keyword_id,
                        "new_max_cpc": bid_data.max_cpc,
                        "timestamp": datetime.utcnow().isoformat(),
                    }
                }
            )
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to update keyword bid")
        
    except CustomGoogleAdsException as e:
        logger.error("Failed to update keyword bid", error=str(e))
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error updating keyword bid", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


# Ad Management Endpoints

@router.post("/ads/responsive-search", response_model=None, status_code=status.HTTP_201_CREATED)
async def create_responsive_search_ad(
    ad_data: ResponsiveSearchAdRequest = Body(...),
) -> JSONResponse:
    """
    Create a responsive search ad.
    
    Args:
        ad_data: Ad creation data
        
    Returns:
        JSONResponse: Created ad details
        
    Raises:
        HTTPException: If creation fails
    """
    logger.info("Creating responsive search ad", ad_group_id=ad_data.ad_group_id)
    
    try:
        # Create responsive search ad in Google Ads
        ad_id = await google_ads_service.create_responsive_search_ad(
            ad_group_id=ad_data.ad_group_id,
            headlines=ad_data.headlines,
            descriptions=ad_data.descriptions,
            final_urls=ad_data.final_urls,
        )
        
        logger.info("Responsive search ad created successfully", ad_id=ad_id)
        
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={
                "success": True,
                "message": "Responsive search ad created successfully",
                "data": {
                    "ad_id": ad_id,
                    "ad_group_id": ad_data.ad_group_id,
                    "headlines_count": len(ad_data.headlines),
                    "descriptions_count": len(ad_data.descriptions),
                    "final_urls": ad_data.final_urls,
                }
            }
        )
        
    except CustomGoogleAdsException as e:
        logger.error("Failed to create responsive search ad", error=str(e))
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error creating responsive search ad", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.put("/ads/{ad_id}/status", response_model=None)
async def update_ad_status(
    ad_id: str = Path(..., description="Ad ID"),
    status_data: Dict[str, str] = Body(...),
) -> JSONResponse:
    """
    Update ad status.
    
    Args:
        ad_id: Ad identifier
        status_data: New status data (should contain 'status' field)
        
    Returns:
        JSONResponse: Update status
        
    Raises:
        HTTPException: If update fails
    """
    logger.info("Updating ad status", ad_id=ad_id, new_status=status_data.get("status"))
    
    try:
        # Validate status
        status_str = status_data.get("status")
        if not status_str:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Status is required")
        
        try:
            new_status = AdStatus(status_str.lower())
        except ValueError:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid status")
        
        # Update ad status in Google Ads
        success = await google_ads_service.update_ad_status(
            ad_group_ad_id=ad_id,
            status=new_status,
        )
        
        if success:
            logger.info("Ad status updated successfully", ad_id=ad_id)
            
            return JSONResponse(
                content={
                    "success": True,
                    "message": "Ad status updated successfully",
                    "data": {
                        "ad_id": ad_id,
                        "new_status": new_status.value,
                        "timestamp": datetime.utcnow().isoformat(),
                    }
                }
            )
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to update ad status")
        
    except CustomGoogleAdsException as e:
        logger.error("Failed to update ad status", error=str(e))
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error updating ad status", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


# Budget Management Endpoints

@router.put("/budgets", response_model=None)
async def update_campaign_budget(
    budget_data: BudgetUpdateRequest = Body(...),
) -> JSONResponse:
    """
    Update campaign daily budget.
    
    Args:
        budget_data: Budget update data
        
    Returns:
        JSONResponse: Update status
        
    Raises:
        HTTPException: If update fails
    """
    logger.info("Updating campaign budget", campaign_id=budget_data.campaign_id, new_budget=budget_data.new_budget_amount)
    
    try:
        # Get campaign data to find Google Ads campaign ID
        campaign = await database_service.get_campaign(budget_data.campaign_id)
        if not campaign:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Campaign not found")
        
        google_ads_campaign_id = campaign.get("google_ads_id")
        if not google_ads_campaign_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Campaign is not linked to Google Ads"
            )
        
        # Update budget in Google Ads
        success = await google_ads_service.update_campaign_budget(
            campaign_id=google_ads_campaign_id,
            new_budget_amount=budget_data.new_budget_amount,
        )
        
        if success:
            # Update local database
            await database_service.update_campaign(
                budget_data.campaign_id,
                {"budget_amount": budget_data.new_budget_amount}
            )
            
            logger.info("Campaign budget updated successfully", campaign_id=budget_data.campaign_id)
            
            return JSONResponse(
                content={
                    "success": True,
                    "message": "Campaign budget updated successfully",
                    "data": {
                        "campaign_id": budget_data.campaign_id,
                        "google_ads_campaign_id": google_ads_campaign_id,
                        "new_budget_amount": budget_data.new_budget_amount,
                        "timestamp": datetime.utcnow().isoformat(),
                    }
                }
            )
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to update campaign budget")
        
    except CustomGoogleAdsException as e:
        logger.error("Failed to update campaign budget", error=str(e))
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error updating campaign budget", error=str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")