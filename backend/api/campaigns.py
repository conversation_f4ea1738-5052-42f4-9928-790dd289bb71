"""
Campaign management API endpoints.
Handles Google Ads campaign creation, management, and optimization.
"""

from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, Query, Path, Body, status
from fastapi.responses import JSONResponse

from models.campaigns import (
    Campaign,
    CampaignCreate,
    CampaignUpdate,
    CampaignResponse,
    CampaignListResponse,
    CampaignStatus,
    CampaignType,
)
from models.common import PaginatedResponse, PaginationInfo
from services import database_service, google_ads_service
from utils.logging import get_logger
from utils.exceptions import NotFoundException, ValidationException


logger = get_logger(__name__)

router = APIRouter()


@router.post("/", response_model=CampaignResponse, status_code=status.HTTP_201_CREATED)
async def create_campaign(
    campaign_data: CampaignCreate,
) -> CampaignResponse:
    """
    Create a new Google Ads campaign.
    
    Args:
        campaign_data: Campaign creation data
        
    Returns:
        CampaignResponse: Created campaign details
        
    Raises:
        ValidationException: If campaign data is invalid
    """
    logger.info("Creating new campaign", campaign_name=campaign_data.name)
    
    try:
        # Step 1: Create campaign in Google Ads first
        google_ads_campaign_id = await google_ads_service.create_campaign(
            name=campaign_data.name,
            campaign_type=campaign_data.type,
            budget_amount=campaign_data.budget_amount,
            bidding_strategy=campaign_data.bidding_strategy,
            target_locations=campaign_data.target_locations,
        )
        
        # Step 2: Prepare campaign data for database
        db_campaign_data = {
            "name": campaign_data.name,
            "description": campaign_data.description,
            "type": campaign_data.type.value,
            "status": CampaignStatus.DRAFT.value,
            "budget_amount": campaign_data.budget_amount,
            "bidding_strategy": campaign_data.bidding_strategy.value,
            "target_locations": campaign_data.target_locations,
            "target_languages": [lang.value for lang in campaign_data.target_languages],
            "keywords": campaign_data.keywords,
            "google_ads_id": google_ads_campaign_id,
            "auto_optimization_enabled": campaign_data.auto_optimization_enabled,
        }
        
        if campaign_data.start_date:
            db_campaign_data["start_date"] = campaign_data.start_date.isoformat()
        if campaign_data.end_date:
            db_campaign_data["end_date"] = campaign_data.end_date.isoformat()
        
        # Step 3: Store campaign in database
        campaign_id = await database_service.create_campaign(db_campaign_data)
        
        # Step 4: Retrieve the created campaign from database
        created_campaign_data = await database_service.get_campaign(campaign_id)
        
        # Step 5: Create Campaign model instance
        campaign = Campaign(
            id=created_campaign_data["id"],
            name=created_campaign_data["name"],
            description=created_campaign_data.get("description"),
            type=CampaignType(created_campaign_data["type"]),
            status=CampaignStatus(created_campaign_data["status"]),
            budget_amount=created_campaign_data["budget_amount"],
            bidding_strategy=created_campaign_data["bidding_strategy"],
            target_locations=created_campaign_data.get("target_locations", []),
            target_languages=created_campaign_data.get("target_languages", []),
            keywords=created_campaign_data.get("keywords", []),
            google_ads_id=created_campaign_data.get("google_ads_id"),
            auto_optimization_enabled=created_campaign_data.get("auto_optimization_enabled", True),
            created_at=created_campaign_data.get("created_at"),
            updated_at=created_campaign_data.get("updated_at"),
        )
        
        logger.info("Campaign created successfully", campaign_id=campaign.id)
        
        return CampaignResponse(
            success=True,
            message="Campaign created successfully",
            data=campaign,
        )
        
    except Exception as e:
        logger.error("Failed to create campaign", error=str(e))
        raise ValidationException(f"Failed to create campaign: {str(e)}")


@router.get("/", response_model=CampaignListResponse)
async def list_campaigns(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    status: Optional[CampaignStatus] = Query(None, description="Filter by campaign status"),
    campaign_type: Optional[CampaignType] = Query(None, description="Filter by campaign type"),
    search: Optional[str] = Query(None, description="Search campaigns by name or description"),
) -> CampaignListResponse:
    """
    List campaigns with optional filtering and pagination.
    
    Args:
        skip: Number of records to skip for pagination
        limit: Maximum number of records to return
        status: Filter by campaign status
        campaign_type: Filter by campaign type
        search: Search term for campaign name or description
        
    Returns:
        CampaignListResponse: List of campaigns with pagination info
    """
    logger.info(
        "Listing campaigns",
        skip=skip,
        limit=limit,
        status=status,
        campaign_type=campaign_type,
        search=search,
    )
    
    try:
        # Step 1: Build filters
        filters = {}
        if status:
            filters["status"] = status.value
        if campaign_type:
            filters["type"] = campaign_type.value
        
        # Step 2: Get campaigns from database
        campaign_data_list = await database_service.list_campaigns(
            filters=filters,
            limit=limit,
            offset=skip,
            order_by="created_at",
            order_direction="desc",
        )
        
        # Step 3: Get total count for pagination
        total = await database_service.get_count("campaigns", filters)
        
        # Step 4: Apply search filter (if needed, implement in database service)
        if search:
            # For now, filter in-memory - in production, implement in database query
            search_lower = search.lower()
            campaign_data_list = [
                c for c in campaign_data_list
                if search_lower in c.get("name", "").lower() 
                or search_lower in c.get("description", "").lower()
            ]
        
        # Step 5: Convert to Campaign models
        campaigns = []
        for campaign_data in campaign_data_list:
            try:
                campaign = Campaign(
                    id=campaign_data["id"],
                    name=campaign_data["name"],
                    description=campaign_data.get("description"),
                    type=CampaignType(campaign_data["type"]),
                    status=CampaignStatus(campaign_data["status"]),
                    budget_amount=campaign_data["budget_amount"],
                    bidding_strategy=campaign_data.get("bidding_strategy", "manual_cpc"),
                    target_locations=campaign_data.get("target_locations", []),
                    target_languages=campaign_data.get("target_languages", []),
                    keywords=campaign_data.get("keywords", []),
                    google_ads_id=campaign_data.get("google_ads_id"),
                    auto_optimization_enabled=campaign_data.get("auto_optimization_enabled", True),
                    created_at=campaign_data.get("created_at"),
                    updated_at=campaign_data.get("updated_at"),
                )
                campaigns.append(campaign)
            except Exception as e:
                logger.warning(
                    "Skipping invalid campaign data",
                    campaign_id=campaign_data.get("id"),
                    error=str(e),
                )
        
        # Step 6: Create pagination info
        pagination = PaginationInfo(
            skip=skip,
            limit=limit,  
            total=total,
            has_more=skip + limit < total,
        )
        
        return CampaignListResponse(
            success=True,
            message="Campaigns retrieved successfully",
            data=campaigns,
            pagination=pagination,
        )
        
    except Exception as e:
        logger.error("Failed to list campaigns", error=str(e))
        raise ValidationException(f"Failed to list campaigns: {str(e)}")


@router.get("/{campaign_id}", response_model=CampaignResponse)
async def get_campaign(
    campaign_id: str = Path(..., description="Campaign ID"),
) -> CampaignResponse:
    """
    Get campaign details by ID.
    
    Args:
        campaign_id: Campaign identifier
        
    Returns:
        CampaignResponse: Campaign details
        
    Raises:
        NotFoundException: If campaign is not found
    """
    logger.info("Getting campaign details", campaign_id=campaign_id)
    
    try:
        # Step 1: Get campaign from database
        campaign_data = await database_service.get_campaign(campaign_id)
        
        if not campaign_data:
            raise NotFoundException("Campaign", campaign_id)
        
        # Step 2: Optionally fetch latest metrics from Google Ads
        # This could be implemented later for real-time data
        
        # Step 3: Create Campaign model
        campaign = Campaign(
            id=campaign_data["id"],
            name=campaign_data["name"],
            description=campaign_data.get("description"),
            type=CampaignType(campaign_data["type"]),
            status=CampaignStatus(campaign_data["status"]),
            budget_amount=campaign_data["budget_amount"],
            bidding_strategy=campaign_data.get("bidding_strategy", "manual_cpc"),
            target_locations=campaign_data.get("target_locations", []),
            target_languages=campaign_data.get("target_languages", []),
            keywords=campaign_data.get("keywords", []),
            google_ads_id=campaign_data.get("google_ads_id"),
            auto_optimization_enabled=campaign_data.get("auto_optimization_enabled", True),
            created_at=campaign_data.get("created_at"),
            updated_at=campaign_data.get("updated_at"),
        )
        
        return CampaignResponse(
            success=True,
            message="Campaign retrieved successfully",
            data=campaign,
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to get campaign", campaign_id=campaign_id, error=str(e))
        raise ValidationException(f"Failed to get campaign: {str(e)}")


@router.put("/{campaign_id}", response_model=CampaignResponse)
async def update_campaign(
    campaign_id: str = Path(..., description="Campaign ID"),
    campaign_data: CampaignUpdate = Body(...),
) -> CampaignResponse:
    """
    Update campaign details.
    
    Args:
        campaign_id: Campaign identifier
        campaign_data: Campaign update data
        
    Returns:
        CampaignResponse: Updated campaign details
        
    Raises:
        NotFoundException: If campaign is not found
        ValidationException: If update data is invalid
    """
    logger.info("Updating campaign", campaign_id=campaign_id)
    
    try:
        # Step 1: Validate campaign exists
        existing_campaign = await database_service.get_campaign(campaign_id)
        if not existing_campaign:
            raise NotFoundException("Campaign", campaign_id)
        
        # Step 2: Prepare update data
        update_data = {}
        
        if campaign_data.name is not None:
            update_data["name"] = campaign_data.name
        if campaign_data.description is not None:
            update_data["description"] = campaign_data.description
        if campaign_data.status is not None:
            update_data["status"] = campaign_data.status.value
        if campaign_data.budget_amount is not None:
            update_data["budget_amount"] = campaign_data.budget_amount
        if campaign_data.bidding_strategy is not None:
            update_data["bidding_strategy"] = campaign_data.bidding_strategy.value
        if campaign_data.target_locations is not None:
            update_data["target_locations"] = campaign_data.target_locations
        if campaign_data.target_languages is not None:
            update_data["target_languages"] = [lang.value for lang in campaign_data.target_languages]
        if campaign_data.keywords is not None:
            update_data["keywords"] = campaign_data.keywords
        if campaign_data.negative_keywords is not None:
            update_data["negative_keywords"] = campaign_data.negative_keywords
        if campaign_data.start_date is not None:
            update_data["start_date"] = campaign_data.start_date.isoformat()
        if campaign_data.end_date is not None:
            update_data["end_date"] = campaign_data.end_date.isoformat()
        if campaign_data.auto_optimization_enabled is not None:
            update_data["auto_optimization_enabled"] = campaign_data.auto_optimization_enabled
        
        # Step 3: Update campaign in Google Ads if status is changing
        if campaign_data.status and campaign_data.status.value != existing_campaign["status"]:
            google_ads_id = existing_campaign.get("google_ads_id")
            if google_ads_id:
                await google_ads_service.update_campaign_status(google_ads_id, campaign_data.status)
        
        # Step 4: Update campaign in database
        await database_service.update_campaign(campaign_id, update_data)
        
        # Step 5: Retrieve updated campaign
        updated_campaign_data = await database_service.get_campaign(campaign_id)
        
        # Step 6: Create Campaign model
        campaign = Campaign(
            id=updated_campaign_data["id"],
            name=updated_campaign_data["name"],
            description=updated_campaign_data.get("description"),
            type=CampaignType(updated_campaign_data["type"]),
            status=CampaignStatus(updated_campaign_data["status"]),
            budget_amount=updated_campaign_data["budget_amount"],
            bidding_strategy=updated_campaign_data.get("bidding_strategy", "manual_cpc"),
            target_locations=updated_campaign_data.get("target_locations", []),
            target_languages=updated_campaign_data.get("target_languages", []),
            keywords=updated_campaign_data.get("keywords", []),
            negative_keywords=updated_campaign_data.get("negative_keywords", []),
            google_ads_id=updated_campaign_data.get("google_ads_id"),
            auto_optimization_enabled=updated_campaign_data.get("auto_optimization_enabled", True),
            created_at=updated_campaign_data.get("created_at"),
            updated_at=updated_campaign_data.get("updated_at"),
        )
        
        logger.info("Campaign updated successfully", campaign_id=campaign_id)
        
        return CampaignResponse(
            success=True,
            message="Campaign updated successfully",
            data=campaign,
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to update campaign", campaign_id=campaign_id, error=str(e))
        raise ValidationException(f"Failed to update campaign: {str(e)}")


@router.delete("/{campaign_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_campaign(
    campaign_id: str = Path(..., description="Campaign ID"),
) -> None:
    """
    Delete a campaign.
    
    Args:
        campaign_id: Campaign identifier
        
    Raises:
        NotFoundException: If campaign is not found
    """
    logger.info("Deleting campaign", campaign_id=campaign_id)
    
    try:
        # Step 1: Validate campaign exists
        existing_campaign = await database_service.get_campaign(campaign_id)
        if not existing_campaign:
            raise NotFoundException("Campaign", campaign_id)
        
        # Step 2: Pause campaign in Google Ads first
        google_ads_id = existing_campaign.get("google_ads_id")
        if google_ads_id:
            try:
                await google_ads_service.update_campaign_status(
                    google_ads_id, 
                    CampaignStatus.PAUSED
                )
            except Exception as e:
                logger.warning(
                    "Failed to pause campaign in Google Ads before deletion",
                    campaign_id=campaign_id,
                    google_ads_id=google_ads_id,
                    error=str(e),
                )
        
        # Step 3: Mark campaign as deleted in database (soft delete)
        await database_service.delete_campaign(campaign_id)
        
        # Step 4: TODO - Stop optimization agents
        # This would be implemented when agent system is fully integrated
        
        logger.info("Campaign deleted successfully", campaign_id=campaign_id)
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to delete campaign", campaign_id=campaign_id, error=str(e))
        raise ValidationException(f"Failed to delete campaign: {str(e)}")


@router.post("/{campaign_id}/start", response_model=CampaignResponse)
async def start_campaign(
    campaign_id: str = Path(..., description="Campaign ID"),
) -> CampaignResponse:
    """
    Start a campaign (set status to ACTIVE).
    
    Args:
        campaign_id: Campaign identifier
        
    Returns:
        CampaignResponse: Updated campaign details
        
    Raises:
        NotFoundException: If campaign is not found
    """
    logger.info("Starting campaign", campaign_id=campaign_id)
    
    try:
        # Step 1: Validate campaign exists and can be started
        existing_campaign = await database_service.get_campaign(campaign_id)
        if not existing_campaign:
            raise NotFoundException("Campaign", campaign_id)
        
        # Step 2: Set campaign status to ACTIVE in Google Ads
        google_ads_id = existing_campaign.get("google_ads_id")
        if google_ads_id:
            await google_ads_service.update_campaign_status(
                google_ads_id, 
                CampaignStatus.ACTIVE
            )
        
        # Step 3: Update campaign status in database
        await database_service.update_campaign(campaign_id, {
            "status": CampaignStatus.ACTIVE.value
        })
        
        # Step 4: Get updated campaign data
        updated_campaign_data = await database_service.get_campaign(campaign_id)
        
        # Step 5: Create Campaign model
        campaign = Campaign(
            id=updated_campaign_data["id"],
            name=updated_campaign_data["name"],
            description=updated_campaign_data.get("description"),
            type=CampaignType(updated_campaign_data["type"]),
            status=CampaignStatus(updated_campaign_data["status"]),
            budget_amount=updated_campaign_data["budget_amount"],
            bidding_strategy=updated_campaign_data.get("bidding_strategy", "manual_cpc"),
            target_locations=updated_campaign_data.get("target_locations", []),
            target_languages=updated_campaign_data.get("target_languages", []),
            keywords=updated_campaign_data.get("keywords", []),
            google_ads_id=updated_campaign_data.get("google_ads_id"),
            auto_optimization_enabled=updated_campaign_data.get("auto_optimization_enabled", True),
            created_at=updated_campaign_data.get("created_at"),
            updated_at=updated_campaign_data.get("updated_at"),
        )
        
        # Step 6: TODO - Start optimization agents
        # This would be implemented when agent system is fully integrated
        
        logger.info("Campaign started successfully", campaign_id=campaign_id)
        
        return CampaignResponse(
            success=True,
            message="Campaign started successfully",
            data=campaign,
        )
        
    except Exception as e:
        logger.error("Failed to start campaign", campaign_id=campaign_id, error=str(e))
        raise ValidationException(f"Failed to start campaign: {str(e)}")


@router.post("/{campaign_id}/pause", response_model=CampaignResponse)
async def pause_campaign(
    campaign_id: str = Path(..., description="Campaign ID"),
) -> CampaignResponse:
    """
    Pause a campaign (set status to PAUSED).
    
    Args:
        campaign_id: Campaign identifier
        
    Returns:
        CampaignResponse: Updated campaign details
        
    Raises:
        NotFoundException: If campaign is not found
    """
    logger.info("Pausing campaign", campaign_id=campaign_id)
    
    try:
        # Step 1: Validate campaign exists and can be paused
        existing_campaign = await database_service.get_campaign(campaign_id)
        if not existing_campaign:
            raise NotFoundException("Campaign", campaign_id)
        
        # Step 2: Set campaign status to PAUSED in Google Ads
        google_ads_id = existing_campaign.get("google_ads_id")
        if google_ads_id:
            await google_ads_service.update_campaign_status(
                google_ads_id, 
                CampaignStatus.PAUSED
            )
        
        # Step 3: Update campaign status in database
        await database_service.update_campaign(campaign_id, {
            "status": CampaignStatus.PAUSED.value
        })
        
        # Step 4: Get updated campaign data
        updated_campaign_data = await database_service.get_campaign(campaign_id)
        
        # Step 5: Create Campaign model
        campaign = Campaign(
            id=updated_campaign_data["id"],
            name=updated_campaign_data["name"],
            description=updated_campaign_data.get("description"),
            type=CampaignType(updated_campaign_data["type"]),
            status=CampaignStatus(updated_campaign_data["status"]),
            budget_amount=updated_campaign_data["budget_amount"],
            bidding_strategy=updated_campaign_data.get("bidding_strategy", "manual_cpc"),
            target_locations=updated_campaign_data.get("target_locations", []),
            target_languages=updated_campaign_data.get("target_languages", []),
            keywords=updated_campaign_data.get("keywords", []),
            google_ads_id=updated_campaign_data.get("google_ads_id"),
            auto_optimization_enabled=updated_campaign_data.get("auto_optimization_enabled", True),
            created_at=updated_campaign_data.get("created_at"),
            updated_at=updated_campaign_data.get("updated_at"),
        )
        
        # Step 6: TODO - Pause optimization agents
        # This would be implemented when agent system is fully integrated
        
        logger.info("Campaign paused successfully", campaign_id=campaign_id)
        
        return CampaignResponse(
            success=True,
            message="Campaign paused successfully",
            data=campaign,
        )
        
    except Exception as e:
        logger.error("Failed to pause campaign", campaign_id=campaign_id, error=str(e))
        raise ValidationException(f"Failed to pause campaign: {str(e)}")


@router.post("/{campaign_id}/optimize")
async def trigger_optimization(
    campaign_id: str = Path(..., description="Campaign ID"),
) -> JSONResponse:
    """
    Trigger manual optimization for a campaign.
    
    Args:
        campaign_id: Campaign identifier
        
    Returns:
        JSONResponse: Optimization trigger confirmation
        
    Raises:
        NotFoundException: If campaign is not found
    """
    logger.info("Triggering optimization", campaign_id=campaign_id)
    
    try:
        # Step 1: Validate campaign exists and is active
        existing_campaign = await database_service.get_campaign(campaign_id)
        if not existing_campaign:
            raise NotFoundException("Campaign", campaign_id)
        
        # Step 2: Create optimization task in database
        import uuid
        from datetime import datetime
        
        task_id = str(uuid.uuid4())
        
        task_data = {
            "id": task_id,
            "agent_id": "optimization-agent",  # This would be a real agent ID
            "campaign_id": campaign_id,
            "name": f"Campaign Optimization - {existing_campaign['name']}",
            "description": f"Manual optimization trigger for campaign {campaign_id}",
            "type": "optimization",
            "priority": "high",
            "status": "pending",
            "input_data": {
                "campaign_id": campaign_id,
                "optimization_type": "manual",
                "trigger_reason": "user_request",
            },
            "context": {
                "campaign_name": existing_campaign["name"],
                "campaign_type": existing_campaign["type"],
                "trigger_time": datetime.utcnow().isoformat(),
            },
        }
        
        # Create the task in database
        created_task_id = await database_service.create_agent_task(task_data)
        
        logger.info(
            "Optimization task created successfully",
            campaign_id=campaign_id,
            task_id=created_task_id,
        )
        
        return JSONResponse(
            status_code=status.HTTP_202_ACCEPTED,
            content={
                "success": True,
                "message": "Optimization triggered successfully",
                "data": {
                    "task_id": created_task_id,
                    "campaign_id": campaign_id,
                    "status": "queued",
                    "estimated_completion": "5-10 minutes",
                },
            },
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to trigger optimization", campaign_id=campaign_id, error=str(e))
        raise ValidationException(f"Failed to trigger optimization: {str(e)}")


@router.get("/{campaign_id}/metrics")
async def get_campaign_metrics(
    campaign_id: str = Path(..., description="Campaign ID"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
) -> JSONResponse:
    """
    Get campaign performance metrics.
    
    Args:
        campaign_id: Campaign identifier
        start_date: Start date for metrics
        end_date: End date for metrics
        
    Returns:
        JSONResponse: Campaign performance metrics
        
    Raises:
        NotFoundException: If campaign is not found
    """
    logger.info("Getting campaign metrics", campaign_id=campaign_id, start_date=start_date, end_date=end_date)
    
    try:
        # Step 1: Validate campaign exists
        existing_campaign = await database_service.get_campaign(campaign_id)
        if not existing_campaign:
            raise NotFoundException("Campaign", campaign_id)
        
        # Step 2: Get metrics from database
        metrics = await database_service.get_campaign_metrics(
            campaign_id=campaign_id,
            start_date=start_date,
            end_date=end_date,
        )
        
        # Step 3: If no metrics found, try to fetch from Google Ads
        if not metrics:
            google_ads_id = existing_campaign.get("google_ads_id")
            if google_ads_id:
                try:
                    # Fetch latest metrics from Google Ads
                    from datetime import datetime, timedelta
                    end_dt = datetime.now()
                    start_dt = end_dt - timedelta(days=30)  # Default to 30 days
                    
                    if start_date:
                        start_dt = datetime.fromisoformat(start_date)
                    if end_date:
                        end_dt = datetime.fromisoformat(end_date)
                    
                    google_metrics = await google_ads_service.get_campaign_metrics(
                        campaign_id=google_ads_id,
                        start_date=start_dt.date(),
                        end_date=end_dt.date(),
                    )
                    
                    # Save metrics to database for future use
                    if google_metrics:
                        await database_service.save_campaign_metrics(
                            campaign_id=campaign_id,
                            metrics_data=google_metrics,
                            date=end_dt,
                        )
                        metrics = [google_metrics]
                        
                except Exception as e:
                    logger.warning(
                        "Failed to fetch metrics from Google Ads",
                        campaign_id=campaign_id,
                        error=str(e),
                    )
        
        # Step 4: Calculate aggregated metrics
        if metrics:
            # Aggregate metrics if multiple records
            total_metrics = {
                "impressions": sum(m.get("metrics", {}).get("impressions", 0) for m in metrics),
                "clicks": sum(m.get("metrics", {}).get("clicks", 0) for m in metrics),
                "conversions": sum(m.get("metrics", {}).get("conversions", 0) for m in metrics),
                "cost": sum(m.get("metrics", {}).get("cost", 0) for m in metrics),
                "revenue": sum(m.get("metrics", {}).get("revenue", 0) for m in metrics),
            }
            
            # Calculate derived metrics
            derived_metrics = {}
            if total_metrics["impressions"] > 0:
                derived_metrics["ctr"] = total_metrics["clicks"] / total_metrics["impressions"]
                derived_metrics["cpm"] = (total_metrics["cost"] / total_metrics["impressions"]) * 1000
            
            if total_metrics["clicks"] > 0:
                derived_metrics["cpc"] = total_metrics["cost"] / total_metrics["clicks"]
                derived_metrics["conversion_rate"] = total_metrics["conversions"] / total_metrics["clicks"]
            
            if total_metrics["conversions"] > 0:
                derived_metrics["cost_per_conversion"] = total_metrics["cost"] / total_metrics["conversions"]
            
            if total_metrics["cost"] > 0 and total_metrics["revenue"] > 0:
                derived_metrics["roas"] = total_metrics["revenue"] / total_metrics["cost"]
                derived_metrics["roi"] = (total_metrics["revenue"] - total_metrics["cost"]) / total_metrics["cost"]
        else:
            total_metrics = {}
            derived_metrics = {}
        
        logger.info("Campaign metrics retrieved successfully", campaign_id=campaign_id, metrics_count=len(metrics))
        
        return JSONResponse(
            content={
                "success": True,
                "message": "Campaign metrics retrieved successfully",
                "data": {
                    "campaign_id": campaign_id,
                    "date_range": {
                        "start_date": start_date,
                        "end_date": end_date,
                    },
                    "performance": total_metrics,
                    "derived_metrics": derived_metrics,
                    "historical_data": metrics,
                },
            },
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to get campaign metrics", campaign_id=campaign_id, error=str(e))
        raise ValidationException(f"Failed to get campaign metrics: {str(e)}")


@router.post("/{campaign_id}/metrics", status_code=status.HTTP_201_CREATED)
async def record_campaign_metrics(
    campaign_id: str = Path(..., description="Campaign ID"),
    metrics_data: Dict[str, Any] = Body(...),
) -> JSONResponse:
    """
    Record new campaign performance metrics.
    
    Args:
        campaign_id: Campaign identifier
        metrics_data: Metrics data to record
        
    Returns:
        JSONResponse: Confirmation of metrics recording
        
    Raises:
        NotFoundException: If campaign is not found
        ValidationException: If metrics data is invalid
    """
    logger.info("Recording campaign metrics", campaign_id=campaign_id)
    
    try:
        # Step 1: Validate campaign exists
        existing_campaign = await database_service.get_campaign(campaign_id)
        if not existing_campaign:
            raise NotFoundException("Campaign", campaign_id)
        
        # Step 2: Validate metrics data
        required_fields = ["impressions", "clicks", "cost"]
        for field in required_fields:
            if field not in metrics_data:
                raise ValidationException(f"Missing required field: {field}")
        
        # Step 3: Save metrics to database
        from datetime import datetime
        record_date = datetime.now()
        if "date" in metrics_data:
            record_date = datetime.fromisoformat(metrics_data["date"])
        
        await database_service.save_campaign_metrics(
            campaign_id=campaign_id,
            metrics_data=metrics_data,
            date=record_date,
        )
        
        logger.info("Campaign metrics recorded successfully", campaign_id=campaign_id)
        
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={
                "success": True,
                "message": "Campaign metrics recorded successfully",
                "data": {
                    "campaign_id": campaign_id,
                    "date": record_date.isoformat(),
                    "metrics_recorded": list(metrics_data.keys()),
                },
            },
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to record campaign metrics", campaign_id=campaign_id, error=str(e))
        raise ValidationException(f"Failed to record campaign metrics: {str(e)}")


@router.get("/{campaign_id}/optimization-history")
async def get_optimization_history(
    campaign_id: str = Path(..., description="Campaign ID"),
    limit: int = Query(50, ge=1, le=500, description="Maximum number of records to return"),
    offset: int = Query(0, ge=0, description="Number of records to skip"),
) -> JSONResponse:
    """
    Get campaign optimization history.
    
    Args:
        campaign_id: Campaign identifier
        limit: Maximum number of records to return
        offset: Number of records to skip
        
    Returns:
        JSONResponse: Campaign optimization history
        
    Raises:
        NotFoundException: If campaign is not found
    """
    logger.info("Getting optimization history", campaign_id=campaign_id, limit=limit, offset=offset)
    
    try:
        # Step 1: Validate campaign exists
        existing_campaign = await database_service.get_campaign(campaign_id)
        if not existing_campaign:
            raise NotFoundException("Campaign", campaign_id)
        
        # Step 2: Get optimization history from agent tasks
        optimization_tasks = await database_service.list_agent_tasks(
            filters={
                "campaign_id": campaign_id,
                "type": "optimization",
            },
            limit=limit,
            offset=offset,
            order_by="created_at",
            order_direction="desc",
        )
        
        # Step 3: Format optimization history
        history = []
        for task in optimization_tasks:
            optimization_record = {
                "id": task.get("id"),
                "timestamp": task.get("created_at"),
                "status": task.get("status"),
                "type": task.get("input_data", {}).get("optimization_type", "unknown"),
                "trigger_reason": task.get("input_data", {}).get("trigger_reason", "unknown"),
                "agent_id": task.get("agent_id"),
                "execution_time_seconds": task.get("execution_time_seconds"),
                "result_summary": task.get("result"),
                "changes_made": task.get("output_data", {}).get("changes_made", []),
                "performance_impact": task.get("output_data", {}).get("performance_impact", {}),
                "error_message": task.get("error_message"),
            }
            history.append(optimization_record)
        
        # Step 4: Get total count for pagination
        total_count = await database_service.get_count(
            "agent_tasks",
            {"campaign_id": campaign_id, "type": "optimization"}
        )
        
        logger.info(
            "Optimization history retrieved successfully",
            campaign_id=campaign_id,
            history_count=len(history),
            total_count=total_count,
        )
        
        return JSONResponse(
            content={
                "success": True,
                "message": "Optimization history retrieved successfully",
                "data": {
                    "campaign_id": campaign_id,
                    "history": history,
                    "pagination": {
                        "limit": limit,
                        "offset": offset,
                        "total": total_count,
                        "has_more": offset + limit < total_count,
                    },
                },
            },
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error("Failed to get optimization history", campaign_id=campaign_id, error=str(e))
        raise ValidationException(f"Failed to get optimization history: {str(e)}")