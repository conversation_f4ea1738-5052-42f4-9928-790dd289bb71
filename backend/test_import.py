#!/usr/bin/env python3
"""Test import issues."""

import sys
import traceback

def test_imports():
    """Test critical imports."""
    try:
        print("Testing FastAPI...")
        from fastapi import FastAPI
        print("✓ FastAPI imported")
    except Exception as e:
        print(f"✗ FastAPI failed: {e}")
        
    try:
        print("Testing auth router...")
        from api.auth import router as auth_router
        print("✓ Auth router imported")
    except Exception as e:
        print(f"✗ Auth router failed: {e}")
        traceback.print_exc()
        
    try:
        print("Testing agents router...")
        from api.agents import router as agents_router
        print("✓ Agents router imported")
    except Exception as e:
        print(f"✗ Agents router failed: {e}")
        traceback.print_exc()
        
    try:
        print("Testing main app...")
        from main import app
        print(f"✓ Main app imported, routes: {len(app.routes)}")
        for route in app.routes:
            if hasattr(route, 'path'):
                print(f"  - {route.path}")
    except Exception as e:
        print(f"✗ Main app failed: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_imports()