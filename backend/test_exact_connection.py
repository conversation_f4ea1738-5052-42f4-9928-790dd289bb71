#!/usr/bin/env python3
"""
Test the exact connection string provided by the user.
"""
import asyncio
import asyncpg

async def test_exact_connection():
    # Exact connection string as provided
    connection_string = "*******************************************************************************/postgres"
    
    print("Testing exact connection string...")
    print(f"Connection: {connection_string}")
    print()
    
    try:
        print("🔄 Attempting to connect...")
        
        # Test connection
        conn = await asyncpg.connect(connection_string)
        
        print("✅ CONNECTION SUCCESSFUL!")
        print()
        
        # Test basic query
        result = await conn.fetchrow('SELECT version();')
        print(f"✅ Database Version: {result[0]}")
        
        # Test current user and database
        user_result = await conn.fetchrow('SELECT current_user, current_database();')
        print(f"✅ Current User: {user_result[0]}")
        print(f"✅ Current Database: {user_result[1]}")
        
        # Test if we can create tables
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS connection_test (
                id SERIAL PRIMARY KEY,
                test_name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT NOW()
            );
        ''')
        print("✅ Table creation successful!")
        
        # Test insert
        await conn.execute(
            'INSERT INTO connection_test (test_name) VALUES ($1)',
            'Connection Test Success'
        )
        print("✅ Data insertion successful!")
        
        # Test select
        rows = await conn.fetch('SELECT * FROM connection_test ORDER BY id DESC LIMIT 1;')
        print(f"✅ Data query successful! Latest record: {dict(rows[0])}")
        
        # Test table info
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'connection_test';
        """)
        print(f"✅ Table exists in schema: {len(tables)} table(s) found")
        
        # Clean up test table
        await conn.execute('DROP TABLE IF EXISTS connection_test;')
        print("✅ Cleanup successful!")
        
        await conn.close()
        
        print()
        print("🎉 ALL TESTS PASSED!")
        print("✅ Connection string is working perfectly")
        print("✅ Database is ready for setup")
        
        return True
        
    except asyncpg.exceptions.InvalidPasswordError as e:
        print(f"❌ Authentication Error: {e}")
        print("🔍 The password might be incorrect or the user doesn't exist")
        return False
        
    except asyncpg.exceptions.InvalidCatalogNameError as e:
        print(f"❌ Database Error: {e}")
        print("🔍 The database 'postgres' might not exist")
        return False
        
    except asyncpg.exceptions.ConnectionFailureError as e:
        print(f"❌ Connection Error: {e}")
        print("🔍 Cannot reach the database server")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        print(f"🔍 Error type: {type(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_exact_connection())
    
    if success:
        print()
        print("🚀 READY TO PROCEED!")
        print("The database connection is working and we can now:")
        print("1. Run database migrations")
        print("2. Set up the schema")
        print("3. Initialize the database service")
    else:
        print()
        print("⚠️ CONNECTION FAILED")
        print("Please verify the credentials or try alternative approaches")