"""
Metrics collection and monitoring middleware for the Google Ads AI Agent System.
Provides comprehensive application metrics, performance monitoring, and health insights.
"""

import time
import json
import asyncio
from typing import Dict, List, Optional, Set, Callable, Any, Union, DefaultDict
from datetime import datetime, timezone, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from threading import Lock

import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from utils.config import settings
from services.redis_service import RedisService


logger = structlog.get_logger(__name__)


@dataclass
class MetricPoint:
    """Represents a single metric point."""
    name: str
    value: float
    timestamp: float
    tags: Dict[str, str]
    metric_type: str = "gauge"  # gauge, counter, histogram, timer
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metric point to dictionary."""
        return asdict(self)


@dataclass 
class RequestMetrics:
    """Metrics for a single request."""
    method: str
    path: str
    status_code: int
    processing_time_ms: float
    request_size_bytes: int
    response_size_bytes: int
    timestamp: float
    client_ip: str
    user_agent: str
    error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert request metrics to dictionary."""
        return asdict(self)


class MetricsCollector:
    """Collects and aggregates application metrics."""
    
    def __init__(self, redis_service: Optional[RedisService] = None):
        self.redis_service = redis_service
        self._lock = Lock()
        
        # In-memory metric storage
        self.counters: DefaultDict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = {}
        self.histograms: DefaultDict[str, List[float]] = defaultdict(list)
        self.timers: DefaultDict[str, List[float]] = defaultdict(list)
        
        # Request metrics buffer
        self.request_metrics: deque = deque(maxlen=10000)  # Keep last 10k requests
        
        # Aggregated statistics
        self.stats = {
            "total_requests": 0,
            "total_errors": 0,
            "avg_response_time": 0.0,
            "requests_per_second": 0.0,
            "error_rate": 0.0,
            "status_codes": defaultdict(int),
            "methods": defaultdict(int),
            "paths": defaultdict(int),
            "start_time": time.time(),
        }
        
        # Performance tracking
        self.performance_windows = {
            "1m": deque(maxlen=60),    # 1 second intervals for 1 minute
            "5m": deque(maxlen=60),    # 5 second intervals for 5 minutes  
            "15m": deque(maxlen=60),   # 15 second intervals for 15 minutes
            "1h": deque(maxlen=60),    # 1 minute intervals for 1 hour
        }
        
        logger.info("Metrics collector initialized")
    
    def increment_counter(self, name: str, value: float = 1.0, tags: Optional[Dict[str, str]] = None):
        """Increment a counter metric."""
        with self._lock:
            metric_key = self._build_metric_key(name, tags)
            self.counters[metric_key] += value
    
    def set_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Set a gauge metric."""
        with self._lock:
            metric_key = self._build_metric_key(name, tags)
            self.gauges[metric_key] = value
    
    def record_histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Record a histogram value."""
        with self._lock:
            metric_key = self._build_metric_key(name, tags)
            self.histograms[metric_key].append(value)
            
            # Keep only last 1000 values for memory efficiency
            if len(self.histograms[metric_key]) > 1000:
                self.histograms[metric_key] = self.histograms[metric_key][-1000:]
    
    def record_timer(self, name: str, value_ms: float, tags: Optional[Dict[str, str]] = None):
        """Record a timer value."""
        with self._lock:
            metric_key = self._build_metric_key(name, tags)
            self.timers[metric_key].append(value_ms)
            
            # Keep only last 1000 values for memory efficiency
            if len(self.timers[metric_key]) > 1000:
                self.timers[metric_key] = self.timers[metric_key][-1000:]
    
    def record_request(self, request_metrics: RequestMetrics):
        """Record request metrics."""
        with self._lock:
            self.request_metrics.append(request_metrics)
            
            # Update aggregated stats
            self.stats["total_requests"] += 1
            if request_metrics.error or request_metrics.status_code >= 400:
                self.stats["total_errors"] += 1
            
            self.stats["status_codes"][str(request_metrics.status_code)] += 1
            self.stats["methods"][request_metrics.method] += 1
            self.stats["paths"][request_metrics.path] += 1
            
            # Update error rate
            self.stats["error_rate"] = (self.stats["total_errors"] / self.stats["total_requests"]) * 100
            
            # Update average response time (rolling average)
            if hasattr(self, '_response_time_sum'):
                self._response_time_sum += request_metrics.processing_time_ms
            else:
                self._response_time_sum = request_metrics.processing_time_ms
            
            self.stats["avg_response_time"] = self._response_time_sum / self.stats["total_requests"]
            
            # Update requests per second
            uptime = time.time() - self.stats["start_time"]
            self.stats["requests_per_second"] = self.stats["total_requests"] / uptime if uptime > 0 else 0
    
    def _build_metric_key(self, name: str, tags: Optional[Dict[str, str]]) -> str:
        """Build a unique key for a metric with tags."""
        if not tags:
            return name
        
        tag_string = ",".join(f"{k}={v}" for k, v in sorted(tags.items()))
        return f"{name}[{tag_string}]"
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of all collected metrics."""
        with self._lock:
            summary = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "uptime_seconds": time.time() - self.stats["start_time"],
                "counters": dict(self.counters),
                "gauges": dict(self.gauges),
                "histograms": {
                    name: {
                        "count": len(values),
                        "min": min(values) if values else 0,
                        "max": max(values) if values else 0,
                        "avg": sum(values) / len(values) if values else 0,
                        "p50": self._percentile(values, 0.5) if values else 0,
                        "p95": self._percentile(values, 0.95) if values else 0,
                        "p99": self._percentile(values, 0.99) if values else 0,
                    }
                    for name, values in self.histograms.items()
                },
                "timers": {
                    name: {
                        "count": len(values),
                        "min_ms": min(values) if values else 0,
                        "max_ms": max(values) if values else 0,
                        "avg_ms": sum(values) / len(values) if values else 0,
                        "p50_ms": self._percentile(values, 0.5) if values else 0,
                        "p95_ms": self._percentile(values, 0.95) if values else 0,
                        "p99_ms": self._percentile(values, 0.99) if values else 0,
                    }
                    for name, values in self.timers.items()
                },
                "request_stats": dict(self.stats),
            }
            
            return summary
    
    def _percentile(self, values: List[float], percentile: float) -> float:
        """Calculate percentile of values."""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = int(percentile * (len(sorted_values) - 1))
        return sorted_values[index]
    
    def get_performance_window(self, window: str) -> List[Dict[str, Any]]:
        """Get performance data for a specific time window."""
        with self._lock:
            return list(self.performance_windows.get(window, []))
    
    async def export_to_redis(self):
        """Export metrics to Redis for persistence and analytics."""
        if not self.redis_service:
            return
        
        try:
            metrics_summary = self.get_metrics_summary()
            
            # Store current metrics
            current_minute = int(time.time() // 60)
            metrics_key = f"metrics:minute:{current_minute}"
            
            await self.redis_service.set(metrics_key, metrics_summary, ttl=3600)  # 1 hour TTL
            
            # Store in time series for analytics
            timeseries_key = "metrics:timeseries"
            timeseries_entry = {
                "timestamp": current_minute * 60,
                "total_requests": self.stats["total_requests"],
                "total_errors": self.stats["total_errors"],
                "avg_response_time": self.stats["avg_response_time"],
                "requests_per_second": self.stats["requests_per_second"],
                "error_rate": self.stats["error_rate"],
            }
            
            await self.redis_service.zadd(timeseries_key, {json.dumps(timeseries_entry): current_minute})
            
            # Keep only last 24 hours of data
            cutoff_time = current_minute - (24 * 60)
            await self.redis_service.zremrangebyscore(timeseries_key, 0, cutoff_time)
            
            logger.debug("Metrics exported to Redis successfully")
            
        except Exception as e:
            logger.error("Failed to export metrics to Redis", error=str(e))


class MetricsConfig:
    """Configuration for metrics middleware."""
    
    def __init__(
        self,
        enable_metrics: bool = True,
        enable_request_metrics: bool = True,
        enable_performance_metrics: bool = True,
        enable_custom_metrics: bool = True,
        collect_detailed_metrics: bool = True,
        export_interval_seconds: float = 60.0,
        max_cardinality: int = 10000,  # Max unique metric combinations
        excluded_paths: Optional[Set[str]] = None,
        slow_request_threshold_ms: float = 1000.0,
        error_sampling_rate: float = 1.0,  # Sample all errors by default
        success_sampling_rate: float = 0.1,  # Sample 10% of successful requests
    ):
        self.enable_metrics = enable_metrics
        self.enable_request_metrics = enable_request_metrics
        self.enable_performance_metrics = enable_performance_metrics
        self.enable_custom_metrics = enable_custom_metrics
        self.collect_detailed_metrics = collect_detailed_metrics
        self.export_interval_seconds = export_interval_seconds
        self.max_cardinality = max_cardinality
        self.slow_request_threshold_ms = slow_request_threshold_ms
        self.error_sampling_rate = error_sampling_rate
        self.success_sampling_rate = success_sampling_rate
        
        self.excluded_paths = excluded_paths or {
            "/metrics", "/health", "/favicon.ico", "/static"
        }


class MetricsMiddleware(BaseHTTPMiddleware):
    """
    Middleware for collecting application metrics and performance data.
    Provides comprehensive monitoring capabilities.
    """
    
    def __init__(
        self,
        app,
        config: Optional[MetricsConfig] = None,
        redis_service: Optional[RedisService] = None,
    ):
        super().__init__(app)
        
        self.config = config or MetricsConfig()
        self.redis_service = redis_service
        
        # Initialize metrics collector
        self.collector = MetricsCollector(redis_service)
        
        # Background export task
        self._export_task: Optional[asyncio.Task] = None
        self._shutdown = False
        
        logger.info(
            "Metrics middleware initialized",
            export_interval=self.config.export_interval_seconds,
            detailed_metrics=self.config.collect_detailed_metrics,
        )
    
    async def startup(self):
        """Start the metrics middleware."""
        if self.config.enable_metrics and self.redis_service:
            self._export_task = asyncio.create_task(self._export_metrics_periodically())
    
    async def shutdown(self):
        """Shutdown the metrics middleware."""
        self._shutdown = True
        if self._export_task:
            self._export_task.cancel()
            try:
                await self._export_task
            except asyncio.CancelledError:
                pass
        
        # Final export
        if self.redis_service:
            await self.collector.export_to_redis()
    
    def _is_path_excluded(self, path: str) -> bool:
        """Check if path is excluded from metrics collection."""
        return any(path.startswith(excluded) for excluded in self.config.excluded_paths)
    
    def _should_sample_request(self, status_code: int) -> bool:
        """Determine if request should be sampled for metrics."""
        import random
        
        if status_code >= 400:
            return random.random() < self.config.error_sampling_rate
        else:
            return random.random() < self.config.success_sampling_rate
    
    def _get_client_info(self, request: Request) -> Dict[str, str]:
        """Extract client information from request."""
        # Get client IP
        client_ip = "unknown"
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        elif request.headers.get("X-Real-IP"):
            client_ip = request.headers.get("X-Real-IP").strip()
        elif hasattr(request.client, "host"):
            client_ip = request.client.host
        
        return {
            "client_ip": client_ip,
            "user_agent": request.headers.get("user-agent", "unknown")[:100],  # Limit length
        }
    
    def _normalize_path(self, path: str) -> str:
        """Normalize path for metrics (remove IDs to reduce cardinality)."""
        import re
        
        # Replace UUIDs with placeholder
        path = re.sub(r'/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', '/{uuid}', path)
        
        # Replace numeric IDs with placeholder
        path = re.sub(r'/\d+', '/{id}', path)
        
        # Replace long alphanumeric strings that might be IDs
        path = re.sub(r'/[a-zA-Z0-9]{10,}', '/{id}', path)
        
        return path
    
    async def _collect_request_metrics(
        self,
        request: Request,
        response: Response,
        processing_time: float,
        error: Optional[Exception] = None
    ):
        """Collect metrics for a request."""
        if not self.config.enable_request_metrics:
            return
        
        # Basic request info
        method = request.method
        path = self._normalize_path(request.url.path)
        status_code = response.status_code if response else 500
        processing_time_ms = processing_time * 1000
        
        # Get request/response sizes
        request_size = int(request.headers.get("content-length", 0))
        response_size = len(getattr(response, "body", b"")) if response else 0
        
        # Get client info
        client_info = self._get_client_info(request)
        
        # Sample request if needed
        if not self._should_sample_request(status_code):
            return
        
        # Create request metrics
        request_metrics = RequestMetrics(
            method=method,
            path=path,
            status_code=status_code,
            processing_time_ms=processing_time_ms,
            request_size_bytes=request_size,
            response_size_bytes=response_size,
            timestamp=time.time(),
            client_ip=client_info["client_ip"],
            user_agent=client_info["user_agent"],
            error=str(error) if error else None,
        )
        
        # Record in collector
        self.collector.record_request(request_metrics)
        
        # Record additional metrics
        tags = {
            "method": method,
            "path": path,
            "status": str(status_code),
        }
        
        # Counter metrics
        self.collector.increment_counter("http_requests_total", 1.0, tags)
        
        if error or status_code >= 400:
            self.collector.increment_counter("http_errors_total", 1.0, tags)
        
        # Timer metrics
        self.collector.record_timer("http_request_duration", processing_time_ms, tags)
        
        # Histogram metrics
        self.collector.record_histogram("http_request_size_bytes", request_size, tags)
        self.collector.record_histogram("http_response_size_bytes", response_size, tags)
        
        # Slow request detection
        if processing_time_ms > self.config.slow_request_threshold_ms:
            slow_tags = {**tags, "slow": "true"}
            self.collector.increment_counter("http_slow_requests_total", 1.0, slow_tags)
        
        # Update gauge metrics
        self.collector.set_gauge("http_requests_in_progress", len(self.collector.request_metrics))
    
    async def _export_metrics_periodically(self):
        """Periodically export metrics to external systems."""
        while not self._shutdown:
            try:
                await asyncio.sleep(self.config.export_interval_seconds)
                await self.collector.export_to_redis()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in periodic metrics export", error=str(e))
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process metrics middleware."""
        # Skip metrics for excluded paths
        if self._is_path_excluded(request.url.path):
            return await call_next(request)
        
        start_time = time.time()
        
        # Record concurrent requests
        if self.config.enable_performance_metrics:
            self.collector.increment_counter("http_concurrent_requests", 1.0)
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Collect metrics
            await self._collect_request_metrics(request, response, processing_time)
            
            # Add metrics headers
            if response:
                response.headers["X-Processing-Time-MS"] = str(round(processing_time * 1000, 2))
                
                # Add trace ID if available
                if hasattr(request.state, "trace_id"):
                    response.headers["X-Trace-ID"] = request.state.trace_id
            
            return response
            
        except Exception as error:
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Create error response for metrics
            error_response = JSONResponse(
                status_code=500,
                content={"error": "Internal server error"}
            )
            
            # Collect metrics for error
            await self._collect_request_metrics(request, error_response, processing_time, error)
            
            # Re-raise the error
            raise
            
        finally:
            # Decrement concurrent requests
            if self.config.enable_performance_metrics:
                self.collector.increment_counter("http_concurrent_requests", -1.0)


# Metrics endpoint handler
async def metrics_endpoint(request: Request) -> JSONResponse:
    """Endpoint to expose collected metrics."""
    try:
        # Get metrics collector from app state
        middleware = None
        for middleware_instance in request.app.user_middleware:
            if isinstance(middleware_instance.cls, type) and issubclass(middleware_instance.cls, MetricsMiddleware):
                middleware = middleware_instance
                break
        
        if not middleware:
            return JSONResponse(
                status_code=503,
                content={"error": "Metrics middleware not found"}
            )
        
        # Get metrics summary
        metrics_summary = middleware.collector.get_metrics_summary()
        
        return JSONResponse(content=metrics_summary)
        
    except Exception as e:
        logger.error("Failed to generate metrics endpoint response", error=str(e))
        return JSONResponse(
            status_code=500,
            content={"error": "Failed to collect metrics"}
        )


# Utility functions
def record_custom_metric(
    request: Request,
    name: str,
    value: float,
    metric_type: str = "gauge",
    tags: Optional[Dict[str, str]] = None
):
    """Record a custom metric from within request handlers."""
    # Try to find metrics middleware in app
    if hasattr(request.app, 'user_middleware'):
        for middleware_instance in request.app.user_middleware:
            if isinstance(middleware_instance.cls, type) and issubclass(middleware_instance.cls, MetricsMiddleware):
                collector = getattr(middleware_instance, 'collector', None)
                if collector:
                    if metric_type == "counter":
                        collector.increment_counter(name, value, tags)
                    elif metric_type == "gauge":
                        collector.set_gauge(name, value, tags)
                    elif metric_type == "histogram":
                        collector.record_histogram(name, value, tags)
                    elif metric_type == "timer":
                        collector.record_timer(name, value, tags)
                    break


# Factory function
def create_metrics_middleware(
    config: Optional[MetricsConfig] = None,
    redis_service: Optional[RedisService] = None,
    **kwargs
) -> type:
    """
    Factory function to create metrics middleware with configuration.
    
    Args:
        config: Metrics configuration
        redis_service: Redis service instance
        **kwargs: Additional configuration options
        
    Returns:
        Configured metrics middleware class
    """
    if config is None:
        config = MetricsConfig(**kwargs)
    
    class ConfiguredMetricsMiddleware(MetricsMiddleware):
        def __init__(self, app):
            super().__init__(app, config=config, redis_service=redis_service)
    
    return ConfiguredMetricsMiddleware