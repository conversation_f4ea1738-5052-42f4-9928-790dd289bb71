"""
Security middleware for the Google Ads AI Agent System.
Provides comprehensive security headers, input sanitization, and threat protection.
"""

import re
import time
import hashlib
import secrets
from typing import Dict, List, Optional, Set, Callable, Any
from urllib.parse import urlparse
from datetime import datetime, timedelta

import structlog
from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from utils.config import settings
from utils.exceptions import ValidationException, AuthenticationException
from services.redis_service import RedisService


logger = structlog.get_logger(__name__)


class SecurityConfig:
    """Security configuration constants."""
    
    # Content Security Policy
    CSP_POLICY = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com; "
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
        "font-src 'self' https://fonts.gstatic.com; "
        "img-src 'self' data: https:; "
        "connect-src 'self' https://api.openai.com https://googleads.googleapis.com; "
        "frame-ancestors 'none'; "
        "base-uri 'self'; "
        "form-action 'self'"
    )
    
    # Security headers
    SECURITY_HEADERS = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Permissions-Policy": (
            "geolocation=(), microphone=(), camera=(), "
            "fullscreen=(self), payment=(), usb=()"
        ),
        "Content-Security-Policy": CSP_POLICY,
    }
    
    # Dangerous file extensions
    DANGEROUS_EXTENSIONS = {
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
        '.jar', '.ps1', '.sh', '.php', '.asp', '.jsp', '.html', '.htm'
    }
    
    # Suspicious user agents
    SUSPICIOUS_USER_AGENTS = [
        r'.*bot.*', r'.*crawler.*', r'.*spider.*', r'.*scraper.*',
        r'.*hack.*', r'.*attack.*', r'.*exploit.*', r'.*vulnerability.*'
    ]


class ThreatDetection:
    """Advanced threat detection patterns."""
    
    # Advanced SQL injection patterns
    SQL_INJECTION_PATTERNS = [
        r"(?i)(union\s+select)",
        r"(?i)(select\s+.*\s+from)",
        r"(?i)(insert\s+into)",
        r"(?i)(delete\s+from)",
        r"(?i)(update\s+.*\s+set)",
        r"(?i)(drop\s+(table|database))",
        r"(?i)(exec\s*\()",
        r"(?i)(execute\s*\()",
        r"(?i)(sp_executesql)",
        r"(?i)(xp_cmdshell)",
        r"(?i)(;\s*(drop|delete|update|insert))",
        r"(?i)(\'\s*(or|and)\s*\'\s*=)",
        r"(?i)(\-\-\s*$)",
        r"(?i)(\/\*.*\*\/)",
        r"(?i)(0x[0-9a-f]+)",
        r"(?i)(char\s*\(\s*\d+\s*\))",
        r"(?i)(ascii\s*\(\s*)",
        r"(?i)(substring\s*\(\s*)",
        r"(?i)(waitfor\s+delay)",
    ]
    
    # Advanced XSS patterns
    XSS_PATTERNS = [
        r"(?i)<script[^>]*>.*?</script>",
        r"(?i)<iframe[^>]*>.*?</iframe>",
        r"(?i)<object[^>]*>.*?</object>",
        r"(?i)<embed[^>]*>.*?</embed>",
        r"(?i)<applet[^>]*>.*?</applet>",
        r"(?i)<meta[^>]*http-equiv[^>]*refresh",
        r"(?i)<link[^>]*rel[^>]*stylesheet",
        r"(?i)javascript:",
        r"(?i)vbscript:",
        r"(?i)data:text/html",
        r"(?i)data:application/",
        r"(?i)on\w+\s*=",
        r"(?i)(expression\s*\()",
        r"(?i)(eval\s*\()",
        r"(?i)(document\.(cookie|domain|write))",
        r"(?i)(window\.(location|open))",
        r"(?i)(<\s*svg[^>]*>)",
        r"(?i)(<\s*img[^>]*onerror)",
    ]
    
    # Command injection patterns
    COMMAND_INJECTION_PATTERNS = [
        r"(?i)(;\s*(rm|del|format|shutdown))",
        r"(?i)(\|\s*(rm|del|format|shutdown))",
        r"(?i)(&&\s*(rm|del|format|shutdown))",
        r"(?i)(cat\s+/etc/passwd)",
        r"(?i)(\/etc\/shadow)",
        r"(?i)(\/proc\/version)",
        r"(?i)(cmd\.exe)",
        r"(?i)(powershell)",
        r"(?i)(system\s*\()",
        r"(?i)(exec\s*\()",
        r"(?i)(eval\s*\()",
        r"(?i)(passthru\s*\()",
        r"(?i)(shell_exec\s*\()",
        r"(?i)(`.*`)",
        r"(?i)(\$\(.*\))",
    ]
    
    # Path traversal patterns
    PATH_TRAVERSAL_PATTERNS = [
        r"(?i)(\.\.\/){2,}",
        r"(?i)(\.\.\\){2,}",
        r"(?i)(\.\./){2,}",
        r"(?i)(\.\.\\){2,}",
        r"(?i)(\/etc\/)",
        r"(?i)(\/proc\/)",
        r"(?i)(\/sys\/)",
        r"(?i)(\/dev\/)",
        r"(?i)(c:\\windows)",
        r"(?i)(c:\\system32)",
        r"(?i)(%2e%2e%2f)",
        r"(?i)(%2e%2e%5c)",
        r"(?i)(file:\/\/)",
    ]
    
    # LDAP injection patterns
    LDAP_INJECTION_PATTERNS = [
        r"(?i)(\(\s*\|\s*\(\s*)",
        r"(?i)(\(\s*&\s*\(\s*)",
        r"(?i)(\(\s*!\s*\(\s*)",
        r"(?i)(\*\s*\)\s*\(\s*)",
        r"(?i)(objectclass\s*=)",
        r"(?i)(cn\s*=)",
        r"(?i)(uid\s*=)",
        r"(?i)(userpassword\s*=)",
    ]


class SecurityMiddleware(BaseHTTPMiddleware):
    """
    Comprehensive security middleware providing multiple layers of protection.
    """
    
    def __init__(
        self,
        app,
        redis_service: Optional[RedisService] = None,
        enable_csp: bool = True,
        enable_threat_detection: bool = True,
        enable_rate_limiting: bool = True,
        enable_ip_whitelist: bool = False,
        trusted_ips: Optional[Set[str]] = None,
        excluded_paths: Optional[Set[str]] = None,
    ):
        super().__init__(app)
        
        self.redis_service = redis_service
        self.enable_csp = enable_csp
        self.enable_threat_detection = enable_threat_detection
        self.enable_rate_limiting = enable_rate_limiting
        self.enable_ip_whitelist = enable_ip_whitelist
        self.trusted_ips = trusted_ips or set()
        
        self.excluded_paths = excluded_paths or {
            "/docs", "/redoc", "/openapi.json", "/health", "/", "/favicon.ico",
            "/static", "/assets", "/metrics"
        }
        
        # Compile threat detection patterns for performance
        self._compile_patterns()
        
        # Rate limiting configuration
        self.rate_limit_window = 60  # 1 minute
        self.rate_limit_max_requests = 100
        self.rate_limit_burst = 20
        
        # IP reputation tracking
        self.threat_score_threshold = 10
        self.ban_duration = 3600  # 1 hour
        
        logger.info(
            "Security middleware initialized",
            csp_enabled=enable_csp,
            threat_detection_enabled=enable_threat_detection,
            rate_limiting_enabled=enable_rate_limiting,
            ip_whitelist_enabled=enable_ip_whitelist,
        )
    
    def _compile_patterns(self):
        """Compile regex patterns for better performance."""
        self.sql_patterns = [re.compile(pattern) for pattern in ThreatDetection.SQL_INJECTION_PATTERNS]
        self.xss_patterns = [re.compile(pattern) for pattern in ThreatDetection.XSS_PATTERNS]
        self.cmd_patterns = [re.compile(pattern) for pattern in ThreatDetection.COMMAND_INJECTION_PATTERNS]
        self.path_patterns = [re.compile(pattern) for pattern in ThreatDetection.PATH_TRAVERSAL_PATTERNS]
        self.ldap_patterns = [re.compile(pattern) for pattern in ThreatDetection.LDAP_INJECTION_PATTERNS]
        self.ua_patterns = [re.compile(pattern) for pattern in SecurityConfig.SUSPICIOUS_USER_AGENTS]
    
    def _is_path_excluded(self, path: str) -> bool:
        """Check if path is excluded from security checks."""
        return any(path.startswith(excluded) for excluded in self.excluded_paths)
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract real client IP address."""
        # Check for forwarded headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Take the first IP in the chain (original client)
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()
        
        # Fallback to direct connection
        if hasattr(request.client, "host"):
            return request.client.host
        
        return "unknown"
    
    def _generate_nonce(self) -> str:
        """Generate a cryptographically secure nonce for CSP."""
        return secrets.token_urlsafe(16)
    
    def _add_security_headers(self, response: Response, nonce: Optional[str] = None) -> None:
        """Add security headers to response."""
        headers = SecurityConfig.SECURITY_HEADERS.copy()
        
        # Add nonce to CSP if provided
        if nonce and self.enable_csp:
            csp = headers["Content-Security-Policy"]
            csp = csp.replace("'unsafe-inline'", f"'nonce-{nonce}'")
            headers["Content-Security-Policy"] = csp
        
        # Add custom headers
        headers["X-Request-ID"] = getattr(response, "_request_id", secrets.token_hex(8))
        headers["X-Response-Time"] = str(int(time.time() * 1000))
        
        # Set headers
        for header, value in headers.items():
            response.headers[header] = value
    
    async def _check_ip_reputation(self, ip: str) -> Dict[str, Any]:
        """Check IP reputation and threat score."""
        if not self.redis_service:
            return {"allowed": True, "threat_score": 0}
        
        try:
            # Check if IP is banned
            ban_key = f"security:banned:{ip}"
            is_banned = await self.redis_service.exists(ban_key)
            
            if is_banned:
                return {
                    "allowed": False,
                    "threat_score": 100,
                    "reason": "IP banned due to previous threats"
                }
            
            # Get threat score
            score_key = f"security:threat_score:{ip}"
            threat_score = await self.redis_service.get(score_key, default=0)
            
            if isinstance(threat_score, str):
                threat_score = int(threat_score)
            
            # Check if IP is whitelisted
            if self.enable_ip_whitelist and ip in self.trusted_ips:
                return {"allowed": True, "threat_score": 0, "whitelisted": True}
            
            return {
                "allowed": threat_score < self.threat_score_threshold,
                "threat_score": threat_score
            }
            
        except Exception as e:
            logger.warning("Failed to check IP reputation", ip=ip, error=str(e))
            return {"allowed": True, "threat_score": 0}
    
    async def _update_threat_score(self, ip: str, increment: int = 1) -> None:
        """Update threat score for an IP address."""
        if not self.redis_service:
            return
        
        try:
            score_key = f"security:threat_score:{ip}"
            current_score = await self.redis_service.get(score_key, default=0)
            
            if isinstance(current_score, str):
                current_score = int(current_score)
            
            new_score = current_score + increment
            
            # Set new score with expiration (24 hours)
            await self.redis_service.set(score_key, new_score, ttl=86400)
            
            # Ban IP if threshold exceeded
            if new_score >= self.threat_score_threshold:
                ban_key = f"security:banned:{ip}"
                await self.redis_service.set(ban_key, "1", ttl=self.ban_duration)
                
                logger.warning(
                    "IP banned due to high threat score",
                    ip=ip,
                    threat_score=new_score,
                    threshold=self.threat_score_threshold,
                )
            
        except Exception as e:
            logger.warning("Failed to update threat score", ip=ip, error=str(e))
    
    def _detect_threats(self, text: str) -> List[Dict[str, Any]]:
        """Detect security threats in text."""
        if not self.enable_threat_detection or not isinstance(text, str):
            return []
        
        threats = []
        
        # SQL injection detection
        for pattern in self.sql_patterns:
            if pattern.search(text):
                threats.append({
                    "type": "sql_injection",
                    "severity": "high",
                    "pattern": pattern.pattern,
                    "match": pattern.search(text).group()[:100]
                })
        
        # XSS detection
        for pattern in self.xss_patterns:
            if pattern.search(text):
                threats.append({
                    "type": "xss",
                    "severity": "high",
                    "pattern": pattern.pattern,
                    "match": pattern.search(text).group()[:100]
                })
        
        # Command injection detection
        for pattern in self.cmd_patterns:
            if pattern.search(text):
                threats.append({
                    "type": "command_injection",
                    "severity": "critical",
                    "pattern": pattern.pattern,
                    "match": pattern.search(text).group()[:100]
                })
        
        # Path traversal detection
        for pattern in self.path_patterns:
            if pattern.search(text):
                threats.append({
                    "type": "path_traversal",
                    "severity": "medium",
                    "pattern": pattern.pattern,
                    "match": pattern.search(text).group()[:100]
                })
        
        # LDAP injection detection
        for pattern in self.ldap_patterns:
            if pattern.search(text):
                threats.append({
                    "type": "ldap_injection",
                    "severity": "medium",
                    "pattern": pattern.pattern,
                    "match": pattern.search(text).group()[:100]
                })
        
        return threats
    
    def _analyze_request_content(self, request: Request) -> List[Dict[str, Any]]:
        """Analyze request content for threats."""
        threats = []
        
        # Analyze URL and query parameters
        full_url = str(request.url)
        url_threats = self._detect_threats(full_url)
        threats.extend(url_threats)
        
        # Analyze headers
        for header_name, header_value in request.headers.items():
            header_threats = self._detect_threats(f"{header_name}:{header_value}")
            threats.extend(header_threats)
        
        # Analyze user agent
        user_agent = request.headers.get("user-agent", "")
        for pattern in self.ua_patterns:
            if pattern.search(user_agent):
                threats.append({
                    "type": "suspicious_user_agent",
                    "severity": "low",
                    "pattern": pattern.pattern,
                    "match": user_agent[:100]
                })
        
        return threats
    
    async def _validate_file_upload(self, request: Request) -> None:
        """Validate file uploads for security threats."""
        content_type = request.headers.get("content-type", "")
        
        if "multipart/form-data" in content_type:
            # Check for dangerous file extensions in the request
            # This is a basic check - full implementation would parse the multipart data
            
            # Get content length
            content_length = request.headers.get("content-length")
            if content_length:
                size = int(content_length)
                max_size = 50 * 1024 * 1024  # 50MB
                
                if size > max_size:
                    raise ValidationException(
                        f"File too large: {size} bytes (max: {max_size})"
                    )
    
    async def _rate_limit_check(self, request: Request, ip: str) -> None:
        """Perform advanced rate limiting."""
        if not self.enable_rate_limiting or not self.redis_service:
            return
        
        current_time = int(time.time())
        
        # Multiple rate limiting windows
        checks = [
            ("minute", 60, self.rate_limit_max_requests),
            ("hour", 3600, self.rate_limit_max_requests * 60),
            ("day", 86400, self.rate_limit_max_requests * 60 * 24),
        ]
        
        for window_name, window_seconds, limit in checks:
            key = f"rate_limit:{window_name}:{ip}:{current_time // window_seconds}"
            
            try:
                # Increment counter
                count = await self.redis_service.get(key, default=0)
                if isinstance(count, str):
                    count = int(count)
                
                count += 1
                
                # Set with expiration
                await self.redis_service.set(key, count, ttl=window_seconds)
                
                if count > limit:
                    # Update threat score for rate limiting
                    await self._update_threat_score(ip, increment=2)
                    
                    raise HTTPException(
                        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                        detail=f"Rate limit exceeded: {count}/{limit} requests per {window_name}",
                        headers={"Retry-After": str(window_seconds)}
                    )
                    
            except HTTPException:
                raise
            except Exception as e:
                logger.warning("Rate limiting check failed", error=str(e))
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process security middleware."""
        start_time = time.time()
        
        # Skip security checks for excluded paths
        if self._is_path_excluded(request.url.path):
            response = await call_next(request)
            self._add_security_headers(response)
            return response
        
        # Get client IP
        client_ip = self._get_client_ip(request)
        
        # Generate request ID for tracing
        request_id = secrets.token_hex(8)
        request.state.request_id = request_id
        
        try:
            # Check IP reputation
            ip_reputation = await self._check_ip_reputation(client_ip)
            if not ip_reputation["allowed"]:
                logger.warning(
                    "Request blocked due to IP reputation",
                    ip=client_ip,
                    threat_score=ip_reputation["threat_score"],
                    reason=ip_reputation.get("reason", "High threat score"),
                )
                
                response = JSONResponse(
                    status_code=status.HTTP_403_FORBIDDEN,
                    content={
                        "error": "Access denied",
                        "code": "IP_BLOCKED",
                        "request_id": request_id
                    }
                )
                self._add_security_headers(response)
                return response
            
            # Rate limiting
            await self._rate_limit_check(request, client_ip)
            
            # Validate file uploads
            await self._validate_file_upload(request)
            
            # Threat detection
            threats = self._analyze_request_content(request)
            
            if threats:
                # Log threats
                logger.warning(
                    "Security threats detected",
                    ip=client_ip,
                    threats=threats,
                    path=request.url.path,
                    user_agent=request.headers.get("user-agent", ""),
                )
                
                # Update threat score based on severity
                threat_increment = 0
                for threat in threats:
                    if threat["severity"] == "critical":
                        threat_increment += 5
                    elif threat["severity"] == "high":
                        threat_increment += 3
                    elif threat["severity"] == "medium":
                        threat_increment += 2
                    else:
                        threat_increment += 1
                
                await self._update_threat_score(client_ip, increment=threat_increment)
                
                # Block critical threats immediately
                critical_threats = [t for t in threats if t["severity"] == "critical"]
                if critical_threats:
                    response = JSONResponse(
                        status_code=status.HTTP_403_FORBIDDEN,
                        content={
                            "error": "Security threat detected",
                            "code": "THREAT_DETECTED",
                            "request_id": request_id
                        }
                    )
                    self._add_security_headers(response)
                    return response
            
            # Process request
            response = await call_next(request)
            
            # Add security headers
            nonce = self._generate_nonce() if self.enable_csp else None
            self._add_security_headers(response, nonce)
            
            # Set request ID in response
            response.headers["X-Request-ID"] = request_id
            
            # Log successful request
            processing_time = (time.time() - start_time) * 1000
            logger.info(
                "Request processed successfully",
                ip=client_ip,
                path=request.url.path,
                method=request.method,
                status_code=response.status_code,
                processing_time_ms=processing_time,
                request_id=request_id,
            )
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                "Security middleware error",
                ip=client_ip,
                path=request.url.path,
                error=str(e),
                request_id=request_id,
            )
            
            response = JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": "Internal server error",
                    "code": "SECURITY_MIDDLEWARE_ERROR",
                    "request_id": request_id
                }
            )
            self._add_security_headers(response)
            return response


def create_security_middleware(
    redis_service: Optional[RedisService] = None,
    **kwargs
) -> type:
    """
    Factory function to create security middleware with configuration.
    
    Args:
        redis_service: Redis service instance
        **kwargs: Additional configuration options
        
    Returns:
        Configured security middleware class
    """
    class ConfiguredSecurityMiddleware(SecurityMiddleware):
        def __init__(self, app):
            super().__init__(app, redis_service=redis_service, **kwargs)
    
    return ConfiguredSecurityMiddleware