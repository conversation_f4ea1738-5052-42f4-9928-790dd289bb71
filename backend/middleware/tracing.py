"""
Request tracing and correlation ID middleware for the Google Ads AI Agent System.
Provides distributed tracing, request correlation, and performance monitoring.
"""

import time
import uuid
import json
import asyncio
from typing import Dict, List, Optional, Set, Callable, Any, Union
from datetime import datetime, timezone
from contextlib import asynccontextmanager
from dataclasses import dataclass, asdict

import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from utils.config import settings
from services.redis_service import RedisService


logger = structlog.get_logger(__name__)


@dataclass
class TraceSpan:
    """Represents a trace span for distributed tracing."""
    span_id: str
    trace_id: str
    parent_span_id: Optional[str]
    operation_name: str
    start_time: float
    end_time: Optional[float] = None
    duration_ms: Optional[float] = None
    tags: Dict[str, Any] = None
    logs: List[Dict[str, Any]] = None
    status: str = "ok"  # ok, error, timeout
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = {}
        if self.logs is None:
            self.logs = []
    
    def finish(self, status: str = "ok"):
        """Finish the span and calculate duration."""
        self.end_time = time.time()
        self.duration_ms = (self.end_time - self.start_time) * 1000
        self.status = status
    
    def add_tag(self, key: str, value: Any):
        """Add a tag to the span."""
        self.tags[key] = value
    
    def add_log(self, message: str, **fields):
        """Add a log entry to the span."""
        log_entry = {
            "timestamp": time.time(),
            "message": message,
            **fields
        }
        self.logs.append(log_entry)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert span to dictionary."""
        return asdict(self)


class TraceContext:
    """Context for managing distributed tracing."""
    
    def __init__(self, trace_id: Optional[str] = None, span_id: Optional[str] = None):
        self.trace_id = trace_id or str(uuid.uuid4())
        self.span_id = span_id or str(uuid.uuid4())
        self.spans: Dict[str, TraceSpan] = {}
        self.active_span: Optional[TraceSpan] = None
        self.baggage: Dict[str, str] = {}
    
    def create_span(
        self,
        operation_name: str,
        parent_span_id: Optional[str] = None,
        tags: Optional[Dict[str, Any]] = None
    ) -> TraceSpan:
        """Create a new span."""
        span = TraceSpan(
            span_id=str(uuid.uuid4()),
            trace_id=self.trace_id,
            parent_span_id=parent_span_id or (self.active_span.span_id if self.active_span else None),
            operation_name=operation_name,
            start_time=time.time(),
            tags=tags or {}
        )
        
        self.spans[span.span_id] = span
        return span
    
    def set_active_span(self, span: TraceSpan):
        """Set the active span."""
        self.active_span = span
    
    def get_active_span(self) -> Optional[TraceSpan]:
        """Get the currently active span."""
        return self.active_span
    
    def add_baggage(self, key: str, value: str):
        """Add baggage item (propagated across service boundaries)."""
        self.baggage[key] = value
    
    def get_baggage(self, key: str) -> Optional[str]:
        """Get baggage item."""
        return self.baggage.get(key)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert trace context to dictionary."""
        return {
            "trace_id": self.trace_id,
            "span_id": self.span_id,
            "spans": {span_id: span.to_dict() for span_id, span in self.spans.items()},
            "baggage": self.baggage,
        }


class TracingConfig:
    """Configuration for tracing middleware."""
    
    def __init__(
        self,
        enable_tracing: bool = True,
        enable_performance_monitoring: bool = True,
        enable_distributed_tracing: bool = True,
        sample_rate: float = 1.0,  # 0.0 to 1.0
        max_spans_per_trace: int = 100,
        span_export_batch_size: int = 50,
        span_export_timeout: float = 30.0,
        excluded_paths: Optional[Set[str]] = None,
        trace_header_name: str = "X-Trace-ID",
        span_header_name: str = "X-Span-ID",
        baggage_header_name: str = "X-Baggage",
        store_traces_in_redis: bool = True,
        trace_retention_hours: int = 24,
    ):
        self.enable_tracing = enable_tracing
        self.enable_performance_monitoring = enable_performance_monitoring
        self.enable_distributed_tracing = enable_distributed_tracing
        self.sample_rate = sample_rate
        self.max_spans_per_trace = max_spans_per_trace
        self.span_export_batch_size = span_export_batch_size
        self.span_export_timeout = span_export_timeout
        self.trace_header_name = trace_header_name
        self.span_header_name = span_header_name
        self.baggage_header_name = baggage_header_name
        self.store_traces_in_redis = store_traces_in_redis
        self.trace_retention_hours = trace_retention_hours
        
        self.excluded_paths = excluded_paths or {
            "/health", "/metrics", "/favicon.ico", "/static", "/docs", "/redoc"
        }


class SpanExporter:
    """Exports spans to external tracing systems."""
    
    def __init__(
        self,
        redis_service: Optional[RedisService] = None,
        batch_size: int = 50,
        timeout: float = 30.0,
    ):
        self.redis_service = redis_service
        self.batch_size = batch_size
        self.timeout = timeout
        self.span_buffer: List[TraceSpan] = []
        self._lock = asyncio.Lock()
        
    async def export_span(self, span: TraceSpan):
        """Export a single span."""
        async with self._lock:
            self.span_buffer.append(span)
            
            if len(self.span_buffer) >= self.batch_size:
                await self._flush_spans()
    
    async def export_spans(self, spans: List[TraceSpan]):
        """Export multiple spans."""
        for span in spans:
            await self.export_span(span)
    
    async def _flush_spans(self):
        """Flush buffered spans to external systems."""
        if not self.span_buffer:
            return
        
        spans_to_export = self.span_buffer.copy()
        self.span_buffer.clear()
        
        try:
            # Export to Redis for storage and analytics
            if self.redis_service:
                await self._export_to_redis(spans_to_export)
            
            # Export to external tracing systems (placeholder)
            await self._export_to_external_systems(spans_to_export)
            
        except Exception as e:
            logger.error("Failed to export spans", error=str(e))
    
    async def _export_to_redis(self, spans: List[TraceSpan]):
        """Export spans to Redis."""
        try:
            # Group spans by trace ID
            traces = {}
            for span in spans:
                if span.trace_id not in traces:
                    traces[span.trace_id] = []
                traces[span.trace_id].append(span.to_dict())
            
            # Store each trace
            for trace_id, trace_spans in traces.items():
                key = f"trace:{trace_id}"
                
                # Get existing trace data
                existing_trace = await self.redis_service.get(key, default={})
                
                # Add new spans
                if "spans" not in existing_trace:
                    existing_trace["spans"] = []
                
                existing_trace["spans"].extend(trace_spans)
                existing_trace["updated_at"] = datetime.now(timezone.utc).isoformat()
                
                # Store with TTL
                await self.redis_service.set(key, existing_trace, ttl=24 * 3600)  # 24 hours
            
            logger.debug(f"Exported {len(spans)} spans to Redis")
            
        except Exception as e:
            logger.error("Failed to export spans to Redis", error=str(e))
    
    async def _export_to_external_systems(self, spans: List[TraceSpan]) -> None:
        """Export spans to external tracing systems (e.g., Jaeger, Zipkin)."""
        # Placeholder for external system integration
        # This could be extended to support:
        # - Jaeger
        # - Zipkin
        # - OpenTelemetry Collector
        # - DataDog APM
        # - New Relic
        
        if settings.PHOENIX_COLLECTOR_ENDPOINT:
            await self._export_to_phoenix(spans)
    
    async def _export_to_phoenix(self, spans: List[TraceSpan]) -> None:
        """Export spans to Phoenix tracing system."""
        try:
            import httpx
            
            # Convert spans to Phoenix format
            phoenix_spans = []
            for span in spans:
                phoenix_span = {
                    "traceID": span.trace_id,
                    "spanID": span.span_id,
                    "parentSpanID": span.parent_span_id,
                    "operationName": span.operation_name,
                    "startTime": int(span.start_time * 1000000),  # microseconds
                    "duration": int((span.duration_ms or 0) * 1000),  # microseconds
                    "tags": span.tags,
                    "logs": span.logs,
                    "process": {
                        "serviceName": settings.APP_NAME,
                        "tags": {
                            "version": settings.VERSION,
                            "environment": settings.ENVIRONMENT,
                        }
                    }
                }
                phoenix_spans.append(phoenix_span)
            
            # Send to Phoenix collector
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{settings.PHOENIX_COLLECTOR_ENDPOINT}/api/traces",
                    json={"batch": phoenix_spans},
                    timeout=self.timeout
                )
                response.raise_for_status()
            
            logger.debug(f"Exported {len(spans)} spans to Phoenix")
            
        except Exception as e:
            logger.warning("Failed to export spans to Phoenix", error=str(e))
    
    async def flush(self) -> None:
        """Flush all buffered spans."""
        await self._flush_spans()


class RequestTracingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for distributed tracing and request correlation.
    Provides comprehensive tracing capabilities with performance monitoring.
    """
    
    def __init__(
        self,
        app,
        config: Optional[TracingConfig] = None,
        redis_service: Optional[RedisService] = None,
    ):
        super().__init__(app)
        
        self.config = config or TracingConfig()
        self.redis_service = redis_service
        
        # Initialize span exporter
        self.span_exporter = SpanExporter(
            redis_service=redis_service,
            batch_size=self.config.span_export_batch_size,
            timeout=self.config.span_export_timeout,
        )
        
        logger.info(
            "Request tracing middleware initialized",
            sample_rate=self.config.sample_rate,
            distributed_tracing=self.config.enable_distributed_tracing,
        )
    
    def _is_path_excluded(self, path: str) -> bool:
        """Check if path is excluded from tracing."""
        return any(path.startswith(excluded) for excluded in self.config.excluded_paths)
    
    def _should_sample(self) -> bool:
        """Determine if request should be sampled for tracing."""
        import random
        return random.random() < self.config.sample_rate
    
    def _extract_trace_context(self, request: Request) -> TraceContext:
        """Extract trace context from request headers."""
        trace_id = request.headers.get(self.config.trace_header_name)
        span_id = request.headers.get(self.config.span_header_name)
        
        context = TraceContext(trace_id=trace_id, span_id=span_id)
        
        # Extract baggage
        baggage_header = request.headers.get(self.config.baggage_header_name)
        if baggage_header:
            try:
                baggage_items = baggage_header.split(",")
                for item in baggage_items:
                    if "=" in item:
                        key, value = item.split("=", 1)
                        context.add_baggage(key.strip(), value.strip())
            except Exception as e:
                logger.warning("Failed to parse baggage header", error=str(e))
        
        return context
    
    def _inject_trace_headers(self, response: Response, context: TraceContext):
        """Inject trace headers into response."""
        response.headers[self.config.trace_header_name] = context.trace_id
        
        if context.active_span:
            response.headers[self.config.span_header_name] = context.active_span.span_id
        
        # Inject baggage
        if context.baggage:
            baggage_items = [f"{k}={v}" for k, v in context.baggage.items()]
            response.headers[self.config.baggage_header_name] = ",".join(baggage_items)
    
    async def _create_request_span(
        self,
        context: TraceContext,
        request: Request
    ) -> TraceSpan:
        """Create a span for the incoming request."""
        span = context.create_span(
            operation_name=f"{request.method} {request.url.path}",
            tags={
                "http.method": request.method,
                "http.url": str(request.url),
                "http.path": request.url.path,
                "http.query": str(request.url.query) if request.url.query else None,
                "http.user_agent": request.headers.get("user-agent"),
                "http.remote_addr": getattr(request.client, "host", None) if request.client else None,
                "component": "http",
                "span.kind": "server",
            }
        )
        
        context.set_active_span(span)
        return span
    
    async def _finish_request_span(
        self,
        span: TraceSpan,
        response: Response,
        error: Optional[Exception] = None
    ):
        """Finish the request span with response information."""
        # Add response tags
        if response:
            span.add_tag("http.status_code", response.status_code)
            span.add_tag("http.response_size", len(getattr(response, "body", b"")))
        
        # Handle errors
        if error:
            span.add_tag("error", True)
            span.add_tag("error.type", type(error).__name__)
            span.add_tag("error.message", str(error))
            span.add_log("error", error=str(error), error_type=type(error).__name__)
            span.finish("error")
        else:
            # Determine status based on response code
            if response and response.status_code >= 400:
                span.finish("error")
            else:
                span.finish("ok")
        
        # Export span
        if self.config.enable_tracing:
            await self.span_exporter.export_span(span)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process tracing middleware."""
        # Skip tracing for excluded paths
        if self._is_path_excluded(request.url.path):
            return await call_next(request)
        
        # Check sampling
        if not self._should_sample():
            return await call_next(request)
        
        # Extract or create trace context
        context = self._extract_trace_context(request)
        
        # Store context in request state
        request.state.trace_context = context
        request.state.trace_id = context.trace_id
        
        # Create request span
        request_span = await self._create_request_span(context, request)
        
        try:
            # Add custom baggage
            user_id = getattr(request.state, "user_id", None)
            if user_id:
                context.add_baggage("user.id", str(user_id))
            
            # Process request
            response = await call_next(request)
            
            # Finish span
            await self._finish_request_span(request_span, response)
            
            # Inject trace headers
            if self.config.enable_distributed_tracing:
                self._inject_trace_headers(response, context)
            
            return response
            
        except Exception as error:
            # Finish span with error
            await self._finish_request_span(request_span, None, error)
            
            # Re-raise the error
            raise
    
    async def shutdown(self):
        """Shutdown the tracing middleware."""
        if self.span_exporter:
            await self.span_exporter.flush()


# Context managers and utilities for custom tracing
@asynccontextmanager
async def trace_operation(
    operation_name: str,
    request: Optional[Request] = None,
    **tags
):
    """Context manager for tracing operations."""
    # Get trace context from request if available
    if request and hasattr(request.state, "trace_context"):
        context = request.state.trace_context
    else:
        context = TraceContext()
    
    # Create span
    span = context.create_span(operation_name, tags=tags)
    original_active = context.get_active_span()
    context.set_active_span(span)
    
    try:
        yield span
        span.finish("ok")
    except Exception as error:
        span.add_tag("error", True)
        span.add_tag("error.message", str(error))
        span.finish("error")
        raise
    finally:
        context.set_active_span(original_active)


def get_current_trace_context(request: Request) -> Optional[TraceContext]:
    """Get the current trace context from request."""
    return getattr(request.state, "trace_context", None)


def get_current_span(request: Request) -> Optional[TraceSpan]:
    """Get the current active span from request."""
    context = get_current_trace_context(request)
    return context.get_active_span() if context else None


def add_span_tag(request: Request, key: str, value: Any):
    """Add a tag to the current span."""
    span = get_current_span(request)
    if span:
        span.add_tag(key, value)


def add_span_log(request: Request, message: str, **fields):
    """Add a log entry to the current span."""
    span = get_current_span(request)
    if span:
        span.add_log(message, **fields)


# Factory function
def create_tracing_middleware(
    config: Optional[TracingConfig] = None,
    redis_service: Optional[RedisService] = None,
    **kwargs
) -> type:
    """
    Factory function to create tracing middleware with configuration.
    
    Args:
        config: Tracing configuration
        redis_service: Redis service instance
        **kwargs: Additional configuration options
        
    Returns:
        Configured tracing middleware class
    """
    if config is None:
        config = TracingConfig(**kwargs)
    
    class ConfiguredTracingMiddleware(RequestTracingMiddleware):
        def __init__(self, app):
            super().__init__(app, config=config, redis_service=redis_service)
    
    return ConfiguredTracingMiddleware