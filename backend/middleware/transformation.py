"""
Request/Response transformation middleware for the Google Ads AI Agent System.
Provides data transformation, response formatting, and API versioning support.
"""

import json
import time
import uuid
from typing import Dict, List, Optional, Set, Callable, Any, Union
from datetime import datetime, timezone
from decimal import Decimal

import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from pydantic import BaseModel, ValidationError

from utils.config import settings
from utils.exceptions import ValidationException


logger = structlog.get_logger(__name__)


class APIVersion:
    """API versioning constants."""
    V1 = "v1"
    V2 = "v2"
    DEFAULT = V1
    SUPPORTED_VERSIONS = {V1, V2}


class TransformationConfig(BaseModel):
    """Configuration for transformation middleware."""
    enable_response_transformation: bool = True
    enable_request_transformation: bool = True
    enable_data_serialization: bool = True
    enable_field_renaming: bool = True
    enable_data_filtering: bool = True
    default_api_version: str = APIVersion.DEFAULT
    strip_null_values: bool = True
    convert_decimals: bool = True
    format_timestamps: bool = True
    include_metadata: bool = True


class ResponseTransformationMiddleware(BaseHTTPMiddleware):
    """
    Middleware for transforming API responses based on client requirements,
    API versioning, and data formatting standards.
    """
    
    def __init__(
        self,
        app,
        config: Optional[TransformationConfig] = None,
        excluded_paths: Optional[Set[str]] = None,
    ):
        super().__init__(app)
        
        self.config = config or TransformationConfig()
        self.excluded_paths = excluded_paths or {
            "/docs", "/redoc", "/openapi.json", "/health", "/", "/favicon.ico",
            "/static", "/assets", "/metrics"
        }
        
        # Field mapping for different API versions
        self.field_mappings = {
            "v1": {
                # Google Ads API v1 field mappings
                "campaign_id": "id",
                "campaign_name": "name",
                "campaign_status": "status",
                "daily_budget": "budget_amount",
                "budget_currency": "currency",
                "click_through_rate": "ctr",
                "cost_per_click": "cpc",
                "return_on_ad_spend": "roas",
            },
            "v2": {
                # Google Ads API v2 field mappings
                "id": "campaign_id",
                "name": "campaign_name", 
                "status": "campaign_status",
                "budget_amount": "daily_budget",
                "currency": "budget_currency",
                "ctr": "click_through_rate",
                "cpc": "cost_per_click",
                "roas": "return_on_ad_spend",
            }
        }
        
        logger.info(
            "Response transformation middleware initialized",
            config=self.config.dict(),
        )
    
    def _is_path_excluded(self, path: str) -> bool:
        """Check if path is excluded from transformation."""
        return any(path.startswith(excluded) for excluded in self.excluded_paths)
    
    def _get_api_version(self, request: Request) -> str:
        """Extract API version from request."""
        # Check header first
        version = request.headers.get("X-API-Version")
        if version and version in APIVersion.SUPPORTED_VERSIONS:
            return version
        
        # Check path
        path_parts = request.url.path.strip("/").split("/")
        if len(path_parts) > 1 and path_parts[0] == "api":
            version = path_parts[1]
            if version in APIVersion.SUPPORTED_VERSIONS:
                return version
        
        # Check query parameter
        version = request.query_params.get("version")
        if version and version in APIVersion.SUPPORTED_VERSIONS:
            return version
        
        return self.config.default_api_version
    
    def _get_client_preferences(self, request: Request) -> Dict[str, Any]:
        """Extract client preferences from request headers."""
        preferences = {
            "include_metadata": self.config.include_metadata,
            "strip_nulls": self.config.strip_null_values,
            "format_timestamps": self.config.format_timestamps,
            "convert_decimals": self.config.convert_decimals,
        }
        
        # Override with client headers
        if "X-Include-Metadata" in request.headers:
            preferences["include_metadata"] = request.headers["X-Include-Metadata"].lower() == "true"
        
        if "X-Strip-Nulls" in request.headers:
            preferences["strip_nulls"] = request.headers["X-Strip-Nulls"].lower() == "true"
        
        if "X-Format-Timestamps" in request.headers:
            preferences["format_timestamps"] = request.headers["X-Format-Timestamps"].lower() == "true"
        
        return preferences
    
    def _serialize_value(self, value: Any, preferences: Dict[str, Any]) -> Any:
        """Serialize a single value based on client preferences."""
        # Handle None values
        if value is None:
            return None if not preferences["strip_nulls"] else ...  # Sentinel for removal
        
        # Handle datetime objects
        if isinstance(value, datetime):
            if preferences["format_timestamps"]:
                return value.isoformat()
            return value.timestamp()
        
        # Handle Decimal objects
        if isinstance(value, Decimal):
            if preferences["convert_decimals"]:
                return float(value)
            return str(value)
        
        # Handle UUID objects
        if hasattr(value, '__dict__') and hasattr(value, 'hex'):  # UUID-like
            return str(value)
        
        # Handle Pydantic models
        if hasattr(value, 'dict'):
            return self._transform_dict(value.dict(), preferences)
        
        # Handle dictionaries
        if isinstance(value, dict):
            return self._transform_dict(value, preferences)
        
        # Handle lists
        if isinstance(value, (list, tuple)):
            return [self._serialize_value(item, preferences) for item in value]
        
        # Handle sets
        if isinstance(value, set):
            return list(value)
        
        return value
    
    def _transform_dict(self, data: Dict[str, Any], preferences: Dict[str, Any]) -> Dict[str, Any]:
        """Transform a dictionary based on client preferences."""
        if not isinstance(data, dict):
            return data
        
        transformed = {}
        
        for key, value in data.items():
            # Serialize the value
            serialized_value = self._serialize_value(value, preferences)
            
            # Skip null values if requested
            if serialized_value is ... and preferences["strip_nulls"]:
                continue
            
            transformed[key] = serialized_value
        
        return transformed
    
    def _apply_field_mapping(
        self,
        data: Dict[str, Any],
        api_version: str,
        reverse: bool = False
    ) -> Dict[str, Any]:
        """Apply field mapping for API version compatibility."""
        if not self.config.enable_field_renaming:
            return data
        
        mapping = self.field_mappings.get(api_version, {})
        if reverse:
            # Reverse the mapping for requests
            mapping = {v: k for k, v in mapping.items()}
        
        if not mapping:
            return data
        
        transformed = {}
        for key, value in data.items():
            new_key = mapping.get(key, key)
            transformed[new_key] = value
        
        return transformed
    
    def _add_response_metadata(
        self,
        data: Any,
        request: Request,
        api_version: str,
        processing_time_ms: float,
    ) -> Dict[str, Any]:
        """Add metadata to response."""
        if not self.config.include_metadata:
            return data
        
        metadata = {
            "api_version": api_version,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "processing_time_ms": round(processing_time_ms, 2),
            "request_id": getattr(request.state, "request_id", str(uuid.uuid4())),
        }
        
        # Add pagination metadata if present
        if isinstance(data, dict) and "items" in data:
            pagination_fields = ["total", "page", "page_size", "total_pages", "has_next", "has_previous"]
            pagination_data = {field: data.get(field) for field in pagination_fields if field in data}
            if pagination_data:
                metadata["pagination"] = pagination_data
        
        # Wrap data with metadata
        return {
            "data": data,
            "meta": metadata,
        }
    
    def _filter_response_data(
        self,
        data: Any,
        request: Request,
        api_version: str,
    ) -> Any:
        """Filter response data based on query parameters and permissions."""
        if not self.config.enable_data_filtering:
            return data
        
        # Check for field selection
        fields_param = request.query_params.get("fields")
        if fields_param and isinstance(data, dict):
            requested_fields = {field.strip() for field in fields_param.split(",")}
            
            # Filter top-level fields
            filtered_data = {
                key: value for key, value in data.items()
                if key in requested_fields or key in ["meta", "metadata"]
            }
            
            # Handle nested data (like items in paginated responses)
            if "items" in data and "items" in requested_fields:
                filtered_data["items"] = data["items"]
            elif "data" in data and isinstance(data["data"], list):
                # Filter fields in list items
                filtered_items = []
                for item in data["data"]:
                    if isinstance(item, dict):
                        filtered_item = {k: v for k, v in item.items() if k in requested_fields}
                        filtered_items.append(filtered_item)
                    else:
                        filtered_items.append(item)
                filtered_data["data"] = filtered_items
            
            return filtered_data
        
        return data
    
    def _transform_error_response(
        self,
        error_data: Dict[str, Any],
        api_version: str,
    ) -> Dict[str, Any]:
        """Transform error responses for consistency."""
        # Standardize error format
        if "detail" in error_data:
            error_data["message"] = error_data.pop("detail")
        
        # Add error code if missing
        if "code" not in error_data:
            error_data["code"] = "UNKNOWN_ERROR"
        
        # Add API version
        error_data["api_version"] = api_version
        
        return error_data
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process transformation middleware."""
        start_time = time.time()
        
        # Skip transformation for excluded paths
        if self._is_path_excluded(request.url.path):
            return await call_next(request)
        
        # Get API version and client preferences
        api_version = self._get_api_version(request)
        preferences = self._get_client_preferences(request)
        
        # Store in request state for downstream use
        request.state.api_version = api_version
        request.state.client_preferences = preferences
        
        try:
            # Process the request
            response = await call_next(request)
            
            # Only transform JSON responses
            if not response.headers.get("content-type", "").startswith("application/json"):
                return response
            
            # Get response body
            response_body = b""
            async for chunk in response.body_iterator:
                response_body += chunk
            
            if not response_body:
                return response
            
            # Parse JSON
            try:
                data = json.loads(response_body.decode())
            except json.JSONDecodeError:
                # Return original response if not valid JSON
                return Response(
                    content=response_body,
                    status_code=response.status_code,
                    headers=response.headers,
                )
            
            # Transform based on response type
            processing_time_ms = (time.time() - start_time) * 1000
            
            if 400 <= response.status_code < 600:
                # Transform error response
                transformed_data = self._transform_error_response(data, api_version)
            else:
                # Transform success response
                if self.config.enable_response_transformation:
                    # Apply field mapping
                    data = self._apply_field_mapping(data, api_version)
                    
                    # Apply data filtering
                    data = self._filter_response_data(data, request, api_version)
                    
                    # Transform data types
                    data = self._transform_dict(data, preferences)
                    
                    # Add metadata
                    transformed_data = self._add_response_metadata(
                        data, request, api_version, processing_time_ms
                    )
                else:
                    transformed_data = data
            
            # Create new response
            new_response = JSONResponse(
                content=transformed_data,
                status_code=response.status_code,
            )
            
            # Copy headers
            for key, value in response.headers.items():
                if key.lower() not in ["content-length", "content-encoding"]:
                    new_response.headers[key] = value
            
            # Add transformation headers
            new_response.headers["X-API-Version"] = api_version
            new_response.headers["X-Response-Transformed"] = "true"
            new_response.headers["X-Processing-Time-MS"] = str(round(processing_time_ms, 2))
            
            logger.debug(
                "Response transformed successfully",
                api_version=api_version,
                processing_time_ms=processing_time_ms,
                response_size=len(json.dumps(transformed_data)),
            )
            
            return new_response
            
        except Exception as e:
            logger.error(
                "Response transformation failed",
                error=str(e),
                path=request.url.path,
                api_version=api_version,
            )
            
            # Return original response on transformation failure
            return await call_next(request)


class RequestTransformationMiddleware(BaseHTTPMiddleware):
    """
    Middleware for transforming incoming requests for backward compatibility
    and data normalization.
    """
    
    def __init__(
        self,
        app,
        config: Optional[TransformationConfig] = None,
        excluded_paths: Optional[Set[str]] = None,
    ):
        super().__init__(app)
        
        self.config = config or TransformationConfig()
        self.excluded_paths = excluded_paths or {
            "/docs", "/redoc", "/openapi.json", "/health", "/", "/favicon.ico",
            "/static", "/assets", "/metrics"
        }
        
        # Response transformation middleware instance for field mappings
        self.response_transformer = ResponseTransformationMiddleware(None, config)
        
        logger.info("Request transformation middleware initialized")
    
    def _is_path_excluded(self, path: str) -> bool:
        """Check if path is excluded from transformation."""
        return any(path.startswith(excluded) for excluded in self.excluded_paths)
    
    async def _transform_request_body(
        self,
        request: Request,
        api_version: str,
    ) -> Optional[bytes]:
        """Transform request body for backward compatibility."""
        if request.method not in ["POST", "PUT", "PATCH"]:
            return None
        
        # Get original body
        body = await request.body()
        if not body:
            return None
        
        # Check content type
        content_type = request.headers.get("content-type", "")
        if not content_type.startswith("application/json"):
            return body
        
        try:
            # Parse JSON
            data = json.loads(body.decode())
            
            # Apply reverse field mapping for requests
            if self.config.enable_field_renaming:
                data = self.response_transformer._apply_field_mapping(
                    data, api_version, reverse=True
                )
            
            # Transform data types for consistency
            preferences = {
                "strip_nulls": False,  # Don't strip nulls in requests
                "format_timestamps": False,  # Keep original timestamp format
                "convert_decimals": False,  # Keep decimals as strings
                "include_metadata": False,  # No metadata in requests
            }
            
            transformed_data = self.response_transformer._transform_dict(data, preferences)
            
            # Return transformed body
            return json.dumps(transformed_data).encode()
            
        except (json.JSONDecodeError, Exception) as e:
            logger.warning(
                "Request body transformation failed",
                error=str(e),
                path=request.url.path,
            )
            return body
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request transformation middleware."""
        # Skip transformation for excluded paths
        if self._is_path_excluded(request.url.path):
            return await call_next(request)
        
        if not self.config.enable_request_transformation:
            return await call_next(request)
        
        try:
            # Get API version
            api_version = getattr(request.state, "api_version", APIVersion.DEFAULT)
            
            # Transform request body if needed
            transformed_body = await self._transform_request_body(request, api_version)
            
            if transformed_body is not None:
                # Create new request with transformed body
                async def receive():
                    return {
                        "type": "http.request",
                        "body": transformed_body,
                        "more_body": False,
                    }
                
                # Update request's receive method
                original_receive = request.receive
                request._receive = receive
                
                logger.debug(
                    "Request body transformed",
                    api_version=api_version,
                    original_size=len(await request.body()) if hasattr(request, '_body') else 0,
                    transformed_size=len(transformed_body),
                )
            
            return await call_next(request)
            
        except Exception as e:
            logger.error(
                "Request transformation failed",
                error=str(e),
                path=request.url.path,
            )
            return await call_next(request)


def create_transformation_middleware(
    config: Optional[TransformationConfig] = None,
    **kwargs
) -> type:
    """
    Factory function to create transformation middleware with configuration.
    
    Args:
        config: Transformation configuration
        **kwargs: Additional configuration options
        
    Returns:
        Configured transformation middleware class
    """
    effective_config = config or TransformationConfig(**kwargs)
    
    class ConfiguredTransformationMiddleware(ResponseTransformationMiddleware):
        def __init__(self, app):
            super().__init__(app, config=effective_config)
    
    return ConfiguredTransformationMiddleware