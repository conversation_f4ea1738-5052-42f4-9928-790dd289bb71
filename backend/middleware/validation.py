"""
Validation middleware for the AiLex Ad Agent System.
Provides comprehensive request/response validation, rate limiting, and request sanitization.
"""

import json
import time
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Callable, Any, Union
from urllib.parse import parse_qs, unquote

import structlog
from fastapi import Request, Response, HTTPException, status
from pydantic import BaseModel, ValidationError
from starlette.middleware.base import BaseHTTPMiddleware
import html

from utils.config import settings
from utils.exceptions import ValidationException, RateLimitException
from services.redis_service import RedisService


logger = structlog.get_logger(__name__)


class RequestMetrics(BaseModel):
    """Request metrics for monitoring and rate limiting."""
    ip_address: str
    user_agent: Optional[str] = None
    endpoint: str
    method: str
    timestamp: datetime
    response_time_ms: Optional[float] = None
    status_code: Optional[int] = None
    request_size_bytes: Optional[int] = None
    response_size_bytes: Optional[int] = None


class RateLimitConfig(BaseModel):
    """Rate limit configuration."""
    requests_per_minute: int = 100
    requests_per_hour: int = 1000
    requests_per_day: int = 10000
    burst_limit: int = 20
    window_size_seconds: int = 60


class ValidationConfig(BaseModel):
    """Validation configuration."""
    max_request_size_mb: float = 10.0
    max_json_depth: int = 10
    max_array_length: int = 1000
    max_string_length: int = 10000
    allowed_content_types: Set[str] = {
        "application/json",
        "application/x-www-form-urlencoded",
        "multipart/form-data",
        "text/plain"
    }
    sanitize_html: bool = True
    validate_sql_injection: bool = True
    validate_xss: bool = True
    block_suspicious_patterns: bool = True


class SecurityPatterns:
    """Security patterns for validation."""
    
    # SQL injection patterns
    SQL_INJECTION_PATTERNS = [
        r"(?i)(union\s+select)",
        r"(?i)(select\s+.*\s+from)",
        r"(?i)(insert\s+into)",
        r"(?i)(delete\s+from)",
        r"(?i)(update\s+.*\s+set)",
        r"(?i)(drop\s+table)",
        r"(?i)(exec\s*\()",
        r"(?i)(script\s*>)",
        r"(?i)(;\s*drop)",
        r"(?i)(;\s*delete)",
    ]
    
    # XSS patterns
    XSS_PATTERNS = [
        r"(?i)<script[^>]*>.*?</script>",
        r"(?i)<iframe[^>]*>.*?</iframe>",
        r"(?i)<object[^>]*>.*?</object>",
        r"(?i)<embed[^>]*>.*?</embed>",
        r"(?i)javascript:",
        r"(?i)vbscript:",
        r"(?i)data:text/html",
        r"(?i)on\w+\s*=",
    ]
    
    # Suspicious patterns
    SUSPICIOUS_PATTERNS = [
        r"(?i)(\.\.\/){2,}",  # Directory traversal
        r"(?i)\/etc\/passwd",
        r"(?i)\/proc\/",
        r"(?i)cmd\.exe",
        r"(?i)powershell",
        r"(?i)eval\s*\(",
        r"(?i)exec\s*\(",
        r"(?i)system\s*\(",
    ]


class ValidationMiddleware(BaseHTTPMiddleware):
    """
    Comprehensive validation middleware.
    Handles request validation, sanitization, rate limiting, and security checks.
    """
    
    def __init__(
        self,
        app,
        redis_service: Optional[RedisService] = None,
        rate_limit_config: Optional[RateLimitConfig] = None,
        validation_config: Optional[ValidationConfig] = None,
        excluded_paths: Optional[Set[str]] = None,
        rate_limit_excluded_paths: Optional[Set[str]] = None,
    ):
        super().__init__(app)
        
        self.redis_service = redis_service
        self.rate_limit_config = rate_limit_config or RateLimitConfig()
        self.validation_config = validation_config or ValidationConfig()
        
        self.excluded_paths = excluded_paths or {
            "/docs", "/redoc", "/openapi.json", "/health", "/", "/favicon.ico"
        }
        
        self.rate_limit_excluded_paths = rate_limit_excluded_paths or {
            "/health", "/", "/favicon.ico"
        }
        
        # Compile security patterns for performance
        self.sql_patterns = [re.compile(pattern) for pattern in SecurityPatterns.SQL_INJECTION_PATTERNS]
        self.xss_patterns = [re.compile(pattern) for pattern in SecurityPatterns.XSS_PATTERNS]
        self.suspicious_patterns = [re.compile(pattern) for pattern in SecurityPatterns.SUSPICIOUS_PATTERNS]
    
    def _is_path_excluded(self, path: str, exclusion_set: Set[str]) -> bool:
        """Check if path is in exclusion set."""
        return any(path.startswith(excluded) for excluded in exclusion_set)
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()
        
        # Fallback to client host
        if hasattr(request.client, "host"):
            return request.client.host
        
        return "unknown"
    
    async def _check_rate_limit(self, request: Request) -> None:
        """Check and enforce rate limiting."""
        if not self.redis_service:
            return  # Skip rate limiting if Redis not available
        
        if self._is_path_excluded(request.url.path, self.rate_limit_excluded_paths):
            return
        
        client_ip = self._get_client_ip(request)
        current_time = int(time.time())
        
        # Create rate limit keys
        minute_key = f"rate_limit:minute:{client_ip}:{current_time // 60}"
        hour_key = f"rate_limit:hour:{client_ip}:{current_time // 3600}"
        day_key = f"rate_limit:day:{client_ip}:{current_time // 86400}"
        burst_key = f"rate_limit:burst:{client_ip}"
        
        try:
            # Check burst limit (sliding window)
            burst_count = await self._check_sliding_window(
                burst_key, 
                self.rate_limit_config.burst_limit,
                self.rate_limit_config.window_size_seconds
            )
            
            if burst_count > self.rate_limit_config.burst_limit:
                raise RateLimitException("Burst rate limit exceeded")
            
            # Check minute limit
            minute_count = await self.redis_service.incr(minute_key)
            if minute_count == 1:
                await self.redis_service.expire(minute_key, 60)
            
            if minute_count > self.rate_limit_config.requests_per_minute:
                raise RateLimitException("Rate limit exceeded: too many requests per minute")
            
            # Check hour limit
            hour_count = await self.redis_service.incr(hour_key)
            if hour_count == 1:
                await self.redis_service.expire(hour_key, 3600)
            
            if hour_count > self.rate_limit_config.requests_per_hour:
                raise RateLimitException("Rate limit exceeded: too many requests per hour")
            
            # Check day limit
            day_count = await self.redis_service.incr(day_key)
            if day_count == 1:
                await self.redis_service.expire(day_key, 86400)
            
            if day_count > self.rate_limit_config.requests_per_day:
                raise RateLimitException("Rate limit exceeded: too many requests per day")
                
        except RateLimitException:
            raise
        except Exception as e:
            logger.warning("Rate limit check failed", error=str(e))
            # Don't block requests if rate limiting fails
    
    async def _check_sliding_window(self, key: str, limit: int, window_seconds: int) -> int:
        """Check sliding window rate limit."""
        if not self.redis_service:
            return 0
        
        current_time = time.time()
        window_start = current_time - window_seconds
        
        # Remove old entries
        await self.redis_service.zremrangebyscore(key, 0, window_start)
        
        # Count current requests
        count = await self.redis_service.zcard(key)
        
        if count < limit:
            # Add current request
            await self.redis_service.zadd(key, {str(current_time): current_time})
            await self.redis_service.expire(key, window_seconds)
        
        return count
    
    def _validate_content_type(self, request: Request) -> None:
        """Validate request content type."""
        content_type = request.headers.get("content-type", "").split(";")[0].lower()
        
        if request.method in ["POST", "PUT", "PATCH"] and content_type:
            if content_type not in self.validation_config.allowed_content_types:
                raise ValidationException(f"Unsupported content type: {content_type}")
    
    def _validate_request_size(self, request: Request) -> None:
        """Validate request size."""
        content_length = request.headers.get("content-length")
        if content_length:
            try:
                size_bytes = int(content_length)
                max_bytes = self.validation_config.max_request_size_mb * 1024 * 1024
                
                if size_bytes > max_bytes:
                    raise ValidationException(
                        f"Request too large: {size_bytes} bytes (max: {max_bytes})"
                    )
            except ValueError:
                raise ValidationException("Invalid content-length header")
    
    def _sanitize_string(self, value: str) -> str:
        """Sanitize string value."""
        if not isinstance(value, str):
            return value
        
        # HTML sanitization
        if self.validation_config.sanitize_html:
            value = html.escape(value)
        
        # URL decode
        value = unquote(value)
        
        # Trim whitespace
        value = value.strip()
        
        # Length validation
        if len(value) > self.validation_config.max_string_length:
            raise ValidationException(
                f"String too long: {len(value)} chars (max: {self.validation_config.max_string_length})"
            )
        
        return value
    
    def _validate_security_patterns(self, text: str) -> None:
        """Validate text against security patterns."""
        if not isinstance(text, str):
            return
        
        # SQL injection detection
        if self.validation_config.validate_sql_injection:
            for pattern in self.sql_patterns:
                if pattern.search(text):
                    raise ValidationException("Potential SQL injection detected")
        
        # XSS detection
        if self.validation_config.validate_xss:
            for pattern in self.xss_patterns:
                if pattern.search(text):
                    raise ValidationException("Potential XSS attack detected")
        
        # Suspicious patterns
        if self.validation_config.block_suspicious_patterns:
            for pattern in self.suspicious_patterns:
                if pattern.search(text):
                    raise ValidationException("Suspicious pattern detected")
    
    def _sanitize_data(self, data: Any, depth: int = 0) -> Any:
        """Recursively sanitize data structure."""
        if depth > self.validation_config.max_json_depth:
            raise ValidationException(f"JSON too deeply nested (max depth: {self.validation_config.max_json_depth})")
        
        if isinstance(data, dict):
            sanitized = {}
            for key, value in data.items():
                # Sanitize key
                clean_key = self._sanitize_string(str(key))
                self._validate_security_patterns(clean_key)
                
                # Sanitize value
                sanitized[clean_key] = self._sanitize_data(value, depth + 1)
            
            return sanitized
        
        elif isinstance(data, list):
            if len(data) > self.validation_config.max_array_length:
                raise ValidationException(
                    f"Array too long: {len(data)} items (max: {self.validation_config.max_array_length})"
                )
            
            return [self._sanitize_data(item, depth + 1) for item in data]
        
        elif isinstance(data, str):
            clean_value = self._sanitize_string(data)
            self._validate_security_patterns(clean_value)
            return clean_value
        
        else:
            return data
    
    async def _validate_request_body(self, request: Request) -> Optional[Dict]:
        """Validate and sanitize request body."""
        if request.method not in ["POST", "PUT", "PATCH"]:
            return None
        
        content_type = request.headers.get("content-type", "").split(";")[0].lower()
        
        if content_type == "application/json":
            try:
                body = await request.body()
                if not body:
                    return None
                
                # Parse JSON
                try:
                    data = json.loads(body.decode('utf-8'))
                except json.JSONDecodeError as e:
                    raise ValidationException(f"Invalid JSON: {str(e)}")
                
                # Sanitize data
                sanitized_data = self._sanitize_data(data)
                
                return sanitized_data
                
            except ValidationException:
                raise
            except Exception as e:
                logger.error("Request body validation error", error=str(e))
                raise ValidationException("Request body validation failed")
        
        return None
    
    def _validate_query_params(self, request: Request) -> None:
        """Validate query parameters."""
        for key, value in request.query_params.items():
            # Sanitize key and value
            clean_key = self._sanitize_string(key)
            clean_value = self._sanitize_string(value)
            
            # Security validation
            self._validate_security_patterns(clean_key)
            self._validate_security_patterns(clean_value)
    
    def _validate_headers(self, request: Request) -> None:
        """Validate request headers."""
        # Check for required security headers
        security_headers = ["user-agent", "accept", "content-type"]
        
        # Validate specific headers
        user_agent = request.headers.get("user-agent", "")
        if len(user_agent) > 500:  # Reasonable user agent length
            raise ValidationException("User-Agent header too long")
        
        # Check for suspicious headers
        for name, value in request.headers.items():
            if len(value) > 8192:  # Max header value length
                raise ValidationException(f"Header '{name}' too long")
            
            # Basic security validation on header values
            self._validate_security_patterns(value)
    
    async def _log_request_metrics(self, request: Request, response: Response, start_time: float) -> None:
        """Log request metrics."""
        try:
            response_time_ms = (time.time() - start_time) * 1000
            
            metrics = RequestMetrics(
                ip_address=self._get_client_ip(request),
                user_agent=request.headers.get("user-agent"),
                endpoint=request.url.path,
                method=request.method,
                timestamp=datetime.utcnow(),
                response_time_ms=response_time_ms,
                status_code=response.status_code if response else None,
                request_size_bytes=int(request.headers.get("content-length", 0)),
            )
            
            logger.info(
                "Request processed",
                **metrics.model_dump()
            )
            
            # Store metrics in Redis for monitoring
            if self.redis_service:
                metrics_key = f"metrics:{int(time.time() // 60)}"  # Per minute
                await self.redis_service.rpush(metrics_key, metrics.model_dump_json())
                await self.redis_service.expire(metrics_key, 3600)  # Keep for 1 hour
                
        except Exception as e:
            logger.warning("Failed to log request metrics", error=str(e))
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process validation middleware."""
        start_time = time.time()
        
        # Skip validation for excluded paths
        if self._is_path_excluded(request.url.path, self.excluded_paths):
            response = await call_next(request)
            await self._log_request_metrics(request, response, start_time)
            return response
        
        try:
            # Rate limiting
            await self._check_rate_limit(request)
            
            # Content type validation
            self._validate_content_type(request)
            
            # Request size validation
            self._validate_request_size(request)
            
            # Header validation
            self._validate_headers(request)
            
            # Query parameter validation
            self._validate_query_params(request)
            
            # Request body validation and sanitization
            sanitized_body = await self._validate_request_body(request)
            
            # Store sanitized data in request state
            if sanitized_body is not None:
                request.state.sanitized_body = sanitized_body
            
            # Process request
            response = await call_next(request)
            
            # Log metrics
            await self._log_request_metrics(request, response, start_time)
            
            return response
            
        except ValidationException as e:
            logger.warning(
                "Request validation failed",
                error=str(e),
                path=request.url.path,
                method=request.method,
                client_ip=self._get_client_ip(request),
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
            
        except RateLimitException as e:
            logger.warning(
                "Rate limit exceeded",
                error=str(e),
                path=request.url.path,
                method=request.method,
                client_ip=self._get_client_ip(request),
            )
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=str(e),
                headers={"Retry-After": "60"}
            )
            
        except Exception as e:
            logger.error(
                "Validation middleware error",
                error=str(e),
                path=request.url.path,
                method=request.method,
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error"
            )


def get_sanitized_body(request: Request) -> Optional[Dict]:
    """
    Get sanitized request body from request state.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Sanitized body data or None
    """
    return getattr(request.state, "sanitized_body", None)


class RequestSanitizer:
    """Utility class for request sanitization."""
    
    @staticmethod
    def sanitize_campaign_data(data: Dict) -> Dict:
        """Sanitize campaign-specific data."""
        if not isinstance(data, dict):
            return data
        
        # Specific validation for campaign fields
        if "budget_amount" in data:
            try:
                budget = float(data["budget_amount"])
                if budget < 0:
                    raise ValidationException("Budget amount must be positive")
                if budget > 1000000:  # $1M limit
                    raise ValidationException("Budget amount too large")
            except (ValueError, TypeError):
                raise ValidationException("Invalid budget amount")
        
        if "keywords" in data and isinstance(data["keywords"], list):
            if len(data["keywords"]) > 1000:  # Reasonable keyword limit
                raise ValidationException("Too many keywords")
        
        return data
    
    @staticmethod
    def sanitize_agent_data(data: Dict) -> Dict:
        """Sanitize agent-specific data."""
        if not isinstance(data, dict):
            return data
        
        # Validate agent configuration
        if "config" in data and isinstance(data["config"], dict):
            config = data["config"]
            
            # Validate numeric ranges
            for key, value in config.items():
                if key.endswith("_rate") and isinstance(value, (int, float)):
                    if not 0 <= value <= 1:
                        raise ValidationException(f"Rate value {key} must be between 0 and 1")
        
        return data