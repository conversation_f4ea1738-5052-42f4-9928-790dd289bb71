"""
Authentication middleware for the AiLex Ad Agent System.
Provides comprehensive authentication with Supabase Auth integration, JWT validation, and API key management.
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Callable
from urllib.parse import parse_qs, urlparse

from jose import jwt
import structlog
from fastapi import Request, Response, HTTPException, status, Depends
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, ValidationError
import httpx

from utils.config import settings
from utils.exceptions import AuthenticationException, AuthorizationException
from services import auth_service
from services.redis_service import RedisService


logger = structlog.get_logger(__name__)

# HTTP Bearer security scheme for FastAPI OpenAPI documentation
security_scheme = HTTPBearer()


class UserContext(BaseModel):
    """User context model for authenticated requests."""
    user_id: str
    email: Optional[str] = None
    role: str = "user"
    permissions: List[str] = []
    organization_id: Optional[str] = None
    session_id: Optional[str] = None
    clerk_user_id: Optional[str] = None
    is_admin: bool = False
    metadata: Dict = {}


class SupabaseUser(BaseModel):
    """Supabase user model for validation."""
    id: str
    email: Optional[str] = None
    phone: Optional[str] = None
    email_confirmed_at: Optional[str] = None
    phone_confirmed_at: Optional[str] = None
    last_sign_in_at: Optional[str] = None
    user_metadata: Dict = {}
    app_metadata: Dict = {}
    created_at: str
    updated_at: str


class AuthenticationMiddleware:
    """
    Base authentication middleware class.
    Provides common authentication functionality.
    """
    
    def __init__(
        self,
        excluded_paths: Optional[Set[str]] = None,
        optional_auth_paths: Optional[Set[str]] = None,
        redis_service: Optional[RedisService] = None,
    ):
        self.excluded_paths = excluded_paths or {
            "/docs", "/redoc", "/openapi.json", "/health", "/", "/api/v1/health"
        }
        self.optional_auth_paths = optional_auth_paths or set()
        self.redis_service = redis_service
        self.security = HTTPBearer(auto_error=False)
    
    def _is_path_excluded(self, path: str) -> bool:
        """Check if the path is excluded from authentication."""
        return any(path.startswith(excluded) for excluded in self.excluded_paths)
    
    def _is_auth_optional(self, path: str) -> bool:
        """Check if authentication is optional for the path."""
        return any(path.startswith(optional) for optional in self.optional_auth_paths)
    
    async def _get_cached_user(self, token_hash: str) -> Optional[UserContext]:
        """Get cached user context from Redis."""
        if not self.redis_service:
            return None
        
        try:
            cached_data = await self.redis_service.get(f"auth:user:{token_hash}")
            if cached_data:
                return UserContext(**json.loads(cached_data))
        except Exception as e:
            logger.warning("Failed to get cached user", error=str(e))
        
        return None
    
    async def _cache_user(self, token_hash: str, user: UserContext, ttl: int = 300):
        """Cache user context in Redis."""
        if not self.redis_service:
            return
        
        try:
            await self.redis_service.set(
                f"auth:user:{token_hash}",
                user.model_dump_json(),
                ex=ttl
            )
        except Exception as e:
            logger.warning("Failed to cache user", error=str(e))
    
    async def __call__(self, request: Request, call_next: Callable) -> Response:
        """Process authentication middleware."""
        # Skip authentication for excluded paths
        if self._is_path_excluded(request.url.path):
            return await call_next(request)
        
        try:
            # Extract and validate authentication
            user_context = await self._authenticate_request(request)
            
            # Set user context in request state
            if user_context:
                request.state.user = user_context
                request.state.authenticated = True
            else:
                request.state.user = None
                request.state.authenticated = False
                
                # Raise error if auth is required
                if not self._is_auth_optional(request.url.path):
                    raise AuthenticationException("Authentication required")
            
            # Log successful authentication
            if user_context:
                logger.info(
                    "Request authenticated",
                    user_id=user_context.user_id,
                    path=request.url.path,
                    method=request.method,
                )
            
            response = await call_next(request)
            return response
            
        except AuthenticationException:
            raise
        except AuthorizationException:
            raise
        except Exception as e:
            logger.error("Authentication middleware error", error=str(e))
            raise AuthenticationException(f"Authentication failed: {str(e)}")
    
    async def _authenticate_request(self, request: Request) -> Optional[UserContext]:
        """
        Authenticate the request and return user context.
        Override this method in subclasses.
        """
        raise NotImplementedError("Subclasses must implement _authenticate_request")


class SupabaseAuthMiddleware(AuthenticationMiddleware):
    """
    Supabase-based authentication middleware.
    Validates JWT tokens issued by Supabase Auth and manages user sessions.
    """
    
    def __init__(
        self,
        supabase_url: Optional[str] = None,
        supabase_service_key: Optional[str] = None,
        excluded_paths: Optional[Set[str]] = None,
        optional_auth_paths: Optional[Set[str]] = None,
        cache_ttl: int = 300,
    ):
        super().__init__(excluded_paths, optional_auth_paths, None)
        
        self.supabase_url = supabase_url or settings.database_url or settings.SUPABASE_URL
        self.supabase_service_key = supabase_service_key or settings.database_service_key or settings.SUPABASE_SERVICE_ROLE_KEY
        self.cache_ttl = cache_ttl
        self._user_cache = {}  # Simple in-memory cache
        
        if not self.supabase_url or not self.supabase_service_key:
            logger.warning("Supabase configuration not found, authentication will be disabled")
    
    async def _get_cached_user_data(self, token_hash: str) -> Optional[Dict]:
        """Get cached user data."""
        cached_data = self._user_cache.get(token_hash)
        if cached_data:
            # Check if cache is still valid
            cache_time, user_data = cached_data
            if time.time() - cache_time < self.cache_ttl:
                return user_data
            else:
                # Remove expired cache entry
                del self._user_cache[token_hash]
        return None
    
    async def _cache_user_data(self, token_hash: str, user_data: Dict) -> None:
        """Cache user data."""
        self._user_cache[token_hash] = (time.time(), user_data)
        
        # Simple cache cleanup - remove old entries if cache gets too large
        if len(self._user_cache) > 1000:
            # Remove oldest half of entries
            sorted_items = sorted(self._user_cache.items(), key=lambda x: x[1][0])
            for key, _ in sorted_items[:500]:
                del self._user_cache[key]
    
    async def _verify_supabase_token(self, token: str) -> Dict:
        """Verify Supabase JWT token and return user information."""
        try:
            # Check if auth service is available
            if auth_service is None:
                raise AuthenticationException("Authentication service not available")
            
            # Use the auth service to verify token and get user info
            user_data = await auth_service.verify_jwt_token(token)
            return user_data
            
        except Exception as e:
            logger.warning("Supabase token verification failed", error=str(e))
            raise AuthenticationException(f"Token verification failed: {str(e)}")
    
    def _extract_user_permissions(self, supabase_user: Dict) -> List[str]:
        """Extract user permissions from Supabase user data."""
        permissions = []
        
        # Extract from user metadata
        user_metadata = supabase_user.get("user_metadata", {})
        if "permissions" in user_metadata:
            permissions.extend(user_metadata["permissions"])
        
        # Extract from app metadata (more secure)
        app_metadata = supabase_user.get("app_metadata", {})
        if "permissions" in app_metadata:
            permissions.extend(app_metadata["permissions"])
        
        # Default permissions for authenticated users
        permissions.extend(["campaigns:read", "analytics:read"])
        
        # Admin permissions
        user_role = user_metadata.get("role") or app_metadata.get("role", "user")
        if user_role == "admin":
            permissions.extend([
                "campaigns:write", "campaigns:delete",
                "users:read", "users:write",
                "system:admin"
            ])
        elif user_role == "manager":
            permissions.extend([
                "campaigns:write",
                "analytics:write"
            ])
        
        return list(set(permissions))  # Remove duplicates
    
    async def _authenticate_request(self, request: Request) -> Optional[UserContext]:
        """Authenticate request using Supabase JWT token."""
        if not self.supabase_url or not self.supabase_service_key:
            logger.warning("Supabase not configured, skipping authentication")
            return None
        
        # Extract token from Authorization header
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return None
        
        token = auth_header.split(" ", 1)[1]
        if not token:
            return None
        
        # Check cache first
        import hashlib
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        cached_user_data = await self._get_cached_user_data(token_hash)
        if cached_user_data:
            return UserContext(**cached_user_data)
        
        try:
            # Verify token with Supabase Auth service
            supabase_user = await self._verify_supabase_token(token)
            
            if not supabase_user or not supabase_user.get("id"):
                raise AuthenticationException("Invalid token or user not found")
            
            # Extract user permissions
            permissions = self._extract_user_permissions(supabase_user)
            
            # Get role from metadata
            user_metadata = supabase_user.get("user_metadata", {})
            app_metadata = supabase_user.get("app_metadata", {})
            user_role = user_metadata.get("role") or app_metadata.get("role", "user")
            
            # Create user context
            user_context = UserContext(
                user_id=supabase_user["id"],
                email=supabase_user.get("email"),
                role=user_role,
                permissions=permissions,
                organization_id=user_metadata.get("organization_id") or app_metadata.get("organization_id"),
                session_id=None,  # Supabase doesn't expose session ID in user data
                clerk_user_id=None,  # Not using Clerk
                is_admin=user_role == "admin",
                metadata={
                    "email_confirmed_at": supabase_user.get("email_confirmed_at"),
                    "phone_confirmed_at": supabase_user.get("phone_confirmed_at"),
                    "last_sign_in_at": supabase_user.get("last_sign_in_at"),
                    "created_at": supabase_user.get("created_at"),
                    "updated_at": supabase_user.get("updated_at"),
                    "user_metadata": user_metadata,
                    "app_metadata": app_metadata,
                }
            )
            
            # Cache user context
            await self._cache_user_data(token_hash, user_context.model_dump())
            
            return user_context
            
        except AuthenticationException:
            raise
        except Exception as e:
            logger.error("Supabase authentication error", error=str(e))
            raise AuthenticationException("Authentication failed")


class APIKeyAuthMiddleware(AuthenticationMiddleware):
    """
    API Key based authentication middleware.
    For service-to-service communication and external integrations.
    """
    
    def __init__(
        self,
        api_key_header: str = "X-API-Key",
        excluded_paths: Optional[Set[str]] = None,
        optional_auth_paths: Optional[Set[str]] = None,
        redis_service: Optional[RedisService] = None,
    ):
        super().__init__(excluded_paths, optional_auth_paths, redis_service)
        self.api_key_header = api_key_header
    
    async def _validate_api_key(self, api_key: str) -> Optional[UserContext]:
        """Validate API key and return user context."""
        # Check cache first
        import hashlib
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        cached_user = await self._get_cached_user(key_hash)
        
        if cached_user:
            return cached_user
        
        try:
            # Validate API key format
            if not self._is_valid_api_key_format(api_key):
                return None
            
            # Hash the API key for database lookup
            api_key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            
            # Query database for API key
            api_key_data = await self._query_api_key_database(api_key_hash)
            
            if not api_key_data:
                logger.warning("API key not found in database", key_hash=key_hash[:8])
                return None
            
            # Validate API key status and expiration
            if not self._is_api_key_valid(api_key_data):
                logger.warning(
                    "API key validation failed",
                    key_hash=key_hash[:8],
                    status=api_key_data.get("status"),
                    expired=api_key_data.get("expires_at", "N/A"),
                )
                return None
            
            # Create user context from API key data
            user_context = self._create_user_context_from_api_key(api_key_data)
            
            # Update last used timestamp
            await self._update_api_key_last_used(api_key_data["id"])
            
            # Cache for 5 minutes (shorter TTL for API keys)
            await self._cache_user(key_hash, user_context, ttl=300)
            
            logger.info(
                "API key validated successfully",
                key_id=api_key_data["id"],
                user_id=user_context.user_id,
                key_name=api_key_data.get("name", "unnamed"),
            )
            
            return user_context
            
        except Exception as e:
            logger.error("API key validation error", error=str(e), key_hash=key_hash[:8])
            return None
    
    def _is_valid_api_key_format(self, api_key: str) -> bool:
        """Validate API key format."""
        # API key should be at least 32 characters and start with expected prefix
        if len(api_key) < 32:
            return False
        
        # Check for valid prefixes
        valid_prefixes = ["ailex_", "gads_", "sys_"]
        if not any(api_key.startswith(prefix) for prefix in valid_prefixes):
            return False
        
        # Check for valid characters (alphanumeric + underscore)
        import re
        if not re.match(r"^[a-zA-Z0-9_]+$", api_key):
            return False
        
        return True
    
    async def _query_api_key_database(self, api_key_hash: str) -> Optional[Dict]:
        """Query database for API key information."""
        try:
            # Import database service
            from services.database import DatabaseService
            
            db_service = DatabaseService()
            
            # Query API keys table
            query = """
                SELECT 
                    id, name, key_hash, status, permissions, 
                    user_id, organization_id, expires_at, 
                    last_used_at, created_at, metadata
                FROM api_keys 
                WHERE key_hash = $1 AND deleted_at IS NULL
            """
            
            result = await db_service.fetch_one(query, api_key_hash)
            
            if result:
                return dict(result)
            
            return None
            
        except ImportError:
            # Fallback if database service not available
            logger.warning("Database service not available, using fallback API key validation")
            return self._fallback_api_key_validation(api_key_hash)
        except Exception as e:
            logger.error("Database query failed for API key", error=str(e))
            return None
    
    def _fallback_api_key_validation(self, api_key_hash: str) -> Optional[Dict]:
        """Fallback API key validation when database is not available."""
        # Hardcoded API keys for development/testing
        fallback_keys = {
            # Hash of "ailex_dev_test_key_12345678901234567890"
            "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2": {
                "id": "fallback-1",
                "name": "Development Test Key",
                "status": "active",
                "permissions": ["campaigns:read", "campaigns:write", "analytics:read"],
                "user_id": "system",
                "organization_id": None,
                "expires_at": None,
                "metadata": {"environment": "development", "fallback": True}
            }
        }
        
        return fallback_keys.get(api_key_hash)
    
    def _is_api_key_valid(self, api_key_data: Dict) -> bool:
        """Check if API key is valid and not expired."""
        # Check status
        if api_key_data.get("status") != "active":
            return False
        
        # Check expiration
        expires_at = api_key_data.get("expires_at")
        if expires_at:
            from datetime import datetime
            if isinstance(expires_at, str):
                expires_at = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
            
            if datetime.utcnow() > expires_at.replace(tzinfo=None):
                return False
        
        return True
    
    def _create_user_context_from_api_key(self, api_key_data: Dict) -> UserContext:
        """Create UserContext from API key data."""
        permissions = api_key_data.get("permissions", [])
        
        # Ensure permissions is a list
        if isinstance(permissions, str):
            import json
            try:
                permissions = json.loads(permissions)
            except json.JSONDecodeError:
                permissions = []
        
        metadata = api_key_data.get("metadata", {})
        if isinstance(metadata, str):
            import json
            try:
                metadata = json.loads(metadata)
            except json.JSONDecodeError:
                metadata = {}
        
        # Add API key specific metadata
        metadata.update({
            "auth_method": "api_key",
            "api_key_id": api_key_data["id"],
            "api_key_name": api_key_data.get("name", "unnamed"),
        })
        
        return UserContext(
            user_id=api_key_data.get("user_id", "system"),
            email=None,  # API keys don't have associated emails
            role="service",
            permissions=permissions,
            organization_id=api_key_data.get("organization_id"),
            session_id=None,
            clerk_user_id=None,
            is_admin=False,  # API keys are never admin by default
            metadata=metadata,
        )
    
    async def _update_api_key_last_used(self, api_key_id: str) -> None:
        """Update the last used timestamp for an API key."""
        try:
            from services.database import DatabaseService
            
            db_service = DatabaseService()
            
            query = """
                UPDATE api_keys 
                SET last_used_at = NOW(), usage_count = COALESCE(usage_count, 0) + 1
                WHERE id = $1
            """
            
            await db_service.execute(query, api_key_id)
            
        except ImportError:
            # Database service not available
            pass
        except Exception as e:
            logger.warning("Failed to update API key last used timestamp", error=str(e))
    
    async def _authenticate_request(self, request: Request) -> Optional[UserContext]:
        """Authenticate request using API key."""
        api_key = request.headers.get(self.api_key_header)
        
        if not api_key:
            return None
        
        return await self._validate_api_key(api_key)


def get_current_user(request: Request) -> Optional[UserContext]:
    """
    Get current authenticated user from request.
    
    Args:
        request: FastAPI request object
        
    Returns:
        UserContext or None if not authenticated
    """
    return getattr(request.state, "user", None)


def require_auth(request: Request) -> UserContext:
    """
    Require authentication and return user context.
    
    Args:
        request: FastAPI request object
        
    Returns:
        UserContext
        
    Raises:
        AuthenticationException: If not authenticated
    """
    user = get_current_user(request)
    if not user:
        raise AuthenticationException("Authentication required")
    return user


def get_current_user_id(request: Request) -> Optional[str]:
    """
    Get the current user's ID from the request.
    
    Args:
        request: FastAPI request object
        
    Returns:
        User ID string or None if not authenticated
    """
    user = get_current_user(request)
    return user.user_id if user else None


def require_permission(permission: str):
    """
    Decorator to require specific permission.
    
    Args:
        permission: Required permission string
        
    Returns:
        Decorated function
    """
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            user = require_auth(request)
            if permission not in user.permissions:
                raise AuthorizationException(f"Permission '{permission}' required")
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator


def require_admin(request: Request) -> UserContext:
    """
    Require admin role and return user context.
    
    Args:
        request: FastAPI request object
        
    Returns:
        UserContext
        
    Raises:
        AuthorizationException: If not admin
    """
    user = require_auth(request)
    if not user.is_admin:
        raise AuthorizationException("Admin role required")
    return user