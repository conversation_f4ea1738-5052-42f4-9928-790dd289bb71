"""
Middleware integration for the AiLex Ad Agent System.
Combines authentication, validation, and other middleware components.
"""

from typing import Optional, Set
from fastapi import Fast<PERSON><PERSON>, Request, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from starlette.middleware.sessions import SessionMiddleware
import structlog

from utils.config import settings
from services.redis_service import RedisService
from middleware.auth import SupabaseAuthMiddleware, APIKeyAuthMiddleware
from middleware.validation import ValidationMiddleware, RateLimitConfig, ValidationConfig
from middleware.security import SecurityMiddleware, create_security_middleware
from middleware.transformation import ResponseTransformationMiddleware, TransformationConfig
from middleware.logging import AdvancedLoggingMiddleware, LoggingConfig
from middleware.tracing import RequestTracingMiddleware, TracingConfig
from middleware.metrics import MetricsMiddleware, MetricsConfig


logger = structlog.get_logger(__name__)


class MiddlewareManager:
    """
    Manages all middleware components for the FastAPI application.
    Provides centralized configuration and setup.
    """
    
    def __init__(
        self,
        redis_service: Optional[RedisService] = None,
        enable_auth: bool = True,
        enable_validation: bool = True,
        enable_rate_limiting: bool = True,
        enable_security: bool = True,
        enable_transformation: bool = True,
        enable_advanced_logging: bool = True,
        enable_request_tracing: bool = True,
        enable_metrics: bool = True,
    ):
        self.redis_service = redis_service
        self.enable_auth = enable_auth
        self.enable_validation = enable_validation
        self.enable_rate_limiting = enable_rate_limiting
        self.enable_security = enable_security
        self.enable_transformation = enable_transformation
        self.enable_advanced_logging = enable_advanced_logging
        self.enable_request_tracing = enable_request_tracing
        self.enable_metrics = enable_metrics
        
        # Configure middleware settings
        self.auth_excluded_paths = {
            "/docs", "/redoc", "/openapi.json", "/health", "/", "/favicon.ico",
            "/api/v1/health", "/api/v1/status", "/metrics"
        }
        
        self.auth_optional_paths = {
            "/api/v1/campaigns/public",
            "/api/v1/analytics/public"
        }
        
        self.validation_excluded_paths = {
            "/docs", "/redoc", "/openapi.json", "/health", "/", "/favicon.ico",
            "/static", "/assets"
        }
        
        self.rate_limit_excluded_paths = {
            "/health", "/", "/favicon.ico", "/static", "/assets"
        }
    
    def setup_middleware(self, app: FastAPI) -> None:
        """
        Set up all middleware components for the FastAPI application.
        
        Args:
            app: FastAPI application instance
        """
        try:
            # Advanced logging middleware (applied first for comprehensive logging)
            if self.enable_advanced_logging:
                self._setup_advanced_logging_middleware(app)
            
            # Security middleware (applied early for threat detection)
            if self.enable_security:
                self._setup_enhanced_security_middleware(app)
            
            # Basic security middleware (sessions, trusted hosts)
            self._setup_basic_security_middleware(app)
            
            # Authentication middleware
            if self.enable_auth:
                self._setup_auth_middleware(app)
            
            # Validation and rate limiting middleware
            if self.enable_validation:
                self._setup_validation_middleware(app)
            
            # Request tracing middleware
            if self.enable_request_tracing:
                self._setup_tracing_middleware(app)
            
            # Response transformation middleware
            if self.enable_transformation:
                self._setup_transformation_middleware(app)
            
            # Metrics collection middleware
            if self.enable_metrics:
                self._setup_metrics_middleware(app)
            
            # Performance middleware
            self._setup_performance_middleware(app)
            
            # CORS middleware (applied last to be processed first)
            self._setup_cors_middleware(app)
            
            logger.info(
                "Middleware setup completed",
                auth_enabled=self.enable_auth,
                validation_enabled=self.enable_validation,
                rate_limiting_enabled=self.enable_rate_limiting,
                security_enabled=self.enable_security,
                transformation_enabled=self.enable_transformation,
                advanced_logging_enabled=self.enable_advanced_logging,
            )
            
        except Exception as e:
            logger.error("Failed to setup middleware", error=str(e))
            raise
    
    def _setup_enhanced_security_middleware(self, app: FastAPI) -> None:
        """Set up enhanced security middleware with threat detection."""
        # Create advanced security middleware
        security_middleware_class = create_security_middleware(
            redis_service=self.redis_service,
            enable_csp=True,
            enable_threat_detection=True,
            enable_rate_limiting=True,
            enable_ip_whitelist=False,
            trusted_ips=set(),
            excluded_paths={
                "/docs", "/redoc", "/openapi.json", "/health", "/", "/favicon.ico",
                "/static", "/assets", "/metrics"
            },
        )
        
        app.add_middleware(security_middleware_class)
        logger.info("Enhanced security middleware enabled")
    
    def _setup_basic_security_middleware(self, app: FastAPI) -> None:
        """Set up basic security-related middleware."""
        # Session middleware for CSRF protection
        app.add_middleware(
            SessionMiddleware,
            secret_key=settings.SECRET_KEY if hasattr(settings, 'SECRET_KEY') else "dev-secret-key-change-in-production",
            max_age=3600,  # 1 hour
            same_site="lax",
            https_only=settings.is_production,
        )
        
        # Trusted host middleware
        if settings.TRUSTED_HOSTS:
            app.add_middleware(
                TrustedHostMiddleware,
                allowed_hosts=settings.TRUSTED_HOSTS
            )
            logger.info("Trusted host middleware enabled", hosts=settings.TRUSTED_HOSTS)
    
    def _setup_advanced_logging_middleware(self, app: FastAPI) -> None:
        """Set up advanced logging middleware."""
        logging_config = LoggingConfig(
            log_requests=True,
            log_responses=True,
            log_request_body=settings.is_development,  # Only in development
            log_response_body=False,  # Disabled for performance
            log_headers=True,
            log_query_params=True,
            log_performance_metrics=True,
            log_errors=True,
            log_stack_traces=settings.is_development,
            mask_sensitive_data=True,
            max_body_size=10000,
            async_logging=True,
            buffer_size=100,
            flush_interval=5.0,
        )
        
        logging_middleware = AdvancedLoggingMiddleware(
            app=None,  # Will be set by FastAPI
            config=logging_config,
            redis_service=self.redis_service,
        )
        
        app.add_middleware(
            type(logging_middleware),
            config=logging_config,
            redis_service=self.redis_service,
        )
        
        logger.info("Advanced logging middleware enabled")
    
    def _setup_transformation_middleware(self, app: FastAPI) -> None:
        """Set up response transformation middleware."""
        transformation_config = TransformationConfig(
            enable_response_transformation=True,
            enable_request_transformation=True,
            enable_data_serialization=True,
            enable_field_renaming=True,
            enable_data_filtering=True,
            default_api_version="v1",
            strip_null_values=True,
            convert_decimals=True,
            format_timestamps=True,
            include_metadata=True,
        )
        
        transformation_middleware = ResponseTransformationMiddleware(
            app=None,  # Will be set by FastAPI
            config=transformation_config,
        )
        
        app.add_middleware(
            type(transformation_middleware),
            config=transformation_config,
        )
        
        logger.info("Response transformation middleware enabled")
    
    def _setup_tracing_middleware(self, app: FastAPI) -> None:
        """Set up distributed tracing middleware."""
        tracing_config = TracingConfig(
            enable_tracing=True,
            enable_performance_monitoring=True,
            enable_distributed_tracing=True,
            sample_rate=1.0 if settings.is_development else 0.1,  # Sample more in dev
            max_spans_per_trace=100,
            span_export_batch_size=50,
            span_export_timeout=30.0,
            store_traces_in_redis=True,
            trace_retention_hours=24,
        )
        
        tracing_middleware = RequestTracingMiddleware(
            app=None,  # Will be set by FastAPI
            config=tracing_config,
            redis_service=self.redis_service,
        )
        
        app.add_middleware(
            type(tracing_middleware),
            config=tracing_config,
            redis_service=self.redis_service,
        )
        
        logger.info("Request tracing middleware enabled")
    
    def _setup_metrics_middleware(self, app: FastAPI) -> None:
        """Set up metrics collection middleware."""
        metrics_config = MetricsConfig(
            enable_metrics=True,
            enable_request_metrics=True,
            enable_performance_metrics=True,
            enable_custom_metrics=True,
            collect_detailed_metrics=settings.is_development,  # More detailed in dev
            export_interval_seconds=60.0,
            max_cardinality=10000,
            slow_request_threshold_ms=1000.0,
            error_sampling_rate=1.0,  # Sample all errors
            success_sampling_rate=0.1,  # Sample 10% of successful requests
        )
        
        metrics_middleware = MetricsMiddleware(
            app=None,  # Will be set by FastAPI
            config=metrics_config,
            redis_service=self.redis_service,
        )
        
        app.add_middleware(
            type(metrics_middleware),
            config=metrics_config,
            redis_service=self.redis_service,
        )
        
        logger.info("Metrics collection middleware enabled")
    
    def _setup_auth_middleware(self, app: FastAPI) -> None:
        """Set up authentication middleware."""
        # Primary authentication: Supabase
        if settings.SUPABASE_URL and settings.SUPABASE_SERVICE_ROLE_KEY:
            app.add_middleware(
                SupabaseAuthMiddleware,
                excluded_paths=self.auth_excluded_paths,
                optional_auth_paths=self.auth_optional_paths,
            )
            logger.info("Supabase authentication middleware enabled")
        
        # Secondary authentication: API Keys (for service-to-service)
        app.add_middleware(
            APIKeyAuthMiddleware,
            excluded_paths=self.auth_excluded_paths,
            optional_auth_paths=self.auth_optional_paths,
            redis_service=self.redis_service,
        )
        logger.info("API key authentication middleware enabled")
    
    def _setup_validation_middleware(self, app: FastAPI) -> None:
        """Set up validation and rate limiting middleware."""
        # Configure rate limiting with hierarchical limits
        rate_limit_config = RateLimitConfig(
            requests_per_minute=settings.RATE_LIMIT_REQUESTS_PER_MINUTE,
            requests_per_hour=settings.RATE_LIMIT_REQUESTS_PER_MINUTE * 60,
            requests_per_day=settings.RATE_LIMIT_REQUESTS_PER_MINUTE * 60 * 24,
            burst_limit=min(50, settings.RATE_LIMIT_REQUESTS_PER_MINUTE // 2),
        )
        
        # Configure validation with enhanced security
        validation_config = ValidationConfig(
            max_request_size_mb=10.0,
            max_json_depth=10,
            max_array_length=1000,
            max_string_length=10000,
            sanitize_html=True,
            validate_sql_injection=True,
            validate_xss=True,
            block_suspicious_patterns=True,
        )
        
        # Add validation middleware
        validation_middleware = ValidationMiddleware(
            redis_service=self.redis_service if self.enable_rate_limiting else None,
            rate_limit_config=rate_limit_config,
            validation_config=validation_config,
            excluded_paths=self.validation_excluded_paths,
            rate_limit_excluded_paths=self.rate_limit_excluded_paths,
        )
        
        app.add_middleware(ValidationMiddleware, **{
            'redis_service': validation_middleware.redis_service,
            'rate_limit_config': validation_middleware.rate_limit_config,
            'validation_config': validation_middleware.validation_config,
            'excluded_paths': validation_middleware.excluded_paths,
            'rate_limit_excluded_paths': validation_middleware.rate_limit_excluded_paths,
        })
        
        logger.info("Validation and rate limiting middleware enabled")
    
    def _setup_performance_middleware(self, app: FastAPI) -> None:
        """Set up performance-related middleware."""
        # GZip compression
        app.add_middleware(GZipMiddleware, minimum_size=1000)
        logger.info("GZip compression middleware enabled")
    
    def _setup_cors_middleware(self, app: FastAPI) -> None:
        """Set up CORS middleware."""
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.CORS_ORIGINS,
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
            allow_headers=[
                "Accept",
                "Accept-Language",
                "Content-Language",
                "Content-Type",
                "Authorization",
                "X-API-Key",
                "X-Request-ID",
                "X-Correlation-ID",
            ],
            expose_headers=[
                "X-Request-ID",
                "X-Rate-Limit-Remaining",
                "X-Rate-Limit-Reset",
            ]
        )
        logger.info("CORS middleware enabled", origins=settings.CORS_ORIGINS)


# Middleware dependency functions
async def get_current_user_optional(request: Request):
    """Get current user if authenticated, otherwise None."""
    from middleware.auth import get_current_user
    return get_current_user(request)


async def get_current_user_required(request: Request):
    """Get current user, raise exception if not authenticated."""
    from middleware.auth import require_auth
    return require_auth(request)


async def get_sanitized_body_optional(request: Request):
    """Get sanitized request body if available."""
    from middleware.validation import get_sanitized_body
    return get_sanitized_body(request)


# Permission checking functions
def require_permission(permission: str):
    """Decorator to require specific permission."""
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            from middleware.auth import require_auth
            from utils.exceptions import AuthorizationException
            
            user = require_auth(request)
            if permission not in user.permissions:
                raise AuthorizationException(f"Permission '{permission}' required")
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator


def require_admin_role():
    """Decorator to require admin role."""
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            from middleware.auth import require_admin
            require_admin(request)
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator


# Global middleware manager instance
middleware_manager = MiddlewareManager()


# Exception handlers for middleware errors
async def authentication_exception_handler(request: Request, exc):
    """Handle authentication exceptions."""
    logger.warning(
        "Authentication failed",
        path=request.url.path,
        method=request.method,
        error=str(exc),
    )
    return HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Authentication required",
        headers={"WWW-Authenticate": "Bearer"},
    )


async def authorization_exception_handler(request: Request, exc):
    """Handle authorization exceptions."""
    logger.warning(
        "Authorization failed",
        path=request.url.path,
        method=request.method,
        error=str(exc),
    )
    return HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="Insufficient permissions",
    )


async def validation_exception_handler(request: Request, exc):
    """Handle validation exceptions."""
    logger.warning(
        "Validation failed",
        path=request.url.path,
        method=request.method,
        error=str(exc),
    )
    return HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail=str(exc),
    )


async def rate_limit_exception_handler(request: Request, exc):
    """Handle rate limit exceptions."""
    logger.warning(
        "Rate limit exceeded",
        path=request.url.path,
        method=request.method,
        error=str(exc),
    )
    return HTTPException(
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        detail=str(exc),
        headers={"Retry-After": "60"},
    )


def setup_exception_handlers(app: FastAPI) -> None:
    """Set up exception handlers for middleware errors."""
    from utils.exceptions import (
        AuthenticationException,
        AuthorizationException,
        ValidationException,
        RateLimitException,
    )
    
    app.add_exception_handler(AuthenticationException, authentication_exception_handler)
    app.add_exception_handler(AuthorizationException, authorization_exception_handler)
    app.add_exception_handler(ValidationException, validation_exception_handler)
    app.add_exception_handler(RateLimitException, rate_limit_exception_handler)
    
    logger.info("Exception handlers for middleware setup completed")