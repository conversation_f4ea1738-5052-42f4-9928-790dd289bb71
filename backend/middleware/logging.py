"""
Advanced logging and error handling middleware for the Google Ads AI Agent System.
Provides comprehensive request/response logging, error tracking, and performance monitoring.
"""

import json
import time
import traceback
import asyncio
import uuid
from typing import Dict, List, Optional, Set, Callable, Any, Union
from datetime import datetime, timezone
from contextlib import asynccontextmanager

import structlog
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from utils.config import settings
from utils.exceptions import CustomException
from services.redis_service import RedisService


logger = structlog.get_logger(__name__)


class LogLevel:
    """Logging level constants."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class LoggingConfig:
    """Configuration for logging middleware."""
    
    def __init__(
        self,
        log_requests: bool = True,
        log_responses: bool = True,
        log_request_body: bool = False,  # Sensitive data concerns
        log_response_body: bool = False,  # Performance concerns
        log_headers: bool = True,
        log_query_params: bool = True,
        log_performance_metrics: bool = True,
        log_errors: bool = True,
        log_stack_traces: bool = True,
        mask_sensitive_data: bool = True,
        max_body_size: int = 10000,  # Max body size to log (10KB)
        async_logging: bool = True,
        buffer_size: int = 100,
        flush_interval: float = 5.0,  # seconds
        excluded_paths: Optional[Set[str]] = None,
        sensitive_headers: Optional[Set[str]] = None,
        sensitive_fields: Optional[Set[str]] = None,
    ):
        self.log_requests = log_requests
        self.log_responses = log_responses
        self.log_request_body = log_request_body
        self.log_response_body = log_response_body
        self.log_headers = log_headers
        self.log_query_params = log_query_params
        self.log_performance_metrics = log_performance_metrics
        self.log_errors = log_errors
        self.log_stack_traces = log_stack_traces
        self.mask_sensitive_data = mask_sensitive_data
        self.max_body_size = max_body_size
        self.async_logging = async_logging
        self.buffer_size = buffer_size
        self.flush_interval = flush_interval
        
        self.excluded_paths = excluded_paths or {
            "/health", "/metrics", "/favicon.ico", "/static"
        }
        
        self.sensitive_headers = sensitive_headers or {
            "authorization", "x-api-key", "cookie", "set-cookie",
            "x-auth-token", "x-access-token", "x-refresh-token"
        }
        
        self.sensitive_fields = sensitive_fields or {
            "password", "secret", "key", "token", "credentials",
            "auth", "session", "refresh_token", "access_token",
            "api_key", "private_key", "client_secret"
        }


class RequestContext:
    """Context for tracking request lifecycle."""
    
    def __init__(self, request: Request):
        self.request_id = str(uuid.uuid4())
        self.correlation_id = request.headers.get("X-Correlation-ID", self.request_id)
        self.start_time = time.time()
        self.method = request.method
        self.url = str(request.url)
        self.path = request.url.path
        self.client_ip = self._get_client_ip(request)
        self.user_agent = request.headers.get("user-agent", "")
        self.content_type = request.headers.get("content-type", "")
        self.content_length = request.headers.get("content-length", "0")
        
        # Response data (filled later)
        self.status_code: Optional[int] = None
        self.response_size: Optional[int] = None
        self.processing_time: Optional[float] = None
        self.error: Optional[Exception] = None
        
        # Custom data
        self.custom_data: Dict[str, Any] = {}
        
    def _get_client_ip(self, request: Request) -> str:
        """Extract real client IP address."""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()
        
        if hasattr(request.client, "host"):
            return request.client.host
        
        return "unknown"
    
    def finish(self, response: Response, error: Optional[Exception] = None):
        """Mark request as finished and calculate metrics."""
        self.processing_time = time.time() - self.start_time
        self.status_code = response.status_code if response else 500
        self.error = error
        
        # Calculate response size
        if hasattr(response, "body"):
            if isinstance(response.body, (bytes, bytearray)):
                self.response_size = len(response.body)
            elif isinstance(response.body, str):
                self.response_size = len(response.body.encode())
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert context to dictionary for logging."""
        return {
            "request_id": self.request_id,
            "correlation_id": self.correlation_id,
            "method": self.method,
            "url": self.url,
            "path": self.path,
            "client_ip": self.client_ip,
            "user_agent": self.user_agent,
            "content_type": self.content_type,
            "content_length": self.content_length,
            "status_code": self.status_code,
            "response_size": self.response_size,
            "processing_time_ms": round(self.processing_time * 1000, 2) if self.processing_time else None,
            "error": str(self.error) if self.error else None,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            **self.custom_data,
        }


class AsyncLogBuffer:
    """Asynchronous log buffer for high-performance logging."""
    
    def __init__(
        self,
        buffer_size: int = 100,
        flush_interval: float = 5.0,
        redis_service: Optional[RedisService] = None,
    ):
        self.buffer_size = buffer_size
        self.flush_interval = flush_interval
        self.redis_service = redis_service
        
        self._buffer: List[Dict[str, Any]] = []
        self._lock = asyncio.Lock()
        self._flush_task: Optional[asyncio.Task] = None
        self._running = True
        
    async def start(self):
        """Start the async log buffer."""
        if self._flush_task is None:
            self._flush_task = asyncio.create_task(self._flush_periodically())
    
    async def stop(self):
        """Stop the async log buffer and flush remaining logs."""
        self._running = False
        if self._flush_task:
            self._flush_task.cancel()
            try:
                await self._flush_task
            except asyncio.CancelledError:
                pass
        
        await self._flush()
    
    async def add(self, log_entry: Dict[str, Any]):
        """Add a log entry to the buffer."""
        async with self._lock:
            self._buffer.append(log_entry)
            
            if len(self._buffer) >= self.buffer_size:
                await self._flush()
    
    async def _flush(self):
        """Flush buffered logs."""
        if not self._buffer:
            return
        
        async with self._lock:
            logs_to_flush = self._buffer.copy()
            self._buffer.clear()
        
        try:
            # Log to structured logger
            for log_entry in logs_to_flush:
                level = log_entry.pop("level", "info")
                message = log_entry.pop("message", "Request processed")
                
                log_method = getattr(logger, level, logger.info)
                log_method(message, **log_entry)
            
            # Store in Redis for analytics
            if self.redis_service:
                await self._store_in_redis(logs_to_flush)
                
        except Exception as e:
            logger.error("Failed to flush log buffer", error=str(e))
    
    async def _store_in_redis(self, logs: List[Dict[str, Any]]):
        """Store logs in Redis for analytics."""
        try:
            # Store in time-series format for analytics
            current_minute = int(time.time() // 60)
            key = f"logs:minute:{current_minute}"
            
            # Aggregate logs by minute
            aggregated = {
                "timestamp": current_minute * 60,
                "count": len(logs),
                "methods": {},
                "status_codes": {},
                "paths": {},
                "errors": [],
                "performance": {
                    "avg_processing_time": 0,
                    "max_processing_time": 0,
                    "min_processing_time": float('inf'),
                }
            }
            
            processing_times = []
            
            for log in logs:
                # Count methods
                method = log.get("method", "unknown")
                aggregated["methods"][method] = aggregated["methods"].get(method, 0) + 1
                
                # Count status codes
                status = log.get("status_code", 0)
                aggregated["status_codes"][str(status)] = aggregated["status_codes"].get(str(status), 0) + 1
                
                # Count paths
                path = log.get("path", "unknown")
                aggregated["paths"][path] = aggregated["paths"].get(path, 0) + 1
                
                # Collect errors
                if log.get("error"):
                    aggregated["errors"].append({
                        "error": log["error"],
                        "path": path,
                        "method": method,
                        "request_id": log.get("request_id"),
                    })
                
                # Collect performance metrics
                if log.get("processing_time_ms"):
                    processing_times.append(log["processing_time_ms"])
            
            # Calculate performance metrics
            if processing_times:
                aggregated["performance"]["avg_processing_time"] = sum(processing_times) / len(processing_times)
                aggregated["performance"]["max_processing_time"] = max(processing_times)
                aggregated["performance"]["min_processing_time"] = min(processing_times)
            
            # Store aggregated data
            await self.redis_service.set(key, aggregated, ttl=3600)  # Keep for 1 hour
            
        except Exception as e:
            logger.warning("Failed to store logs in Redis", error=str(e))
    
    async def _flush_periodically(self):
        """Periodically flush the buffer."""
        while self._running:
            try:
                await asyncio.sleep(self.flush_interval)
                await self._flush()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in periodic flush", error=str(e))


class AdvancedLoggingMiddleware(BaseHTTPMiddleware):
    """
    Advanced logging middleware with comprehensive request/response tracking,
    performance monitoring, and error handling.
    """
    
    def __init__(
        self,
        app,
        config: Optional[LoggingConfig] = None,
        redis_service: Optional[RedisService] = None,
    ):
        super().__init__(app)
        
        self.config = config or LoggingConfig()
        self.redis_service = redis_service
        
        # Initialize async log buffer
        if self.config.async_logging:
            self.log_buffer = AsyncLogBuffer(
                buffer_size=self.config.buffer_size,
                flush_interval=self.config.flush_interval,
                redis_service=redis_service,
            )
        else:
            self.log_buffer = None
        
        logger.info(
            "Advanced logging middleware initialized",
            async_logging=self.config.async_logging,
            buffer_size=self.config.buffer_size,
        )
    
    async def startup(self):
        """Start the logging middleware."""
        if self.log_buffer:
            await self.log_buffer.start()
    
    async def shutdown(self):
        """Shutdown the logging middleware."""
        if self.log_buffer:
            await self.log_buffer.stop()
    
    def _is_path_excluded(self, path: str) -> bool:
        """Check if path is excluded from logging."""
        return any(path.startswith(excluded) for excluded in self.config.excluded_paths)
    
    def _mask_sensitive_data(self, data: Any, context: str = "") -> Any:
        """Mask sensitive data in logs."""
        if not self.config.mask_sensitive_data:
            return data
        
        if isinstance(data, dict):
            masked = {}
            for key, value in data.items():
                if any(sensitive in key.lower() for sensitive in self.config.sensitive_fields):
                    masked[key] = "***MASKED***"
                else:
                    masked[key] = self._mask_sensitive_data(value, f"{context}.{key}")
            return masked
        
        elif isinstance(data, list):
            return [self._mask_sensitive_data(item, f"{context}[{i}]") for i, item in enumerate(data)]
        
        elif isinstance(data, str) and len(data) > 50:
            # Mask long strings that might contain tokens
            if any(sensitive in context.lower() for sensitive in self.config.sensitive_fields):
                return "***MASKED***"
        
        return data
    
    def _sanitize_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """Sanitize headers for logging."""
        if not self.config.log_headers:
            return {}
        
        sanitized = {}
        for key, value in headers.items():
            if key.lower() in self.config.sensitive_headers:
                sanitized[key] = "***MASKED***"
            else:
                sanitized[key] = value
        
        return sanitized
    
    async def _log_request(self, context: RequestContext, request: Request):
        """Log incoming request."""
        if not self.config.log_requests:
            return
        
        log_data = {
            "event": "request_started",
            "level": LogLevel.INFO,
            "message": f"{context.method} {context.path}",
            **context.to_dict(),
        }
        
        # Add headers
        if self.config.log_headers:
            log_data["headers"] = self._sanitize_headers(dict(request.headers))
        
        # Add query parameters
        if self.config.log_query_params and request.query_params:
            log_data["query_params"] = dict(request.query_params)
        
        # Add request body
        if self.config.log_request_body:
            try:
                body = await request.body()
                if body and len(body) <= self.config.max_body_size:
                    if context.content_type.startswith("application/json"):
                        try:
                            body_data = json.loads(body.decode())
                            log_data["request_body"] = self._mask_sensitive_data(body_data)
                        except json.JSONDecodeError:
                            log_data["request_body"] = body.decode()[:500]  # First 500 chars
                    else:
                        log_data["request_body"] = body.decode()[:500]
            except Exception as e:
                log_data["request_body_error"] = str(e)
        
        await self._emit_log(log_data)
    
    async def _log_response(self, context: RequestContext, response: Response):
        """Log response."""
        if not self.config.log_responses:
            return
        
        level = LogLevel.INFO
        if context.status_code >= 400:
            level = LogLevel.WARNING if context.status_code < 500 else LogLevel.ERROR
        
        log_data = {
            "event": "request_completed",
            "level": level,
            "message": f"{context.method} {context.path} - {context.status_code}",
            **context.to_dict(),
        }
        
        # Add response headers
        if self.config.log_headers and response:
            log_data["response_headers"] = self._sanitize_headers(dict(response.headers))
        
        # Add response body for errors or if explicitly enabled
        if self.config.log_response_body or (context.status_code >= 400 and context.status_code < 500):
            try:
                if hasattr(response, "body"):
                    body = response.body
                    if isinstance(body, bytes) and len(body) <= self.config.max_body_size:
                        body_str = body.decode()
                        if response.headers.get("content-type", "").startswith("application/json"):
                            try:
                                body_data = json.loads(body_str)
                                log_data["response_body"] = self._mask_sensitive_data(body_data)
                            except json.JSONDecodeError:
                                log_data["response_body"] = body_str[:500]
                        else:
                            log_data["response_body"] = body_str[:500]
            except Exception as e:
                log_data["response_body_error"] = str(e)
        
        await self._emit_log(log_data)
    
    async def _log_error(self, context: RequestContext, error: Exception):
        """Log error with detailed information."""
        if not self.config.log_errors:
            return
        
        log_data = {
            "event": "request_error",
            "level": LogLevel.ERROR,
            "message": f"Error in {context.method} {context.path}: {str(error)}",
            "error_type": type(error).__name__,
            "error_message": str(error),
            **context.to_dict(),
        }
        
        # Add stack trace for debugging
        if self.config.log_stack_traces:
            log_data["stack_trace"] = traceback.format_exc()
        
        # Add custom exception details
        if isinstance(error, CustomException):
            log_data["error_code"] = error.error_code
            log_data["error_details"] = error.details
        
        await self._emit_log(log_data)
    
    async def _log_performance_metrics(self, context: RequestContext):
        """Log performance metrics."""
        if not self.config.log_performance_metrics or not context.processing_time:
            return
        
        # Define performance thresholds
        slow_threshold = 1.0  # 1 second
        very_slow_threshold = 5.0  # 5 seconds
        
        level = LogLevel.INFO
        if context.processing_time > very_slow_threshold:
            level = LogLevel.WARNING
        elif context.processing_time > slow_threshold:
            level = LogLevel.INFO
        
        log_data = {
            "event": "performance_metrics",
            "level": level,
            "message": f"Request took {context.processing_time:.3f}s",
            "performance": {
                "processing_time_ms": round(context.processing_time * 1000, 2),
                "is_slow": context.processing_time > slow_threshold,
                "is_very_slow": context.processing_time > very_slow_threshold,
            },
            **context.to_dict(),
        }
        
        await self._emit_log(log_data)
    
    async def _emit_log(self, log_data: Dict[str, Any]):
        """Emit log entry."""
        if self.log_buffer:
            await self.log_buffer.add(log_data)
        else:
            # Synchronous logging
            level = log_data.pop("level", LogLevel.INFO)
            message = log_data.pop("message", "Log entry")
            
            log_method = getattr(logger, level, logger.info)
            log_method(message, **log_data)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process logging middleware."""
        # Skip logging for excluded paths
        if self._is_path_excluded(request.url.path):
            return await call_next(request)
        
        # Create request context
        context = RequestContext(request)
        
        # Store context in request state
        request.state.request_id = context.request_id
        request.state.correlation_id = context.correlation_id
        
        try:
            # Log incoming request
            await self._log_request(context, request)
            
            # Process request
            response = await call_next(request)
            
            # Finish context
            context.finish(response)
            
            # Log response
            await self._log_response(context, response)
            
            # Log performance metrics
            await self._log_performance_metrics(context)
            
            return response
            
        except Exception as error:
            # Finish context with error
            error_response = JSONResponse(
                status_code=500,
                content={"error": "Internal server error", "request_id": context.request_id}
            )
            context.finish(error_response, error)
            
            # Log error
            await self._log_error(context, error)
            
            # Re-raise for upstream handling
            raise


# Context managers for custom logging
@asynccontextmanager
async def log_context(
    operation: str,
    **context_data
):
    """Context manager for logging operations with timing."""
    start_time = time.time()
    operation_id = str(uuid.uuid4())
    
    logger.info(
        f"Operation started: {operation}",
        operation_id=operation_id,
        operation=operation,
        **context_data
    )
    
    try:
        yield operation_id
        
        processing_time = time.time() - start_time
        logger.info(
            f"Operation completed: {operation}",
            operation_id=operation_id,
            operation=operation,
            processing_time_ms=round(processing_time * 1000, 2),
            **context_data
        )
        
    except Exception as error:
        processing_time = time.time() - start_time
        logger.error(
            f"Operation failed: {operation}",
            operation_id=operation_id,
            operation=operation,
            error=str(error),
            error_type=type(error).__name__,
            processing_time_ms=round(processing_time * 1000, 2),
            **context_data
        )
        raise


def get_request_logger(request: Request) -> structlog.BoundLogger:
    """Get a logger bound to request context."""
    request_id = getattr(request.state, "request_id", str(uuid.uuid4()))
    correlation_id = getattr(request.state, "correlation_id", request_id)
    
    return logger.bind(
        request_id=request_id,
        correlation_id=correlation_id,
    )


# Global middleware instance
def create_logging_middleware(
    config: Optional[LoggingConfig] = None,
    redis_service: Optional[RedisService] = None,
    **kwargs
) -> type:
    """
    Factory function to create logging middleware with configuration.
    
    Args:
        config: Logging configuration
        redis_service: Redis service instance
        **kwargs: Additional configuration options
        
    Returns:
        Configured logging middleware class
    """
    if config is None:
        config = LoggingConfig(**kwargs)
    
    class ConfiguredLoggingMiddleware(AdvancedLoggingMiddleware):
        def __init__(self, app):
            super().__init__(app, config=config, redis_service=redis_service)
    
    return ConfiguredLoggingMiddleware