"""
Base service classes and interfaces.
Provides common functionality for all external service integrations.
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, TypeVar, Generic
from datetime import datetime, timedelta

import structlog
from utils.config import settings
from utils.exceptions import ExternalServiceException, RateLimitException
from utils.helpers import retry_async


# Generic type for service responses
ResponseType = TypeVar('ResponseType')

logger = structlog.get_logger(__name__)


class BaseService(ABC):
    """
    Abstract base class for all external service integrations.
    Provides common functionality like rate limiting, error handling, and logging.
    """
    
    def __init__(self, service_name: str, rate_limit_per_minute: int = 60):
        self.service_name = service_name
        self.rate_limit_per_minute = rate_limit_per_minute
        self._request_timestamps: List[datetime] = []
        self.logger = structlog.get_logger(self.__class__.__name__)
    
    async def _check_rate_limit(self) -> None:
        """
        Check if the service rate limit is exceeded.
        
        Raises:
            RateLimitException: If rate limit is exceeded
        """
        now = datetime.utcnow()
        cutoff_time = now - timedelta(minutes=1)
        
        # Remove old timestamps
        self._request_timestamps = [
            ts for ts in self._request_timestamps 
            if ts > cutoff_time
        ]
        
        if len(self._request_timestamps) >= self.rate_limit_per_minute:
            raise RateLimitException(
                f"Rate limit exceeded for {self.service_name}",
                details={
                    "rate_limit": self.rate_limit_per_minute,
                    "window": "1 minute",
                    "requests_made": len(self._request_timestamps),
                }
            )
        
        # Record this request
        self._request_timestamps.append(now)
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        **kwargs: Any
    ) -> Any:
        """
        Make a rate-limited request to the external service.
        
        Args:
            method: HTTP method
            endpoint: API endpoint
            **kwargs: Additional request parameters
            
        Returns:
            Any: Service response
            
        Raises:
            ExternalServiceException: If the request fails
        """
        await self._check_rate_limit()
        
        self.logger.info(
            "Making API request",
            service=self.service_name,
            method=method,
            endpoint=endpoint,
        )
        
        try:
            response = await self._execute_request(method, endpoint, **kwargs)
            
            self.logger.info(
                "API request successful",
                service=self.service_name,
                method=method,
                endpoint=endpoint,
            )
            
            return response
            
        except Exception as e:
            self.logger.error(
                "API request failed",
                service=self.service_name,
                method=method,
                endpoint=endpoint,
                error=str(e),
            )
            
            raise ExternalServiceException(
                service_name=self.service_name,
                message=str(e),
                details={"method": method, "endpoint": endpoint}
            )
    
    @abstractmethod
    async def _execute_request(
        self,
        method: str,
        endpoint: str,
        **kwargs: Any
    ) -> Any:
        """
        Execute the actual HTTP request.
        Must be implemented by each service.
        
        Args:
            method: HTTP method
            endpoint: API endpoint
            **kwargs: Additional request parameters
            
        Returns:
            Any: Raw service response
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check for this service.
        
        Returns:
            Dict[str, Any]: Health check results
        """
        pass
    
    async def initialize(self) -> None:
        """
        Initialize the service (optional override).
        Called when the service is first created.
        """
        self.logger.info("Service initialized", service=self.service_name)
    
    async def cleanup(self) -> None:
        """
        Cleanup service resources (optional override).
        Called when the service is being destroyed.
        """
        self.logger.info("Service cleanup completed", service=self.service_name)


class AuthenticatedService(BaseService):
    """
    Base class for services that require authentication.
    """
    
    def __init__(
        self,
        service_name: str,
        rate_limit_per_minute: int = 60,
        auth_token: Optional[str] = None,
    ):
        super().__init__(service_name, rate_limit_per_minute)
        self._auth_token = auth_token
        self._token_expires_at: Optional[datetime] = None
    
    @property
    def is_authenticated(self) -> bool:
        """Check if the service is currently authenticated."""
        return self._auth_token is not None
    
    @property
    def token_expired(self) -> bool:
        """Check if the authentication token has expired."""
        if not self._token_expires_at:
            return False
        return datetime.utcnow() >= self._token_expires_at
    
    async def authenticate(self) -> None:
        """
        Authenticate with the service.
        Must be implemented by each authenticated service.
        """
        if self.is_authenticated and not self.token_expired:
            return
        
        self.logger.info("Authenticating with service", service=self.service_name)
        
        try:
            await self._perform_authentication()
            self.logger.info("Authentication successful", service=self.service_name)
        except Exception as e:
            self.logger.error(
                "Authentication failed",
                service=self.service_name,
                error=str(e),
            )
            raise ExternalServiceException(
                service_name=self.service_name,
                message=f"Authentication failed: {str(e)}"
            )
    
    @abstractmethod
    async def _perform_authentication(self) -> None:
        """
        Perform the actual authentication process.
        Must be implemented by each authenticated service.
        """
        pass
    
    async def _make_authenticated_request(
        self,
        method: str,
        endpoint: str,
        **kwargs: Any
    ) -> Any:
        """
        Make an authenticated request to the service.
        
        Args:
            method: HTTP method
            endpoint: API endpoint
            **kwargs: Additional request parameters
            
        Returns:
            Any: Service response
        """
        await self.authenticate()
        
        # Add authentication headers
        headers = kwargs.get("headers", {})
        headers.update(self._get_auth_headers())
        kwargs["headers"] = headers
        
        return await self._make_request(method, endpoint, **kwargs)
    
    @abstractmethod
    def _get_auth_headers(self) -> Dict[str, str]:
        """
        Get authentication headers for requests.
        Must be implemented by each authenticated service.
        
        Returns:
            Dict[str, str]: Authentication headers
        """
        pass


class CacheableService(BaseService):
    """
    Base class for services that support caching.
    """
    
    def __init__(
        self,
        service_name: str,
        rate_limit_per_minute: int = 60,
        cache_ttl_seconds: int = 300,
    ):
        super().__init__(service_name, rate_limit_per_minute)
        self.cache_ttl_seconds = cache_ttl_seconds
        self._cache: Dict[str, Dict[str, Any]] = {}
    
    def _get_cache_key(self, method: str, endpoint: str, **kwargs: Any) -> str:
        """
        Generate a cache key for the request.
        
        Args:
            method: HTTP method
            endpoint: API endpoint
            **kwargs: Request parameters
            
        Returns:
            str: Cache key
        """
        import hashlib
        import json
        
        # Create a consistent cache key
        cache_data = {
            "method": method,
            "endpoint": endpoint,
            "params": kwargs.get("params", {}),
        }
        
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_entry: Dict[str, Any]) -> bool:
        """
        Check if a cache entry is still valid.
        
        Args:
            cache_entry: Cache entry with timestamp and data
            
        Returns:
            bool: True if cache is still valid
        """
        cached_at = cache_entry.get("cached_at")
        if not cached_at:
            return False
        
        age_seconds = (datetime.utcnow() - cached_at).total_seconds()
        return age_seconds < self.cache_ttl_seconds
    
    async def _make_cached_request(
        self,
        method: str,
        endpoint: str,
        use_cache: bool = True,
        **kwargs: Any
    ) -> Any:
        """
        Make a request with caching support.
        
        Args:
            method: HTTP method
            endpoint: API endpoint
            use_cache: Whether to use caching
            **kwargs: Additional request parameters
            
        Returns:
            Any: Service response (cached or fresh)
        """
        cache_key = self._get_cache_key(method, endpoint, **kwargs)
        
        # Check cache first
        if use_cache and cache_key in self._cache:
            cache_entry = self._cache[cache_key]
            if self._is_cache_valid(cache_entry):
                self.logger.debug(
                    "Cache hit",
                    service=self.service_name,
                    cache_key=cache_key,
                )
                return cache_entry["data"]
        
        # Make fresh request
        response = await self._make_request(method, endpoint, **kwargs)
        
        # Cache the response
        if use_cache:
            self._cache[cache_key] = {
                "data": response,
                "cached_at": datetime.utcnow(),
            }
            
            self.logger.debug(
                "Response cached",
                service=self.service_name,
                cache_key=cache_key,
            )
        
        return response
    
    def clear_cache(self) -> None:
        """Clear all cached responses."""
        self._cache.clear()
        self.logger.info("Cache cleared", service=self.service_name)


class ServiceManager:
    """
    Manager for all external services.
    Handles service lifecycle and provides centralized access.
    """
    
    def __init__(self):
        self._services: Dict[str, BaseService] = {}
        self.logger = structlog.get_logger(__name__)
    
    def register_service(self, name: str, service: BaseService) -> None:
        """
        Register a service with the manager.
        
        Args:
            name: Service name
            service: Service instance
        """
        self._services[name] = service
        self.logger.info("Service registered", service_name=name)
    
    def get_service(self, name: str) -> Optional[BaseService]:
        """
        Get a service by name.
        
        Args:
            name: Service name
            
        Returns:
            Optional[BaseService]: Service instance or None if not found
        """
        return self._services.get(name)
    
    async def initialize_all(self) -> None:
        """Initialize all registered services."""
        self.logger.info("Initializing all services")
        
        for name, service in self._services.items():
            try:
                await service.initialize()
            except Exception as e:
                self.logger.error(
                    "Service initialization failed",
                    service_name=name,
                    error=str(e),
                )
        
        self.logger.info("Service initialization completed")
    
    async def cleanup_all(self) -> None:
        """Cleanup all registered services."""
        self.logger.info("Cleaning up all services")
        
        for name, service in self._services.items():
            try:
                await service.cleanup()
            except Exception as e:
                self.logger.error(
                    "Service cleanup failed",
                    service_name=name,
                    error=str(e),
                )
        
        self.logger.info("Service cleanup completed")
    
    async def health_check_all(self) -> Dict[str, Dict[str, Any]]:
        """
        Perform health checks for all services.
        
        Returns:
            Dict[str, Dict[str, Any]]: Health check results for all services
        """
        results = {}
        
        for name, service in self._services.items():
            try:
                result = await service.health_check()
                results[name] = {
                    "healthy": True,
                    "details": result,
                }
            except Exception as e:
                results[name] = {
                    "healthy": False,
                    "error": str(e),
                }
        
        return results


# Global service manager instance
service_manager = ServiceManager()