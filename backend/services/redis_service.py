"""
Redis service integration.
Handles caching, session management, and task queue operations.
"""

import asyncio
import json
import pickle
import time
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta

import redis.asyncio as redis
import structlog

from .base import BaseService
from utils.config import settings
from utils.exceptions import ExternalServiceException
from utils.helpers import retry_async, utc_now


class RedisService(BaseService):
    """
    Service for Redis operations.
    Handles caching, session storage, and distributed task coordination.
    """
    
    def __init__(self):
        super().__init__(
            service_name="Redis",
            rate_limit_per_minute=10000,  # Redis can handle high throughput
        )
        
        self._client: Optional[redis.Redis] = None
        self._redis_url = settings.REDIS_URL
        
        # Default cache TTL (Time To Live) in seconds
        self._default_ttl = 3600  # 1 hour
    
    async def _execute_request(self, method: str, endpoint: str, **kwargs: Any) -> Any:
        """Execute Redis request using the client."""
        # This method is not used for Redis client
        # Individual methods handle client calls directly
        pass
    
    async def initialize(self) -> None:
        """Initialize Redis client and test connection."""
        try:
            # Create Redis client from URL
            self._client = redis.from_url(
                self._redis_url,
                encoding="utf-8",
                decode_responses=False,  # We'll handle encoding/decoding manually
            )
            
            # Test connection
            await self._test_connection()
            
            self.logger.info("Redis client initialized successfully")
            
        except Exception as e:
            self.logger.error("Redis initialization failed", error=str(e))
            raise ExternalServiceException(
                service_name=self.service_name,
                message=f"Initialization failed: {str(e)}"
            )
    
    async def _test_connection(self) -> None:
        """Test Redis connection with a simple ping."""
        try:
            result = await self._client.ping()
            if result:
                self.logger.info("Redis connection test successful")
            else:
                raise Exception("Ping returned False")
                
        except Exception as e:
            self.logger.error("Redis connection test failed", error=str(e))
            raise ExternalServiceException(
                service_name=self.service_name,
                message=f"Connection test failed: {str(e)}"
            )
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform Redis health check."""
        try:
            if not self._client:
                await self.initialize()
            
            start_time = datetime.utcnow()
            
            # Test basic operations
            test_key = "health_check_test"
            test_value = "test_value"
            
            # Set a test value
            await self._client.set(test_key, test_value, ex=10)  # 10 second expiry
            
            # Get the test value
            retrieved_value = await self._client.get(test_key)
            
            # Clean up
            await self._client.delete(test_key)
            
            operation_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Get Redis info
            info = await self._client.info()
            
            return {
                "status": "healthy",
                "operation_time_seconds": operation_time,
                "connected_clients": info.get("connected_clients", 0),
                "used_memory_human": info.get("used_memory_human", "unknown"),
                "redis_version": info.get("redis_version", "unknown"),
                "last_check": datetime.utcnow().isoformat(),
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "last_check": datetime.utcnow().isoformat(),
            }
    
    async def cleanup(self) -> None:
        """Cleanup Redis client resources."""
        if self._client:
            await self._client.close()
            self.logger.info("Redis client connection closed")
    
    @retry_async(max_attempts=3, delay=0.5, backoff=2.0)
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        serialize: bool = True,
    ) -> bool:
        """
        Set a value in Redis with optional TTL.
        
        Args:
            key: Redis key
            value: Value to store
            ttl: Time to live in seconds (uses default if None)
            serialize: Whether to serialize the value as JSON
            
        Returns:
            bool: True if set was successful
            
        Raises:
            ExternalServiceException: If operation fails
        """
        if not self._client:
            await self.initialize()
        
        try:
            # Serialize value if requested
            if serialize:
                if isinstance(value, (dict, list)):
                    value = json.dumps(value)
                elif not isinstance(value, (str, bytes, int, float)):
                    value = pickle.dumps(value)
            
            # Use default TTL if not specified
            ttl = ttl or self._default_ttl
            
            result = await self._client.set(key, value, ex=ttl)
            
            self.logger.debug(
                "Redis set operation successful",
                key=key,
                ttl=ttl,
                serialized=serialize,
            )
            
            return bool(result)
            
        except Exception as e:
            error_message = f"Failed to set key {key}: {str(e)}"
            self.logger.error("Redis set operation failed", error=error_message)
            raise ExternalServiceException(
                service_name=self.service_name,
                message=error_message
            )
    
    @retry_async(max_attempts=3, delay=0.5, backoff=2.0)
    async def get(
        self,
        key: str,
        deserialize: bool = True,
        default: Any = None,
    ) -> Any:
        """
        Get a value from Redis.
        
        Args:
            key: Redis key
            deserialize: Whether to deserialize the value
            default: Default value if key doesn't exist
            
        Returns:
            Any: Retrieved value or default
            
        Raises:
            ExternalServiceException: If operation fails
        """
        if not self._client:
            await self.initialize()
        
        try:
            value = await self._client.get(key)
            
            if value is None:
                return default
            
            # Deserialize value if requested
            if deserialize:
                try:
                    # Try JSON first
                    if isinstance(value, bytes):
                        value = value.decode('utf-8')
                    
                    if isinstance(value, str) and (value.startswith('{') or value.startswith('[')):
                        value = json.loads(value)
                    elif isinstance(value, bytes):
                        # Try pickle for complex objects
                        try:
                            value = pickle.loads(value)
                        except:
                            # If pickle fails, return as string
                            value = value.decode('utf-8')
                except Exception:
                    # If deserialization fails, return raw value
                    pass
            
            self.logger.debug(
                "Redis get operation successful",
                key=key,
                has_value=value is not None,
                deserialized=deserialize,
            )
            
            return value
            
        except Exception as e:
            error_message = f"Failed to get key {key}: {str(e)}"
            self.logger.error("Redis get operation failed", error=error_message)
            raise ExternalServiceException(
                service_name=self.service_name,
                message=error_message
            )
    
    @retry_async(max_attempts=3, delay=0.5, backoff=2.0)
    async def delete(self, *keys: str) -> int:
        """
        Delete one or more keys from Redis.
        
        Args:
            *keys: Keys to delete
            
        Returns:
            int: Number of keys deleted
            
        Raises:
            ExternalServiceException: If operation fails
        """
        if not self._client:
            await self.initialize()
        
        try:
            result = await self._client.delete(*keys)
            
            self.logger.debug(
                "Redis delete operation successful",
                keys=keys,
                deleted_count=result,
            )
            
            return result
            
        except Exception as e:
            error_message = f"Failed to delete keys {keys}: {str(e)}"
            self.logger.error("Redis delete operation failed", error=error_message)
            raise ExternalServiceException(
                service_name=self.service_name,
                message=error_message
            )
    
    @retry_async(max_attempts=3, delay=0.5, backoff=2.0)
    async def exists(self, *keys: str) -> int:
        """
        Check if keys exist in Redis.
        
        Args:
            *keys: Keys to check
            
        Returns:
            int: Number of keys that exist
            
        Raises:
            ExternalServiceException: If operation fails
        """
        if not self._client:
            await self.initialize()
        
        try:
            result = await self._client.exists(*keys)
            
            self.logger.debug(
                "Redis exists operation successful",
                keys=keys,
                exists_count=result,
            )
            
            return result
            
        except Exception as e:
            error_message = f"Failed to check existence of keys {keys}: {str(e)}"
            self.logger.error("Redis exists operation failed", error=error_message)
            raise ExternalServiceException(
                service_name=self.service_name,
                message=error_message
            )
    
    @retry_async(max_attempts=3, delay=0.5, backoff=2.0)
    async def expire(self, key: str, seconds: int) -> bool:
        """
        Set expiration time for a key.
        
        Args:
            key: Redis key
            seconds: Expiration time in seconds
            
        Returns:
            bool: True if expiration was set
            
        Raises:
            ExternalServiceException: If operation fails
        """
        if not self._client:
            await self.initialize()
        
        try:
            result = await self._client.expire(key, seconds)
            
            self.logger.debug(
                "Redis expire operation successful",
                key=key,
                seconds=seconds,
                result=result,
            )
            
            return bool(result)
            
        except Exception as e:
            error_message = f"Failed to set expiration for key {key}: {str(e)}"
            self.logger.error("Redis expire operation failed", error=error_message)
            raise ExternalServiceException(
                service_name=self.service_name,
                message=error_message
            )
    
    async def cache_campaign_metrics(
        self,
        campaign_id: str,
        metrics: Dict[str, Any],
        ttl: int = 300,  # 5 minutes default
    ) -> bool:
        """
        Cache campaign performance metrics.
        
        Args:
            campaign_id: Campaign identifier
            metrics: Metrics data to cache
            ttl: Cache TTL in seconds
            
        Returns:
            bool: True if caching was successful
        """
        cache_key = f"campaign_metrics:{campaign_id}"
        
        cache_data = {
            "campaign_id": campaign_id,
            "metrics": metrics,
            "cached_at": utc_now().isoformat(),
        }
        
        return await self.set(cache_key, cache_data, ttl=ttl)
    
    async def get_cached_campaign_metrics(
        self,
        campaign_id: str,
    ) -> Optional[Dict[str, Any]]:
        """
        Get cached campaign metrics.
        
        Args:
            campaign_id: Campaign identifier
            
        Returns:
            Optional[Dict[str, Any]]: Cached metrics or None
        """
        cache_key = f"campaign_metrics:{campaign_id}"
        return await self.get(cache_key)
    
    async def invalidate_campaign_cache(self, campaign_id: str) -> int:
        """
        Invalidate all cached data for a campaign.
        
        Args:
            campaign_id: Campaign identifier
            
        Returns:
            int: Number of keys deleted
        """
        # Find all keys related to this campaign
        pattern = f"*{campaign_id}*"
        
        try:
            if not self._client:
                await self.initialize()
            
            # Get all matching keys
            keys = []
            async for key in self._client.scan_iter(match=pattern):
                keys.append(key)
            
            if keys:
                return await self.delete(*keys)
            else:
                return 0
                
        except Exception as e:
            self.logger.error(
                "Failed to invalidate campaign cache",
                campaign_id=campaign_id,
                error=str(e),
            )
            return 0
    
    @retry_async(max_attempts=3, delay=0.5, backoff=2.0)
    async def sliding_window_rate_limit(
        self,
        identifier: str,
        limit: int,
        window_seconds: int,
        weight: int = 1,
    ) -> Dict[str, Any]:
        """Advanced sliding window rate limiting.
        
        Args:
            identifier: Rate limit identifier (e.g., IP, user ID)
            limit: Request limit
            window_seconds: Time window in seconds
            weight: Weight of this request (default: 1)
            
        Returns:
            Dict containing rate limit status and metadata
        """
        if not self._client:
            await self.initialize()
        
        key = f"rate_limit:sliding:{identifier}"
        current_time = time.time()
        window_start = current_time - window_seconds
        
        try:
            # Use Redis pipeline for atomic operations
            pipe = self._client.pipeline()
            
            # Remove expired entries
            pipe.zremrangebyscore(key, 0, window_start)
            
            # Count current requests
            pipe.zcard(key)
            
            # Get current window usage
            pipe.zrangebyscore(key, window_start, current_time, withscores=True)
            
            results = await pipe.execute()
            current_count = results[1]
            current_requests = results[2]
            
            # Calculate weighted count
            weighted_count = sum(weight for _, _ in current_requests)
            
            # Check if limit would be exceeded
            new_weighted_count = weighted_count + weight
            allowed = new_weighted_count <= limit
            
            if allowed:
                # Add current request with weight
                score = current_time
                await self._client.zadd(key, {f"{current_time}:{weight}": score})
                await self._client.expire(key, window_seconds + 1)
            
            # Calculate reset time
            if current_requests:
                oldest_request_time = min(score for _, score in current_requests)
                reset_time = int(oldest_request_time + window_seconds)
            else:
                reset_time = int(current_time + window_seconds)
            
            return {
                "allowed": allowed,
                "limit": limit,
                "remaining": max(0, limit - int(new_weighted_count)),
                "reset_time": reset_time,
                "current_count": int(new_weighted_count),
                "window_seconds": window_seconds,
                "identifier": identifier,
            }
            
        except Exception as e:
            error_message = f"Sliding window rate limit failed for {identifier}: {str(e)}"
            self.logger.error("Redis sliding window rate limit failed", error=error_message)
            # Return permissive response on Redis failure
            return {
                "allowed": True,
                "limit": limit,
                "remaining": limit,
                "reset_time": int(current_time + window_seconds),
                "current_count": 0,
                "window_seconds": window_seconds,
                "identifier": identifier,
                "error": "Redis unavailable"
            }
    
    @retry_async(max_attempts=3, delay=0.5, backoff=2.0)
    async def hierarchical_rate_limit(
        self,
        identifier: str,
        limits: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """Hierarchical rate limiting with multiple time windows.
        
        Args:
            identifier: Rate limit identifier
            limits: List of limit configurations [{"limit": int, "window_seconds": int, "name": str}]
            
        Returns:
            Dict containing rate limit status for all limits
        """
        results = {}
        overall_allowed = True
        most_restrictive = None
        
        for limit_config in limits:
            limit = limit_config["limit"]
            window_seconds = limit_config["window_seconds"]
            name = limit_config.get("name", f"{window_seconds}s")
            
            result = await self.sliding_window_rate_limit(
                f"{identifier}:{name}",
                limit,
                window_seconds
            )
            
            results[name] = result
            
            if not result["allowed"]:
                overall_allowed = False
                if most_restrictive is None or result["reset_time"] > most_restrictive["reset_time"]:
                    most_restrictive = result
        
        return {
            "allowed": overall_allowed,
            "limits": results,
            "most_restrictive": most_restrictive,
            "identifier": identifier,
        }
    
    async def set_rate_limit(
        self,
        identifier: str,
        limit: int,
        window_seconds: int,
    ) -> bool:
        """
        Set rate limiting data.
        
        Args:
            identifier: Rate limit identifier (e.g., IP, user ID)
            limit: Request limit
            window_seconds: Time window in seconds
            
        Returns:
            bool: True if rate limit was set
        """
        key = f"rate_limit:{identifier}"
        current_time = int(datetime.utcnow().timestamp())
        
        # Use Redis pipeline for atomic operations
        pipe = self._client.pipeline()
        
        # Remove expired entries
        pipe.zremrangebyscore(key, 0, current_time - window_seconds)
        
        # Count current requests
        pipe.zcard(key)
        
        # Add current request
        pipe.zadd(key, {str(current_time): current_time})
        
        # Set expiration
        pipe.expire(key, window_seconds)
        
        results = await pipe.execute()
        current_count = results[1] + 1  # +1 for the request we just added
        
        return current_count <= limit
    
    async def get_rate_limit_status(
        self,
        identifier: str,
        window_seconds: int,
    ) -> Dict[str, Any]:
        """
        Get current rate limit status.
        
        Args:
            identifier: Rate limit identifier
            window_seconds: Time window in seconds
            
        Returns:
            Dict[str, Any]: Rate limit status
        """
        key = f"rate_limit:{identifier}"
        current_time = int(datetime.utcnow().timestamp())
        
        # Clean up expired entries and count current requests
        pipe = self._client.pipeline()
        pipe.zremrangebyscore(key, 0, current_time - window_seconds)
        pipe.zcard(key)
        
        results = await pipe.execute()
        current_count = results[1]
        
        return {
            "identifier": identifier,
            "current_count": current_count,
            "window_seconds": window_seconds,
            "reset_time": current_time + window_seconds,
        }
    
    async def store_session(
        self,
        session_id: str,
        session_data: Dict[str, Any],
        ttl: int = 86400,  # 24 hours default
    ) -> bool:
        """
        Store session data.
        
        Args:
            session_id: Session identifier
            session_data: Session data to store
            ttl: Session TTL in seconds
            
        Returns:
            bool: True if session was stored successfully
        """
        key = f"session:{session_id}"
        
        session_record = {
            "session_id": session_id,
            "data": session_data,
            "created_at": utc_now().isoformat(),
            "expires_at": (utc_now() + timedelta(seconds=ttl)).isoformat(),
        }
        
        return await self.set(key, session_record, ttl=ttl)
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get session data.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Optional[Dict[str, Any]]: Session data or None if not found
        """
        key = f"session:{session_id}"
        session_record = await self.get(key)
        
        if session_record and isinstance(session_record, dict):
            return session_record.get("data")
        
        return None
    
    async def update_session(
        self,
        session_id: str,
        session_data: Dict[str, Any],
        extend_ttl: bool = True,
        ttl: int = 86400,
    ) -> bool:
        """
        Update session data.
        
        Args:
            session_id: Session identifier
            session_data: Updated session data
            extend_ttl: Whether to extend the TTL
            ttl: New TTL in seconds (if extending)
            
        Returns:
            bool: True if session was updated successfully
        """
        key = f"session:{session_id}"
        
        # Get existing session to preserve creation time
        existing_session = await self.get(key)
        created_at = utc_now().isoformat()
        
        if existing_session and isinstance(existing_session, dict):
            created_at = existing_session.get("created_at", created_at)
        
        session_record = {
            "session_id": session_id,
            "data": session_data,
            "created_at": created_at,
            "updated_at": utc_now().isoformat(),
            "expires_at": (utc_now() + timedelta(seconds=ttl)).isoformat(),
        }
        
        session_ttl = ttl if extend_ttl else None
        return await self.set(key, session_record, ttl=session_ttl)
    
    async def delete_session(self, session_id: str) -> bool:
        """
        Delete session data.
        
        Args:
            session_id: Session identifier
            
        Returns:
            bool: True if session was deleted
        """
        key = f"session:{session_id}"
        result = await self.delete(key)
        return result > 0
    
    async def list_active_sessions(
        self,
        pattern: str = "session:*",
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        List active sessions.
        
        Args:
            pattern: Key pattern to match
            limit: Maximum number of sessions to return
            
        Returns:
            List[Dict[str, Any]]: List of active sessions
        """
        try:
            if not self._client:
                await self.initialize()
            
            sessions = []
            count = 0
            
            async for key in self._client.scan_iter(match=pattern):
                if count >= limit:
                    break
                
                session_record = await self.get(key.decode('utf-8') if isinstance(key, bytes) else key)
                if session_record and isinstance(session_record, dict):
                    sessions.append({
                        "session_id": session_record.get("session_id"),
                        "created_at": session_record.get("created_at"),
                        "updated_at": session_record.get("updated_at"),
                        "expires_at": session_record.get("expires_at"),
                    })
                
                count += 1
            
            return sessions
            
        except Exception as e:
            self.logger.error(
                "Failed to list active sessions",
                error=str(e),
            )
            return []
    
    async def cache_google_ads_data(
        self,
        cache_key: str,
        data: Dict[str, Any],
        ttl: int = 300,  # 5 minutes default
    ) -> bool:
        """
        Cache Google Ads API data.
        
        Args:
            cache_key: Cache key identifier
            data: Data to cache
            ttl: Cache TTL in seconds
            
        Returns:
            bool: True if caching was successful
        """
        key = f"google_ads:{cache_key}"
        
        cache_data = {
            "data": data,
            "cached_at": utc_now().isoformat(),
            "expires_at": (utc_now() + timedelta(seconds=ttl)).isoformat(),
        }
        
        return await self.set(key, cache_data, ttl=ttl)
    
    async def get_cached_google_ads_data(
        self,
        cache_key: str,
    ) -> Optional[Dict[str, Any]]:
        """
        Get cached Google Ads data.
        
        Args:
            cache_key: Cache key identifier
            
        Returns:
            Optional[Dict[str, Any]]: Cached data or None
        """
        key = f"google_ads:{cache_key}"
        cache_record = await self.get(key)
        
        if cache_record and isinstance(cache_record, dict):
            return cache_record.get("data")
        
        return None
    
    async def increment_counter(
        self,
        key: str,
        increment: int = 1,
        ttl: Optional[int] = None,
    ) -> int:
        """
        Increment a counter value.
        
        Args:
            key: Counter key
            increment: Amount to increment by
            ttl: Optional TTL for the counter
            
        Returns:
            int: New counter value
        """
        if not self._client:
            await self.initialize()
        
        try:
            # Use pipeline for atomic operations
            pipe = self._client.pipeline()
            pipe.incrby(key, increment)
            
            if ttl:
                pipe.expire(key, ttl)
            
            results = await pipe.execute()
            new_value = results[0]
            
            self.logger.debug(
                "Counter incremented",
                key=key,
                increment=increment,
                new_value=new_value,
            )
            
            return new_value
            
        except Exception as e:
            error_message = f"Failed to increment counter {key}: {str(e)}"
            self.logger.error("Redis counter increment failed", error=error_message)
            raise ExternalServiceException(
                service_name=self.service_name,
                message=error_message
            )
    
    async def get_counter(self, key: str) -> int:
        """
        Get counter value.
        
        Args:
            key: Counter key
            
        Returns:
            int: Counter value (0 if not found)
        """
        try:
            value = await self.get(key, deserialize=False, default=0)
            return int(value) if value is not None else 0
        except (ValueError, TypeError):
            return 0
    
    async def reset_counter(self, key: str) -> bool:
        """
        Reset counter to zero.
        
        Args:
            key: Counter key
            
        Returns:
            bool: True if counter was reset
        """
        return await self.set(key, 0, serialize=False)
    
    async def batch_operations(
        self,
        operations: List[Dict[str, Any]],
    ) -> List[Any]:
        """
        Execute multiple Redis operations in a batch.
        
        Args:
            operations: List of operations to execute
                Each operation should be a dict with:
                - 'operation': str - The operation type (set, get, delete, etc.)
                - 'key': str - The key to operate on
                - 'value': Any - The value (for set operations)
                - 'ttl': int - TTL for set operations (optional)
            
        Returns:
            List[Any]: Results of all operations
        """
        if not self._client:
            await self.initialize()
        
        try:
            pipe = self._client.pipeline()
            
            for op in operations:
                operation = op.get('operation')
                key = op.get('key')
                
                if operation == 'set':
                    value = op.get('value')
                    ttl = op.get('ttl')
                    if ttl:
                        pipe.set(key, value, ex=ttl)
                    else:
                        pipe.set(key, value)
                elif operation == 'get':
                    pipe.get(key)
                elif operation == 'delete':
                    pipe.delete(key)
                elif operation == 'exists':
                    pipe.exists(key)
                elif operation == 'expire':
                    ttl = op.get('ttl', 3600)
                    pipe.expire(key, ttl)
            
            results = await pipe.execute()
            
            self.logger.debug(
                "Batch operations completed",
                operation_count=len(operations),
                results_count=len(results),
            )
            
            return results
            
        except Exception as e:
            error_message = f"Batch operations failed: {str(e)}"
            self.logger.error("Redis batch operations failed", error=error_message)
            raise ExternalServiceException(
                service_name=self.service_name,
                message=error_message
            )


# Global Redis service instance
redis_service = RedisService()