"""
Agent Management Service for AiLex Ad Agent System.
Provides service layer to bridge FastAPI endpoints with CrewAI orchestration system.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from dataclasses import asdict

import structlog
from fastapi import HTTPException

from agents.factory import agent_factory, AgentRole
from agents.orchestration import orchestrator
from agents.communication import communication_hub
from agents.base import AgentContext, AgentMessage
from models.agents import (
    AgentType, AgentStatus, AgentConfig, TaskStatus, TaskPriority,
    Agent, AgentCreate, AgentUpdate, AgentTask, TaskCreate, TaskUpdate,
    AgentCommand, AgentMetrics
)
from utils.config import settings


logger = structlog.get_logger(__name__)


class AgentManagementService:
    """
    Service layer for managing AI agents and orchestrating workflows.
    Bridges FastAPI endpoints with the CrewAI orchestration system.
    """
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
        
        # Core orchestration components  
        self.orchestrator = orchestrator
        self.communication_hub = communication_hub
        
        # Service state
        self.active_workflows: Dict[str, Dict[str, Any]] = {}
        self.task_queue: asyncio.Queue = asyncio.Queue()
        self.task_results: Dict[str, Dict[str, Any]] = {}
        
        # Performance tracking
        self.service_metrics = {
            "agents_created": 0,
            "workflows_executed": 0,
            "tasks_completed": 0,
            "tasks_failed": 0,
            "uptime_start": datetime.utcnow()
        }
        
        # Initialize service
        asyncio.create_task(self._initialize_service())
    
    async def _initialize_service(self) -> None:
        """Initialize the agent management service."""
        try:
            # Initialize orchestrator with agent factory
            if self.orchestrator.agent_factory is None:
                self.orchestrator.agent_factory = agent_factory
            
            # Start orchestrator and communication hub
            await self.orchestrator.start()
            await self.communication_hub.start()
            
            # Create default agent pool
            await self._create_default_agent_pool()
            
            # Set up workflow templates
            await self._setup_workflow_templates()
            
            self.logger.info("Agent management service initialized successfully")
            
        except Exception as e:
            self.logger.error("Failed to initialize agent management service", error=str(e))
            raise
    
    async def _create_default_agent_pool(self) -> None:
        """Create a default pool of agents for immediate availability."""
        # Create one agent of each type for the full 8-agent team
        default_agents = [
            (AgentRole.PROJECT_ORCHESTRATOR, 1),
            (AgentRole.CAMPAIGN_PLANNER, 1),
            (AgentRole.ASSET_GENERATOR, 1),
            (AgentRole.SOFTWARE_ENGINEER_FULLSTACK, 1),
            (AgentRole.SOFTWARE_ENGINEER_BACKEND, 1),
            (AgentRole.SECURITY_REVIEWER, 1),
            (AgentRole.FRONTEND_UX_EXPERT, 1),
            (AgentRole.MIDDLEWARE_VALIDATOR, 1),
            (AgentRole.INFRA_DEPLOYMENT_SPECIALIST, 1)
        ]
        
        for role, count in default_agents:
            for i in range(count):
                try:
                    agent_id = await agent_factory.create_agent(role)
                    
                    # Register agent with communication hub
                    agent_instance = await agent_factory.get_agent(agent_id)
                    if agent_instance:
                        self.communication_hub.register_agent(agent_instance, role.value)
                    
                    self.logger.info("Created default agent", agent_id=agent_id, role=role.value)
                    self.service_metrics["agents_created"] += 1
                except Exception as e:
                    self.logger.warning("Failed to create default agent", role=role.value, error=str(e))
    
    async def _setup_workflow_templates(self) -> None:
        """Set up standard workflow templates in the orchestrator."""
        # Create sample Google Ads campaign workflow
        sample_workflow_id = await self.orchestrator.create_full_team_workflow(
            name="Complete Google Ads Campaign Management",
            description="End-to-end Google Ads campaign creation and optimization using all 8 specialized agents",
            google_ads_context={
                "campaign_id": "template_campaign",
                "user_id": "system",
                "budget": 10000,
                "target_audience": "Small business owners",
                "business_objectives": ["increase_leads", "improve_brand_awareness"]
            }
        )
        
        self.logger.info("Registered sample workflow template", workflow_id=sample_workflow_id)
    
    # Agent Management Methods
    
    async def create_agent(self, agent_data: AgentCreate) -> Agent:
        """
        Create a new agent instance.
        
        Args:
            agent_data: Agent creation data
            
        Returns:
            Agent: Created agent model
            
        Raises:
            HTTPException: If agent creation fails
        """
        try:
            self.logger.info("Creating new agent", name=agent_data.name, type=agent_data.type.value)
            
            # Map AgentType to AgentRole
            role_mapping = {
                AgentType.CAMPAIGN_PLANNING: AgentRole.CAMPAIGN_PLANNER,
                AgentType.AD_ASSET_GENERATION: AgentRole.ASSET_GENERATOR,
                AgentType.KEYWORD_RESEARCH: AgentRole.CAMPAIGN_PLANNER,
                AgentType.BID_OPTIMIZATION: AgentRole.CAMPAIGN_PLANNER,
                AgentType.PERFORMANCE_ANALYSIS: AgentRole.CAMPAIGN_PLANNER,
                AgentType.QUALITY_ASSURANCE: AgentRole.SECURITY_REVIEWER
            }
            
            agent_role = role_mapping.get(agent_data.type, AgentRole.CAMPAIGN_PLANNER)
            
            # Create agent through factory
            agent_id = await agent_factory.create_agent(
                role=agent_role,
                config_overrides={
                    "verbose": agent_data.config.verbose,
                    "max_iterations": agent_data.config.max_iterations,
                    "timeout_seconds": agent_data.config.timeout_seconds
                }
            )
            
            # Register with orchestrator
            agent_instance = await agent_factory.get_agent(agent_id)
            if agent_instance:
                self.flow_orchestrator.register_agent(agent_instance)
            
            # Create response model
            agent_model = Agent(
                id=agent_id,
                name=agent_data.name,
                description=agent_data.description,
                type=agent_data.type,
                status=AgentStatus.IDLE,
                config=agent_data.config,
                campaign_id=agent_data.campaign_id,
                tasks_completed=0,
                tasks_failed=0,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.service_metrics["agents_created"] += 1
            
            self.logger.info("Agent created successfully", agent_id=agent_id, name=agent_data.name)
            return agent_model
            
        except Exception as e:
            self.logger.error("Agent creation failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Failed to create agent: {str(e)}")
    
    async def get_agent(self, agent_id: str) -> Optional[Agent]:
        """
        Get agent by ID.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            Agent: Agent model or None if not found
        """
        try:
            # Get agent from factory
            agent_instances = await agent_factory.list_agents()
            
            for agent_info in agent_instances:
                if agent_info["agent_id"] == agent_id:
                    return Agent(
                        id=agent_id,
                        name=agent_info["role"],
                        description=f"Agent for {agent_info['role']}",
                        type=AgentType.CAMPAIGN_PLANNING,  # Default mapping
                        status=AgentStatus(agent_info["status"]),
                        config=AgentConfig(
                            model=None,  # Would need to retrieve from actual instance
                            max_iterations=10,
                            timeout_seconds=300,
                            verbose=False
                        ),
                        tasks_completed=agent_info["performance_metrics"]["tasks_completed"],
                        tasks_failed=agent_info["performance_metrics"]["tasks_failed"],
                        created_at=datetime.fromisoformat(agent_info["created_at"]),
                        updated_at=datetime.fromisoformat(agent_info["last_activity"])
                    )
            
            return None
            
        except Exception as e:
            self.logger.error("Failed to get agent", agent_id=agent_id, error=str(e))
            return None
    
    async def list_agents(
        self,
        campaign_id: Optional[str] = None,
        agent_type: Optional[AgentType] = None,
        status: Optional[AgentStatus] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Agent]:
        """
        List agents with optional filters.
        
        Args:
            campaign_id: Filter by campaign ID
            agent_type: Filter by agent type
            status: Filter by status
            limit: Maximum number of agents to return
            offset: Number of agents to skip
            
        Returns:
            List[Agent]: List of agent models
        """
        try:
            # Get agents from factory
            agent_instances = await agent_factory.list_agents()
            
            agents = []
            for agent_info in agent_instances[offset:offset + limit]:
                # Apply filters
                if status and agent_info["status"] != status.value:
                    continue
                
                agent = Agent(
                    id=agent_info["agent_id"],
                    name=agent_info["role"],
                    description=f"Agent for {agent_info['role']}",
                    type=AgentType.CAMPAIGN_PLANNING,  # Default mapping
                    status=AgentStatus(agent_info["status"]),
                    config=AgentConfig(
                        model=None,
                        max_iterations=10,
                        timeout_seconds=300,
                        verbose=False
                    ),
                    tasks_completed=agent_info["performance_metrics"]["tasks_completed"],
                    tasks_failed=agent_info["performance_metrics"]["tasks_failed"],
                    created_at=datetime.fromisoformat(agent_info["created_at"]),
                    updated_at=datetime.fromisoformat(agent_info["last_activity"])
                )
                
                agents.append(agent)
            
            return agents
            
        except Exception as e:
            self.logger.error("Failed to list agents", error=str(e))
            return []
    
    async def update_agent(self, agent_id: str, agent_update: AgentUpdate) -> Optional[Agent]:
        """
        Update agent configuration.
        
        Args:
            agent_id: Agent identifier
            agent_update: Update data
            
        Returns:
            Agent: Updated agent model or None if not found
        """
        try:
            # Update agent status if provided
            if agent_update.status:
                success = await agent_factory.update_agent_status(agent_id, agent_update.status)
                if not success:
                    raise HTTPException(status_code=404, detail="Agent not found")
            
            # Get updated agent
            return await self.get_agent(agent_id)
            
        except Exception as e:
            self.logger.error("Failed to update agent", agent_id=agent_id, error=str(e))
            return None
    
    async def delete_agent(self, agent_id: str) -> bool:
        """
        Delete agent instance.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            bool: True if agent was deleted successfully
        """
        try:
            success = await agent_factory.remove_agent(agent_id)
            if success:
                self.logger.info("Agent deleted successfully", agent_id=agent_id)
            return success
            
        except Exception as e:
            self.logger.error("Failed to delete agent", agent_id=agent_id, error=str(e))
            return False
    
    # Task Management Methods
    
    async def create_task(self, task_data: TaskCreate) -> AgentTask:
        """
        Create and assign a task to an agent.
        
        Args:
            task_data: Task creation data
            
        Returns:
            AgentTask: Created task model
            
        Raises:
            HTTPException: If task creation fails
        """
        try:
            self.logger.info(
                "Creating new task",
                agent_id=task_data.agent_id,
                task_name=task_data.name,
                task_type=task_data.type
            )
            
            # Generate task ID
            task_id = f"task_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{task_data.agent_id[-8:]}"
            
            # Get agent instance
            agent_instance = await agent_factory.get_agent(task_data.agent_id)
            if not agent_instance:
                raise HTTPException(status_code=404, detail="Agent not found")
            
            # Create task model
            task = AgentTask(
                id=task_id,
                agent_id=task_data.agent_id,
                campaign_id=task_data.campaign_id,
                name=task_data.name,
                description=task_data.description,
                type=task_data.type,
                priority=task_data.priority,
                status=TaskStatus.PENDING,
                input_data=task_data.input_data,
                context=task_data.context,
                scheduled_at=task_data.scheduled_at,
                deadline=task_data.deadline,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            # Assign task to agent
            await agent_factory.assign_task(task_data.agent_id, task_id)
            
            # Queue task for execution
            await self.task_queue.put({
                "task_id": task_id,
                "task": task,
                "agent_id": task_data.agent_id
            })
            
            self.logger.info("Task created and queued", task_id=task_id, agent_id=task_data.agent_id)
            return task
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error("Task creation failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Failed to create task: {str(e)}")
    
    async def get_task(self, task_id: str) -> Optional[AgentTask]:
        """
        Get task by ID.
        
        Args:
            task_id: Task identifier
            
        Returns:
            AgentTask: Task model or None if not found
        """
        return self.task_results.get(task_id, {}).get("task")
    
    async def list_tasks(
        self,
        agent_id: Optional[str] = None,
        campaign_id: Optional[str] = None,
        status: Optional[TaskStatus] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[AgentTask]:
        """
        List tasks with optional filters.
        
        Args:
            agent_id: Filter by agent ID
            campaign_id: Filter by campaign ID
            status: Filter by status
            limit: Maximum number of tasks to return
            offset: Number of tasks to skip
            
        Returns:
            List[AgentTask]: List of task models
        """
        tasks = []
        
        for task_result in list(self.task_results.values())[offset:offset + limit]:
            task = task_result.get("task")
            if not task:
                continue
            
            # Apply filters
            if agent_id and task.agent_id != agent_id:
                continue
            if campaign_id and task.campaign_id != campaign_id:
                continue
            if status and task.status != status:
                continue
            
            tasks.append(task)
        
        return tasks
    
    async def update_task(self, task_id: str, task_update: TaskUpdate) -> Optional[AgentTask]:
        """
        Update task status and data.
        
        Args:
            task_id: Task identifier
            task_update: Update data
            
        Returns:
            AgentTask: Updated task model or None if not found
        """
        task_result = self.task_results.get(task_id)
        if not task_result:
            return None
        
        task = task_result["task"]
        
        # Update fields
        if task_update.status:
            task.status = task_update.status
        if task_update.priority:
            task.priority = task_update.priority
        if task_update.output_data:
            task.output_data = task_update.output_data
        if task_update.result:
            task.result = task_update.result
        if task_update.error_message:
            task.error_message = task_update.error_message
        
        task.updated_at = datetime.utcnow()
        
        return task
    
    # Workflow Management Methods
    
    async def execute_workflow(
        self,
        workflow_type: str,
        input_data: Dict[str, Any],
        campaign_id: Optional[str] = None
    ) -> str:
        """
        Execute a workflow using the enhanced orchestrator.
        
        Args:
            workflow_type: Type of workflow to execute
            input_data: Input data for workflow
            campaign_id: Optional campaign ID
            
        Returns:
            str: Workflow execution ID
            
        Raises:
            HTTPException: If workflow execution fails
        """
        try:
            self.logger.info(
                "Starting workflow execution",
                workflow_type=workflow_type,
                campaign_id=campaign_id
            )
            
            # Map workflow type to orchestrator methods
            if workflow_type == "full_campaign":
                # Execute full campaign workflow with all 8 agents
                workflow_id = await self.orchestrator.create_full_team_workflow(
                    name=f"Google Ads Campaign: {input_data.get('campaign_name', 'Untitled')}",
                    description=f"Complete campaign management for {campaign_id or 'new campaign'}",
                    google_ads_context={
                        "campaign_id": campaign_id,
                        "user_id": input_data.get("user_id"),
                        "budget": input_data.get("budget", 5000),
                        "target_audience": input_data.get("target_audience"),
                        "business_objectives": input_data.get("business_objectives", []),
                        "campaign_type": input_data.get("campaign_type", "search"),
                        **input_data
                    }
                )
                
                # Execute the workflow
                await self.orchestrator.execute_workflow(workflow_id)
                execution_id = workflow_id
                
            elif workflow_type == "crew_workflow":
                # Create and execute a CrewAI crew
                workflow_id = await self.orchestrator.create_full_team_workflow(
                    name=f"CrewAI Workflow: {input_data.get('name', 'Untitled')}",
                    description=input_data.get("description", "CrewAI multi-agent collaboration"),
                    google_ads_context=input_data
                )
                
                crew_id = await self.orchestrator.create_crew_for_workflow(workflow_id)
                execution_result = await self.orchestrator.execute_crew_workflow(crew_id)
                execution_id = crew_id
                
            else:
                # Create custom workflow
                workflow_id = await self.orchestrator.create_workflow(
                    name=f"Custom Workflow: {workflow_type}",
                    description=f"Custom workflow execution for {workflow_type}",
                    tasks=input_data.get("tasks", []),
                    context=AgentContext(
                        campaign_id=campaign_id,
                        user_id=input_data.get("user_id"),
                        metadata={"workflow_type": workflow_type}
                    )
                )
                
                await self.orchestrator.execute_workflow(workflow_id)
                execution_id = workflow_id
            
            # Track workflow
            self.active_workflows[execution_id] = {
                "workflow_type": workflow_type,
                "workflow_id": workflow_id,
                "campaign_id": campaign_id,
                "started_at": datetime.utcnow(),
                "input_data": input_data
            }
            
            self.service_metrics["workflows_executed"] += 1
            
            self.logger.info(
                "Workflow execution started",
                execution_id=execution_id,
                workflow_type=workflow_type
            )
            
            return execution_id
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error("Workflow execution failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Failed to execute workflow: {str(e)}")
    
    async def create_google_ads_campaign_workflow(
        self,
        campaign_name: str,
        campaign_data: Dict[str, Any],
        user_id: str
    ) -> str:
        """
        Create a complete Google Ads campaign workflow using all 8 agents.
        
        Args:
            campaign_name: Name of the campaign
            campaign_data: Campaign configuration data
            user_id: User identifier
            
        Returns:
            str: Workflow execution ID
        """
        try:
            workflow_id = await self.orchestrator.create_full_team_workflow(
                name=f"Google Ads Campaign: {campaign_name}",
                description=f"Complete campaign setup and optimization for {campaign_name}",
                google_ads_context={
                    "user_id": user_id,
                    "campaign_name": campaign_name,
                    **campaign_data
                }
            )
            
            # Create and execute crew
            crew_id = await self.orchestrator.create_crew_for_workflow(
                workflow_id=workflow_id,
                crew_name=f"Campaign Team: {campaign_name}"
            )
            
            # Execute the crew workflow
            execution_result = await self.orchestrator.execute_crew_workflow(crew_id)
            
            # Track the workflow
            self.active_workflows[crew_id] = {
                "workflow_type": "google_ads_campaign",
                "workflow_id": workflow_id,
                "crew_id": crew_id,
                "campaign_name": campaign_name,
                "user_id": user_id,
                "started_at": datetime.utcnow(),
                "execution_result": execution_result
            }
            
            self.logger.info(
                "Google Ads campaign workflow created and executed",
                workflow_id=workflow_id,
                crew_id=crew_id,
                campaign_name=campaign_name
            )
            
            return crew_id
            
        except Exception as e:
            self.logger.error("Failed to create Google Ads campaign workflow", error=str(e))
            raise HTTPException(status_code=500, detail=f"Failed to create campaign workflow: {str(e)}")
    
    async def get_workflow_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """
        Get workflow execution status from enhanced orchestrator.
        
        Args:
            execution_id: Workflow execution ID
            
        Returns:
            Dict[str, Any]: Workflow status or None if not found
        """
        try:
            # Get status from orchestrator
            workflow_status = await self.orchestrator.get_workflow_status(execution_id)
            if not workflow_status:
                # Try crew status
                crew_status = await self.orchestrator.get_crew_status(execution_id)
                if crew_status:
                    workflow_info = self.active_workflows.get(execution_id, {})
                    return {
                        "execution_id": execution_id,
                        "workflow_type": workflow_info.get("workflow_type", "crew_workflow"),
                        "crew_info": crew_status,
                        "started_at": workflow_info.get("started_at", datetime.utcnow()).isoformat(),
                        "execution_result": workflow_info.get("execution_result", {})
                    }
                return None
            
            workflow_info = self.active_workflows.get(execution_id, {})
            
            return {
                "execution_id": execution_id,
                "workflow_type": workflow_info.get("workflow_type"),
                "status": workflow_status.get("status"),
                "total_tasks": workflow_status.get("total_tasks", 0),
                "completed_tasks": workflow_status.get("completed_tasks", 0),
                "failed_tasks": workflow_status.get("failed_tasks", 0),
                "running_tasks": workflow_status.get("running_tasks", 0),
                "started_at": workflow_status.get("started_at"),
                "completed_at": workflow_status.get("completed_at"),
                "execution_metrics": workflow_status.get("execution_metrics", {}),
                "workflow_info": workflow_info
            }
            
        except Exception as e:
            self.logger.error("Failed to get workflow status", execution_id=execution_id, error=str(e))
            return None
    
    async def cancel_workflow(self, execution_id: str) -> bool:
        """
        Cancel workflow execution.
        
        Args:
            execution_id: Workflow execution ID
            
        Returns:
            bool: True if workflow was cancelled successfully
        """
        try:
            success = await self.orchestrator.cancel_workflow(execution_id)
            if success and execution_id in self.active_workflows:
                del self.active_workflows[execution_id]
            return success
            
        except Exception as e:
            self.logger.error("Failed to cancel workflow", execution_id=execution_id, error=str(e))
            return False
    
    # Command Execution Methods
    
    async def execute_agent_command(
        self,
        agent_id: str,
        command: AgentCommand
    ) -> Dict[str, Any]:
        """
        Execute a command on a specific agent.
        
        Args:
            agent_id: Agent identifier
            command: Command to execute
            
        Returns:
            Dict[str, Any]: Command execution result
            
        Raises:
            HTTPException: If command execution fails
        """
        try:
            self.logger.info(
                "Executing agent command",
                agent_id=agent_id,
                command=command.command
            )
            
            # Get agent instance
            agent_instance = await agent_factory.get_agent(agent_id)
            if not agent_instance:
                raise HTTPException(status_code=404, detail="Agent not found")
            
            # Create command message
            message = AgentMessage(
                sender_id="agent_service",
                recipient_id=agent_id,
                message_type="command",
                content={
                    "command": command.command,
                    "parameters": command.parameters,
                    "timeout": command.timeout_seconds
                },
                requires_response=True,
                priority=command.priority.value
            )
            
            # Send command to agent
            response = await agent_instance.handle_message(message)
            
            result = {
                "command": command.command,
                "agent_id": agent_id,
                "executed_at": datetime.utcnow().isoformat(),
                "success": True,
                "response": response.content if response else None
            }
            
            self.logger.info("Agent command executed successfully", agent_id=agent_id, command=command.command)
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error("Agent command execution failed", agent_id=agent_id, error=str(e))
            raise HTTPException(status_code=500, detail=f"Failed to execute command: {str(e)}")
    
    # Metrics and Monitoring Methods
    
    async def get_agent_metrics(self, agent_id: str) -> Optional[AgentMetrics]:
        """
        Get performance metrics for a specific agent.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            AgentMetrics: Agent performance metrics or None if not found
        """
        try:
            # Get agent info from factory
            agent_instances = await agent_factory.list_agents()
            
            for agent_info in agent_instances:
                if agent_info["agent_id"] == agent_id:
                    performance = agent_info["performance_metrics"]
                    
                    return AgentMetrics(
                        agent_id=agent_id,
                        period_start=datetime.fromisoformat(agent_info["created_at"]),
                        period_end=datetime.utcnow(),
                        total_tasks=performance["tasks_completed"] + performance["tasks_failed"],
                        completed_tasks=performance["tasks_completed"],
                        failed_tasks=performance["tasks_failed"],
                        success_rate=performance["success_rate"],
                        average_execution_time=performance["average_execution_time"],
                        uptime_percentage=95.0  # Mock value
                    )
            
            return None
            
        except Exception as e:
            self.logger.error("Failed to get agent metrics", agent_id=agent_id, error=str(e))
            return None
    
    async def get_system_metrics(self) -> Dict[str, Any]:
        """
        Get system-wide metrics from enhanced system.
        
        Returns:
            Dict[str, Any]: System metrics
        """
        try:
            # Get metrics from factory
            factory_metrics = await agent_factory.get_system_metrics()
            
            # Get orchestrator metrics
            orchestrator_metrics = await self.orchestrator.get_orchestrator_metrics()
            
            # Get communication metrics
            communication_metrics = self.communication_hub.get_communication_stats()
            
            # Combine with service metrics
            uptime = (datetime.utcnow() - self.service_metrics["uptime_start"]).total_seconds()
            
            return {
                "service_uptime_seconds": uptime,
                "agents": factory_metrics,
                "orchestrator": orchestrator_metrics,
                "communication": communication_metrics,
                "service_metrics": self.service_metrics,
                "active_workflows": len(self.active_workflows),
                "queued_tasks": self.task_queue.qsize(),
                "completed_tasks": len(self.task_results),
                "system_health": {
                    "orchestrator_running": self.orchestrator.is_running,
                    "communication_hub_running": self.communication_hub.is_running,
                    "total_agents": factory_metrics.get("total_agents", 0),
                    "active_workflows": orchestrator_metrics.get("active_workflows", 0),
                    "crew_efficiency": orchestrator_metrics.get("crew_efficiency", 0.0)
                }
            }
            
        except Exception as e:
            self.logger.error("Failed to get system metrics", error=str(e))
            return {}
    
    async def get_workflow_recommendations(
        self,
        campaign_id: str
    ) -> List[Dict[str, Any]]:
        """
        Get workflow recommendations for a campaign.
        
        Args:
            campaign_id: Campaign identifier
            
        Returns:
            List[Dict[str, Any]]: Workflow recommendations
        """
        try:
            # Mock campaign data and performance metrics
            # In real implementation, these would come from database/Google Ads API
            campaign_data = {
                "campaign_id": campaign_id,
                "created_date": (datetime.utcnow() - timedelta(days=45)).isoformat(),
                "last_optimization": None
            }
            
            performance_metrics = {
                "ctr": 1.5,  # Low CTR
                "conversion_rate": 2.0,  # Low conversion rate
                "cpc": 2.50,
                "roas": 3.0  # Low ROAS
            }
            
            recommendations = workflow_builder.get_workflow_recommendations(
                campaign_data,
                performance_metrics
            )
            
            return recommendations
            
        except Exception as e:
            self.logger.error("Failed to get workflow recommendations", campaign_id=campaign_id, error=str(e))
            return []
    
    # Cleanup and Management Methods
    
    async def cleanup_completed_workflows(self, older_than_hours: int = 24) -> int:
        """
        Clean up completed workflow executions older than specified hours.
        
        Args:
            older_than_hours: Remove workflows completed more than this many hours ago
            
        Returns:
            int: Number of workflows cleaned up
        """
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=older_than_hours)
            cleaned_count = 0
            
            workflows_to_remove = []
            for execution_id, workflow_info in self.active_workflows.items():
                if workflow_info.get("completed_at"):
                    completed_time = datetime.fromisoformat(workflow_info["completed_at"])
                    if completed_time < cutoff_time:
                        workflows_to_remove.append(execution_id)
            
            for execution_id in workflows_to_remove:
                del self.active_workflows[execution_id]
                cleaned_count += 1
            
            self.logger.info("Cleaned up completed workflows", count=cleaned_count)
            return cleaned_count
            
        except Exception as e:
            self.logger.error("Failed to cleanup workflows", error=str(e))
            return 0
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check of the enhanced agent management service.
        
        Returns:
            Dict[str, Any]: Health check results
        """
        try:
            # Check core components
            checks = {
                "agent_factory": "healthy",
                "orchestrator": "healthy" if self.orchestrator.is_running else "unhealthy",
                "communication_hub": "healthy" if self.communication_hub.is_running else "unhealthy",
                "task_queue": "healthy",
                "database": "healthy"  # Would check actual database connection
            }
            
            # Get system metrics
            system_metrics = await self.get_system_metrics()
            
            overall_status = "healthy" if all(status == "healthy" for status in checks.values()) else "degraded"
            
            return {
                "status": overall_status,
                "timestamp": datetime.utcnow().isoformat(),
                "components": checks,
                "metrics": {
                    "total_agents": system_metrics.get("agents", {}).get("total_agents", 0),
                    "active_workflows": len(self.active_workflows),
                    "active_crews": system_metrics.get("orchestrator", {}).get("active_crews", 0),
                    "registered_agents": system_metrics.get("communication", {}).get("registered_agents", 0),
                    "queued_tasks": self.task_queue.qsize(),
                    "service_uptime_hours": (datetime.utcnow() - self.service_metrics["uptime_start"]).total_seconds() / 3600,
                    "crew_efficiency": system_metrics.get("orchestrator", {}).get("crew_efficiency", 0.0),
                    "message_throughput": system_metrics.get("communication", {}).get("messages_delivered", 0)
                },
                "agent_roles_available": [
                    "project_orchestrator",
                    "campaign_planner", 
                    "asset_generator",
                    "software_engineer_fullstack",
                    "software_engineer_backend",
                    "security_reviewer",
                    "frontend_ux_expert",
                    "middleware_validator",
                    "infra_deployment_specialist"
                ]
            }
            
        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e)
            }


# Global service instance
agent_service = AgentManagementService()