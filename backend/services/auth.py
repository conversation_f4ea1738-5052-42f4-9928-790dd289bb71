"""
Authentication service using Supabase Auth with email functionality via Resend.
Handles user authentication, password reset, and email verification.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from urllib.parse import urljoin
import secrets
import hashlib
import hmac

import resend
from supabase import create_client, Client
from gotrue.errors import AuthError
import structlog

from .base import AuthenticatedService
from utils.config import settings
from utils.exceptions import AuthenticationException, EmailServiceException
from utils.helpers import retry_async, utc_now


class AuthService(AuthenticatedService):
    """
    Authentication service using Supabase Auth with Resend for emails.
    Provides comprehensive authentication functionality including:
    - User registration and login
    - Password reset and email verification
    - Session management
    - Role-based access control
    """
    
    def __init__(self):
        super().__init__(
            service_name="Supabase Auth Service",
            rate_limit_per_minute=100,
        )
        
        # Supabase client
        self._supabase: Optional[Client] = None
        
        # Resend client
        self._resend_client: Optional[resend.Resend] = None
        
        # Configuration
        self._supabase_url = settings.database_url or settings.SUPABASE_URL
        # Use anon key for client authentication operations (signup, login)
        self._supabase_key = settings.SUPABASE_ANON_KEY or settings.database_service_key or settings.SUPABASE_SERVICE_ROLE_KEY
        self._resend_api_key = settings.RESEND_API_KEY
        self._from_email = settings.FROM_EMAIL
        self._from_name = settings.FROM_NAME
        self._secret_key = settings.SECRET_KEY
    
    async def _perform_authentication(self) -> None:
        """Initialize Supabase and Resend clients."""
        await self._initialize_supabase_client()
        await self._initialize_resend_client()
        
        self._auth_token = "authenticated"
        self.logger.info("Authentication service initialized successfully")
    
    async def _initialize_supabase_client(self) -> None:
        """Initialize Supabase client for authentication."""
        if not self._supabase_url or not self._supabase_key:
            raise AuthenticationException("Supabase URL or key not configured")
        
        try:
            # Extract HTTP URL for Supabase client
            if self._supabase_url.startswith('postgresql://'):
                # Convert PostgreSQL URL to HTTPS for Supabase client
                from urllib.parse import urlparse
                parsed = urlparse(self._supabase_url)
                supabase_url = f"https://{parsed.hostname.replace('db.', '')}.supabase.co"
            else:
                supabase_url = self._supabase_url
            
            self._supabase = create_client(supabase_url, self._supabase_key)
            
            # Test the connection
            await self._test_supabase_auth_connection()
            
            self.logger.info("Supabase Auth client initialized successfully")
            
        except Exception as e:
            self.logger.error("Supabase Auth client initialization failed", error=str(e))
            raise AuthenticationException(f"Supabase Auth client initialization failed: {str(e)}")
    
    async def _initialize_resend_client(self) -> None:
        """Initialize Resend client for email services."""
        if not self._resend_api_key:
            self.logger.warning("Resend API key not configured, email functionality will be disabled")
            return
        
        try:
            resend.api_key = self._resend_api_key
            self._resend_client = resend
            
            # Test the connection with a simple API call
            # Note: Resend doesn't have a ping endpoint, so we'll just verify the key format
            if not self._resend_api_key.startswith('re_'):
                raise EmailServiceException("Invalid Resend API key format")
            
            self.logger.info("Resend email client initialized successfully")
            
        except Exception as e:
            self.logger.error("Resend client initialization failed", error=str(e))
            raise EmailServiceException(f"Resend client initialization failed: {str(e)}")
    
    async def _test_supabase_auth_connection(self) -> None:
        """Test Supabase Auth connection."""
        try:
            # Try to get the current session (will fail gracefully if no session)
            response = self._supabase.auth.get_session()
            
            self.logger.info(
                "Supabase Auth connection test successful",
                has_session=response.session is not None,
            )
            
        except Exception as e:
            # Log warning but don't fail - this is expected for service role key
            self.logger.warning(
                "Supabase Auth connection test completed",
                note="Service role key doesn't have user session",
                error=str(e),
            )
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for API requests."""
        return {
            "Authorization": f"Bearer {self._supabase_key}",
            "apikey": self._supabase_key,
        }
    
    async def _execute_request(self, method: str, endpoint: str, **kwargs: Any) -> Any:
        """Execute authentication request."""
        # This method is not used for Supabase Auth
        # Individual methods handle auth operations directly
        pass
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def sign_up(
        self,
        email: str,
        password: str,
        user_metadata: Optional[Dict[str, Any]] = None,
        email_redirect_to: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Register a new user with email and password.
        
        Args:
            email: User's email address
            password: User's password
            user_metadata: Optional user metadata
            email_redirect_to: Optional redirect URL after email confirmation
            
        Returns:
            Dict[str, Any]: User registration result
            
        Raises:
            AuthenticationException: If registration fails
        """
        await self.authenticate()
        
        try:
            self.logger.info(
                "Creating new user account",
                email=email,
                has_metadata=user_metadata is not None,
            )
            
            # Sign up user with Supabase Auth
            response = self._supabase.auth.sign_up({
                "email": email,
                "password": password,
                "options": {
                    "data": user_metadata or {},
                    "email_redirect_to": email_redirect_to,
                }
            })
            
            if response.user is None:
                raise AuthenticationException("User registration failed - no user returned")
            
            user_data = {
                "id": response.user.id,
                "email": response.user.email,
                "email_confirmed_at": response.user.email_confirmed_at,
                "created_at": response.user.created_at,
                "user_metadata": response.user.user_metadata,
                "session": {
                    "access_token": response.session.access_token if response.session else None,
                    "refresh_token": response.session.refresh_token if response.session else None,
                    "expires_at": response.session.expires_at if response.session else None,
                } if response.session else None,
            }
            
            self.logger.info(
                "User registration successful",
                user_id=response.user.id,
                email=email,
                needs_confirmation=response.user.email_confirmed_at is None,
            )
            
            return user_data
            
        except AuthError as e:
            error_message = f"User registration failed: {str(e)}"
            self.logger.error("Supabase Auth registration failed", error=error_message, email=email)
            raise AuthenticationException(error_message)
        except Exception as e:
            error_message = f"Unexpected error during registration: {str(e)}"
            self.logger.error("Unexpected registration error", error=error_message, email=email)
            raise AuthenticationException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def sign_in(
        self,
        email: str,
        password: str,
    ) -> Dict[str, Any]:
        """
        Sign in user with email and password.
        
        Args:
            email: User's email address
            password: User's password
            
        Returns:
            Dict[str, Any]: User session data
            
        Raises:
            AuthenticationException: If sign in fails
        """
        await self.authenticate()
        
        try:
            self.logger.info("Attempting user sign in", email=email)
            
            # Sign in user with Supabase Auth
            response = self._supabase.auth.sign_in_with_password({
                "email": email,
                "password": password,
            })
            
            if response.user is None or response.session is None:
                raise AuthenticationException("Sign in failed - no user session returned")
            
            session_data = {
                "user": {
                    "id": response.user.id,
                    "email": response.user.email,
                    "email_confirmed_at": response.user.email_confirmed_at,
                    "last_sign_in_at": response.user.last_sign_in_at,
                    "user_metadata": response.user.user_metadata,
                },
                "session": {
                    "access_token": response.session.access_token,
                    "refresh_token": response.session.refresh_token,
                    "expires_at": response.session.expires_at,
                    "token_type": response.session.token_type,
                },
            }
            
            self.logger.info(
                "User sign in successful",
                user_id=response.user.id,
                email=email,
            )
            
            return session_data
            
        except AuthError as e:
            error_message = f"User sign in failed: {str(e)}"
            self.logger.error("Supabase Auth sign in failed", error=error_message, email=email)
            raise AuthenticationException(error_message)
        except Exception as e:
            error_message = f"Unexpected error during sign in: {str(e)}"
            self.logger.error("Unexpected sign in error", error=error_message, email=email)
            raise AuthenticationException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def sign_out(self, access_token: str) -> bool:
        """
        Sign out user and invalidate session.
        
        Args:
            access_token: User's access token
            
        Returns:
            bool: True if sign out was successful
            
        Raises:
            AuthenticationException: If sign out fails
        """
        await self.authenticate()
        
        try:
            self.logger.info("Attempting user sign out")
            
            # Set the session for the sign out operation
            self._supabase.auth.set_session(access_token, "")
            
            # Sign out user
            response = self._supabase.auth.sign_out()
            
            self.logger.info("User sign out successful")
            
            return True
            
        except AuthError as e:
            error_message = f"User sign out failed: {str(e)}"
            self.logger.error("Supabase Auth sign out failed", error=error_message)
            raise AuthenticationException(error_message)
        except Exception as e:
            error_message = f"Unexpected error during sign out: {str(e)}"
            self.logger.error("Unexpected sign out error", error=error_message)
            raise AuthenticationException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def reset_password(
        self,
        email: str,
        redirect_to: Optional[str] = None,
    ) -> bool:
        """
        Send password reset email to user.
        
        Args:
            email: User's email address
            redirect_to: Optional redirect URL after password reset
            
        Returns:
            bool: True if reset email was sent
            
        Raises:
            AuthenticationException: If password reset fails
        """
        await self.authenticate()
        
        try:
            self.logger.info("Sending password reset email", email=email)
            
            # Send password reset email via Supabase Auth
            response = self._supabase.auth.reset_password_email(
                email,
                {
                    "redirect_to": redirect_to,
                }
            )
            
            self.logger.info("Password reset email sent successfully", email=email)
            
            return True
            
        except AuthError as e:
            error_message = f"Password reset failed: {str(e)}"
            self.logger.error("Supabase Auth password reset failed", error=error_message, email=email)
            raise AuthenticationException(error_message)
        except Exception as e:
            error_message = f"Unexpected error during password reset: {str(e)}"
            self.logger.error("Unexpected password reset error", error=error_message, email=email)
            raise AuthenticationException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def update_password(
        self,
        access_token: str,
        new_password: str,
    ) -> Dict[str, Any]:
        """
        Update user's password.
        
        Args:
            access_token: User's access token
            new_password: New password
            
        Returns:
            Dict[str, Any]: Updated user data
            
        Raises:
            AuthenticationException: If password update fails
        """
        await self.authenticate()
        
        try:
            self.logger.info("Updating user password")
            
            # Set the session for the update operation
            self._supabase.auth.set_session(access_token, "")
            
            # Update password
            response = self._supabase.auth.update_user({
                "password": new_password,
            })
            
            if response.user is None:
                raise AuthenticationException("Password update failed - no user returned")
            
            user_data = {
                "id": response.user.id,
                "email": response.user.email,
                "updated_at": response.user.updated_at,
                "user_metadata": response.user.user_metadata,
            }
            
            self.logger.info(
                "Password update successful",
                user_id=response.user.id,
            )
            
            return user_data
            
        except AuthError as e:
            error_message = f"Password update failed: {str(e)}"
            self.logger.error("Supabase Auth password update failed", error=error_message)
            raise AuthenticationException(error_message)
        except Exception as e:
            error_message = f"Unexpected error during password update: {str(e)}"
            self.logger.error("Unexpected password update error", error=error_message)
            raise AuthenticationException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def get_user(self, access_token: str) -> Dict[str, Any]:
        """
        Get user information from access token.
        
        Args:
            access_token: User's access token
            
        Returns:
            Dict[str, Any]: User information
            
        Raises:
            AuthenticationException: If user retrieval fails
        """
        await self.authenticate()
        
        try:
            self.logger.info("Getting user information from token")
            
            # Set the session and get user
            self._supabase.auth.set_session(access_token, "")
            response = self._supabase.auth.get_user()
            
            if response.user is None:
                raise AuthenticationException("User retrieval failed - no user found")
            
            user_data = {
                "id": response.user.id,
                "email": response.user.email,
                "email_confirmed_at": response.user.email_confirmed_at,
                "phone_confirmed_at": response.user.phone_confirmed_at,
                "last_sign_in_at": response.user.last_sign_in_at,
                "created_at": response.user.created_at,
                "updated_at": response.user.updated_at,
                "user_metadata": response.user.user_metadata,
                "app_metadata": response.user.app_metadata,
            }
            
            self.logger.info(
                "User information retrieved successfully",
                user_id=response.user.id,
                email=response.user.email,
            )
            
            return user_data
            
        except AuthError as e:
            error_message = f"User retrieval failed: {str(e)}"
            self.logger.error("Supabase Auth user retrieval failed", error=error_message)
            raise AuthenticationException(error_message)
        except Exception as e:
            error_message = f"Unexpected error during user retrieval: {str(e)}"
            self.logger.error("Unexpected user retrieval error", error=error_message)
            raise AuthenticationException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def refresh_session(self, refresh_token: str) -> Dict[str, Any]:
        """
        Refresh user session using refresh token.
        
        Args:
            refresh_token: User's refresh token
            
        Returns:
            Dict[str, Any]: New session data
            
        Raises:
            AuthenticationException: If session refresh fails
        """
        await self.authenticate()
        
        try:
            self.logger.info("Refreshing user session")
            
            # Refresh session
            response = self._supabase.auth.refresh_session(refresh_token)
            
            if response.user is None or response.session is None:
                raise AuthenticationException("Session refresh failed - no session returned")
            
            session_data = {
                "user": {
                    "id": response.user.id,
                    "email": response.user.email,
                    "email_confirmed_at": response.user.email_confirmed_at,
                    "last_sign_in_at": response.user.last_sign_in_at,
                    "user_metadata": response.user.user_metadata,
                },
                "session": {
                    "access_token": response.session.access_token,
                    "refresh_token": response.session.refresh_token,
                    "expires_at": response.session.expires_at,
                    "token_type": response.session.token_type,
                },
            }
            
            self.logger.info(
                "Session refresh successful",
                user_id=response.user.id,
            )
            
            return session_data
            
        except AuthError as e:
            error_message = f"Session refresh failed: {str(e)}"
            self.logger.error("Supabase Auth session refresh failed", error=error_message)
            raise AuthenticationException(error_message)
        except Exception as e:
            error_message = f"Unexpected error during session refresh: {str(e)}"
            self.logger.error("Unexpected session refresh error", error=error_message)
            raise AuthenticationException(error_message)
    
    async def verify_jwt_token(self, access_token: str) -> Dict[str, Any]:
        """
        Verify JWT access token and extract user information.
        
        Args:
            access_token: JWT access token to verify
            
        Returns:
            Dict[str, Any]: User information from token
            
        Raises:
            AuthenticationException: If token verification fails
        """
        try:
            # Use get_user method which validates the token
            user_data = await self.get_user(access_token)
            
            self.logger.info(
                "JWT token verification successful",
                user_id=user_data.get("id"),
            )
            
            return user_data
            
        except Exception as e:
            error_message = f"JWT token verification failed: {str(e)}"
            self.logger.error("JWT token verification failed", error=error_message)
            raise AuthenticationException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def send_welcome_email(
        self,
        email: str,
        name: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Send welcome email to new user using Resend.
        
        Args:
            email: User's email address
            name: Optional user's name
            additional_data: Optional additional data for email template
            
        Returns:
            bool: True if email was sent successfully
            
        Raises:
            EmailServiceException: If email sending fails
        """
        if not self._resend_client:
            self.logger.warning("Resend client not configured, skipping welcome email")
            return False
        
        try:
            self.logger.info("Sending welcome email", email=email)
            
            subject = f"Welcome to {self._from_name}!"
            
            # Create HTML email content
            html_content = self._create_welcome_email_html(name or email, additional_data)
            
            # Send email via Resend
            params = {
                "from": f"{self._from_name} <{self._from_email}>",
                "to": [email],
                "subject": subject,
                "html": html_content,
            }
            
            response = self._resend_client.emails.send(params)
            
            self.logger.info(
                "Welcome email sent successfully",
                email=email,
                email_id=response.get("id") if isinstance(response, dict) else None,
            )
            
            return True
            
        except Exception as e:
            error_message = f"Failed to send welcome email: {str(e)}"
            self.logger.error("Welcome email send failed", error=error_message, email=email)
            raise EmailServiceException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def send_password_reset_email(
        self,
        email: str,
        reset_url: str,
        name: Optional[str] = None,
    ) -> bool:
        """
        Send custom password reset email using Resend.
        
        Args:
            email: User's email address
            reset_url: Password reset URL
            name: Optional user's name
            
        Returns:
            bool: True if email was sent successfully
            
        Raises:
            EmailServiceException: If email sending fails
        """
        if not self._resend_client:
            self.logger.warning("Resend client not configured, using Supabase default email")
            return True  # Supabase will handle it
        
        try:
            self.logger.info("Sending password reset email", email=email)
            
            subject = f"Reset Your {self._from_name} Password"
            
            # Create HTML email content
            html_content = self._create_password_reset_email_html(name or email, reset_url)
            
            # Send email via Resend
            params = {
                "from": f"{self._from_name} <{self._from_email}>",
                "to": [email],
                "subject": subject,
                "html": html_content,
            }
            
            response = self._resend_client.emails.send(params)
            
            self.logger.info(
                "Password reset email sent successfully",
                email=email,
                email_id=response.get("id") if isinstance(response, dict) else None,
            )
            
            return True
            
        except Exception as e:
            error_message = f"Failed to send password reset email: {str(e)}"
            self.logger.error("Password reset email send failed", error=error_message, email=email)
            raise EmailServiceException(error_message)
    
    def _create_welcome_email_html(self, name: str, additional_data: Optional[Dict[str, Any]] = None) -> str:
        """Create HTML content for welcome email."""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to {self._from_name}</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #2563eb;">Welcome to {self._from_name}!</h1>
            </div>
            
            <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <p>Hello {name},</p>
                
                <p>Thank you for joining {self._from_name}! We're excited to have you on board.</p>
                
                <p>Your account has been successfully created and you can now start using all our features:</p>
                
                <ul>
                    <li>Create and manage Google Ads campaigns</li>
                    <li>Use AI agents to optimize your advertising</li>
                    <li>Track performance metrics and analytics</li>
                    <li>Access advanced campaign management tools</li>
                </ul>
                
                <p>If you have any questions or need assistance getting started, don't hesitate to reach out to our support team.</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                <p style="color: #6b7280; font-size: 14px;">
                    Best regards,<br>
                    The {self._from_name} Team
                </p>
            </div>
        </body>
        </html>
        """
    
    def _create_password_reset_email_html(self, name: str, reset_url: str) -> str:
        """Create HTML content for password reset email."""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reset Your Password</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #dc2626;">Reset Your Password</h1>
            </div>
            
            <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <p>Hello {name},</p>
                
                <p>We received a request to reset your password for your {self._from_name} account.</p>
                
                <p>Click the button below to reset your password:</p>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{reset_url}" style="display: inline-block; background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">Reset Password</a>
                </div>
                
                <p>If the button doesn't work, you can also copy and paste this URL into your browser:</p>
                <p style="word-break: break-all; color: #6b7280; font-size: 14px;">{reset_url}</p>
                
                <p><strong>This link will expire in 1 hour.</strong></p>
                
                <p>If you didn't request this password reset, please ignore this email or contact our support team if you have concerns.</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                <p style="color: #6b7280; font-size: 14px;">
                    Best regards,<br>
                    The {self._from_name} Team
                </p>
            </div>
        </body>
        </html>
        """
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive authentication service health check."""
        health_status = {
            "status": "healthy",
            "checks": {},
            "timestamp": datetime.utcnow().isoformat(),
        }
        
        # Test Supabase Auth
        try:
            await self.authenticate()
            health_status["checks"]["supabase_auth"] = {
                "status": "healthy",
                "authenticated": True,
            }
        except Exception as e:
            health_status["checks"]["supabase_auth"] = {
                "status": "unhealthy",
                "error": str(e),
                "authenticated": False,
            }
            health_status["status"] = "unhealthy"
        
        # Test Resend email service
        if self._resend_client:
            try:
                # Resend doesn't have a health check endpoint, so we just verify the client exists
                health_status["checks"]["resend_email"] = {
                    "status": "healthy",
                    "configured": True,
                }
            except Exception as e:
                health_status["checks"]["resend_email"] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "configured": True,
                }
                if health_status["status"] == "healthy":
                    health_status["status"] = "degraded"
        else:
            health_status["checks"]["resend_email"] = {
                "status": "disabled",
                "configured": False,
                "note": "Resend API key not configured",
            }
        
        return health_status


# Global authentication service instance
auth_service = AuthService()# Force rebuild Sat Aug  9 14:55:26 CAT 2025
