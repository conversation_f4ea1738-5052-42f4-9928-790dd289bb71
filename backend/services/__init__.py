"""
Services package for the AiLex Ad Agent System.
Contains external API integrations and business logic services.
"""

from .base import service_manager

# Import services with error handling to prevent startup failures
google_ads_service = None
google_ads_auth_service = None
openai_service = None
database_service = None
redis_service = None
auth_service = None

try:
    from .database import database_service
except ImportError as e:
    import logging
    logging.warning(f"Failed to import database service: {e}")

try:
    from .redis_service import redis_service
except ImportError as e:
    import logging
    logging.warning(f"Failed to import Redis service: {e}")

try:
    from .auth import auth_service
except ImportError as e:
    import logging
    logging.warning(f"Failed to import Auth service: {e}")

try:
    from .google_ads import google_ads_service
    if google_ads_service:
        service_manager.register_service("google_ads", google_ads_service)
except ImportError as e:
    import logging
    logging.warning(f"Failed to import Google Ads service: {e}")

try:
    from .google_ads_auth import google_ads_auth_service
    if google_ads_auth_service:
        service_manager.register_service("google_ads_auth", google_ads_auth_service)
except ImportError as e:
    import logging
    logging.warning(f"Failed to import Google Ads Auth service: {e}")

try:
    from .openai_service import openai_service
    if openai_service:
        service_manager.register_service("openai", openai_service)
except ImportError as e:
    import logging
    logging.warning(f"Failed to import OpenAI service: {e}")

# Register available services
if database_service:
    service_manager.register_service("database", database_service)
if redis_service:
    service_manager.register_service("redis", redis_service)
if auth_service:
    service_manager.register_service("auth", auth_service)

__all__ = [
    "service_manager",
    "google_ads_service",
    "google_ads_auth_service",
    "openai_service", 
    "database_service",
    "redis_service",
    "auth_service",
]