"""
Google Ads OAuth2 authentication service.
Handles OAuth2 flow, token management, and multi-account authentication for Google Ads API.
"""

import asyncio
import json
import secrets
import time
from typing import Dict, Any, Optional, List
from urllib.parse import urlencode, parse_qs, urlparse
from datetime import datetime, timedelta

import httpx
import structlog
from pydantic import BaseModel, Field

from utils.config import settings
from utils.exceptions import GoogleAdsException as CustomGoogleAdsException
from services.database import database_service


logger = structlog.get_logger(__name__)


class GoogleAdsCredentials(BaseModel):
    """Model for Google Ads API credentials."""
    access_token: str = Field(..., description="OAuth2 access token")
    refresh_token: str = Field(..., description="OAuth2 refresh token")
    expires_at: datetime = Field(..., description="Token expiration timestamp")
    customer_id: str = Field(..., description="Google Ads customer ID")
    developer_token: str = Field(..., description="Google Ads developer token")
    client_id: str = Field(..., description="OAuth2 client ID")
    client_secret: str = Field(..., description="OAuth2 client secret")


class GoogleAdsAccount(BaseModel):
    """Model for Google Ads account information."""
    customer_id: str = Field(..., description="Customer account ID")
    descriptive_name: str = Field(..., description="Account descriptive name")
    currency_code: str = Field(..., description="Account currency code")
    time_zone: str = Field(..., description="Account time zone")
    manager: bool = Field(..., description="Whether this is a manager account")
    credentials: Optional[GoogleAdsCredentials] = Field(None, description="Account credentials")


class GoogleAdsAuthService:
    """
    Service for Google Ads OAuth2 authentication and token management.
    Supports multi-account authentication for agency use cases.
    """
    
    def __init__(self):
        self.logger = logger.bind(service="GoogleAdsAuthService")
        
        # OAuth2 endpoints
        self.auth_url = "https://accounts.google.com/o/oauth2/v2/auth"
        self.token_url = "https://oauth2.googleapis.com/token"
        self.revoke_url = "https://oauth2.googleapis.com/revoke"
        
        # OAuth2 configuration
        self.client_id = settings.GOOGLE_ADS_CLIENT_ID
        self.client_secret = settings.GOOGLE_ADS_CLIENT_SECRET
        self.developer_token = settings.GOOGLE_ADS_DEVELOPER_TOKEN
        
        # Required OAuth2 scopes for Google Ads API
        self.scopes = [
            "https://www.googleapis.com/auth/adwords"
        ]
        
        # In-memory cache for active credentials
        self._credentials_cache: Dict[str, GoogleAdsCredentials] = {}
        self._cache_lock = asyncio.Lock()
    
    def generate_auth_url(
        self,
        redirect_uri: str,
        state: Optional[str] = None
    ) -> Dict[str, str]:
        """
        Generate OAuth2 authorization URL.
        
        Args:
            redirect_uri: Redirect URI after authorization
            state: Optional state parameter for CSRF protection
            
        Returns:
            Dict[str, str]: Authorization URL and state
        """
        if not state:
            state = secrets.token_urlsafe(32)
        
        params = {
            "client_id": self.client_id,
            "redirect_uri": redirect_uri,
            "scope": " ".join(self.scopes),
            "response_type": "code",
            "access_type": "offline",
            "prompt": "consent",
            "state": state,
        }
        
        auth_url = f"{self.auth_url}?{urlencode(params)}"
        
        self.logger.info("Generated OAuth2 authorization URL", state=state)
        
        return {
            "auth_url": auth_url,
            "state": state,
        }
    
    async def exchange_code_for_tokens(
        self,
        authorization_code: str,
        redirect_uri: str,
        customer_id: Optional[str] = None,
    ) -> GoogleAdsCredentials:
        """
        Exchange authorization code for access and refresh tokens.
        
        Args:
            authorization_code: Authorization code from OAuth2 callback
            redirect_uri: Redirect URI used in authorization
            customer_id: Optional Google Ads customer ID
            
        Returns:
            GoogleAdsCredentials: OAuth2 credentials
            
        Raises:
            CustomGoogleAdsException: If token exchange fails
        """
        try:
            self.logger.info("Exchanging authorization code for tokens")
            
            data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "code": authorization_code,
                "grant_type": "authorization_code",
                "redirect_uri": redirect_uri,
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(self.token_url, data=data)
                response.raise_for_status()
                
                token_data = response.json()
            
            # Calculate token expiration
            expires_in = token_data.get("expires_in", 3600)
            expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
            
            credentials = GoogleAdsCredentials(
                access_token=token_data["access_token"],
                refresh_token=token_data["refresh_token"],
                expires_at=expires_at,
                customer_id=customer_id or settings.GOOGLE_ADS_CUSTOMER_ID or "",
                developer_token=self.developer_token,
                client_id=self.client_id,
                client_secret=self.client_secret,
            )
            
            # Cache credentials
            await self._cache_credentials(credentials)
            
            # Store credentials in database for persistence
            await self._store_credentials(credentials)
            
            self.logger.info(
                "OAuth2 token exchange successful",
                customer_id=credentials.customer_id,
                expires_at=expires_at.isoformat(),
            )
            
            return credentials
            
        except httpx.HTTPStatusError as e:
            error_data = e.response.json() if e.response.content else {}
            error_message = f"Token exchange failed: {error_data.get('error_description', str(e))}"
            self.logger.error("OAuth2 token exchange failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
        except Exception as e:
            error_message = f"Unexpected error during token exchange: {str(e)}"
            self.logger.error("OAuth2 token exchange failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    async def refresh_access_token(
        self,
        refresh_token: str,
        customer_id: str,
    ) -> GoogleAdsCredentials:
        """
        Refresh expired access token using refresh token.
        
        Args:
            refresh_token: OAuth2 refresh token
            customer_id: Google Ads customer ID
            
        Returns:
            GoogleAdsCredentials: Updated credentials with new access token
            
        Raises:
            CustomGoogleAdsException: If token refresh fails
        """
        try:
            self.logger.info("Refreshing access token", customer_id=customer_id)
            
            data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "refresh_token": refresh_token,
                "grant_type": "refresh_token",
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(self.token_url, data=data)
                response.raise_for_status()
                
                token_data = response.json()
            
            # Calculate token expiration
            expires_in = token_data.get("expires_in", 3600)
            expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
            
            credentials = GoogleAdsCredentials(
                access_token=token_data["access_token"],
                refresh_token=refresh_token,  # Keep existing refresh token
                expires_at=expires_at,
                customer_id=customer_id,
                developer_token=self.developer_token,
                client_id=self.client_id,
                client_secret=self.client_secret,
            )
            
            # Cache updated credentials
            await self._cache_credentials(credentials)
            
            # Update stored credentials
            await self._store_credentials(credentials)
            
            self.logger.info(
                "Access token refreshed successfully",
                customer_id=customer_id,
                expires_at=expires_at.isoformat(),
            )
            
            return credentials
            
        except httpx.HTTPStatusError as e:
            error_data = e.response.json() if e.response.content else {}
            error_message = f"Token refresh failed: {error_data.get('error_description', str(e))}"
            self.logger.error("OAuth2 token refresh failed", error=error_message, customer_id=customer_id)
            raise CustomGoogleAdsException(error_message)
        except Exception as e:
            error_message = f"Unexpected error during token refresh: {str(e)}"
            self.logger.error("OAuth2 token refresh failed", error=error_message, customer_id=customer_id)
            raise CustomGoogleAdsException(error_message)
    
    async def get_credentials(
        self,
        customer_id: str,
        auto_refresh: bool = True,
    ) -> Optional[GoogleAdsCredentials]:
        """
        Get credentials for a specific customer account.
        
        Args:
            customer_id: Google Ads customer ID
            auto_refresh: Whether to automatically refresh expired tokens
            
        Returns:
            Optional[GoogleAdsCredentials]: Credentials if available, None otherwise
            
        Raises:
            CustomGoogleAdsException: If auto-refresh fails
        """
        async with self._cache_lock:
            # Check cache first
            credentials = self._credentials_cache.get(customer_id)
            
            if not credentials:
                # Try to load from database
                credentials = await self._load_credentials(customer_id)
                
                if credentials:
                    self._credentials_cache[customer_id] = credentials
            
            if not credentials:
                return None
            
            # Check if token is expired
            if datetime.utcnow() >= credentials.expires_at:
                if auto_refresh:
                    self.logger.info("Access token expired, refreshing", customer_id=customer_id)
                    credentials = await self.refresh_access_token(
                        credentials.refresh_token,
                        customer_id,
                    )
                else:
                    self.logger.warning("Access token expired", customer_id=customer_id)
                    return None
            
            return credentials
    
    async def revoke_credentials(self, customer_id: str) -> bool:
        """
        Revoke OAuth2 credentials for a customer account.
        
        Args:
            customer_id: Google Ads customer ID
            
        Returns:
            bool: True if revocation was successful
            
        Raises:
            CustomGoogleAdsException: If revocation fails
        """
        try:
            credentials = await self.get_credentials(customer_id, auto_refresh=False)
            if not credentials:
                self.logger.warning("No credentials found for revocation", customer_id=customer_id)
                return True
            
            self.logger.info("Revoking OAuth2 credentials", customer_id=customer_id)
            
            # Revoke refresh token
            data = {"token": credentials.refresh_token}
            
            async with httpx.AsyncClient() as client:
                response = await client.post(self.revoke_url, data=data)
                response.raise_for_status()
            
            # Remove from cache and database
            await self._remove_credentials(customer_id)
            
            self.logger.info("OAuth2 credentials revoked successfully", customer_id=customer_id)
            return True
            
        except httpx.HTTPStatusError as e:
            error_message = f"Credential revocation failed: {str(e)}"
            self.logger.error("OAuth2 credential revocation failed", error=error_message, customer_id=customer_id)
            raise CustomGoogleAdsException(error_message)
        except Exception as e:
            error_message = f"Unexpected error during credential revocation: {str(e)}"
            self.logger.error("OAuth2 credential revocation failed", error=error_message, customer_id=customer_id)
            raise CustomGoogleAdsException(error_message)
    
    async def list_authenticated_accounts(self) -> List[GoogleAdsAccount]:
        """
        List all authenticated Google Ads accounts.
        
        Returns:
            List[GoogleAdsAccount]: List of authenticated accounts
        """
        try:
            # Get all stored credentials from database
            stored_credentials = await self._list_stored_credentials()
            
            accounts = []
            for customer_id, credentials in stored_credentials.items():
                # Get account details (this would require Google Ads API call)
                # For now, create a basic account entry
                account = GoogleAdsAccount(
                    customer_id=customer_id,
                    descriptive_name=f"Account {customer_id}",
                    currency_code="USD",  # Default
                    time_zone="America/New_York",  # Default
                    manager=False,  # Default
                    credentials=credentials,
                )
                accounts.append(account)
            
            self.logger.info(f"Found {len(accounts)} authenticated accounts")
            return accounts
            
        except Exception as e:
            error_message = f"Failed to list authenticated accounts: {str(e)}"
            self.logger.error("Failed to list authenticated accounts", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    async def _cache_credentials(self, credentials: GoogleAdsCredentials) -> None:
        """Cache credentials in memory."""
        async with self._cache_lock:
            self._credentials_cache[credentials.customer_id] = credentials
    
    async def _store_credentials(self, credentials: GoogleAdsCredentials) -> None:
        """Store credentials in database for persistence."""
        try:
            # Create a secure way to store credentials
            # Note: In production, credentials should be encrypted
            credential_data = {
                "customer_id": credentials.customer_id,
                "access_token": credentials.access_token,  # Should be encrypted
                "refresh_token": credentials.refresh_token,  # Should be encrypted
                "expires_at": credentials.expires_at.isoformat(),
                "developer_token": credentials.developer_token,  # Should be encrypted
                "client_id": credentials.client_id,
                "client_secret": credentials.client_secret,  # Should be encrypted
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
            }
            
            # Store in a dedicated table (would need to be created in database schema)
            # For now, we'll use a simple approach and store as JSON in settings table
            await database_service.execute_query(
                """
                INSERT INTO google_ads_credentials (customer_id, credentials_data, created_at, updated_at)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT (customer_id) 
                DO UPDATE SET 
                    credentials_data = $2,
                    updated_at = $4
                """,
                [
                    credentials.customer_id,
                    json.dumps(credential_data),
                    datetime.utcnow(),
                    datetime.utcnow(),
                ]
            )
            
        except Exception as e:
            self.logger.error("Failed to store credentials", error=str(e))
            # Don't raise exception as this is not critical for OAuth flow
    
    async def _load_credentials(self, customer_id: str) -> Optional[GoogleAdsCredentials]:
        """Load credentials from database."""
        try:
            result = await database_service.execute_query(
                "SELECT credentials_data FROM google_ads_credentials WHERE customer_id = $1",
                [customer_id]
            )
            
            if result:
                credential_data = json.loads(result[0]["credentials_data"])
                return GoogleAdsCredentials(
                    access_token=credential_data["access_token"],
                    refresh_token=credential_data["refresh_token"],
                    expires_at=datetime.fromisoformat(credential_data["expires_at"]),
                    customer_id=credential_data["customer_id"],
                    developer_token=credential_data["developer_token"],
                    client_id=credential_data["client_id"],
                    client_secret=credential_data["client_secret"],
                )
            
            return None
            
        except Exception as e:
            self.logger.error("Failed to load credentials", error=str(e), customer_id=customer_id)
            return None
    
    async def _list_stored_credentials(self) -> Dict[str, GoogleAdsCredentials]:
        """List all stored credentials."""
        try:
            results = await database_service.execute_query(
                "SELECT customer_id, credentials_data FROM google_ads_credentials"
            )
            
            credentials_dict = {}
            for row in results or []:
                customer_id = row["customer_id"]
                credential_data = json.loads(row["credentials_data"])
                
                credentials = GoogleAdsCredentials(
                    access_token=credential_data["access_token"],
                    refresh_token=credential_data["refresh_token"],
                    expires_at=datetime.fromisoformat(credential_data["expires_at"]),
                    customer_id=credential_data["customer_id"],
                    developer_token=credential_data["developer_token"],
                    client_id=credential_data["client_id"],
                    client_secret=credential_data["client_secret"],
                )
                
                credentials_dict[customer_id] = credentials
            
            return credentials_dict
            
        except Exception as e:
            self.logger.error("Failed to list stored credentials", error=str(e))
            return {}
    
    async def _remove_credentials(self, customer_id: str) -> None:
        """Remove credentials from cache and database."""
        try:
            # Remove from cache
            async with self._cache_lock:
                self._credentials_cache.pop(customer_id, None)
            
            # Remove from database
            await database_service.execute_query(
                "DELETE FROM google_ads_credentials WHERE customer_id = $1",
                [customer_id]
            )
            
        except Exception as e:
            self.logger.error("Failed to remove credentials", error=str(e), customer_id=customer_id)


# Global authentication service instance
google_ads_auth_service = GoogleAdsAuthService()