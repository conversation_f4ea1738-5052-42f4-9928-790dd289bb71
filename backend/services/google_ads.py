"""
Google Ads API service integration.
Handles all Google Ads API operations including campaign management, optimization, and reporting.
"""

import asyncio
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, date
import json
import time

from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
from google.api_core.exceptions import ResourceExhausted, DeadlineExceeded
import structlog

from .base import AuthenticatedService, CacheableService
from utils.config import settings
from utils.exceptions import GoogleAdsException as CustomGoogleAdsException
from utils.helpers import retry_async, format_google_ads_customer_id, clamp
from models.campaigns import Campaign, CampaignType, CampaignStatus, BiddingStrategy, KeywordMatchType, AdStatus


class GoogleAdsService(AuthenticatedService):
    """
    Service for Google Ads API operations.
    Handles authentication, campaign management, and data retrieval.
    """
    
    def __init__(self):
        super().__init__(
            service_name="Google Ads API",
            rate_limit_per_minute=1000,  # Google Ads API rate limit
        )
        
        # Add simple caching functionality
        self._cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl_seconds = 300
        
        self._client: Optional[GoogleAdsClient] = None
        self._customer_id = settings.GOOGLE_ADS_CUSTOMER_ID
        
        # Rate limiting and quota management
        self._last_request_time = 0
        self._request_count = 0
        self._quota_exhausted_until = 0
        
        # Service mappings
        self._campaign_type_mapping = {
            CampaignType.SEARCH: "SEARCH",
            CampaignType.DISPLAY: "DISPLAY", 
            CampaignType.SHOPPING: "SHOPPING",
            CampaignType.VIDEO: "VIDEO",
            CampaignType.PERFORMANCE_MAX: "PERFORMANCE_MAX",
            CampaignType.DISCOVERY: "DISCOVERY",
            CampaignType.LOCAL: "LOCAL",
            CampaignType.SMART: "SMART",
        }
        
        self._campaign_status_mapping = {
            CampaignStatus.ACTIVE: "ENABLED",
            CampaignStatus.PAUSED: "PAUSED",
            CampaignStatus.REMOVED: "REMOVED",
            CampaignStatus.DRAFT: "PAUSED",  # Draft maps to paused
        }
        
        # Keyword match type mappings
        self._keyword_match_type_mapping = {
            KeywordMatchType.EXACT: "EXACT",
            KeywordMatchType.PHRASE: "PHRASE",
            KeywordMatchType.BROAD: "BROAD",
        }
    
    async def _perform_authentication(self) -> None:
        """Initialize Google Ads client with credentials."""
        try:
            # Create credentials dictionary
            credentials = {
                "developer_token": settings.GOOGLE_ADS_DEVELOPER_TOKEN,
                "client_id": settings.GOOGLE_ADS_CLIENT_ID,
                "client_secret": settings.GOOGLE_ADS_CLIENT_SECRET,
                "refresh_token": settings.GOOGLE_ADS_REFRESH_TOKEN,
                "use_proto_plus": True,
            }
            
            # Validate required credentials
            required_fields = ["developer_token", "client_id", "client_secret", "refresh_token"]
            for field in required_fields:
                if not credentials[field]:
                    raise CustomGoogleAdsException(f"Missing required Google Ads credential: {field}")
            
            # Initialize Google Ads client
            self._client = GoogleAdsClient.load_from_dict(credentials)
            
            # Test authentication with a simple query
            await self._test_authentication()
            
            self._auth_token = "authenticated"  # Mark as authenticated
            
            self.logger.info("Google Ads client authenticated successfully")
            
        except Exception as e:
            self.logger.error("Google Ads authentication failed", error=str(e))
            raise CustomGoogleAdsException(f"Authentication failed: {str(e)}")
    
    async def _test_authentication(self) -> None:
        """Test authentication by making a simple API call."""
        if not self._client or not self._customer_id:
            raise CustomGoogleAdsException("Client or customer ID not configured")
        
        try:
            customer_service = self._client.get_service("CustomerService")
            customer = customer_service.get_customer(
                customer_id=format_google_ads_customer_id(self._customer_id)
            )
            
            self.logger.info(
                "Authentication test successful",
                customer_id=customer.id,
                descriptive_name=customer.descriptive_name,
            )
            
        except GoogleAdsException as e:
            self.logger.error("Authentication test failed", error=str(e))
            raise CustomGoogleAdsException(f"Authentication test failed: {str(e)}")
    
    async def _handle_rate_limit(self) -> None:
        """Handle rate limiting for Google Ads API."""
        current_time = time.time()
        
        # Check if quota is currently exhausted
        if current_time < self._quota_exhausted_until:
            wait_time = self._quota_exhausted_until - current_time
            self.logger.warning("Google Ads API quota exhausted, waiting", wait_seconds=wait_time)
            await asyncio.sleep(wait_time)
        
        # Rate limit to avoid overwhelming the API
        time_since_last = current_time - self._last_request_time
        min_interval = 60.0 / self.rate_limit_per_minute  # Convert to seconds
        
        if time_since_last < min_interval:
            wait_time = min_interval - time_since_last
            await asyncio.sleep(wait_time)
        
        self._last_request_time = time.time()
        self._request_count += 1
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers (not used for Google Ads SDK)."""
        return {}
    
    async def _execute_request(self, method: str, endpoint: str, **kwargs: Any) -> Any:
        """Execute Google Ads API request using the SDK."""
        # This method is not used for Google Ads SDK
        # Individual methods handle SDK calls directly
        pass
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform Google Ads API health check."""
        try:
            await self.authenticate()
            await self._test_authentication()
            
            return {
                "status": "healthy",
                "customer_id": self._customer_id,
                "authenticated": True,
                "last_check": datetime.utcnow().isoformat(),
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "authenticated": False,
                "last_check": datetime.utcnow().isoformat(),
            }
    
    # Campaign Management Methods
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def create_campaign(
        self,
        name: str,
        campaign_type: CampaignType,
        budget_amount: float,
        bidding_strategy: BiddingStrategy = BiddingStrategy.MANUAL_CPC,
        target_locations: Optional[List[str]] = None,
        **kwargs: Any
    ) -> str:
        """Create a new Google Ads campaign.
        
        Args:
            name: Campaign name
            campaign_type: Campaign type
            budget_amount: Daily budget amount in account currency
            bidding_strategy: Bidding strategy
            target_locations: List of target location names
            **kwargs: Additional campaign parameters
            
        Returns:
            str: Created campaign ID
            
        Raises:
            CustomGoogleAdsException: If campaign creation fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            self.logger.info(
                "Creating Google Ads campaign",
                name=name,
                type=campaign_type,
                budget=budget_amount,
            )
            
            # Create campaign budget
            budget_resource_name = await self._create_campaign_budget(
                name=f"{name} Budget",
                amount_micros=int(budget_amount * 1_000_000),  # Convert to micros
            )
            
            # Create campaign
            campaign_service = self._client.get_service("CampaignService")
            campaign_operation = self._client.get_type("CampaignOperation")
            
            campaign = campaign_operation.create
            campaign.name = name
            campaign.advertising_channel_type = self._client.enums.AdvertisingChannelTypeEnum[
                self._campaign_type_mapping[campaign_type]
            ]
            campaign.status = self._client.enums.CampaignStatusEnum.PAUSED
            campaign.campaign_budget = budget_resource_name
            
            # Set bidding strategy
            if bidding_strategy == BiddingStrategy.MANUAL_CPC:
                campaign.manual_cpc.enhanced_cpc_enabled = False
            elif bidding_strategy == BiddingStrategy.ENHANCED_CPC:
                campaign.manual_cpc.enhanced_cpc_enabled = True
            elif bidding_strategy == BiddingStrategy.TARGET_CPA:
                if kwargs.get("target_cpa"):
                    campaign.target_cpa.target_cpa_micros = int(kwargs["target_cpa"] * 1_000_000)
            elif bidding_strategy == BiddingStrategy.TARGET_ROAS:
                if kwargs.get("target_roas"):
                    campaign.target_roas.target_roas = kwargs["target_roas"]
            elif bidding_strategy == BiddingStrategy.MAXIMIZE_CLICKS:
                campaign.maximize_clicks.SetInParent()
            elif bidding_strategy == BiddingStrategy.MAXIMIZE_CONVERSIONS:
                campaign.maximize_conversions.SetInParent()
            elif bidding_strategy == BiddingStrategy.MAXIMIZE_CONVERSION_VALUE:
                campaign.maximize_conversion_value.SetInParent()
            
            # Set dates if provided
            if kwargs.get("start_date"):
                campaign.start_date = kwargs["start_date"].strftime("%Y-%m-%d")
            if kwargs.get("end_date"):
                campaign.end_date = kwargs["end_date"].strftime("%Y-%m-%d")
            
            # Add targeting if specified
            if target_locations:
                await self._add_location_targeting(campaign, target_locations)
            
            # Create the campaign
            response = campaign_service.mutate_campaigns(
                customer_id=format_google_ads_customer_id(self._customer_id),
                operations=[campaign_operation],
            )
            
            campaign_resource_name = response.results[0].resource_name
            campaign_id = campaign_resource_name.split("/")[-1]
            
            self.logger.info(
                "Google Ads campaign created successfully",
                campaign_id=campaign_id,
                resource_name=campaign_resource_name,
            )
            
            return campaign_id
            
        except ResourceExhausted as e:
            # Handle quota exhaustion
            self._quota_exhausted_until = time.time() + 300  # Wait 5 minutes
            error_message = f"Google Ads API quota exhausted: {str(e)}"
            self.logger.error("Campaign creation failed due to quota", error=error_message)
            raise CustomGoogleAdsException(error_message)
        except GoogleAdsException as e:
            error_message = f"Failed to create campaign: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Campaign creation failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    async def _create_campaign_budget(self, name: str, amount_micros: int) -> str:
        """Create a campaign budget and return its resource name."""
        await self._handle_rate_limit()
        
        budget_service = self._client.get_service("CampaignBudgetService")
        budget_operation = self._client.get_type("CampaignBudgetOperation")
        
        budget = budget_operation.create
        budget.name = name
        budget.delivery_method = self._client.enums.BudgetDeliveryMethodEnum.STANDARD
        budget.amount_micros = amount_micros
        
        response = budget_service.mutate_campaign_budgets(
            customer_id=format_google_ads_customer_id(self._customer_id),
            operations=[budget_operation],
        )
        
        return response.results[0].resource_name
    
    async def _add_location_targeting(self, campaign: Any, locations: List[str]) -> None:
        """Add location targeting to a campaign (placeholder implementation)."""
        # This would involve creating location criteria
        # Implementation depends on specific location targeting requirements  
        # For now, just log the locations that would be targeted
        self.logger.info("Location targeting would be added here", locations=locations)
        # TODO: Implement actual location targeting using location criteria
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def get_campaign(self, campaign_id: str) -> Dict[str, Any]:
        """
        Get campaign details by ID.
        
        Args:
            campaign_id: Google Ads campaign ID
            
        Returns:
            Dict[str, Any]: Campaign details
            
        Raises:
            CustomGoogleAdsException: If campaign retrieval fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            google_ads_service = self._client.get_service("GoogleAdsService")
            
            query = f"""
                SELECT
                    campaign.id,
                    campaign.name,
                    campaign.status,
                    campaign.advertising_channel_type,
                    campaign.campaign_budget,
                    campaign.start_date,
                    campaign.end_date,
                    campaign_budget.amount_micros
                FROM campaign
                WHERE campaign.id = {campaign_id}
            """
            
            response = google_ads_service.search(
                customer_id=format_google_ads_customer_id(self._customer_id),
                query=query,
            )
            
            campaigns = list(response)
            if not campaigns:
                raise CustomGoogleAdsException(f"Campaign {campaign_id} not found")
            
            campaign_row = campaigns[0]
            campaign = campaign_row.campaign
            budget = campaign_row.campaign_budget
            
            return {
                "id": str(campaign.id),
                "name": campaign.name,
                "status": campaign.status.name,
                "type": campaign.advertising_channel_type.name,
                "budget_micros": budget.amount_micros,
                "budget_amount": budget.amount_micros / 1_000_000,
                "start_date": campaign.start_date or None,
                "end_date": campaign.end_date or None,
            }
            
        except GoogleAdsException as e:
            error_message = f"Failed to get campaign {campaign_id}: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Campaign retrieval failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def list_campaigns(
        self,
        status_filter: Optional[CampaignStatus] = None,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        List campaigns with optional filtering.
        
        Args:
            status_filter: Filter by campaign status
            limit: Maximum number of campaigns to return
            
        Returns:
            List[Dict[str, Any]]: List of campaign details
            
        Raises:
            CustomGoogleAdsException: If campaign listing fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            google_ads_service = self._client.get_service("GoogleAdsService")
            
            query = """
                SELECT
                    campaign.id,
                    campaign.name,
                    campaign.status,
                    campaign.advertising_channel_type,
                    campaign_budget.amount_micros
                FROM campaign
                WHERE campaign.status != 'REMOVED'
            """
            
            if status_filter:
                google_status = self._campaign_status_mapping.get(status_filter)
                if google_status:
                    query += f" AND campaign.status = '{google_status}'"
            
            query += f" LIMIT {limit}"
            
            response = google_ads_service.search(
                customer_id=format_google_ads_customer_id(self._customer_id),
                query=query,
            )
            
            campaigns = []
            for row in response:
                campaign = row.campaign
                budget = row.campaign_budget
                
                campaigns.append({
                    "id": str(campaign.id),
                    "name": campaign.name,
                    "status": campaign.status.name,
                    "type": campaign.advertising_channel_type.name,
                    "budget_micros": budget.amount_micros,
                    "budget_amount": budget.amount_micros / 1_000_000,
                })
            
            self.logger.info(f"Retrieved {len(campaigns)} campaigns")
            return campaigns
            
        except GoogleAdsException as e:
            error_message = f"Failed to list campaigns: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Campaign listing failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def update_campaign_status(
        self,
        campaign_id: str,
        status: CampaignStatus,
    ) -> bool:
        """
        Update campaign status.
        
        Args:
            campaign_id: Google Ads campaign ID
            status: New campaign status
            
        Returns:
            bool: True if update was successful
            
        Raises:
            CustomGoogleAdsException: If status update fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            campaign_service = self._client.get_service("CampaignService")
            campaign_operation = self._client.get_type("CampaignOperation")
            
            campaign = campaign_operation.update
            campaign.resource_name = self._client.get_service("CampaignService").campaign_path(
                format_google_ads_customer_id(self._customer_id),
                campaign_id,
            )
            
            google_status = self._campaign_status_mapping.get(status)
            if not google_status:
                raise CustomGoogleAdsException(f"Invalid status: {status}")
            
            campaign.status = self._client.enums.CampaignStatusEnum[google_status]
            
            # Specify which fields to update
            self._client.copy_from(
                campaign_operation.update_mask,
                {"paths": ["status"]},
            )
            
            response = campaign_service.mutate_campaigns(
                customer_id=format_google_ads_customer_id(self._customer_id),
                operations=[campaign_operation],
            )
            
            self.logger.info(
                "Campaign status updated successfully",
                campaign_id=campaign_id,
                new_status=status,
            )
            
            return True
            
        except GoogleAdsException as e:
            error_message = f"Failed to update campaign {campaign_id} status: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Campaign status update failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def get_campaign_metrics(
        self,
        campaign_id: str,
        start_date: date,
        end_date: date,
        metrics: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        Get campaign performance metrics for a date range.
        
        Args:
            campaign_id: Google Ads campaign ID
            start_date: Start date for metrics
            end_date: End date for metrics
            metrics: List of specific metrics to retrieve
            
        Returns:
            Dict[str, Any]: Campaign performance metrics
            
        Raises:
            CustomGoogleAdsException: If metrics retrieval fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            google_ads_service = self._client.get_service("GoogleAdsService")
            
            # Default metrics if none specified
            if not metrics:
                metrics = [
                    "impressions",
                    "clicks",
                    "conversions",
                    "cost_micros",
                    "ctr",
                    "average_cpc",
                ]
            
            # Build the query
            metric_fields = ", ".join([f"metrics.{metric}" for metric in metrics])
            
            query = f"""
                SELECT
                    campaign.id,
                    campaign.name,
                    {metric_fields}
                FROM campaign
                WHERE campaign.id = {campaign_id}
                AND segments.date BETWEEN '{start_date}' AND '{end_date}'
            """
            
            response = google_ads_service.search(
                customer_id=format_google_ads_customer_id(self._customer_id),
                query=query,
            )
            
            # Aggregate metrics across all rows
            aggregated_metrics = {}
            total_rows = 0
            
            for row in response:
                total_rows += 1
                metrics_obj = row.metrics
                
                for metric in metrics:
                    value = getattr(metrics_obj, metric, 0)
                    
                    # Convert micros to standard units for cost metrics
                    if metric.endswith("_micros"):
                        value = value / 1_000_000
                        metric = metric.replace("_micros", "")
                    
                    aggregated_metrics[metric] = aggregated_metrics.get(metric, 0) + value
            
            # Calculate averages for rate metrics
            if total_rows > 0:
                rate_metrics = ["ctr", "average_cpc"]
                for metric in rate_metrics:
                    if metric in aggregated_metrics:
                        aggregated_metrics[metric] = aggregated_metrics[metric] / total_rows
            
            self.logger.info(
                "Campaign metrics retrieved successfully",
                campaign_id=campaign_id,
                date_range=f"{start_date} to {end_date}",
                metrics_count=len(aggregated_metrics),
            )
            
            return {
                "campaign_id": campaign_id,
                "date_range": {"start_date": start_date, "end_date": end_date},
                "metrics": aggregated_metrics,
                "total_days": total_rows,
            }
            
        except GoogleAdsException as e:
            error_message = f"Failed to get metrics for campaign {campaign_id}: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Campaign metrics retrieval failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    # Ad Group Management Methods
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def create_ad_group(
        self,
        campaign_id: str,
        name: str,
        max_cpc: Optional[float] = None,
        **kwargs: Any
    ) -> str:
        """Create a new ad group in a campaign.
        
        Args:
            campaign_id: Parent campaign ID
            name: Ad group name
            max_cpc: Maximum cost per click in account currency
            **kwargs: Additional ad group parameters
            
        Returns:
            str: Created ad group ID
            
        Raises:
            CustomGoogleAdsException: If ad group creation fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            self.logger.info(
                "Creating ad group",
                campaign_id=campaign_id,
                name=name,
                max_cpc=max_cpc,
            )
            
            ad_group_service = self._client.get_service("AdGroupService")
            ad_group_operation = self._client.get_type("AdGroupOperation")
            
            ad_group = ad_group_operation.create
            ad_group.name = name
            ad_group.campaign = self._client.get_service("CampaignService").campaign_path(
                format_google_ads_customer_id(self._customer_id),
                campaign_id,
            )
            ad_group.status = self._client.enums.AdGroupStatusEnum.ENABLED
            ad_group.type_ = self._client.enums.AdGroupTypeEnum.SEARCH_STANDARD
            
            # Set max CPC if provided
            if max_cpc:
                ad_group.cpc_bid_micros = int(max_cpc * 1_000_000)
            
            response = ad_group_service.mutate_ad_groups(
                customer_id=format_google_ads_customer_id(self._customer_id),
                operations=[ad_group_operation],
            )
            
            ad_group_resource_name = response.results[0].resource_name
            ad_group_id = ad_group_resource_name.split("/")[-1]
            
            self.logger.info(
                "Ad group created successfully",
                ad_group_id=ad_group_id,
                resource_name=ad_group_resource_name,
            )
            
            return ad_group_id
            
        except GoogleAdsException as e:
            error_message = f"Failed to create ad group: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Ad group creation failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def list_ad_groups(
        self,
        campaign_id: str,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """List ad groups for a campaign.
        
        Args:
            campaign_id: Parent campaign ID
            limit: Maximum number of ad groups to return
            
        Returns:
            List[Dict[str, Any]]: List of ad group details
            
        Raises:
            CustomGoogleAdsException: If ad group listing fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            google_ads_service = self._client.get_service("GoogleAdsService")
            
            query = f"""
                SELECT
                    ad_group.id,
                    ad_group.name,
                    ad_group.status,
                    ad_group.type,
                    ad_group.cpc_bid_micros
                FROM ad_group
                WHERE ad_group.campaign = 'customers/{format_google_ads_customer_id(self._customer_id)}/campaigns/{campaign_id}'
                AND ad_group.status != 'REMOVED'
                LIMIT {limit}
            """
            
            response = google_ads_service.search(
                customer_id=format_google_ads_customer_id(self._customer_id),
                query=query,
            )
            
            ad_groups = []
            for row in response:
                ad_group = row.ad_group
                ad_groups.append({
                    "id": str(ad_group.id),
                    "name": ad_group.name,
                    "status": ad_group.status.name,
                    "type": ad_group.type_.name,
                    "cpc_bid_micros": ad_group.cpc_bid_micros,
                    "cpc_bid_amount": ad_group.cpc_bid_micros / 1_000_000,
                })
            
            self.logger.info(f"Retrieved {len(ad_groups)} ad groups for campaign {campaign_id}")
            return ad_groups
            
        except GoogleAdsException as e:
            error_message = f"Failed to list ad groups: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Ad group listing failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def update_ad_group_cpc(
        self,
        ad_group_id: str,
        max_cpc: float,
    ) -> bool:
        """Update ad group max CPC bid.
        
        Args:
            ad_group_id: Ad group ID
            max_cpc: New maximum cost per click
            
        Returns:
            bool: True if update was successful
            
        Raises:
            CustomGoogleAdsException: If update fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            ad_group_service = self._client.get_service("AdGroupService")
            ad_group_operation = self._client.get_type("AdGroupOperation")
            
            ad_group = ad_group_operation.update
            ad_group.resource_name = self._client.get_service("AdGroupService").ad_group_path(
                format_google_ads_customer_id(self._customer_id),
                ad_group_id,
            )
            ad_group.cpc_bid_micros = int(max_cpc * 1_000_000)
            
            # Specify which fields to update
            self._client.copy_from(
                ad_group_operation.update_mask,
                {"paths": ["cpc_bid_micros"]},
            )
            
            response = ad_group_service.mutate_ad_groups(
                customer_id=format_google_ads_customer_id(self._customer_id),
                operations=[ad_group_operation],
            )
            
            self.logger.info(
                "Ad group CPC updated successfully",
                ad_group_id=ad_group_id,
                new_cpc=max_cpc,
            )
            
            return True
            
        except GoogleAdsException as e:
            error_message = f"Failed to update ad group CPC: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Ad group CPC update failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    # Keyword Management Methods
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def add_keywords(
        self,
        ad_group_id: str,
        keywords: List[Dict[str, Any]],
    ) -> List[str]:
        """Add keywords to an ad group.
        
        Args:
            ad_group_id: Target ad group ID
            keywords: List of keyword dictionaries with 'text', 'match_type', and optional 'max_cpc'
            
        Returns:
            List[str]: List of created keyword IDs
            
        Raises:
            CustomGoogleAdsException: If keyword creation fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            self.logger.info(
                "Adding keywords to ad group",
                ad_group_id=ad_group_id,
                keyword_count=len(keywords),
            )
            
            ad_group_criterion_service = self._client.get_service("AdGroupCriterionService")
            operations = []
            
            for keyword_data in keywords:
                operation = self._client.get_type("AdGroupCriterionOperation")
                criterion = operation.create
                
                criterion.ad_group = self._client.get_service("AdGroupService").ad_group_path(
                    format_google_ads_customer_id(self._customer_id),
                    ad_group_id,
                )
                
                criterion.keyword.text = keyword_data["text"]
                criterion.keyword.match_type = self._client.enums.KeywordMatchTypeEnum[
                    self._keyword_match_type_mapping.get(
                        KeywordMatchType(keyword_data["match_type"]),
                        "BROAD"
                    )
                ]
                
                if keyword_data.get("max_cpc"):
                    criterion.cpc_bid_micros = int(keyword_data["max_cpc"] * 1_000_000)
                
                criterion.status = self._client.enums.AdGroupCriterionStatusEnum.ENABLED
                operations.append(operation)
            
            response = ad_group_criterion_service.mutate_ad_group_criteria(
                customer_id=format_google_ads_customer_id(self._customer_id),
                operations=operations,
            )
            
            keyword_ids = []
            for result in response.results:
                keyword_id = result.resource_name.split("/")[-1]
                keyword_ids.append(keyword_id)
            
            self.logger.info(
                "Keywords added successfully",
                ad_group_id=ad_group_id,
                keywords_added=len(keyword_ids),
            )
            
            return keyword_ids
            
        except GoogleAdsException as e:
            error_message = f"Failed to add keywords: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Keyword creation failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def update_keyword_bid(
        self,
        ad_group_criterion_id: str,
        max_cpc: float,
    ) -> bool:
        """Update keyword bid.
        
        Args:
            ad_group_criterion_id: Keyword criterion ID
            max_cpc: New maximum cost per click
            
        Returns:
            bool: True if update was successful
            
        Raises:
            CustomGoogleAdsException: If update fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            ad_group_criterion_service = self._client.get_service("AdGroupCriterionService")
            operation = self._client.get_type("AdGroupCriterionOperation")
            
            criterion = operation.update
            criterion.resource_name = self._client.get_service("AdGroupCriterionService").ad_group_criterion_path(
                format_google_ads_customer_id(self._customer_id),
                ad_group_criterion_id,
            )
            criterion.cpc_bid_micros = int(max_cpc * 1_000_000)
            
            # Specify which fields to update
            self._client.copy_from(
                operation.update_mask,
                {"paths": ["cpc_bid_micros"]},
            )
            
            response = ad_group_criterion_service.mutate_ad_group_criteria(
                customer_id=format_google_ads_customer_id(self._customer_id),
                operations=[operation],
            )
            
            self.logger.info(
                "Keyword bid updated successfully",
                criterion_id=ad_group_criterion_id,
                new_bid=max_cpc,
            )
            
            return True
            
        except GoogleAdsException as e:
            error_message = f"Failed to update keyword bid: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Keyword bid update failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    # Ad Management Methods
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def create_responsive_search_ad(
        self,
        ad_group_id: str,
        headlines: List[str],
        descriptions: List[str],
        final_urls: List[str],
        **kwargs: Any
    ) -> str:
        """Create a responsive search ad.
        
        Args:
            ad_group_id: Target ad group ID
            headlines: List of headline texts (3-15 required)
            descriptions: List of description texts (2-4 required)
            final_urls: List of final landing URLs
            **kwargs: Additional ad parameters
            
        Returns:
            str: Created ad ID
            
        Raises:
            CustomGoogleAdsException: If ad creation fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            if len(headlines) < 3:
                raise CustomGoogleAdsException("Responsive search ads require at least 3 headlines")
            if len(descriptions) < 2:
                raise CustomGoogleAdsException("Responsive search ads require at least 2 descriptions")
            
            self.logger.info(
                "Creating responsive search ad",
                ad_group_id=ad_group_id,
                headlines_count=len(headlines),
                descriptions_count=len(descriptions),
            )
            
            ad_group_ad_service = self._client.get_service("AdGroupAdService")
            ad_group_ad_operation = self._client.get_type("AdGroupAdOperation")
            
            ad_group_ad = ad_group_ad_operation.create
            ad_group_ad.ad_group = self._client.get_service("AdGroupService").ad_group_path(
                format_google_ads_customer_id(self._customer_id),
                ad_group_id,
            )
            
            # Create responsive search ad
            responsive_search_ad = ad_group_ad.ad.responsive_search_ad
            
            # Add headlines
            for headline in headlines[:15]:  # Max 15 headlines
                headline_asset = self._client.get_type("AdTextAsset")
                headline_asset.text = headline
                responsive_search_ad.headlines.append(headline_asset)
            
            # Add descriptions
            for description in descriptions[:4]:  # Max 4 descriptions
                description_asset = self._client.get_type("AdTextAsset")
                description_asset.text = description
                responsive_search_ad.descriptions.append(description_asset)
            
            # Set final URLs
            ad_group_ad.ad.final_urls.extend(final_urls)
            
            # Set ad status
            ad_group_ad.status = self._client.enums.AdGroupAdStatusEnum.ENABLED
            
            response = ad_group_ad_service.mutate_ad_group_ads(
                customer_id=format_google_ads_customer_id(self._customer_id),
                operations=[ad_group_ad_operation],
            )
            
            ad_resource_name = response.results[0].resource_name
            ad_id = ad_resource_name.split("/")[-1]
            
            self.logger.info(
                "Responsive search ad created successfully",
                ad_id=ad_id,
                resource_name=ad_resource_name,
            )
            
            return ad_id
            
        except GoogleAdsException as e:
            error_message = f"Failed to create responsive search ad: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Ad creation failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def update_ad_status(
        self,
        ad_group_ad_id: str,
        status: AdStatus,
    ) -> bool:
        """Update ad status.
        
        Args:
            ad_group_ad_id: Ad group ad ID
            status: New ad status
            
        Returns:
            bool: True if update was successful
            
        Raises:
            CustomGoogleAdsException: If update fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            ad_group_ad_service = self._client.get_service("AdGroupAdService")
            operation = self._client.get_type("AdGroupAdOperation")
            
            ad_group_ad = operation.update
            ad_group_ad.resource_name = self._client.get_service("AdGroupAdService").ad_group_ad_path(
                format_google_ads_customer_id(self._customer_id),
                ad_group_ad_id,
            )
            
            # Map our status to Google Ads status
            if status == AdStatus.ENABLED:
                google_status = "ENABLED"
            elif status == AdStatus.PAUSED:
                google_status = "PAUSED"
            elif status == AdStatus.REMOVED:
                google_status = "REMOVED"
            else:
                raise CustomGoogleAdsException(f"Invalid ad status: {status}")
            
            ad_group_ad.status = self._client.enums.AdGroupAdStatusEnum[google_status]
            
            # Specify which fields to update
            self._client.copy_from(
                operation.update_mask,
                {"paths": ["status"]},
            )
            
            response = ad_group_ad_service.mutate_ad_group_ads(
                customer_id=format_google_ads_customer_id(self._customer_id),
                operations=[operation],
            )
            
            self.logger.info(
                "Ad status updated successfully",
                ad_id=ad_group_ad_id,
                new_status=status,
            )
            
            return True
            
        except GoogleAdsException as e:
            error_message = f"Failed to update ad status: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Ad status update failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    # Budget Management Methods
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def update_campaign_budget(
        self,
        campaign_id: str,
        new_budget_amount: float,
    ) -> bool:
        """Update campaign daily budget.
        
        Args:
            campaign_id: Campaign ID
            new_budget_amount: New daily budget amount
            
        Returns:
            bool: True if update was successful
            
        Raises:
            CustomGoogleAdsException: If update fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            # First get the campaign budget resource name
            google_ads_service = self._client.get_service("GoogleAdsService")
            query = f"""
                SELECT
                    campaign.id,
                    campaign.campaign_budget,
                    campaign_budget.id
                FROM campaign
                WHERE campaign.id = {campaign_id}
            """
            
            response = google_ads_service.search(
                customer_id=format_google_ads_customer_id(self._customer_id),
                query=query,
            )
            
            campaigns = list(response)
            if not campaigns:
                raise CustomGoogleAdsException(f"Campaign {campaign_id} not found")
            
            budget_id = campaigns[0].campaign_budget.id
            
            # Update the budget
            budget_service = self._client.get_service("CampaignBudgetService")
            budget_operation = self._client.get_type("CampaignBudgetOperation")
            
            budget = budget_operation.update
            budget.resource_name = self._client.get_service("CampaignBudgetService").campaign_budget_path(
                format_google_ads_customer_id(self._customer_id),
                budget_id,
            )
            budget.amount_micros = int(new_budget_amount * 1_000_000)
            
            # Specify which fields to update
            self._client.copy_from(
                budget_operation.update_mask,
                {"paths": ["amount_micros"]},
            )
            
            budget_response = budget_service.mutate_campaign_budgets(
                customer_id=format_google_ads_customer_id(self._customer_id),
                operations=[budget_operation],
            )
            
            self.logger.info(
                "Campaign budget updated successfully",
                campaign_id=campaign_id,
                new_budget=new_budget_amount,
            )
            
            return True
            
        except GoogleAdsException as e:
            error_message = f"Failed to update campaign budget: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Budget update failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    # Account and Authentication Methods
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def list_accessible_customers(self) -> List[Dict[str, Any]]:
        """List accessible Google Ads customer accounts.
        
        Returns:
            List[Dict[str, Any]]: List of accessible customer accounts
            
        Raises:
            CustomGoogleAdsException: If listing fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            customer_service = self._client.get_service("CustomerService")
            accessible_customers = customer_service.list_accessible_customers()
            
            customers = []
            for customer_resource in accessible_customers.resource_names:
                customer_id = customer_resource.split("/")[-1]
                
                # Get customer details
                try:
                    customer = customer_service.get_customer(
                        customer_id=customer_id
                    )
                    customers.append({
                        "id": str(customer.id),
                        "descriptive_name": customer.descriptive_name,
                        "currency_code": customer.currency_code,
                        "time_zone": customer.time_zone,
                        "manager": customer.manager,
                    })
                except Exception as e:
                    self.logger.warning(
                        "Failed to get details for customer",
                        customer_id=customer_id,
                        error=str(e),
                    )
            
            self.logger.info(f"Retrieved {len(customers)} accessible customers")
            return customers
            
        except GoogleAdsException as e:
            error_message = f"Failed to list accessible customers: {e.message if hasattr(e, 'message') else str(e)}"
            self.logger.error("Customer listing failed", error=error_message)
            raise CustomGoogleAdsException(error_message)
    
    # Performance Sync Methods
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def sync_campaign_performance(
        self,
        campaign_id: str,
        start_date: date,
        end_date: date,
    ) -> Dict[str, Any]:
        """Sync campaign performance data from Google Ads.
        
        Args:
            campaign_id: Campaign ID
            start_date: Start date for performance data
            end_date: End date for performance data
            
        Returns:
            Dict[str, Any]: Performance data
            
        Raises:
            CustomGoogleAdsException: If sync fails
        """
        return await self.get_campaign_metrics(
            campaign_id=campaign_id,
            start_date=start_date,
            end_date=end_date,
        )
    
    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def sync_all_campaigns_performance(
        self,
        start_date: date,
        end_date: date,
    ) -> List[Dict[str, Any]]:
        """Sync performance data for all campaigns.
        
        Args:
            start_date: Start date for performance data
            end_date: End date for performance data
            
        Returns:
            List[Dict[str, Any]]: Performance data for all campaigns
            
        Raises:
            CustomGoogleAdsException: If sync fails
        """
        await self.authenticate()
        await self._handle_rate_limit()
        
        try:
            # Get all campaigns first
            campaigns = await self.list_campaigns(limit=1000)
            
            all_performance_data = []
            for campaign in campaigns:
                try:
                    performance_data = await self.sync_campaign_performance(
                        campaign_id=campaign["id"],
                        start_date=start_date,
                        end_date=end_date,
                    )
                    all_performance_data.append(performance_data)
                    
                    # Small delay between requests to avoid rate limiting
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    self.logger.warning(
                        "Failed to sync performance for campaign",
                        campaign_id=campaign["id"],
                        error=str(e),
                    )
            
            self.logger.info(
                "Synced performance data for campaigns",
                total_campaigns=len(campaigns),
                successful_syncs=len(all_performance_data),
            )
            
            return all_performance_data
            
        except Exception as e:
            error_message = f"Failed to sync all campaigns performance: {str(e)}"
            self.logger.error("Performance sync failed", error=error_message)
            raise CustomGoogleAdsException(error_message)


# Global Google Ads service instance
google_ads_service = GoogleAdsService()