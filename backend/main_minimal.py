"""
Minimal FastAPI application for initial deployment testing
"""

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Create FastAPI application
app = FastAPI(
    title="Google Ads AI Agent System",
    description="AI-powered Google Ads campaign management system",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint returning basic API information."""
    return {
        "name": "AiLex Ad Agent System",
        "version": "1.0.0",
        "status": "operational",
        "environment": "production"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "message": "API is running"}

@app.get("/api/v1/health/liveness")
async def liveness_check():
    """Liveness check endpoint for Fly.io."""
    return {"status": "alive"}

@app.get("/api/v1/health/readiness")
async def readiness_check():
    """Readiness check endpoint for Fly.io."""
    return {"status": "ready"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main_minimal:app", host="0.0.0.0", port=8000)