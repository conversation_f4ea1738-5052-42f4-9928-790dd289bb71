# AiLex Ad Agent System - Worker Service
# Full build with uv and all heavy dependencies for background processing
# syntax=docker/dockerfile:1.7-labs

# Build stage
FROM python:3.11-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV UV_SYSTEM_PYTHON=1

# Set work directory
WORKDIR /app

# Install system dependencies required for building
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        git \
        pkg-config \
        libpq-dev \
        gcc \
        g++ \
        gfortran \
        libopenblas-dev \
        liblapack-dev \
    && rm -rf /var/lib/apt/lists/*

# Install uv for fast Python package management
RUN curl -LsSf https://astral.sh/uv/install.sh | sh \
    && ln -s /root/.local/bin/uv /usr/local/bin/uv

# Copy worker dependency files
COPY requirements.worker.txt /app/

# Use BuildKit cache for uv - 10-100x faster dependency installation
RUN --mount=type=cache,target=/root/.cache/uv \
    uv pip install --system \
    --find-links https://download.pytorch.org/whl/cpu \
    --prefer-binary \
    -r requirements.worker.txt

# Production stage
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV UV_SYSTEM_PYTHON=1
ENV PATH="/home/<USER>/.local/bin:$PATH"
ENV SERVICE_TYPE=worker
ENV CELERY_WORKER=true

# Set work directory
WORKDIR /app

# Install only runtime dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        curl \
        libpq5 \
        postgresql-client \
        libopenblas0 \
        liblapack3 \
        libgomp1 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get purge -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false

# Create non-root user
RUN useradd --create-home --shell /bin/bash app

# Copy Python dependencies from builder stage (uv installs to system)
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy project files
COPY --chown=app:app . /app/

# Create necessary directories
RUN mkdir -p /app/logs /app/tmp /app/data \
    && chown -R app:app /app

# Switch to non-root user
USER app

# Expose port for worker health checks
EXPOSE 8001

# Health check for worker service
HEALTHCHECK --interval=60s --timeout=30s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8001/worker/health || exit 1

# Run the worker service
CMD ["python", "worker_main.py"]
