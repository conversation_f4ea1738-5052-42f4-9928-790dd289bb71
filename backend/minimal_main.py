"""
Minimal FastAPI server to test the setup
"""

import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager"""
    logging.info("Starting AiLex Ad Agent System...")
    yield
    logging.info("Shutting down AiLex Ad Agent System...")


# Create FastAPI application
app = FastAPI(
    title="AiLex Ad Agent System",
    description="AI-powered Google Ads management and optimization platform",
    version="1.0.0",
    lifespan=lifespan,
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "https://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "AiLex Ad Agent System is running!", "status": "healthy"}


@app.get("/api/v1/health/")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "message": "AiLex Ad Agent System is operational",
        "version": "1.0.0"
    }


@app.get("/api/v1/health/liveness")
async def liveness_check():
    """Kubernetes liveness probe"""
    return {"status": "alive"}


@app.get("/api/v1/health/readiness")
async def readiness_check():
    """Kubernetes readiness probe"""
    return {"status": "ready"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "minimal_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )