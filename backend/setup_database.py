#!/usr/bin/env python3
"""
Database setup script for the AiLex Ad Agent System.
Initializes the database, runs migrations, and sets up initial data.
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import structlog

# Add the backend directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from services.database import database_service
from database.migration_manager import migration_manager
from utils.config import settings
from utils.exceptions import DatabaseException


# Configure logging
structlog.configure(
    processors=[
        structlog.processors.TimeStamper(fmt="ISO"),
        structlog.dev.ConsoleRenderer()
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class DatabaseSetup:
    """Manages database initialization and setup."""
    
    def __init__(self):
        self.setup_complete = False
    
    async def check_prerequisites(self) -> bool:
        """Check if all prerequisites are met for database setup."""
        logger.info("Checking database setup prerequisites")
        
        # Check environment configuration
        if not settings.DATABASE_URL and not settings.SUPABASE_URL:
            logger.error("No database URL configured. Please set DATABASE_URL or SUPABASE_URL in .env")
            return False
        
        if not settings.database_service_key:
            logger.error("No database service key configured. Please set DATABASE_KEY or SUPABASE_KEY in .env")
            return False
        
        logger.info("Prerequisites check passed")
        return True
    
    async def test_connectivity(self) -> bool:
        """Test database connectivity."""
        logger.info("Testing database connectivity")
        
        try:
            health = await database_service.health_check()
            
            if health["status"] == "healthy":
                logger.info("Database connectivity test passed")
                return True
            elif health["status"] == "degraded":
                logger.warning("Database connectivity is degraded but functional", checks=health["checks"])
                return True
            else:
                logger.error("Database connectivity test failed", health=health)
                return False
                
        except Exception as e:
            logger.error("Database connectivity test failed", error=str(e))
            return False
    
    async def run_migrations(self, target_version: Optional[str] = None) -> bool:
        """Run database migrations."""
        logger.info("Running database migrations", target_version=target_version)
        
        try:
            # Get migration status before running
            status_before = await migration_manager.get_migration_status()
            logger.info(
                "Migration status before run",
                pending=status_before["pending"],
                applied_count=len(status_before["applied"])
            )
            
            # Run migrations
            result = await migration_manager.migrate(target_version)
            
            if result["failed"]:
                logger.error("Some migrations failed", failed=result["failed"])
                return False
            
            if result["applied"]:
                logger.info("Migrations applied successfully", applied=result["applied"])
            else:
                logger.info("No new migrations to apply")
            
            return True
            
        except Exception as e:
            logger.error("Migration run failed", error=str(e))
            return False
    
    async def verify_schema(self) -> bool:
        """Verify that all expected tables exist."""
        logger.info("Verifying database schema")
        
        expected_tables = [
            "campaigns",
            "ad_groups", 
            "ads",
            "agents",
            "agent_tasks",
            "performance_metrics",
            "compliance_logs",
            "keywords",
            "budget_history",
            "optimization_history",
            "schema_migrations"
        ]
        
        missing_tables = []
        
        for table in expected_tables:
            try:
                exists = await database_service.check_table_exists(table)
                if not exists:
                    missing_tables.append(table)
            except Exception as e:
                logger.error("Failed to check table existence", table=table, error=str(e))
                missing_tables.append(table)
        
        if missing_tables:
            logger.error("Missing database tables", missing=missing_tables)
            return False
        
        logger.info("Schema verification passed", table_count=len(expected_tables))
        return True
    
    async def setup_initial_data(self) -> bool:
        """Set up initial data if needed."""
        logger.info("Setting up initial data")
        
        try:
            # Check if we already have any campaigns (indicating setup was done before)
            campaign_count = await database_service.get_count("campaigns")
            
            if campaign_count > 0:
                logger.info("Initial data already exists, skipping setup", campaign_count=campaign_count)
                return True
            
            # Create a sample agent configuration for testing
            sample_agent_data = {
                "name": "Test Campaign Planner",
                "description": "Sample agent for testing campaign planning capabilities",
                "type": "campaign_planning",
                "status": "created",
                "config": {
                    "model": {
                        "provider": "openai",
                        "model_name": "gpt-4",
                        "temperature": 0.7,
                        "max_tokens": 2000
                    },
                    "memory": {
                        "enabled": True,
                        "memory_type": "vector",
                        "max_entries": 1000
                    },
                    "max_iterations": 10,
                    "timeout_seconds": 300,
                    "verbose": False
                },
                "capabilities": [
                    {
                        "name": "campaign_creation",
                        "description": "Create new advertising campaigns",
                        "input_types": ["campaign_brief"],
                        "output_types": ["campaign_config"]
                    }
                ],
                "version": "1.0.0"
            }
            
            agent_id = await database_service.create_agent(sample_agent_data)
            logger.info("Created sample agent", agent_id=agent_id)
            
            logger.info("Initial data setup completed")
            return True
            
        except Exception as e:
            logger.error("Failed to set up initial data", error=str(e))
            return False
    
    async def run_health_checks(self) -> bool:
        """Run comprehensive health checks."""
        logger.info("Running post-setup health checks")
        
        try:
            health = await database_service.health_check()
            
            # Check each component
            all_healthy = True
            for component, check in health["checks"].items():
                if check["status"] != "healthy":
                    logger.warning(
                        "Component health check failed",
                        component=component,
                        status=check["status"],
                        error=check.get("error")
                    )
                    if check["status"] == "unhealthy":
                        all_healthy = False
                else:
                    logger.info(
                        "Component health check passed",
                        component=component,
                        query_time=check.get("query_time_seconds")
                    )
            
            if all_healthy:
                logger.info("All health checks passed")
                return True
            else:
                logger.warning("Some health checks failed but system is functional")
                return True  # Still return True if system is functional
                
        except Exception as e:
            logger.error("Health checks failed", error=str(e))
            return False
    
    async def cleanup_connections(self) -> None:
        """Clean up database connections."""
        logger.info("Cleaning up database connections")
        
        try:
            await database_service.close_connections()
            logger.info("Database connections closed successfully")
        except Exception as e:
            logger.error("Failed to close database connections", error=str(e))
    
    async def run_full_setup(self, target_version: Optional[str] = None) -> bool:
        """Run complete database setup process."""
        logger.info("Starting database setup", target_version=target_version)
        
        try:
            # Step 1: Check prerequisites
            if not await self.check_prerequisites():
                return False
            
            # Step 2: Test connectivity
            if not await self.test_connectivity():
                return False
            
            # Step 3: Run migrations
            if not await self.run_migrations(target_version):
                return False
            
            # Step 4: Verify schema
            if not await self.verify_schema():
                return False
            
            # Step 5: Setup initial data
            if not await self.setup_initial_data():
                return False
            
            # Step 6: Run health checks
            if not await self.run_health_checks():
                return False
            
            self.setup_complete = True
            logger.info("Database setup completed successfully")
            return True
            
        except Exception as e:
            logger.error("Database setup failed", error=str(e))
            return False
        finally:
            await self.cleanup_connections()


async def main():
    """Main entry point for database setup."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Database setup for AiLex Ad Agent System")
    parser.add_argument(
        "--target-version",
        help="Target migration version to apply up to"
    )
    parser.add_argument(
        "--check-only",
        action="store_true",
        help="Only run health checks without setup"
    )
    parser.add_argument(
        "--migrations-only",
        action="store_true",
        help="Only run migrations without full setup"
    )
    parser.add_argument(
        "--status",
        action="store_true",
        help="Show migration status and exit"
    )
    
    args = parser.parse_args()
    
    setup = DatabaseSetup()
    
    if args.status:
        # Show migration status
        try:
            await database_service.authenticate()
            status = await migration_manager.get_migration_status()
            
            print("\n=== Migration Status ===")
            print(f"Total migrations: {status['total_migrations']}")
            print(f"Applied: {len(status['applied'])}")
            print(f"Pending: {len(status['pending'])}")
            print(f"Failed: {len(status['failed'])}")
            
            if status['last_migration']:
                print(f"Last migration: {status['last_migration']}")
            
            if status['pending']:
                print(f"\nPending migrations: {', '.join(status['pending'])}")
            
            if status['failed']:
                print(f"\nFailed migrations: {', '.join(status['failed'])}")
            
            await setup.cleanup_connections()
            return 0
            
        except Exception as e:
            logger.error("Failed to get migration status", error=str(e))
            return 1
    
    elif args.check_only:
        # Run health checks only
        success = await setup.run_health_checks()
        await setup.cleanup_connections()
        return 0 if success else 1
    
    elif args.migrations_only:
        # Run migrations only
        if not await setup.check_prerequisites():
            return 1
        if not await setup.test_connectivity():
            return 1
        
        success = await setup.run_migrations(args.target_version)
        await setup.cleanup_connections()
        return 0 if success else 1
    
    else:
        # Run full setup
        success = await setup.run_full_setup(args.target_version)
        return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)