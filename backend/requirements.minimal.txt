# Minimal production requirements
# FastAPI and web framework
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
python-multipart>=0.0.6
pydantic>=2.6.1
pydantic-settings>=2.1.0

# Logging and Monitoring (required for services)
structlog>=23.0.0
sentry-sdk[fastapi]>=1.38.0

# Database and Storage
supabase>=2.18.0
asyncpg>=0.28.0
sqlalchemy[asyncio]>=2.0.0
alembic>=1.13.0
psycopg2-binary>=2.9.0

# Google APIs
google-ads>=22.0.0
google-analytics-data>=0.18.0

# AI/ML Models
openai>=1.13.3
google-generativeai>=0.3.0

# AI Agents
crewai>=0.28.0
crewai-tools>=0.1.0

# Task Queue and Caching
celery>=5.3.0
redis>=5.0.0

# Environment and Configuration
python-dotenv>=1.0.0

# HTTP and Requests
requests>=2.31.0
httpx>=0.26.0

# Email Service
resend>=0.8.0
email-validator>=2.0.0

# Data Processing
pandas>=2.1.0
numpy>=1.25.0

# Logging and Monitoring (moved to top)

# Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
cryptography>=41.0.0
itsdangerous>=2.0.0

# Rate Limiting and Protection
slowapi>=0.1.9
limits>=3.8.0

# Additional utilities
aiofiles>=23.0.0