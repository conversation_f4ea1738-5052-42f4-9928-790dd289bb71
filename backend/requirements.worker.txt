# Worker requirements - Full dependencies for CrewAI and background processing
# Includes all heavy dependencies needed for AI agents and data processing

# FastAPI and web framework (minimal for worker health checks)
fastapi>=0.100.0
uvicorn>=0.20.0
pydantic>=2.6.1
pydantic-settings>=2.1.0

# AI Agents and Orchestration
crewai>=0.28.0
crewai-tools>=0.1.0

# Google APIs (heavy dependencies)
google-ads>=22.0.0
google-analytics-data>=0.18.0

# AI/ML Models
openai>=1.13.3
google-generativeai>=0.3.0

# Database and Storage
supabase>=2.18.0
asyncpg>=0.28.0
sqlalchemy[asyncio]>=2.0.0
psycopg2-binary>=2.9.0

# Vector Database
pinecone-client>=2.2.0

# Task Queue and Caching
celery>=5.3.0
redis>=5.0.0

# Data Processing (heavy dependencies)
pandas>=2.1.0
numpy>=1.25.0
scipy>=1.11.0
statsmodels>=0.14.0

# Environment and Configuration
python-dotenv>=1.0.0

# HTTP and Requests
requests>=2.31.0
httpx>=0.26.0

# Logging and Monitoring
structlog>=23.0.0
sentry-sdk>=1.38.0

# Email Service
resend>=0.8.0
email-validator>=2.0.0

# Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
cryptography>=41.0.0
itsdangerous>=2.0.0

# Additional utilities
aiofiles>=23.0.0
