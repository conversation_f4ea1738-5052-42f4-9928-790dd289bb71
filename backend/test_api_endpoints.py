#!/usr/bin/env python3
"""
Test script to validate API endpoints work correctly with the database layer.
This script performs basic integration tests for the main API functionality.
"""

import asyncio
import json
import sys
from typing import Dict, Any

import httpx
import structlog
from datetime import datetime

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.dev.ConsoleRenderer()
    ],
    wrapper_class=structlog.stdlib.BoundLogger,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Test configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"


class APIEndpointTester:
    """Test class for API endpoint validation."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.client = None
        self.test_results = []
        
    async def __aenter__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.aclose()
    
    async def test_endpoint(self, method: str, endpoint: str, expected_status: int = 200, 
                          data: Dict[str, Any] = None, description: str = ""):
        """Test a single API endpoint."""
        url = f"{self.base_url}{endpoint}"
        
        try:
            logger.info(f"Testing {method} {endpoint}", description=description)
            
            if method.upper() == "GET":
                response = await self.client.get(url)
            elif method.upper() == "POST":
                response = await self.client.post(url, json=data)
            elif method.upper() == "PUT":
                response = await self.client.put(url, json=data)
            elif method.upper() == "DELETE":
                response = await self.client.delete(url)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            success = response.status_code == expected_status
            
            result = {
                "endpoint": endpoint,
                "method": method,
                "expected_status": expected_status,
                "actual_status": response.status_code,
                "success": success,
                "description": description,
                "response_size": len(response.content) if response.content else 0,
            }
            
            if success:
                logger.info("✅ Test passed", endpoint=endpoint, status=response.status_code)
            else:
                logger.error("❌ Test failed", endpoint=endpoint, 
                           expected=expected_status, actual=response.status_code)
                if response.content:
                    try:
                        error_data = response.json()
                        logger.error("Error response", error=error_data)
                    except:
                        logger.error("Error response (raw)", content=response.text[:500])
            
            self.test_results.append(result)
            return response
            
        except Exception as e:
            logger.error("Test exception", endpoint=endpoint, error=str(e))
            self.test_results.append({
                "endpoint": endpoint,
                "method": method,
                "expected_status": expected_status,
                "actual_status": "ERROR",
                "success": False,
                "description": description,
                "error": str(e),
            })
            return None
    
    async def run_health_tests(self):
        """Test health check endpoints."""
        logger.info("🔍 Testing Health Check Endpoints")
        
        await self.test_endpoint("GET", "/api/v1/health", 200, 
                               description="Comprehensive health check")
        await self.test_endpoint("GET", "/api/v1/health/liveness", 200,
                               description="Liveness probe")
        await self.test_endpoint("GET", "/api/v1/health/readiness", 200,
                               description="Readiness probe")
    
    async def run_campaign_tests(self):
        """Test campaign management endpoints."""
        logger.info("🔍 Testing Campaign Management Endpoints")
        
        # List campaigns (should work even with empty database)
        response = await self.test_endpoint("GET", "/api/v1/campaigns", 200,
                                          description="List campaigns")
        
        # Create a test campaign
        campaign_data = {
            "name": f"Test Campaign {datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "description": "API integration test campaign",
            "type": "search",
            "budget_amount": 100.0,
            "bidding_strategy": "manual_cpc",
            "target_locations": ["United States"],
            "target_languages": ["en"],
            "keywords": ["test keyword", "api test"],
            "auto_optimization_enabled": True
        }
        
        create_response = await self.test_endpoint("POST", "/api/v1/campaigns", 201, 
                                                 data=campaign_data,
                                                 description="Create test campaign")
        
        if create_response and create_response.status_code == 201:
            try:
                created_campaign = create_response.json()
                campaign_id = created_campaign.get("data", {}).get("id")
                
                if campaign_id:
                    # Test get campaign by ID
                    await self.test_endpoint("GET", f"/api/v1/campaigns/{campaign_id}", 200,
                                           description=f"Get campaign {campaign_id}")
                    
                    # Test campaign metrics
                    await self.test_endpoint("GET", f"/api/v1/campaigns/{campaign_id}/metrics", 200,
                                           description=f"Get campaign metrics")
                    
                    # Test optimization history
                    await self.test_endpoint("GET", f"/api/v1/campaigns/{campaign_id}/optimization-history", 200,
                                           description=f"Get optimization history")
                    
                    # Test campaign update
                    update_data = {
                        "description": "Updated test campaign description",
                        "budget_amount": 150.0
                    }
                    await self.test_endpoint("PUT", f"/api/v1/campaigns/{campaign_id}", 200,
                                           data=update_data,
                                           description=f"Update campaign {campaign_id}")
                    
                else:
                    logger.warning("No campaign ID returned from create campaign")
                    
            except Exception as e:
                logger.error("Failed to parse campaign creation response", error=str(e))
    
    async def run_agent_tests(self):
        """Test agent management endpoints."""
        logger.info("🔍 Testing Agent Management Endpoints")
        
        # List agents
        await self.test_endpoint("GET", "/api/v1/agents", 200,
                               description="List agents")
        
        # Create a test agent
        agent_data = {
            "name": f"Test Agent {datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "description": "API integration test agent",
            "type": "campaign_planning",
            "config": {
                "model": {
                    "provider": "openai",
                    "model_name": "gpt-4",
                    "temperature": 0.7,
                    "max_tokens": 2000
                },
                "max_iterations": 10,
                "timeout_seconds": 300,
                "retry_attempts": 3,
                "verbose": False
            }
        }
        
        create_response = await self.test_endpoint("POST", "/api/v1/agents", 201,
                                                 data=agent_data,
                                                 description="Create test agent")
        
        if create_response and create_response.status_code == 201:
            try:
                created_agent = create_response.json()
                agent_id = created_agent.get("data", {}).get("id")
                
                if agent_id:
                    # Test get agent by ID
                    await self.test_endpoint("GET", f"/api/v1/agents/{agent_id}", 200,
                                           description=f"Get agent {agent_id}")
                    
                    # Test agent tasks
                    await self.test_endpoint("GET", f"/api/v1/agents/{agent_id}/tasks", 200,
                                           description=f"Get agent tasks")
                    
                    # Test agent metrics
                    await self.test_endpoint("GET", f"/api/v1/agents/{agent_id}/metrics", 200,
                                           description=f"Get agent metrics")
                    
            except Exception as e:
                logger.error("Failed to parse agent creation response", error=str(e))
    
    async def run_analytics_tests(self):
        """Test analytics endpoints."""
        logger.info("🔍 Testing Analytics Endpoints")
        
        # Test dashboard data
        await self.test_endpoint("GET", "/api/v1/analytics/dashboard", 200,
                               description="Get dashboard data")
        
        # Test report generation
        await self.test_endpoint("GET", "/api/v1/analytics/reports/campaign_performance", 200,
                               description="Generate campaign performance report")
    
    async def run_all_tests(self):
        """Run all endpoint tests."""
        logger.info("🚀 Starting API Endpoint Integration Tests")
        
        await self.run_health_tests()
        await self.run_campaign_tests()
        await self.run_agent_tests() 
        await self.run_analytics_tests()
        
        # Print summary
        self.print_test_summary()
    
    def print_test_summary(self):
        """Print test results summary."""
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["success"]])
        failed_tests = total_tests - passed_tests
        
        logger.info("📊 Test Summary")
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {failed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            logger.error("❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    logger.error(f"  {result['method']} {result['endpoint']} - {result.get('error', 'Status code mismatch')}")
        
        return failed_tests == 0


async def main():
    """Main test execution function."""
    try:
        async with APIEndpointTester(API_BASE) as tester:
            success = await tester.run_all_tests()
            sys.exit(0 if success else 1)
            
    except Exception as e:
        logger.error("Test execution failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())