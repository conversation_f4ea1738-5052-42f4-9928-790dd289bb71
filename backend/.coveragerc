[run]
source = .
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */site-packages/*
    */venv/*
    */env/*
    .venv/*
    setup.py
    */migrations/*
    */alembic/*
    */conftest.py
    main.py
    # Exclude generated files
    */pb2.py
    */pb2_grpc.py

[report]
# Regexes for lines to exclude from consideration
exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover

    # Don't complain about missing debug-only code:
    def __repr__
    if self\.debug

    # Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError

    # Don't complain if non-runnable code isn't run:
    if 0:
    if __name__ == .__main__.:

    # Don't complain about abstract methods, they aren't run:
    @(abc\.)?abstractmethod

    # Don't complain about type checking blocks:
    if TYPE_CHECKING:

    # Don't complain about protocol methods:
    \.\.\.

precision = 2
show_missing = True
skip_covered = False

[html]
directory = htmlcov
title = AiLex Ad Agent System Test Coverage

[xml]
output = coverage.xml