#!/usr/bin/env python3
"""
Database testing script for the AiLex Ad Agent System.
Validates all database operations, CRUD functionality, and data integrity.
"""

import asyncio
import sys
import uuid
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime, timedelta

import structlog

# Add the backend directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from services.database import database_service
from database.migration_manager import migration_manager
from models.campaigns import CampaignType, BiddingStrategy, Language
from models.agents import AgentType, AgentStatus, TaskStatus, TaskPriority
from utils.config import settings


# Configure logging
structlog.configure(
    processors=[
        structlog.processors.TimeStamper(fmt="ISO"),
        structlog.dev.Console<PERSON>enderer()
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class DatabaseTester:
    """Comprehensive database testing suite."""
    
    def __init__(self):
        self.test_data = {}
        self.cleanup_ids = {
            "campaigns": [],
            "agents": [],
            "agent_tasks": [],
            "ad_groups": [],
            "ads": [],
            "keywords": []
        }
    
    async def setup(self) -> bool:
        """Set up test environment."""
        logger.info("Setting up database test environment")
        
        try:
            # Test database connectivity
            health = await database_service.health_check()
            if health["status"] not in ["healthy", "degraded"]:
                logger.error("Database is not accessible", health=health)
                return False
            
            logger.info("Database test environment ready")
            return True
            
        except Exception as e:
            logger.error("Failed to set up test environment", error=str(e))
            return False
    
    async def test_basic_operations(self) -> bool:
        """Test basic database operations."""
        logger.info("Testing basic database operations")
        
        try:
            # Test health check
            health = await database_service.health_check()
            assert health["status"] in ["healthy", "degraded"], "Database should be accessible"
            
            # Test authentication
            await database_service.authenticate()
            
            # Test table existence checks
            for table in ["campaigns", "agents", "agent_tasks"]:
                exists = await database_service.check_table_exists(table)
                assert exists, f"Table {table} should exist"
            
            # Test table info retrieval
            campaigns_info = await database_service.get_table_info("campaigns")
            assert campaigns_info is not None, "Should be able to get table info"
            assert len(campaigns_info["columns"]) > 0, "Table should have columns"
            
            logger.info("Basic operations test passed")
            return True
            
        except Exception as e:
            logger.error("Basic operations test failed", error=str(e))
            return False
    
    async def test_campaign_operations(self) -> bool:
        """Test campaign CRUD operations."""
        logger.info("Testing campaign operations")
        
        try:
            # Create test campaign
            campaign_data = {
                "name": f"Test Campaign {uuid.uuid4().hex[:8]}",
                "description": "Test campaign for database validation",
                "type": CampaignType.SEARCH.value,
                "budget_amount": 100.00,
                "budget_currency": "USD",
                "bidding_strategy": BiddingStrategy.MANUAL_CPC.value,
                "target_locations": ["US", "CA"],
                "target_languages": [Language.ENGLISH.value],
                "keywords": ["test keyword", "database test"],
                "auto_optimization_enabled": True
            }
            
            # Test create
            campaign_id = await database_service.create_campaign(campaign_data)
            assert campaign_id, "Campaign creation should return ID"
            self.cleanup_ids["campaigns"].append(campaign_id)
            
            logger.info("Campaign created", campaign_id=campaign_id)
            
            # Test read
            campaign = await database_service.get_campaign(campaign_id)
            assert campaign is not None, "Should be able to retrieve created campaign"
            assert campaign["name"] == campaign_data["name"], "Campaign name should match"
            assert campaign["type"] == campaign_data["type"], "Campaign type should match"
            
            # Test update
            update_data = {
                "description": "Updated test campaign description",
                "budget_amount": 150.00
            }
            
            success = await database_service.update_campaign(campaign_id, update_data)
            assert success, "Campaign update should succeed"
            
            # Verify update
            updated_campaign = await database_service.get_campaign(campaign_id)
            assert updated_campaign["description"] == update_data["description"], "Description should be updated"
            assert float(updated_campaign["budget_amount"]) == update_data["budget_amount"], "Budget should be updated"
            
            # Test list
            campaigns = await database_service.list_campaigns(limit=10)
            assert len(campaigns) > 0, "Should have at least one campaign"
            
            found_campaign = next((c for c in campaigns if c["id"] == campaign_id), None)
            assert found_campaign is not None, "Created campaign should be in list"
            
            # Test count
            count = await database_service.get_count("campaigns")
            assert count > 0, "Should have campaign count"
            
            logger.info("Campaign operations test passed")
            return True
            
        except Exception as e:
            logger.error("Campaign operations test failed", error=str(e))
            return False
    
    async def test_agent_operations(self) -> bool:
        """Test agent CRUD operations."""
        logger.info("Testing agent operations")
        
        try:
            # Create test agent
            agent_data = {
                "name": f"Test Agent {uuid.uuid4().hex[:8]}",
                "description": "Test agent for database validation",
                "type": AgentType.CAMPAIGN_PLANNING.value,
                "status": AgentStatus.CREATED.value,
                "config": {
                    "model": {
                        "provider": "openai",
                        "model_name": "gpt-4",
                        "temperature": 0.7,
                        "max_tokens": 2000
                    },
                    "memory": {
                        "enabled": True,
                        "memory_type": "vector",
                        "max_entries": 1000
                    },
                    "max_iterations": 10,
                    "timeout_seconds": 300
                },
                "capabilities": [
                    {
                        "name": "test_capability",
                        "description": "Test capability",
                        "input_types": ["text"],
                        "output_types": ["text"]
                    }
                ],
                "version": "1.0.0"
            }
            
            # Test create
            agent_id = await database_service.create_agent(agent_data)
            assert agent_id, "Agent creation should return ID"
            self.cleanup_ids["agents"].append(agent_id)
            
            logger.info("Agent created", agent_id=agent_id)
            
            # Test read
            agent = await database_service.get_agent(agent_id)
            assert agent is not None, "Should be able to retrieve created agent"
            assert agent["name"] == agent_data["name"], "Agent name should match"
            assert agent["type"] == agent_data["type"], "Agent type should match"
            
            # Test update
            update_data = {
                "description": "Updated test agent description",
                "status": AgentStatus.ACTIVE.value
            }
            
            success = await database_service.update_agent(agent_id, update_data)
            assert success, "Agent update should succeed"
            
            # Verify update
            updated_agent = await database_service.get_agent(agent_id)
            assert updated_agent["description"] == update_data["description"], "Description should be updated"
            assert updated_agent["status"] == update_data["status"], "Status should be updated"
            
            # Test list
            agents = await database_service.list_agents(limit=10)
            assert len(agents) > 0, "Should have at least one agent"
            
            found_agent = next((a for a in agents if a["id"] == agent_id), None)
            assert found_agent is not None, "Created agent should be in list"
            
            logger.info("Agent operations test passed")
            return True
            
        except Exception as e:
            logger.error("Agent operations test failed", error=str(e))
            return False
    
    async def test_agent_task_operations(self) -> bool:
        """Test agent task operations."""
        logger.info("Testing agent task operations")
        
        try:
            # We need an agent first
            if not self.cleanup_ids["agents"]:
                logger.warning("No agents available for task testing")
                return True
            
            agent_id = self.cleanup_ids["agents"][0]
            
            # Create test task
            task_data = {
                "agent_id": agent_id,
                "name": f"Test Task {uuid.uuid4().hex[:8]}",
                "description": "Test task for database validation",
                "type": "test_task",
                "priority": TaskPriority.NORMAL.value,
                "status": TaskStatus.PENDING.value,
                "input_data": {
                    "test_input": "test value",
                    "parameters": {"param1": "value1"}
                },
                "context": {
                    "test_context": "context value"
                },
                "scheduled_at": datetime.utcnow().isoformat()
            }
            
            # Test create
            task_id = await database_service.create_agent_task(task_data)
            assert task_id, "Task creation should return ID"
            self.cleanup_ids["agent_tasks"].append(task_id)
            
            logger.info("Agent task created", task_id=task_id)
            
            # Test read
            task = await database_service.get_agent_task(task_id)
            assert task is not None, "Should be able to retrieve created task"
            assert task["name"] == task_data["name"], "Task name should match"
            assert task["agent_id"] == agent_id, "Agent ID should match"
            
            # Test update
            update_data = {
                "status": TaskStatus.RUNNING.value,
                "started_at": datetime.utcnow().isoformat(),
                "output_data": {
                    "test_output": "test result"
                }
            }
            
            success = await database_service.update_agent_task(task_id, update_data)
            assert success, "Task update should succeed"
            
            # Verify update
            updated_task = await database_service.get_agent_task(task_id)
            assert updated_task["status"] == update_data["status"], "Status should be updated"
            assert updated_task["output_data"] is not None, "Output data should be set"
            
            # Test list by agent
            agent_tasks = await database_service.list_agent_tasks(agent_id=agent_id, limit=10)
            assert len(agent_tasks) > 0, "Should have at least one task for agent"
            
            found_task = next((t for t in agent_tasks if t["id"] == task_id), None)
            assert found_task is not None, "Created task should be in agent's task list"
            
            logger.info("Agent task operations test passed")
            return True
            
        except Exception as e:
            logger.error("Agent task operations test failed", error=str(e))
            return False
    
    async def test_performance_metrics(self) -> bool:
        """Test performance metrics operations."""
        logger.info("Testing performance metrics operations")
        
        try:
            if not self.cleanup_ids["campaigns"]:
                logger.warning("No campaigns available for metrics testing")
                return True
            
            campaign_id = self.cleanup_ids["campaigns"][0]
            
            # Test saving metrics
            metrics_data = {
                "impressions": 1000,
                "clicks": 50,
                "conversions": 5.0,
                "cost": 25.50,
                "ctr": 0.05,
                "cpc": 0.51,
                "conversion_rate": 0.10
            }
            
            today = datetime.utcnow().date()
            success = await database_service.save_campaign_metrics(
                campaign_id, metrics_data, datetime.utcnow()
            )
            assert success, "Saving metrics should succeed"
            
            # Test retrieving metrics
            retrieved_metrics = await database_service.get_campaign_metrics(
                campaign_id,
                start_date=today.isoformat(),
                end_date=today.isoformat()
            )
            
            assert len(retrieved_metrics) > 0, "Should retrieve saved metrics"
            
            logger.info("Performance metrics test passed")
            return True
            
        except Exception as e:
            logger.error("Performance metrics test failed", error=str(e))
            return False
    
    async def test_bulk_operations(self) -> bool:
        """Test bulk database operations."""
        logger.info("Testing bulk operations")
        
        try:
            # Test bulk campaign creation
            bulk_campaigns = []
            for i in range(3):
                campaign_data = {
                    "name": f"Bulk Test Campaign {i} {uuid.uuid4().hex[:8]}",
                    "description": f"Bulk test campaign {i}",
                    "type": CampaignType.SEARCH.value,
                    "budget_amount": 50.00 + (i * 10),
                    "budget_currency": "USD",
                    "bidding_strategy": BiddingStrategy.MANUAL_CPC.value,
                    "target_locations": ["US"],
                    "target_languages": [Language.ENGLISH.value],
                    "auto_optimization_enabled": True
                }
                bulk_campaigns.append(campaign_data)
            
            # Test bulk insert
            created_ids = await database_service.bulk_insert("campaigns", bulk_campaigns)
            assert len(created_ids) == 3, "Should create 3 campaigns"
            self.cleanup_ids["campaigns"].extend(created_ids)
            
            logger.info("Bulk insert test passed", created_count=len(created_ids))
            
            # Test bulk update
            bulk_updates = []
            for campaign_id in created_ids:
                bulk_updates.append({
                    "id": campaign_id,
                    "description": "Updated via bulk operation"
                })
            
            updated_count = await database_service.bulk_update("campaigns", bulk_updates)
            assert updated_count == len(created_ids), "Should update all campaigns"
            
            logger.info("Bulk update test passed", updated_count=updated_count)
            return True
            
        except Exception as e:
            logger.error("Bulk operations test failed", error=str(e))
            return False
    
    async def test_raw_queries(self) -> bool:
        """Test raw SQL query execution."""
        logger.info("Testing raw query operations")
        
        try:
            # Test raw query
            query = """
                SELECT 
                    COUNT(*) as total_campaigns,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_campaigns
                FROM campaigns
                WHERE deleted_at IS NULL;
            """
            
            results = await database_service.execute_raw_query(query)
            assert len(results) == 1, "Should return one result row"
            assert "total_campaigns" in results[0], "Should have total_campaigns column"
            assert "active_campaigns" in results[0], "Should have active_campaigns column"
            
            # Test raw query with parameters
            param_query = """
                SELECT name, type, status
                FROM campaigns
                WHERE type = :campaign_type
                AND deleted_at IS NULL
                LIMIT 5;
            """
            
            param_results = await database_service.execute_raw_query(
                param_query,
                {"campaign_type": CampaignType.SEARCH.value}
            )
            
            # Test raw command
            command = """
                UPDATE campaigns 
                SET last_optimized = NOW() 
                WHERE id = :campaign_id;
            """
            
            if self.cleanup_ids["campaigns"]:
                result = await database_service.execute_raw_command(
                    command,
                    {"campaign_id": self.cleanup_ids["campaigns"][0]}
                )
                assert result is not None, "Raw command should return result"
            
            logger.info("Raw query operations test passed")
            return True
            
        except Exception as e:
            logger.error("Raw query operations test failed", error=str(e))
            return False
    
    async def test_migration_status(self) -> bool:
        """Test migration system functionality."""
        logger.info("Testing migration status")
        
        try:
            # Test migration status retrieval
            status = await migration_manager.get_migration_status()
            
            assert "total_migrations" in status, "Status should include total migrations"
            assert "applied" in status, "Status should include applied migrations"
            assert "pending" in status, "Status should include pending migrations"
            
            # Check that initial migration was applied
            assert len(status["applied"]) > 0, "Should have at least one applied migration"
            assert "001_initial_schema" in status["applied"], "Initial schema should be applied"
            
            logger.info("Migration status test passed", status=status)
            return True
            
        except Exception as e:
            logger.error("Migration status test failed", error=str(e))
            return False
    
    async def cleanup(self) -> None:
        """Clean up test data."""
        logger.info("Cleaning up test data")
        
        try:
            # Clean up in reverse order due to foreign key constraints
            for task_id in self.cleanup_ids["agent_tasks"]:
                try:
                    await database_service.update_agent_task(task_id, {"status": "cancelled"})
                except Exception:
                    pass  # Best effort cleanup
            
            for agent_id in self.cleanup_ids["agents"]:
                try:
                    await database_service.delete_agent(agent_id)
                except Exception:
                    pass  # Best effort cleanup
            
            for campaign_id in self.cleanup_ids["campaigns"]:
                try:
                    await database_service.delete_campaign(campaign_id)
                except Exception:
                    pass  # Best effort cleanup
            
            # Close database connections
            await database_service.close_connections()
            
            logger.info("Cleanup completed")
            
        except Exception as e:
            logger.error("Cleanup failed", error=str(e))
    
    async def run_all_tests(self) -> bool:
        """Run all database tests."""
        logger.info("Starting comprehensive database tests")
        
        test_methods = [
            ("Basic Operations", self.test_basic_operations),
            ("Campaign Operations", self.test_campaign_operations),
            ("Agent Operations", self.test_agent_operations),
            ("Agent Task Operations", self.test_agent_task_operations),
            ("Performance Metrics", self.test_performance_metrics),
            ("Bulk Operations", self.test_bulk_operations),
            ("Raw Queries", self.test_raw_queries),
            ("Migration Status", self.test_migration_status),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_method in test_methods:
            logger.info(f"Running {test_name} test")
            
            try:
                success = await test_method()
                if success:
                    logger.info(f"✓ {test_name} test PASSED")
                    passed += 1
                else:
                    logger.error(f"✗ {test_name} test FAILED")
                    failed += 1
            except Exception as e:
                logger.error(f"✗ {test_name} test FAILED with exception", error=str(e))
                failed += 1
        
        total = passed + failed
        success_rate = (passed / total) * 100 if total > 0 else 0
        
        logger.info(
            "Database testing completed",
            passed=passed,
            failed=failed,
            total=total,
            success_rate=f"{success_rate:.1f}%"
        )
        
        return failed == 0


async def main():
    """Main entry point for database testing."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Database testing for AiLex Ad Agent System")
    parser.add_argument(
        "--test",
        choices=["basic", "campaign", "agent", "metrics", "bulk", "raw", "migration", "all"],
        default="all",
        help="Specific test to run"
    )
    
    args = parser.parse_args()
    
    tester = DatabaseTester()
    
    try:
        # Setup
        if not await tester.setup():
            logger.error("Test setup failed")
            return 1
        
        success = False
        
        if args.test == "all":
            success = await tester.run_all_tests()
        elif args.test == "basic":
            success = await tester.test_basic_operations()
        elif args.test == "campaign":
            success = await tester.test_campaign_operations()
        elif args.test == "agent":
            success = await tester.test_agent_operations()
        elif args.test == "metrics":
            success = await tester.test_performance_metrics()
        elif args.test == "bulk":
            success = await tester.test_bulk_operations()
        elif args.test == "raw":
            success = await tester.test_raw_queries()
        elif args.test == "migration":
            success = await tester.test_migration_status()
        
        return 0 if success else 1
        
    except Exception as e:
        logger.error("Test execution failed", error=str(e))
        return 1
    
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)