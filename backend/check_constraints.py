#!/usr/bin/env python3
"""
Check database constraints and structure.
"""
import asyncio
import asyncpg

DATABASE_URL = "*******************************************************************************/postgres"

async def check_constraints():
    conn = await asyncpg.connect(DATABASE_URL)
    
    # Check constraint definitions
    constraints = await conn.fetch("""
        SELECT 
            tc.constraint_name,
            tc.table_name,
            cc.check_clause
        FROM information_schema.table_constraints tc
        JOIN information_schema.check_constraints cc 
            ON tc.constraint_name = cc.constraint_name
        WHERE tc.table_schema = 'public'
        AND tc.constraint_type = 'CHECK'
        ORDER BY tc.table_name, tc.constraint_name;
    """)
    
    print('Database Constraints:')
    for constraint in constraints:
        print(f'  {constraint["table_name"]}.{constraint["constraint_name"]}: {constraint["check_clause"]}')
    
    # Check campaign table structure
    columns = await conn.fetch("""
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'campaigns'
        ORDER BY ordinal_position;
    """)
    
    print('\nCampaigns table structure:')
    for col in columns:
        print(f'  {col["column_name"]}: {col["data_type"]} (nullable: {col["is_nullable"]}, default: {col["column_default"]})')
    
    await conn.close()

if __name__ == "__main__":
    asyncio.run(check_constraints())