"""
Simple test script to verify the AI agent orchestration system.
Run this to test the system without full workflow execution.
"""

import asyncio
import sys
import traceback
from typing import Dict, Any

import structlog

# Set up logging
logger = structlog.get_logger(__name__)


async def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        # Test agent imports
        from agents.orchestrator import campaign_orchestrator, CampaignRequest
        from agents.base import BaseAiLexAgent, AgentError
        from agents.core.campaign_planning import CampaignPlanningAgent
        from agents.core.ad_asset_generation import AdAssetGenerationAgent
        from agents.core.audience_targeting import AudienceTargetingAgent
        from agents.core.budget_management import BudgetManagementAgent
        from agents.core.performance_analysis import PerformanceAnalysisAgent
        
        # Test service imports
        from services.gemini_service import gemini_service, GoogleGeminiService
        
        # Test model imports
        from models.agents import AgentType, AgentConfig, AgentStatus
        
        print("✅ All imports successful")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all dependencies are installed:")
        print("- pip install google-generativeai")
        print("- pip install crewai")
        print("- pip install structlog")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during imports: {e}")
        traceback.print_exc()
        return False


async def test_configuration():
    """Test system configuration."""
    print("Testing configuration...")
    
    try:
        from utils.config import settings
        
        # Check basic config
        print(f"App Name: {settings.APP_NAME}")
        print(f"Environment: {settings.ENVIRONMENT}")
        
        # Check Gemini configuration
        if settings.GEMINI_API_KEY:
            print("✅ Gemini API key configured")
        else:
            print("⚠️  Gemini API key not configured - will use fallback responses")
        
        # Check other important settings
        if hasattr(settings, 'DATABASE_URL') or hasattr(settings, 'SUPABASE_URL'):
            print("✅ Database configuration found")
        else:
            print("⚠️  No database configuration found")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False


async def test_gemini_service():
    """Test Gemini service functionality."""
    print("Testing Gemini service...")
    
    try:
        from services.gemini_service import gemini_service
        from utils.config import settings
        
        if not settings.GEMINI_API_KEY:
            print("⚠️  Skipping Gemini test - API key not configured")
            return True
        
        # Test authentication
        await gemini_service.authenticate()
        print("✅ Gemini service authenticated")
        
        # Test health check
        health = await gemini_service.health_check()
        if health["status"] == "healthy":
            print("✅ Gemini service health check passed")
        else:
            print(f"❌ Gemini service health check failed: {health}")
            return False
        
        # Test basic text generation
        response = await gemini_service.generate_text(
            prompt="Say 'Hello from Gemini AI!'",
            max_tokens=50
        )
        print(f"✅ Text generation test: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini service error: {e}")
        traceback.print_exc()
        return False


async def test_agent_creation():
    """Test individual agent creation."""
    print("Testing agent creation...")
    
    try:
        from models.agents import AgentConfig, AgentType
        from agents.core.campaign_planning import CampaignPlanningAgent
        from agents.core.ad_asset_generation import AdAssetGenerationAgent
        from agents.core.audience_targeting import AudienceTargetingAgent
        
        # Create basic agent config
        config = AgentConfig(
            timeout_seconds=60,
            max_iterations=2,
            verbose=False,
            allow_delegation=False,
            memory={"enabled": False},
            model={
                "model_name": "gemini-1.5-flash",
                "temperature": 0.7,
                "max_tokens": 500
            }
        )
        
        # Test creating different agent types
        agents_to_test = [
            (CampaignPlanningAgent, "campaign_planning_test"),
            (AdAssetGenerationAgent, "ad_generation_test"),
            (AudienceTargetingAgent, "audience_test")
        ]
        
        for agent_class, agent_id in agents_to_test:
            try:
                agent = agent_class(agent_id, config)
                print(f"✅ Created {agent_class.__name__}")
            except Exception as e:
                print(f"❌ Failed to create {agent_class.__name__}: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Agent creation error: {e}")
        traceback.print_exc()
        return False


async def test_orchestrator_basic():
    """Test basic orchestrator functionality."""
    print("Testing orchestrator initialization...")
    
    try:
        from agents.orchestrator import campaign_orchestrator
        
        # Test initialization
        await campaign_orchestrator.initialize()
        print("✅ Orchestrator initialized")
        
        # Test status
        status = await campaign_orchestrator.get_orchestrator_status()
        print(f"✅ Orchestrator status: {status['initialized']}")
        print(f"   Available agents: {len(status['available_agents'])}")
        print(f"   Agent types: {status['available_agents']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator error: {e}")
        traceback.print_exc()
        return False


async def test_workflow_creation():
    """Test workflow creation without execution."""
    print("Testing workflow creation...")
    
    try:
        from agents.orchestrator import campaign_orchestrator, CampaignRequest
        
        # Ensure orchestrator is initialized
        if not campaign_orchestrator.initialized:
            await campaign_orchestrator.initialize()
        
        # Create a test campaign request
        campaign_request = CampaignRequest(
            business_description="Test legal tech company",
            industry="Legal Technology",
            target_audience="Law firms",
            campaign_objectives=["Generate leads"],
            budget=1000.0,
            duration_days=7,
            location="US",
            additional_requirements={"user_id": "test_user"}
        )
        
        # Create workflow
        workflow = await campaign_orchestrator.create_campaign_workflow(campaign_request)
        print(f"✅ Workflow created: {workflow.workflow_id}")
        print(f"   Name: {workflow.name}")
        print(f"   Tasks: {len(workflow.tasks)}")
        print(f"   Status: {workflow.status.value}")
        
        # List tasks
        for i, task in enumerate(workflow.tasks, 1):
            print(f"   {i}. {task.name} ({task.agent_type.value})")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow creation error: {e}")
        traceback.print_exc()
        return False


async def run_all_tests():
    """Run all tests and return summary."""
    print("🧪 AiLex AI Agent Orchestration System - Test Suite")
    print("=" * 60)
    print()
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configuration),
        ("Gemini Service Test", test_gemini_service),
        ("Agent Creation Test", test_agent_creation),
        ("Orchestrator Basic Test", test_orchestrator_basic),
        ("Workflow Creation Test", test_workflow_creation),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            success = await test_func()
            results[test_name] = success
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
        print()
    
    # Summary
    print("=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for use.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False


async def main():
    """Main test function."""
    try:
        success = await run_all_tests()
        
        if success:
            print("\n🚀 System validation complete - ready for production use!")
            print("\nTo run the full demo:")
            print("python example_orchestration.py")
            sys.exit(0)
        else:
            print("\n❌ System validation failed - check configuration and dependencies")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during testing: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())