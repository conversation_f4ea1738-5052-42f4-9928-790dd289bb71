#!/usr/bin/env python3
"""
Simplified API Testing Suite for Google Ads AI Agent System - Phase 2
Uses FastAPI TestClient for direct testing without external server dependency.
"""

import json
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from unittest.mock import patch, AsyncMock

import structlog
from fastapi.testclient import TestClient

# Configure logging for tests
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.dev.Console<PERSON>enderer(colors=True)
    ],
    wrapper_class=structlog.stdlib.BoundLogger,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class TestResult:
    """Individual test result container."""
    
    def __init__(self, name: str, endpoint: str, method: str):
        self.name = name
        self.endpoint = endpoint
        self.method = method
        self.success = False
        self.status_code = None
        self.expected_status = None
        self.response_time = None
        self.error = None
        self.response_data = None
        
    def mark_success(self, status_code: int, expected_status: int, response_time: float, response_data: Any = None):
        self.success = True
        self.status_code = status_code
        self.expected_status = expected_status
        self.response_time = response_time
        self.response_data = response_data
        
    def mark_failure(self, status_code: int, expected_status: int, response_time: float, error: str = None):
        self.success = False
        self.status_code = status_code
        self.expected_status = expected_status
        self.response_time = response_time
        self.error = error


class SimpleAPITester:
    """Simplified API testing using FastAPI TestClient."""
    
    def __init__(self):
        self.client = None
        self.results: List[TestResult] = []
        self.test_data = {}
        
    def setup(self):
        """Setup test client."""
        try:
            # Import and create app
            from main import create_app
            app = create_app()
            self.client = TestClient(app)
            logger.info("✅ FastAPI TestClient created successfully")
            return True
        except Exception as e:
            logger.error("❌ Failed to create TestClient", error=str(e))
            return False
    
    def execute_test(self, name: str, method: str, endpoint: str, 
                    expected_status: int = 200, data: Dict[str, Any] = None,
                    headers: Dict[str, str] = None) -> TestResult:
        """Execute a single API test."""
        result = TestResult(name, endpoint, method)
        
        try:
            start_time = time.time()
            
            # Execute HTTP request using TestClient
            if method.upper() == "GET":
                response = self.client.get(endpoint, headers=headers)
            elif method.upper() == "POST":
                response = self.client.post(endpoint, json=data, headers=headers)
            elif method.upper() == "PUT":
                response = self.client.put(endpoint, json=data, headers=headers)
            elif method.upper() == "PATCH":
                response = self.client.patch(endpoint, json=data, headers=headers)
            elif method.upper() == "DELETE":
                response = self.client.delete(endpoint, headers=headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
            response_time = time.time() - start_time
            
            # Parse response
            response_data = None
            try:
                response_data = response.json() if response.content else {}
            except json.JSONDecodeError:
                response_data = {"raw_response": response.text}
            
            # Evaluate result
            if response.status_code == expected_status:
                result.mark_success(response.status_code, expected_status, response_time, response_data)
                logger.info(f"✅ {name}", status=response.status_code, time=f"{response_time:.3f}s")
            else:
                result.mark_failure(response.status_code, expected_status, response_time, 
                                  f"Status mismatch: expected {expected_status}, got {response.status_code}")
                logger.error(f"❌ {name}", expected=expected_status, actual=response.status_code)
                if response_data and isinstance(response_data, dict) and 'detail' in response_data:
                    logger.error("Error details", detail=response_data['detail'])
            
        except Exception as e:
            response_time = time.time() - start_time if 'start_time' in locals() else 0
            result.mark_failure(None, expected_status, response_time, str(e))
            logger.error(f"❌ {name} - Exception", error=str(e))
        
        self.results.append(result)
        return result

    def test_server_startup(self):
        """Test basic server functionality."""
        logger.info("🚀 Testing Server Startup and Configuration")
        
        # Test root endpoint
        self.execute_test("Root endpoint", "GET", "/", 200)
        
        # Test API documentation endpoints (might be disabled in production)
        self.execute_test("OpenAPI schema", "GET", "/openapi.json", 200)

    def test_health_endpoints(self):
        """Test health check endpoints."""
        logger.info("🔍 Testing Health Check Endpoints")
        
        # Mock database and external services for health checks
        with patch('services.database.DatabaseService.health_check') as mock_db_health:
            mock_db_health.return_value = {"status": "healthy", "connection": "ok"}
            
            with patch('services.google_ads.GoogleAdsService.health_check') as mock_gads_health:
                mock_gads_health.return_value = {"status": "healthy", "api_access": "ok"}
                
                # Comprehensive health check
                result = self.execute_test("Comprehensive health check", "GET", "/api/v1/health", 200)
                
                # Liveness probe
                self.execute_test("Liveness probe", "GET", "/api/v1/health/liveness", 200)
                
                # Readiness probe  
                self.execute_test("Readiness probe", "GET", "/api/v1/health/readiness", 200)

    def test_campaign_management(self):
        """Test campaign management endpoints."""
        logger.info("🎯 Testing Campaign Management Endpoints")
        
        # Mock database operations
        with patch('services.database.DatabaseService.execute_query') as mock_execute:
            with patch('services.database.DatabaseService.fetch_one') as mock_fetch_one:
                with patch('services.database.DatabaseService.fetch_all') as mock_fetch_all:
                    
                    # Setup mock returns
                    mock_fetch_all.return_value = []  # Empty campaigns list initially
                    
                    # 1. List campaigns (empty)
                    self.execute_test("List campaigns (empty)", "GET", "/api/v1/campaigns", 200)
                    
                    # 2. Create campaign
                    campaign_data = {
                        "name": f"Test Campaign {datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        "description": "Test campaign for API validation",
                        "type": "search",
                        "budget_amount": 100.0,
                        "budget_type": "daily",
                        "bidding_strategy": "manual_cpc",
                        "target_locations": ["United States"],
                        "target_languages": ["en"],
                        "keywords": ["test keyword", "api test"],
                        "auto_optimization_enabled": True
                    }
                    
                    # Mock successful campaign creation
                    mock_campaign_id = "test-campaign-123"
                    mock_fetch_one.return_value = {
                        "id": mock_campaign_id,
                        "name": campaign_data["name"],
                        "status": "active",
                        "created_at": datetime.now().isoformat(),
                        **campaign_data
                    }
                    
                    create_result = self.execute_test(
                        "Create campaign", "POST", "/api/v1/campaigns", 201, data=campaign_data
                    )
                    
                    # Store campaign ID for further tests
                    if create_result.success and create_result.response_data:
                        campaign_id = mock_campaign_id
                        self.test_data['campaign_id'] = campaign_id
                        
                        # 3. Get campaign by ID
                        self.execute_test(
                            "Get campaign by ID", "GET", f"/api/v1/campaigns/{campaign_id}", 200
                        )
                        
                        # 4. Update campaign
                        update_data = {
                            "description": "Updated test campaign",
                            "budget_amount": 150.0
                        }
                        self.execute_test(
                            "Update campaign", "PUT", f"/api/v1/campaigns/{campaign_id}", 200, data=update_data
                        )
                        
                        # 5. Get campaign metrics
                        mock_fetch_one.return_value = {
                            "campaign_id": campaign_id,
                            "impressions": 1000,
                            "clicks": 50,
                            "cost": 25.50,
                            "conversions": 5,
                            "date": datetime.now().date().isoformat()
                        }
                        self.execute_test(
                            "Get campaign metrics", "GET", f"/api/v1/campaigns/{campaign_id}/metrics", 200
                        )
                        
                        # 6. Trigger optimization
                        self.execute_test(
                            "Trigger campaign optimization", "POST", f"/api/v1/campaigns/{campaign_id}/optimize", 200
                        )
                    
                    # 7. Test validation errors
                    invalid_campaign = {
                        "name": "",  # Invalid: empty name
                        "type": "invalid_type",  # Invalid type
                        "budget_amount": -10  # Invalid: negative budget
                    }
                    self.execute_test(
                        "Create campaign with invalid data", "POST", "/api/v1/campaigns", 422, data=invalid_campaign
                    )
                    
                    # 8. Test 404 error
                    self.execute_test(
                        "Get non-existent campaign", "GET", "/api/v1/campaigns/non-existent", 404
                    )

    def test_agent_management(self):
        """Test agent management endpoints."""
        logger.info("🤖 Testing Agent Management Endpoints")
        
        with patch('services.database.DatabaseService.execute_query') as mock_execute:
            with patch('services.database.DatabaseService.fetch_one') as mock_fetch_one:
                with patch('services.database.DatabaseService.fetch_all') as mock_fetch_all:
                    
                    # Setup mock returns
                    mock_fetch_all.return_value = []  # Empty agents list initially
                    
                    # 1. List agents
                    self.execute_test("List agents", "GET", "/api/v1/agents", 200)
                    
                    # 2. Create agent
                    agent_data = {
                        "name": f"Test Agent {datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        "description": "Test agent for API validation",
                        "type": "campaign_planning",
                        "config": {
                            "model": {
                                "provider": "openai",
                                "model_name": "gpt-4",
                                "temperature": 0.7,
                                "max_tokens": 2000
                            },
                            "max_iterations": 10,
                            "timeout_seconds": 300
                        }
                    }
                    
                    # Mock successful agent creation
                    mock_agent_id = "test-agent-456"
                    mock_fetch_one.return_value = {
                        "id": mock_agent_id,
                        "name": agent_data["name"],
                        "status": "active",
                        "created_at": datetime.now().isoformat(),
                        **agent_data
                    }
                    
                    create_result = self.execute_test(
                        "Create agent", "POST", "/api/v1/agents", 201, data=agent_data
                    )
                    
                    if create_result.success:
                        agent_id = mock_agent_id
                        self.test_data['agent_id'] = agent_id
                        
                        # 3. Get agent by ID
                        self.execute_test(
                            "Get agent by ID", "GET", f"/api/v1/agents/{agent_id}", 200
                        )
                        
                        # 4. Get agent tasks
                        mock_fetch_all.return_value = []  # No tasks initially
                        self.execute_test(
                            "Get agent tasks", "GET", f"/api/v1/agents/{agent_id}/tasks", 200
                        )
                        
                        # 5. Create agent task
                        task_data = {
                            "type": "campaign_analysis",
                            "priority": "medium",
                            "config": {
                                "campaign_id": self.test_data.get('campaign_id', 'test-campaign-123'),
                                "analysis_type": "performance_review"
                            }
                        }
                        self.execute_test(
                            "Create agent task", "POST", f"/api/v1/agents/{agent_id}/tasks", 201, data=task_data
                        )
                    
                    # 6. Test validation errors
                    invalid_agent = {
                        "name": "",  # Invalid: empty name
                        "type": "invalid_type",  # Invalid type
                    }
                    self.execute_test(
                        "Create agent with invalid data", "POST", "/api/v1/agents", 422, data=invalid_agent
                    )

    def test_google_ads_integration(self):
        """Test Google Ads integration endpoints."""
        logger.info("📊 Testing Google Ads Integration Endpoints")
        
        # Mock Google Ads service
        with patch('services.google_ads.GoogleAdsService.health_check') as mock_health:
            with patch('services.google_ads.GoogleAdsService.sync_campaigns') as mock_sync:
                with patch('services.google_ads.GoogleAdsService.get_account_info') as mock_account:
                    
                    # Setup mocks
                    mock_health.return_value = {"status": "healthy", "api_access": "ok"}
                    mock_sync.return_value = {"synced_campaigns": 5, "status": "success"}
                    mock_account.return_value = {
                        "customer_id": "************",
                        "name": "Test Account",
                        "currency": "USD"
                    }
                    
                    # 1. Health check
                    self.execute_test("Google Ads API health check", "GET", "/api/v1/google-ads/health", 200)
                    
                    # 2. Sync campaigns
                    self.execute_test("Sync campaigns from Google Ads", "POST", "/api/v1/google-ads/sync/campaigns", 200)
                    
                    # 3. Get account info
                    self.execute_test("Get Google Ads account info", "GET", "/api/v1/google-ads/account", 200)

    def test_analytics_endpoints(self):
        """Test analytics endpoints."""
        logger.info("📈 Testing Analytics Endpoints")
        
        with patch('services.database.DatabaseService.fetch_all') as mock_fetch_all:
            with patch('services.database.DatabaseService.fetch_one') as mock_fetch_one:
                
                # Setup mock analytics data
                mock_fetch_one.return_value = {
                    "total_campaigns": 5,
                    "active_campaigns": 3,
                    "total_spend": 1250.75,
                    "total_conversions": 25
                }
                
                # 1. Dashboard data
                self.execute_test("Get dashboard data", "GET", "/api/v1/analytics/dashboard", 200)
                
                # 2. Campaign performance report
                self.execute_test("Campaign performance report", "GET", "/api/v1/analytics/reports/campaign_performance", 200)
                
                # 3. Agent performance report
                self.execute_test("Agent performance report", "GET", "/api/v1/analytics/reports/agent_performance", 200)

    def test_error_handling(self):
        """Test error handling scenarios."""
        logger.info("🚨 Testing Error Handling Scenarios")
        
        # 1. 404 errors
        self.execute_test("Non-existent endpoint", "GET", "/api/v1/non-existent", 404)
        
        # 2. 405 method not allowed
        self.execute_test("Method not allowed", "POST", "/api/v1/health/liveness", 405)

    def run_all_tests(self):
        """Execute all tests."""
        logger.info("🚀 Starting Simplified API Test Suite")
        start_time = time.time()
        
        if not self.setup():
            logger.error("❌ Failed to setup test client")
            return False
        
        try:
            # Execute all test categories
            self.test_server_startup()
            self.test_health_endpoints()
            self.test_campaign_management()
            self.test_agent_management()
            self.test_google_ads_integration()
            self.test_analytics_endpoints()
            self.test_error_handling()
            
        except Exception as e:
            logger.error("Test execution failed", error=str(e))
            
        total_time = time.time() - start_time
        return self.print_summary(total_time)

    def print_summary(self, total_time: float) -> bool:
        """Print test results summary."""
        logger.info("=" * 80)
        logger.info("📊 API TEST SUITE RESULTS")
        logger.info("=" * 80)
        
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r.success])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # Overall statistics
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {failed_tests}")
        logger.info(f"Success Rate: {success_rate:.1f}%")
        logger.info(f"Total Time: {total_time:.2f}s")
        
        # Performance statistics
        response_times = [r.response_time for r in self.results if r.response_time]
        if response_times:
            avg_response = sum(response_times) / len(response_times)
            logger.info(f"Avg Response Time: {avg_response:.3f}s")
        
        # Test breakdown by category
        categories = {}
        for result in self.results:
            category = result.name.split(' ')[0]
            if category not in categories:
                categories[category] = {"total": 0, "passed": 0}
            categories[category]["total"] += 1
            if result.success:
                categories[category]["passed"] += 1
        
        logger.info("\n📋 Results by Category:")
        for category, stats in categories.items():
            rate = (stats["passed"] / stats["total"] * 100) if stats["total"] > 0 else 0
            logger.info(f"  {category}: {stats['passed']}/{stats['total']} ({rate:.1f}%)")
        
        # Failed tests details
        if failed_tests > 0:
            logger.info("\n❌ Failed Tests:")
            for result in self.results:
                if not result.success:
                    logger.error(f"  {result.method} {result.endpoint}")
                    logger.error(f"    Test: {result.name}")
                    if result.error:
                        logger.error(f"    Error: {result.error}")
        
        # Final assessment
        logger.info("=" * 80)
        if success_rate >= 90:
            logger.info("🎉 PHASE 2 API IMPLEMENTATION: EXCELLENT")
            logger.info("✅ All critical endpoints are working correctly")
            status = "EXCELLENT"
        elif success_rate >= 80:
            logger.info("🟡 PHASE 2 API IMPLEMENTATION: GOOD")
            logger.info("⚠️  Some minor issues need attention")
            status = "GOOD"
        elif success_rate >= 70:
            logger.info("🟠 PHASE 2 API IMPLEMENTATION: NEEDS IMPROVEMENT")
            logger.info("⚠️  Several issues require fixes")
            status = "NEEDS_IMPROVEMENT"
        else:
            logger.info("🔴 PHASE 2 API IMPLEMENTATION: CRITICAL ISSUES")
            logger.info("❌ Major problems need immediate attention")
            status = "CRITICAL_ISSUES"
        
        # Store results for summary report
        self.test_data['final_status'] = status
        self.test_data['success_rate'] = success_rate
        self.test_data['total_tests'] = total_tests
        self.test_data['passed_tests'] = passed_tests
        
        logger.info("=" * 80)
        
        return success_rate >= 80  # 80% threshold for success


def main():
    """Main test execution function."""
    logger.info("🚀 Google Ads AI Agent System - Simplified API Test Suite")
    logger.info("Phase 2 Implementation Validation")
    
    try:
        tester = SimpleAPITester()
        success = tester.run_all_tests()
        
        # Generate summary report
        logger.info("\n" + "="*60)
        logger.info("PHASE 2 IMPLEMENTATION STATUS SUMMARY")
        logger.info("="*60)
        logger.info(f"Final Status: {tester.test_data.get('final_status', 'Unknown')}")
        logger.info(f"Success Rate: {tester.test_data.get('success_rate', 0):.1f}%")
        logger.info(f"Tests Passed: {tester.test_data.get('passed_tests', 0)}/{tester.test_data.get('total_tests', 0)}")
        logger.info("="*60)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("Test execution cancelled by user")
        sys.exit(130)
    except Exception as e:
        logger.error("Test suite execution failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()