#!/usr/bin/env python3
"""
Test the auth fix with proper key usage.
"""

import asyncio
import os
import sys

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_auth_fix():
    """Test the auth service with the fix."""
    print("🔧 Testing Auth Service Fix")
    print("=" * 40)
    
    try:
        from services.auth import auth_service
        from utils.config import settings
        
        print(f"SUPABASE_ANON_KEY configured: {'✓' if settings.SUPABASE_ANON_KEY else '❌'}")
        print(f"SERVICE_ROLE_KEY configured: {'✓' if settings.SUPABASE_SERVICE_ROLE_KEY else '❌'}")
        
        # Initialize the service
        await auth_service.authenticate()
        print("✅ Auth service initialized successfully")
        
        # Test health check
        health = await auth_service.health_check()
        print(f"✅ Health check: {health['status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Auth service error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_auth_fix())
    if success:
        print("\n✅ Auth fix is working locally!")
    else:
        print("\n❌ Auth fix needs more work.")