#!/usr/bin/env python3
"""
Simple test script to verify Supabase database connection.
"""
import asyncio
import asyncpg
from urllib.parse import quote_plus

async def test_connection():
    # URL encode the password to handle special characters
    password = "ShakaSenghor189!"
    encoded_password = quote_plus(password)
    
    connection_string = f"postgresql://postgres:{encoded_password}@db.pamppqrhytvyclvdbbxx.supabase.co:5432/postgres"
    
    print("Testing database connection...")
    print(f"Connection string: postgresql://postgres:{password}@db.pamppqrhytvyclvdbbxx.supabase.co:5432/postgres")
    
    try:
        # Test direct connection
        conn = await asyncpg.connect(connection_string)
        
        # Test basic query
        result = await conn.fetchrow('SELECT version();')
        print(f"✅ Connection successful!")
        print(f"PostgreSQL version: {result[0]}")
        
        # Test creating a simple table
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS test_connection (
                id SERIAL PRIMARY KEY,
                name TEXT,
                created_at TIMESTAMP DEFAULT NOW()
            );
        ''')
        print("✅ Table creation successful!")
        
        # Test inserting data
        await conn.execute(
            'INSERT INTO test_connection (name) VALUES ($1)',
            'Test connection'
        )
        print("✅ Data insertion successful!")
        
        # Test querying data
        rows = await conn.fetch('SELECT * FROM test_connection;')
        print(f"✅ Data query successful! Found {len(rows)} records")
        
        # Clean up
        await conn.execute('DROP TABLE test_connection;')
        print("✅ Cleanup successful!")
        
        await conn.close()
        print("\n🎉 All database tests passed! Connection is working perfectly.")
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print(f"Error type: {type(e)}")
        
        # Try alternative connection methods
        print("\nTrying alternative connection methods...")
        
        try:
            # Try with SSL disabled
            alt_string = f"postgresql://postgres:{encoded_password}@db.pamppqrhytvyclvdbbxx.supabase.co:5432/postgres?sslmode=disable"
            conn = await asyncpg.connect(alt_string)
            print("✅ Connection successful with SSL disabled!")
            await conn.close()
        except Exception as e2:
            print(f"❌ Alternative connection also failed: {e2}")

if __name__ == "__main__":
    asyncio.run(test_connection())