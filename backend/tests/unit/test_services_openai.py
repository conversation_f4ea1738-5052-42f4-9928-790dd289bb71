"""
Unit tests for OpenAI service.
Tests OpenAI API integration, content generation, and AI-powered features.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List

from services.openai_service import OpenAIService
from utils.exceptions import OpenAIException


class TestOpenAIService:
    """Test OpenAIService functionality."""
    
    @pytest.fixture
    def openai_service(self):
        """Create OpenAIService instance."""
        with patch('services.openai_service.settings') as mock_settings:
            mock_settings.OPENAI_API_KEY = "test-api-key"
            mock_settings.OPENAI_MODEL = "gpt-4"
            
            service = OpenAIService()
            return service
    
    @pytest.fixture
    def mock_openai_client(self):
        """Create mock OpenAI client."""
        mock_client = AsyncMock()
        
        # Mock chat completion response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = "Generated content"
        mock_response.usage.total_tokens = 150
        
        mock_client.chat.completions.create = AsyncMock(return_value=mock_response)
        
        return mock_client
    
    def test_service_initialization(self, openai_service):
        """Test OpenAIService initialization."""
        assert openai_service.service_name == "OpenAI API"
        assert openai_service.rate_limit_per_minute == 3000
        assert openai_service._auth_token == "test-api-key"
        assert openai_service._model == "gpt-4"
        assert openai_service._client is None
    
    def test_default_parameters(self, openai_service):
        """Test default model parameters."""
        params = openai_service._default_params
        
        assert params["temperature"] == 0.7
        assert params["max_tokens"] == 2000
        assert params["top_p"] == 0.9
        assert params["frequency_penalty"] == 0.0
        assert params["presence_penalty"] == 0.0
    
    @pytest.mark.asyncio
    async def test_perform_authentication_success(self, openai_service):
        """Test successful authentication."""
        with patch('services.openai_service.AsyncOpenAI') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            
            await openai_service._perform_authentication()
            
            assert openai_service._client == mock_client
            mock_client_class.assert_called_once_with(api_key="test-api-key")
    
    @pytest.mark.asyncio
    async def test_perform_authentication_missing_key(self, openai_service):
        """Test authentication with missing API key."""
        openai_service._auth_token = None
        
        with pytest.raises(OpenAIException) as exc_info:
            await openai_service._perform_authentication()
        
        assert "OpenAI API key not configured" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_execute_request_success(self, openai_service, mock_openai_client):
        """Test successful request execution."""
        openai_service._client = mock_openai_client
        
        response = await openai_service._execute_request(
            "POST", "chat/completions", 
            messages=[{"role": "user", "content": "Hello"}]
        )
        
        assert response is not None
        mock_openai_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_request_openai_exception(self, openai_service, mock_openai_client):
        """Test request execution with OpenAI exception."""
        openai_service._client = mock_openai_client
        
        # Mock OpenAI exception
        import openai
        mock_openai_client.chat.completions.create.side_effect = openai.APIError("API Error")
        
        with pytest.raises(Exception):  # Will be wrapped as general exception
            await openai_service._execute_request(
                "POST", "chat/completions", 
                messages=[{"role": "user", "content": "Hello"}]
            )
    
    @pytest.mark.asyncio
    async def test_health_check_success(self, openai_service, mock_openai_client):
        """Test successful health check."""
        openai_service._client = mock_openai_client
        
        # Mock successful completion
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = "healthy"
        mock_response.usage.total_tokens = 10
        
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        health = await openai_service.health_check()
        
        assert health["status"] == "healthy"
        assert health["connected"] is True
        assert health["model"] == "gpt-4"
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, openai_service):
        """Test health check failure."""
        # No client set (not authenticated)
        health = await openai_service.health_check()
        
        assert health["status"] == "unhealthy"
        assert health["connected"] is False
        assert "error" in health
    
    @pytest.mark.asyncio
    async def test_generate_text_success(self, openai_service, mock_openai_client):
        """Test successful text generation."""
        openai_service._client = mock_openai_client
        
        result = await openai_service.generate_text(
            prompt="Write a catchy headline for running shoes",
            max_tokens=50,
            temperature=0.8
        )
        
        assert result is not None
        assert "content" in result
        assert "usage" in result
        assert result["content"] == "Generated content"
        
        # Check that parameters were passed correctly
        mock_openai_client.chat.completions.create.assert_called_once()
        call_args = mock_openai_client.chat.completions.create.call_args
        assert call_args.kwargs["max_tokens"] == 50
        assert call_args.kwargs["temperature"] == 0.8
    
    @pytest.mark.asyncio
    async def test_generate_ad_copy_success(self, openai_service, mock_openai_client):
        """Test successful ad copy generation."""
        openai_service._client = mock_openai_client
        
        # Mock structured response
        structured_response = {
            "headlines": ["Great Running Shoes!", "Run Faster Today!"],
            "descriptions": ["The best shoes for runners.", "Comfortable and durable."]
        }
        
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = str(structured_response)
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        result = await openai_service.generate_ad_copy(
            product="Running Shoes",
            target_audience="Fitness enthusiasts",
            keywords=["running", "shoes", "athletic"]
        )
        
        assert result is not None
        assert "headlines" in result or "content" in result  # Flexible assertion
        mock_openai_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_analyze_performance_success(self, openai_service, mock_openai_client):
        """Test successful performance analysis."""
        openai_service._client = mock_openai_client
        
        # Mock analysis response
        analysis_response = {
            "insights": ["CTR is below average for this industry"],
            "recommendations": ["Increase bid on high-performing keywords"],
            "sentiment": "negative"
        }
        
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = str(analysis_response)
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        performance_data = {
            "impressions": 10000,
            "clicks": 250,
            "conversions": 15,
            "cost": 500.00
        }
        
        result = await openai_service.analyze_performance(performance_data)
        
        assert result is not None
        mock_openai_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_keywords_success(self, openai_service, mock_openai_client):
        """Test successful keyword generation."""
        openai_service._client = mock_openai_client
        
        # Mock keyword response
        keywords_response = {
            "keywords": [
                {"text": "running shoes", "match_type": "phrase", "volume": "high"},
                {"text": "athletic footwear", "match_type": "broad", "volume": "medium"},
                {"text": "jogging sneakers", "match_type": "exact", "volume": "low"}
            ]
        }
        
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = str(keywords_response)
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        result = await openai_service.generate_keywords(
            business_description="Online running shoe retailer",
            target_audience="Marathon runners and fitness enthusiasts",
            max_keywords=10
        )
        
        assert result is not None
        mock_openai_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_optimize_campaign_success(self, openai_service, mock_openai_client):
        """Test successful campaign optimization."""
        openai_service._client = mock_openai_client
        
        # Mock optimization response
        optimization_response = {
            "optimizations": [
                {
                    "type": "bid_adjustment",
                    "target": "keyword_123",
                    "action": "increase",
                    "amount": 15,
                    "reason": "High conversion rate, low impression share"
                },
                {
                    "type": "budget_reallocation", 
                    "target": "ad_group_456",
                    "action": "increase",
                    "amount": 20,
                    "reason": "Best performing ad group"
                }
            ]
        }
        
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = str(optimization_response)
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        campaign_data = {
            "campaign_id": "12345",
            "performance_metrics": {
                "impressions": 50000,
                "clicks": 2500,
                "conversions": 125,
                "cost": 2500.00
            },
            "keywords": ["running shoes", "athletic footwear"],
            "budget": 100.00
        }
        
        result = await openai_service.optimize_campaign(campaign_data)
        
        assert result is not None
        mock_openai_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_analyze_audience_success(self, openai_service, mock_openai_client):
        """Test successful audience analysis."""
        openai_service._client = mock_openai_client
        
        # Mock audience analysis response
        audience_response = {
            "demographics": {
                "age_ranges": ["25-34", "35-44"],
                "genders": ["male", "female"], 
                "income_levels": ["middle", "high"]
            },
            "interests": ["fitness", "health", "sports"],
            "behaviors": ["online_shopping", "brand_conscious"],
            "targeting_suggestions": [
                "Target fitness enthusiasts aged 25-44",
                "Focus on weekend and evening ad scheduling"
            ]
        }
        
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = str(audience_response)
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        result = await openai_service.analyze_audience(
            business_type="Athletic footwear retailer",
            product_categories=["running shoes", "cross-training"],
            current_performance={
                "top_converting_keywords": ["running shoes", "marathon training"],
                "peak_hours": ["18:00-21:00", "06:00-09:00"]
            }
        )
        
        assert result is not None
        mock_openai_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_landing_page_copy_success(self, openai_service, mock_openai_client):
        """Test successful landing page copy generation."""
        openai_service._client = mock_openai_client
        
        result = await openai_service.generate_landing_page_copy(
            product="Premium Running Shoes",
            value_proposition="Lightweight, durable, and comfortable",
            target_keywords=["running shoes", "athletic footwear", "marathon gear"]
        )
        
        assert result is not None
        mock_openai_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_chat_with_history_success(self, openai_service, mock_openai_client):
        """Test chat with conversation history."""
        openai_service._client = mock_openai_client
        
        conversation_history = [
            {"role": "user", "content": "What are good keywords for running shoes?"},
            {"role": "assistant", "content": "Great keywords include: running shoes, athletic footwear, marathon gear"},
            {"role": "user", "content": "What about for trail running?"}
        ]
        
        result = await openai_service.chat_with_history(
            message="What about for trail running?",
            history=conversation_history[:-1],  # Exclude the last message
            context={"campaign_type": "search", "budget": 1000}
        )
        
        assert result is not None
        mock_openai_client.chat.completions.create.assert_called_once()
        
        # Verify that history was included in the call
        call_args = mock_openai_client.chat.completions.create.call_args
        messages = call_args.kwargs["messages"]
        assert len(messages) >= 2  # Should include history + new message
    
    @pytest.mark.asyncio
    async def test_function_calling_success(self, openai_service, mock_openai_client):
        """Test function calling capability."""
        openai_service._client = mock_openai_client
        
        # Mock function call response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.function_call = MagicMock()
        mock_response.choices[0].message.function_call.name = "get_campaign_performance"
        mock_response.choices[0].message.function_call.arguments = '{"campaign_id": "12345"}'
        
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        functions = [
            {
                "name": "get_campaign_performance",
                "description": "Get performance metrics for a campaign",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "campaign_id": {"type": "string"}
                    },
                    "required": ["campaign_id"]
                }
            }
        ]
        
        result = await openai_service.generate_text(
            prompt="What's the performance of campaign 12345?",
            functions=functions,
            function_call="auto"
        )
        
        assert result is not None
        mock_openai_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_streaming_response_success(self, openai_service, mock_openai_client):
        """Test streaming response handling."""
        openai_service._client = mock_openai_client
        
        # Mock streaming response
        async def mock_stream():
            chunk1 = MagicMock()
            chunk1.choices = [MagicMock()]
            chunk1.choices[0].delta.content = "Hello "
            yield chunk1
            
            chunk2 = MagicMock()
            chunk2.choices = [MagicMock()]
            chunk2.choices[0].delta.content = "world!"
            yield chunk2
        
        mock_openai_client.chat.completions.create.return_value = mock_stream()
        
        result = await openai_service.generate_text_stream(
            prompt="Say hello world",
            max_tokens=50
        )
        
        # Collect streamed content
        content_chunks = []
        async for chunk in result:
            if chunk:
                content_chunks.append(chunk)
        
        # Should have received chunks
        assert len(content_chunks) > 0
    
    @pytest.mark.asyncio
    async def test_embeddings_success(self, openai_service):
        """Test text embeddings generation."""
        # Mock embeddings client
        mock_embeddings_client = AsyncMock()
        mock_response = MagicMock()
        mock_response.data = [MagicMock()]
        mock_response.data[0].embedding = [0.1, 0.2, 0.3, 0.4, 0.5]
        mock_response.usage.total_tokens = 10
        
        with patch('services.openai_service.AsyncOpenAI') as mock_client_class:
            mock_client = AsyncMock()
            mock_client.embeddings.create.return_value = mock_response
            mock_client_class.return_value = mock_client
            openai_service._client = mock_client
            
            result = await openai_service.get_embeddings(
                texts=["running shoes", "athletic footwear"],
                model="text-embedding-ada-002"
            )
            
            assert result is not None
            assert "embeddings" in result
            assert len(result["embeddings"]) == 1  # Mock returns one embedding
            mock_client.embeddings.create.assert_called_once()
    
    def test_get_auth_headers(self, openai_service):
        """Test authentication headers generation."""
        headers = openai_service._get_auth_headers()
        
        assert "Authorization" in headers
        assert headers["Authorization"] == "Bearer test-api-key"
    
    @pytest.mark.asyncio
    async def test_service_with_rate_limiting(self, openai_service, mock_openai_client):
        """Test service respects rate limiting."""
        openai_service._client = mock_openai_client
        openai_service.rate_limit_per_minute = 2  # Set low limit for testing
        
        # First two requests should succeed
        await openai_service.generate_text("Test 1")
        await openai_service.generate_text("Test 2")
        
        # Third request should be rate limited
        from utils.exceptions import RateLimitException
        with pytest.raises(RateLimitException):
            await openai_service.generate_text("Test 3")
    
    @pytest.mark.asyncio
    async def test_caching_functionality(self, openai_service, mock_openai_client):
        """Test caching functionality."""
        openai_service._client = mock_openai_client
        
        # First request should hit the API
        result1 = await openai_service.generate_text("Cached prompt")
        assert mock_openai_client.chat.completions.create.call_count == 1
        
        # Second request with same prompt should use cache
        result2 = await openai_service.generate_text("Cached prompt")
        assert mock_openai_client.chat.completions.create.call_count == 1  # No additional call
        
        assert result1 == result2
    
    def test_prompt_engineering_utilities(self, openai_service):
        """Test prompt engineering utility methods."""
        # Test system message creation
        system_message = openai_service._create_system_message(
            role="Google Ads Campaign Manager",
            context="E-commerce running shoe business",
            constraints=["Budget: $1000/month", "Target audience: Fitness enthusiasts"]
        )
        
        assert "Google Ads Campaign Manager" in system_message
        assert "running shoe business" in system_message
        assert "Budget: $1000/month" in system_message
    
    def test_response_parsing_utilities(self, openai_service):
        """Test response parsing utility methods."""
        # Test JSON extraction from response
        response_text = 'Here is the data: {"keywords": ["running", "shoes"], "volume": "high"} and some other text.'
        
        json_data = openai_service._extract_json_from_response(response_text)
        
        assert json_data is not None
        assert "keywords" in json_data
        assert json_data["keywords"] == ["running", "shoes"]
        assert json_data["volume"] == "high"
    
    def test_token_estimation(self, openai_service):
        """Test token count estimation."""
        text = "This is a sample text for token counting estimation."
        
        estimated_tokens = openai_service._estimate_tokens(text)
        
        assert isinstance(estimated_tokens, int)
        assert estimated_tokens > 0
        assert estimated_tokens < 100  # Should be reasonable for this short text


class TestOpenAIServiceUtilities:
    """Test utility methods and helper functions."""
    
    @pytest.fixture
    def openai_service(self):
        """Create OpenAIService instance for utilities testing."""
        with patch('services.openai_service.settings') as mock_settings:
            mock_settings.OPENAI_API_KEY = "test-api-key"
            mock_settings.OPENAI_MODEL = "gpt-4"
            return OpenAIService()
    
    def test_message_formatting(self, openai_service):
        """Test message formatting for chat completions."""
        messages = openai_service._format_messages(
            system_message="You are a helpful assistant",
            user_message="Generate ad copy for running shoes",
            context={"product": "running shoes", "budget": 1000}
        )
        
        assert len(messages) >= 2
        assert messages[0]["role"] == "system"
        assert messages[-1]["role"] == "user"
        assert "running shoes" in messages[-1]["content"]
    
    def test_parameter_validation(self, openai_service):
        """Test parameter validation for API calls."""
        # Test valid parameters
        params = openai_service._validate_parameters(
            temperature=0.7,
            max_tokens=1000,
            top_p=0.9
        )
        
        assert params["temperature"] == 0.7
        assert params["max_tokens"] == 1000
        assert params["top_p"] == 0.9
        
        # Test parameter clamping
        params = openai_service._validate_parameters(
            temperature=2.5,  # Too high, should be clamped
            max_tokens=-100   # Negative, should use default
        )
        
        assert params["temperature"] <= 2.0
        assert params["max_tokens"] > 0
    
    def test_error_handling_utilities(self, openai_service):
        """Test error handling and retry logic utilities."""
        import openai
        
        # Test retry decision for different errors
        assert openai_service._should_retry_error(openai.RateLimitError("Rate limit")) is True
        assert openai_service._should_retry_error(openai.APIConnectionError("Connection")) is True
        assert openai_service._should_retry_error(openai.AuthenticationError("Auth")) is False
        assert openai_service._should_retry_error(ValueError("Invalid")) is False