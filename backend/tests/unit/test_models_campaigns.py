"""
Unit tests for campaign Pydantic models.
Tests validation, serialization, and business logic for campaign-related models.
"""

import pytest
from datetime import datetime, timedelta
from pydantic import ValidationError

from models.campaigns import (
    Campaign, CampaignCreate, CampaignUpdate, CampaignType, CampaignStatus,
    BiddingStrategy, AdType, KeywordMatchType, AdStatus, CampaignBudget,
    TargetingCriteria, Keyword, AdAsset, Ad, AdGroup, CampaignMetrics,
    CampaignOptimizationSuggestion, KeywordPerformance, AdPerformance
)
from models.common import Language, Currency


class TestCampaignEnums:
    """Test campaign enum classes."""
    
    def test_campaign_type_enum(self):
        """Test CampaignType enum values."""
        assert CampaignType.SEARCH == "search"
        assert CampaignType.DISPLAY == "display"
        assert CampaignType.SHOPPING == "shopping"
        assert CampaignType.VIDEO == "video"
        assert CampaignType.PERFORMANCE_MAX == "performance_max"
    
    def test_campaign_status_enum(self):
        """Test CampaignStatus enum values."""
        assert CampaignStatus.DRAFT == "draft"
        assert CampaignStatus.ACTIVE == "active"
        assert CampaignStatus.PAUSED == "paused"
        assert CampaignStatus.REMOVED == "removed"
        assert CampaignStatus.ENDED == "ended"
    
    def test_bidding_strategy_enum(self):
        """Test BiddingStrategy enum values."""
        assert BiddingStrategy.MANUAL_CPC == "manual_cpc"
        assert BiddingStrategy.TARGET_CPA == "target_cpa"
        assert BiddingStrategy.TARGET_ROAS == "target_roas"
        assert BiddingStrategy.MAXIMIZE_CLICKS == "maximize_clicks"
    
    def test_keyword_match_type_enum(self):
        """Test KeywordMatchType enum values."""
        assert KeywordMatchType.EXACT == "exact"
        assert KeywordMatchType.PHRASE == "phrase"
        assert KeywordMatchType.BROAD == "broad"
        assert KeywordMatchType.BROAD_MODIFIED == "broad_modified"
    
    def test_ad_status_enum(self):
        """Test AdStatus enum values."""
        assert AdStatus.ENABLED == "enabled"
        assert AdStatus.PAUSED == "paused"
        assert AdStatus.REMOVED == "removed"
        assert AdStatus.APPROVED == "approved"
        assert AdStatus.DISAPPROVED == "disapproved"


class TestCampaignBudget:
    """Test CampaignBudget model."""
    
    def test_campaign_budget_valid(self):
        """Test valid CampaignBudget creation."""
        budget = CampaignBudget(
            daily_amount=100.50,
            currency=Currency.USD,
            delivery_method="standard",
            total_amount=3000.00
        )
        
        assert budget.daily_amount == 100.50
        assert budget.currency == Currency.USD
        assert budget.delivery_method == "standard"
        assert budget.total_amount == 3000.00
    
    def test_campaign_budget_validation(self):
        """Test CampaignBudget validation."""
        # Test negative daily_amount
        with pytest.raises(ValidationError) as exc_info:
            CampaignBudget(daily_amount=-10.0)
        assert "greater than 0" in str(exc_info.value)
        
        # Test zero daily_amount
        with pytest.raises(ValidationError) as exc_info:
            CampaignBudget(daily_amount=0.0)
        assert "greater than 0" in str(exc_info.value)
    
    def test_campaign_budget_defaults(self):
        """Test CampaignBudget default values."""
        budget = CampaignBudget(daily_amount=50.0)
        
        assert budget.currency == Currency.USD
        assert budget.delivery_method == "standard"
        assert budget.total_amount is None


class TestTargetingCriteria:
    """Test TargetingCriteria model."""
    
    def test_targeting_criteria_valid(self):
        """Test valid TargetingCriteria creation."""
        targeting = TargetingCriteria(
            locations=["US", "CA", "GB"],
            languages=[Language.ENGLISH, Language.FRENCH],
            age_ranges=["18-24", "25-34"],
            genders=["male", "female"],
            devices=["mobile", "desktop"],
            audiences=["remarketing", "lookalike"]
        )
        
        assert len(targeting.locations) == 3
        assert len(targeting.languages) == 2
        assert len(targeting.age_ranges) == 2
        assert len(targeting.genders) == 2
        assert len(targeting.devices) == 2
        assert len(targeting.audiences) == 2
    
    def test_targeting_criteria_defaults(self):
        """Test TargetingCriteria default values."""
        targeting = TargetingCriteria()
        
        assert targeting.locations == []
        assert targeting.languages == []
        assert targeting.age_ranges is None
        assert targeting.genders is None
        assert targeting.devices is None
        assert targeting.audiences is None


class TestKeyword:
    """Test Keyword model."""
    
    def test_keyword_valid(self):
        """Test valid Keyword creation."""
        keyword = Keyword(
            id="kw_123",
            text="running shoes",
            match_type=KeywordMatchType.PHRASE,
            max_cpc=1.50,
            quality_score=8,
            status=AdStatus.ENABLED,
            negative=False
        )
        
        assert keyword.id == "kw_123"
        assert keyword.text == "running shoes"
        assert keyword.match_type == KeywordMatchType.PHRASE
        assert keyword.max_cpc == 1.50
        assert keyword.quality_score == 8
        assert keyword.status == AdStatus.ENABLED
        assert keyword.negative is False
    
    def test_keyword_validation(self):
        """Test Keyword validation."""
        # Test empty text
        with pytest.raises(ValidationError):
            Keyword(text="", match_type=KeywordMatchType.EXACT)
        
        # Test text too long
        with pytest.raises(ValidationError):
            Keyword(text="a" * 81, match_type=KeywordMatchType.EXACT)
        
        # Test negative max_cpc
        with pytest.raises(ValidationError):
            Keyword(text="test", match_type=KeywordMatchType.EXACT, max_cpc=-1.0)
        
        # Test invalid quality_score
        with pytest.raises(ValidationError):
            Keyword(text="test", match_type=KeywordMatchType.EXACT, quality_score=11)
        
        with pytest.raises(ValidationError):
            Keyword(text="test", match_type=KeywordMatchType.EXACT, quality_score=0)
    
    def test_keyword_defaults(self):
        """Test Keyword default values."""
        keyword = Keyword(text="test keyword", match_type=KeywordMatchType.BROAD)
        
        assert keyword.id is None
        assert keyword.max_cpc is None
        assert keyword.quality_score is None
        assert keyword.status == AdStatus.ENABLED
        assert keyword.negative is False


class TestAdAsset:
    """Test AdAsset model."""
    
    def test_ad_asset_valid(self):
        """Test valid AdAsset creation."""
        asset = AdAsset(
            id="asset_123",
            type="headline",
            content="Great Product Sale!",
            pinned_position=1,
            performance_label="good"
        )
        
        assert asset.id == "asset_123"
        assert asset.type == "headline"
        assert asset.content == "Great Product Sale!"
        assert asset.pinned_position == 1
        assert asset.performance_label == "good"
    
    def test_ad_asset_minimal(self):
        """Test AdAsset with minimal required fields."""
        asset = AdAsset(type="description", content="Buy now!")
        
        assert asset.type == "description"
        assert asset.content == "Buy now!"
        assert asset.id is None
        assert asset.pinned_position is None
        assert asset.performance_label is None


class TestAd:
    """Test Ad model."""
    
    def test_ad_valid(self):
        """Test valid Ad creation."""
        headlines = [
            AdAsset(type="headline", content="Great Product!"),
            AdAsset(type="headline", content="Buy Now!")
        ]
        descriptions = [
            AdAsset(type="description", content="The best product ever."),
            AdAsset(type="description", content="Don't miss out!")
        ]
        
        ad = Ad(
            id="ad_123",
            type=AdType.RESPONSIVE_SEARCH_AD,
            status=AdStatus.ENABLED,
            headlines=headlines,
            descriptions=descriptions,
            display_url="example.com",
            final_urls=["https://example.com/product"]
        )
        
        assert ad.id == "ad_123"
        assert ad.type == AdType.RESPONSIVE_SEARCH_AD
        assert ad.status == AdStatus.ENABLED
        assert len(ad.headlines) == 2
        assert len(ad.descriptions) == 2
        assert ad.display_url == "example.com"
        assert len(ad.final_urls) == 1
    
    def test_ad_defaults(self):
        """Test Ad default values."""
        ad = Ad(type=AdType.TEXT_AD)
        
        assert ad.id is None
        assert ad.status == AdStatus.ENABLED
        assert ad.headlines == []
        assert ad.descriptions == []
        assert ad.display_url is None
        assert ad.final_urls == []
        assert ad.image_assets is None
        assert ad.video_assets is None


class TestAdGroup:
    """Test AdGroup model."""
    
    def test_ad_group_valid(self):
        """Test valid AdGroup creation."""
        keywords = [
            Keyword(text="running shoes", match_type=KeywordMatchType.PHRASE),
            Keyword(text="athletic footwear", match_type=KeywordMatchType.BROAD)
        ]
        ads = [
            Ad(type=AdType.RESPONSIVE_SEARCH_AD),
            Ad(type=AdType.TEXT_AD)
        ]
        targeting = TargetingCriteria(
            locations=["US"],
            languages=[Language.ENGLISH]
        )
        
        ad_group = AdGroup(
            id="ag_123",
            name="Running Shoes Ad Group",
            status=AdStatus.ENABLED,
            max_cpc=2.00,
            keywords=keywords,
            ads=ads,
            targeting=targeting
        )
        
        assert ad_group.id == "ag_123"
        assert ad_group.name == "Running Shoes Ad Group"
        assert ad_group.status == AdStatus.ENABLED
        assert ad_group.max_cpc == 2.00
        assert len(ad_group.keywords) == 2
        assert len(ad_group.ads) == 2
        assert ad_group.targeting is not None
    
    def test_ad_group_validation(self):
        """Test AdGroup validation."""
        # Test empty name
        with pytest.raises(ValidationError):
            AdGroup(name="")
        
        # Test name too long
        with pytest.raises(ValidationError):
            AdGroup(name="a" * 256)
        
        # Test negative max_cpc
        with pytest.raises(ValidationError):
            AdGroup(name="Test", max_cpc=-1.0)


class TestCampaignMetrics:
    """Test CampaignMetrics model."""
    
    def test_campaign_metrics_valid(self):
        """Test valid CampaignMetrics creation."""
        metrics = CampaignMetrics(
            impressions=10000,
            clicks=500,
            conversions=25.5,
            cost=1250.75,
            revenue=5000.00,
            ctr=0.05,
            cpc=2.50,
            cpm=125.08,
            conversion_rate=0.051,
            cost_per_conversion=49.05,
            roas=4.0,
            roi=3.0
        )
        
        assert metrics.impressions == 10000
        assert metrics.clicks == 500
        assert metrics.conversions == 25.5
        assert metrics.cost == 1250.75
        assert metrics.revenue == 5000.00
        assert metrics.ctr == 0.05
        assert metrics.cpc == 2.50
        assert metrics.roas == 4.0
    
    def test_campaign_metrics_validation(self):
        """Test CampaignMetrics validation."""
        # Test negative impressions
        with pytest.raises(ValidationError):
            CampaignMetrics(impressions=-1)
        
        # Test negative clicks
        with pytest.raises(ValidationError):
            CampaignMetrics(clicks=-1)
        
        # Test negative conversions
        with pytest.raises(ValidationError):
            CampaignMetrics(conversions=-1.0)
        
        # Test CTR > 1
        with pytest.raises(ValidationError):
            CampaignMetrics(ctr=1.5)
        
        # Test negative CPC
        with pytest.raises(ValidationError):
            CampaignMetrics(cpc=-1.0)
    
    def test_campaign_metrics_defaults(self):
        """Test CampaignMetrics default values."""
        metrics = CampaignMetrics()
        
        assert metrics.impressions == 0
        assert metrics.clicks == 0
        assert metrics.conversions == 0
        assert metrics.cost == 0
        assert metrics.revenue is None
        assert metrics.ctr is None
        assert metrics.cpc is None


class TestCampaign:
    """Test Campaign model."""
    
    def test_campaign_valid(self, fake, sample_campaign_data):
        """Test valid Campaign creation."""
        campaign = Campaign(
            id=fake.uuid4(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            **sample_campaign_data
        )
        
        assert campaign.name == sample_campaign_data["name"]
        assert campaign.type == sample_campaign_data["type"]
        assert campaign.budget_amount == sample_campaign_data["budget_amount"]
        assert campaign.bidding_strategy == sample_campaign_data["bidding_strategy"]
        assert len(campaign.target_locations) == len(sample_campaign_data["target_locations"])
        assert len(campaign.target_languages) == len(sample_campaign_data["target_languages"])
    
    def test_campaign_validation(self, fake):
        """Test Campaign validation."""
        # Test empty name
        with pytest.raises(ValidationError):
            Campaign(
                id=fake.uuid4(),
                name="",
                type=CampaignType.SEARCH,
                budget_amount=100.0,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
        
        # Test name too long
        with pytest.raises(ValidationError):
            Campaign(
                id=fake.uuid4(),
                name="a" * 256,
                type=CampaignType.SEARCH,
                budget_amount=100.0,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
        
        # Test negative budget
        with pytest.raises(ValidationError):
            Campaign(
                id=fake.uuid4(),
                name="Test Campaign",
                type=CampaignType.SEARCH,
                budget_amount=-10.0,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
        
        # Test negative target_cpa
        with pytest.raises(ValidationError):
            Campaign(
                id=fake.uuid4(),
                name="Test Campaign",
                type=CampaignType.SEARCH,
                budget_amount=100.0,
                target_cpa=-5.0,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
    
    def test_campaign_defaults(self, fake):
        """Test Campaign default values."""
        campaign = Campaign(
            id=fake.uuid4(),
            name="Test Campaign",
            type=CampaignType.SEARCH,
            budget_amount=100.0,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        assert campaign.description is None
        assert campaign.status == CampaignStatus.DRAFT
        assert campaign.bidding_strategy == BiddingStrategy.MANUAL_CPC
        assert campaign.target_locations == []
        assert campaign.target_languages == []
        assert campaign.ad_groups == []
        assert campaign.keywords == []
        assert campaign.negative_keywords == []
        assert campaign.auto_optimization_enabled is True


class TestCampaignCreate:
    """Test CampaignCreate model."""
    
    def test_campaign_create_valid(self, sample_campaign_create):
        """Test valid CampaignCreate."""
        assert sample_campaign_create.name is not None
        assert sample_campaign_create.type in list(CampaignType)
        assert sample_campaign_create.budget_amount > 0
        assert len(sample_campaign_create.target_locations) >= 1
        assert len(sample_campaign_create.target_languages) >= 1
    
    def test_campaign_create_validation(self):
        """Test CampaignCreate validation."""
        # Test empty target_locations
        with pytest.raises(ValidationError):
            CampaignCreate(
                name="Test",
                type=CampaignType.SEARCH,
                budget_amount=100.0,
                target_locations=[],
                target_languages=[Language.ENGLISH]
            )
        
        # Test empty target_languages
        with pytest.raises(ValidationError):
            CampaignCreate(
                name="Test",
                type=CampaignType.SEARCH,
                budget_amount=100.0,
                target_locations=["US"],
                target_languages=[]
            )
    
    def test_campaign_create_defaults(self):
        """Test CampaignCreate default values."""
        campaign_create = CampaignCreate(
            name="Test Campaign",
            type=CampaignType.SEARCH,
            budget_amount=100.0,
            target_locations=["US"],
            target_languages=[Language.ENGLISH]
        )
        
        assert campaign_create.description is None
        assert campaign_create.bidding_strategy == BiddingStrategy.MANUAL_CPC
        assert campaign_create.keywords == []
        assert campaign_create.start_date is None
        assert campaign_create.end_date is None
        assert campaign_create.auto_optimization_enabled is True


class TestCampaignUpdate:
    """Test CampaignUpdate model."""
    
    def test_campaign_update_valid(self):
        """Test valid CampaignUpdate."""
        update = CampaignUpdate(
            name="Updated Campaign Name",
            status=CampaignStatus.ACTIVE,
            budget_amount=200.0,
            bidding_strategy=BiddingStrategy.TARGET_CPA,
            keywords=["new", "keywords"]
        )
        
        assert update.name == "Updated Campaign Name"
        assert update.status == CampaignStatus.ACTIVE
        assert update.budget_amount == 200.0
        assert update.bidding_strategy == BiddingStrategy.TARGET_CPA
        assert len(update.keywords) == 2
    
    def test_campaign_update_all_optional(self):
        """Test CampaignUpdate with all optional fields."""
        update = CampaignUpdate()
        
        assert update.name is None
        assert update.description is None
        assert update.status is None
        assert update.budget_amount is None
        assert update.bidding_strategy is None
        assert update.target_locations is None
        assert update.target_languages is None
    
    def test_campaign_update_validation(self):
        """Test CampaignUpdate validation."""
        # Test negative budget_amount
        with pytest.raises(ValidationError):
            CampaignUpdate(budget_amount=-50.0)


class TestCampaignOptimizationSuggestion:
    """Test CampaignOptimizationSuggestion model."""
    
    def test_optimization_suggestion_valid(self):
        """Test valid CampaignOptimizationSuggestion creation."""
        suggestion = CampaignOptimizationSuggestion(
            id="suggestion_123",
            type="bid_adjustment",
            priority="high",
            title="Increase bids for high-performing keywords",
            description="Your top keywords have low average position. Consider increasing bids by 20%.",
            estimated_impact={"ctr_increase": 0.15, "cost_increase": 50.0},
            action_required="Increase bids for keywords: running shoes, athletic footwear",
            confidence=0.85
        )
        
        assert suggestion.id == "suggestion_123"
        assert suggestion.type == "bid_adjustment"
        assert suggestion.priority == "high"
        assert suggestion.confidence == 0.85
        assert "ctr_increase" in suggestion.estimated_impact
    
    def test_optimization_suggestion_validation(self):
        """Test CampaignOptimizationSuggestion validation."""
        # Test invalid priority
        with pytest.raises(ValidationError):
            CampaignOptimizationSuggestion(
                id="test",
                type="test",
                priority="invalid",
                title="Test",
                description="Test",
                estimated_impact={},
                action_required="Test",
                confidence=0.8
            )
        
        # Test confidence > 1
        with pytest.raises(ValidationError):
            CampaignOptimizationSuggestion(
                id="test",
                type="test",
                priority="high",
                title="Test",
                description="Test",
                estimated_impact={},
                action_required="Test",
                confidence=1.5
            )
        
        # Test confidence < 0
        with pytest.raises(ValidationError):
            CampaignOptimizationSuggestion(
                id="test",
                type="test",
                priority="high",
                title="Test",
                description="Test",
                estimated_impact={},
                action_required="Test",
                confidence=-0.1
            )


class TestKeywordPerformance:
    """Test KeywordPerformance model."""
    
    def test_keyword_performance_valid(self):
        """Test valid KeywordPerformance creation."""
        keyword = Keyword(
            text="running shoes",
            match_type=KeywordMatchType.PHRASE
        )
        metrics = CampaignMetrics(
            impressions=1000,
            clicks=50,
            cost=125.00,
            conversions=5.0
        )
        
        performance = KeywordPerformance(
            keyword=keyword,
            metrics=metrics,
            search_volume=10000,
            competition="high",
            suggested_bid=2.50
        )
        
        assert performance.keyword.text == "running shoes"
        assert performance.metrics.impressions == 1000
        assert performance.search_volume == 10000
        assert performance.competition == "high"
        assert performance.suggested_bid == 2.50
    
    def test_keyword_performance_minimal(self):
        """Test KeywordPerformance with minimal required fields."""
        keyword = Keyword(text="test", match_type=KeywordMatchType.EXACT)
        metrics = CampaignMetrics()
        
        performance = KeywordPerformance(keyword=keyword, metrics=metrics)
        
        assert performance.search_volume is None
        assert performance.competition is None
        assert performance.suggested_bid is None


class TestAdPerformance:
    """Test AdPerformance model."""
    
    def test_ad_performance_valid(self):
        """Test valid AdPerformance creation."""
        ad = Ad(type=AdType.RESPONSIVE_SEARCH_AD)
        metrics = CampaignMetrics(
            impressions=2000,
            clicks=100,
            cost=250.00,
            conversions=10.0
        )
        
        performance = AdPerformance(
            ad=ad,
            metrics=metrics,
            approval_status="approved",
            policy_summary="All policies compliant"
        )
        
        assert performance.ad.type == AdType.RESPONSIVE_SEARCH_AD
        assert performance.metrics.impressions == 2000
        assert performance.approval_status == "approved"
        assert performance.policy_summary == "All policies compliant"
    
    def test_ad_performance_minimal(self):
        """Test AdPerformance with minimal required fields."""
        ad = Ad(type=AdType.TEXT_AD)
        metrics = CampaignMetrics()
        
        performance = AdPerformance(ad=ad, metrics=metrics)
        
        assert performance.approval_status is None
        assert performance.policy_summary is None