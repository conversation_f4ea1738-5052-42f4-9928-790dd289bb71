"""
Unit tests for common Pydantic models.
Tests validation, serialization, and model behavior.
"""

import pytest
from datetime import datetime, timedelta
from pydantic import ValidationError

from models.common import (
    BaseResponse, PaginationInfo, PaginatedResponse, Status, Priority,
    Currency, Language, Country, BaseEntity, MoneyAmount, Location,
    TimeRange, MetricValue, Tag, Address, ContactInfo, Percentage,
    ErrorDetail, ValidationErrorResponse, HealthStatus, RateLimitInfo
)


class TestBaseResponse:
    """Test BaseResponse model."""
    
    def test_base_response_valid(self):
        """Test valid BaseResponse creation."""
        response = BaseResponse[dict](
            success=True,
            message="Operation successful",
            data={"key": "value"}
        )
        
        assert response.success is True
        assert response.message == "Operation successful"
        assert response.data == {"key": "value"}
        assert response.errors is None
        assert isinstance(response.timestamp, datetime)
    
    def test_base_response_with_errors(self):
        """Test BaseResponse with errors."""
        response = BaseResponse[None](
            success=False,
            message="Operation failed",
            errors=["Error 1", "Error 2"]
        )
        
        assert response.success is False
        assert response.errors == ["Error 1", "Error 2"]
        assert response.data is None
    
    def test_base_response_serialization(self):
        """Test BaseResponse JSON serialization."""
        response = BaseResponse[str](
            success=True,
            message="Test",
            data="test_data"
        )
        
        json_data = response.model_dump()
        assert "success" in json_data
        assert "message" in json_data
        assert "data" in json_data
        assert "timestamp" in json_data


class TestPaginationInfo:
    """Test PaginationInfo model."""
    
    def test_pagination_info_valid(self):
        """Test valid PaginationInfo creation."""
        pagination = PaginationInfo(
            skip=0,
            limit=20,
            total=100,
            has_more=True
        )
        
        assert pagination.skip == 0
        assert pagination.limit == 20
        assert pagination.total == 100
        assert pagination.has_more is True
    
    def test_pagination_info_validation(self):
        """Test PaginationInfo validation."""
        # Test negative skip
        with pytest.raises(ValidationError) as exc_info:
            PaginationInfo(skip=-1, limit=20, total=100, has_more=True)
        assert "greater than or equal to 0" in str(exc_info.value)
        
        # Test zero limit
        with pytest.raises(ValidationError) as exc_info:
            PaginationInfo(skip=0, limit=0, total=100, has_more=True)
        assert "greater than or equal to 1" in str(exc_info.value)
        
        # Test negative total
        with pytest.raises(ValidationError) as exc_info:
            PaginationInfo(skip=0, limit=20, total=-1, has_more=True)
        assert "greater than or equal to 0" in str(exc_info.value)


class TestPaginatedResponse:
    """Test PaginatedResponse model."""
    
    def test_paginated_response_valid(self):
        """Test valid PaginatedResponse creation."""
        pagination = PaginationInfo(
            skip=0, limit=10, total=50, has_more=True
        )
        response = PaginatedResponse[str](
            success=True,
            message="Success",
            data=["item1", "item2", "item3"],
            pagination=pagination
        )
        
        assert response.success is True
        assert len(response.data) == 3
        assert response.pagination.total == 50


class TestEnums:
    """Test enum classes."""
    
    def test_status_enum(self):
        """Test Status enum values."""
        assert Status.ACTIVE == "active"
        assert Status.INACTIVE == "inactive"
        assert Status.PAUSED == "paused"
        assert Status.DRAFT == "draft"
        assert Status.ARCHIVED == "archived"
        assert Status.DELETED == "deleted"
    
    def test_priority_enum(self):
        """Test Priority enum values."""
        assert Priority.LOW == "low"
        assert Priority.MEDIUM == "medium"
        assert Priority.HIGH == "high"
        assert Priority.CRITICAL == "critical"
    
    def test_currency_enum(self):
        """Test Currency enum values."""
        assert Currency.USD == "USD"
        assert Currency.EUR == "EUR"
        assert Currency.GBP == "GBP"
        assert Currency.CAD == "CAD"
    
    def test_language_enum(self):
        """Test Language enum values."""
        assert Language.ENGLISH == "en"
        assert Language.DUTCH == "nl"
        assert Language.FRENCH == "fr"
        assert Language.GERMAN == "de"
        assert Language.SPANISH == "es"
        assert Language.ITALIAN == "it"
    
    def test_country_enum(self):
        """Test Country enum values."""
        assert Country.UNITED_STATES == "US"
        assert Country.BELGIUM == "BE"
        assert Country.NETHERLANDS == "NL"
        assert Country.FRANCE == "FR"
        assert Country.GERMANY == "DE"
        assert Country.UNITED_KINGDOM == "GB"
        assert Country.CANADA == "CA"


class TestBaseEntity:
    """Test BaseEntity model."""
    
    def test_base_entity_valid(self, fake):
        """Test valid BaseEntity creation."""
        entity = BaseEntity(
            id=fake.uuid4(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            created_by=fake.uuid4(),
            updated_by=fake.uuid4()
        )
        
        assert entity.id is not None
        assert isinstance(entity.created_at, datetime)
        assert isinstance(entity.updated_at, datetime)
    
    def test_base_entity_minimal(self, fake):
        """Test BaseEntity with minimal required fields."""
        entity = BaseEntity(id=fake.uuid4())
        
        assert entity.id is not None
        assert entity.created_at is None
        assert entity.updated_at is None
        assert entity.created_by is None
        assert entity.updated_by is None


class TestMoneyAmount:
    """Test MoneyAmount model."""
    
    def test_money_amount_valid(self):
        """Test valid MoneyAmount creation."""
        amount = MoneyAmount(amount=100.50, currency=Currency.USD)
        
        assert amount.amount == 100.50
        assert amount.currency == Currency.USD
    
    def test_money_amount_validation(self):
        """Test MoneyAmount validation."""
        # Test negative amount
        with pytest.raises(ValidationError) as exc_info:
            MoneyAmount(amount=-10.0, currency=Currency.USD)
        assert "greater than or equal to 0" in str(exc_info.value)
    
    def test_money_amount_string_representation(self):
        """Test MoneyAmount string representation."""
        usd_amount = MoneyAmount(amount=100.50, currency=Currency.USD)
        assert str(usd_amount) == "$100.50"
        
        eur_amount = MoneyAmount(amount=75.25, currency=Currency.EUR)
        assert str(eur_amount) == "€75.25"
        
        gbp_amount = MoneyAmount(amount=85.75, currency=Currency.GBP)
        assert str(gbp_amount) == "£85.75"
        
        cad_amount = MoneyAmount(amount=120.00, currency=Currency.CAD)
        assert str(cad_amount) == "120.00 CAD"


class TestLocation:
    """Test Location model."""
    
    def test_location_valid(self):
        """Test valid Location creation."""
        location = Location(
            country=Country.UNITED_STATES,
            region="California",
            city="San Francisco",
            postal_code="94102"
        )
        
        assert location.country == Country.UNITED_STATES
        assert location.region == "California"
        assert location.city == "San Francisco"
        assert location.postal_code == "94102"
    
    def test_location_minimal(self):
        """Test Location with minimal required fields."""
        location = Location(country=Country.BELGIUM)
        
        assert location.country == Country.BELGIUM
        assert location.region is None
        assert location.city is None
        assert location.postal_code is None
    
    def test_location_string_representation(self):
        """Test Location string representation."""
        location = Location(
            country=Country.UNITED_STATES,
            region="California",
            city="San Francisco"
        )
        assert "San Francisco, California, US" in str(location)
        
        minimal_location = Location(country=Country.BELGIUM)
        assert str(minimal_location) == "BE"


class TestTimeRange:
    """Test TimeRange model."""
    
    def test_time_range_valid(self):
        """Test valid TimeRange creation."""
        start = datetime(2024, 1, 1, 0, 0, 0)
        end = datetime(2024, 1, 31, 23, 59, 59)
        
        time_range = TimeRange(
            start_date=start,
            end_date=end,
            timezone="UTC"
        )
        
        assert time_range.start_date == start
        assert time_range.end_date == end
        assert time_range.timezone == "UTC"
    
    def test_time_range_default_timezone(self):
        """Test TimeRange with default timezone."""
        start = datetime.utcnow()
        end = start + timedelta(days=1)
        
        time_range = TimeRange(start_date=start, end_date=end)
        assert time_range.timezone == "UTC"


class TestMetricValue:
    """Test MetricValue model."""
    
    def test_metric_value_valid(self):
        """Test valid MetricValue creation."""
        metric = MetricValue(
            value=100.0,
            previous_value=90.0,
            change_percentage=11.11,
            trend="up",
            confidence=0.95
        )
        
        assert metric.value == 100.0
        assert metric.previous_value == 90.0
        assert metric.change_percentage == 11.11
        assert metric.trend == "up"
        assert metric.confidence == 0.95
    
    def test_metric_value_minimal(self):
        """Test MetricValue with minimal required fields."""
        metric = MetricValue(value=50.0)
        
        assert metric.value == 50.0
        assert metric.previous_value is None
        assert metric.change_percentage is None
        assert metric.trend is None
        assert metric.confidence is None
    
    def test_metric_value_confidence_validation(self):
        """Test MetricValue confidence validation."""
        # Test confidence > 1
        with pytest.raises(ValidationError):
            MetricValue(value=100.0, confidence=1.5)
        
        # Test confidence < 0
        with pytest.raises(ValidationError):
            MetricValue(value=100.0, confidence=-0.1)


class TestTag:
    """Test Tag model."""
    
    def test_tag_valid(self):
        """Test valid Tag creation."""
        tag = Tag(key="environment", value="production")
        
        assert tag.key == "environment"
        assert tag.value == "production"
    
    def test_tag_validation(self):
        """Test Tag validation."""
        # Test empty key
        with pytest.raises(ValidationError):
            Tag(key="", value="production")
        
        # Test empty value
        with pytest.raises(ValidationError):
            Tag(key="environment", value="")
        
        # Test key too long
        with pytest.raises(ValidationError):
            Tag(key="a" * 51, value="production")
        
        # Test value too long
        with pytest.raises(ValidationError):
            Tag(key="environment", value="a" * 101)
    
    def test_tag_string_representation(self):
        """Test Tag string representation."""
        tag = Tag(key="environment", value="production")
        assert str(tag) == "environment:production"


class TestAddress:
    """Test Address model."""
    
    def test_address_valid(self):
        """Test valid Address creation."""
        address = Address(
            street_address="123 Main St",
            city="San Francisco",
            state_province="California",
            postal_code="94102",
            country=Country.UNITED_STATES
        )
        
        assert address.street_address == "123 Main St"
        assert address.city == "San Francisco"
        assert address.state_province == "California"
        assert address.postal_code == "94102"
        assert address.country == Country.UNITED_STATES
    
    def test_address_string_representation(self):
        """Test Address string representation."""
        address = Address(
            street_address="123 Main St",
            city="San Francisco",
            state_province="California",
            postal_code="94102",
            country=Country.UNITED_STATES
        )
        
        address_str = str(address)
        assert "123 Main St" in address_str
        assert "San Francisco" in address_str
        assert "California" in address_str
        assert "94102" in address_str
        assert "US" in address_str


class TestPercentage:
    """Test Percentage model."""
    
    def test_percentage_valid(self):
        """Test valid Percentage creation."""
        percentage = Percentage(value=85.5)
        
        assert percentage.value == 85.5
    
    def test_percentage_validation(self):
        """Test Percentage validation."""
        # Test negative value
        with pytest.raises(ValidationError):
            Percentage(value=-1.0)
        
        # Test value > 100
        with pytest.raises(ValidationError):
            Percentage(value=101.0)
    
    def test_percentage_string_representation(self):
        """Test Percentage string representation."""
        percentage = Percentage(value=85.5)
        assert str(percentage) == "85.50%"
    
    def test_percentage_decimal_property(self):
        """Test Percentage decimal property."""
        percentage = Percentage(value=85.0)
        assert percentage.decimal == 0.85


class TestErrorDetail:
    """Test ErrorDetail model."""
    
    def test_error_detail_valid(self):
        """Test valid ErrorDetail creation."""
        error = ErrorDetail(
            code="INVALID_INPUT",
            message="The provided input is invalid",
            field="email",
            context={"pattern": "email_format"}
        )
        
        assert error.code == "INVALID_INPUT"
        assert error.message == "The provided input is invalid"
        assert error.field == "email"
        assert error.context == {"pattern": "email_format"}
    
    def test_error_detail_minimal(self):
        """Test ErrorDetail with minimal required fields."""
        error = ErrorDetail(
            code="GENERIC_ERROR",
            message="Something went wrong"
        )
        
        assert error.code == "GENERIC_ERROR"
        assert error.message == "Something went wrong"
        assert error.field is None
        assert error.context is None


class TestValidationErrorResponse:
    """Test ValidationErrorResponse model."""
    
    def test_validation_error_response_valid(self):
        """Test valid ValidationErrorResponse creation."""
        errors = [
            ErrorDetail(code="REQUIRED", message="Field is required", field="name"),
            ErrorDetail(code="INVALID", message="Invalid format", field="email")
        ]
        
        response = ValidationErrorResponse(
            message="Validation failed",
            errors=errors
        )
        
        assert response.success is False  # Should always be False
        assert response.message == "Validation failed"
        assert len(response.errors) == 2
        assert isinstance(response.timestamp, datetime)


class TestHealthStatus:
    """Test HealthStatus model."""
    
    def test_health_status_valid(self):
        """Test valid HealthStatus creation."""
        checks = {
            "database": {"status": "healthy", "response_time": 0.05},
            "redis": {"status": "healthy", "response_time": 0.01},
            "external_api": {"status": "unhealthy", "error": "Connection timeout"}
        }
        
        health = HealthStatus(
            status="degraded",
            checks=checks,
            timestamp=datetime.utcnow(),
            uptime_seconds=3600.5
        )
        
        assert health.status == "degraded"
        assert len(health.checks) == 3
        assert health.uptime_seconds == 3600.5
        assert isinstance(health.timestamp, datetime)


class TestRateLimitInfo:
    """Test RateLimitInfo model."""
    
    def test_rate_limit_info_valid(self):
        """Test valid RateLimitInfo creation."""
        reset_time = datetime.utcnow() + timedelta(minutes=1)
        
        rate_limit = RateLimitInfo(
            limit=100,
            remaining=75,
            reset_time=reset_time,
            window_seconds=60
        )
        
        assert rate_limit.limit == 100
        assert rate_limit.remaining == 75
        assert rate_limit.reset_time == reset_time
        assert rate_limit.window_seconds == 60
    
    def test_rate_limit_info_serialization(self):
        """Test RateLimitInfo serialization."""
        reset_time = datetime.utcnow() + timedelta(minutes=1)
        
        rate_limit = RateLimitInfo(
            limit=100,
            remaining=75,
            reset_time=reset_time,
            window_seconds=60
        )
        
        data = rate_limit.model_dump()
        assert "limit" in data
        assert "remaining" in data
        assert "reset_time" in data
        assert "window_seconds" in data