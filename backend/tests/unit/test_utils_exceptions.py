"""
Unit tests for custom exceptions and error handling utilities.
Tests exception classes, error handlers, and utility functions.
"""

import pytest
from unittest.mock import MagicM<PERSON>, patch
from fastapi import Request, status
from fastapi.exceptions import RequestValidationError

from utils.exceptions import (
    CustomException, ValidationException, AuthenticationException,
    AuthorizationException, NotFoundException, ConflictException,
    RateLimitException, ExternalServiceException, GoogleAdsException,
    OpenAIException, DatabaseException, CampaignException, AgentException,
    custom_exception_handler, validation_exception_handler,
    handle_external_api_error, safe_execute
)


class TestCustomException:
    """Test CustomException base class."""
    
    def test_custom_exception_creation(self):
        """Test CustomException creation with all parameters."""
        exc = CustomException(
            message="Test error",
            error_code="TEST_ERROR",
            status_code=status.HTTP_400_BAD_REQUEST,
            details={"context": "test"}
        )
        
        assert exc.message == "Test error"
        assert exc.error_code == "TEST_ERROR"
        assert exc.status_code == status.HTTP_400_BAD_REQUEST
        assert exc.details == {"context": "test"}
        assert str(exc) == "Test error"
    
    def test_custom_exception_defaults(self):
        """Test CustomException with default values."""
        exc = CustomException("Basic error")
        
        assert exc.message == "Basic error"
        assert exc.error_code == "GENERIC_ERROR"
        assert exc.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert exc.details == {}


class TestSpecificExceptions:
    """Test specific exception classes."""
    
    def test_validation_exception(self):
        """Test ValidationException."""
        exc = ValidationException("Invalid input", {"field": "email"})
        
        assert exc.error_code == "VALIDATION_ERROR"
        assert exc.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        assert exc.message == "Invalid input"
        assert exc.details == {"field": "email"}
    
    def test_authentication_exception(self):
        """Test AuthenticationException."""
        exc = AuthenticationException()
        assert exc.error_code == "AUTHENTICATION_ERROR"
        assert exc.status_code == status.HTTP_401_UNAUTHORIZED
        assert exc.message == "Authentication failed"
        
        # Test with custom message
        exc_custom = AuthenticationException("Token expired")
        assert exc_custom.message == "Token expired"
    
    def test_authorization_exception(self):
        """Test AuthorizationException."""
        exc = AuthorizationException()
        assert exc.error_code == "AUTHORIZATION_ERROR"
        assert exc.status_code == status.HTTP_403_FORBIDDEN
        assert exc.message == "Access denied"
        
        # Test with custom message
        exc_custom = AuthorizationException("Insufficient privileges")
        assert exc_custom.message == "Insufficient privileges"
    
    def test_not_found_exception(self):
        """Test NotFoundException."""
        exc = NotFoundException("Campaign", "12345")
        
        assert exc.error_code == "NOT_FOUND"
        assert exc.status_code == status.HTTP_404_NOT_FOUND
        assert "Campaign with identifier '12345' not found" in exc.message
    
    def test_conflict_exception(self):
        """Test ConflictException."""
        exc = ConflictException("Resource already exists")
        
        assert exc.error_code == "CONFLICT"
        assert exc.status_code == status.HTTP_409_CONFLICT
        assert exc.message == "Resource already exists"
    
    def test_rate_limit_exception(self):
        """Test RateLimitException."""
        exc = RateLimitException()
        assert exc.error_code == "RATE_LIMIT_EXCEEDED"
        assert exc.status_code == status.HTTP_429_TOO_MANY_REQUESTS
        assert exc.message == "Rate limit exceeded"
        
        # Test with details
        details = {"limit": 100, "window": "1 minute"}
        exc_details = RateLimitException("API limit exceeded", details)
        assert exc_details.details == details
    
    def test_external_service_exception(self):
        """Test ExternalServiceException."""
        exc = ExternalServiceException("Google Ads API", "Connection timeout")
        
        assert exc.error_code == "EXTERNAL_SERVICE_ERROR"
        assert exc.status_code == status.HTTP_502_BAD_GATEWAY
        assert "Google Ads API: Connection timeout" in exc.message
        assert exc.details["service"] == "Google Ads API"
    
    def test_google_ads_exception(self):
        """Test GoogleAdsException."""
        exc = GoogleAdsException("Invalid campaign ID")
        
        assert exc.error_code == "EXTERNAL_SERVICE_ERROR"
        assert "Google Ads API: Invalid campaign ID" in exc.message
        assert exc.details["service"] == "Google Ads API"
    
    def test_openai_exception(self):
        """Test OpenAIException."""
        exc = OpenAIException("Token limit exceeded")
        
        assert exc.error_code == "EXTERNAL_SERVICE_ERROR"
        assert "OpenAI API: Token limit exceeded" in exc.message
        assert exc.details["service"] == "OpenAI API"
    
    def test_database_exception(self):
        """Test DatabaseException."""
        exc = DatabaseException("Connection failed")
        
        assert exc.error_code == "DATABASE_ERROR"
        assert exc.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert exc.message == "Connection failed"
    
    def test_campaign_exception(self):
        """Test CampaignException."""
        exc = CampaignException(
            "Invalid budget amount",
            campaign_id="12345",
            error_code="INVALID_BUDGET"
        )
        
        assert exc.error_code == "INVALID_BUDGET"
        assert exc.status_code == status.HTTP_400_BAD_REQUEST
        assert exc.message == "Invalid budget amount"
        assert exc.details["campaign_id"] == "12345"
        
        # Test without campaign_id
        exc_no_id = CampaignException("General campaign error")
        assert exc_no_id.details.get("campaign_id") is None
    
    def test_agent_exception(self):
        """Test AgentException."""
        exc = AgentException(
            "Agent processing failed",
            agent_name="keyword_research_agent"
        )
        
        assert exc.error_code == "AGENT_ERROR"
        assert exc.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert exc.message == "Agent processing failed"
        assert exc.details["agent_name"] == "keyword_research_agent"
        
        # Test without agent_name
        exc_no_name = AgentException("General agent error")
        assert exc_no_name.details.get("agent_name") is None


class TestExceptionHandlers:
    """Test exception handler functions."""
    
    @pytest.fixture
    def mock_request(self):
        """Create mock FastAPI request."""
        request = MagicMock(spec=Request)
        request.url.path = "/test/endpoint"
        request.method = "POST"
        request.headers.get.return_value = "test-request-id"
        return request
    
    @pytest.mark.asyncio
    async def test_custom_exception_handler(self, mock_request):
        """Test custom exception handler."""
        exc = CustomException(
            "Test error",
            error_code="TEST_ERROR",
            status_code=status.HTTP_400_BAD_REQUEST,
            details={"field": "value"}
        )
        
        with patch('utils.exceptions.structlog') as mock_structlog:
            mock_logger = MagicMock()
            mock_structlog.get_logger.return_value = mock_logger
            
            response = await custom_exception_handler(mock_request, exc)
            
            # Check logging was called
            mock_logger.error.assert_called_once()
            log_call = mock_logger.error.call_args
            assert log_call.kwargs["error_code"] == "TEST_ERROR"
            assert log_call.kwargs["message"] == "Test error"
            assert log_call.kwargs["status_code"] == status.HTTP_400_BAD_REQUEST
            
            # Check response
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert response.media_type == "application/json"
            
            # Check response content structure
            content = response.body.decode()
            assert "TEST_ERROR" in content
            assert "Test error" in content
            assert "test-request-id" in content
    
    @pytest.mark.asyncio
    async def test_validation_exception_handler(self, mock_request):
        """Test validation exception handler."""
        # Mock validation error
        validation_error = RequestValidationError([
            {
                "loc": ("body", "email"),
                "msg": "field required",
                "type": "value_error.missing"
            },
            {
                "loc": ("body", "age"),
                "msg": "ensure this value is greater than 0",
                "type": "value_error.number.not_gt"
            }
        ])
        
        with patch('utils.exceptions.structlog') as mock_structlog:
            mock_logger = MagicMock()
            mock_structlog.get_logger.return_value = mock_logger
            
            response = await validation_exception_handler(mock_request, validation_error)
            
            # Check logging was called
            mock_logger.warning.assert_called_once()
            
            # Check response
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
            
            # Check response content structure
            content = response.body.decode()
            assert "VALIDATION_ERROR" in content
            assert "Request validation failed" in content
            assert "body -> email" in content
            assert "body -> age" in content


class TestUtilityFunctions:
    """Test utility functions for error handling."""
    
    def test_handle_external_api_error_rate_limit(self):
        """Test handling rate limit errors."""
        original_error = Exception("Rate limit exceeded: too many requests")
        
        with patch('utils.exceptions.structlog') as mock_structlog:
            mock_logger = MagicMock()
            mock_structlog.get_logger.return_value = mock_logger
            
            exc = handle_external_api_error("Test API", original_error)
            
            assert isinstance(exc, RateLimitException)
            assert "Rate limit exceeded for Test API" in exc.message
            assert exc.details["original_error"] == str(original_error)
            mock_logger.error.assert_called_once()
    
    def test_handle_external_api_error_authentication(self):
        """Test handling authentication errors."""
        original_error = Exception("Authentication failed: invalid credentials")
        
        with patch('utils.exceptions.structlog') as mock_structlog:
            mock_logger = MagicMock()
            mock_structlog.get_logger.return_value = mock_logger
            
            exc = handle_external_api_error("Test API", original_error)
            
            assert isinstance(exc, AuthenticationException)
            assert "Authentication failed for Test API" in exc.message
            assert exc.details["original_error"] == str(original_error)
    
    def test_handle_external_api_error_generic(self):
        """Test handling generic external service errors."""
        original_error = Exception("Connection timeout")
        
        with patch('utils.exceptions.structlog') as mock_structlog:
            mock_logger = MagicMock()
            mock_structlog.get_logger.return_value = mock_logger
            
            exc = handle_external_api_error("Test API", original_error, {"endpoint": "/test"})
            
            assert isinstance(exc, ExternalServiceException)
            assert "Test API: Connection timeout" in exc.message
            assert exc.details["original_error"] == str(original_error)
            assert exc.details["endpoint"] == "/test"
    
    def test_safe_execute_success(self):
        """Test successful execution with safe_execute."""
        def test_function(x, y):
            return x + y
        
        result = safe_execute(test_function, 5, 3)
        assert result == 8
    
    def test_safe_execute_custom_exception(self):
        """Test safe_execute with custom exception."""
        def failing_function():
            raise ValidationException("Test validation error")
        
        # Custom exceptions should be re-raised
        with pytest.raises(ValidationException):
            safe_execute(failing_function)
    
    def test_safe_execute_generic_exception(self):
        """Test safe_execute with generic exception."""
        def failing_function():
            raise ValueError("Generic error")
        
        with patch('utils.exceptions.structlog') as mock_structlog:
            mock_logger = MagicMock()
            mock_structlog.get_logger.return_value = mock_logger
            
            with pytest.raises(CustomException) as exc_info:
                safe_execute(failing_function)
            
            assert exc_info.value.error_code == "UNEXPECTED_ERROR"
            assert "Unexpected error in failing_function" in exc_info.value.message
            assert exc_info.value.details["function"] == "failing_function"
            assert exc_info.value.details["original_error"] == "Generic error"
            
            mock_logger.error.assert_called_once()


class TestExceptionChaining:
    """Test exception chaining and context preservation."""
    
    def test_exception_chaining_with_context(self):
        """Test that exception context is preserved."""
        def level_3():
            raise ValueError("Original error")
        
        def level_2():
            try:
                level_3()
            except ValueError as e:
                raise DatabaseException("Database operation failed") from e
        
        def level_1():
            try:
                level_2()
            except DatabaseException as e:
                raise CampaignException("Campaign creation failed", campaign_id="12345") from e
        
        with pytest.raises(CampaignException) as exc_info:
            level_1()
        
        # Check exception chaining
        assert exc_info.value.__cause__.__class__ == DatabaseException
        assert exc_info.value.__cause__.__cause__.__class__ == ValueError
    
    def test_exception_details_merging(self):
        """Test that exception details are properly merged."""
        base_details = {"service": "google_ads", "endpoint": "/campaigns"}
        additional_details = {"campaign_id": "12345", "user_id": "user_789"}
        
        exc = ExternalServiceException(
            "Google Ads API",
            "Operation failed",
            details={**base_details, **additional_details}
        )
        
        assert exc.details["service"] == "google_ads"
        assert exc.details["endpoint"] == "/campaigns"
        assert exc.details["campaign_id"] == "12345"
        assert exc.details["user_id"] == "user_789"


class TestExceptionSerialization:
    """Test exception serialization for logging and responses."""
    
    def test_custom_exception_dict_representation(self):
        """Test custom exception dictionary representation."""
        exc = CampaignException(
            "Invalid budget",
            campaign_id="12345",
            details={"budget": -100, "currency": "USD"}
        )
        
        # Test that exception attributes are accessible
        error_dict = {
            "code": exc.error_code,
            "message": exc.message,
            "status_code": exc.status_code,
            "details": exc.details
        }
        
        assert error_dict["code"] == "CAMPAIGN_ERROR"
        assert error_dict["message"] == "Invalid budget"
        assert error_dict["status_code"] == status.HTTP_400_BAD_REQUEST
        assert error_dict["details"]["campaign_id"] == "12345"
        assert error_dict["details"]["budget"] == -100
    
    def test_exception_json_serialization(self):
        """Test exception JSON serialization compatibility."""
        import json
        
        exc = ValidationException(
            "Field validation failed",
            details={
                "field": "email",
                "value": "invalid-email",
                "expected": "valid email format"
            }
        )
        
        # Should be able to serialize exception data to JSON
        error_data = {
            "error_code": exc.error_code,
            "message": exc.message,
            "details": exc.details
        }
        
        json_str = json.dumps(error_data)
        assert "VALIDATION_ERROR" in json_str
        assert "Field validation failed" in json_str
        assert "invalid-email" in json_str


class TestErrorRecovery:
    """Test error recovery and fallback mechanisms."""
    
    def test_graceful_degradation_on_error(self):
        """Test graceful degradation when errors occur."""
        def unreliable_function(should_fail: bool = False):
            if should_fail:
                raise ExternalServiceException("Test Service", "Service unavailable")
            return "success"
        
        # Test success case
        result = safe_execute(unreliable_function, False)
        assert result == "success"
        
        # Test failure case with graceful handling
        with patch('utils.exceptions.structlog') as mock_structlog:
            mock_logger = MagicMock()
            mock_structlog.get_logger.return_value = mock_logger
            
            with pytest.raises(CustomException):
                safe_execute(unreliable_function, True)
    
    def test_error_context_preservation(self):
        """Test that error context is preserved through layers."""
        def service_layer():
            raise GoogleAdsException("Campaign not found", details={"campaign_id": "12345"})
        
        def business_layer():
            try:
                service_layer()
            except GoogleAdsException as e:
                # Wrap in business logic exception while preserving context
                raise CampaignException(
                    "Failed to retrieve campaign",
                    campaign_id="12345",
                    details={"original_error": e.message, **e.details}
                ) from e
        
        with pytest.raises(CampaignException) as exc_info:
            business_layer()
        
        # Check that original context is preserved
        assert exc_info.value.details["campaign_id"] == "12345"
        assert exc_info.value.details["original_error"] == "Campaign not found"
        assert exc_info.value.__cause__.__class__ == GoogleAdsException