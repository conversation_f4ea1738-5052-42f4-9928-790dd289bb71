"""
Unit tests for AI agent Pydantic models.
Tests validation, serialization, and business logic for agent-related models.
"""

import pytest
from datetime import datetime, timedelta
from pydantic import ValidationError

from models.agents import (
    Agent, AgentCreate, AgentUpdate, AgentTask, TaskCreate, TaskUpdate,
    AgentType, AgentStatus, TaskStatus, TaskPriority, ModelProvider,
    AgentCapability, AgentModel, AgentMemory, AgentTool, AgentConfig,
    AgentMetrics, AgentCommand, AgentCollaboration, AgentPerformanceReport
)


class TestAgentEnums:
    """Test agent enum classes."""
    
    def test_agent_type_enum(self):
        """Test AgentType enum values."""
        assert AgentType.CAMPAIGN_PLANNING == "campaign_planning"
        assert AgentType.AD_ASSET_GENERATION == "ad_asset_generation"
        assert AgentType.KEYWORD_RESEARCH == "keyword_research"
        assert AgentType.BID_OPTIMIZATION == "bid_optimization"
        assert AgentType.BUDGET_MANAGEMENT == "budget_management"
        assert AgentType.PERFORMANCE_ANALYSIS == "performance_analysis"
    
    def test_agent_status_enum(self):
        """Test AgentStatus enum values."""
        assert AgentStatus.CREATED == "created"
        assert AgentStatus.INITIALIZING == "initializing"
        assert AgentStatus.ACTIVE == "active"
        assert AgentStatus.BUSY == "busy"
        assert AgentStatus.IDLE == "idle"
        assert AgentStatus.PAUSED == "paused"
        assert AgentStatus.STOPPED == "stopped"
        assert AgentStatus.ERROR == "error"
    
    def test_task_status_enum(self):
        """Test TaskStatus enum values."""
        assert TaskStatus.PENDING == "pending"
        assert TaskStatus.QUEUED == "queued"
        assert TaskStatus.RUNNING == "running"
        assert TaskStatus.COMPLETED == "completed"
        assert TaskStatus.FAILED == "failed"
        assert TaskStatus.CANCELLED == "cancelled"
        assert TaskStatus.TIMEOUT == "timeout"
    
    def test_task_priority_enum(self):
        """Test TaskPriority enum values."""
        assert TaskPriority.LOW == "low"
        assert TaskPriority.NORMAL == "normal"
        assert TaskPriority.HIGH == "high"
        assert TaskPriority.URGENT == "urgent"
    
    def test_model_provider_enum(self):
        """Test ModelProvider enum values."""
        assert ModelProvider.OPENAI == "openai"
        assert ModelProvider.GOOGLE == "google"
        assert ModelProvider.ANTHROPIC == "anthropic"
        assert ModelProvider.AZURE_OPENAI == "azure_openai"


class TestAgentCapability:
    """Test AgentCapability model."""
    
    def test_agent_capability_valid(self):
        """Test valid AgentCapability creation."""
        capability = AgentCapability(
            name="keyword_research",
            description="Research and suggest relevant keywords for campaigns",
            input_types=["campaign_brief", "target_audience"],
            output_types=["keyword_list", "search_volume_data"],
            prerequisites=["google_ads_access", "keyword_tool_access"]
        )
        
        assert capability.name == "keyword_research"
        assert capability.description == "Research and suggest relevant keywords for campaigns"
        assert len(capability.input_types) == 2
        assert len(capability.output_types) == 2
        assert len(capability.prerequisites) == 2
    
    def test_agent_capability_minimal(self):
        """Test AgentCapability with minimal required fields."""
        capability = AgentCapability(
            name="basic_task",
            description="Basic task capability",
            input_types=["text"],
            output_types=["text"]
        )
        
        assert capability.name == "basic_task"
        assert capability.prerequisites == []


class TestAgentModel:
    """Test AgentModel model."""
    
    def test_agent_model_valid(self):
        """Test valid AgentModel creation."""
        model = AgentModel(
            provider=ModelProvider.OPENAI,
            model_name="gpt-4",
            temperature=0.7,
            max_tokens=2000,
            top_p=0.9,
            frequency_penalty=0.1,
            presence_penalty=0.1
        )
        
        assert model.provider == ModelProvider.OPENAI
        assert model.model_name == "gpt-4"
        assert model.temperature == 0.7
        assert model.max_tokens == 2000
        assert model.top_p == 0.9
        assert model.frequency_penalty == 0.1
        assert model.presence_penalty == 0.1
    
    def test_agent_model_validation(self):
        """Test AgentModel validation."""
        # Test temperature too high
        with pytest.raises(ValidationError):
            AgentModel(
                provider=ModelProvider.OPENAI,
                model_name="gpt-4",
                temperature=3.0
            )
        
        # Test temperature too low
        with pytest.raises(ValidationError):
            AgentModel(
                provider=ModelProvider.OPENAI,
                model_name="gpt-4",
                temperature=-1.0
            )
        
        # Test max_tokens too low
        with pytest.raises(ValidationError):
            AgentModel(
                provider=ModelProvider.OPENAI,
                model_name="gpt-4",
                max_tokens=0
            )
        
        # Test max_tokens too high
        with pytest.raises(ValidationError):
            AgentModel(
                provider=ModelProvider.OPENAI,
                model_name="gpt-4",
                max_tokens=10000
            )
    
    def test_agent_model_defaults(self):
        """Test AgentModel default values."""
        model = AgentModel(
            provider=ModelProvider.OPENAI,
            model_name="gpt-3.5-turbo"
        )
        
        assert model.temperature == 0.7
        assert model.max_tokens == 2000
        assert model.top_p is None
        assert model.frequency_penalty is None
        assert model.presence_penalty is None


class TestAgentMemory:
    """Test AgentMemory model."""
    
    def test_agent_memory_valid(self):
        """Test valid AgentMemory creation."""
        memory = AgentMemory(
            enabled=True,
            memory_type="vector",
            max_entries=1000,
            retention_days=30,
            similarity_threshold=0.8
        )
        
        assert memory.enabled is True
        assert memory.memory_type == "vector"
        assert memory.max_entries == 1000
        assert memory.retention_days == 30
        assert memory.similarity_threshold == 0.8
    
    def test_agent_memory_defaults(self):
        """Test AgentMemory default values."""
        memory = AgentMemory()
        
        assert memory.enabled is True
        assert memory.memory_type == "vector"
        assert memory.max_entries == 1000
        assert memory.retention_days == 30
        assert memory.similarity_threshold == 0.8


class TestAgentTool:
    """Test AgentTool model."""
    
    def test_agent_tool_valid(self):
        """Test valid AgentTool creation."""
        tool = AgentTool(
            name="google_ads_api",
            description="Access Google Ads API for campaign management",
            parameters={"api_version": "v13", "timeout": 30},
            enabled=True,
            rate_limit=100
        )
        
        assert tool.name == "google_ads_api"
        assert tool.description == "Access Google Ads API for campaign management"
        assert tool.parameters["api_version"] == "v13"
        assert tool.enabled is True
        assert tool.rate_limit == 100
    
    def test_agent_tool_defaults(self):
        """Test AgentTool default values."""
        tool = AgentTool(
            name="basic_tool",
            description="Basic tool"
        )
        
        assert tool.parameters == {}
        assert tool.enabled is True
        assert tool.rate_limit is None


class TestAgentConfig:
    """Test AgentConfig model."""
    
    def test_agent_config_valid(self):
        """Test valid AgentConfig creation."""
        model = AgentModel(
            provider=ModelProvider.OPENAI,
            model_name="gpt-4"
        )
        memory = AgentMemory()
        tools = [
            AgentTool(name="tool1", description="Tool 1"),
            AgentTool(name="tool2", description="Tool 2")
        ]
        
        config = AgentConfig(
            model=model,
            memory=memory,
            tools=tools,
            max_iterations=10,
            timeout_seconds=300,
            retry_attempts=3,
            verbose=True,
            allow_delegation=True,
            system_message="You are a helpful AI assistant."
        )
        
        assert config.model.model_name == "gpt-4"
        assert config.memory.enabled is True
        assert len(config.tools) == 2
        assert config.max_iterations == 10
        assert config.timeout_seconds == 300
        assert config.retry_attempts == 3
        assert config.verbose is True
        assert config.allow_delegation is True
        assert config.system_message == "You are a helpful AI assistant."
    
    def test_agent_config_defaults(self):
        """Test AgentConfig default values."""
        model = AgentModel(
            provider=ModelProvider.OPENAI,
            model_name="gpt-4"
        )
        
        config = AgentConfig(model=model)
        
        assert isinstance(config.memory, AgentMemory)
        assert config.tools == []
        assert config.max_iterations == 10
        assert config.timeout_seconds == 300
        assert config.retry_attempts == 3
        assert config.verbose is False
        assert config.allow_delegation is True
        assert config.system_message is None


class TestAgent:
    """Test Agent model."""
    
    def test_agent_valid(self, fake):
        """Test valid Agent creation."""
        model = AgentModel(
            provider=ModelProvider.OPENAI,
            model_name="gpt-4"
        )
        config = AgentConfig(model=model)
        capabilities = [
            AgentCapability(
                name="test_capability",
                description="Test capability",
                input_types=["text"],
                output_types=["text"]
            )
        ]
        
        agent = Agent(
            id=fake.uuid4(),
            name="Campaign Planner",
            description="AI agent specialized in campaign planning and optimization",
            type=AgentType.CAMPAIGN_PLANNING,
            status=AgentStatus.ACTIVE,
            config=config,
            capabilities=capabilities,
            campaign_id=fake.uuid4(),
            user_id=fake.uuid4(),
            tasks_completed=25,
            tasks_failed=2,
            average_execution_time=45.5,
            success_rate=0.92,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        assert agent.name == "Campaign Planner"
        assert agent.type == AgentType.CAMPAIGN_PLANNING
        assert agent.status == AgentStatus.ACTIVE
        assert len(agent.capabilities) == 1
        assert agent.tasks_completed == 25
        assert agent.tasks_failed == 2
        assert agent.success_rate == 0.92
    
    def test_agent_validation(self, fake):
        """Test Agent validation."""
        model = AgentModel(provider=ModelProvider.OPENAI, model_name="gpt-4")
        config = AgentConfig(model=model)
        
        # Test empty name
        with pytest.raises(ValidationError):
            Agent(
                id=fake.uuid4(),
                name="",
                description="Test agent",
                type=AgentType.CAMPAIGN_PLANNING,
                config=config,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
        
        # Test name too long
        with pytest.raises(ValidationError):
            Agent(
                id=fake.uuid4(),
                name="a" * 256,
                description="Test agent",
                type=AgentType.CAMPAIGN_PLANNING,
                config=config,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
    
    def test_agent_defaults(self, fake):
        """Test Agent default values."""
        model = AgentModel(provider=ModelProvider.OPENAI, model_name="gpt-4")
        config = AgentConfig(model=model)
        
        agent = Agent(
            id=fake.uuid4(),
            name="Test Agent",
            description="Test description",
            type=AgentType.CAMPAIGN_PLANNING,
            config=config,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        assert agent.status == AgentStatus.CREATED
        assert agent.capabilities == []
        assert agent.campaign_id is None
        assert agent.user_id is None
        assert agent.tasks_completed == 0
        assert agent.tasks_failed == 0
        assert agent.version == "1.0.0"


class TestAgentTask:
    """Test AgentTask model."""
    
    def test_agent_task_valid(self, fake):
        """Test valid AgentTask creation."""
        task = AgentTask(
            id=fake.uuid4(),
            agent_id=fake.uuid4(),
            campaign_id=fake.uuid4(),
            name="Keyword Research",
            description="Research relevant keywords for the campaign",
            type="keyword_research",
            priority=TaskPriority.HIGH,
            status=TaskStatus.RUNNING,
            input_data={"campaign_brief": "Sell running shoes", "budget": 1000},
            context={"user_id": fake.uuid4(), "deadline": "2024-01-31"},
            started_at=datetime.utcnow(),
            retry_count=1,
            scheduled_at=datetime.utcnow() + timedelta(hours=1),
            deadline=datetime.utcnow() + timedelta(days=1),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        assert task.name == "Keyword Research"
        assert task.type == "keyword_research"
        assert task.priority == TaskPriority.HIGH
        assert task.status == TaskStatus.RUNNING
        assert "campaign_brief" in task.input_data
        assert "user_id" in task.context
        assert task.retry_count == 1
    
    def test_agent_task_defaults(self, fake):
        """Test AgentTask default values."""
        task = AgentTask(
            id=fake.uuid4(),
            agent_id=fake.uuid4(),
            name="Test Task",
            description="Test description",
            type="test",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        assert task.priority == TaskPriority.NORMAL
        assert task.status == TaskStatus.PENDING
        assert task.input_data == {}
        assert task.output_data is None
        assert task.context == {}
        assert task.retry_count == 0
        assert task.logs == []


class TestAgentMetrics:
    """Test AgentMetrics model."""
    
    def test_agent_metrics_valid(self, fake):
        """Test valid AgentMetrics creation."""
        metrics = AgentMetrics(
            agent_id=fake.uuid4(),
            period_start=datetime(2024, 1, 1),
            period_end=datetime(2024, 1, 31),
            total_tasks=100,
            completed_tasks=95,
            failed_tasks=3,
            cancelled_tasks=2,
            success_rate=0.95,
            average_execution_time=30.5,
            throughput_per_hour=12.5,
            avg_memory_usage_mb=256.5,
            avg_cpu_usage_percent=45.2,
            uptime_percentage=99.5,
            output_quality_score=4.8,
            user_satisfaction_score=4.9,
            error_rate=0.03
        )
        
        assert metrics.total_tasks == 100
        assert metrics.completed_tasks == 95
        assert metrics.failed_tasks == 3
        assert metrics.success_rate == 0.95
        assert metrics.average_execution_time == 30.5
        assert metrics.uptime_percentage == 99.5
    
    def test_agent_metrics_defaults(self, fake):
        """Test AgentMetrics default values."""
        metrics = AgentMetrics(
            agent_id=fake.uuid4(),
            period_start=datetime(2024, 1, 1),
            period_end=datetime(2024, 1, 31)
        )
        
        assert metrics.total_tasks == 0
        assert metrics.completed_tasks == 0
        assert metrics.failed_tasks == 0
        assert metrics.success_rate == 0
        assert metrics.average_execution_time == 0
        assert metrics.error_rate == 0


class TestAgentCreate:
    """Test AgentCreate model."""
    
    def test_agent_create_valid(self):
        """Test valid AgentCreate creation."""
        model = AgentModel(provider=ModelProvider.OPENAI, model_name="gpt-4")
        config = AgentConfig(model=model)
        
        agent_create = AgentCreate(
            name="New Agent",
            description="New AI agent for testing",
            type=AgentType.KEYWORD_RESEARCH,
            config=config
        )
        
        assert agent_create.name == "New Agent"
        assert agent_create.description == "New AI agent for testing"
        assert agent_create.type == AgentType.KEYWORD_RESEARCH
        assert agent_create.config is not None
        assert agent_create.campaign_id is None


class TestAgentUpdate:
    """Test AgentUpdate model."""
    
    def test_agent_update_valid(self):
        """Test valid AgentUpdate creation."""
        model = AgentModel(provider=ModelProvider.OPENAI, model_name="gpt-4")
        config = AgentConfig(model=model)
        
        agent_update = AgentUpdate(
            name="Updated Agent Name",
            description="Updated description",
            status=AgentStatus.PAUSED,
            config=config
        )
        
        assert agent_update.name == "Updated Agent Name"
        assert agent_update.description == "Updated description"
        assert agent_update.status == AgentStatus.PAUSED
        assert agent_update.config is not None
    
    def test_agent_update_all_optional(self):
        """Test AgentUpdate with all optional fields."""
        agent_update = AgentUpdate()
        
        assert agent_update.name is None
        assert agent_update.description is None
        assert agent_update.status is None
        assert agent_update.campaign_id is None
        assert agent_update.config is None


class TestTaskCreate:
    """Test TaskCreate model."""
    
    def test_task_create_valid(self, fake):
        """Test valid TaskCreate creation."""
        task_create = TaskCreate(
            agent_id=fake.uuid4(),
            name="New Task",
            description="New task for testing",
            type="test_task",
            priority=TaskPriority.HIGH,
            input_data={"key": "value"},
            context={"source": "api"},
            scheduled_at=datetime.utcnow() + timedelta(hours=1),
            deadline=datetime.utcnow() + timedelta(days=1)
        )
        
        assert task_create.name == "New Task"
        assert task_create.description == "New task for testing"
        assert task_create.type == "test_task"
        assert task_create.priority == TaskPriority.HIGH
        assert task_create.input_data == {"key": "value"}
        assert task_create.context == {"source": "api"}
    
    def test_task_create_defaults(self, fake):
        """Test TaskCreate default values."""
        task_create = TaskCreate(
            agent_id=fake.uuid4(),
            name="Test Task",
            description="Test description",
            type="test"
        )
        
        assert task_create.priority == TaskPriority.NORMAL
        assert task_create.input_data == {}
        assert task_create.context == {}
        assert task_create.scheduled_at is None
        assert task_create.deadline is None


class TestTaskUpdate:
    """Test TaskUpdate model."""
    
    def test_task_update_valid(self):
        """Test valid TaskUpdate creation."""
        task_update = TaskUpdate(
            status=TaskStatus.COMPLETED,
            priority=TaskPriority.LOW,
            output_data={"result": "success"},
            result="Task completed successfully",
            scheduled_at=datetime.utcnow() + timedelta(hours=2)
        )
        
        assert task_update.status == TaskStatus.COMPLETED
        assert task_update.priority == TaskPriority.LOW
        assert task_update.output_data == {"result": "success"}
        assert task_update.result == "Task completed successfully"
    
    def test_task_update_all_optional(self):
        """Test TaskUpdate with all optional fields."""
        task_update = TaskUpdate()
        
        assert task_update.status is None
        assert task_update.priority is None
        assert task_update.output_data is None
        assert task_update.result is None
        assert task_update.error_message is None


class TestAgentCommand:
    """Test AgentCommand model."""
    
    def test_agent_command_valid(self):
        """Test valid AgentCommand creation."""
        command = AgentCommand(
            command="start_keyword_research",
            parameters={"campaign_id": "12345", "max_keywords": 50},
            timeout_seconds=300,
            priority=TaskPriority.HIGH
        )
        
        assert command.command == "start_keyword_research"
        assert command.parameters["campaign_id"] == "12345"
        assert command.timeout_seconds == 300
        assert command.priority == TaskPriority.HIGH
    
    def test_agent_command_defaults(self):
        """Test AgentCommand default values."""
        command = AgentCommand(command="test_command")
        
        assert command.parameters == {}
        assert command.timeout_seconds is None
        assert command.priority == TaskPriority.NORMAL


class TestAgentCollaboration:
    """Test AgentCollaboration model."""
    
    def test_agent_collaboration_valid(self):
        """Test valid AgentCollaboration creation."""
        collaboration = AgentCollaboration(
            enabled=True,
            max_collaborators=3,
            collaboration_strategy="peer_to_peer",
            communication_protocol="websocket",
            shared_memory=True
        )
        
        assert collaboration.enabled is True
        assert collaboration.max_collaborators == 3
        assert collaboration.collaboration_strategy == "peer_to_peer"
        assert collaboration.communication_protocol == "websocket"
        assert collaboration.shared_memory is True
    
    def test_agent_collaboration_defaults(self):
        """Test AgentCollaboration default values."""
        collaboration = AgentCollaboration()
        
        assert collaboration.enabled is True
        assert collaboration.max_collaborators == 5
        assert collaboration.collaboration_strategy == "hierarchical"
        assert collaboration.communication_protocol == "direct"
        assert collaboration.shared_memory is False


class TestAgentPerformanceReport:
    """Test AgentPerformanceReport model."""
    
    def test_agent_performance_report_valid(self, fake):
        """Test valid AgentPerformanceReport creation."""
        metrics = AgentMetrics(
            agent_id=fake.uuid4(),
            period_start=datetime(2024, 1, 1),
            period_end=datetime(2024, 1, 31),
            total_tasks=100,
            completed_tasks=95
        )
        
        report = AgentPerformanceReport(
            agent_id=fake.uuid4(),
            report_period="weekly",
            generated_at=datetime.utcnow(),
            metrics=metrics,
            trends={"success_rate": 0.02, "throughput": -0.05},
            recommendations=["Increase timeout for complex tasks", "Add more memory"],
            issues=["High memory usage detected"]
        )
        
        assert report.report_period == "weekly"
        assert report.metrics.total_tasks == 100
        assert len(report.recommendations) == 2
        assert len(report.issues) == 1
        assert "success_rate" in report.trends
    
    def test_agent_performance_report_validation(self, fake):
        """Test AgentPerformanceReport validation."""
        metrics = AgentMetrics(
            agent_id=fake.uuid4(),
            period_start=datetime(2024, 1, 1),
            period_end=datetime(2024, 1, 31)
        )
        
        # Test invalid report_period
        with pytest.raises(ValidationError):
            AgentPerformanceReport(
                agent_id=fake.uuid4(),
                report_period="invalid_period",
                generated_at=datetime.utcnow(),
                metrics=metrics,
                trends={},
                recommendations=[]
            )
    
    def test_agent_performance_report_defaults(self, fake):
        """Test AgentPerformanceReport default values."""
        metrics = AgentMetrics(
            agent_id=fake.uuid4(),
            period_start=datetime(2024, 1, 1),
            period_end=datetime(2024, 1, 31)
        )
        
        report = AgentPerformanceReport(
            agent_id=fake.uuid4(),
            report_period="daily",
            generated_at=datetime.utcnow(),
            metrics=metrics,
            trends={},
            recommendations=[]
        )
        
        assert report.issues == []
        assert report.comparison_period is None