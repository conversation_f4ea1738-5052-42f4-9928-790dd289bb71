"""
Unit tests for base service classes.
Tests common functionality like rate limiting, authentication, and caching.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from services.base import (
    BaseService, AuthenticatedService, CacheableService, ServiceManager
)
from utils.exceptions import ExternalServiceException, RateLimitException


class MockBaseService(BaseService):
    """Mock implementation of BaseService for testing."""
    
    def __init__(self, service_name: str = "test_service", rate_limit: int = 60):
        super().__init__(service_name, rate_limit)
        self.execute_request_calls = []
        self.mock_response = {"status": "success"}
        self.should_raise_exception = False
    
    async def _execute_request(self, method: str, endpoint: str, **kwargs: Any) -> Any:
        """Mock request execution."""
        self.execute_request_calls.append({
            "method": method,
            "endpoint": endpoint,
            "kwargs": kwargs
        })
        
        if self.should_raise_exception:
            raise Exception("Mock request failed")
        
        return self.mock_response
    
    async def health_check(self) -> Dict[str, Any]:
        """Mock health check."""
        return {"status": "healthy", "service": self.service_name}


class MockAuthenticatedService(AuthenticatedService):
    """Mock implementation of AuthenticatedService for testing."""
    
    def __init__(self, service_name: str = "test_auth_service"):
        super().__init__(service_name, 60, "test-token")
        self.authentication_calls = 0
        self.execute_request_calls = []
        self.mock_response = {"status": "authenticated"}
        self.auth_should_fail = False
    
    async def _perform_authentication(self) -> None:
        """Mock authentication."""
        self.authentication_calls += 1
        if self.auth_should_fail:
            raise Exception("Authentication failed")
        self._auth_token = "authenticated-token"
        self._token_expires_at = datetime.utcnow() + timedelta(hours=1)
    
    async def _execute_request(self, method: str, endpoint: str, **kwargs: Any) -> Any:
        """Mock request execution."""
        self.execute_request_calls.append({
            "method": method,
            "endpoint": endpoint,
            "kwargs": kwargs
        })
        return self.mock_response
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Mock auth headers."""
        return {"Authorization": f"Bearer {self._auth_token}"}
    
    async def health_check(self) -> Dict[str, Any]:
        """Mock health check."""
        return {"status": "healthy", "authenticated": self.is_authenticated}


class MockCacheableService(CacheableService):
    """Mock implementation of CacheableService for testing."""
    
    def __init__(self, cache_ttl: int = 300):
        super().__init__("test_cache_service", 60, cache_ttl)
        self.execute_request_calls = []
        self.mock_response = {"status": "cached"}
    
    async def _execute_request(self, method: str, endpoint: str, **kwargs: Any) -> Any:
        """Mock request execution."""
        self.execute_request_calls.append({
            "method": method,
            "endpoint": endpoint,
            "kwargs": kwargs
        })
        return self.mock_response
    
    async def health_check(self) -> Dict[str, Any]:
        """Mock health check."""
        return {"status": "healthy", "cache_entries": len(self._cache)}


class TestBaseService:
    """Test BaseService functionality."""
    
    @pytest.fixture
    def mock_service(self):
        """Create mock base service."""
        return MockBaseService()
    
    @pytest.mark.asyncio
    async def test_service_initialization(self, mock_service):
        """Test service initialization."""
        assert mock_service.service_name == "test_service"
        assert mock_service.rate_limit_per_minute == 60
        assert mock_service._request_timestamps == []
    
    @pytest.mark.asyncio
    async def test_make_request_success(self, mock_service):
        """Test successful request."""
        response = await mock_service._make_request("GET", "/test")
        
        assert response == {"status": "success"}
        assert len(mock_service.execute_request_calls) == 1
        assert mock_service.execute_request_calls[0]["method"] == "GET"
        assert mock_service.execute_request_calls[0]["endpoint"] == "/test"
    
    @pytest.mark.asyncio
    async def test_make_request_failure(self, mock_service):
        """Test request failure handling."""
        mock_service.should_raise_exception = True
        
        with pytest.raises(ExternalServiceException) as exc_info:
            await mock_service._make_request("POST", "/test")
        
        assert exc_info.value.service_name == "test_service"
        assert "Mock request failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, mock_service):
        """Test rate limiting functionality."""
        # Set a very low rate limit for testing
        mock_service.rate_limit_per_minute = 2
        
        # First two requests should succeed
        await mock_service._make_request("GET", "/test1")
        await mock_service._make_request("GET", "/test2")
        
        # Third request should fail due to rate limit
        with pytest.raises(RateLimitException) as exc_info:
            await mock_service._make_request("GET", "/test3")
        
        assert "Rate limit exceeded" in str(exc_info.value)
        assert exc_info.value.details["rate_limit"] == 2
    
    @pytest.mark.asyncio
    async def test_rate_limit_reset(self, mock_service):
        """Test rate limit reset over time."""
        # Set very low rate limit
        mock_service.rate_limit_per_minute = 1
        
        # Make one request
        await mock_service._make_request("GET", "/test1")
        
        # Simulate time passing by manipulating timestamps
        mock_service._request_timestamps[0] = datetime.utcnow() - timedelta(minutes=2)
        
        # Next request should succeed after reset
        await mock_service._make_request("GET", "/test2")
        assert len(mock_service.execute_request_calls) == 2
    
    @pytest.mark.asyncio
    async def test_health_check(self, mock_service):
        """Test health check functionality."""
        health = await mock_service.health_check()
        
        assert health["status"] == "healthy"
        assert health["service"] == "test_service"
    
    @pytest.mark.asyncio
    async def test_initialize_and_cleanup(self, mock_service):
        """Test service initialization and cleanup."""
        await mock_service.initialize()
        await mock_service.cleanup()
        # Should not raise any exceptions


class TestAuthenticatedService:
    """Test AuthenticatedService functionality."""
    
    @pytest.fixture
    def auth_service(self):
        """Create mock authenticated service."""
        return MockAuthenticatedService()
    
    @pytest.mark.asyncio
    async def test_authentication_initialization(self, auth_service):
        """Test authentication initialization."""
        assert auth_service.service_name == "test_auth_service"
        assert auth_service._auth_token == "test-token"
        assert auth_service.is_authenticated is True
    
    @pytest.mark.asyncio
    async def test_perform_authentication(self, auth_service):
        """Test authentication process."""
        # Clear existing token
        auth_service._auth_token = None
        assert auth_service.is_authenticated is False
        
        # Perform authentication
        await auth_service.authenticate()
        
        assert auth_service.authentication_calls == 1
        assert auth_service._auth_token == "authenticated-token"
        assert auth_service.is_authenticated is True
    
    @pytest.mark.asyncio
    async def test_authentication_failure(self, auth_service):
        """Test authentication failure handling."""
        auth_service._auth_token = None
        auth_service.auth_should_fail = True
        
        with pytest.raises(ExternalServiceException) as exc_info:
            await auth_service.authenticate()
        
        assert "Authentication failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_token_expiration_check(self, auth_service):
        """Test token expiration checking."""
        # Set token as expired
        auth_service._token_expires_at = datetime.utcnow() - timedelta(minutes=1)
        assert auth_service.token_expired is True
        
        # Set token as not expired
        auth_service._token_expires_at = datetime.utcnow() + timedelta(hours=1)
        assert auth_service.token_expired is False
    
    @pytest.mark.asyncio
    async def test_authenticated_request(self, auth_service):
        """Test making authenticated requests."""
        response = await auth_service._make_authenticated_request("GET", "/auth-test")
        
        assert response == {"status": "authenticated"}
        assert len(auth_service.execute_request_calls) == 1
        
        # Check that auth headers were added
        call = auth_service.execute_request_calls[0]
        assert "headers" in call["kwargs"]
        assert "Authorization" in call["kwargs"]["headers"]
        assert call["kwargs"]["headers"]["Authorization"] == "Bearer authenticated-token"
    
    @pytest.mark.asyncio
    async def test_skip_authentication_if_valid(self, auth_service):
        """Test skipping authentication if token is still valid."""
        # Token should already be valid
        initial_calls = auth_service.authentication_calls
        
        await auth_service.authenticate()
        
        # Should not have made additional auth calls
        assert auth_service.authentication_calls == initial_calls


class TestCacheableService:
    """Test CacheableService functionality."""
    
    @pytest.fixture
    def cache_service(self):
        """Create mock cacheable service."""
        return MockCacheableService()
    
    @pytest.mark.asyncio
    async def test_cache_key_generation(self, cache_service):
        """Test cache key generation."""
        key1 = cache_service._get_cache_key("GET", "/test", params={"q": "search"})
        key2 = cache_service._get_cache_key("GET", "/test", params={"q": "search"})
        key3 = cache_service._get_cache_key("GET", "/test", params={"q": "different"})
        
        # Same parameters should generate same key
        assert key1 == key2
        # Different parameters should generate different key
        assert key1 != key3
        assert len(key1) == 32  # MD5 hash length
    
    @pytest.mark.asyncio
    async def test_cache_miss(self, cache_service):
        """Test cache miss behavior."""
        response = await cache_service._make_cached_request("GET", "/test")
        
        assert response == {"status": "cached"}
        assert len(cache_service.execute_request_calls) == 1
        assert len(cache_service._cache) == 1
    
    @pytest.mark.asyncio
    async def test_cache_hit(self, cache_service):
        """Test cache hit behavior."""
        # First request (cache miss)
        response1 = await cache_service._make_cached_request("GET", "/test")
        assert len(cache_service.execute_request_calls) == 1
        
        # Second request (cache hit)
        response2 = await cache_service._make_cached_request("GET", "/test")
        
        assert response1 == response2
        # Should not have made additional request
        assert len(cache_service.execute_request_calls) == 1
    
    @pytest.mark.asyncio
    async def test_cache_expiration(self, cache_service):
        """Test cache expiration."""
        # Set very short cache TTL
        cache_service.cache_ttl_seconds = 1
        
        # First request
        await cache_service._make_cached_request("GET", "/test")
        assert len(cache_service.execute_request_calls) == 1
        
        # Wait for cache to expire
        await asyncio.sleep(1.1)
        
        # Second request should bypass cache
        await cache_service._make_cached_request("GET", "/test")
        assert len(cache_service.execute_request_calls) == 2
    
    @pytest.mark.asyncio
    async def test_cache_disabled(self, cache_service):
        """Test requests with caching disabled."""
        # First request without cache
        response1 = await cache_service._make_cached_request("GET", "/test", use_cache=False)
        assert len(cache_service.execute_request_calls) == 1
        assert len(cache_service._cache) == 0  # Should not cache
        
        # Second request without cache
        response2 = await cache_service._make_cached_request("GET", "/test", use_cache=False)
        assert len(cache_service.execute_request_calls) == 2
        assert response1 == response2
    
    @pytest.mark.asyncio
    async def test_clear_cache(self, cache_service):
        """Test cache clearing."""
        # Add some entries to cache
        await cache_service._make_cached_request("GET", "/test1")
        await cache_service._make_cached_request("GET", "/test2")
        assert len(cache_service._cache) == 2
        
        # Clear cache
        cache_service.clear_cache()
        assert len(cache_service._cache) == 0


class TestServiceManager:
    """Test ServiceManager functionality."""
    
    @pytest.fixture
    def service_manager(self):
        """Create service manager."""
        return ServiceManager()
    
    @pytest.fixture
    def mock_services(self):
        """Create mock services for testing."""
        return {
            "service1": MockBaseService("service1"),
            "service2": MockAuthenticatedService("service2"),
            "service3": MockCacheableService(),
        }
    
    def test_service_registration(self, service_manager, mock_services):
        """Test service registration."""
        for name, service in mock_services.items():
            service_manager.register_service(name, service)
        
        assert len(service_manager._services) == 3
        assert service_manager.get_service("service1") == mock_services["service1"]
        assert service_manager.get_service("service2") == mock_services["service2"]
        assert service_manager.get_service("service3") == mock_services["service3"]
    
    def test_get_nonexistent_service(self, service_manager):
        """Test getting non-existent service."""
        service = service_manager.get_service("nonexistent")
        assert service is None
    
    @pytest.mark.asyncio
    async def test_initialize_all_services(self, service_manager, mock_services):
        """Test initializing all services."""
        for name, service in mock_services.items():
            service_manager.register_service(name, service)
        
        await service_manager.initialize_all()
        # Should complete without errors
    
    @pytest.mark.asyncio
    async def test_initialize_with_failure(self, service_manager):
        """Test initialization with service failure."""
        # Create a service that fails on initialization
        failing_service = MockBaseService()
        failing_service.initialize = AsyncMock(side_effect=Exception("Init failed"))
        
        service_manager.register_service("failing", failing_service)
        
        # Should not raise exception, but log the error
        await service_manager.initialize_all()
    
    @pytest.mark.asyncio
    async def test_cleanup_all_services(self, service_manager, mock_services):
        """Test cleaning up all services."""
        for name, service in mock_services.items():
            service_manager.register_service(name, service)
        
        await service_manager.cleanup_all()
        # Should complete without errors
    
    @pytest.mark.asyncio
    async def test_health_check_all_services(self, service_manager, mock_services):
        """Test health checking all services."""
        for name, service in mock_services.items():
            service_manager.register_service(name, service)
        
        results = await service_manager.health_check_all()
        
        assert len(results) == 3
        assert results["service1"]["healthy"] is True
        assert results["service2"]["healthy"] is True
        assert results["service3"]["healthy"] is True
    
    @pytest.mark.asyncio
    async def test_health_check_with_failure(self, service_manager):
        """Test health check with service failure."""
        failing_service = MockBaseService()
        failing_service.health_check = AsyncMock(side_effect=Exception("Health check failed"))
        
        service_manager.register_service("failing", failing_service)
        
        results = await service_manager.health_check_all()
        
        assert results["failing"]["healthy"] is False
        assert "Health check failed" in results["failing"]["error"]


class TestServiceManagerGlobal:
    """Test global service manager instance."""
    
    @pytest.mark.asyncio
    async def test_global_service_manager_exists(self):
        """Test that global service manager exists."""
        from services.base import service_manager
        
        assert service_manager is not None
        assert isinstance(service_manager, ServiceManager)
    
    @pytest.mark.asyncio
    async def test_global_service_manager_health_check(self):
        """Test health check on global service manager."""
        from services.base import service_manager
        
        # Should work even with no services registered
        results = await service_manager.health_check_all()
        assert isinstance(results, dict)