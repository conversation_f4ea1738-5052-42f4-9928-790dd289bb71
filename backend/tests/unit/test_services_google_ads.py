"""
Unit tests for Google Ads service.
Tests Google Ads API integration, campaign management, and data operations.
"""

import pytest
from datetime import datetime, date
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from typing import Dict, Any, List

from services.google_ads import GoogleAdsService
from models.campaigns import Campaign, CampaignType, CampaignStatus, BiddingStrategy
from utils.exceptions import GoogleAdsException


class TestGoogleAdsService:
    """Test GoogleAdsService functionality."""
    
    @pytest.fixture
    def google_ads_service(self):
        """Create GoogleAdsService instance."""
        with patch('services.google_ads.settings') as mock_settings:
            mock_settings.GOOGLE_ADS_CUSTOMER_ID = "**********"
            mock_settings.GOOGLE_ADS_DEVELOPER_TOKEN = "test-dev-token"
            mock_settings.GOOGLE_ADS_CLIENT_ID = "test-client-id"
            mock_settings.GOOGLE_ADS_CLIENT_SECRET = "test-client-secret"
            mock_settings.GOOGLE_ADS_REFRESH_TOKEN = "test-refresh-token"
            
            service = GoogleAdsService()
            return service
    
    @pytest.fixture
    def mock_google_ads_client(self):
        """Create mock Google Ads client."""
        mock_client = MagicMock()
        
        # Mock services
        mock_client.get_service.return_value = MagicMock()
        
        # Mock campaign service
        campaign_service = MagicMock()
        campaign_service.mutate_campaigns = MagicMock()
        mock_client.get_service.return_value = campaign_service
        
        return mock_client
    
    def test_service_initialization(self, google_ads_service):
        """Test GoogleAdsService initialization."""
        assert google_ads_service.service_name == "Google Ads API"
        assert google_ads_service.rate_limit_per_minute == 1000
        assert google_ads_service._customer_id == "**********"
        assert google_ads_service._client is None
    
    def test_campaign_type_mapping(self, google_ads_service):
        """Test campaign type mapping."""
        mapping = google_ads_service._campaign_type_mapping
        
        assert mapping[CampaignType.SEARCH] == "SEARCH"
        assert mapping[CampaignType.DISPLAY] == "DISPLAY"
        assert mapping[CampaignType.SHOPPING] == "SHOPPING_CAMPAIGN"
        assert mapping[CampaignType.VIDEO] == "VIDEO"
        assert mapping[CampaignType.PERFORMANCE_MAX] == "PERFORMANCE_MAX"
    
    def test_campaign_status_mapping(self, google_ads_service):
        """Test campaign status mapping."""
        mapping = google_ads_service._campaign_status_mapping
        
        assert mapping[CampaignStatus.ACTIVE] == "ENABLED"
        assert mapping[CampaignStatus.PAUSED] == "PAUSED"
        assert mapping[CampaignStatus.REMOVED] == "REMOVED"
    
    @pytest.mark.asyncio
    async def test_perform_authentication_success(self, google_ads_service):
        """Test successful authentication."""
        with patch('services.google_ads.GoogleAdsClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.load_from_dict.return_value = mock_client
            
            await google_ads_service._perform_authentication()
            
            assert google_ads_service._client == mock_client
            mock_client_class.load_from_dict.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_perform_authentication_missing_config(self, google_ads_service):
        """Test authentication with missing configuration."""
        # Clear customer ID to simulate missing config
        google_ads_service._customer_id = None
        
        with pytest.raises(GoogleAdsException) as exc_info:
            await google_ads_service._perform_authentication()
        
        assert "Google Ads configuration not complete" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_execute_request_success(self, google_ads_service, mock_google_ads_client):
        """Test successful request execution."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock successful response
        mock_response = MagicMock()
        mock_response.results = [{"campaign": {"name": "Test Campaign"}}]
        mock_google_ads_client.get_service().search_stream.return_value = [mock_response]
        
        response = await google_ads_service._execute_request(
            "GET", "campaigns", query="SELECT campaign.name FROM campaign"
        )
        
        assert response is not None
        mock_google_ads_client.get_service.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_request_google_ads_exception(self, google_ads_service, mock_google_ads_client):
        """Test request execution with Google Ads exception."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock Google Ads exception
        from google.ads.googleads.errors import GoogleAdsException as GoogleAdsAPIException
        mock_exception = GoogleAdsAPIException(
            error=MagicMock(),
            call=MagicMock(),
            failure=MagicMock()
        )
        mock_google_ads_client.get_service().search_stream.side_effect = mock_exception
        
        with pytest.raises(Exception):  # Will be wrapped as general exception
            await google_ads_service._execute_request(
                "GET", "campaigns", query="SELECT campaign.name FROM campaign"
            )
    
    @pytest.mark.asyncio
    async def test_health_check_success(self, google_ads_service, mock_google_ads_client):
        """Test successful health check."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock successful customer query
        mock_response = MagicMock()
        mock_response.results = [{"customer": {"id": "**********"}}]
        mock_google_ads_client.get_service().search_stream.return_value = [mock_response]
        
        health = await google_ads_service.health_check()
        
        assert health["status"] == "healthy"
        assert health["customer_id"] == "**********"
        assert health["connected"] is True
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, google_ads_service):
        """Test health check failure."""
        # No client set (not authenticated)
        health = await google_ads_service.health_check()
        
        assert health["status"] == "unhealthy"
        assert health["connected"] is False
        assert "error" in health
    
    @pytest.mark.asyncio
    async def test_create_campaign_success(self, google_ads_service, mock_google_ads_client, sample_campaign_data):
        """Test successful campaign creation."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock successful campaign creation
        mock_response = MagicMock()
        mock_response.results = [MagicMock()]
        mock_response.results[0].resource_name = "customers/**********/campaigns/12345678"
        
        campaign_service = mock_google_ads_client.get_service.return_value
        campaign_service.mutate_campaigns.return_value = mock_response
        
        result = await google_ads_service.create_campaign(sample_campaign_data)
        
        assert result is not None
        assert "campaign_id" in result
        campaign_service.mutate_campaigns.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_campaign_validation_error(self, google_ads_service):
        """Test campaign creation with validation error."""
        invalid_data = {}  # Missing required fields
        
        with pytest.raises(GoogleAdsException) as exc_info:
            await google_ads_service.create_campaign(invalid_data)
        
        assert "Missing required campaign data" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_campaign_success(self, google_ads_service, mock_google_ads_client):
        """Test successful campaign retrieval."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock successful campaign query
        mock_response = MagicMock()
        mock_response.results = [MagicMock()]
        mock_response.results[0].campaign = MagicMock()
        mock_response.results[0].campaign.name = "Test Campaign"
        mock_response.results[0].campaign.id = "12345678"
        mock_response.results[0].campaign.status.name = "ENABLED"
        
        google_ads_client_service = mock_google_ads_client.get_service.return_value
        google_ads_client_service.search_stream.return_value = [mock_response]
        
        campaign = await google_ads_service.get_campaign("12345678")
        
        assert campaign is not None
        assert campaign["name"] == "Test Campaign"
        assert campaign["id"] == "12345678"
    
    @pytest.mark.asyncio
    async def test_get_campaign_not_found(self, google_ads_service, mock_google_ads_client):
        """Test campaign retrieval when campaign not found."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock empty response
        mock_response = MagicMock()
        mock_response.results = []
        
        google_ads_client_service = mock_google_ads_client.get_service.return_value
        google_ads_client_service.search_stream.return_value = [mock_response]
        
        campaign = await google_ads_service.get_campaign("nonexistent")
        assert campaign is None
    
    @pytest.mark.asyncio
    async def test_update_campaign_success(self, google_ads_service, mock_google_ads_client):
        """Test successful campaign update."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock successful campaign update
        mock_response = MagicMock()
        mock_response.results = [MagicMock()]
        mock_response.results[0].resource_name = "customers/**********/campaigns/12345678"
        
        campaign_service = mock_google_ads_client.get_service.return_value
        campaign_service.mutate_campaigns.return_value = mock_response
        
        update_data = {
            "name": "Updated Campaign",
            "status": CampaignStatus.PAUSED
        }
        
        result = await google_ads_service.update_campaign("12345678", update_data)
        
        assert result is not None
        assert "updated" in result
        campaign_service.mutate_campaigns.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_campaign_success(self, google_ads_service, mock_google_ads_client):
        """Test successful campaign deletion."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock successful campaign deletion
        mock_response = MagicMock()
        mock_response.results = [MagicMock()]
        
        campaign_service = mock_google_ads_client.get_service.return_value
        campaign_service.mutate_campaigns.return_value = mock_response
        
        result = await google_ads_service.delete_campaign("12345678")
        
        assert result is True
        campaign_service.mutate_campaigns.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_list_campaigns_success(self, google_ads_service, mock_google_ads_client):
        """Test successful campaign listing."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock successful campaigns query
        mock_response = MagicMock()
        mock_campaigns = []
        
        for i in range(3):
            mock_campaign = MagicMock()
            mock_campaign.campaign.name = f"Campaign {i}"
            mock_campaign.campaign.id = f"1234567{i}"
            mock_campaign.campaign.status.name = "ENABLED"
            mock_campaigns.append(mock_campaign)
        
        mock_response.results = mock_campaigns
        
        google_ads_client_service = mock_google_ads_client.get_service.return_value
        google_ads_client_service.search_stream.return_value = [mock_response]
        
        campaigns = await google_ads_service.list_campaigns()
        
        assert len(campaigns) == 3
        assert campaigns[0]["name"] == "Campaign 0"
        assert campaigns[1]["name"] == "Campaign 1"
        assert campaigns[2]["name"] == "Campaign 2"
    
    @pytest.mark.asyncio
    async def test_get_campaign_performance_success(self, google_ads_service, mock_google_ads_client):
        """Test successful campaign performance retrieval."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock performance data
        mock_response = MagicMock()
        mock_response.results = [MagicMock()]
        mock_metrics = mock_response.results[0].metrics
        mock_metrics.impressions = 10000
        mock_metrics.clicks = 500
        mock_metrics.cost_micros = 250000000  # $250.00 in micros
        mock_metrics.conversions = 25.5
        
        google_ads_client_service = mock_google_ads_client.get_service.return_value
        google_ads_client_service.search_stream.return_value = [mock_response]
        
        performance = await google_ads_service.get_campaign_performance(
            "12345678", 
            start_date=date(2024, 1, 1), 
            end_date=date(2024, 1, 31)
        )
        
        assert performance is not None
        assert performance["impressions"] == 10000
        assert performance["clicks"] == 500
        assert performance["cost"] == 250.0  # Converted from micros
        assert performance["conversions"] == 25.5
    
    @pytest.mark.asyncio
    async def test_get_keywords_success(self, google_ads_service, mock_google_ads_client):
        """Test successful keyword retrieval."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock keywords data
        mock_response = MagicMock()
        mock_keywords = []
        
        for i in range(2):
            mock_keyword = MagicMock()
            mock_keyword.ad_group_criterion.keyword.text = f"keyword {i}"
            mock_keyword.ad_group_criterion.keyword.match_type.name = "PHRASE"
            mock_keyword.ad_group_criterion.quality_info.quality_score = 8 + i
            mock_keywords.append(mock_keyword)
        
        mock_response.results = mock_keywords
        
        google_ads_client_service = mock_google_ads_client.get_service.return_value
        google_ads_client_service.search_stream.return_value = [mock_response]
        
        keywords = await google_ads_service.get_keywords("12345678")
        
        assert len(keywords) == 2
        assert keywords[0]["text"] == "keyword 0"
        assert keywords[0]["match_type"] == "PHRASE"
        assert keywords[0]["quality_score"] == 8
        assert keywords[1]["quality_score"] == 9
    
    @pytest.mark.asyncio
    async def test_add_keywords_success(self, google_ads_service, mock_google_ads_client):
        """Test successful keyword addition."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock successful keyword addition
        mock_response = MagicMock()
        mock_response.results = [MagicMock(), MagicMock()]
        
        ad_group_criterion_service = mock_google_ads_client.get_service.return_value
        ad_group_criterion_service.mutate_ad_group_criteria.return_value = mock_response
        
        keywords_data = [
            {"text": "running shoes", "match_type": "PHRASE", "max_cpc": 2.50},
            {"text": "athletic footwear", "match_type": "BROAD", "max_cpc": 1.75}
        ]
        
        result = await google_ads_service.add_keywords("12345678", "67890", keywords_data)
        
        assert result is not None
        assert result["added_count"] == 2
        ad_group_criterion_service.mutate_ad_group_criteria.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_ad_suggestions_success(self, google_ads_service, mock_google_ads_client):
        """Test successful ad suggestions retrieval."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock ad suggestions (this would typically come from AI or recommendations API)
        suggestions = await google_ads_service.get_ad_suggestions(
            campaign_id="12345678",
            keywords=["running shoes", "athletic footwear"],
            target_audience="fitness enthusiasts"
        )
        
        # For now, this returns mock data since we don't have the full implementation
        assert suggestions is not None
        assert isinstance(suggestions, dict)
    
    @pytest.mark.asyncio
    async def test_optimize_bids_success(self, google_ads_service, mock_google_ads_client):
        """Test successful bid optimization."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock successful bid optimization
        mock_response = MagicMock()
        mock_response.results = [MagicMock()]
        
        ad_group_criterion_service = mock_google_ads_client.get_service.return_value
        ad_group_criterion_service.mutate_ad_group_criteria.return_value = mock_response
        
        result = await google_ads_service.optimize_bids("12345678")
        
        assert result is not None
        assert isinstance(result, dict)
        # Implementation would include actual bid optimization logic
    
    @pytest.mark.asyncio
    async def test_format_campaign_data(self, google_ads_service, sample_campaign_data):
        """Test campaign data formatting for Google Ads API."""
        # This method would format data for API calls
        formatted_data = google_ads_service._format_campaign_data(sample_campaign_data)
        
        assert isinstance(formatted_data, dict)
        assert "name" in formatted_data
        assert "type" in formatted_data
        assert "budget_amount" in formatted_data
    
    def test_get_auth_headers(self, google_ads_service):
        """Test authentication headers generation."""
        google_ads_service._auth_token = "test-token"
        headers = google_ads_service._get_auth_headers()
        
        assert "Authorization" in headers
        assert headers["Authorization"] == "Bearer test-token"
    
    @pytest.mark.asyncio
    async def test_service_with_rate_limiting(self, google_ads_service, mock_google_ads_client):
        """Test service respects rate limiting."""
        google_ads_service._client = mock_google_ads_client
        google_ads_service.rate_limit_per_minute = 2  # Set low limit for testing
        
        # Mock successful responses
        mock_response = MagicMock()
        mock_response.results = []
        google_ads_client_service = mock_google_ads_client.get_service.return_value
        google_ads_client_service.search_stream.return_value = [mock_response]
        
        # First two requests should succeed
        await google_ads_service.get_campaign("12345")
        await google_ads_service.get_campaign("67890")
        
        # Third request should be rate limited
        from utils.exceptions import RateLimitException
        with pytest.raises(RateLimitException):
            await google_ads_service.get_campaign("11111")
    
    @pytest.mark.asyncio
    async def test_caching_functionality(self, google_ads_service, mock_google_ads_client):
        """Test caching functionality."""
        google_ads_service._client = mock_google_ads_client
        
        # Mock successful response
        mock_response = MagicMock()
        mock_response.results = [MagicMock()]
        mock_response.results[0].campaign.name = "Cached Campaign"
        
        google_ads_client_service = mock_google_ads_client.get_service.return_value
        google_ads_client_service.search_stream.return_value = [mock_response]
        
        # First request should hit the API
        campaign1 = await google_ads_service.get_campaign("12345")
        assert google_ads_client_service.search_stream.call_count == 1
        
        # Second request should use cache
        campaign2 = await google_ads_service.get_campaign("12345")
        assert google_ads_client_service.search_stream.call_count == 1  # No additional call
        
        assert campaign1 == campaign2
    
    def test_micros_conversion_utilities(self, google_ads_service):
        """Test utility methods for micros conversion."""
        # Test dollars to micros
        micros = google_ads_service._dollars_to_micros(25.50)
        assert micros == 25500000
        
        # Test micros to dollars
        dollars = google_ads_service._micros_to_dollars(25500000)
        assert dollars == 25.50
    
    def test_resource_name_utilities(self, google_ads_service):
        """Test resource name utility methods."""
        # Test campaign resource name
        resource_name = google_ads_service._get_campaign_resource_name("12345")
        assert resource_name == "customers/**********/campaigns/12345"
        
        # Test ad group resource name
        resource_name = google_ads_service._get_ad_group_resource_name("67890")
        assert resource_name == "customers/**********/adGroups/67890"


class TestGoogleAdsServiceUtilities:
    """Test utility methods and helper functions."""
    
    @pytest.fixture
    def google_ads_service(self):
        """Create GoogleAdsService instance for utilities testing."""
        with patch('services.google_ads.settings') as mock_settings:
            mock_settings.GOOGLE_ADS_CUSTOMER_ID = "**********"
            return GoogleAdsService()
    
    def test_campaign_type_conversion(self, google_ads_service):
        """Test campaign type conversion between internal and API formats."""
        # Internal to API
        api_type = google_ads_service._convert_campaign_type_to_api(CampaignType.SEARCH)
        assert api_type == "SEARCH"
        
        # API to internal
        internal_type = google_ads_service._convert_campaign_type_from_api("SEARCH")
        assert internal_type == CampaignType.SEARCH
    
    def test_campaign_status_conversion(self, google_ads_service):
        """Test campaign status conversion between internal and API formats."""
        # Internal to API
        api_status = google_ads_service._convert_campaign_status_to_api(CampaignStatus.ACTIVE)
        assert api_status == "ENABLED"
        
        # API to internal
        internal_status = google_ads_service._convert_campaign_status_from_api("ENABLED")
        assert internal_status == CampaignStatus.ACTIVE
    
    def test_date_range_formatting(self, google_ads_service):
        """Test date range formatting for API queries."""
        start_date = date(2024, 1, 1)
        end_date = date(2024, 1, 31)
        
        date_range = google_ads_service._format_date_range(start_date, end_date)
        
        assert "start_date" in date_range
        assert "end_date" in date_range
        assert date_range["start_date"] == "2024-01-01"
        assert date_range["end_date"] == "2024-01-31"