"""
Integration tests for health check API endpoints.
Tests the health monitoring system and service checks.
"""

import pytest
from unittest.mock import patch, AsyncMock, MagicMock
import time

from fastapi import status


class TestHealthCheckEndpoints:
    """Test health check API endpoints."""
    
    @pytest.mark.asyncio
    async def test_liveness_check_success(self, async_client):
        """Test liveness check endpoint."""
        response = await async_client.get("/api/v1/health/liveness")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "status" in data
        assert data["status"] == "alive"
        assert "timestamp" in data
    
    @pytest.mark.asyncio
    async def test_readiness_check_all_services_healthy(
        self, async_client, mock_database_service, mock_redis_service
    ):
        """Test readiness check when all critical services are healthy."""
        # Mock healthy service responses
        mock_database_service.health_check.return_value = {"status": "healthy"}
        mock_redis_service.health_check.return_value = {"status": "healthy"}
        
        response = await async_client.get("/api/v1/health/readiness")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["status"] == "ready"
        assert "timestamp" in data
        assert data["checks"]["database"] is True
        assert data["checks"]["redis"] is True
    
    @pytest.mark.asyncio
    async def test_readiness_check_database_unhealthy(
        self, async_client, mock_database_service, mock_redis_service
    ):
        """Test readiness check when database is unhealthy."""
        # Mock unhealthy database
        mock_database_service.health_check.return_value = {"status": "unhealthy"}
        mock_redis_service.health_check.return_value = {"status": "healthy"}
        
        response = await async_client.get("/api/v1/health/readiness")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["status"] == "not_ready"
        assert data["checks"]["database"] is False
        assert data["checks"]["redis"] is True
    
    @pytest.mark.asyncio
    async def test_comprehensive_health_check_all_healthy(
        self, async_client, mock_database_service, mock_redis_service, 
        mock_google_ads_service, test_settings
    ):
        """Test comprehensive health check when all services are healthy."""
        # Mock all services as healthy
        mock_database_service.health_check.return_value = {
            "status": "healthy",
            "authenticated": True,
            "last_check": "2024-01-01T12:00:00Z"
        }
        mock_redis_service.health_check.return_value = {
            "status": "healthy",
            "operation_time_seconds": 0.001,
            "connected_clients": 5,
            "used_memory_human": "1MB",
            "redis_version": "7.0.0"
        }
        mock_google_ads_service.health_check.return_value = {
            "status": "healthy",
            "customer_id": "**********",
            "authenticated": True
        }
        
        response = await async_client.get("/api/v1/health/")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Check overall status
        assert data["status"] == "healthy"
        assert data["version"] == "1.0.0"  # From test settings
        assert data["environment"] == "testing"
        assert "uptime_seconds" in data
        assert isinstance(data["uptime_seconds"], (int, float))
        
        # Check individual service statuses
        assert data["checks"]["database"]["healthy"] is True
        assert data["checks"]["redis"]["healthy"] is True
        assert data["checks"]["google_ads_api"]["healthy"] is True
        assert data["checks"]["openai_api"]["healthy"] is True  # Should be healthy with test key
        assert data["checks"]["pinecone"]["healthy"] is False  # No test config
        
        # Check response times are recorded
        assert "response_time_ms" in data["checks"]["database"]
        assert "response_time_ms" in data["checks"]["redis"]
        assert "response_time_ms" in data["checks"]["google_ads_api"]
    
    @pytest.mark.asyncio
    async def test_comprehensive_health_check_some_unhealthy(
        self, async_client, mock_database_service, mock_redis_service, 
        mock_google_ads_service
    ):
        """Test comprehensive health check when some services are unhealthy."""
        # Mock mixed service health
        mock_database_service.health_check.return_value = {"status": "healthy"}
        mock_redis_service.health_check.return_value = {"status": "unhealthy", "error": "Connection timeout"}
        mock_google_ads_service.health_check.return_value = {"status": "unhealthy", "error": "Invalid credentials"}
        
        response = await async_client.get("/api/v1/health/")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Overall status should be unhealthy
        assert data["status"] == "unhealthy"
        
        # Check individual service statuses
        assert data["checks"]["database"]["healthy"] is True
        assert data["checks"]["redis"]["healthy"] is False
        assert data["checks"]["google_ads_api"]["healthy"] is False
        
        # Check error messages are included
        assert "Connection timeout" in data["checks"]["redis"]["message"] or "Connection timeout" in str(data["checks"]["redis"]["details"])
    
    @pytest.mark.asyncio
    async def test_health_check_service_exception(
        self, async_client, mock_database_service, mock_redis_service
    ):
        """Test health check when service throws exception."""
        # Mock service exception
        mock_database_service.health_check.side_effect = Exception("Database connection failed")
        mock_redis_service.health_check.return_value = {"status": "healthy"}
        
        response = await async_client.get("/api/v1/health/")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Overall status should be unhealthy due to database failure
        assert data["status"] == "unhealthy"
        assert data["checks"]["database"]["healthy"] is False
        assert "Database connection failed" in data["checks"]["database"]["message"]
    
    @pytest.mark.asyncio
    async def test_health_check_response_times(self, async_client):
        """Test that health check records response times."""
        with patch('api.health.check_database') as mock_db_check, \
             patch('api.health.check_redis') as mock_redis_check, \
             patch('api.health.check_google_ads_api') as mock_gads_check, \
             patch('api.health.check_openai_api') as mock_openai_check, \
             patch('api.health.check_pinecone') as mock_pinecone_check:
            
            # Mock service checks with response times
            mock_service_check = MagicMock()
            mock_service_check.dict.return_value = {
                "healthy": True,
                "response_time_ms": 50.0,
                "message": "Service healthy",
                "details": {}
            }
            
            mock_db_check.return_value = mock_service_check
            mock_redis_check.return_value = mock_service_check
            mock_gads_check.return_value = mock_service_check
            mock_openai_check.return_value = mock_service_check
            mock_pinecone_check.return_value = mock_service_check
            
            response = await async_client.get("/api/v1/health/")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            
            # All services should have response times recorded
            for service in ["database", "redis", "google_ads_api", "openai_api", "pinecone"]:
                assert data["checks"][service]["response_time_ms"] == 50.0
    
    @pytest.mark.asyncio
    async def test_health_check_uptime_calculation(self, async_client):
        """Test that uptime is calculated correctly."""
        # Record start time
        start_time = time.time()
        
        response = await async_client.get("/api/v1/health/")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Uptime should be close to the time elapsed
        uptime = data["uptime_seconds"]
        elapsed = time.time() - start_time
        
        # Uptime should be reasonable (allowing for test execution time)
        assert uptime >= 0
        assert uptime <= elapsed + 5  # Allow 5 seconds buffer for test execution
    
    @pytest.mark.asyncio
    async def test_health_check_includes_environment_info(
        self, async_client, test_settings
    ):
        """Test that health check includes environment information."""
        response = await async_client.get("/api/v1/health/")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["version"] == test_settings.VERSION
        assert data["environment"] == test_settings.ENVIRONMENT
        assert "timestamp" in data
    
    @pytest.mark.asyncio
    async def test_health_check_concurrent_execution(self, async_client):
        """Test that health checks are executed concurrently."""
        with patch('api.health.check_database') as mock_db_check, \
             patch('api.health.check_redis') as mock_redis_check:
            
            # Add delays to simulate slow health checks
            async def slow_check():
                import asyncio
                await asyncio.sleep(0.1)
                mock_check = MagicMock()
                mock_check.dict.return_value = {
                    "healthy": True,
                    "response_time_ms": 100.0,
                    "message": "Service healthy",
                    "details": {}
                }
                return mock_check
            
            mock_db_check.side_effect = slow_check
            mock_redis_check.side_effect = slow_check
            
            start_time = time.time()
            response = await async_client.get("/api/v1/health/")
            end_time = time.time()
            
            assert response.status_code == status.HTTP_200_OK
            
            # Total time should be less than sum of individual delays due to concurrency
            total_time = end_time - start_time
            assert total_time < 0.3  # Should be less than 2 * 0.1 + overhead


class TestServiceHealthChecks:
    """Test individual service health check functions."""
    
    @pytest.mark.asyncio
    async def test_database_health_check_success(self, mock_database_service, test_settings):
        """Test database health check with successful connection."""
        from api.health import check_database
        
        mock_database_service.health_check.return_value = {
            "status": "healthy",
            "authenticated": True,
            "last_check": "2024-01-01T12:00:00Z"
        }
        
        result = await check_database()
        
        assert result.healthy is True
        assert result.response_time_ms > 0
        assert "successful" in result.message.lower()
        assert result.details["authenticated"] is True
    
    @pytest.mark.asyncio
    async def test_database_health_check_failure(self, mock_database_service):
        """Test database health check with connection failure."""
        from api.health import check_database
        
        mock_database_service.health_check.side_effect = Exception("Connection refused")
        
        result = await check_database()
        
        assert result.healthy is False
        assert result.response_time_ms > 0
        assert "Connection refused" in result.message
        assert "Connection refused" in result.details["error"]
    
    @pytest.mark.asyncio
    async def test_redis_health_check_success(self, mock_redis_service, test_settings):
        """Test Redis health check with successful connection."""
        from api.health import check_redis
        
        mock_redis_service.health_check.return_value = {
            "status": "healthy",
            "operation_time_seconds": 0.001,
            "connected_clients": 5,
            "used_memory_human": "1MB",
            "redis_version": "7.0.0"
        }
        
        result = await check_redis()
        
        assert result.healthy is True
        assert result.response_time_ms > 0
        assert "successful" in result.message.lower()
        assert result.details["used_memory_human"] == "1MB"
        assert result.details["redis_version"] == "7.0.0"
    
    @pytest.mark.asyncio
    async def test_redis_health_check_failure(self, mock_redis_service):
        """Test Redis health check with connection failure."""
        from api.health import check_redis
        
        mock_redis_service.health_check.side_effect = Exception("Connection timeout")
        
        result = await check_redis()
        
        assert result.healthy is False
        assert result.response_time_ms > 0
        assert "Connection timeout" in result.message
    
    @pytest.mark.asyncio
    async def test_google_ads_health_check_success(self, mock_google_ads_service):
        """Test Google Ads API health check with successful authentication."""
        from api.health import check_google_ads_api
        
        mock_google_ads_service.health_check.return_value = {
            "status": "healthy",
            "customer_id": "**********",
            "authenticated": True,
            "last_check": "2024-01-01T12:00:00Z"
        }
        
        result = await check_google_ads_api()
        
        assert result.healthy is True
        assert result.response_time_ms > 0
        assert "successful" in result.message.lower()
        assert result.details["customer_id"] == "**********"
        assert result.details["authenticated"] is True
    
    @pytest.mark.asyncio
    async def test_google_ads_health_check_failure(self, mock_google_ads_service):
        """Test Google Ads API health check with authentication failure."""
        from api.health import check_google_ads_api
        
        mock_google_ads_service.health_check.side_effect = Exception("Invalid credentials")
        
        result = await check_google_ads_api()
        
        assert result.healthy is False
        assert result.response_time_ms > 0
        assert "Invalid credentials" in result.message
    
    @pytest.mark.asyncio
    async def test_openai_health_check_configured(self, test_settings):
        """Test OpenAI API health check when API key is configured."""
        from api.health import check_openai_api
        
        # test_settings should have OPENAI_API_KEY set to "test-key"
        result = await check_openai_api()
        
        assert result.healthy is True
        assert result.response_time_ms > 0
        assert "configured" in result.message.lower()
        assert result.details["model"] == test_settings.OPENAI_MODEL
    
    @pytest.mark.asyncio
    async def test_openai_health_check_not_configured(self):
        """Test OpenAI API health check when API key is not configured."""
        from api.health import check_openai_api
        
        with patch('api.health.settings') as mock_settings:
            mock_settings.OPENAI_API_KEY = None
            mock_settings.OPENAI_MODEL = "gpt-4"
            
            result = await check_openai_api()
            
            assert result.healthy is False
            assert result.response_time_ms > 0
            assert "not configured" in result.message.lower()
            assert result.details["missing_api_key"] is True
    
    @pytest.mark.asyncio
    async def test_pinecone_health_check_configured(self):
        """Test Pinecone health check when credentials are configured."""
        from api.health import check_pinecone
        
        with patch('api.health.settings') as mock_settings:
            mock_settings.PINECONE_API_KEY = "test-api-key"
            mock_settings.PINECONE_ENVIRONMENT = "test-env"
            mock_settings.PINECONE_INDEX_NAME = "test-index"
            
            result = await check_pinecone()
            
            assert result.healthy is True
            assert result.response_time_ms > 0
            assert "configured" in result.message.lower()
            assert result.details["environment"] == "test-env"
            assert result.details["index_name"] == "test-index"
    
    @pytest.mark.asyncio
    async def test_pinecone_health_check_not_configured(self):
        """Test Pinecone health check when credentials are not configured."""
        from api.health import check_pinecone
        
        with patch('api.health.settings') as mock_settings:
            mock_settings.PINECONE_API_KEY = None
            mock_settings.PINECONE_ENVIRONMENT = None
            mock_settings.PINECONE_INDEX_NAME = "test-index"
            
            result = await check_pinecone()
            
            assert result.healthy is False
            assert result.response_time_ms > 0
            assert "not configured" in result.message.lower()
            assert result.details["missing_credentials"] is True


class TestHealthCheckModels:
    """Test health check response models."""
    
    def test_service_check_model(self):
        """Test ServiceCheck model validation."""
        from api.health import ServiceCheck
        
        # Valid service check
        check = ServiceCheck(
            healthy=True,
            response_time_ms=25.5,
            message="Service is healthy",
            details={"version": "1.0.0"}
        )
        
        assert check.healthy is True
        assert check.response_time_ms == 25.5
        assert check.message == "Service is healthy"
        assert check.details == {"version": "1.0.0"}
    
    def test_health_status_model(self):
        """Test HealthStatus model validation."""
        from api.health import HealthStatus
        from datetime import datetime
        
        # Valid health status
        status_obj = HealthStatus(
            status="healthy",
            timestamp=datetime.utcnow(),
            version="1.0.0",
            environment="testing",
            uptime_seconds=123.45,
            checks={
                "database": {
                    "healthy": True,
                    "response_time_ms": 10.0,
                    "message": "Database healthy",
                    "details": {}
                }
            }
        )
        
        assert status_obj.status == "healthy"
        assert status_obj.version == "1.0.0"
        assert status_obj.environment == "testing"
        assert status_obj.uptime_seconds == 123.45
        assert "database" in status_obj.checks


class TestHealthCheckLogging:
    """Test health check logging and monitoring."""
    
    @pytest.mark.asyncio
    async def test_health_check_logging(self, async_client):
        """Test that health checks are properly logged."""
        with patch('api.health.logger') as mock_logger:
            response = await async_client.get("/api/v1/health/")
            
            assert response.status_code == status.HTTP_200_OK
            
            # Should log health check request and completion
            mock_logger.info.assert_any_call("Health check requested")
            
            # Should log completion with status
            completion_calls = [call for call in mock_logger.info.call_args_list 
                              if "Health check completed" in str(call)]
            assert len(completion_calls) > 0
    
    @pytest.mark.asyncio
    async def test_service_error_logging(self, async_client, mock_database_service):
        """Test that service errors are properly logged."""
        mock_database_service.health_check.side_effect = Exception("Database error")
        
        with patch('api.health.logger') as mock_logger:
            response = await async_client.get("/api/v1/health/")
            
            assert response.status_code == status.HTTP_200_OK
            
            # Should log database health check failure
            error_calls = [call for call in mock_logger.error.call_args_list 
                          if "Database health check failed" in str(call)]
            assert len(error_calls) > 0