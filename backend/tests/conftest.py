"""
Pytest configuration and shared fixtures.
Contains test fixtures for database, services, and mock data.
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta
from typing import AsyncGenerator, Dict, Any, List
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
# import faker  # Not available in current environment

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from main import create_app
from utils.config import Settings
from models.campaigns import (
    Campaign, CampaignCreate, CampaignType, CampaignStatus, 
    BiddingStrategy, Language, Currency
)
from models.common import BaseEntity
from services.base import BaseService, service_manager
from services.google_ads import GoogleAdsService
from services.openai_service import OpenAIService
from services.database import DatabaseService
from services.redis_service import RedisService


# Test configuration
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_settings() -> Settings:
    """Test application settings with overrides for testing."""
    test_env_vars = {
        "ENVIRONMENT": "testing",
        "DEBUG": "true",
        "DATABASE_URL": "sqlite:///./test.db",
        "REDIS_URL": "redis://localhost:6379/1",  # Use different DB for tests
        "OPENAI_API_KEY": "test-key",
        "GOOGLE_ADS_DEVELOPER_TOKEN": "test-token",
        "GOOGLE_ADS_CLIENT_ID": "test-client-id",
        "GOOGLE_ADS_CLIENT_SECRET": "test-secret",
        "GOOGLE_ADS_REFRESH_TOKEN": "test-refresh",
        "GOOGLE_ADS_CUSTOMER_ID": "1234567890",
        "SECRET_KEY": "test-secret-key-for-testing-only",
    }
    
    with patch.dict(os.environ, test_env_vars):
        return Settings()


@pytest.fixture(scope="session")
def app(test_settings):
    """Create FastAPI test application."""
    with patch('utils.config.settings', test_settings):
        app = create_app()
        return app


@pytest.fixture
def client(app):
    """Create FastAPI test client."""
    return TestClient(app)


@pytest.fixture
async def async_client(app) -> AsyncGenerator[AsyncClient, None]:
    """Create async HTTP client for testing."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
def fake():
    """Simple fake data generator since faker is not available."""
    class SimpleFake:
        def uuid4(self):
            import uuid
            return str(uuid.uuid4())
        
        def sentence(self, nb_words=4):
            words = ["test", "sample", "data", "value", "content", "text", "example"]
            import random
            return " ".join(random.choices(words, k=nb_words))
        
        def text(self, max_nb_chars=200):
            return "Sample text content for testing purposes." * (max_nb_chars // 40 + 1)
        
        def pyfloat(self, left_digits=2, right_digits=2, positive=True):
            import random
            value = random.uniform(10 ** (left_digits - 1), 10 ** left_digits)
            return round(value, right_digits) if positive else round(-value, right_digits)
        
        def country(self):
            countries = ["United States", "Canada", "United Kingdom", "Germany", "France"]
            import random
            return random.choice(countries)
        
        def date_time_between(self, start_date=None, end_date=None):
            from datetime import datetime, timedelta
            import random
            base = datetime.utcnow()
            if isinstance(start_date, str):
                if start_date.startswith('-'):
                    days = int(start_date[:-1])
                    return base - timedelta(days=days)
                elif start_date.startswith('+'):
                    days = int(start_date[1:-1])
                    return base + timedelta(days=days)
            return base
        
        def boolean(self):
            import random
            return random.choice([True, False])
        
        def word(self):
            words = ["keyword", "search", "term", "phrase", "word"]
            import random
            return random.choice(words)
        
        def random_element(self, elements):
            import random
            return random.choice(list(elements))
    
    return SimpleFake()


# Mock services
@pytest.fixture
def mock_redis_service():
    """Mock Redis service."""
    mock_service = AsyncMock(spec=RedisService)
    mock_service.authenticate.return_value = None
    mock_service.health_check.return_value = {"status": "healthy", "connected": True}
    mock_service.get.return_value = None
    mock_service.set.return_value = True
    mock_service.delete.return_value = True
    mock_service.exists.return_value = False
    return mock_service


@pytest.fixture
def mock_database_service():
    """Mock database service."""
    mock_service = AsyncMock(spec=DatabaseService)
    mock_service.health_check.return_value = {"status": "healthy", "connected": True}
    mock_service.create_campaign.return_value = {"id": "test-campaign-id"}
    mock_service.get_campaign.return_value = None
    mock_service.update_campaign.return_value = {"id": "test-campaign-id", "updated": True}
    mock_service.delete_campaign.return_value = True
    mock_service.list_campaigns.return_value = {"campaigns": [], "total": 0}
    return mock_service


@pytest.fixture
def mock_google_ads_service():
    """Mock Google Ads service."""
    mock_service = AsyncMock(spec=GoogleAdsService)
    mock_service.authenticate.return_value = None
    mock_service.health_check.return_value = {"status": "healthy", "connected": True}
    mock_service.create_campaign.return_value = {"id": "12345678", "status": "ACTIVE"}
    mock_service.get_campaign.return_value = {"id": "12345678", "name": "Test Campaign"}
    mock_service.update_campaign.return_value = {"id": "12345678", "updated": True}
    mock_service.get_campaign_performance.return_value = {
        "impressions": 1000,
        "clicks": 50,
        "cost": 25.00,
        "conversions": 5
    }
    return mock_service


@pytest.fixture
def mock_openai_service():
    """Mock OpenAI service."""
    mock_service = AsyncMock(spec=OpenAIService)
    mock_service.authenticate.return_value = None
    mock_service.health_check.return_value = {"status": "healthy", "connected": True}
    mock_service.generate_ad_copy.return_value = {
        "headlines": ["Great Product!", "Buy Now!", "Limited Time!"],
        "descriptions": ["The best product you'll ever need.", "Don't miss out!"]
    }
    mock_service.analyze_performance.return_value = {
        "insights": ["Increase bid on high-performing keywords"],
        "recommendations": ["Add negative keywords to reduce waste"]
    }
    return mock_service


# Test data factories
@pytest.fixture
def sample_campaign_data(fake) -> Dict[str, Any]:
    """Generate sample campaign data."""
    return {
        "name": fake.sentence(nb_words=3).replace('.', ''),
        "description": fake.text(max_nb_chars=200),
        "type": CampaignType.SEARCH,
        "budget_amount": fake.pyfloat(left_digits=3, right_digits=2, positive=True),
        "bidding_strategy": BiddingStrategy.TARGET_CPA,
        "target_locations": [fake.country(), fake.country()],
        "target_languages": [Language.ENGLISH, Language.SPANISH],
        "keywords": [fake.word() for _ in range(5)],
        "start_date": fake.date_time_between(start_date="-1d", end_date="+30d"),
        "end_date": fake.date_time_between(start_date="+30d", end_date="+60d"),
        "auto_optimization_enabled": fake.boolean()
    }


@pytest.fixture
def sample_campaign(sample_campaign_data) -> Campaign:
    """Generate sample campaign model."""
    return Campaign(
        id=fake.Faker().uuid4(),
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        **sample_campaign_data
    )


@pytest.fixture
def sample_campaign_create(sample_campaign_data) -> CampaignCreate:
    """Generate sample campaign creation data."""
    # Remove fields that are not in CampaignCreate
    create_data = sample_campaign_data.copy()
    return CampaignCreate(**create_data)


@pytest.fixture
def multiple_campaigns(fake) -> List[Campaign]:
    """Generate multiple sample campaigns."""
    campaigns = []
    for _ in range(3):
        campaigns.append(Campaign(
            id=fake.uuid4(),
            name=fake.sentence(nb_words=3).replace('.', ''),
            description=fake.text(max_nb_chars=200),
            type=fake.random_element(elements=list(CampaignType)),
            status=fake.random_element(elements=list(CampaignStatus)),
            budget_amount=fake.pyfloat(left_digits=3, right_digits=2, positive=True),
            bidding_strategy=fake.random_element(elements=list(BiddingStrategy)),
            target_locations=[fake.country()],
            target_languages=[Language.ENGLISH],
            keywords=[fake.word() for _ in range(3)],
            created_at=fake.date_time_between(start_date="-30d", end_date="now"),
            updated_at=fake.date_time_between(start_date="-5d", end_date="now"),
        ))
    return campaigns


# Mock external API responses
@pytest.fixture
def mock_google_ads_responses():
    """Mock responses from Google Ads API."""
    return {
        "create_campaign": {
            "results": [{"resourceName": "customers/1234567890/campaigns/12345678"}]
        },
        "get_campaign": {
            "results": [{
                "campaign": {
                    "resourceName": "customers/1234567890/campaigns/12345678",
                    "name": "Test Campaign",
                    "status": "ACTIVE"
                }
            }]
        },
        "get_performance": {
            "results": [{
                "metrics": {
                    "impressions": "1000",
                    "clicks": "50",
                    "costMicros": "25000000",
                    "conversions": "5.0"
                }
            }]
        }
    }


@pytest.fixture
def mock_openai_responses():
    """Mock responses from OpenAI API."""
    return {
        "chat_completion": {
            "id": "chatcmpl-test",
            "object": "chat.completion",
            "choices": [{
                "message": {
                    "role": "assistant",
                    "content": '{"headlines": ["Great Product!", "Buy Now!"], "descriptions": ["The best product ever."]}'
                },
                "finish_reason": "stop"
            }],
            "usage": {"total_tokens": 50}
        }
    }


# Database fixtures
@pytest.fixture
async def clean_database(mock_database_service):
    """Ensure clean database state for tests."""
    # Setup: Clean any existing test data
    yield mock_database_service
    # Teardown: Clean up after test


# Authentication fixtures  
@pytest.fixture
def mock_auth_headers():
    """Mock authentication headers."""
    return {
        "Authorization": "Bearer test-token",
        "X-User-ID": "test-user-id",
    }


@pytest.fixture
def authenticated_client(client, mock_auth_headers):
    """Client with authentication headers."""
    client.headers.update(mock_auth_headers)
    return client


# Service manager fixtures
@pytest.fixture
def mock_service_manager(
    mock_redis_service,
    mock_database_service, 
    mock_google_ads_service,
    mock_openai_service
):
    """Mock service manager with all services."""
    manager = MagicMock()
    manager.get_service.side_effect = lambda name: {
        "redis": mock_redis_service,
        "database": mock_database_service,
        "google_ads": mock_google_ads_service,
        "openai": mock_openai_service
    }.get(name)
    return manager


# Patch fixtures for global mocks
@pytest.fixture(autouse=True)
def mock_services(
    mock_service_manager,
    mock_redis_service,
    mock_database_service,
    mock_google_ads_service,
    mock_openai_service
):
    """Auto-use fixture to mock all services globally."""
    with patch('services.service_manager', mock_service_manager), \
         patch('services.redis_service', mock_redis_service), \
         patch('services.database_service', mock_database_service), \
         patch('services.google_ads_service', mock_google_ads_service), \
         patch('services.openai_service', mock_openai_service):
        yield


# Error simulation fixtures
@pytest.fixture
def simulate_service_error():
    """Fixture to simulate service errors."""
    def _simulate_error(service_mock, method_name, exception):
        getattr(service_mock, method_name).side_effect = exception
    return _simulate_error


@pytest.fixture
def simulate_network_error():
    """Simulate network/connectivity errors."""
    from utils.exceptions import ExternalServiceException
    return ExternalServiceException("google_ads", "Network connection failed")


@pytest.fixture
def simulate_rate_limit_error():
    """Simulate rate limiting errors."""
    from utils.exceptions import RateLimitException
    return RateLimitException("Rate limit exceeded", details={"limit": 100})


# Performance testing fixtures
@pytest.fixture
def performance_timer():
    """Timer for performance tests."""
    import time
    start_time = time.time()
    yield lambda: time.time() - start_time


# Cleanup fixtures
@pytest.fixture(autouse=True)
def cleanup_after_test():
    """Automatic cleanup after each test."""
    yield
    # Cleanup code would go here
    pass


# Async test helpers
@pytest_asyncio.fixture
async def async_mock_context():
    """Async context manager for mocking."""
    async with AsyncMock() as mock:
        yield mock


# Configuration overrides for specific test scenarios
@pytest.fixture
def production_settings(test_settings):
    """Settings for production-like testing."""
    test_settings.ENVIRONMENT = "production"
    test_settings.DEBUG = False
    return test_settings


@pytest.fixture
def development_settings(test_settings):
    """Settings for development testing.""" 
    test_settings.ENVIRONMENT = "development"
    test_settings.DEBUG = True
    return test_settings