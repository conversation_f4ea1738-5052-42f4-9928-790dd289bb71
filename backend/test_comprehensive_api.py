#!/usr/bin/env python3
"""
Comprehensive API Testing Suite for Google Ads AI Agent System - Phase 2

This suite provides exhaustive testing of all API endpoints, including:
1. FastAPI server startup and health checks
2. Campaign management CRUD operations
3. Agent lifecycle and task management
4. Google Ads integration endpoints
5. Analytics and reporting functionality
6. Error handling and edge cases
7. Integration workflows
"""

import asyncio
import json
import sys
import time
import uuid
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from unittest.mock import AsyncMock, patch

import httpx
import pytest
import structlog
from fastapi.testclient import TestClient

# Local imports
from main import create_app
from services.database import DatabaseService
from utils.config import settings

# Configure logging for tests
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.dev.Console<PERSON><PERSON>er(colors=True)
    ],
    wrapper_class=structlog.stdlib.BoundLogger,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Test configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"
TEST_TIMEOUT = 30.0


class TestResult:
    """Individual test result container."""
    
    def __init__(self, name: str, endpoint: str, method: str):
        self.name = name
        self.endpoint = endpoint
        self.method = method
        self.success = False
        self.status_code = None
        self.expected_status = None
        self.response_time = None
        self.error = None
        self.response_data = None
        
    def mark_success(self, status_code: int, expected_status: int, response_time: float, response_data: Any = None):
        self.success = True
        self.status_code = status_code
        self.expected_status = expected_status
        self.response_time = response_time
        self.response_data = response_data
        
    def mark_failure(self, status_code: int, expected_status: int, response_time: float, error: str = None):
        self.success = False
        self.status_code = status_code
        self.expected_status = expected_status
        self.response_time = response_time
        self.error = error


class ComprehensiveAPITester:
    """Comprehensive API testing framework."""
    
    def __init__(self, base_url: str = API_BASE):
        self.base_url = base_url
        self.client = None
        self.test_client = None
        self.results: List[TestResult] = []
        self.test_data = {}  # Store created test data for cleanup
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.client = httpx.AsyncClient(timeout=TEST_TIMEOUT)
        # Also create FastAPI test client for internal testing
        app = create_app()
        self.test_client = TestClient(app)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with cleanup."""
        await self.cleanup_test_data()
        if self.client:
            await self.client.aclose()
            
    async def cleanup_test_data(self):
        """Clean up test data created during testing."""
        logger.info("🧹 Cleaning up test data")
        
        # Clean up campaigns
        if 'created_campaigns' in self.test_data:
            for campaign_id in self.test_data['created_campaigns']:
                try:
                    await self.client.delete(f"{self.base_url}/campaigns/{campaign_id}")
                except Exception as e:
                    logger.warning(f"Failed to delete campaign {campaign_id}", error=str(e))
                    
        # Clean up agents
        if 'created_agents' in self.test_data:
            for agent_id in self.test_data['created_agents']:
                try:
                    await self.client.delete(f"{self.base_url}/agents/{agent_id}")
                except Exception as e:
                    logger.warning(f"Failed to delete agent {agent_id}", error=str(e))
    
    async def execute_test(self, name: str, method: str, endpoint: str, 
                          expected_status: int = 200, data: Dict[str, Any] = None,
                          headers: Dict[str, str] = None) -> TestResult:
        """Execute a single API test."""
        result = TestResult(name, endpoint, method)
        url = f"{self.base_url}{endpoint}"
        
        try:
            start_time = time.time()
            
            # Execute HTTP request
            if method.upper() == "GET":
                response = await self.client.get(url, headers=headers)
            elif method.upper() == "POST":
                response = await self.client.post(url, json=data, headers=headers)
            elif method.upper() == "PUT":
                response = await self.client.put(url, json=data, headers=headers)
            elif method.upper() == "PATCH":
                response = await self.client.patch(url, json=data, headers=headers)
            elif method.upper() == "DELETE":
                response = await self.client.delete(url, headers=headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
            response_time = time.time() - start_time
            
            # Parse response
            response_data = None
            try:
                response_data = response.json() if response.content else {}
            except json.JSONDecodeError:
                response_data = {"raw_response": response.text}
            
            # Evaluate result
            if response.status_code == expected_status:
                result.mark_success(response.status_code, expected_status, response_time, response_data)
                logger.info(f"✅ {name}", status=response.status_code, time=f"{response_time:.3f}s")
            else:
                result.mark_failure(response.status_code, expected_status, response_time, 
                                  f"Status mismatch: expected {expected_status}, got {response.status_code}")
                logger.error(f"❌ {name}", expected=expected_status, actual=response.status_code)
                if response_data and 'detail' in response_data:
                    logger.error("Error details", detail=response_data['detail'])
            
        except Exception as e:
            response_time = time.time() - start_time if 'start_time' in locals() else 0
            result.mark_failure(None, expected_status, response_time, str(e))
            logger.error(f"❌ {name} - Exception", error=str(e))
        
        self.results.append(result)
        return result

    async def test_server_startup(self):
        """Test FastAPI server startup and basic configuration."""
        logger.info("🚀 Testing Server Startup and Configuration")
        
        # Test root endpoint
        await self.execute_test(
            "Root endpoint", "GET", "/", 200
        )
        
        # Test API documentation endpoints
        await self.execute_test(
            "OpenAPI schema", "GET", "/openapi.json", 200
        )
        
        await self.execute_test(
            "API documentation", "GET", "/docs", 200
        )
        
        # Test CORS headers (this would need a browser request to fully test)
        result = await self.execute_test(
            "CORS preflight", "OPTIONS", "/api/v1/health", 200
        )

    async def test_health_endpoints(self):
        """Test all health check endpoints."""
        logger.info("🔍 Testing Health Check Endpoints")
        
        # Comprehensive health check
        result = await self.execute_test(
            "Comprehensive health check", "GET", "/health", 200
        )
        
        if result.success and result.response_data:
            health_data = result.response_data.get("data", {})
            logger.info("Health check components", 
                       database=health_data.get("database", "unknown"),
                       google_ads=health_data.get("google_ads_api", "unknown"),
                       redis=health_data.get("redis", "unknown"))
        
        # Liveness probe
        await self.execute_test(
            "Liveness probe", "GET", "/health/liveness", 200
        )
        
        # Readiness probe
        await self.execute_test(
            "Readiness probe", "GET", "/health/readiness", 200
        )
        
        # Metrics endpoint
        await self.execute_test(
            "System metrics", "GET", "/health/metrics", 200
        )

    async def test_campaign_management(self):
        """Test complete campaign management workflow."""
        logger.info("🎯 Testing Campaign Management Endpoints")
        
        # Initialize test data storage
        if 'created_campaigns' not in self.test_data:
            self.test_data['created_campaigns'] = []
        
        # 1. List campaigns (should work with empty database)
        await self.execute_test(
            "List campaigns (empty)", "GET", "/campaigns", 200
        )
        
        # 2. List campaigns with pagination
        await self.execute_test(
            "List campaigns with pagination", "GET", "/campaigns?page=1&page_size=10", 200
        )
        
        # 3. Create a test campaign
        campaign_data = {
            "name": f"Test Campaign {datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "description": "Comprehensive API test campaign",
            "type": "search",
            "budget_amount": 100.0,
            "budget_type": "daily",
            "bidding_strategy": "manual_cpc",
            "target_locations": ["United States"],
            "target_languages": ["en"],
            "keywords": ["test keyword", "api integration", "automated testing"],
            "auto_optimization_enabled": True,
            "optimization_goals": ["maximize_clicks", "improve_quality_score"]
        }
        
        create_result = await self.execute_test(
            "Create campaign", "POST", "/campaigns", 201, data=campaign_data
        )
        
        campaign_id = None
        if create_result.success and create_result.response_data:
            created_campaign = create_result.response_data.get("data", {})
            campaign_id = created_campaign.get("id")
            if campaign_id:
                self.test_data['created_campaigns'].append(campaign_id)
                logger.info(f"Created test campaign", campaign_id=campaign_id)
        
        if campaign_id:
            # 4. Get campaign by ID
            await self.execute_test(
                "Get campaign by ID", "GET", f"/campaigns/{campaign_id}", 200
            )
            
            # 5. Update campaign
            update_data = {
                "description": "Updated comprehensive test campaign",
                "budget_amount": 150.0,
                "keywords": ["test keyword", "api integration", "automated testing", "updated"]
            }
            
            await self.execute_test(
                "Update campaign", "PUT", f"/campaigns/{campaign_id}", 200, data=update_data
            )
            
            # 6. Get campaign metrics
            await self.execute_test(
                "Get campaign metrics", "GET", f"/campaigns/{campaign_id}/metrics", 200
            )
            
            # 7. Get campaign performance data with date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            await self.execute_test(
                "Get campaign performance (30 days)", "GET", 
                f"/campaigns/{campaign_id}/metrics?start_date={start_date.isoformat()}&end_date={end_date.isoformat()}",
                200
            )
            
            # 8. Trigger campaign optimization
            await self.execute_test(
                "Trigger campaign optimization", "POST", f"/campaigns/{campaign_id}/optimize", 200
            )
            
            # 9. Get optimization history
            await self.execute_test(
                "Get optimization history", "GET", f"/campaigns/{campaign_id}/optimization-history", 200
            )
            
            # 10. Pause campaign
            await self.execute_test(
                "Pause campaign", "POST", f"/campaigns/{campaign_id}/pause", 200
            )
            
            # 11. Resume campaign
            await self.execute_test(
                "Resume campaign", "POST", f"/campaigns/{campaign_id}/resume", 200
            )
        
        # 12. Test campaign filtering
        await self.execute_test(
            "Filter campaigns by status", "GET", "/campaigns?status=active", 200
        )
        
        await self.execute_test(
            "Filter campaigns by type", "GET", "/campaigns?type=search", 200
        )
        
        # 13. Test invalid campaign ID (404 error)
        await self.execute_test(
            "Get non-existent campaign", "GET", "/campaigns/99999", 404
        )
        
        # 14. Test invalid campaign data (422 validation error)
        invalid_data = {
            "name": "",  # Invalid: empty name
            "type": "invalid_type",  # Invalid type
            "budget_amount": -10  # Invalid: negative budget
        }
        
        await self.execute_test(
            "Create campaign with invalid data", "POST", "/campaigns", 422, data=invalid_data
        )

    async def test_agent_management(self):
        """Test agent lifecycle and task management."""
        logger.info("🤖 Testing Agent Management Endpoints")
        
        # Initialize test data storage
        if 'created_agents' not in self.test_data:
            self.test_data['created_agents'] = []
        
        # 1. List agents
        await self.execute_test(
            "List agents", "GET", "/agents", 200
        )
        
        # 2. Create a test agent
        agent_data = {
            "name": f"Test Agent {datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "description": "Comprehensive API test agent",
            "type": "campaign_planning",
            "config": {
                "model": {
                    "provider": "openai",
                    "model_name": "gpt-4",
                    "temperature": 0.7,
                    "max_tokens": 2000
                },
                "max_iterations": 10,
                "timeout_seconds": 300,
                "retry_attempts": 3,
                "verbose": False,
                "tools": ["keyword_research", "budget_optimization"]
            }
        }
        
        create_result = await self.execute_test(
            "Create agent", "POST", "/agents", 201, data=agent_data
        )
        
        agent_id = None
        if create_result.success and create_result.response_data:
            created_agent = create_result.response_data.get("data", {})
            agent_id = created_agent.get("id")
            if agent_id:
                self.test_data['created_agents'].append(agent_id)
                logger.info(f"Created test agent", agent_id=agent_id)
        
        if agent_id:
            # 3. Get agent by ID
            await self.execute_test(
                "Get agent by ID", "GET", f"/agents/{agent_id}", 200
            )
            
            # 4. Update agent status
            await self.execute_test(
                "Update agent status to active", "PUT", f"/agents/{agent_id}/status", 200,
                data={"status": "active"}
            )
            
            # 5. Get agent tasks
            await self.execute_test(
                "Get agent tasks", "GET", f"/agents/{agent_id}/tasks", 200
            )
            
            # 6. Create agent task
            task_data = {
                "type": "campaign_analysis",
                "priority": "high",
                "config": {
                    "campaign_id": self.test_data.get('created_campaigns', [None])[0],
                    "analysis_type": "performance_review",
                    "include_recommendations": True
                },
                "scheduled_at": (datetime.now() + timedelta(minutes=5)).isoformat()
            }
            
            await self.execute_test(
                "Create agent task", "POST", f"/agents/{agent_id}/tasks", 201, data=task_data
            )
            
            # 7. Get agent metrics
            await self.execute_test(
                "Get agent metrics", "GET", f"/agents/{agent_id}/metrics", 200
            )
            
            # 8. Get agent execution history
            await self.execute_test(
                "Get agent execution history", "GET", f"/agents/{agent_id}/executions", 200
            )
        
        # 9. List agents with filtering
        await self.execute_test(
            "Filter agents by type", "GET", "/agents?type=campaign_planning", 200
        )
        
        await self.execute_test(
            "Filter agents by status", "GET", "/agents?status=active", 200
        )
        
        # 10. Test invalid agent data
        invalid_agent_data = {
            "name": "",  # Invalid: empty name
            "type": "invalid_type",  # Invalid type
            "config": "invalid_config"  # Invalid: should be dict
        }
        
        await self.execute_test(
            "Create agent with invalid data", "POST", "/agents", 422, data=invalid_agent_data
        )

    async def test_google_ads_integration(self):
        """Test Google Ads API integration endpoints."""
        logger.info("📊 Testing Google Ads Integration Endpoints")
        
        # 1. Test Google Ads API health check
        await self.execute_test(
            "Google Ads API health check", "GET", "/google-ads/health", 200
        )
        
        # 2. Test campaign synchronization (mocked)
        with patch('services.google_ads.GoogleAdsService.sync_campaigns') as mock_sync:
            mock_sync.return_value = {"synced_campaigns": 5, "status": "success"}
            
            await self.execute_test(
                "Sync campaigns from Google Ads", "POST", "/google-ads/sync/campaigns", 200
            )
        
        # 3. Test account information retrieval (mocked)
        with patch('services.google_ads.GoogleAdsService.get_account_info') as mock_account:
            mock_account.return_value = {
                "customer_id": "************",
                "name": "Test Account",
                "currency": "USD",
                "time_zone": "America/New_York"
            }
            
            await self.execute_test(
                "Get Google Ads account info", "GET", "/google-ads/account", 200
            )
        
        # 4. Test performance data retrieval (mocked)
        if self.test_data.get('created_campaigns'):
            campaign_id = self.test_data['created_campaigns'][0]
            
            with patch('services.google_ads.GoogleAdsService.get_campaign_performance') as mock_perf:
                mock_perf.return_value = {
                    "impressions": 1000,
                    "clicks": 50,
                    "cost": 25.50,
                    "conversions": 5
                }
                
                await self.execute_test(
                    "Get Google Ads performance data", "GET", 
                    f"/campaigns/{campaign_id}/google-ads/performance", 200
                )
        
        # 5. Test keywords sync (mocked)
        with patch('services.google_ads.GoogleAdsService.sync_keywords') as mock_keywords:
            mock_keywords.return_value = {"synced_keywords": 25, "status": "success"}
            
            await self.execute_test(
                "Sync keywords from Google Ads", "POST", "/google-ads/sync/keywords", 200
            )
        
        # 6. Test ad groups sync (mocked)
        with patch('services.google_ads.GoogleAdsService.sync_ad_groups') as mock_ad_groups:
            mock_ad_groups.return_value = {"synced_ad_groups": 10, "status": "success"}
            
            await self.execute_test(
                "Sync ad groups from Google Ads", "POST", "/google-ads/sync/ad-groups", 200
            )

    async def test_analytics_endpoints(self):
        """Test analytics and reporting functionality."""
        logger.info("📈 Testing Analytics Endpoints")
        
        # 1. Get dashboard data
        await self.execute_test(
            "Get dashboard data", "GET", "/analytics/dashboard", 200
        )
        
        # 2. Get campaign performance report
        await self.execute_test(
            "Campaign performance report", "GET", "/analytics/reports/campaign_performance", 200
        )
        
        # 3. Get agent performance report
        await self.execute_test(
            "Agent performance report", "GET", "/analytics/reports/agent_performance", 200
        )
        
        # 4. Get optimization insights
        await self.execute_test(
            "Optimization insights", "GET", "/analytics/insights/optimization", 200
        )
        
        # 5. Campaign-specific insights
        if self.test_data.get('created_campaigns'):
            campaign_id = self.test_data['created_campaigns'][0]
            
            await self.execute_test(
                "Campaign insights", "GET", f"/analytics/campaigns/{campaign_id}/insights", 200
            )
            
            # Time-series data
            await self.execute_test(
                "Campaign time-series data", "GET", 
                f"/analytics/campaigns/{campaign_id}/time-series?metric=clicks&period=7d", 200
            )
        
        # 6. Budget analysis
        await self.execute_test(
            "Budget analysis", "GET", "/analytics/budget-analysis", 200
        )
        
        # 7. Keyword performance
        await self.execute_test(
            "Keyword performance", "GET", "/analytics/keywords/performance", 200
        )
        
        # 8. Custom report generation
        report_config = {
            "report_type": "custom",
            "metrics": ["impressions", "clicks", "cost", "conversions"],
            "dimensions": ["campaign_name", "date"],
            "date_range": {
                "start_date": (datetime.now() - timedelta(days=30)).isoformat(),
                "end_date": datetime.now().isoformat()
            }
        }
        
        await self.execute_test(
            "Generate custom report", "POST", "/analytics/reports/custom", 200,
            data=report_config
        )

    async def test_error_handling(self):
        """Test error handling scenarios."""
        logger.info("🚨 Testing Error Handling Scenarios")
        
        # 1. Test 404 errors
        await self.execute_test(
            "Non-existent endpoint", "GET", "/non-existent-endpoint", 404
        )
        
        await self.execute_test(
            "Non-existent campaign", "GET", "/campaigns/99999", 404
        )
        
        await self.execute_test(
            "Non-existent agent", "GET", "/agents/99999", 404
        )
        
        # 2. Test 422 validation errors
        invalid_campaign = {
            "name": "",  # Required field empty
            "budget_amount": "invalid_number",  # Invalid data type
            "type": "invalid_type"  # Invalid enum value
        }
        
        await self.execute_test(
            "Invalid campaign data validation", "POST", "/campaigns", 422,
            data=invalid_campaign
        )
        
        # 3. Test 405 method not allowed
        await self.execute_test(
            "Method not allowed", "POST", "/health/liveness", 405
        )
        
        # 4. Test malformed JSON
        try:
            response = await self.client.post(
                f"{self.base_url}/campaigns",
                content="invalid json content",
                headers={"Content-Type": "application/json"}
            )
            # Should get 422 for malformed JSON
            result = TestResult("Malformed JSON handling", "/campaigns", "POST")
            if response.status_code in [400, 422]:
                result.mark_success(response.status_code, 422, 0)
                logger.info("✅ Malformed JSON handling", status=response.status_code)
            else:
                result.mark_failure(response.status_code, 422, 0, "Unexpected status for malformed JSON")
                logger.error("❌ Malformed JSON handling", expected=422, actual=response.status_code)
            self.results.append(result)
        except Exception as e:
            result = TestResult("Malformed JSON handling", "/campaigns", "POST")
            result.mark_failure(None, 422, 0, str(e))
            self.results.append(result)
            logger.error("❌ Malformed JSON handling - Exception", error=str(e))

    async def test_integration_workflows(self):
        """Test complete integration workflows."""
        logger.info("🔄 Testing Integration Workflows")
        
        # Workflow 1: Create Campaign → Create Agent → Assign Task → Get Metrics
        if self.test_data.get('created_campaigns') and self.test_data.get('created_agents'):
            campaign_id = self.test_data['created_campaigns'][0]
            agent_id = self.test_data['created_agents'][0]
            
            # Create optimization task for the campaign
            task_data = {
                "type": "campaign_optimization",
                "priority": "medium",
                "config": {
                    "campaign_id": campaign_id,
                    "optimization_type": "bid_adjustment",
                    "target_cpa": 25.0
                }
            }
            
            task_result = await self.execute_test(
                "Workflow: Assign optimization task", "POST", 
                f"/agents/{agent_id}/tasks", 201, data=task_data
            )
            
            # Get updated metrics after task assignment
            await self.execute_test(
                "Workflow: Get campaign metrics after task", "GET", 
                f"/campaigns/{campaign_id}/metrics", 200
            )
            
            await self.execute_test(
                "Workflow: Get agent tasks after assignment", "GET", 
                f"/agents/{agent_id}/tasks", 200
            )
        
        # Workflow 2: Bulk Operations
        # Create multiple campaigns and test bulk operations
        bulk_campaigns = []
        for i in range(3):
            campaign_data = {
                "name": f"Bulk Test Campaign {i+1} {datetime.now().strftime('%H%M%S')}",
                "description": f"Bulk operation test campaign {i+1}",
                "type": "search",
                "budget_amount": 50.0 * (i+1),
                "bidding_strategy": "manual_cpc",
                "target_locations": ["United States"],
                "target_languages": ["en"],
                "keywords": [f"bulk test {i+1}"]
            }
            
            result = await self.execute_test(
                f"Workflow: Create bulk campaign {i+1}", "POST", 
                "/campaigns", 201, data=campaign_data
            )
            
            if result.success and result.response_data:
                campaign_id = result.response_data.get("data", {}).get("id")
                if campaign_id:
                    bulk_campaigns.append(campaign_id)
                    self.test_data.setdefault('created_campaigns', []).append(campaign_id)
        
        # Test batch operations on bulk campaigns
        if bulk_campaigns:
            # Pause all bulk campaigns
            for campaign_id in bulk_campaigns:
                await self.execute_test(
                    f"Workflow: Pause bulk campaign {campaign_id}", "POST", 
                    f"/campaigns/{campaign_id}/pause", 200
                )
        
        # Workflow 3: Data consistency checks
        # Verify that created campaigns appear in listings
        list_result = await self.execute_test(
            "Workflow: Verify created campaigns in listing", "GET", 
            "/campaigns", 200
        )
        
        if list_result.success and list_result.response_data:
            campaigns_data = list_result.response_data.get("data", [])
            created_count = len(self.test_data.get('created_campaigns', []))
            listed_count = len(campaigns_data)
            
            logger.info("Data consistency check", 
                       created_campaigns=created_count,
                       listed_campaigns=listed_count)

    async def run_comprehensive_tests(self):
        """Execute the complete test suite."""
        logger.info("🚀 Starting Comprehensive API Test Suite")
        start_time = time.time()
        
        try:
            # Core functionality tests
            await self.test_server_startup()
            await self.test_health_endpoints()
            await self.test_campaign_management()
            await self.test_agent_management()
            await self.test_google_ads_integration()
            await self.test_analytics_endpoints()
            
            # Error handling and edge cases
            await self.test_error_handling()
            
            # Integration workflows
            await self.test_integration_workflows()
            
        except Exception as e:
            logger.error("Test suite execution failed", error=str(e))
            
        total_time = time.time() - start_time
        self.print_comprehensive_summary(total_time)
        
        return self.get_success_rate() >= 0.9  # 90% success rate threshold

    def print_comprehensive_summary(self, total_time: float):
        """Print detailed test results summary."""
        logger.info("=" * 80)
        logger.info("📊 COMPREHENSIVE TEST SUITE RESULTS")
        logger.info("=" * 80)
        
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r.success])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # Overall statistics
        logger.info(f"Total Tests Executed: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {failed_tests}")
        logger.info(f"Success Rate: {success_rate:.1f}%")
        logger.info(f"Total Execution Time: {total_time:.2f}s")
        logger.info(f"Average Test Time: {(total_time/total_tests):.3f}s")
        
        # Performance statistics
        response_times = [r.response_time for r in self.results if r.response_time]
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
            
            logger.info(f"Average Response Time: {avg_response_time:.3f}s")
            logger.info(f"Min Response Time: {min_response_time:.3f}s")
            logger.info(f"Max Response Time: {max_response_time:.3f}s")
        
        # Test breakdown by category
        categories = {}
        for result in self.results:
            category = result.name.split(':')[0] if ':' in result.name else "General"
            if category not in categories:
                categories[category] = {"total": 0, "passed": 0}
            categories[category]["total"] += 1
            if result.success:
                categories[category]["passed"] += 1
        
        logger.info("\n📋 Results by Category:")
        for category, stats in categories.items():
            rate = (stats["passed"] / stats["total"] * 100) if stats["total"] > 0 else 0
            logger.info(f"  {category}: {stats['passed']}/{stats['total']} ({rate:.1f}%)")
        
        # Failed tests details
        if failed_tests > 0:
            logger.info("\n❌ Failed Tests Details:")
            for result in self.results:
                if not result.success:
                    logger.error(f"  {result.method} {result.endpoint}")
                    logger.error(f"    Test: {result.name}")
                    logger.error(f"    Expected: {result.expected_status}, Got: {result.status_code}")
                    if result.error:
                        logger.error(f"    Error: {result.error}")
        
        # Test data summary
        if self.test_data:
            logger.info("\n🗃️  Test Data Created:")
            for data_type, items in self.test_data.items():
                logger.info(f"  {data_type}: {len(items) if isinstance(items, list) else items}")
        
        # Final assessment
        logger.info("=" * 80)
        if success_rate >= 90:
            logger.info("🎉 PHASE 2 API IMPLEMENTATION: EXCELLENT")
            logger.info("✅ All critical endpoints are working correctly")
        elif success_rate >= 80:
            logger.info("🟡 PHASE 2 API IMPLEMENTATION: GOOD")
            logger.info("⚠️  Some minor issues need attention")
        elif success_rate >= 70:
            logger.info("🟠 PHASE 2 API IMPLEMENTATION: NEEDS IMPROVEMENT")
            logger.info("⚠️  Several issues require fixes")
        else:
            logger.info("🔴 PHASE 2 API IMPLEMENTATION: CRITICAL ISSUES")
            logger.info("❌ Major problems need immediate attention")
        logger.info("=" * 80)

    def get_success_rate(self) -> float:
        """Get the overall success rate."""
        if not self.results:
            return 0.0
        return len([r for r in self.results if r.success]) / len(self.results)


async def main():
    """Main test execution function."""
    logger.info("🚀 Google Ads AI Agent System - Comprehensive API Test Suite")
    logger.info("Phase 2 Implementation Validation")
    
    try:
        async with ComprehensiveAPITester() as tester:
            success = await tester.run_comprehensive_tests()
            
            # Exit with appropriate code
            sys.exit(0 if success else 1)
            
    except KeyboardInterrupt:
        logger.info("Test execution cancelled by user")
        sys.exit(130)
    except Exception as e:
        logger.error("Test suite execution failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())