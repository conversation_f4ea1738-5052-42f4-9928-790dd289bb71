#!/usr/bin/env python3
"""
Simple database setup using direct AsyncPG connection.
"""
import asyncio
import asyncpg
from datetime import datetime

# Database connection string
DATABASE_URL = "*******************************************************************************/postgres"

# Core schema SQL
SCHEMA_SQL = """
-- Schema version tracking
CREATE TABLE IF NOT EXISTS schema_migrations (
    version VARCHAR(20) PRIMARY KEY,
    applied_at TIMESTAMP DEFAULT NOW(),
    description TEXT
);

-- Campaigns table
CREATE TABLE IF NOT EXISTS campaigns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    google_ads_id BIGINT UNIQUE,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',
    budget_amount DECIMAL(12, 2) NOT NULL,
    budget_currency VARCHAR(3) DEFAULT 'USD',
    bidding_strategy VARCHAR(50) NOT NULL,
    target_locations TEXT[],
    start_date DATE,
    end_date DATE,
    optimization_enabled BOOLEAN DEFAULT true,
    ai_agent_id UUID,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Ad Groups table
CREATE TABLE IF NOT EXISTS ad_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    google_ads_id BIGINT UNIQUE,
    status VARCHAR(20) NOT NULL DEFAULT 'ENABLED',
    default_cpc_bid_micros BIGINT,
    targeting_settings JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Ads table
CREATE TABLE IF NOT EXISTS ads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ad_group_id UUID REFERENCES ad_groups(id) ON DELETE CASCADE,
    google_ads_id BIGINT UNIQUE,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'ENABLED',
    headlines TEXT[],
    descriptions TEXT[],
    path1 VARCHAR(15),
    path2 VARCHAR(15),
    final_urls TEXT[],
    assets JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Agents table
CREATE TABLE IF NOT EXISTS agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    configuration JSONB,
    capabilities TEXT[],
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Agent Tasks table
CREATE TABLE IF NOT EXISTS agent_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
    task_type VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    priority INTEGER DEFAULT 5,
    parameters JSONB,
    result JSONB,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Performance Metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
    ad_group_id UUID REFERENCES ad_groups(id) ON DELETE CASCADE,
    ad_id UUID REFERENCES ads(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    impressions BIGINT DEFAULT 0,
    clicks BIGINT DEFAULT 0,
    conversions DECIMAL(12, 4) DEFAULT 0,
    cost_micros BIGINT DEFAULT 0,
    ctr DECIMAL(8, 6),
    average_cpc_micros BIGINT,
    conversion_rate DECIMAL(8, 6),
    cost_per_conversion_micros BIGINT,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(campaign_id, ad_group_id, ad_id, date)
);

-- Keywords table
CREATE TABLE IF NOT EXISTS keywords (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ad_group_id UUID REFERENCES ad_groups(id) ON DELETE CASCADE,
    google_ads_id BIGINT UNIQUE,
    text VARCHAR(255) NOT NULL,
    match_type VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'ENABLED',
    cpc_bid_micros BIGINT,
    quality_score INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Budget History table
CREATE TABLE IF NOT EXISTS budget_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
    old_budget_amount DECIMAL(12, 2),
    new_budget_amount DECIMAL(12, 2) NOT NULL,
    change_reason VARCHAR(255),
    changed_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Optimization History table
CREATE TABLE IF NOT EXISTS optimization_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id),
    optimization_type VARCHAR(100) NOT NULL,
    changes_made JSONB NOT NULL,
    metrics_before JSONB,
    metrics_after JSONB,
    success BOOLEAN,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Compliance Logs table
CREATE TABLE IF NOT EXISTS compliance_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
    compliance_type VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL,
    details JSONB,
    checked_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_campaigns_status ON campaigns(status);
CREATE INDEX IF NOT EXISTS idx_campaigns_type ON campaigns(type);
CREATE INDEX IF NOT EXISTS idx_campaigns_google_ads_id ON campaigns(google_ads_id);
CREATE INDEX IF NOT EXISTS idx_ad_groups_campaign_id ON ad_groups(campaign_id);
CREATE INDEX IF NOT EXISTS idx_ads_ad_group_id ON ads(ad_group_id);
CREATE INDEX IF NOT EXISTS idx_agent_tasks_status ON agent_tasks(status);
CREATE INDEX IF NOT EXISTS idx_agent_tasks_agent_id ON agent_tasks(agent_id);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_date ON performance_metrics(date);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_campaign_id ON performance_metrics(campaign_id);
CREATE INDEX IF NOT EXISTS idx_keywords_ad_group_id ON keywords(ad_group_id);

-- Insert initial migration record
INSERT INTO schema_migrations (version, description) 
VALUES ('1.0.0', 'Initial schema creation') 
ON CONFLICT (version) DO NOTHING;
"""

async def setup_database():
    """Set up the database schema."""
    print("🔧 Setting up database schema...")
    
    try:
        # Connect to the database
        conn = await asyncpg.connect(DATABASE_URL)
        print("✅ Connected to database")
        
        # Execute the schema SQL
        await conn.execute(SCHEMA_SQL)
        print("✅ Schema created successfully")
        
        # Check migrations table
        migrations = await conn.fetch("SELECT * FROM schema_migrations ORDER BY applied_at DESC")
        print(f"✅ Migrations applied: {len(migrations)}")
        for migration in migrations:
            print(f"   - {migration['version']}: {migration['description']}")
        
        # Check table counts
        tables = [
            'campaigns', 'ad_groups', 'ads', 'agents', 'agent_tasks', 
            'performance_metrics', 'keywords', 'budget_history', 
            'optimization_history', 'compliance_logs'
        ]
        
        print("\n📊 Table Status:")
        for table in tables:
            try:
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                print(f"   - {table}: {count} records")
            except Exception as e:
                print(f"   - {table}: Error - {e}")
        
        # Test basic operations
        print("\n🧪 Testing basic operations...")
        
        # Create a test campaign
        campaign_id = await conn.fetchval("""
            INSERT INTO campaigns (name, type, budget_amount, bidding_strategy)
            VALUES ($1, $2, $3, $4)
            RETURNING id
        """, "Test Campaign", "SEARCH", 100.0, "MANUAL_CPC")
        
        print(f"✅ Test campaign created: {campaign_id}")
        
        # Create a test agent
        agent_id = await conn.fetchval("""
            INSERT INTO agents (name, type, configuration)
            VALUES ($1, $2, $3)
            RETURNING id
        """, "Test Agent", "OPTIMIZATION", {"test": True})
        
        print(f"✅ Test agent created: {agent_id}")
        
        # Create a test task
        task_id = await conn.fetchval("""
            INSERT INTO agent_tasks (agent_id, campaign_id, task_type, parameters)
            VALUES ($1, $2, $3, $4)
            RETURNING id
        """, agent_id, campaign_id, "OPTIMIZATION", {"target": "cpc"})
        
        print(f"✅ Test task created: {task_id}")
        
        # Clean up test data
        await conn.execute("DELETE FROM agent_tasks WHERE id = $1", task_id)
        await conn.execute("DELETE FROM agents WHERE id = $1", agent_id)
        await conn.execute("DELETE FROM campaigns WHERE id = $1", campaign_id)
        print("✅ Test data cleaned up")
        
        await conn.close()
        print("\n🎉 Database setup completed successfully!")
        print("📋 Summary:")
        print("   - ✅ Schema created")
        print("   - ✅ Indexes created")
        print("   - ✅ Migration tracking enabled")
        print("   - ✅ Basic operations validated")
        
        return True
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False

async def check_database_status():
    """Check the current database status."""
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Check if tables exist
        tables_query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name;
        """
        
        tables = await conn.fetch(tables_query)
        print(f"📊 Found {len(tables)} tables:")
        for table in tables:
            count = await conn.fetchval(f"SELECT COUNT(*) FROM {table['table_name']}")
            print(f"   - {table['table_name']}: {count} records")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database status check failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 AiLex Ad Agent System - Database Setup")
    print("=" * 50)
    
    # Check current status
    print("\n1. Checking current database status...")
    asyncio.run(check_database_status())
    
    print("\n2. Setting up database schema...")
    success = asyncio.run(setup_database())
    
    if success:
        print("\n✅ Database is ready for production!")
        print("Next steps:")
        print("1. Start the FastAPI backend server")
        print("2. Begin Phase 2 implementation (API endpoints)")
        print("3. Set up monitoring and alerting")
    else:
        print("\n❌ Database setup failed. Please check the logs above.")