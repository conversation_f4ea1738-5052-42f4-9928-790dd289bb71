# AiLex Ad Agent System - Environment Configuration
# Copy this file to .env and fill in your actual values

# Application Settings
APP_NAME="AiLex Ad Agent System"
VERSION="1.0.0"
ENVIRONMENT="development"  # development, staging, production
DEBUG=true
HOST="0.0.0.0"
PORT=8000

# CORS Settings (comma-separated list)
CORS_ORIGINS="http://localhost:3000,https://localhost:3000,http://localhost:3001,https://localhost:3001"

# Trusted Hosts (optional, comma-separated list for production)
# TRUSTED_HOSTS="yourdomain.com,www.yourdomain.com"

# Database Settings (Supabase)
SUPABASE_URL="https://your-project-id.supabase.co"
SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"

# Legacy Database Settings (for backward compatibility)
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
DATABASE_KEY="your-supabase-service-key"

# Authentication Settings
SECRET_KEY="your-secret-key-for-sessions"

# Email Service (Resend)
RESEND_API_KEY="re_HBkWbnmb_JZ3UKJq4M5Hk7cBVpmMBibxF"
FROM_EMAIL="<EMAIL>"
FROM_NAME="AiLex Ad Agent System"

# Redis Settings
REDIS_URL="redis://localhost:6379/0"

# Google Ads API Settings
GOOGLE_ADS_DEVELOPER_TOKEN="your-developer-token"
GOOGLE_ADS_CLIENT_ID="your-oauth2-client-id"
GOOGLE_ADS_CLIENT_SECRET="your-oauth2-client-secret"
GOOGLE_ADS_REFRESH_TOKEN="your-oauth2-refresh-token"
GOOGLE_ADS_CUSTOMER_ID="*********0"  # Without dashes

# Google Analytics Settings
GOOGLE_ANALYTICS_PROPERTY_ID="*********"
GOOGLE_ANALYTICS_CREDENTIALS_PATH="/path/to/service-account-key.json"

# OpenAI Settings
OPENAI_API_KEY="sk-your-openai-api-key"
OPENAI_MODEL="gpt-4o"

# Google Gemini Settings
GEMINI_API_KEY="your-gemini-api-key"

# Pinecone Settings
PINECONE_API_KEY="your-pinecone-api-key"
PINECONE_ENVIRONMENT="your-pinecone-environment"
PINECONE_INDEX_NAME="ailex-memory"

# Celery Settings
CELERY_BROKER_URL="redis://localhost:6379/1"
CELERY_RESULT_BACKEND="redis://localhost:6379/2"

# Logging Settings
LOG_LEVEL="INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# Sentry Settings (optional, for error tracking)
# SENTRY_DSN="https://your-sentry-dsn"
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_PROFILES_SAMPLE_RATE=0.1

# Phoenix Tracing Settings (optional)
# PHOENIX_COLLECTOR_ENDPOINT="http://localhost:6006"
PHOENIX_PROJECT_NAME="ailex-ad-agents"

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# Campaign Optimization Settings
MAX_BID_ADJUSTMENT_PERCENT=20.0
MAX_BUDGET_ADJUSTMENT_PERCENT_PER_HOUR=5.0
OPTIMIZATION_INTERVAL_MINUTES=10

# A/B Testing Settings
MIN_SAMPLE_SIZE=100
STATISTICAL_SIGNIFICANCE_THRESHOLD=0.05

# GDPR and Compliance Settings
GDPR_ENABLED=true
DATA_RETENTION_DAYS=365

# Webhooks and Notifications
# SLACK_WEBHOOK_URL="https://hooks.slack.com/services/your/webhook/url"