You must install the lxml package before you can run mypy with `--html-report`.
You can do this with `python3 -m pip install lxml`.
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Coding/googleads/backend/venv/bin/mypy", line 8, in <module>
    sys.exit(console_entry())
             ^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Coding/googleads/backend/venv/lib/python3.12/site-packages/mypy/__main__.py", line 15, in console_entry
    main()
  File "mypy/main.py", line 127, in main
  File "mypy/main.py", line 211, in run_build
  File "mypy/build.py", line 191, in build
  File "mypy/build.py", line 232, in _build
  File "mypy/report.py", line 61, in __init__
  File "mypy/report.py", line 78, in add_report
ImportError
