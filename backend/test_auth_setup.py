#!/usr/bin/env python3
"""
Authentication Setup Test Script
Tests the Supabase Auth integration and email service configuration.
"""

import asyncio
import os
import sys
from datetime import datetime
import structlog

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.auth import auth_service
from utils.config import settings

logger = structlog.get_logger(__name__)


async def test_auth_service_initialization():
    """Test that the auth service initializes correctly."""
    print("🔧 Testing Auth Service Initialization...")
    
    try:
        # Test service initialization
        await auth_service.authenticate()
        print("✅ Auth service initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Auth service initialization failed: {str(e)}")
        return False


async def test_auth_service_health():
    """Test the auth service health check."""
    print("\n🏥 Testing Auth Service Health Check...")
    
    try:
        health_status = await auth_service.health_check()
        print(f"📊 Health Status: {health_status.get('status')}")
        
        # Check individual components
        checks = health_status.get('checks', {})
        for component, status in checks.items():
            component_status = status.get('status', 'unknown')
            if component_status == 'healthy':
                print(f"✅ {component}: {component_status}")
            elif component_status == 'disabled':
                print(f"⚠️  {component}: {component_status} - {status.get('note', '')}")
            else:
                print(f"❌ {component}: {component_status} - {status.get('error', '')}")
        
        return health_status.get('status') in ['healthy', 'degraded']
    except Exception as e:
        print(f"❌ Health check failed: {str(e)}")
        return False


async def test_email_service_configuration():
    """Test email service configuration."""
    print("\n📧 Testing Email Service Configuration...")
    
    try:
        # Check if Resend is configured
        if not settings.RESEND_API_KEY:
            print("⚠️  Resend API key not configured - email functionality will be limited")
            return False
        
        if not settings.FROM_EMAIL:
            print("⚠️  FROM_EMAIL not configured")
            return False
        
        print(f"✅ Resend API key: configured")
        print(f"✅ From email: {settings.FROM_EMAIL}")
        print(f"✅ From name: {settings.FROM_NAME}")
        
        return True
    except Exception as e:
        print(f"❌ Email configuration test failed: {str(e)}")
        return False


async def test_environment_variables():
    """Test that required environment variables are configured."""
    print("\n🌍 Testing Environment Variables...")
    
    required_vars = [
        ('SUPABASE_URL or DATABASE_URL', settings.database_url),
        ('SUPABASE_SERVICE_ROLE_KEY or DATABASE_KEY', settings.database_service_key),
    ]
    
    optional_vars = [
        ('RESEND_API_KEY', settings.RESEND_API_KEY),
        ('FROM_EMAIL', settings.FROM_EMAIL),
        ('SECRET_KEY', settings.SECRET_KEY),
    ]
    
    all_good = True
    
    print("Required variables:")
    for var_name, var_value in required_vars:
        if var_value:
            # Mask sensitive values
            masked_value = f"{var_value[:10]}..." if len(str(var_value)) > 10 else "***"
            print(f"✅ {var_name}: {masked_value}")
        else:
            print(f"❌ {var_name}: not configured")
            all_good = False
    
    print("\nOptional variables:")
    for var_name, var_value in optional_vars:
        if var_value:
            # Mask sensitive values
            if 'KEY' in var_name or 'SECRET' in var_name:
                masked_value = f"{var_value[:8]}..." if len(str(var_value)) > 8 else "***"
            else:
                masked_value = str(var_value)
            print(f"✅ {var_name}: {masked_value}")
        else:
            print(f"⚠️  {var_name}: not configured")
    
    return all_good


def print_configuration_summary():
    """Print a summary of the current configuration."""
    print("\n📋 Configuration Summary:")
    print("=" * 50)
    print(f"Environment: {settings.ENVIRONMENT}")
    print(f"Debug Mode: {settings.DEBUG}")
    print(f"App Name: {settings.APP_NAME}")
    print(f"Version: {settings.VERSION}")
    print()
    print("Database Configuration:")
    if settings.database_url:
        print(f"  URL: {settings.database_url[:30]}...")
    else:
        print("  URL: Not configured")
    
    print(f"  Service Key: {'Configured' if settings.database_service_key else 'Not configured'}")
    print()
    print("Email Configuration:")
    print(f"  Resend API: {'Configured' if settings.RESEND_API_KEY else 'Not configured'}")
    print(f"  From Email: {settings.FROM_EMAIL}")
    print(f"  From Name: {settings.FROM_NAME}")
    print("=" * 50)


async def run_comprehensive_test():
    """Run all authentication tests."""
    print("🚀 Starting Authentication Setup Tests")
    print("=" * 60)
    
    # Print configuration summary
    print_configuration_summary()
    
    # Run tests
    test_results = []
    
    test_results.append(await test_environment_variables())
    test_results.append(await test_auth_service_initialization())
    test_results.append(await test_auth_service_health())
    test_results.append(await test_email_service_configuration())
    
    # Summary
    print("\n🏁 Test Results Summary")
    print("=" * 30)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Authentication setup is ready.")
        return 0
    elif passed_tests >= total_tests - 1:
        print("⚠️  Most tests passed. Check warnings above.")
        return 0
    else:
        print("❌ Some tests failed. Please review the configuration.")
        return 1


if __name__ == "__main__":
    # Configure structlog for testing
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer(),
        ],
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        context_class=dict,
        cache_logger_on_first_use=True,
    )
    
    # Run tests
    exit_code = asyncio.run(run_comprehensive_test())
    sys.exit(exit_code)