#!/usr/bin/env python3
"""
Validate database setup and operations.
"""
import asyncio
import asyncpg
from datetime import datetime, date
import json

DATABASE_URL = "*******************************************************************************/postgres"

async def validate_database():
    """Validate all database operations."""
    print("🔍 Validating database setup and operations...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        print("✅ Database connection successful")
        
        # Check all expected tables exist
        expected_tables = [
            'campaigns', 'ad_groups', 'ads', 'agents', 'agent_tasks', 
            'performance_metrics', 'keywords', 'budget_history', 
            'optimization_history', 'compliance_logs', 'schema_migrations'
        ]
        
        existing_tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
            ORDER BY table_name;
        """)
        
        existing_table_names = [row['table_name'] for row in existing_tables]
        
        print(f"\n📊 Table Validation:")
        all_tables_exist = True
        for table in expected_tables:
            exists = table in existing_table_names
            status = "✅" if exists else "❌"
            print(f"   {status} {table}: {'EXISTS' if exists else 'MISSING'}")
            if not exists:
                all_tables_exist = False
        
        if not all_tables_exist:
            print("❌ Some tables are missing. Database setup incomplete.")
            await conn.close()
            return False
        
        # Test CRUD operations on each major table
        print(f"\n🧪 Testing CRUD Operations:")
        
        # Test Campaigns
        print("   📝 Testing Campaigns...")
        campaign_id = await conn.fetchval("""
            INSERT INTO campaigns (name, type, status, budget_amount, bidding_strategy)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id
        """, "Test Campaign", "search", "active", 100.00, "manual_cpc")
        
        campaign = await conn.fetchrow("SELECT * FROM campaigns WHERE id = $1", campaign_id)
        assert campaign['name'] == "Test Campaign"
        print("   ✅ Campaigns: CREATE/READ successful")
        
        await conn.execute("""
            UPDATE campaigns SET name = $1, updated_at = $2 WHERE id = $3
        """, "Updated Test Campaign", datetime.now(), campaign_id)
        
        updated_campaign = await conn.fetchrow("SELECT * FROM campaigns WHERE id = $1", campaign_id)
        assert updated_campaign['name'] == "Updated Test Campaign"
        print("   ✅ Campaigns: UPDATE successful")
        
        # Test Agents
        print("   🤖 Testing Agents...")
        agent_config = json.dumps({"optimization_target": "cpc", "threshold": 0.05})
        agent_id = await conn.fetchval("""
            INSERT INTO agents (name, description, type, status, config, version)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id
        """, "Test Optimization Agent", "AI agent for bid optimization", "bid_optimization", "active", agent_config, "1.0.0")
        
        agent = await conn.fetchrow("SELECT * FROM agents WHERE id = $1", agent_id)
        assert agent['name'] == "Test Optimization Agent"
        assert agent['type'] == "bid_optimization"
        print("   ✅ Agents: CREATE/READ successful")
        
        # Test Agent Tasks
        print("   📋 Testing Agent Tasks...")
        task_params = json.dumps({"target_cpc": 2.50, "max_bid_adjustment": 0.20})
        task_id = await conn.fetchval("""
            INSERT INTO agent_tasks (agent_id, campaign_id, name, description, type, status, priority, input_data)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING id
        """, agent_id, campaign_id, "Optimize Campaign Bids", "Optimize bidding strategy for better performance", "bid_optimization", "pending", "high", task_params)
        
        task = await conn.fetchrow("SELECT * FROM agent_tasks WHERE id = $1", task_id)
        assert task['type'] == "bid_optimization"
        assert task['priority'] == "high"
        print("   ✅ Agent Tasks: CREATE/READ successful")
        
        # Test Ad Groups
        print("   📂 Testing Ad Groups...")
        adgroup_id = await conn.fetchval("""
            INSERT INTO ad_groups (campaign_id, name, status, bid_strategy, keywords)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id
        """, campaign_id, "Test Ad Group", "enabled", "manual_cpc", ["test keyword", "sample keyword"])
        
        adgroup = await conn.fetchrow("SELECT * FROM ad_groups WHERE id = $1", adgroup_id)
        assert adgroup['name'] == "Test Ad Group"
        assert adgroup['status'] == "enabled"
        print("   ✅ Ad Groups: CREATE/READ successful")
        
        # Test Performance Metrics
        print("   📈 Testing Performance Metrics...")
        metrics_id = await conn.fetchval("""
            INSERT INTO performance_metrics (
                campaign_id, ad_group_id, date, impressions, clicks, 
                conversions, cost_micros, ctr, average_cpc_micros
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING id
        """, campaign_id, adgroup_id, date.today(), 1500, 75, 3.5, 18750000, 0.05, 250000)
        
        metrics = await conn.fetchrow("SELECT * FROM performance_metrics WHERE id = $1", metrics_id)
        assert metrics['impressions'] == 1500
        assert metrics['clicks'] == 75
        print("   ✅ Performance Metrics: CREATE/READ successful")
        
        # Test Budget History
        print("   💰 Testing Budget History...")
        budget_history_id = await conn.fetchval("""
            INSERT INTO budget_history (campaign_id, old_budget_amount, new_budget_amount, change_reason, changed_by)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id
        """, campaign_id, 100.00, 150.00, "Performance increase", "AI Agent")
        
        budget_history = await conn.fetchrow("SELECT * FROM budget_history WHERE id = $1", budget_history_id)
        assert budget_history['new_budget_amount'] == 150.00
        print("   ✅ Budget History: CREATE/READ successful")
        
        # Test Optimization History
        print("   ⚡ Testing Optimization History...")
        changes_made = json.dumps({
            "bid_adjustments": {"mobile": 0.15, "desktop": -0.10},
            "new_keywords": ["buy online", "best price"]
        })
        metrics_before = json.dumps({"cpc": 2.50, "ctr": 0.045, "conversions": 12})
        metrics_after = json.dumps({"cpc": 2.25, "ctr": 0.052, "conversions": 15})
        
        opt_history_id = await conn.fetchval("""
            INSERT INTO optimization_history (
                campaign_id, agent_id, optimization_type, changes_made, 
                metrics_before, metrics_after, success, notes
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING id
        """, campaign_id, agent_id, "BID_OPTIMIZATION", changes_made, 
        metrics_before, metrics_after, True, "Successful CPC reduction")
        
        opt_history = await conn.fetchrow("SELECT * FROM optimization_history WHERE id = $1", opt_history_id)
        assert opt_history['optimization_type'] == "BID_OPTIMIZATION"
        assert opt_history['success'] == True
        print("   ✅ Optimization History: CREATE/READ successful")
        
        # Test Foreign Key Relationships
        print("\n🔗 Testing Foreign Key Relationships...")
        
        # Try to delete a campaign with related data (should fail due to FK constraints with CASCADE)
        related_count = await conn.fetchval("""
            SELECT COUNT(*) FROM (
                SELECT 1 FROM ad_groups WHERE campaign_id = $1
                UNION ALL
                SELECT 1 FROM agent_tasks WHERE campaign_id = $1
                UNION ALL
                SELECT 1 FROM performance_metrics WHERE campaign_id = $1
                UNION ALL
                SELECT 1 FROM budget_history WHERE campaign_id = $1
                UNION ALL
                SELECT 1 FROM optimization_history WHERE campaign_id = $1
            ) related_records
        """, campaign_id)
        
        print(f"   📊 Found {related_count} related records")
        assert related_count > 0, "Should have related records"
        print("   ✅ Foreign key relationships working")
        
        # Test cascading delete
        print("   🗑️  Testing cascading delete...")
        await conn.execute("DELETE FROM campaigns WHERE id = $1", campaign_id)
        
        # Check that related records were also deleted
        remaining_related = await conn.fetchval("""
            SELECT COUNT(*) FROM (
                SELECT 1 FROM ad_groups WHERE campaign_id = $1
                UNION ALL
                SELECT 1 FROM agent_tasks WHERE campaign_id = $1
                UNION ALL
                SELECT 1 FROM performance_metrics WHERE campaign_id = $1
                UNION ALL
                SELECT 1 FROM budget_history WHERE campaign_id = $1
                UNION ALL
                SELECT 1 FROM optimization_history WHERE campaign_id = $1
            ) related_records
        """, campaign_id)
        
        assert remaining_related == 0, "Related records should be deleted"
        print("   ✅ Cascading delete working")
        
        # Clean up remaining test data
        await conn.execute("DELETE FROM agents WHERE id = $1", agent_id)
        
        # Test indexes exist
        print("\n📋 Testing Indexes...")
        indexes = await conn.fetch("""
            SELECT indexname, tablename 
            FROM pg_indexes 
            WHERE schemaname = 'public' 
            AND indexname LIKE 'idx_%'
            ORDER BY tablename, indexname;
        """)
        
        print(f"   📊 Found {len(indexes)} custom indexes:")
        for idx in indexes:
            print(f"      - {idx['tablename']}.{idx['indexname']}")
        
        assert len(indexes) >= 8, "Should have at least 8 custom indexes"
        print("   ✅ Database indexes properly created")
        
        await conn.close()
        
        print(f"\n🎉 DATABASE VALIDATION SUCCESSFUL!")
        print("=" * 50)
        print("✅ All tables exist and are properly structured")
        print("✅ CRUD operations working on all major tables")
        print("✅ Foreign key relationships and cascading deletes working")
        print("✅ Database indexes created and functional")
        print("✅ JSON fields working properly")
        print("✅ Date/timestamp fields working properly")
        print("✅ Decimal fields working properly")
        print("✅ Array fields working properly")
        print("\n🚀 Database is production-ready!")
        
        return True
        
    except Exception as e:
        print(f"❌ Database validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 AiLex Ad Agent System - Database Validation")
    print("=" * 50)
    
    success = asyncio.run(validate_database())
    
    if success:
        print("\n✅ PHASE 1 COMPLETE!")
        print("The database layer is fully implemented and tested.")
        print("\nReady for Phase 2:")
        print("1. FastAPI endpoint implementation")
        print("2. Google Ads API integration")
        print("3. AI agent system activation")
    else:
        print("\n❌ Validation failed. Please review the errors above.")