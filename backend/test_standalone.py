"""
Standalone test to verify our test structure works.
This test doesn't require external dependencies.
"""

import sys
import os

# Add the backend directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from models.common import BaseResponse, Currency, Language


def test_base_response_creation():
    """Test that we can create a BaseResponse."""
    response = BaseResponse[dict](
        success=True,
        message="Test successful",
        data={"key": "value"}
    )
    
    assert response.success is True
    assert response.message == "Test successful"
    assert response.data == {"key": "value"}
    print("✓ BaseResponse test passed")


def test_currency_enum():
    """Test Currency enum."""
    assert Currency.USD == "USD"
    assert Currency.EUR == "EUR"
    print("✓ Currency enum test passed")


def test_language_enum():
    """Test Language enum."""
    assert Language.ENGLISH == "en"
    assert Language.SPANISH == "es"
    print("✓ Language enum test passed")


if __name__ == "__main__":
    print("Running standalone tests...")
    
    try:
        test_base_response_creation()
        test_currency_enum()
        test_language_enum()
        print("\n🎉 All standalone tests passed!")
        print("✅ Test structure is working correctly")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)