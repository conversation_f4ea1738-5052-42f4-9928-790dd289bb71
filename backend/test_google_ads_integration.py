#!/usr/bin/env python3
"""
Test script for Google Ads API integration.
Tests basic functionality and service imports.
"""

import sys
import os
import asyncio
from datetime import date, datetime
from unittest.mock import patch, MagicMock

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Test imports
def test_imports():
    """Test that all modules can be imported successfully."""
    print("Testing imports...")
    
    try:
        # Test core service imports
        from services.google_ads import GoogleAdsService, google_ads_service
        print("✓ Google Ads service imported successfully")
        
        from services.google_ads_auth import GoogleAdsAuthService, google_ads_auth_service
        print("✓ Google Ads auth service imported successfully")
        
        # Test API imports
        from api.google_ads import router
        print("✓ Google Ads API router imported successfully")
        
        # Test model imports
        from models.campaigns import CampaignType, CampaignStatus, BiddingStrategy
        print("✓ Campaign models imported successfully")
        
        print("All imports successful! ✓")
        return True
        
    except ImportError as e:
        print(f"Import error: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False


def test_google_ads_service_initialization():
    """Test GoogleAdsService initialization."""
    print("\nTesting GoogleAdsService initialization...")
    
    try:
        from services.google_ads import GoogleAdsService
        
        # Create service instance
        service = GoogleAdsService()
        
        # Check basic attributes
        assert service.service_name == "Google Ads API"
        assert service.rate_limit_per_minute == 1000
        assert hasattr(service, '_campaign_type_mapping')
        assert hasattr(service, '_campaign_status_mapping')
        assert hasattr(service, '_keyword_match_type_mapping')
        
        print("✓ GoogleAdsService initialized successfully")
        return True
        
    except Exception as e:
        print(f"GoogleAdsService initialization failed: {e}")
        return False


def test_google_ads_auth_service_initialization():
    """Test GoogleAdsAuthService initialization."""
    print("\nTesting GoogleAdsAuthService initialization...")
    
    try:
        from services.google_ads_auth import GoogleAdsAuthService
        
        # Create service instance
        auth_service = GoogleAdsAuthService()
        
        # Check basic attributes
        assert auth_service.auth_url == "https://accounts.google.com/o/oauth2/v2/auth"
        assert auth_service.token_url == "https://oauth2.googleapis.com/token"
        assert hasattr(auth_service, 'scopes')
        assert len(auth_service.scopes) > 0
        
        print("✓ GoogleAdsAuthService initialized successfully")
        return True
        
    except Exception as e:
        print(f"GoogleAdsAuthService initialization failed: {e}")
        return False


def test_oauth2_url_generation():
    """Test OAuth2 URL generation."""
    print("\nTesting OAuth2 URL generation...")
    
    try:
        from services.google_ads_auth import GoogleAdsAuthService
        
        auth_service = GoogleAdsAuthService()
        redirect_uri = "http://localhost:8000/callback"
        
        # Generate auth URL
        auth_data = auth_service.generate_auth_url(redirect_uri)
        
        assert "auth_url" in auth_data, f"auth_url not in response: {auth_data}"
        assert "state" in auth_data, f"state not in response: {auth_data}"
        assert "https://accounts.google.com/o/oauth2/v2/auth" in auth_data["auth_url"], f"Auth URL incorrect: {auth_data['auth_url']}"
        # Check that redirect URI is URL-encoded in the auth URL
        from urllib.parse import quote
        encoded_redirect_uri = quote(redirect_uri, safe='')
        assert encoded_redirect_uri in auth_data["auth_url"], f"Encoded redirect URI not in auth URL: {auth_data['auth_url']}"
        
        print("✓ OAuth2 URL generation successful")
        print(f"  Generated state: {auth_data['state'][:20]}...")
        return True
        
    except Exception as e:
        print(f"OAuth2 URL generation failed: {e}")
        return False


def test_campaign_type_mappings():
    """Test campaign type mappings."""
    print("\nTesting campaign type mappings...")
    
    try:
        from services.google_ads import GoogleAdsService
        from models.campaigns import CampaignType
        
        service = GoogleAdsService()
        
        # Test all campaign type mappings
        expected_mappings = {
            CampaignType.SEARCH: "SEARCH",
            CampaignType.DISPLAY: "DISPLAY",
            CampaignType.SHOPPING: "SHOPPING",
            CampaignType.VIDEO: "VIDEO",
            CampaignType.PERFORMANCE_MAX: "PERFORMANCE_MAX",
        }
        
        for campaign_type, expected_google_type in expected_mappings.items():
            google_type = service._campaign_type_mapping.get(campaign_type)
            assert google_type == expected_google_type, f"Mapping failed for {campaign_type}"
        
        print("✓ Campaign type mappings are correct")
        return True
        
    except Exception as e:
        print(f"Campaign type mapping test failed: {e}")
        return False


def test_api_router():
    """Test API router configuration."""
    print("\nTesting API router configuration...")
    
    try:
        from api.google_ads import router
        from fastapi import APIRouter
        
        # Check that router is an APIRouter instance
        assert isinstance(router, APIRouter)
        
        # Check that routes are defined
        routes = [route.path for route in router.routes]
        
        # Expected endpoint patterns
        expected_endpoints = [
            "/auth/url",
            "/auth/token", 
            "/accounts",
            "/sync/campaigns",
            "/sync/performance",
            "/ad-groups",
            "/keywords",
            "/budgets",
        ]
        
        found_endpoints = 0
        for expected in expected_endpoints:
            if any(expected in route for route in routes):
                found_endpoints += 1
        
        print(f"✓ API router configured with {len(routes)} routes")
        print(f"✓ Found {found_endpoints}/{len(expected_endpoints)} expected endpoint patterns")
        return True
        
    except Exception as e:
        print(f"API router test failed: {e}")
        return False


async def test_service_health_check():
    """Test service health check without requiring actual Google Ads credentials."""
    print("\nTesting service health check (mock)...")
    
    try:
        from services.google_ads import GoogleAdsService
        
        service = GoogleAdsService()
        
        # Mock the authentication to avoid needing real credentials
        with patch.object(service, 'authenticate') as mock_auth, \
             patch.object(service, '_test_authentication') as mock_test:
            
            mock_auth.return_value = None
            mock_test.return_value = None
            
            health_status = await service.health_check()
            
            assert "status" in health_status
            assert "authenticated" in health_status
            assert "last_check" in health_status
        
        print("✓ Service health check completed")
        return True
        
    except Exception as e:
        print(f"Service health check failed: {e}")
        return False


def test_error_handling():
    """Test error handling and exception classes."""
    print("\nTesting error handling...")
    
    try:
        from utils.exceptions import GoogleAdsException as CustomGoogleAdsException
        
        # Test custom exception creation
        test_message = "Test Google Ads error"
        exception = CustomGoogleAdsException(test_message)
        
        # The GoogleAdsException has a prefix, so check if our message is in the exception
        assert test_message in str(exception), f"Exception message incorrect: {str(exception)}"
        
        print("✓ Error handling classes work correctly")
        return True
        
    except Exception as e:
        print(f"Error handling test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("Google Ads API Integration Test Suite")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_google_ads_service_initialization,
        test_google_ads_auth_service_initialization,
        test_oauth2_url_generation,
        test_campaign_type_mappings,
        test_api_router,
        test_error_handling,
    ]
    
    async_tests = [
        test_service_health_check,
    ]
    
    # Run synchronous tests
    passed = 0
    total = len(tests) + len(async_tests)
    
    for test in tests:
        if test():
            passed += 1
    
    # Run asynchronous tests
    for async_test in async_tests:
        try:
            result = asyncio.run(async_test())
            if result:
                passed += 1
        except Exception as e:
            print(f"Async test {async_test.__name__} failed: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Google Ads integration is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)