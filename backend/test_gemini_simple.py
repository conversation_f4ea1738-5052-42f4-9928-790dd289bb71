#!/usr/bin/env python3
"""
Simple test to verify Gemini AI integration without CrewAI dependencies.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent))

async def test_gemini():
    """Test Gemini service directly."""
    print("🧪 Testing Gemini AI Integration")
    print("=" * 60)
    
    # Check for API key
    gemini_key = os.getenv("GEMINI_API_KEY")
    if not gemini_key:
        print("❌ GEMINI_API_KEY not set in environment")
        print("Set it with: export GEMINI_API_KEY='your-key-here'")
        return False
    
    print("✅ GEMINI_API_KEY found")
    
    try:
        # Import Gemini service
        from services.gemini_service import GoogleGeminiService
        
        # Initialize service
        service = GoogleGeminiService()
        print("✅ Gemini service initialized")
        
        # Test authentication
        await service.authenticate()
        print("✅ Authenticated with Gemini API")
        
        # Test health check
        health = await service.health_check()
        print(f"✅ Health check: {health['status']}")
        
        # Test text generation
        print("\n📝 Testing text generation...")
        response = await service.generate_text(
            prompt="Write a brief tagline for a Google Ads campaign for a legal tech startup",
            max_tokens=100
        )
        print(f"Generated: {response}")
        
        # Test ad copy generation
        print("\n📝 Testing ad copy generation...")
        ad_copy = await service.generate_ad_copy(
            business_description="Legal tech startup providing AI-powered contract review",
            target_audience="Law firms and legal departments",
            ad_format="responsive_search_ad",
            tone="professional"
        )
        print("Generated ad copy:")
        for key, value in ad_copy.items():
            if isinstance(value, list):
                print(f"  {key}:")
                for item in value[:3]:  # Show first 3
                    print(f"    - {item}")
            else:
                print(f"  {key}: {value}")
        
        # Test keyword generation
        print("\n🔍 Testing keyword generation...")
        keywords = await service.generate_keywords(
            business_description="Legal tech AI contract review",
            target_audience="Corporate lawyers",
            campaign_objectives=["Generate leads", "Build awareness"]
        )
        print(f"Generated {len(keywords)} keywords:")
        for kw in keywords[:5]:  # Show first 5
            print(f"  - {kw}")
        
        print("\n✅ All Gemini tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Install: pip install google-generativeai")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_orchestration():
    """Test a simple orchestration without full CrewAI."""
    print("\n🤖 Testing Simple Agent Orchestration")
    print("=" * 60)
    
    try:
        from services.gemini_service import GoogleGeminiService
        
        # Initialize service
        service = GoogleGeminiService()
        await service.authenticate()
        
        # Simulate multi-agent collaboration using Gemini
        print("\n📊 Simulating multi-agent workflow...")
        
        # Agent 1: Market Research
        print("\n1️⃣ Market Research Agent:")
        market_research = await service.generate_text(
            prompt="""As a market research agent, analyze the legal tech industry briefly:
            - Market size
            - Key trends
            - Target audience
            Provide 3 bullet points.""",
            max_tokens=200
        )
        print(market_research)
        
        # Agent 2: Campaign Strategy
        print("\n2️⃣ Campaign Strategy Agent:")
        strategy = await service.generate_text(
            prompt=f"""Based on this market research: {market_research}
            
            As a campaign strategy agent, recommend:
            - Best advertising channels
            - Key message
            - Budget allocation
            Provide 3 bullet points.""",
            max_tokens=200
        )
        print(strategy)
        
        # Agent 3: Ad Creation
        print("\n3️⃣ Ad Creation Agent:")
        ad_creation = await service.generate_ad_copy(
            business_description=f"Legal tech startup. Strategy: {strategy}",
            target_audience="Corporate law firms",
            ad_format="responsive_search_ad",
            tone="professional"
        )
        print("Generated ads based on strategy:")
        for headline in ad_creation.get("headlines", [])[:3]:
            print(f"  Headline: {headline}")
        for desc in ad_creation.get("descriptions", [])[:2]:
            print(f"  Description: {desc}")
        
        print("\n✅ Simple orchestration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Orchestration error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    try:
        # Test Gemini
        gemini_success = await test_gemini()
        
        if gemini_success:
            # Test orchestration
            orchestration_success = await test_simple_orchestration()
            
            if orchestration_success:
                print("\n🎉 All tests passed! AI system is working!")
                print("\n📝 Next steps:")
                print("1. Install CrewAI for full orchestration: pip install crewai")
                print("2. Run full demo: python example_orchestration.py")
                print("3. Test API endpoints: POST /api/v1/agents/orchestrator/workflows")
            else:
                print("\n⚠️ Orchestration test failed")
        else:
            print("\n⚠️ Gemini test failed - check API key")
            
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())