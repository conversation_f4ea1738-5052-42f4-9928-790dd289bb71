"""
Celery configuration for AiLex Worker Service.
Centralized configuration for all Celery tasks and workers.
"""

from celery import Celery
from utils.config import settings

# Create Celery app
celery_app = Celery(
    "ailex_worker",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "agents.tasks",
        "services.background_tasks",
    ]
)

# Celery configuration
celery_app.conf.update(
    # Serialization
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    
    # Timezone
    timezone="UTC",
    enable_utc=True,
    
    # Task settings
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    
    # Worker settings
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    worker_disable_rate_limits=False,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_persistent=True,
    
    # Routing
    task_routes={
        # Agent tasks
        "agents.create_campaign": {"queue": "campaigns"},
        "agents.optimize_campaign": {"queue": "optimization"},
        "agents.analyze_performance": {"queue": "analytics"},
        "agents.generate_ad_assets": {"queue": "creative"},
        "agents.research_keywords": {"queue": "research"},
        "agents.health_check": {"queue": "health"},
        
        # Service tasks
        "services.sync_campaign_data": {"queue": "sync"},
        "services.update_performance_metrics": {"queue": "metrics"},
        "services.cleanup_expired_data": {"queue": "maintenance"},
        "services.refresh_auth_tokens": {"queue": "auth"},
        "services.database_health_check": {"queue": "health"},
        "services.system_maintenance": {"queue": "maintenance"}
    },
    
    # Queue configuration
    task_default_queue="default",
    task_default_exchange="default",
    task_default_exchange_type="direct",
    task_default_routing_key="default",
    
    # Monitoring
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # Security
    worker_hijack_root_logger=False,
    worker_log_color=False,
)

# Periodic task schedule
celery_app.conf.beat_schedule = {
    # Data synchronization
    "sync-campaign-data": {
        "task": "services.sync_campaign_data",
        "schedule": 3600.0,  # Every hour
        "options": {"queue": "sync"}
    },
    
    # Performance monitoring
    "update-performance-metrics": {
        "task": "services.update_performance_metrics", 
        "schedule": 1800.0,  # Every 30 minutes
        "options": {"queue": "metrics"}
    },
    
    # Maintenance tasks
    "cleanup-expired-data": {
        "task": "services.cleanup_expired_data",
        "schedule": 86400.0,  # Daily
        "options": {"queue": "maintenance"}
    },
    
    # Authentication
    "refresh-auth-tokens": {
        "task": "services.refresh_auth_tokens",
        "schedule": 21600.0,  # Every 6 hours
        "options": {"queue": "auth"}
    },
    
    # Health checks
    "database-health-check": {
        "task": "services.database_health_check",
        "schedule": 300.0,  # Every 5 minutes
        "options": {"queue": "health"}
    },
    
    "agent-health-check": {
        "task": "agents.health_check",
        "schedule": 600.0,  # Every 10 minutes
        "options": {"queue": "health"}
    },
    
    # System maintenance
    "system-maintenance": {
        "task": "services.system_maintenance",
        "schedule": 604800.0,  # Weekly
        "options": {"queue": "maintenance"}
    }
}
