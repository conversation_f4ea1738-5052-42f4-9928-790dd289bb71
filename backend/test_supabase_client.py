#!/usr/bin/env python3
"""
Test Supabase client connection using the service key.
"""
import os
from supabase import create_client

def test_supabase_client():
    """Test Supabase client connection."""
    try:
        # Use the service key from .env
        supabase_url = "https://pamppqrhytvyclvdbbxx.supabase.co"
        supabase_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBhbXBwcXJoeXR2eWNsdmRiYnh4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzUwMTYzNiwiZXhwIjoyMDQ5MDc3NjM2fQ.KJvBBbyJ_oZt8T2tRJMz0bwEjvZCtj41jSGYQvZd8so"
        
        print("Testing Supabase client connection...")
        print(f"URL: {supabase_url}")
        
        # Create Supabase client
        supabase = create_client(supabase_url, supabase_key)
        
        # Test basic connection by trying to read from a system table
        result = supabase.rpc('version').execute()
        print(f"✅ Supabase client connection successful!")
        print(f"Database info: {result}")
        
        # Try to create a test table using raw SQL
        result = supabase.rpc('exec', {
            'sql': '''
                CREATE TABLE IF NOT EXISTS test_supabase_connection (
                    id SERIAL PRIMARY KEY,
                    name TEXT,
                    created_at TIMESTAMP DEFAULT NOW()
                );
            '''
        }).execute()
        print("✅ Table creation via Supabase client successful!")
        
        # Test inserting data
        result = supabase.table('test_supabase_connection').insert({
            'name': 'Test via Supabase Client'
        }).execute()
        print("✅ Data insertion via Supabase client successful!")
        
        # Test querying data
        result = supabase.table('test_supabase_connection').select('*').execute()
        print(f"✅ Data query successful! Found {len(result.data)} records")
        
        # Clean up
        supabase.rpc('exec', {
            'sql': 'DROP TABLE IF EXISTS test_supabase_connection;'
        }).execute()
        print("✅ Cleanup successful!")
        
        print("\n🎉 Supabase client test passed! We can use this approach.")
        
    except Exception as e:
        print(f"❌ Supabase client test failed: {e}")
        print(f"Error type: {type(e)}")
        
        # Let's try a simpler test - just check auth
        try:
            print("\nTrying basic auth test...")
            from supabase import create_client
            supabase = create_client(supabase_url, supabase_key)
            
            # Just test if we can create the client
            print("✅ Supabase client created successfully!")
            
            # Test health check
            print("Testing basic table operations...")
            result = supabase.table('_supabase_migrations').select('version').limit(1).execute()
            print(f"✅ Basic query successful: {result}")
            
        except Exception as e2:
            print(f"❌ Basic test also failed: {e2}")

if __name__ == "__main__":
    test_supabase_client()