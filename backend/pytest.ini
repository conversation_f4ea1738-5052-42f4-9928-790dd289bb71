[tool:pytest]
# Pytest configuration file
minversion = 7.0
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Async support
asyncio_mode = auto

# Coverage settings
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --cov=.
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --no-cov-on-fail

# Coverage exclusions
cov-config = .coveragerc

# Markers for test categorization
markers =
    unit: Unit tests for individual components
    integration: Integration tests for API endpoints and services
    slow: Slow tests that take more than 1 second
    external: Tests that require external services
    auth: Tests related to authentication and authorization
    database: Tests that interact with the database
    redis: Tests that interact with Redis
    google_ads: Tests for Google Ads API integration
    openai: Tests for OpenAI API integration
    performance: Performance and load tests
    security: Security-related tests
    validation: Input validation tests

# Test discovery
norecursedirs = .git .tox htmlcov build dist *.egg

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:urllib3.*
    ignore::UserWarning:google.*

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Test timeout (prevent hanging tests)
timeout = 300

# Parallel execution
addopts_parallel = -n auto --dist=worksteal

# Environment variables for testing
env = 
    ENVIRONMENT = testing
    DEBUG = true
    TESTING = true