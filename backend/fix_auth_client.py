#!/usr/bin/env python3
"""
Fix for Supabase client proxy parameter issue.
This script patches the auth service to remove the problematic proxy parameter.
"""

import asyncio
import os
import sys

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from supabase import create_client, Client
from utils.config import settings

async def test_supabase_client():
    """Test Supabase client without proxy parameter."""
    print("🔧 Testing Supabase Client Fix")
    print("=" * 40)
    
    try:
        # Get configuration
        supabase_url = settings.database_url or settings.SUPABASE_URL
        supabase_key = settings.database_service_key or settings.SUPABASE_SERVICE_ROLE_KEY
        
        if not supabase_url or not supabase_key:
            print("❌ Supabase configuration missing")
            print(f"URL: {'✓' if supabase_url else '❌'}")
            print(f"Key: {'✓' if supabase_key else '❌'}")
            return False
        
        # Clean the URL if it's a database URL
        if 'db.' in supabase_url:
            from urllib.parse import urlparse
            parsed = urlparse(supabase_url)
            supabase_url = f"https://{parsed.hostname.replace('db.', '')}.supabase.co"
        
        print(f"Using URL: {supabase_url[:50]}...")
        
        # Create client without any extra parameters
        client = create_client(supabase_url, supabase_key)
        print("✅ Supabase client created successfully")
        
        # Test auth functionality
        try:
            # This should not fail if the client is working
            auth_response = client.auth.get_user()  # This will return None for service role
            print("✅ Auth client accessible")
        except Exception as auth_error:
            print(f"⚠️  Auth client warning (expected for service role): {auth_error}")
        
        # Test database connection
        try:
            # Simple query to test connection
            response = client.table('users').select('id').limit(1).execute()
            print("✅ Database connection working")
        except Exception as db_error:
            print(f"⚠️  Database query warning: {db_error}")
        
        return True
        
    except Exception as e:
        print(f"❌ Client creation failed: {e}")
        return False

async def main():
    """Test the fix."""
    success = await test_supabase_client()
    
    if success:
        print("\n✅ Supabase client is working correctly!")
        print("The authentication issue should be resolved.")
    else:
        print("\n❌ Supabase client still has issues.")
        print("Check the configuration and try again.")

if __name__ == "__main__":
    asyncio.run(main())