<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../mypy-html.css">
</head>
<body>
<h2>utils.config</h2>
<table>
<caption>utils/config.py</caption>
<tbody><tr>
<td class="table-lines"><pre><span id="L1" class="lineno"><a class="lineno" href="#L1">1</a></span>
<span id="L2" class="lineno"><a class="lineno" href="#L2">2</a></span>
<span id="L3" class="lineno"><a class="lineno" href="#L3">3</a></span>
<span id="L4" class="lineno"><a class="lineno" href="#L4">4</a></span>
<span id="L5" class="lineno"><a class="lineno" href="#L5">5</a></span>
<span id="L6" class="lineno"><a class="lineno" href="#L6">6</a></span>
<span id="L7" class="lineno"><a class="lineno" href="#L7">7</a></span>
<span id="L8" class="lineno"><a class="lineno" href="#L8">8</a></span>
<span id="L9" class="lineno"><a class="lineno" href="#L9">9</a></span>
<span id="L10" class="lineno"><a class="lineno" href="#L10">10</a></span>
<span id="L11" class="lineno"><a class="lineno" href="#L11">11</a></span>
<span id="L12" class="lineno"><a class="lineno" href="#L12">12</a></span>
<span id="L13" class="lineno"><a class="lineno" href="#L13">13</a></span>
<span id="L14" class="lineno"><a class="lineno" href="#L14">14</a></span>
<span id="L15" class="lineno"><a class="lineno" href="#L15">15</a></span>
<span id="L16" class="lineno"><a class="lineno" href="#L16">16</a></span>
<span id="L17" class="lineno"><a class="lineno" href="#L17">17</a></span>
<span id="L18" class="lineno"><a class="lineno" href="#L18">18</a></span>
<span id="L19" class="lineno"><a class="lineno" href="#L19">19</a></span>
<span id="L20" class="lineno"><a class="lineno" href="#L20">20</a></span>
<span id="L21" class="lineno"><a class="lineno" href="#L21">21</a></span>
<span id="L22" class="lineno"><a class="lineno" href="#L22">22</a></span>
<span id="L23" class="lineno"><a class="lineno" href="#L23">23</a></span>
<span id="L24" class="lineno"><a class="lineno" href="#L24">24</a></span>
<span id="L25" class="lineno"><a class="lineno" href="#L25">25</a></span>
<span id="L26" class="lineno"><a class="lineno" href="#L26">26</a></span>
<span id="L27" class="lineno"><a class="lineno" href="#L27">27</a></span>
<span id="L28" class="lineno"><a class="lineno" href="#L28">28</a></span>
<span id="L29" class="lineno"><a class="lineno" href="#L29">29</a></span>
<span id="L30" class="lineno"><a class="lineno" href="#L30">30</a></span>
<span id="L31" class="lineno"><a class="lineno" href="#L31">31</a></span>
<span id="L32" class="lineno"><a class="lineno" href="#L32">32</a></span>
<span id="L33" class="lineno"><a class="lineno" href="#L33">33</a></span>
<span id="L34" class="lineno"><a class="lineno" href="#L34">34</a></span>
<span id="L35" class="lineno"><a class="lineno" href="#L35">35</a></span>
<span id="L36" class="lineno"><a class="lineno" href="#L36">36</a></span>
<span id="L37" class="lineno"><a class="lineno" href="#L37">37</a></span>
<span id="L38" class="lineno"><a class="lineno" href="#L38">38</a></span>
<span id="L39" class="lineno"><a class="lineno" href="#L39">39</a></span>
<span id="L40" class="lineno"><a class="lineno" href="#L40">40</a></span>
<span id="L41" class="lineno"><a class="lineno" href="#L41">41</a></span>
<span id="L42" class="lineno"><a class="lineno" href="#L42">42</a></span>
<span id="L43" class="lineno"><a class="lineno" href="#L43">43</a></span>
<span id="L44" class="lineno"><a class="lineno" href="#L44">44</a></span>
<span id="L45" class="lineno"><a class="lineno" href="#L45">45</a></span>
<span id="L46" class="lineno"><a class="lineno" href="#L46">46</a></span>
<span id="L47" class="lineno"><a class="lineno" href="#L47">47</a></span>
<span id="L48" class="lineno"><a class="lineno" href="#L48">48</a></span>
<span id="L49" class="lineno"><a class="lineno" href="#L49">49</a></span>
<span id="L50" class="lineno"><a class="lineno" href="#L50">50</a></span>
<span id="L51" class="lineno"><a class="lineno" href="#L51">51</a></span>
<span id="L52" class="lineno"><a class="lineno" href="#L52">52</a></span>
<span id="L53" class="lineno"><a class="lineno" href="#L53">53</a></span>
<span id="L54" class="lineno"><a class="lineno" href="#L54">54</a></span>
<span id="L55" class="lineno"><a class="lineno" href="#L55">55</a></span>
<span id="L56" class="lineno"><a class="lineno" href="#L56">56</a></span>
<span id="L57" class="lineno"><a class="lineno" href="#L57">57</a></span>
<span id="L58" class="lineno"><a class="lineno" href="#L58">58</a></span>
<span id="L59" class="lineno"><a class="lineno" href="#L59">59</a></span>
<span id="L60" class="lineno"><a class="lineno" href="#L60">60</a></span>
<span id="L61" class="lineno"><a class="lineno" href="#L61">61</a></span>
<span id="L62" class="lineno"><a class="lineno" href="#L62">62</a></span>
<span id="L63" class="lineno"><a class="lineno" href="#L63">63</a></span>
<span id="L64" class="lineno"><a class="lineno" href="#L64">64</a></span>
<span id="L65" class="lineno"><a class="lineno" href="#L65">65</a></span>
<span id="L66" class="lineno"><a class="lineno" href="#L66">66</a></span>
<span id="L67" class="lineno"><a class="lineno" href="#L67">67</a></span>
<span id="L68" class="lineno"><a class="lineno" href="#L68">68</a></span>
<span id="L69" class="lineno"><a class="lineno" href="#L69">69</a></span>
<span id="L70" class="lineno"><a class="lineno" href="#L70">70</a></span>
<span id="L71" class="lineno"><a class="lineno" href="#L71">71</a></span>
<span id="L72" class="lineno"><a class="lineno" href="#L72">72</a></span>
<span id="L73" class="lineno"><a class="lineno" href="#L73">73</a></span>
<span id="L74" class="lineno"><a class="lineno" href="#L74">74</a></span>
<span id="L75" class="lineno"><a class="lineno" href="#L75">75</a></span>
<span id="L76" class="lineno"><a class="lineno" href="#L76">76</a></span>
<span id="L77" class="lineno"><a class="lineno" href="#L77">77</a></span>
<span id="L78" class="lineno"><a class="lineno" href="#L78">78</a></span>
<span id="L79" class="lineno"><a class="lineno" href="#L79">79</a></span>
<span id="L80" class="lineno"><a class="lineno" href="#L80">80</a></span>
<span id="L81" class="lineno"><a class="lineno" href="#L81">81</a></span>
<span id="L82" class="lineno"><a class="lineno" href="#L82">82</a></span>
<span id="L83" class="lineno"><a class="lineno" href="#L83">83</a></span>
<span id="L84" class="lineno"><a class="lineno" href="#L84">84</a></span>
<span id="L85" class="lineno"><a class="lineno" href="#L85">85</a></span>
<span id="L86" class="lineno"><a class="lineno" href="#L86">86</a></span>
<span id="L87" class="lineno"><a class="lineno" href="#L87">87</a></span>
<span id="L88" class="lineno"><a class="lineno" href="#L88">88</a></span>
<span id="L89" class="lineno"><a class="lineno" href="#L89">89</a></span>
<span id="L90" class="lineno"><a class="lineno" href="#L90">90</a></span>
<span id="L91" class="lineno"><a class="lineno" href="#L91">91</a></span>
<span id="L92" class="lineno"><a class="lineno" href="#L92">92</a></span>
<span id="L93" class="lineno"><a class="lineno" href="#L93">93</a></span>
<span id="L94" class="lineno"><a class="lineno" href="#L94">94</a></span>
<span id="L95" class="lineno"><a class="lineno" href="#L95">95</a></span>
<span id="L96" class="lineno"><a class="lineno" href="#L96">96</a></span>
<span id="L97" class="lineno"><a class="lineno" href="#L97">97</a></span>
<span id="L98" class="lineno"><a class="lineno" href="#L98">98</a></span>
<span id="L99" class="lineno"><a class="lineno" href="#L99">99</a></span>
<span id="L100" class="lineno"><a class="lineno" href="#L100">100</a></span>
<span id="L101" class="lineno"><a class="lineno" href="#L101">101</a></span>
<span id="L102" class="lineno"><a class="lineno" href="#L102">102</a></span>
<span id="L103" class="lineno"><a class="lineno" href="#L103">103</a></span>
<span id="L104" class="lineno"><a class="lineno" href="#L104">104</a></span>
<span id="L105" class="lineno"><a class="lineno" href="#L105">105</a></span>
<span id="L106" class="lineno"><a class="lineno" href="#L106">106</a></span>
<span id="L107" class="lineno"><a class="lineno" href="#L107">107</a></span>
<span id="L108" class="lineno"><a class="lineno" href="#L108">108</a></span>
<span id="L109" class="lineno"><a class="lineno" href="#L109">109</a></span>
<span id="L110" class="lineno"><a class="lineno" href="#L110">110</a></span>
<span id="L111" class="lineno"><a class="lineno" href="#L111">111</a></span>
<span id="L112" class="lineno"><a class="lineno" href="#L112">112</a></span>
<span id="L113" class="lineno"><a class="lineno" href="#L113">113</a></span>
<span id="L114" class="lineno"><a class="lineno" href="#L114">114</a></span>
<span id="L115" class="lineno"><a class="lineno" href="#L115">115</a></span>
<span id="L116" class="lineno"><a class="lineno" href="#L116">116</a></span>
<span id="L117" class="lineno"><a class="lineno" href="#L117">117</a></span>
<span id="L118" class="lineno"><a class="lineno" href="#L118">118</a></span>
<span id="L119" class="lineno"><a class="lineno" href="#L119">119</a></span>
<span id="L120" class="lineno"><a class="lineno" href="#L120">120</a></span>
<span id="L121" class="lineno"><a class="lineno" href="#L121">121</a></span>
<span id="L122" class="lineno"><a class="lineno" href="#L122">122</a></span>
<span id="L123" class="lineno"><a class="lineno" href="#L123">123</a></span>
<span id="L124" class="lineno"><a class="lineno" href="#L124">124</a></span>
<span id="L125" class="lineno"><a class="lineno" href="#L125">125</a></span>
<span id="L126" class="lineno"><a class="lineno" href="#L126">126</a></span>
<span id="L127" class="lineno"><a class="lineno" href="#L127">127</a></span>
<span id="L128" class="lineno"><a class="lineno" href="#L128">128</a></span>
<span id="L129" class="lineno"><a class="lineno" href="#L129">129</a></span>
<span id="L130" class="lineno"><a class="lineno" href="#L130">130</a></span>
<span id="L131" class="lineno"><a class="lineno" href="#L131">131</a></span>
<span id="L132" class="lineno"><a class="lineno" href="#L132">132</a></span>
<span id="L133" class="lineno"><a class="lineno" href="#L133">133</a></span>
<span id="L134" class="lineno"><a class="lineno" href="#L134">134</a></span>
<span id="L135" class="lineno"><a class="lineno" href="#L135">135</a></span>
<span id="L136" class="lineno"><a class="lineno" href="#L136">136</a></span>
<span id="L137" class="lineno"><a class="lineno" href="#L137">137</a></span>
<span id="L138" class="lineno"><a class="lineno" href="#L138">138</a></span>
<span id="L139" class="lineno"><a class="lineno" href="#L139">139</a></span>
<span id="L140" class="lineno"><a class="lineno" href="#L140">140</a></span>
<span id="L141" class="lineno"><a class="lineno" href="#L141">141</a></span>
<span id="L142" class="lineno"><a class="lineno" href="#L142">142</a></span>
<span id="L143" class="lineno"><a class="lineno" href="#L143">143</a></span>
<span id="L144" class="lineno"><a class="lineno" href="#L144">144</a></span>
<span id="L145" class="lineno"><a class="lineno" href="#L145">145</a></span>
<span id="L146" class="lineno"><a class="lineno" href="#L146">146</a></span>
<span id="L147" class="lineno"><a class="lineno" href="#L147">147</a></span>
<span id="L148" class="lineno"><a class="lineno" href="#L148">148</a></span>
<span id="L149" class="lineno"><a class="lineno" href="#L149">149</a></span>
<span id="L150" class="lineno"><a class="lineno" href="#L150">150</a></span>
<span id="L151" class="lineno"><a class="lineno" href="#L151">151</a></span>
<span id="L152" class="lineno"><a class="lineno" href="#L152">152</a></span>
<span id="L153" class="lineno"><a class="lineno" href="#L153">153</a></span>
<span id="L154" class="lineno"><a class="lineno" href="#L154">154</a></span>
<span id="L155" class="lineno"><a class="lineno" href="#L155">155</a></span>
<span id="L156" class="lineno"><a class="lineno" href="#L156">156</a></span>
<span id="L157" class="lineno"><a class="lineno" href="#L157">157</a></span>
<span id="L158" class="lineno"><a class="lineno" href="#L158">158</a></span>
<span id="L159" class="lineno"><a class="lineno" href="#L159">159</a></span>
<span id="L160" class="lineno"><a class="lineno" href="#L160">160</a></span>
<span id="L161" class="lineno"><a class="lineno" href="#L161">161</a></span>
<span id="L162" class="lineno"><a class="lineno" href="#L162">162</a></span>
<span id="L163" class="lineno"><a class="lineno" href="#L163">163</a></span>
<span id="L164" class="lineno"><a class="lineno" href="#L164">164</a></span>
<span id="L165" class="lineno"><a class="lineno" href="#L165">165</a></span>
<span id="L166" class="lineno"><a class="lineno" href="#L166">166</a></span>
<span id="L167" class="lineno"><a class="lineno" href="#L167">167</a></span>
<span id="L168" class="lineno"><a class="lineno" href="#L168">168</a></span>
<span id="L169" class="lineno"><a class="lineno" href="#L169">169</a></span>
<span id="L170" class="lineno"><a class="lineno" href="#L170">170</a></span>
<span id="L171" class="lineno"><a class="lineno" href="#L171">171</a></span>
<span id="L172" class="lineno"><a class="lineno" href="#L172">172</a></span>
<span id="L173" class="lineno"><a class="lineno" href="#L173">173</a></span>
<span id="L174" class="lineno"><a class="lineno" href="#L174">174</a></span>
<span id="L175" class="lineno"><a class="lineno" href="#L175">175</a></span>
<span id="L176" class="lineno"><a class="lineno" href="#L176">176</a></span>
<span id="L177" class="lineno"><a class="lineno" href="#L177">177</a></span>
<span id="L178" class="lineno"><a class="lineno" href="#L178">178</a></span>
<span id="L179" class="lineno"><a class="lineno" href="#L179">179</a></span>
<span id="L180" class="lineno"><a class="lineno" href="#L180">180</a></span>
<span id="L181" class="lineno"><a class="lineno" href="#L181">181</a></span>
<span id="L182" class="lineno"><a class="lineno" href="#L182">182</a></span>
<span id="L183" class="lineno"><a class="lineno" href="#L183">183</a></span>
<span id="L184" class="lineno"><a class="lineno" href="#L184">184</a></span>
<span id="L185" class="lineno"><a class="lineno" href="#L185">185</a></span>
<span id="L186" class="lineno"><a class="lineno" href="#L186">186</a></span>
<span id="L187" class="lineno"><a class="lineno" href="#L187">187</a></span>
<span id="L188" class="lineno"><a class="lineno" href="#L188">188</a></span>
<span id="L189" class="lineno"><a class="lineno" href="#L189">189</a></span>
<span id="L190" class="lineno"><a class="lineno" href="#L190">190</a></span>
<span id="L191" class="lineno"><a class="lineno" href="#L191">191</a></span>
<span id="L192" class="lineno"><a class="lineno" href="#L192">192</a></span>
<span id="L193" class="lineno"><a class="lineno" href="#L193">193</a></span>
<span id="L194" class="lineno"><a class="lineno" href="#L194">194</a></span>
<span id="L195" class="lineno"><a class="lineno" href="#L195">195</a></span>
<span id="L196" class="lineno"><a class="lineno" href="#L196">196</a></span>
<span id="L197" class="lineno"><a class="lineno" href="#L197">197</a></span>
<span id="L198" class="lineno"><a class="lineno" href="#L198">198</a></span>
<span id="L199" class="lineno"><a class="lineno" href="#L199">199</a></span>
<span id="L200" class="lineno"><a class="lineno" href="#L200">200</a></span>
<span id="L201" class="lineno"><a class="lineno" href="#L201">201</a></span>
<span id="L202" class="lineno"><a class="lineno" href="#L202">202</a></span>
<span id="L203" class="lineno"><a class="lineno" href="#L203">203</a></span>
<span id="L204" class="lineno"><a class="lineno" href="#L204">204</a></span>
<span id="L205" class="lineno"><a class="lineno" href="#L205">205</a></span>
<span id="L206" class="lineno"><a class="lineno" href="#L206">206</a></span>
<span id="L207" class="lineno"><a class="lineno" href="#L207">207</a></span>
<span id="L208" class="lineno"><a class="lineno" href="#L208">208</a></span>
<span id="L209" class="lineno"><a class="lineno" href="#L209">209</a></span>
<span id="L210" class="lineno"><a class="lineno" href="#L210">210</a></span>
<span id="L211" class="lineno"><a class="lineno" href="#L211">211</a></span>
<span id="L212" class="lineno"><a class="lineno" href="#L212">212</a></span>
<span id="L213" class="lineno"><a class="lineno" href="#L213">213</a></span>
<span id="L214" class="lineno"><a class="lineno" href="#L214">214</a></span>
<span id="L215" class="lineno"><a class="lineno" href="#L215">215</a></span>
<span id="L216" class="lineno"><a class="lineno" href="#L216">216</a></span>
<span id="L217" class="lineno"><a class="lineno" href="#L217">217</a></span>
<span id="L218" class="lineno"><a class="lineno" href="#L218">218</a></span>
<span id="L219" class="lineno"><a class="lineno" href="#L219">219</a></span>
<span id="L220" class="lineno"><a class="lineno" href="#L220">220</a></span>
<span id="L221" class="lineno"><a class="lineno" href="#L221">221</a></span>
<span id="L222" class="lineno"><a class="lineno" href="#L222">222</a></span>
<span id="L223" class="lineno"><a class="lineno" href="#L223">223</a></span>
<span id="L224" class="lineno"><a class="lineno" href="#L224">224</a></span>
<span id="L225" class="lineno"><a class="lineno" href="#L225">225</a></span>
<span id="L226" class="lineno"><a class="lineno" href="#L226">226</a></span>
<span id="L227" class="lineno"><a class="lineno" href="#L227">227</a></span>
<span id="L228" class="lineno"><a class="lineno" href="#L228">228</a></span>
<span id="L229" class="lineno"><a class="lineno" href="#L229">229</a></span>
<span id="L230" class="lineno"><a class="lineno" href="#L230">230</a></span>
<span id="L231" class="lineno"><a class="lineno" href="#L231">231</a></span>
<span id="L232" class="lineno"><a class="lineno" href="#L232">232</a></span>
<span id="L233" class="lineno"><a class="lineno" href="#L233">233</a></span>
<span id="L234" class="lineno"><a class="lineno" href="#L234">234</a></span>
<span id="L235" class="lineno"><a class="lineno" href="#L235">235</a></span>
<span id="L236" class="lineno"><a class="lineno" href="#L236">236</a></span>
<span id="L237" class="lineno"><a class="lineno" href="#L237">237</a></span>
<span id="L238" class="lineno"><a class="lineno" href="#L238">238</a></span>
<span id="L239" class="lineno"><a class="lineno" href="#L239">239</a></span>
<span id="L240" class="lineno"><a class="lineno" href="#L240">240</a></span>
<span id="L241" class="lineno"><a class="lineno" href="#L241">241</a></span>
<span id="L242" class="lineno"><a class="lineno" href="#L242">242</a></span>
<span id="L243" class="lineno"><a class="lineno" href="#L243">243</a></span>
<span id="L244" class="lineno"><a class="lineno" href="#L244">244</a></span>
<span id="L245" class="lineno"><a class="lineno" href="#L245">245</a></span>
<span id="L246" class="lineno"><a class="lineno" href="#L246">246</a></span>
<span id="L247" class="lineno"><a class="lineno" href="#L247">247</a></span>
<span id="L248" class="lineno"><a class="lineno" href="#L248">248</a></span>
<span id="L249" class="lineno"><a class="lineno" href="#L249">249</a></span>
<span id="L250" class="lineno"><a class="lineno" href="#L250">250</a></span>
<span id="L251" class="lineno"><a class="lineno" href="#L251">251</a></span>
<span id="L252" class="lineno"><a class="lineno" href="#L252">252</a></span>
<span id="L253" class="lineno"><a class="lineno" href="#L253">253</a></span>
<span id="L254" class="lineno"><a class="lineno" href="#L254">254</a></span>
<span id="L255" class="lineno"><a class="lineno" href="#L255">255</a></span>
<span id="L256" class="lineno"><a class="lineno" href="#L256">256</a></span>
<span id="L257" class="lineno"><a class="lineno" href="#L257">257</a></span>
<span id="L258" class="lineno"><a class="lineno" href="#L258">258</a></span>
<span id="L259" class="lineno"><a class="lineno" href="#L259">259</a></span>
<span id="L260" class="lineno"><a class="lineno" href="#L260">260</a></span>
<span id="L261" class="lineno"><a class="lineno" href="#L261">261</a></span>
<span id="L262" class="lineno"><a class="lineno" href="#L262">262</a></span>
<span id="L263" class="lineno"><a class="lineno" href="#L263">263</a></span>
<span id="L264" class="lineno"><a class="lineno" href="#L264">264</a></span>
<span id="L265" class="lineno"><a class="lineno" href="#L265">265</a></span>
<span id="L266" class="lineno"><a class="lineno" href="#L266">266</a></span>
<span id="L267" class="lineno"><a class="lineno" href="#L267">267</a></span>
<span id="L268" class="lineno"><a class="lineno" href="#L268">268</a></span>
<span id="L269" class="lineno"><a class="lineno" href="#L269">269</a></span>
<span id="L270" class="lineno"><a class="lineno" href="#L270">270</a></span>
<span id="L271" class="lineno"><a class="lineno" href="#L271">271</a></span>
<span id="L272" class="lineno"><a class="lineno" href="#L272">272</a></span>
<span id="L273" class="lineno"><a class="lineno" href="#L273">273</a></span>
<span id="L274" class="lineno"><a class="lineno" href="#L274">274</a></span>
<span id="L275" class="lineno"><a class="lineno" href="#L275">275</a></span>
<span id="L276" class="lineno"><a class="lineno" href="#L276">276</a></span>
<span id="L277" class="lineno"><a class="lineno" href="#L277">277</a></span>
<span id="L278" class="lineno"><a class="lineno" href="#L278">278</a></span>
<span id="L279" class="lineno"><a class="lineno" href="#L279">279</a></span>
<span id="L280" class="lineno"><a class="lineno" href="#L280">280</a></span>
<span id="L281" class="lineno"><a class="lineno" href="#L281">281</a></span>
<span id="L282" class="lineno"><a class="lineno" href="#L282">282</a></span>
<span id="L283" class="lineno"><a class="lineno" href="#L283">283</a></span>
<span id="L284" class="lineno"><a class="lineno" href="#L284">284</a></span>
<span id="L285" class="lineno"><a class="lineno" href="#L285">285</a></span>
<span id="L286" class="lineno"><a class="lineno" href="#L286">286</a></span>
<span id="L287" class="lineno"><a class="lineno" href="#L287">287</a></span>
<span id="L288" class="lineno"><a class="lineno" href="#L288">288</a></span>
<span id="L289" class="lineno"><a class="lineno" href="#L289">289</a></span>
<span id="L290" class="lineno"><a class="lineno" href="#L290">290</a></span>
<span id="L291" class="lineno"><a class="lineno" href="#L291">291</a></span>
<span id="L292" class="lineno"><a class="lineno" href="#L292">292</a></span>
<span id="L293" class="lineno"><a class="lineno" href="#L293">293</a></span>
<span id="L294" class="lineno"><a class="lineno" href="#L294">294</a></span>
<span id="L295" class="lineno"><a class="lineno" href="#L295">295</a></span>
<span id="L296" class="lineno"><a class="lineno" href="#L296">296</a></span>
<span id="L297" class="lineno"><a class="lineno" href="#L297">297</a></span>
<span id="L298" class="lineno"><a class="lineno" href="#L298">298</a></span>
<span id="L299" class="lineno"><a class="lineno" href="#L299">299</a></span>
<span id="L300" class="lineno"><a class="lineno" href="#L300">300</a></span>
<span id="L301" class="lineno"><a class="lineno" href="#L301">301</a></span>
<span id="L302" class="lineno"><a class="lineno" href="#L302">302</a></span>
<span id="L303" class="lineno"><a class="lineno" href="#L303">303</a></span>
<span id="L304" class="lineno"><a class="lineno" href="#L304">304</a></span>
<span id="L305" class="lineno"><a class="lineno" href="#L305">305</a></span>
</pre></td>
<td class="table-code"><pre><span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!">Configuration management using Pydantic Settings.</span>
<span class="line-empty" title="No Anys on this line!">Handles environment variables and application settings.</span>
<span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from typing import List, Optional</span>
<span class="line-precise" title="No Anys on this line!">from pydantic import Field, validator</span>
<span class="line-precise" title="No Anys on this line!">from pydantic_settings import BaseSettings, SettingsConfigDict</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class Settings(BaseSettings):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Application settings loaded from environment variables.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    model_config = SettingsConfigDict(</span>
<span class="line-precise" title="No Anys on this line!">        env_file=".env",</span>
<span class="line-precise" title="No Anys on this line!">        env_file_encoding="utf-8",</span>
<span class="line-precise" title="No Anys on this line!">        case_sensitive=True,</span>
<span class="line-precise" title="No Anys on this line!">        extra="ignore"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Application settings</span>
<span class="line-precise" title="No Anys on this line!">    APP_NAME: str = "AiLex Ad Agent System"</span>
<span class="line-precise" title="No Anys on this line!">    VERSION: str = "1.0.0"</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    ENVIRONMENT: str = Field(default="development", description="Environment: development, staging, production")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    DEBUG: bool = Field(default=False, description="Enable debug mode")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Server settings</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    HOST: str = Field(default="0.0.0.0", description="Server host")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    PORT: int = Field(default=8000, description="Server port")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # CORS settings</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    CORS_ORIGINS: List[str] = Field(</span>
<span class="line-empty" title="No Anys on this line!">        default=[</span>
<span class="line-precise" title="No Anys on this line!">            "http://localhost:3000",</span>
<span class="line-precise" title="No Anys on this line!">            "http://localhost:3001",</span>
<span class="line-precise" title="No Anys on this line!">            "https://localhost:3000",</span>
<span class="line-precise" title="No Anys on this line!">            "https://localhost:3001",</span>
<span class="line-empty" title="No Anys on this line!">        ],</span>
<span class="line-precise" title="No Anys on this line!">        description="Allowed CORS origins"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Trusted hosts for security</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    TRUSTED_HOSTS: Optional[List[str]] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Trusted hosts for security middleware"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Supabase Database settings</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    SUPABASE_URL: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Supabase project URL"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    SUPABASE_ANON_KEY: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Supabase anonymous/public key"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    SUPABASE_SERVICE_ROLE_KEY: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Supabase service role key for backend operations"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Legacy database settings (for backward compatibility)</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    DATABASE_URL: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Legacy: Supabase database URL (use SUPABASE_URL instead)"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    DATABASE_KEY: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Legacy: Supabase service key (use SUPABASE_SERVICE_ROLE_KEY instead)"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Redis settings</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    REDIS_URL: str = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default="redis://localhost:6379/0",</span>
<span class="line-precise" title="No Anys on this line!">        description="Redis connection URL"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Google Ads API settings</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    GOOGLE_ADS_DEVELOPER_TOKEN: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Google Ads Developer Token"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    GOOGLE_ADS_CLIENT_ID: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Google Ads OAuth2 Client ID"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    GOOGLE_ADS_CLIENT_SECRET: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Google Ads OAuth2 Client Secret"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    GOOGLE_ADS_REFRESH_TOKEN: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Google Ads OAuth2 Refresh Token"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    GOOGLE_ADS_CUSTOMER_ID: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Google Ads Customer ID (without dashes)"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Google Analytics settings</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    GOOGLE_ANALYTICS_PROPERTY_ID: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Google Analytics 4 Property ID"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    GOOGLE_ANALYTICS_CREDENTIALS_PATH: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Path to Google Analytics service account credentials"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # OpenAI settings</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    OPENAI_API_KEY: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="OpenAI API key for GPT models"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    OPENAI_MODEL: str = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default="gpt-4o",</span>
<span class="line-precise" title="No Anys on this line!">        description="Default OpenAI model to use"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Google Gemini settings</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    GEMINI_API_KEY: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Google Gemini API key"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Pinecone settings</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    PINECONE_API_KEY: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Pinecone API key for vector database"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    PINECONE_ENVIRONMENT: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Pinecone environment"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    PINECONE_INDEX_NAME: str = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default="ailex-memory",</span>
<span class="line-precise" title="No Anys on this line!">        description="Pinecone index name"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Celery settings</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    CELERY_BROKER_URL: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Celery broker URL (Redis)"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    CELERY_RESULT_BACKEND: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Celery result backend URL"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Logging settings</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    LOG_LEVEL: str = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default="INFO",</span>
<span class="line-precise" title="No Anys on this line!">        description="Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Sentry settings for error tracking</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    SENTRY_DSN: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Sentry DSN for error tracking"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    SENTRY_TRACES_SAMPLE_RATE: float = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=0.1,</span>
<span class="line-precise" title="No Anys on this line!">        description="Sentry traces sample rate (0.0 to 1.0)"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    SENTRY_PROFILES_SAMPLE_RATE: float = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=0.1,</span>
<span class="line-precise" title="No Anys on this line!">        description="Sentry profiles sample rate (0.0 to 1.0)"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Phoenix Tracing settings</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    PHOENIX_COLLECTOR_ENDPOINT: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Phoenix collector endpoint for tracing"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    PHOENIX_PROJECT_NAME: str = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default="ailex-ad-agents",</span>
<span class="line-precise" title="No Anys on this line!">        description="Phoenix project name"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Rate limiting settings</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    RATE_LIMIT_REQUESTS_PER_MINUTE: int = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=100,</span>
<span class="line-precise" title="No Anys on this line!">        description="Rate limit: requests per minute per IP"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Campaign optimization settings</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    MAX_BID_ADJUSTMENT_PERCENT: float = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=20.0,</span>
<span class="line-precise" title="No Anys on this line!">        description="Maximum bid adjustment percentage"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    MAX_BUDGET_ADJUSTMENT_PERCENT_PER_HOUR: float = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=5.0,</span>
<span class="line-precise" title="No Anys on this line!">        description="Maximum budget adjustment percentage per hour"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    OPTIMIZATION_INTERVAL_MINUTES: int = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=10,</span>
<span class="line-precise" title="No Anys on this line!">        description="Optimization interval in minutes"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # A/B Testing settings</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    MIN_SAMPLE_SIZE: int = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=100,</span>
<span class="line-precise" title="No Anys on this line!">        description="Minimum sample size for A/B tests"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    STATISTICAL_SIGNIFICANCE_THRESHOLD: float = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=0.05,</span>
<span class="line-precise" title="No Anys on this line!">        description="P-value threshold for statistical significance"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # GDPR and Compliance settings</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    GDPR_ENABLED: bool = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=True,</span>
<span class="line-precise" title="No Anys on this line!">        description="Enable GDPR compliance features"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    DATA_RETENTION_DAYS: int = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=365,</span>
<span class="line-precise" title="No Anys on this line!">        description="Data retention period in days"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Security settings</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    SECRET_KEY: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Secret key for session middleware and other security features"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Clerk Authentication settings</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    CLERK_PUBLISHABLE_KEY: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Clerk publishable key for frontend authentication"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    CLERK_SECRET_KEY: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Clerk secret key for backend authentication"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    CLERK_JWT_TEMPLATE: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Clerk JWT template name"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Webhooks and notifications</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    SLACK_WEBHOOK_URL: Optional[str] = Field(</span>
<span class="line-precise" title="No Anys on this line!">        default=None,</span>
<span class="line-precise" title="No Anys on this line!">        description="Slack webhook URL for notifications"</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">    @validator("ENVIRONMENT")</span>
<span class="line-any" title="No Anys on this line!">    def validate_environment(cls, v):</span>
<span class="line-empty" title="No Anys on this line!">        """Validate environment setting."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        allowed_environments = ["development", "staging", "production"]</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">        if v not in allowed_environments:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x6)">            raise ValueError(f"Environment must be one of: {allowed_environments}")</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        return v</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">    @validator("LOG_LEVEL")</span>
<span class="line-any" title="No Anys on this line!">    def validate_log_level(cls, v):</span>
<span class="line-empty" title="No Anys on this line!">        """Validate log level setting."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x4)">        if v.upper() not in allowed_levels:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x6)">            raise ValueError(f"Log level must be one of: {allowed_levels}")</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">        return v.upper()</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">    @validator("CORS_ORIGINS", pre=True)</span>
<span class="line-any" title="No Anys on this line!">    def parse_cors_origins(cls, v):</span>
<span class="line-empty" title="No Anys on this line!">        """Parse CORS origins from string or list."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">        if isinstance(v, str):</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x6)">            return [origin.strip() for origin in v.split(",")]</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        return v</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">    @validator("TRUSTED_HOSTS", pre=True)</span>
<span class="line-any" title="No Anys on this line!">    def parse_trusted_hosts(cls, v):</span>
<span class="line-empty" title="No Anys on this line!">        """Parse trusted hosts from string or list."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">        if isinstance(v, str):</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x6)">            return [host.strip() for host in v.split(",")]</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        return v</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    @property</span>
<span class="line-precise" title="No Anys on this line!">    def is_development(self) -&gt; bool:</span>
<span class="line-empty" title="No Anys on this line!">        """Check if running in development mode."""</span>
<span class="line-precise" title="No Anys on this line!">        return self.ENVIRONMENT == "development"</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    @property</span>
<span class="line-precise" title="No Anys on this line!">    def is_production(self) -&gt; bool:</span>
<span class="line-empty" title="No Anys on this line!">        """Check if running in production mode."""</span>
<span class="line-precise" title="No Anys on this line!">        return self.ENVIRONMENT == "production"</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    @property</span>
<span class="line-precise" title="No Anys on this line!">    def is_staging(self) -&gt; bool:</span>
<span class="line-empty" title="No Anys on this line!">        """Check if running in staging mode."""</span>
<span class="line-precise" title="No Anys on this line!">        return self.ENVIRONMENT == "staging"</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    @property</span>
<span class="line-precise" title="No Anys on this line!">    def database_url(self) -&gt; Optional[str]:</span>
<span class="line-empty" title="No Anys on this line!">        """Get the database URL, preferring new SUPABASE_URL over legacy DATABASE_URL."""</span>
<span class="line-precise" title="No Anys on this line!">        return self.SUPABASE_URL or self.DATABASE_URL</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    @property</span>
<span class="line-precise" title="No Anys on this line!">    def database_service_key(self) -&gt; Optional[str]:</span>
<span class="line-empty" title="No Anys on this line!">        """Get the database service key, preferring new SUPABASE_SERVICE_ROLE_KEY over legacy DATABASE_KEY."""</span>
<span class="line-precise" title="No Anys on this line!">        return self.SUPABASE_SERVICE_ROLE_KEY or self.DATABASE_KEY</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"># Global settings instance</span>
<span class="line-precise" title="No Anys on this line!">settings = Settings()</span>
</pre></td>
</tr></tbody>
</table>
</body>
</html>
