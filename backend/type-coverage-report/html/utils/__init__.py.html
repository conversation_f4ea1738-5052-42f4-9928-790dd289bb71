<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../mypy-html.css">
</head>
<body>
<h2>utils</h2>
<table>
<caption>utils/__init__.py</caption>
<tbody><tr>
<td class="table-lines"><pre><span id="L1" class="lineno"><a class="lineno" href="#L1">1</a></span>
<span id="L2" class="lineno"><a class="lineno" href="#L2">2</a></span>
<span id="L3" class="lineno"><a class="lineno" href="#L3">3</a></span>
<span id="L4" class="lineno"><a class="lineno" href="#L4">4</a></span>
</pre></td>
<td class="table-code"><pre><span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!">Utilities package for the AiLex Ad Agent System.</span>
<span class="line-empty" title="No Anys on this line!">Contains common utilities, logging, error handling, and helper functions.</span>
<span class="line-empty" title="No Anys on this line!">"""</span>
</pre></td>
</tr></tbody>
</table>
</body>
</html>
