<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../mypy-html.css">
</head>
<body>
<h2>test_standalone</h2>
<table>
<caption>test_standalone.py</caption>
<tbody><tr>
<td class="table-lines"><pre><span id="L1" class="lineno"><a class="lineno" href="#L1">1</a></span>
<span id="L2" class="lineno"><a class="lineno" href="#L2">2</a></span>
<span id="L3" class="lineno"><a class="lineno" href="#L3">3</a></span>
<span id="L4" class="lineno"><a class="lineno" href="#L4">4</a></span>
<span id="L5" class="lineno"><a class="lineno" href="#L5">5</a></span>
<span id="L6" class="lineno"><a class="lineno" href="#L6">6</a></span>
<span id="L7" class="lineno"><a class="lineno" href="#L7">7</a></span>
<span id="L8" class="lineno"><a class="lineno" href="#L8">8</a></span>
<span id="L9" class="lineno"><a class="lineno" href="#L9">9</a></span>
<span id="L10" class="lineno"><a class="lineno" href="#L10">10</a></span>
<span id="L11" class="lineno"><a class="lineno" href="#L11">11</a></span>
<span id="L12" class="lineno"><a class="lineno" href="#L12">12</a></span>
<span id="L13" class="lineno"><a class="lineno" href="#L13">13</a></span>
<span id="L14" class="lineno"><a class="lineno" href="#L14">14</a></span>
<span id="L15" class="lineno"><a class="lineno" href="#L15">15</a></span>
<span id="L16" class="lineno"><a class="lineno" href="#L16">16</a></span>
<span id="L17" class="lineno"><a class="lineno" href="#L17">17</a></span>
<span id="L18" class="lineno"><a class="lineno" href="#L18">18</a></span>
<span id="L19" class="lineno"><a class="lineno" href="#L19">19</a></span>
<span id="L20" class="lineno"><a class="lineno" href="#L20">20</a></span>
<span id="L21" class="lineno"><a class="lineno" href="#L21">21</a></span>
<span id="L22" class="lineno"><a class="lineno" href="#L22">22</a></span>
<span id="L23" class="lineno"><a class="lineno" href="#L23">23</a></span>
<span id="L24" class="lineno"><a class="lineno" href="#L24">24</a></span>
<span id="L25" class="lineno"><a class="lineno" href="#L25">25</a></span>
<span id="L26" class="lineno"><a class="lineno" href="#L26">26</a></span>
<span id="L27" class="lineno"><a class="lineno" href="#L27">27</a></span>
<span id="L28" class="lineno"><a class="lineno" href="#L28">28</a></span>
<span id="L29" class="lineno"><a class="lineno" href="#L29">29</a></span>
<span id="L30" class="lineno"><a class="lineno" href="#L30">30</a></span>
<span id="L31" class="lineno"><a class="lineno" href="#L31">31</a></span>
<span id="L32" class="lineno"><a class="lineno" href="#L32">32</a></span>
<span id="L33" class="lineno"><a class="lineno" href="#L33">33</a></span>
<span id="L34" class="lineno"><a class="lineno" href="#L34">34</a></span>
<span id="L35" class="lineno"><a class="lineno" href="#L35">35</a></span>
<span id="L36" class="lineno"><a class="lineno" href="#L36">36</a></span>
<span id="L37" class="lineno"><a class="lineno" href="#L37">37</a></span>
<span id="L38" class="lineno"><a class="lineno" href="#L38">38</a></span>
<span id="L39" class="lineno"><a class="lineno" href="#L39">39</a></span>
<span id="L40" class="lineno"><a class="lineno" href="#L40">40</a></span>
<span id="L41" class="lineno"><a class="lineno" href="#L41">41</a></span>
<span id="L42" class="lineno"><a class="lineno" href="#L42">42</a></span>
<span id="L43" class="lineno"><a class="lineno" href="#L43">43</a></span>
<span id="L44" class="lineno"><a class="lineno" href="#L44">44</a></span>
<span id="L45" class="lineno"><a class="lineno" href="#L45">45</a></span>
<span id="L46" class="lineno"><a class="lineno" href="#L46">46</a></span>
<span id="L47" class="lineno"><a class="lineno" href="#L47">47</a></span>
<span id="L48" class="lineno"><a class="lineno" href="#L48">48</a></span>
<span id="L49" class="lineno"><a class="lineno" href="#L49">49</a></span>
<span id="L50" class="lineno"><a class="lineno" href="#L50">50</a></span>
<span id="L51" class="lineno"><a class="lineno" href="#L51">51</a></span>
<span id="L52" class="lineno"><a class="lineno" href="#L52">52</a></span>
<span id="L53" class="lineno"><a class="lineno" href="#L53">53</a></span>
<span id="L54" class="lineno"><a class="lineno" href="#L54">54</a></span>
</pre></td>
<td class="table-code"><pre><span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!">Standalone test to verify our test structure works.</span>
<span class="line-empty" title="No Anys on this line!">This test doesn't require external dependencies.</span>
<span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">import sys</span>
<span class="line-precise" title="No Anys on this line!">import os</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"># Add the backend directory to Python path</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">sys.path.insert(0, os.path.join(os.path.dirname(__file__)))</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from models.common import BaseResponse, Currency, Language</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="No Anys on this line!">def test_base_response_creation():</span>
<span class="line-empty" title="No Anys on this line!">    """Test that we can create a BaseResponse."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)
Omitted Generics (x4)
Error (x2)">    response = BaseResponse[dict](</span>
<span class="line-any" title="No Anys on this line!">        success=True,</span>
<span class="line-any" title="No Anys on this line!">        message="Test successful",</span>
<span class="line-any" title="No Anys on this line!">        data={"key": "value"}</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">    assert response.success is True</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">    assert response.message == "Test successful"</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">    assert response.data == {"key": "value"}</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">    print("✓ BaseResponse test passed")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="No Anys on this line!">def test_currency_enum():</span>
<span class="line-empty" title="No Anys on this line!">    """Test Currency enum."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">    assert Currency.USD == "USD"</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">    assert Currency.EUR == "EUR"</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">    print("✓ Currency enum test passed")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="No Anys on this line!">def test_language_enum():</span>
<span class="line-empty" title="No Anys on this line!">    """Test Language enum."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">    assert Language.ENGLISH == "en"</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">    assert Language.SPANISH == "es"</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">    print("✓ Language enum test passed")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">if __name__ == "__main__":</span>
<span class="line-precise" title="No Anys on this line!">    print("Running standalone tests...")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    try:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">        test_base_response_creation()</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">        test_currency_enum()</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">        test_language_enum()</span>
<span class="line-precise" title="No Anys on this line!">        print("\n🎉 All standalone tests passed!")</span>
<span class="line-precise" title="No Anys on this line!">        print("✅ Test structure is working correctly")</span>
<span class="line-precise" title="No Anys on this line!">    except Exception as e:</span>
<span class="line-precise" title="No Anys on this line!">        print(f"\n❌ Test failed: {e}")</span>
<span class="line-precise" title="No Anys on this line!">        sys.exit(1)</span>
</pre></td>
</tr></tbody>
</table>
</body>
</html>
