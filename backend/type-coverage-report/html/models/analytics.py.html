<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../mypy-html.css">
</head>
<body>
<h2>models.analytics</h2>
<table>
<caption>models/analytics.py</caption>
<tbody><tr>
<td class="table-lines"><pre><span id="L1" class="lineno"><a class="lineno" href="#L1">1</a></span>
<span id="L2" class="lineno"><a class="lineno" href="#L2">2</a></span>
<span id="L3" class="lineno"><a class="lineno" href="#L3">3</a></span>
<span id="L4" class="lineno"><a class="lineno" href="#L4">4</a></span>
<span id="L5" class="lineno"><a class="lineno" href="#L5">5</a></span>
<span id="L6" class="lineno"><a class="lineno" href="#L6">6</a></span>
<span id="L7" class="lineno"><a class="lineno" href="#L7">7</a></span>
<span id="L8" class="lineno"><a class="lineno" href="#L8">8</a></span>
<span id="L9" class="lineno"><a class="lineno" href="#L9">9</a></span>
<span id="L10" class="lineno"><a class="lineno" href="#L10">10</a></span>
<span id="L11" class="lineno"><a class="lineno" href="#L11">11</a></span>
<span id="L12" class="lineno"><a class="lineno" href="#L12">12</a></span>
<span id="L13" class="lineno"><a class="lineno" href="#L13">13</a></span>
<span id="L14" class="lineno"><a class="lineno" href="#L14">14</a></span>
<span id="L15" class="lineno"><a class="lineno" href="#L15">15</a></span>
<span id="L16" class="lineno"><a class="lineno" href="#L16">16</a></span>
<span id="L17" class="lineno"><a class="lineno" href="#L17">17</a></span>
<span id="L18" class="lineno"><a class="lineno" href="#L18">18</a></span>
<span id="L19" class="lineno"><a class="lineno" href="#L19">19</a></span>
<span id="L20" class="lineno"><a class="lineno" href="#L20">20</a></span>
<span id="L21" class="lineno"><a class="lineno" href="#L21">21</a></span>
<span id="L22" class="lineno"><a class="lineno" href="#L22">22</a></span>
<span id="L23" class="lineno"><a class="lineno" href="#L23">23</a></span>
<span id="L24" class="lineno"><a class="lineno" href="#L24">24</a></span>
<span id="L25" class="lineno"><a class="lineno" href="#L25">25</a></span>
<span id="L26" class="lineno"><a class="lineno" href="#L26">26</a></span>
<span id="L27" class="lineno"><a class="lineno" href="#L27">27</a></span>
<span id="L28" class="lineno"><a class="lineno" href="#L28">28</a></span>
<span id="L29" class="lineno"><a class="lineno" href="#L29">29</a></span>
<span id="L30" class="lineno"><a class="lineno" href="#L30">30</a></span>
<span id="L31" class="lineno"><a class="lineno" href="#L31">31</a></span>
<span id="L32" class="lineno"><a class="lineno" href="#L32">32</a></span>
<span id="L33" class="lineno"><a class="lineno" href="#L33">33</a></span>
<span id="L34" class="lineno"><a class="lineno" href="#L34">34</a></span>
<span id="L35" class="lineno"><a class="lineno" href="#L35">35</a></span>
<span id="L36" class="lineno"><a class="lineno" href="#L36">36</a></span>
<span id="L37" class="lineno"><a class="lineno" href="#L37">37</a></span>
<span id="L38" class="lineno"><a class="lineno" href="#L38">38</a></span>
<span id="L39" class="lineno"><a class="lineno" href="#L39">39</a></span>
<span id="L40" class="lineno"><a class="lineno" href="#L40">40</a></span>
<span id="L41" class="lineno"><a class="lineno" href="#L41">41</a></span>
<span id="L42" class="lineno"><a class="lineno" href="#L42">42</a></span>
<span id="L43" class="lineno"><a class="lineno" href="#L43">43</a></span>
<span id="L44" class="lineno"><a class="lineno" href="#L44">44</a></span>
<span id="L45" class="lineno"><a class="lineno" href="#L45">45</a></span>
<span id="L46" class="lineno"><a class="lineno" href="#L46">46</a></span>
<span id="L47" class="lineno"><a class="lineno" href="#L47">47</a></span>
<span id="L48" class="lineno"><a class="lineno" href="#L48">48</a></span>
<span id="L49" class="lineno"><a class="lineno" href="#L49">49</a></span>
<span id="L50" class="lineno"><a class="lineno" href="#L50">50</a></span>
<span id="L51" class="lineno"><a class="lineno" href="#L51">51</a></span>
<span id="L52" class="lineno"><a class="lineno" href="#L52">52</a></span>
<span id="L53" class="lineno"><a class="lineno" href="#L53">53</a></span>
<span id="L54" class="lineno"><a class="lineno" href="#L54">54</a></span>
<span id="L55" class="lineno"><a class="lineno" href="#L55">55</a></span>
<span id="L56" class="lineno"><a class="lineno" href="#L56">56</a></span>
<span id="L57" class="lineno"><a class="lineno" href="#L57">57</a></span>
<span id="L58" class="lineno"><a class="lineno" href="#L58">58</a></span>
<span id="L59" class="lineno"><a class="lineno" href="#L59">59</a></span>
<span id="L60" class="lineno"><a class="lineno" href="#L60">60</a></span>
<span id="L61" class="lineno"><a class="lineno" href="#L61">61</a></span>
<span id="L62" class="lineno"><a class="lineno" href="#L62">62</a></span>
<span id="L63" class="lineno"><a class="lineno" href="#L63">63</a></span>
<span id="L64" class="lineno"><a class="lineno" href="#L64">64</a></span>
<span id="L65" class="lineno"><a class="lineno" href="#L65">65</a></span>
<span id="L66" class="lineno"><a class="lineno" href="#L66">66</a></span>
<span id="L67" class="lineno"><a class="lineno" href="#L67">67</a></span>
<span id="L68" class="lineno"><a class="lineno" href="#L68">68</a></span>
<span id="L69" class="lineno"><a class="lineno" href="#L69">69</a></span>
<span id="L70" class="lineno"><a class="lineno" href="#L70">70</a></span>
<span id="L71" class="lineno"><a class="lineno" href="#L71">71</a></span>
<span id="L72" class="lineno"><a class="lineno" href="#L72">72</a></span>
<span id="L73" class="lineno"><a class="lineno" href="#L73">73</a></span>
<span id="L74" class="lineno"><a class="lineno" href="#L74">74</a></span>
<span id="L75" class="lineno"><a class="lineno" href="#L75">75</a></span>
<span id="L76" class="lineno"><a class="lineno" href="#L76">76</a></span>
<span id="L77" class="lineno"><a class="lineno" href="#L77">77</a></span>
<span id="L78" class="lineno"><a class="lineno" href="#L78">78</a></span>
<span id="L79" class="lineno"><a class="lineno" href="#L79">79</a></span>
<span id="L80" class="lineno"><a class="lineno" href="#L80">80</a></span>
<span id="L81" class="lineno"><a class="lineno" href="#L81">81</a></span>
<span id="L82" class="lineno"><a class="lineno" href="#L82">82</a></span>
<span id="L83" class="lineno"><a class="lineno" href="#L83">83</a></span>
<span id="L84" class="lineno"><a class="lineno" href="#L84">84</a></span>
<span id="L85" class="lineno"><a class="lineno" href="#L85">85</a></span>
<span id="L86" class="lineno"><a class="lineno" href="#L86">86</a></span>
<span id="L87" class="lineno"><a class="lineno" href="#L87">87</a></span>
<span id="L88" class="lineno"><a class="lineno" href="#L88">88</a></span>
<span id="L89" class="lineno"><a class="lineno" href="#L89">89</a></span>
<span id="L90" class="lineno"><a class="lineno" href="#L90">90</a></span>
<span id="L91" class="lineno"><a class="lineno" href="#L91">91</a></span>
<span id="L92" class="lineno"><a class="lineno" href="#L92">92</a></span>
<span id="L93" class="lineno"><a class="lineno" href="#L93">93</a></span>
<span id="L94" class="lineno"><a class="lineno" href="#L94">94</a></span>
<span id="L95" class="lineno"><a class="lineno" href="#L95">95</a></span>
<span id="L96" class="lineno"><a class="lineno" href="#L96">96</a></span>
<span id="L97" class="lineno"><a class="lineno" href="#L97">97</a></span>
<span id="L98" class="lineno"><a class="lineno" href="#L98">98</a></span>
<span id="L99" class="lineno"><a class="lineno" href="#L99">99</a></span>
<span id="L100" class="lineno"><a class="lineno" href="#L100">100</a></span>
<span id="L101" class="lineno"><a class="lineno" href="#L101">101</a></span>
<span id="L102" class="lineno"><a class="lineno" href="#L102">102</a></span>
<span id="L103" class="lineno"><a class="lineno" href="#L103">103</a></span>
<span id="L104" class="lineno"><a class="lineno" href="#L104">104</a></span>
<span id="L105" class="lineno"><a class="lineno" href="#L105">105</a></span>
<span id="L106" class="lineno"><a class="lineno" href="#L106">106</a></span>
<span id="L107" class="lineno"><a class="lineno" href="#L107">107</a></span>
<span id="L108" class="lineno"><a class="lineno" href="#L108">108</a></span>
<span id="L109" class="lineno"><a class="lineno" href="#L109">109</a></span>
<span id="L110" class="lineno"><a class="lineno" href="#L110">110</a></span>
<span id="L111" class="lineno"><a class="lineno" href="#L111">111</a></span>
<span id="L112" class="lineno"><a class="lineno" href="#L112">112</a></span>
<span id="L113" class="lineno"><a class="lineno" href="#L113">113</a></span>
<span id="L114" class="lineno"><a class="lineno" href="#L114">114</a></span>
<span id="L115" class="lineno"><a class="lineno" href="#L115">115</a></span>
<span id="L116" class="lineno"><a class="lineno" href="#L116">116</a></span>
<span id="L117" class="lineno"><a class="lineno" href="#L117">117</a></span>
<span id="L118" class="lineno"><a class="lineno" href="#L118">118</a></span>
<span id="L119" class="lineno"><a class="lineno" href="#L119">119</a></span>
<span id="L120" class="lineno"><a class="lineno" href="#L120">120</a></span>
<span id="L121" class="lineno"><a class="lineno" href="#L121">121</a></span>
<span id="L122" class="lineno"><a class="lineno" href="#L122">122</a></span>
<span id="L123" class="lineno"><a class="lineno" href="#L123">123</a></span>
<span id="L124" class="lineno"><a class="lineno" href="#L124">124</a></span>
<span id="L125" class="lineno"><a class="lineno" href="#L125">125</a></span>
<span id="L126" class="lineno"><a class="lineno" href="#L126">126</a></span>
<span id="L127" class="lineno"><a class="lineno" href="#L127">127</a></span>
<span id="L128" class="lineno"><a class="lineno" href="#L128">128</a></span>
<span id="L129" class="lineno"><a class="lineno" href="#L129">129</a></span>
<span id="L130" class="lineno"><a class="lineno" href="#L130">130</a></span>
<span id="L131" class="lineno"><a class="lineno" href="#L131">131</a></span>
<span id="L132" class="lineno"><a class="lineno" href="#L132">132</a></span>
<span id="L133" class="lineno"><a class="lineno" href="#L133">133</a></span>
<span id="L134" class="lineno"><a class="lineno" href="#L134">134</a></span>
<span id="L135" class="lineno"><a class="lineno" href="#L135">135</a></span>
<span id="L136" class="lineno"><a class="lineno" href="#L136">136</a></span>
<span id="L137" class="lineno"><a class="lineno" href="#L137">137</a></span>
<span id="L138" class="lineno"><a class="lineno" href="#L138">138</a></span>
<span id="L139" class="lineno"><a class="lineno" href="#L139">139</a></span>
<span id="L140" class="lineno"><a class="lineno" href="#L140">140</a></span>
<span id="L141" class="lineno"><a class="lineno" href="#L141">141</a></span>
<span id="L142" class="lineno"><a class="lineno" href="#L142">142</a></span>
<span id="L143" class="lineno"><a class="lineno" href="#L143">143</a></span>
<span id="L144" class="lineno"><a class="lineno" href="#L144">144</a></span>
<span id="L145" class="lineno"><a class="lineno" href="#L145">145</a></span>
<span id="L146" class="lineno"><a class="lineno" href="#L146">146</a></span>
<span id="L147" class="lineno"><a class="lineno" href="#L147">147</a></span>
<span id="L148" class="lineno"><a class="lineno" href="#L148">148</a></span>
<span id="L149" class="lineno"><a class="lineno" href="#L149">149</a></span>
<span id="L150" class="lineno"><a class="lineno" href="#L150">150</a></span>
<span id="L151" class="lineno"><a class="lineno" href="#L151">151</a></span>
<span id="L152" class="lineno"><a class="lineno" href="#L152">152</a></span>
<span id="L153" class="lineno"><a class="lineno" href="#L153">153</a></span>
<span id="L154" class="lineno"><a class="lineno" href="#L154">154</a></span>
<span id="L155" class="lineno"><a class="lineno" href="#L155">155</a></span>
<span id="L156" class="lineno"><a class="lineno" href="#L156">156</a></span>
<span id="L157" class="lineno"><a class="lineno" href="#L157">157</a></span>
<span id="L158" class="lineno"><a class="lineno" href="#L158">158</a></span>
<span id="L159" class="lineno"><a class="lineno" href="#L159">159</a></span>
<span id="L160" class="lineno"><a class="lineno" href="#L160">160</a></span>
<span id="L161" class="lineno"><a class="lineno" href="#L161">161</a></span>
<span id="L162" class="lineno"><a class="lineno" href="#L162">162</a></span>
<span id="L163" class="lineno"><a class="lineno" href="#L163">163</a></span>
<span id="L164" class="lineno"><a class="lineno" href="#L164">164</a></span>
<span id="L165" class="lineno"><a class="lineno" href="#L165">165</a></span>
<span id="L166" class="lineno"><a class="lineno" href="#L166">166</a></span>
<span id="L167" class="lineno"><a class="lineno" href="#L167">167</a></span>
<span id="L168" class="lineno"><a class="lineno" href="#L168">168</a></span>
<span id="L169" class="lineno"><a class="lineno" href="#L169">169</a></span>
<span id="L170" class="lineno"><a class="lineno" href="#L170">170</a></span>
<span id="L171" class="lineno"><a class="lineno" href="#L171">171</a></span>
<span id="L172" class="lineno"><a class="lineno" href="#L172">172</a></span>
<span id="L173" class="lineno"><a class="lineno" href="#L173">173</a></span>
<span id="L174" class="lineno"><a class="lineno" href="#L174">174</a></span>
<span id="L175" class="lineno"><a class="lineno" href="#L175">175</a></span>
<span id="L176" class="lineno"><a class="lineno" href="#L176">176</a></span>
<span id="L177" class="lineno"><a class="lineno" href="#L177">177</a></span>
<span id="L178" class="lineno"><a class="lineno" href="#L178">178</a></span>
<span id="L179" class="lineno"><a class="lineno" href="#L179">179</a></span>
<span id="L180" class="lineno"><a class="lineno" href="#L180">180</a></span>
<span id="L181" class="lineno"><a class="lineno" href="#L181">181</a></span>
<span id="L182" class="lineno"><a class="lineno" href="#L182">182</a></span>
<span id="L183" class="lineno"><a class="lineno" href="#L183">183</a></span>
<span id="L184" class="lineno"><a class="lineno" href="#L184">184</a></span>
<span id="L185" class="lineno"><a class="lineno" href="#L185">185</a></span>
<span id="L186" class="lineno"><a class="lineno" href="#L186">186</a></span>
<span id="L187" class="lineno"><a class="lineno" href="#L187">187</a></span>
<span id="L188" class="lineno"><a class="lineno" href="#L188">188</a></span>
<span id="L189" class="lineno"><a class="lineno" href="#L189">189</a></span>
<span id="L190" class="lineno"><a class="lineno" href="#L190">190</a></span>
<span id="L191" class="lineno"><a class="lineno" href="#L191">191</a></span>
<span id="L192" class="lineno"><a class="lineno" href="#L192">192</a></span>
<span id="L193" class="lineno"><a class="lineno" href="#L193">193</a></span>
<span id="L194" class="lineno"><a class="lineno" href="#L194">194</a></span>
<span id="L195" class="lineno"><a class="lineno" href="#L195">195</a></span>
<span id="L196" class="lineno"><a class="lineno" href="#L196">196</a></span>
<span id="L197" class="lineno"><a class="lineno" href="#L197">197</a></span>
<span id="L198" class="lineno"><a class="lineno" href="#L198">198</a></span>
<span id="L199" class="lineno"><a class="lineno" href="#L199">199</a></span>
<span id="L200" class="lineno"><a class="lineno" href="#L200">200</a></span>
<span id="L201" class="lineno"><a class="lineno" href="#L201">201</a></span>
<span id="L202" class="lineno"><a class="lineno" href="#L202">202</a></span>
<span id="L203" class="lineno"><a class="lineno" href="#L203">203</a></span>
<span id="L204" class="lineno"><a class="lineno" href="#L204">204</a></span>
<span id="L205" class="lineno"><a class="lineno" href="#L205">205</a></span>
<span id="L206" class="lineno"><a class="lineno" href="#L206">206</a></span>
<span id="L207" class="lineno"><a class="lineno" href="#L207">207</a></span>
<span id="L208" class="lineno"><a class="lineno" href="#L208">208</a></span>
<span id="L209" class="lineno"><a class="lineno" href="#L209">209</a></span>
<span id="L210" class="lineno"><a class="lineno" href="#L210">210</a></span>
<span id="L211" class="lineno"><a class="lineno" href="#L211">211</a></span>
<span id="L212" class="lineno"><a class="lineno" href="#L212">212</a></span>
<span id="L213" class="lineno"><a class="lineno" href="#L213">213</a></span>
<span id="L214" class="lineno"><a class="lineno" href="#L214">214</a></span>
<span id="L215" class="lineno"><a class="lineno" href="#L215">215</a></span>
<span id="L216" class="lineno"><a class="lineno" href="#L216">216</a></span>
<span id="L217" class="lineno"><a class="lineno" href="#L217">217</a></span>
<span id="L218" class="lineno"><a class="lineno" href="#L218">218</a></span>
<span id="L219" class="lineno"><a class="lineno" href="#L219">219</a></span>
<span id="L220" class="lineno"><a class="lineno" href="#L220">220</a></span>
<span id="L221" class="lineno"><a class="lineno" href="#L221">221</a></span>
<span id="L222" class="lineno"><a class="lineno" href="#L222">222</a></span>
<span id="L223" class="lineno"><a class="lineno" href="#L223">223</a></span>
<span id="L224" class="lineno"><a class="lineno" href="#L224">224</a></span>
<span id="L225" class="lineno"><a class="lineno" href="#L225">225</a></span>
<span id="L226" class="lineno"><a class="lineno" href="#L226">226</a></span>
<span id="L227" class="lineno"><a class="lineno" href="#L227">227</a></span>
<span id="L228" class="lineno"><a class="lineno" href="#L228">228</a></span>
<span id="L229" class="lineno"><a class="lineno" href="#L229">229</a></span>
<span id="L230" class="lineno"><a class="lineno" href="#L230">230</a></span>
<span id="L231" class="lineno"><a class="lineno" href="#L231">231</a></span>
<span id="L232" class="lineno"><a class="lineno" href="#L232">232</a></span>
<span id="L233" class="lineno"><a class="lineno" href="#L233">233</a></span>
<span id="L234" class="lineno"><a class="lineno" href="#L234">234</a></span>
<span id="L235" class="lineno"><a class="lineno" href="#L235">235</a></span>
<span id="L236" class="lineno"><a class="lineno" href="#L236">236</a></span>
<span id="L237" class="lineno"><a class="lineno" href="#L237">237</a></span>
<span id="L238" class="lineno"><a class="lineno" href="#L238">238</a></span>
<span id="L239" class="lineno"><a class="lineno" href="#L239">239</a></span>
<span id="L240" class="lineno"><a class="lineno" href="#L240">240</a></span>
<span id="L241" class="lineno"><a class="lineno" href="#L241">241</a></span>
<span id="L242" class="lineno"><a class="lineno" href="#L242">242</a></span>
<span id="L243" class="lineno"><a class="lineno" href="#L243">243</a></span>
<span id="L244" class="lineno"><a class="lineno" href="#L244">244</a></span>
<span id="L245" class="lineno"><a class="lineno" href="#L245">245</a></span>
<span id="L246" class="lineno"><a class="lineno" href="#L246">246</a></span>
<span id="L247" class="lineno"><a class="lineno" href="#L247">247</a></span>
<span id="L248" class="lineno"><a class="lineno" href="#L248">248</a></span>
<span id="L249" class="lineno"><a class="lineno" href="#L249">249</a></span>
<span id="L250" class="lineno"><a class="lineno" href="#L250">250</a></span>
<span id="L251" class="lineno"><a class="lineno" href="#L251">251</a></span>
<span id="L252" class="lineno"><a class="lineno" href="#L252">252</a></span>
<span id="L253" class="lineno"><a class="lineno" href="#L253">253</a></span>
<span id="L254" class="lineno"><a class="lineno" href="#L254">254</a></span>
<span id="L255" class="lineno"><a class="lineno" href="#L255">255</a></span>
<span id="L256" class="lineno"><a class="lineno" href="#L256">256</a></span>
<span id="L257" class="lineno"><a class="lineno" href="#L257">257</a></span>
<span id="L258" class="lineno"><a class="lineno" href="#L258">258</a></span>
<span id="L259" class="lineno"><a class="lineno" href="#L259">259</a></span>
<span id="L260" class="lineno"><a class="lineno" href="#L260">260</a></span>
<span id="L261" class="lineno"><a class="lineno" href="#L261">261</a></span>
<span id="L262" class="lineno"><a class="lineno" href="#L262">262</a></span>
<span id="L263" class="lineno"><a class="lineno" href="#L263">263</a></span>
<span id="L264" class="lineno"><a class="lineno" href="#L264">264</a></span>
<span id="L265" class="lineno"><a class="lineno" href="#L265">265</a></span>
<span id="L266" class="lineno"><a class="lineno" href="#L266">266</a></span>
<span id="L267" class="lineno"><a class="lineno" href="#L267">267</a></span>
<span id="L268" class="lineno"><a class="lineno" href="#L268">268</a></span>
<span id="L269" class="lineno"><a class="lineno" href="#L269">269</a></span>
<span id="L270" class="lineno"><a class="lineno" href="#L270">270</a></span>
<span id="L271" class="lineno"><a class="lineno" href="#L271">271</a></span>
<span id="L272" class="lineno"><a class="lineno" href="#L272">272</a></span>
<span id="L273" class="lineno"><a class="lineno" href="#L273">273</a></span>
<span id="L274" class="lineno"><a class="lineno" href="#L274">274</a></span>
<span id="L275" class="lineno"><a class="lineno" href="#L275">275</a></span>
<span id="L276" class="lineno"><a class="lineno" href="#L276">276</a></span>
<span id="L277" class="lineno"><a class="lineno" href="#L277">277</a></span>
<span id="L278" class="lineno"><a class="lineno" href="#L278">278</a></span>
<span id="L279" class="lineno"><a class="lineno" href="#L279">279</a></span>
<span id="L280" class="lineno"><a class="lineno" href="#L280">280</a></span>
<span id="L281" class="lineno"><a class="lineno" href="#L281">281</a></span>
<span id="L282" class="lineno"><a class="lineno" href="#L282">282</a></span>
<span id="L283" class="lineno"><a class="lineno" href="#L283">283</a></span>
<span id="L284" class="lineno"><a class="lineno" href="#L284">284</a></span>
<span id="L285" class="lineno"><a class="lineno" href="#L285">285</a></span>
<span id="L286" class="lineno"><a class="lineno" href="#L286">286</a></span>
<span id="L287" class="lineno"><a class="lineno" href="#L287">287</a></span>
<span id="L288" class="lineno"><a class="lineno" href="#L288">288</a></span>
<span id="L289" class="lineno"><a class="lineno" href="#L289">289</a></span>
<span id="L290" class="lineno"><a class="lineno" href="#L290">290</a></span>
<span id="L291" class="lineno"><a class="lineno" href="#L291">291</a></span>
<span id="L292" class="lineno"><a class="lineno" href="#L292">292</a></span>
<span id="L293" class="lineno"><a class="lineno" href="#L293">293</a></span>
<span id="L294" class="lineno"><a class="lineno" href="#L294">294</a></span>
<span id="L295" class="lineno"><a class="lineno" href="#L295">295</a></span>
<span id="L296" class="lineno"><a class="lineno" href="#L296">296</a></span>
<span id="L297" class="lineno"><a class="lineno" href="#L297">297</a></span>
<span id="L298" class="lineno"><a class="lineno" href="#L298">298</a></span>
<span id="L299" class="lineno"><a class="lineno" href="#L299">299</a></span>
<span id="L300" class="lineno"><a class="lineno" href="#L300">300</a></span>
<span id="L301" class="lineno"><a class="lineno" href="#L301">301</a></span>
<span id="L302" class="lineno"><a class="lineno" href="#L302">302</a></span>
<span id="L303" class="lineno"><a class="lineno" href="#L303">303</a></span>
<span id="L304" class="lineno"><a class="lineno" href="#L304">304</a></span>
<span id="L305" class="lineno"><a class="lineno" href="#L305">305</a></span>
<span id="L306" class="lineno"><a class="lineno" href="#L306">306</a></span>
<span id="L307" class="lineno"><a class="lineno" href="#L307">307</a></span>
<span id="L308" class="lineno"><a class="lineno" href="#L308">308</a></span>
<span id="L309" class="lineno"><a class="lineno" href="#L309">309</a></span>
<span id="L310" class="lineno"><a class="lineno" href="#L310">310</a></span>
<span id="L311" class="lineno"><a class="lineno" href="#L311">311</a></span>
<span id="L312" class="lineno"><a class="lineno" href="#L312">312</a></span>
<span id="L313" class="lineno"><a class="lineno" href="#L313">313</a></span>
<span id="L314" class="lineno"><a class="lineno" href="#L314">314</a></span>
<span id="L315" class="lineno"><a class="lineno" href="#L315">315</a></span>
<span id="L316" class="lineno"><a class="lineno" href="#L316">316</a></span>
<span id="L317" class="lineno"><a class="lineno" href="#L317">317</a></span>
<span id="L318" class="lineno"><a class="lineno" href="#L318">318</a></span>
<span id="L319" class="lineno"><a class="lineno" href="#L319">319</a></span>
<span id="L320" class="lineno"><a class="lineno" href="#L320">320</a></span>
<span id="L321" class="lineno"><a class="lineno" href="#L321">321</a></span>
<span id="L322" class="lineno"><a class="lineno" href="#L322">322</a></span>
<span id="L323" class="lineno"><a class="lineno" href="#L323">323</a></span>
<span id="L324" class="lineno"><a class="lineno" href="#L324">324</a></span>
<span id="L325" class="lineno"><a class="lineno" href="#L325">325</a></span>
<span id="L326" class="lineno"><a class="lineno" href="#L326">326</a></span>
<span id="L327" class="lineno"><a class="lineno" href="#L327">327</a></span>
<span id="L328" class="lineno"><a class="lineno" href="#L328">328</a></span>
<span id="L329" class="lineno"><a class="lineno" href="#L329">329</a></span>
<span id="L330" class="lineno"><a class="lineno" href="#L330">330</a></span>
<span id="L331" class="lineno"><a class="lineno" href="#L331">331</a></span>
<span id="L332" class="lineno"><a class="lineno" href="#L332">332</a></span>
<span id="L333" class="lineno"><a class="lineno" href="#L333">333</a></span>
<span id="L334" class="lineno"><a class="lineno" href="#L334">334</a></span>
<span id="L335" class="lineno"><a class="lineno" href="#L335">335</a></span>
<span id="L336" class="lineno"><a class="lineno" href="#L336">336</a></span>
<span id="L337" class="lineno"><a class="lineno" href="#L337">337</a></span>
<span id="L338" class="lineno"><a class="lineno" href="#L338">338</a></span>
<span id="L339" class="lineno"><a class="lineno" href="#L339">339</a></span>
<span id="L340" class="lineno"><a class="lineno" href="#L340">340</a></span>
<span id="L341" class="lineno"><a class="lineno" href="#L341">341</a></span>
<span id="L342" class="lineno"><a class="lineno" href="#L342">342</a></span>
<span id="L343" class="lineno"><a class="lineno" href="#L343">343</a></span>
<span id="L344" class="lineno"><a class="lineno" href="#L344">344</a></span>
<span id="L345" class="lineno"><a class="lineno" href="#L345">345</a></span>
<span id="L346" class="lineno"><a class="lineno" href="#L346">346</a></span>
<span id="L347" class="lineno"><a class="lineno" href="#L347">347</a></span>
<span id="L348" class="lineno"><a class="lineno" href="#L348">348</a></span>
<span id="L349" class="lineno"><a class="lineno" href="#L349">349</a></span>
<span id="L350" class="lineno"><a class="lineno" href="#L350">350</a></span>
<span id="L351" class="lineno"><a class="lineno" href="#L351">351</a></span>
<span id="L352" class="lineno"><a class="lineno" href="#L352">352</a></span>
<span id="L353" class="lineno"><a class="lineno" href="#L353">353</a></span>
<span id="L354" class="lineno"><a class="lineno" href="#L354">354</a></span>
<span id="L355" class="lineno"><a class="lineno" href="#L355">355</a></span>
<span id="L356" class="lineno"><a class="lineno" href="#L356">356</a></span>
<span id="L357" class="lineno"><a class="lineno" href="#L357">357</a></span>
<span id="L358" class="lineno"><a class="lineno" href="#L358">358</a></span>
<span id="L359" class="lineno"><a class="lineno" href="#L359">359</a></span>
<span id="L360" class="lineno"><a class="lineno" href="#L360">360</a></span>
<span id="L361" class="lineno"><a class="lineno" href="#L361">361</a></span>
<span id="L362" class="lineno"><a class="lineno" href="#L362">362</a></span>
<span id="L363" class="lineno"><a class="lineno" href="#L363">363</a></span>
<span id="L364" class="lineno"><a class="lineno" href="#L364">364</a></span>
<span id="L365" class="lineno"><a class="lineno" href="#L365">365</a></span>
<span id="L366" class="lineno"><a class="lineno" href="#L366">366</a></span>
<span id="L367" class="lineno"><a class="lineno" href="#L367">367</a></span>
<span id="L368" class="lineno"><a class="lineno" href="#L368">368</a></span>
<span id="L369" class="lineno"><a class="lineno" href="#L369">369</a></span>
<span id="L370" class="lineno"><a class="lineno" href="#L370">370</a></span>
<span id="L371" class="lineno"><a class="lineno" href="#L371">371</a></span>
<span id="L372" class="lineno"><a class="lineno" href="#L372">372</a></span>
<span id="L373" class="lineno"><a class="lineno" href="#L373">373</a></span>
<span id="L374" class="lineno"><a class="lineno" href="#L374">374</a></span>
<span id="L375" class="lineno"><a class="lineno" href="#L375">375</a></span>
<span id="L376" class="lineno"><a class="lineno" href="#L376">376</a></span>
<span id="L377" class="lineno"><a class="lineno" href="#L377">377</a></span>
<span id="L378" class="lineno"><a class="lineno" href="#L378">378</a></span>
<span id="L379" class="lineno"><a class="lineno" href="#L379">379</a></span>
<span id="L380" class="lineno"><a class="lineno" href="#L380">380</a></span>
<span id="L381" class="lineno"><a class="lineno" href="#L381">381</a></span>
<span id="L382" class="lineno"><a class="lineno" href="#L382">382</a></span>
<span id="L383" class="lineno"><a class="lineno" href="#L383">383</a></span>
<span id="L384" class="lineno"><a class="lineno" href="#L384">384</a></span>
<span id="L385" class="lineno"><a class="lineno" href="#L385">385</a></span>
<span id="L386" class="lineno"><a class="lineno" href="#L386">386</a></span>
<span id="L387" class="lineno"><a class="lineno" href="#L387">387</a></span>
<span id="L388" class="lineno"><a class="lineno" href="#L388">388</a></span>
<span id="L389" class="lineno"><a class="lineno" href="#L389">389</a></span>
<span id="L390" class="lineno"><a class="lineno" href="#L390">390</a></span>
<span id="L391" class="lineno"><a class="lineno" href="#L391">391</a></span>
<span id="L392" class="lineno"><a class="lineno" href="#L392">392</a></span>
<span id="L393" class="lineno"><a class="lineno" href="#L393">393</a></span>
<span id="L394" class="lineno"><a class="lineno" href="#L394">394</a></span>
<span id="L395" class="lineno"><a class="lineno" href="#L395">395</a></span>
<span id="L396" class="lineno"><a class="lineno" href="#L396">396</a></span>
<span id="L397" class="lineno"><a class="lineno" href="#L397">397</a></span>
<span id="L398" class="lineno"><a class="lineno" href="#L398">398</a></span>
<span id="L399" class="lineno"><a class="lineno" href="#L399">399</a></span>
</pre></td>
<td class="table-code"><pre><span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!">Analytics and reporting Pydantic models.</span>
<span class="line-empty" title="No Anys on this line!">Data structures for performance metrics, insights, and reporting.</span>
<span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from datetime import datetime, date</span>
<span class="line-precise" title="No Anys on this line!">from typing import Any, Dict, List, Optional, Union</span>
<span class="line-precise" title="No Anys on this line!">from enum import Enum</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from pydantic import BaseModel, Field, validator</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from .common import BaseEntity, BaseResponse, Currency, MetricValue</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class MetricType(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Available metric types for analytics.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    IMPRESSIONS = "impressions"</span>
<span class="line-precise" title="No Anys on this line!">    CLICKS = "clicks"</span>
<span class="line-precise" title="No Anys on this line!">    CONVERSIONS = "conversions"</span>
<span class="line-precise" title="No Anys on this line!">    COST = "cost"</span>
<span class="line-precise" title="No Anys on this line!">    REVENUE = "revenue"</span>
<span class="line-precise" title="No Anys on this line!">    CTR = "ctr"</span>
<span class="line-precise" title="No Anys on this line!">    CPC = "cpc"</span>
<span class="line-precise" title="No Anys on this line!">    CPM = "cpm"</span>
<span class="line-precise" title="No Anys on this line!">    CONVERSION_RATE = "conversion_rate"</span>
<span class="line-precise" title="No Anys on this line!">    COST_PER_CONVERSION = "cost_per_conversion"</span>
<span class="line-precise" title="No Anys on this line!">    ROAS = "roas"</span>
<span class="line-precise" title="No Anys on this line!">    ROI = "roi"</span>
<span class="line-precise" title="No Anys on this line!">    QUALITY_SCORE = "quality_score"</span>
<span class="line-precise" title="No Anys on this line!">    IMPRESSION_SHARE = "impression_share"</span>
<span class="line-precise" title="No Anys on this line!">    SEARCH_VOLUME = "search_volume"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class TimeRange(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Predefined time ranges for analytics.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    TODAY = "today"</span>
<span class="line-precise" title="No Anys on this line!">    YESTERDAY = "yesterday"</span>
<span class="line-precise" title="No Anys on this line!">    LAST_7_DAYS = "last_7_days"</span>
<span class="line-precise" title="No Anys on this line!">    LAST_14_DAYS = "last_14_days"</span>
<span class="line-precise" title="No Anys on this line!">    LAST_30_DAYS = "last_30_days"</span>
<span class="line-precise" title="No Anys on this line!">    LAST_90_DAYS = "last_90_days"</span>
<span class="line-precise" title="No Anys on this line!">    THIS_MONTH = "this_month"</span>
<span class="line-precise" title="No Anys on this line!">    LAST_MONTH = "last_month"</span>
<span class="line-precise" title="No Anys on this line!">    THIS_QUARTER = "this_quarter"</span>
<span class="line-precise" title="No Anys on this line!">    LAST_QUARTER = "last_quarter"</span>
<span class="line-precise" title="No Anys on this line!">    THIS_YEAR = "this_year"</span>
<span class="line-precise" title="No Anys on this line!">    LAST_YEAR = "last_year"</span>
<span class="line-precise" title="No Anys on this line!">    CUSTOM = "custom"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class Granularity(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Data granularity levels.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    HOURLY = "hourly"</span>
<span class="line-precise" title="No Anys on this line!">    DAILY = "daily"</span>
<span class="line-precise" title="No Anys on this line!">    WEEKLY = "weekly"</span>
<span class="line-precise" title="No Anys on this line!">    MONTHLY = "monthly"</span>
<span class="line-precise" title="No Anys on this line!">    QUARTERLY = "quarterly"</span>
<span class="line-precise" title="No Anys on this line!">    YEARLY = "yearly"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class ReportFormat(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Report export formats.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    JSON = "json"</span>
<span class="line-precise" title="No Anys on this line!">    CSV = "csv"</span>
<span class="line-precise" title="No Anys on this line!">    XLSX = "xlsx"</span>
<span class="line-precise" title="No Anys on this line!">    PDF = "pdf"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class InsightType(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Types of performance insights.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    PERFORMANCE_ANOMALY = "performance_anomaly"</span>
<span class="line-precise" title="No Anys on this line!">    OPTIMIZATION_OPPORTUNITY = "optimization_opportunity"</span>
<span class="line-precise" title="No Anys on this line!">    TREND_ANALYSIS = "trend_analysis"</span>
<span class="line-precise" title="No Anys on this line!">    BUDGET_RECOMMENDATION = "budget_recommendation"</span>
<span class="line-precise" title="No Anys on this line!">    AUDIENCE_INSIGHT = "audience_insight"</span>
<span class="line-precise" title="No Anys on this line!">    COMPETITIVE_ANALYSIS = "competitive_analysis"</span>
<span class="line-precise" title="No Anys on this line!">    SEASONAL_PATTERN = "seasonal_pattern"</span>
<span class="line-precise" title="No Anys on this line!">    QUALITY_ISSUE = "quality_issue"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class ImpactLevel(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Impact levels for insights and recommendations.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    LOW = "low"</span>
<span class="line-precise" title="No Anys on this line!">    MEDIUM = "medium"</span>
<span class="line-precise" title="No Anys on this line!">    HIGH = "high"</span>
<span class="line-precise" title="No Anys on this line!">    CRITICAL = "critical"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class DataPoint(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Single data point in a time series.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    timestamp: datetime = Field(..., description="Data point timestamp")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    value: float = Field(..., description="Metric value")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class TimeSeries(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Time series data for a metric.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    metric: MetricType = Field(..., description="Metric type")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    data_points: List[DataPoint] = Field(..., description="Time series data points")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    total: Optional[float] = Field(None, description="Total value across all data points")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    average: Optional[float] = Field(None, description="Average value")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    trend: Optional[str] = Field(None, description="Trend direction (up, down, stable)")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class CampaignMetrics(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Comprehensive campaign performance metrics.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    campaign_id: str = Field(..., description="Campaign ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    date_range: Dict[str, date] = Field(..., description="Metrics date range")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Core performance metrics</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    performance: Dict[str, Union[int, float]] = Field(..., description="Raw performance metrics")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Derived metrics</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    derived_metrics: Dict[str, float] = Field(..., description="Calculated derived metrics")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Trend data</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    trends: Dict[str, float] = Field(default={}, description="Period-over-period changes")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Benchmarks</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    benchmarks: Optional[Dict[str, float]] = Field(None, description="Industry/account benchmarks")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class PerformanceInsight(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    AI-generated performance insight.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    id: Optional[str] = Field(None, description="Insight ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: InsightType = Field(..., description="Insight type")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    title: str = Field(..., description="Insight title")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: str = Field(..., description="Detailed description")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    confidence: float = Field(..., ge=0, le=1, description="Confidence score")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    impact: ImpactLevel = Field(..., description="Impact level")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    recommendation: str = Field(..., description="Recommended action")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Supporting data</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    affected_entities: List[str] = Field(default=[], description="Affected campaigns/ad groups/keywords")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    metrics_impact: Dict[str, float] = Field(default={}, description="Expected metrics impact")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    timeframe: Optional[str] = Field(None, description="Relevant timeframe")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Metadata</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Generation timestamp")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    expires_at: Optional[datetime] = Field(None, description="Insight expiration time")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    priority: int = Field(1, ge=1, le=5, description="Priority level (1-5)")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class OptimizationSuggestion(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    AI-generated optimization suggestion.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    id: str = Field(..., description="Suggestion ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: str = Field(..., description="Suggestion type")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    category: str = Field(..., description="Suggestion category")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    priority: str = Field(..., description="Priority level")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    title: str = Field(..., description="Suggestion title")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: str = Field(..., description="Detailed description")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Impact estimation</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    estimated_impact: Dict[str, float] = Field(..., description="Estimated impact metrics")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    confidence: float = Field(..., ge=0, le=1, description="Confidence score")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Implementation</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    action_required: str = Field(..., description="Required action")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    implementation_effort: Optional[str] = Field(None, description="Implementation effort level")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    prerequisites: List[str] = Field(default=[], description="Implementation prerequisites")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Context</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    affected_entities: List[str] = Field(default=[], description="Affected entities")</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    supporting_data: Dict[str, Any] = Field(default={}, description="Supporting data")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Metadata</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Generation timestamp")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    status: str = Field("pending", description="Implementation status")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x8)">class AnalyticsReport(BaseEntity):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Analytics report model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    title: str = Field(..., description="Report title")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: Optional[str] = Field(None, description="Report description")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: str = Field(..., description="Report type")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Report configuration</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    date_range: Dict[str, date] = Field(..., description="Report date range")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    granularity: Granularity = Field(Granularity.DAILY, description="Data granularity")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    metrics: List[MetricType] = Field(..., description="Included metrics")</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    filters: Dict[str, Any] = Field(default={}, description="Applied filters")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Report data</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    data: List[Dict[str, Any]] = Field(..., description="Report data")</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    summary: Dict[str, Any] = Field(default={}, description="Report summary")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    insights: List[PerformanceInsight] = Field(default=[], description="Generated insights")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Metadata</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    generated_at: datetime = Field(..., description="Report generation timestamp")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    generated_by: Optional[str] = Field(None, description="Report generator (user/system)")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    format: ReportFormat = Field(ReportFormat.JSON, description="Report format")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Status</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    status: str = Field("completed", description="Report status")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    error_message: Optional[str] = Field(None, description="Error message if failed")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x6)">class DashboardWidget(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Dashboard widget configuration.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    id: str = Field(..., description="Widget ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: str = Field(..., description="Widget type")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    title: str = Field(..., description="Widget title")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    position: Dict[str, int] = Field(..., description="Widget position and size")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Configuration</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    metrics: List[MetricType] = Field(..., description="Displayed metrics")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    time_range: TimeRange = Field(..., description="Data time range")</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    filters: Dict[str, Any] = Field(default={}, description="Applied filters")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Data</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    data: Dict[str, Any] = Field(default={}, description="Widget data")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class Dashboard(BaseEntity):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Analytics dashboard model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: str = Field(..., description="Dashboard name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: Optional[str] = Field(None, description="Dashboard description")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Configuration</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    widgets: List[DashboardWidget] = Field(..., description="Dashboard widgets")</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    layout: Dict[str, Any] = Field(default={}, description="Dashboard layout configuration")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Access control</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    owner_id: str = Field(..., description="Dashboard owner ID")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    shared_with: List[str] = Field(default=[], description="Users with access")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    is_public: bool = Field(False, description="Public dashboard flag")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Metadata</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    last_viewed: Optional[datetime] = Field(None, description="Last viewed timestamp")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    view_count: int = Field(0, description="Total view count")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AlertRule(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Performance alert rule.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    id: Optional[str] = Field(None, description="Alert rule ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: str = Field(..., description="Alert rule name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: Optional[str] = Field(None, description="Alert rule description")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Conditions</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    metric: MetricType = Field(..., description="Monitored metric")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    condition: str = Field(..., description="Alert condition (&gt;, &lt;, =, etc.)")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    threshold: float = Field(..., description="Alert threshold value")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    time_window_minutes: int = Field(5, description="Time window for evaluation")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Actions</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    notification_channels: List[str] = Field(..., description="Notification channels")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    severity: str = Field("medium", description="Alert severity")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    auto_resolve: bool = Field(True, description="Auto-resolve when condition clears")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Status</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    enabled: bool = Field(True, description="Alert rule enabled")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    last_triggered: Optional[datetime] = Field(None, description="Last trigger timestamp")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    trigger_count: int = Field(0, description="Total trigger count")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class Alert(BaseEntity):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Performance alert instance.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    rule_id: str = Field(..., description="Alert rule ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    campaign_id: Optional[str] = Field(None, description="Related campaign ID")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Alert details</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    title: str = Field(..., description="Alert title")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    message: str = Field(..., description="Alert message")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    severity: str = Field(..., description="Alert severity")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    metric: MetricType = Field(..., description="Triggered metric")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Values</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    current_value: float = Field(..., description="Current metric value")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    threshold_value: float = Field(..., description="Threshold value")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    deviation_percentage: Optional[float] = Field(None, description="Deviation from threshold")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Status</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    status: str = Field("active", description="Alert status")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    acknowledged: bool = Field(False, description="Alert acknowledged")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    acknowledged_by: Optional[str] = Field(None, description="User who acknowledged")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    acknowledged_at: Optional[datetime] = Field(None, description="Acknowledgment timestamp")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    resolved_at: Optional[datetime] = Field(None, description="Resolution timestamp")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AnalyticsReportResponse(BaseResponse[AnalyticsReport]):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Analytics report response model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    pass</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class DashboardResponse(BaseResponse[Dashboard]):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Dashboard response model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    pass</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class CompetitorAnalysis(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Competitor analysis data.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    competitor_domain: str = Field(..., description="Competitor domain")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    competitor_name: Optional[str] = Field(None, description="Competitor name")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Market share data</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    estimated_traffic: Optional[int] = Field(None, description="Estimated monthly traffic")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    market_share: Optional[float] = Field(None, description="Estimated market share")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Ad performance</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    estimated_ad_spend: Optional[float] = Field(None, description="Estimated monthly ad spend")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    active_keywords: Optional[int] = Field(None, description="Number of active keywords")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    ad_copy_samples: List[str] = Field(default=[], description="Sample ad copies")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Insights</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    strengths: List[str] = Field(default=[], description="Competitor strengths")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    opportunities: List[str] = Field(default=[], description="Opportunities against competitor")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Metadata</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    last_analyzed: datetime = Field(default_factory=datetime.utcnow, description="Last analysis timestamp")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    confidence_score: Optional[float] = Field(None, description="Analysis confidence score")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class SeasonalityPattern(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Seasonality pattern analysis.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    metric: MetricType = Field(..., description="Analyzed metric")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    pattern_type: str = Field(..., description="Pattern type (weekly, monthly, yearly)")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Pattern data</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    pattern: Dict[str, float] = Field(..., description="Seasonal pattern values")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    strength: float = Field(..., ge=0, le=1, description="Pattern strength")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    confidence: float = Field(..., ge=0, le=1, description="Pattern confidence")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Predictions</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    forecast: Optional[Dict[str, float]] = Field(None, description="Forecast values")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    recommendations: List[str] = Field(default=[], description="Seasonality-based recommendations")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Metadata</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    analyzed_period: Dict[str, date] = Field(..., description="Analysis period")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Analysis timestamp")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class AttributionModel(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Attribution model configuration and results.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: str = Field(..., description="Attribution model name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: str = Field(..., description="Attribution type (first-click, last-click, linear, etc.)")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: Optional[str] = Field(None, description="Model description")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Configuration</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    lookback_window_days: int = Field(30, description="Attribution lookback window")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    include_view_through: bool = Field(True, description="Include view-through conversions")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Results</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    attribution_results: Dict[str, float] = Field(default={}, description="Attribution results by channel")</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    conversion_paths: List[Dict[str, Any]] = Field(default=[], description="Common conversion paths")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Performance</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    model_accuracy: Optional[float] = Field(None, description="Model accuracy score")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@validator("confidence", "strength", "model_accuracy", pre=True, allow_reuse=True)</span>
<span class="line-any" title="No Anys on this line!">def validate_percentage(cls, v):</span>
<span class="line-empty" title="No Anys on this line!">    """Validate percentage values are between 0 and 1."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x7)">    if v is not None and (v &lt; 0 or v &gt; 1):</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        raise ValueError("Value must be between 0 and 1")</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">    return v</span>
</pre></td>
</tr></tbody>
</table>
</body>
</html>
