<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../mypy-html.css">
</head>
<body>
<h2>models.common</h2>
<table>
<caption>models/common.py</caption>
<tbody><tr>
<td class="table-lines"><pre><span id="L1" class="lineno"><a class="lineno" href="#L1">1</a></span>
<span id="L2" class="lineno"><a class="lineno" href="#L2">2</a></span>
<span id="L3" class="lineno"><a class="lineno" href="#L3">3</a></span>
<span id="L4" class="lineno"><a class="lineno" href="#L4">4</a></span>
<span id="L5" class="lineno"><a class="lineno" href="#L5">5</a></span>
<span id="L6" class="lineno"><a class="lineno" href="#L6">6</a></span>
<span id="L7" class="lineno"><a class="lineno" href="#L7">7</a></span>
<span id="L8" class="lineno"><a class="lineno" href="#L8">8</a></span>
<span id="L9" class="lineno"><a class="lineno" href="#L9">9</a></span>
<span id="L10" class="lineno"><a class="lineno" href="#L10">10</a></span>
<span id="L11" class="lineno"><a class="lineno" href="#L11">11</a></span>
<span id="L12" class="lineno"><a class="lineno" href="#L12">12</a></span>
<span id="L13" class="lineno"><a class="lineno" href="#L13">13</a></span>
<span id="L14" class="lineno"><a class="lineno" href="#L14">14</a></span>
<span id="L15" class="lineno"><a class="lineno" href="#L15">15</a></span>
<span id="L16" class="lineno"><a class="lineno" href="#L16">16</a></span>
<span id="L17" class="lineno"><a class="lineno" href="#L17">17</a></span>
<span id="L18" class="lineno"><a class="lineno" href="#L18">18</a></span>
<span id="L19" class="lineno"><a class="lineno" href="#L19">19</a></span>
<span id="L20" class="lineno"><a class="lineno" href="#L20">20</a></span>
<span id="L21" class="lineno"><a class="lineno" href="#L21">21</a></span>
<span id="L22" class="lineno"><a class="lineno" href="#L22">22</a></span>
<span id="L23" class="lineno"><a class="lineno" href="#L23">23</a></span>
<span id="L24" class="lineno"><a class="lineno" href="#L24">24</a></span>
<span id="L25" class="lineno"><a class="lineno" href="#L25">25</a></span>
<span id="L26" class="lineno"><a class="lineno" href="#L26">26</a></span>
<span id="L27" class="lineno"><a class="lineno" href="#L27">27</a></span>
<span id="L28" class="lineno"><a class="lineno" href="#L28">28</a></span>
<span id="L29" class="lineno"><a class="lineno" href="#L29">29</a></span>
<span id="L30" class="lineno"><a class="lineno" href="#L30">30</a></span>
<span id="L31" class="lineno"><a class="lineno" href="#L31">31</a></span>
<span id="L32" class="lineno"><a class="lineno" href="#L32">32</a></span>
<span id="L33" class="lineno"><a class="lineno" href="#L33">33</a></span>
<span id="L34" class="lineno"><a class="lineno" href="#L34">34</a></span>
<span id="L35" class="lineno"><a class="lineno" href="#L35">35</a></span>
<span id="L36" class="lineno"><a class="lineno" href="#L36">36</a></span>
<span id="L37" class="lineno"><a class="lineno" href="#L37">37</a></span>
<span id="L38" class="lineno"><a class="lineno" href="#L38">38</a></span>
<span id="L39" class="lineno"><a class="lineno" href="#L39">39</a></span>
<span id="L40" class="lineno"><a class="lineno" href="#L40">40</a></span>
<span id="L41" class="lineno"><a class="lineno" href="#L41">41</a></span>
<span id="L42" class="lineno"><a class="lineno" href="#L42">42</a></span>
<span id="L43" class="lineno"><a class="lineno" href="#L43">43</a></span>
<span id="L44" class="lineno"><a class="lineno" href="#L44">44</a></span>
<span id="L45" class="lineno"><a class="lineno" href="#L45">45</a></span>
<span id="L46" class="lineno"><a class="lineno" href="#L46">46</a></span>
<span id="L47" class="lineno"><a class="lineno" href="#L47">47</a></span>
<span id="L48" class="lineno"><a class="lineno" href="#L48">48</a></span>
<span id="L49" class="lineno"><a class="lineno" href="#L49">49</a></span>
<span id="L50" class="lineno"><a class="lineno" href="#L50">50</a></span>
<span id="L51" class="lineno"><a class="lineno" href="#L51">51</a></span>
<span id="L52" class="lineno"><a class="lineno" href="#L52">52</a></span>
<span id="L53" class="lineno"><a class="lineno" href="#L53">53</a></span>
<span id="L54" class="lineno"><a class="lineno" href="#L54">54</a></span>
<span id="L55" class="lineno"><a class="lineno" href="#L55">55</a></span>
<span id="L56" class="lineno"><a class="lineno" href="#L56">56</a></span>
<span id="L57" class="lineno"><a class="lineno" href="#L57">57</a></span>
<span id="L58" class="lineno"><a class="lineno" href="#L58">58</a></span>
<span id="L59" class="lineno"><a class="lineno" href="#L59">59</a></span>
<span id="L60" class="lineno"><a class="lineno" href="#L60">60</a></span>
<span id="L61" class="lineno"><a class="lineno" href="#L61">61</a></span>
<span id="L62" class="lineno"><a class="lineno" href="#L62">62</a></span>
<span id="L63" class="lineno"><a class="lineno" href="#L63">63</a></span>
<span id="L64" class="lineno"><a class="lineno" href="#L64">64</a></span>
<span id="L65" class="lineno"><a class="lineno" href="#L65">65</a></span>
<span id="L66" class="lineno"><a class="lineno" href="#L66">66</a></span>
<span id="L67" class="lineno"><a class="lineno" href="#L67">67</a></span>
<span id="L68" class="lineno"><a class="lineno" href="#L68">68</a></span>
<span id="L69" class="lineno"><a class="lineno" href="#L69">69</a></span>
<span id="L70" class="lineno"><a class="lineno" href="#L70">70</a></span>
<span id="L71" class="lineno"><a class="lineno" href="#L71">71</a></span>
<span id="L72" class="lineno"><a class="lineno" href="#L72">72</a></span>
<span id="L73" class="lineno"><a class="lineno" href="#L73">73</a></span>
<span id="L74" class="lineno"><a class="lineno" href="#L74">74</a></span>
<span id="L75" class="lineno"><a class="lineno" href="#L75">75</a></span>
<span id="L76" class="lineno"><a class="lineno" href="#L76">76</a></span>
<span id="L77" class="lineno"><a class="lineno" href="#L77">77</a></span>
<span id="L78" class="lineno"><a class="lineno" href="#L78">78</a></span>
<span id="L79" class="lineno"><a class="lineno" href="#L79">79</a></span>
<span id="L80" class="lineno"><a class="lineno" href="#L80">80</a></span>
<span id="L81" class="lineno"><a class="lineno" href="#L81">81</a></span>
<span id="L82" class="lineno"><a class="lineno" href="#L82">82</a></span>
<span id="L83" class="lineno"><a class="lineno" href="#L83">83</a></span>
<span id="L84" class="lineno"><a class="lineno" href="#L84">84</a></span>
<span id="L85" class="lineno"><a class="lineno" href="#L85">85</a></span>
<span id="L86" class="lineno"><a class="lineno" href="#L86">86</a></span>
<span id="L87" class="lineno"><a class="lineno" href="#L87">87</a></span>
<span id="L88" class="lineno"><a class="lineno" href="#L88">88</a></span>
<span id="L89" class="lineno"><a class="lineno" href="#L89">89</a></span>
<span id="L90" class="lineno"><a class="lineno" href="#L90">90</a></span>
<span id="L91" class="lineno"><a class="lineno" href="#L91">91</a></span>
<span id="L92" class="lineno"><a class="lineno" href="#L92">92</a></span>
<span id="L93" class="lineno"><a class="lineno" href="#L93">93</a></span>
<span id="L94" class="lineno"><a class="lineno" href="#L94">94</a></span>
<span id="L95" class="lineno"><a class="lineno" href="#L95">95</a></span>
<span id="L96" class="lineno"><a class="lineno" href="#L96">96</a></span>
<span id="L97" class="lineno"><a class="lineno" href="#L97">97</a></span>
<span id="L98" class="lineno"><a class="lineno" href="#L98">98</a></span>
<span id="L99" class="lineno"><a class="lineno" href="#L99">99</a></span>
<span id="L100" class="lineno"><a class="lineno" href="#L100">100</a></span>
<span id="L101" class="lineno"><a class="lineno" href="#L101">101</a></span>
<span id="L102" class="lineno"><a class="lineno" href="#L102">102</a></span>
<span id="L103" class="lineno"><a class="lineno" href="#L103">103</a></span>
<span id="L104" class="lineno"><a class="lineno" href="#L104">104</a></span>
<span id="L105" class="lineno"><a class="lineno" href="#L105">105</a></span>
<span id="L106" class="lineno"><a class="lineno" href="#L106">106</a></span>
<span id="L107" class="lineno"><a class="lineno" href="#L107">107</a></span>
<span id="L108" class="lineno"><a class="lineno" href="#L108">108</a></span>
<span id="L109" class="lineno"><a class="lineno" href="#L109">109</a></span>
<span id="L110" class="lineno"><a class="lineno" href="#L110">110</a></span>
<span id="L111" class="lineno"><a class="lineno" href="#L111">111</a></span>
<span id="L112" class="lineno"><a class="lineno" href="#L112">112</a></span>
<span id="L113" class="lineno"><a class="lineno" href="#L113">113</a></span>
<span id="L114" class="lineno"><a class="lineno" href="#L114">114</a></span>
<span id="L115" class="lineno"><a class="lineno" href="#L115">115</a></span>
<span id="L116" class="lineno"><a class="lineno" href="#L116">116</a></span>
<span id="L117" class="lineno"><a class="lineno" href="#L117">117</a></span>
<span id="L118" class="lineno"><a class="lineno" href="#L118">118</a></span>
<span id="L119" class="lineno"><a class="lineno" href="#L119">119</a></span>
<span id="L120" class="lineno"><a class="lineno" href="#L120">120</a></span>
<span id="L121" class="lineno"><a class="lineno" href="#L121">121</a></span>
<span id="L122" class="lineno"><a class="lineno" href="#L122">122</a></span>
<span id="L123" class="lineno"><a class="lineno" href="#L123">123</a></span>
<span id="L124" class="lineno"><a class="lineno" href="#L124">124</a></span>
<span id="L125" class="lineno"><a class="lineno" href="#L125">125</a></span>
<span id="L126" class="lineno"><a class="lineno" href="#L126">126</a></span>
<span id="L127" class="lineno"><a class="lineno" href="#L127">127</a></span>
<span id="L128" class="lineno"><a class="lineno" href="#L128">128</a></span>
<span id="L129" class="lineno"><a class="lineno" href="#L129">129</a></span>
<span id="L130" class="lineno"><a class="lineno" href="#L130">130</a></span>
<span id="L131" class="lineno"><a class="lineno" href="#L131">131</a></span>
<span id="L132" class="lineno"><a class="lineno" href="#L132">132</a></span>
<span id="L133" class="lineno"><a class="lineno" href="#L133">133</a></span>
<span id="L134" class="lineno"><a class="lineno" href="#L134">134</a></span>
<span id="L135" class="lineno"><a class="lineno" href="#L135">135</a></span>
<span id="L136" class="lineno"><a class="lineno" href="#L136">136</a></span>
<span id="L137" class="lineno"><a class="lineno" href="#L137">137</a></span>
<span id="L138" class="lineno"><a class="lineno" href="#L138">138</a></span>
<span id="L139" class="lineno"><a class="lineno" href="#L139">139</a></span>
<span id="L140" class="lineno"><a class="lineno" href="#L140">140</a></span>
<span id="L141" class="lineno"><a class="lineno" href="#L141">141</a></span>
<span id="L142" class="lineno"><a class="lineno" href="#L142">142</a></span>
<span id="L143" class="lineno"><a class="lineno" href="#L143">143</a></span>
<span id="L144" class="lineno"><a class="lineno" href="#L144">144</a></span>
<span id="L145" class="lineno"><a class="lineno" href="#L145">145</a></span>
<span id="L146" class="lineno"><a class="lineno" href="#L146">146</a></span>
<span id="L147" class="lineno"><a class="lineno" href="#L147">147</a></span>
<span id="L148" class="lineno"><a class="lineno" href="#L148">148</a></span>
<span id="L149" class="lineno"><a class="lineno" href="#L149">149</a></span>
<span id="L150" class="lineno"><a class="lineno" href="#L150">150</a></span>
<span id="L151" class="lineno"><a class="lineno" href="#L151">151</a></span>
<span id="L152" class="lineno"><a class="lineno" href="#L152">152</a></span>
<span id="L153" class="lineno"><a class="lineno" href="#L153">153</a></span>
<span id="L154" class="lineno"><a class="lineno" href="#L154">154</a></span>
<span id="L155" class="lineno"><a class="lineno" href="#L155">155</a></span>
<span id="L156" class="lineno"><a class="lineno" href="#L156">156</a></span>
<span id="L157" class="lineno"><a class="lineno" href="#L157">157</a></span>
<span id="L158" class="lineno"><a class="lineno" href="#L158">158</a></span>
<span id="L159" class="lineno"><a class="lineno" href="#L159">159</a></span>
<span id="L160" class="lineno"><a class="lineno" href="#L160">160</a></span>
<span id="L161" class="lineno"><a class="lineno" href="#L161">161</a></span>
<span id="L162" class="lineno"><a class="lineno" href="#L162">162</a></span>
<span id="L163" class="lineno"><a class="lineno" href="#L163">163</a></span>
<span id="L164" class="lineno"><a class="lineno" href="#L164">164</a></span>
<span id="L165" class="lineno"><a class="lineno" href="#L165">165</a></span>
<span id="L166" class="lineno"><a class="lineno" href="#L166">166</a></span>
<span id="L167" class="lineno"><a class="lineno" href="#L167">167</a></span>
<span id="L168" class="lineno"><a class="lineno" href="#L168">168</a></span>
<span id="L169" class="lineno"><a class="lineno" href="#L169">169</a></span>
<span id="L170" class="lineno"><a class="lineno" href="#L170">170</a></span>
<span id="L171" class="lineno"><a class="lineno" href="#L171">171</a></span>
<span id="L172" class="lineno"><a class="lineno" href="#L172">172</a></span>
<span id="L173" class="lineno"><a class="lineno" href="#L173">173</a></span>
<span id="L174" class="lineno"><a class="lineno" href="#L174">174</a></span>
<span id="L175" class="lineno"><a class="lineno" href="#L175">175</a></span>
<span id="L176" class="lineno"><a class="lineno" href="#L176">176</a></span>
<span id="L177" class="lineno"><a class="lineno" href="#L177">177</a></span>
<span id="L178" class="lineno"><a class="lineno" href="#L178">178</a></span>
<span id="L179" class="lineno"><a class="lineno" href="#L179">179</a></span>
<span id="L180" class="lineno"><a class="lineno" href="#L180">180</a></span>
<span id="L181" class="lineno"><a class="lineno" href="#L181">181</a></span>
<span id="L182" class="lineno"><a class="lineno" href="#L182">182</a></span>
<span id="L183" class="lineno"><a class="lineno" href="#L183">183</a></span>
<span id="L184" class="lineno"><a class="lineno" href="#L184">184</a></span>
<span id="L185" class="lineno"><a class="lineno" href="#L185">185</a></span>
<span id="L186" class="lineno"><a class="lineno" href="#L186">186</a></span>
<span id="L187" class="lineno"><a class="lineno" href="#L187">187</a></span>
<span id="L188" class="lineno"><a class="lineno" href="#L188">188</a></span>
<span id="L189" class="lineno"><a class="lineno" href="#L189">189</a></span>
<span id="L190" class="lineno"><a class="lineno" href="#L190">190</a></span>
<span id="L191" class="lineno"><a class="lineno" href="#L191">191</a></span>
<span id="L192" class="lineno"><a class="lineno" href="#L192">192</a></span>
<span id="L193" class="lineno"><a class="lineno" href="#L193">193</a></span>
<span id="L194" class="lineno"><a class="lineno" href="#L194">194</a></span>
<span id="L195" class="lineno"><a class="lineno" href="#L195">195</a></span>
<span id="L196" class="lineno"><a class="lineno" href="#L196">196</a></span>
<span id="L197" class="lineno"><a class="lineno" href="#L197">197</a></span>
<span id="L198" class="lineno"><a class="lineno" href="#L198">198</a></span>
<span id="L199" class="lineno"><a class="lineno" href="#L199">199</a></span>
<span id="L200" class="lineno"><a class="lineno" href="#L200">200</a></span>
<span id="L201" class="lineno"><a class="lineno" href="#L201">201</a></span>
<span id="L202" class="lineno"><a class="lineno" href="#L202">202</a></span>
<span id="L203" class="lineno"><a class="lineno" href="#L203">203</a></span>
<span id="L204" class="lineno"><a class="lineno" href="#L204">204</a></span>
<span id="L205" class="lineno"><a class="lineno" href="#L205">205</a></span>
<span id="L206" class="lineno"><a class="lineno" href="#L206">206</a></span>
<span id="L207" class="lineno"><a class="lineno" href="#L207">207</a></span>
<span id="L208" class="lineno"><a class="lineno" href="#L208">208</a></span>
<span id="L209" class="lineno"><a class="lineno" href="#L209">209</a></span>
<span id="L210" class="lineno"><a class="lineno" href="#L210">210</a></span>
<span id="L211" class="lineno"><a class="lineno" href="#L211">211</a></span>
<span id="L212" class="lineno"><a class="lineno" href="#L212">212</a></span>
<span id="L213" class="lineno"><a class="lineno" href="#L213">213</a></span>
<span id="L214" class="lineno"><a class="lineno" href="#L214">214</a></span>
<span id="L215" class="lineno"><a class="lineno" href="#L215">215</a></span>
<span id="L216" class="lineno"><a class="lineno" href="#L216">216</a></span>
<span id="L217" class="lineno"><a class="lineno" href="#L217">217</a></span>
<span id="L218" class="lineno"><a class="lineno" href="#L218">218</a></span>
<span id="L219" class="lineno"><a class="lineno" href="#L219">219</a></span>
<span id="L220" class="lineno"><a class="lineno" href="#L220">220</a></span>
<span id="L221" class="lineno"><a class="lineno" href="#L221">221</a></span>
<span id="L222" class="lineno"><a class="lineno" href="#L222">222</a></span>
<span id="L223" class="lineno"><a class="lineno" href="#L223">223</a></span>
<span id="L224" class="lineno"><a class="lineno" href="#L224">224</a></span>
<span id="L225" class="lineno"><a class="lineno" href="#L225">225</a></span>
<span id="L226" class="lineno"><a class="lineno" href="#L226">226</a></span>
<span id="L227" class="lineno"><a class="lineno" href="#L227">227</a></span>
<span id="L228" class="lineno"><a class="lineno" href="#L228">228</a></span>
<span id="L229" class="lineno"><a class="lineno" href="#L229">229</a></span>
<span id="L230" class="lineno"><a class="lineno" href="#L230">230</a></span>
<span id="L231" class="lineno"><a class="lineno" href="#L231">231</a></span>
<span id="L232" class="lineno"><a class="lineno" href="#L232">232</a></span>
<span id="L233" class="lineno"><a class="lineno" href="#L233">233</a></span>
<span id="L234" class="lineno"><a class="lineno" href="#L234">234</a></span>
<span id="L235" class="lineno"><a class="lineno" href="#L235">235</a></span>
<span id="L236" class="lineno"><a class="lineno" href="#L236">236</a></span>
<span id="L237" class="lineno"><a class="lineno" href="#L237">237</a></span>
<span id="L238" class="lineno"><a class="lineno" href="#L238">238</a></span>
<span id="L239" class="lineno"><a class="lineno" href="#L239">239</a></span>
<span id="L240" class="lineno"><a class="lineno" href="#L240">240</a></span>
<span id="L241" class="lineno"><a class="lineno" href="#L241">241</a></span>
<span id="L242" class="lineno"><a class="lineno" href="#L242">242</a></span>
<span id="L243" class="lineno"><a class="lineno" href="#L243">243</a></span>
<span id="L244" class="lineno"><a class="lineno" href="#L244">244</a></span>
<span id="L245" class="lineno"><a class="lineno" href="#L245">245</a></span>
<span id="L246" class="lineno"><a class="lineno" href="#L246">246</a></span>
<span id="L247" class="lineno"><a class="lineno" href="#L247">247</a></span>
<span id="L248" class="lineno"><a class="lineno" href="#L248">248</a></span>
<span id="L249" class="lineno"><a class="lineno" href="#L249">249</a></span>
<span id="L250" class="lineno"><a class="lineno" href="#L250">250</a></span>
<span id="L251" class="lineno"><a class="lineno" href="#L251">251</a></span>
<span id="L252" class="lineno"><a class="lineno" href="#L252">252</a></span>
<span id="L253" class="lineno"><a class="lineno" href="#L253">253</a></span>
<span id="L254" class="lineno"><a class="lineno" href="#L254">254</a></span>
<span id="L255" class="lineno"><a class="lineno" href="#L255">255</a></span>
<span id="L256" class="lineno"><a class="lineno" href="#L256">256</a></span>
<span id="L257" class="lineno"><a class="lineno" href="#L257">257</a></span>
<span id="L258" class="lineno"><a class="lineno" href="#L258">258</a></span>
<span id="L259" class="lineno"><a class="lineno" href="#L259">259</a></span>
<span id="L260" class="lineno"><a class="lineno" href="#L260">260</a></span>
<span id="L261" class="lineno"><a class="lineno" href="#L261">261</a></span>
<span id="L262" class="lineno"><a class="lineno" href="#L262">262</a></span>
<span id="L263" class="lineno"><a class="lineno" href="#L263">263</a></span>
<span id="L264" class="lineno"><a class="lineno" href="#L264">264</a></span>
<span id="L265" class="lineno"><a class="lineno" href="#L265">265</a></span>
<span id="L266" class="lineno"><a class="lineno" href="#L266">266</a></span>
<span id="L267" class="lineno"><a class="lineno" href="#L267">267</a></span>
<span id="L268" class="lineno"><a class="lineno" href="#L268">268</a></span>
<span id="L269" class="lineno"><a class="lineno" href="#L269">269</a></span>
<span id="L270" class="lineno"><a class="lineno" href="#L270">270</a></span>
<span id="L271" class="lineno"><a class="lineno" href="#L271">271</a></span>
<span id="L272" class="lineno"><a class="lineno" href="#L272">272</a></span>
<span id="L273" class="lineno"><a class="lineno" href="#L273">273</a></span>
</pre></td>
<td class="table-code"><pre><span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!">Common Pydantic models and base classes.</span>
<span class="line-empty" title="No Anys on this line!">Shared structures used across multiple modules.</span>
<span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from datetime import datetime</span>
<span class="line-precise" title="No Anys on this line!">from typing import Any, Dict, Generic, List, Optional, TypeVar</span>
<span class="line-precise" title="No Anys on this line!">from enum import Enum</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from pydantic import BaseModel, Field</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"># Generic type for response data</span>
<span class="line-empty" title="No Anys on this line!">DataType = TypeVar("DataType")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Omitted Generics (x3)
Explicit (x2)">class BaseResponse(BaseModel, Generic[DataType]):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Base response model for all API responses.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    success: bool = Field(..., description="Indicates if the request was successful")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    message: str = Field(..., description="Human-readable message describing the result")</span>
<span class="line-any" title="Any Types on this line: 
Omitted Generics (x6)
Explicit (x8)">    data: Optional[DataType] = Field(None, description="Response data payload")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    errors: Optional[List[str]] = Field(None, description="List of error messages if any")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class PaginationInfo(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Pagination information for list responses.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    skip: int = Field(..., ge=0, description="Number of records skipped")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    limit: int = Field(..., ge=1, description="Maximum number of records returned")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    total: int = Field(..., ge=0, description="Total number of records available")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    has_more: bool = Field(..., description="Indicates if more records are available")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Omitted Generics (x3)
Explicit (x2)">class PaginatedResponse(BaseModel, Generic[DataType]):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Paginated response model for list endpoints.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    success: bool = Field(..., description="Indicates if the request was successful")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    message: str = Field(..., description="Human-readable message describing the result")</span>
<span class="line-any" title="Any Types on this line: 
Omitted Generics (x6)
Explicit (x8)">    data: List[DataType] = Field(..., description="List of response data items")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    pagination: PaginationInfo = Field(..., description="Pagination information")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class Status(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Common status enumeration.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    ACTIVE = "active"</span>
<span class="line-precise" title="No Anys on this line!">    INACTIVE = "inactive"</span>
<span class="line-precise" title="No Anys on this line!">    PAUSED = "paused"</span>
<span class="line-precise" title="No Anys on this line!">    DRAFT = "draft"</span>
<span class="line-precise" title="No Anys on this line!">    ARCHIVED = "archived"</span>
<span class="line-precise" title="No Anys on this line!">    DELETED = "deleted"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class Priority(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Priority levels enumeration.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    LOW = "low"</span>
<span class="line-precise" title="No Anys on this line!">    MEDIUM = "medium"</span>
<span class="line-precise" title="No Anys on this line!">    HIGH = "high"</span>
<span class="line-precise" title="No Anys on this line!">    CRITICAL = "critical"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class Currency(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Supported currencies.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    USD = "USD"</span>
<span class="line-precise" title="No Anys on this line!">    EUR = "EUR"</span>
<span class="line-precise" title="No Anys on this line!">    GBP = "GBP"</span>
<span class="line-precise" title="No Anys on this line!">    CAD = "CAD"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class Language(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Supported languages for campaigns.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    ENGLISH = "en"</span>
<span class="line-precise" title="No Anys on this line!">    DUTCH = "nl"</span>
<span class="line-precise" title="No Anys on this line!">    FRENCH = "fr"</span>
<span class="line-precise" title="No Anys on this line!">    GERMAN = "de"</span>
<span class="line-precise" title="No Anys on this line!">    SPANISH = "es"</span>
<span class="line-precise" title="No Anys on this line!">    ITALIAN = "it"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class Country(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Supported countries/regions.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    UNITED_STATES = "US"</span>
<span class="line-precise" title="No Anys on this line!">    BELGIUM = "BE"</span>
<span class="line-precise" title="No Anys on this line!">    NETHERLANDS = "NL"</span>
<span class="line-precise" title="No Anys on this line!">    FRANCE = "FR"</span>
<span class="line-precise" title="No Anys on this line!">    GERMANY = "DE"</span>
<span class="line-precise" title="No Anys on this line!">    UNITED_KINGDOM = "GB"</span>
<span class="line-precise" title="No Anys on this line!">    CANADA = "CA"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class BaseEntity(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Base entity model with common fields.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    id: str = Field(..., description="Unique identifier")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    created_at: Optional[datetime] = Field(None, description="Creation timestamp")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    created_by: Optional[str] = Field(None, description="Creator user ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    updated_by: Optional[str] = Field(None, description="Last updater user ID")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class MoneyAmount(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Model for representing monetary amounts.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    amount: float = Field(..., ge=0, description="Amount value")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    currency: Currency = Field(Currency.USD, description="Currency code")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __str__(self) -&gt; str:</span>
<span class="line-empty" title="No Anys on this line!">        """String representation of the money amount."""</span>
<span class="line-precise" title="No Anys on this line!">        if self.currency == Currency.USD:</span>
<span class="line-precise" title="No Anys on this line!">            return f"${self.amount:,.2f}"</span>
<span class="line-precise" title="No Anys on this line!">        elif self.currency == Currency.EUR:</span>
<span class="line-precise" title="No Anys on this line!">            return f"€{self.amount:,.2f}"</span>
<span class="line-precise" title="No Anys on this line!">        elif self.currency == Currency.GBP:</span>
<span class="line-precise" title="No Anys on this line!">            return f"£{self.amount:,.2f}"</span>
<span class="line-empty" title="No Anys on this line!">        else:</span>
<span class="line-precise" title="No Anys on this line!">            return f"{self.amount:,.2f} {self.currency}"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class Location(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Geographic location model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    country: Country = Field(..., description="Country code")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    region: Optional[str] = Field(None, description="Region or state")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    city: Optional[str] = Field(None, description="City name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    postal_code: Optional[str] = Field(None, description="Postal/ZIP code")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __str__(self) -&gt; str:</span>
<span class="line-empty" title="No Anys on this line!">        """String representation of the location."""</span>
<span class="line-precise" title="No Anys on this line!">        parts = [self.city, self.region, self.country.value]</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x11)
Explicit (x1)">        return ", ".join(filter(None, parts))</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class TimeRange(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Time range model for analytics and reporting.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    start_date: datetime = Field(..., description="Start date and time")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    end_date: datetime = Field(..., description="End date and time")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    timezone: str = Field("UTC", description="Timezone for the dates")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    class Config:</span>
<span class="line-precise" title="Any Types on this line: 
Unannotated (x1)">        json_encoders = {</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">            datetime: lambda v: v.isoformat()</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class MetricValue(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Model for representing metric values with metadata.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    value: float = Field(..., description="Metric value")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    previous_value: Optional[float] = Field(None, description="Previous period value for comparison")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    change_percentage: Optional[float] = Field(None, description="Percentage change from previous period")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    trend: Optional[str] = Field(None, description="Trend direction (up, down, stable)")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    confidence: Optional[float] = Field(None, ge=0, le=1, description="Confidence score for the metric")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class Tag(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Tag model for categorizing entities.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    key: str = Field(..., min_length=1, max_length=50, description="Tag key")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    value: str = Field(..., min_length=1, max_length=100, description="Tag value")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __str__(self) -&gt; str:</span>
<span class="line-empty" title="No Anys on this line!">        """String representation of the tag."""</span>
<span class="line-precise" title="No Anys on this line!">        return f"{self.key}:{self.value}"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class Address(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Physical address model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    street_address: str = Field(..., description="Street address")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    city: str = Field(..., description="City name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    state_province: Optional[str] = Field(None, description="State or province")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    postal_code: str = Field(..., description="Postal or ZIP code")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    country: Country = Field(..., description="Country code")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __str__(self) -&gt; str:</span>
<span class="line-empty" title="No Anys on this line!">        """String representation of the address."""</span>
<span class="line-precise" title="No Anys on this line!">        parts = [</span>
<span class="line-precise" title="No Anys on this line!">            self.street_address,</span>
<span class="line-precise" title="No Anys on this line!">            self.city,</span>
<span class="line-precise" title="No Anys on this line!">            self.state_province,</span>
<span class="line-precise" title="No Anys on this line!">            self.postal_code,</span>
<span class="line-precise" title="No Anys on this line!">            self.country.value</span>
<span class="line-empty" title="No Anys on this line!">        ]</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x11)
Explicit (x1)">        return ", ".join(filter(None, parts))</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class ContactInfo(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Contact information model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    email: Optional[str] = Field(None, description="Email address")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    phone: Optional[str] = Field(None, description="Phone number")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    website: Optional[str] = Field(None, description="Website URL")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    address: Optional[Address] = Field(None, description="Physical address")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class Percentage(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Model for representing percentage values.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    value: float = Field(..., ge=0, le=100, description="Percentage value (0-100)")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __str__(self) -&gt; str:</span>
<span class="line-empty" title="No Anys on this line!">        """String representation of the percentage."""</span>
<span class="line-precise" title="No Anys on this line!">        return f"{self.value:.2f}%"</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    @property</span>
<span class="line-precise" title="No Anys on this line!">    def decimal(self) -&gt; float:</span>
<span class="line-empty" title="No Anys on this line!">        """Get the decimal representation (0-1)."""</span>
<span class="line-precise" title="No Anys on this line!">        return self.value / 100</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class ErrorDetail(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Detailed error information.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    code: str = Field(..., description="Error code")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    message: str = Field(..., description="Error message")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    field: Optional[str] = Field(None, description="Field that caused the error")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    context: Optional[Dict[str, Any]] = Field(None, description="Additional error context")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class ValidationErrorResponse(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Validation error response model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    success: bool = Field(False, description="Always false for error responses")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    message: str = Field(..., description="Error summary message")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    errors: List[ErrorDetail] = Field(..., description="Detailed error information")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class HealthStatus(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Health check status model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    status: str = Field(..., description="Overall health status")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    checks: Dict[str, Dict[str, Any]] = Field(..., description="Individual service check results")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    timestamp: datetime = Field(..., description="Health check timestamp")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    uptime_seconds: float = Field(..., description="Application uptime in seconds")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class RateLimitInfo(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Rate limiting information.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    limit: int = Field(..., description="Request limit per time window")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    remaining: int = Field(..., description="Remaining requests in current window")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    reset_time: datetime = Field(..., description="Time when the limit resets")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    window_seconds: int = Field(..., description="Time window in seconds")</span>
</pre></td>
</tr></tbody>
</table>
</body>
</html>
