<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../mypy-html.css">
</head>
<body>
<h2>models.campaigns</h2>
<table>
<caption>models/campaigns.py</caption>
<tbody><tr>
<td class="table-lines"><pre><span id="L1" class="lineno"><a class="lineno" href="#L1">1</a></span>
<span id="L2" class="lineno"><a class="lineno" href="#L2">2</a></span>
<span id="L3" class="lineno"><a class="lineno" href="#L3">3</a></span>
<span id="L4" class="lineno"><a class="lineno" href="#L4">4</a></span>
<span id="L5" class="lineno"><a class="lineno" href="#L5">5</a></span>
<span id="L6" class="lineno"><a class="lineno" href="#L6">6</a></span>
<span id="L7" class="lineno"><a class="lineno" href="#L7">7</a></span>
<span id="L8" class="lineno"><a class="lineno" href="#L8">8</a></span>
<span id="L9" class="lineno"><a class="lineno" href="#L9">9</a></span>
<span id="L10" class="lineno"><a class="lineno" href="#L10">10</a></span>
<span id="L11" class="lineno"><a class="lineno" href="#L11">11</a></span>
<span id="L12" class="lineno"><a class="lineno" href="#L12">12</a></span>
<span id="L13" class="lineno"><a class="lineno" href="#L13">13</a></span>
<span id="L14" class="lineno"><a class="lineno" href="#L14">14</a></span>
<span id="L15" class="lineno"><a class="lineno" href="#L15">15</a></span>
<span id="L16" class="lineno"><a class="lineno" href="#L16">16</a></span>
<span id="L17" class="lineno"><a class="lineno" href="#L17">17</a></span>
<span id="L18" class="lineno"><a class="lineno" href="#L18">18</a></span>
<span id="L19" class="lineno"><a class="lineno" href="#L19">19</a></span>
<span id="L20" class="lineno"><a class="lineno" href="#L20">20</a></span>
<span id="L21" class="lineno"><a class="lineno" href="#L21">21</a></span>
<span id="L22" class="lineno"><a class="lineno" href="#L22">22</a></span>
<span id="L23" class="lineno"><a class="lineno" href="#L23">23</a></span>
<span id="L24" class="lineno"><a class="lineno" href="#L24">24</a></span>
<span id="L25" class="lineno"><a class="lineno" href="#L25">25</a></span>
<span id="L26" class="lineno"><a class="lineno" href="#L26">26</a></span>
<span id="L27" class="lineno"><a class="lineno" href="#L27">27</a></span>
<span id="L28" class="lineno"><a class="lineno" href="#L28">28</a></span>
<span id="L29" class="lineno"><a class="lineno" href="#L29">29</a></span>
<span id="L30" class="lineno"><a class="lineno" href="#L30">30</a></span>
<span id="L31" class="lineno"><a class="lineno" href="#L31">31</a></span>
<span id="L32" class="lineno"><a class="lineno" href="#L32">32</a></span>
<span id="L33" class="lineno"><a class="lineno" href="#L33">33</a></span>
<span id="L34" class="lineno"><a class="lineno" href="#L34">34</a></span>
<span id="L35" class="lineno"><a class="lineno" href="#L35">35</a></span>
<span id="L36" class="lineno"><a class="lineno" href="#L36">36</a></span>
<span id="L37" class="lineno"><a class="lineno" href="#L37">37</a></span>
<span id="L38" class="lineno"><a class="lineno" href="#L38">38</a></span>
<span id="L39" class="lineno"><a class="lineno" href="#L39">39</a></span>
<span id="L40" class="lineno"><a class="lineno" href="#L40">40</a></span>
<span id="L41" class="lineno"><a class="lineno" href="#L41">41</a></span>
<span id="L42" class="lineno"><a class="lineno" href="#L42">42</a></span>
<span id="L43" class="lineno"><a class="lineno" href="#L43">43</a></span>
<span id="L44" class="lineno"><a class="lineno" href="#L44">44</a></span>
<span id="L45" class="lineno"><a class="lineno" href="#L45">45</a></span>
<span id="L46" class="lineno"><a class="lineno" href="#L46">46</a></span>
<span id="L47" class="lineno"><a class="lineno" href="#L47">47</a></span>
<span id="L48" class="lineno"><a class="lineno" href="#L48">48</a></span>
<span id="L49" class="lineno"><a class="lineno" href="#L49">49</a></span>
<span id="L50" class="lineno"><a class="lineno" href="#L50">50</a></span>
<span id="L51" class="lineno"><a class="lineno" href="#L51">51</a></span>
<span id="L52" class="lineno"><a class="lineno" href="#L52">52</a></span>
<span id="L53" class="lineno"><a class="lineno" href="#L53">53</a></span>
<span id="L54" class="lineno"><a class="lineno" href="#L54">54</a></span>
<span id="L55" class="lineno"><a class="lineno" href="#L55">55</a></span>
<span id="L56" class="lineno"><a class="lineno" href="#L56">56</a></span>
<span id="L57" class="lineno"><a class="lineno" href="#L57">57</a></span>
<span id="L58" class="lineno"><a class="lineno" href="#L58">58</a></span>
<span id="L59" class="lineno"><a class="lineno" href="#L59">59</a></span>
<span id="L60" class="lineno"><a class="lineno" href="#L60">60</a></span>
<span id="L61" class="lineno"><a class="lineno" href="#L61">61</a></span>
<span id="L62" class="lineno"><a class="lineno" href="#L62">62</a></span>
<span id="L63" class="lineno"><a class="lineno" href="#L63">63</a></span>
<span id="L64" class="lineno"><a class="lineno" href="#L64">64</a></span>
<span id="L65" class="lineno"><a class="lineno" href="#L65">65</a></span>
<span id="L66" class="lineno"><a class="lineno" href="#L66">66</a></span>
<span id="L67" class="lineno"><a class="lineno" href="#L67">67</a></span>
<span id="L68" class="lineno"><a class="lineno" href="#L68">68</a></span>
<span id="L69" class="lineno"><a class="lineno" href="#L69">69</a></span>
<span id="L70" class="lineno"><a class="lineno" href="#L70">70</a></span>
<span id="L71" class="lineno"><a class="lineno" href="#L71">71</a></span>
<span id="L72" class="lineno"><a class="lineno" href="#L72">72</a></span>
<span id="L73" class="lineno"><a class="lineno" href="#L73">73</a></span>
<span id="L74" class="lineno"><a class="lineno" href="#L74">74</a></span>
<span id="L75" class="lineno"><a class="lineno" href="#L75">75</a></span>
<span id="L76" class="lineno"><a class="lineno" href="#L76">76</a></span>
<span id="L77" class="lineno"><a class="lineno" href="#L77">77</a></span>
<span id="L78" class="lineno"><a class="lineno" href="#L78">78</a></span>
<span id="L79" class="lineno"><a class="lineno" href="#L79">79</a></span>
<span id="L80" class="lineno"><a class="lineno" href="#L80">80</a></span>
<span id="L81" class="lineno"><a class="lineno" href="#L81">81</a></span>
<span id="L82" class="lineno"><a class="lineno" href="#L82">82</a></span>
<span id="L83" class="lineno"><a class="lineno" href="#L83">83</a></span>
<span id="L84" class="lineno"><a class="lineno" href="#L84">84</a></span>
<span id="L85" class="lineno"><a class="lineno" href="#L85">85</a></span>
<span id="L86" class="lineno"><a class="lineno" href="#L86">86</a></span>
<span id="L87" class="lineno"><a class="lineno" href="#L87">87</a></span>
<span id="L88" class="lineno"><a class="lineno" href="#L88">88</a></span>
<span id="L89" class="lineno"><a class="lineno" href="#L89">89</a></span>
<span id="L90" class="lineno"><a class="lineno" href="#L90">90</a></span>
<span id="L91" class="lineno"><a class="lineno" href="#L91">91</a></span>
<span id="L92" class="lineno"><a class="lineno" href="#L92">92</a></span>
<span id="L93" class="lineno"><a class="lineno" href="#L93">93</a></span>
<span id="L94" class="lineno"><a class="lineno" href="#L94">94</a></span>
<span id="L95" class="lineno"><a class="lineno" href="#L95">95</a></span>
<span id="L96" class="lineno"><a class="lineno" href="#L96">96</a></span>
<span id="L97" class="lineno"><a class="lineno" href="#L97">97</a></span>
<span id="L98" class="lineno"><a class="lineno" href="#L98">98</a></span>
<span id="L99" class="lineno"><a class="lineno" href="#L99">99</a></span>
<span id="L100" class="lineno"><a class="lineno" href="#L100">100</a></span>
<span id="L101" class="lineno"><a class="lineno" href="#L101">101</a></span>
<span id="L102" class="lineno"><a class="lineno" href="#L102">102</a></span>
<span id="L103" class="lineno"><a class="lineno" href="#L103">103</a></span>
<span id="L104" class="lineno"><a class="lineno" href="#L104">104</a></span>
<span id="L105" class="lineno"><a class="lineno" href="#L105">105</a></span>
<span id="L106" class="lineno"><a class="lineno" href="#L106">106</a></span>
<span id="L107" class="lineno"><a class="lineno" href="#L107">107</a></span>
<span id="L108" class="lineno"><a class="lineno" href="#L108">108</a></span>
<span id="L109" class="lineno"><a class="lineno" href="#L109">109</a></span>
<span id="L110" class="lineno"><a class="lineno" href="#L110">110</a></span>
<span id="L111" class="lineno"><a class="lineno" href="#L111">111</a></span>
<span id="L112" class="lineno"><a class="lineno" href="#L112">112</a></span>
<span id="L113" class="lineno"><a class="lineno" href="#L113">113</a></span>
<span id="L114" class="lineno"><a class="lineno" href="#L114">114</a></span>
<span id="L115" class="lineno"><a class="lineno" href="#L115">115</a></span>
<span id="L116" class="lineno"><a class="lineno" href="#L116">116</a></span>
<span id="L117" class="lineno"><a class="lineno" href="#L117">117</a></span>
<span id="L118" class="lineno"><a class="lineno" href="#L118">118</a></span>
<span id="L119" class="lineno"><a class="lineno" href="#L119">119</a></span>
<span id="L120" class="lineno"><a class="lineno" href="#L120">120</a></span>
<span id="L121" class="lineno"><a class="lineno" href="#L121">121</a></span>
<span id="L122" class="lineno"><a class="lineno" href="#L122">122</a></span>
<span id="L123" class="lineno"><a class="lineno" href="#L123">123</a></span>
<span id="L124" class="lineno"><a class="lineno" href="#L124">124</a></span>
<span id="L125" class="lineno"><a class="lineno" href="#L125">125</a></span>
<span id="L126" class="lineno"><a class="lineno" href="#L126">126</a></span>
<span id="L127" class="lineno"><a class="lineno" href="#L127">127</a></span>
<span id="L128" class="lineno"><a class="lineno" href="#L128">128</a></span>
<span id="L129" class="lineno"><a class="lineno" href="#L129">129</a></span>
<span id="L130" class="lineno"><a class="lineno" href="#L130">130</a></span>
<span id="L131" class="lineno"><a class="lineno" href="#L131">131</a></span>
<span id="L132" class="lineno"><a class="lineno" href="#L132">132</a></span>
<span id="L133" class="lineno"><a class="lineno" href="#L133">133</a></span>
<span id="L134" class="lineno"><a class="lineno" href="#L134">134</a></span>
<span id="L135" class="lineno"><a class="lineno" href="#L135">135</a></span>
<span id="L136" class="lineno"><a class="lineno" href="#L136">136</a></span>
<span id="L137" class="lineno"><a class="lineno" href="#L137">137</a></span>
<span id="L138" class="lineno"><a class="lineno" href="#L138">138</a></span>
<span id="L139" class="lineno"><a class="lineno" href="#L139">139</a></span>
<span id="L140" class="lineno"><a class="lineno" href="#L140">140</a></span>
<span id="L141" class="lineno"><a class="lineno" href="#L141">141</a></span>
<span id="L142" class="lineno"><a class="lineno" href="#L142">142</a></span>
<span id="L143" class="lineno"><a class="lineno" href="#L143">143</a></span>
<span id="L144" class="lineno"><a class="lineno" href="#L144">144</a></span>
<span id="L145" class="lineno"><a class="lineno" href="#L145">145</a></span>
<span id="L146" class="lineno"><a class="lineno" href="#L146">146</a></span>
<span id="L147" class="lineno"><a class="lineno" href="#L147">147</a></span>
<span id="L148" class="lineno"><a class="lineno" href="#L148">148</a></span>
<span id="L149" class="lineno"><a class="lineno" href="#L149">149</a></span>
<span id="L150" class="lineno"><a class="lineno" href="#L150">150</a></span>
<span id="L151" class="lineno"><a class="lineno" href="#L151">151</a></span>
<span id="L152" class="lineno"><a class="lineno" href="#L152">152</a></span>
<span id="L153" class="lineno"><a class="lineno" href="#L153">153</a></span>
<span id="L154" class="lineno"><a class="lineno" href="#L154">154</a></span>
<span id="L155" class="lineno"><a class="lineno" href="#L155">155</a></span>
<span id="L156" class="lineno"><a class="lineno" href="#L156">156</a></span>
<span id="L157" class="lineno"><a class="lineno" href="#L157">157</a></span>
<span id="L158" class="lineno"><a class="lineno" href="#L158">158</a></span>
<span id="L159" class="lineno"><a class="lineno" href="#L159">159</a></span>
<span id="L160" class="lineno"><a class="lineno" href="#L160">160</a></span>
<span id="L161" class="lineno"><a class="lineno" href="#L161">161</a></span>
<span id="L162" class="lineno"><a class="lineno" href="#L162">162</a></span>
<span id="L163" class="lineno"><a class="lineno" href="#L163">163</a></span>
<span id="L164" class="lineno"><a class="lineno" href="#L164">164</a></span>
<span id="L165" class="lineno"><a class="lineno" href="#L165">165</a></span>
<span id="L166" class="lineno"><a class="lineno" href="#L166">166</a></span>
<span id="L167" class="lineno"><a class="lineno" href="#L167">167</a></span>
<span id="L168" class="lineno"><a class="lineno" href="#L168">168</a></span>
<span id="L169" class="lineno"><a class="lineno" href="#L169">169</a></span>
<span id="L170" class="lineno"><a class="lineno" href="#L170">170</a></span>
<span id="L171" class="lineno"><a class="lineno" href="#L171">171</a></span>
<span id="L172" class="lineno"><a class="lineno" href="#L172">172</a></span>
<span id="L173" class="lineno"><a class="lineno" href="#L173">173</a></span>
<span id="L174" class="lineno"><a class="lineno" href="#L174">174</a></span>
<span id="L175" class="lineno"><a class="lineno" href="#L175">175</a></span>
<span id="L176" class="lineno"><a class="lineno" href="#L176">176</a></span>
<span id="L177" class="lineno"><a class="lineno" href="#L177">177</a></span>
<span id="L178" class="lineno"><a class="lineno" href="#L178">178</a></span>
<span id="L179" class="lineno"><a class="lineno" href="#L179">179</a></span>
<span id="L180" class="lineno"><a class="lineno" href="#L180">180</a></span>
<span id="L181" class="lineno"><a class="lineno" href="#L181">181</a></span>
<span id="L182" class="lineno"><a class="lineno" href="#L182">182</a></span>
<span id="L183" class="lineno"><a class="lineno" href="#L183">183</a></span>
<span id="L184" class="lineno"><a class="lineno" href="#L184">184</a></span>
<span id="L185" class="lineno"><a class="lineno" href="#L185">185</a></span>
<span id="L186" class="lineno"><a class="lineno" href="#L186">186</a></span>
<span id="L187" class="lineno"><a class="lineno" href="#L187">187</a></span>
<span id="L188" class="lineno"><a class="lineno" href="#L188">188</a></span>
<span id="L189" class="lineno"><a class="lineno" href="#L189">189</a></span>
<span id="L190" class="lineno"><a class="lineno" href="#L190">190</a></span>
<span id="L191" class="lineno"><a class="lineno" href="#L191">191</a></span>
<span id="L192" class="lineno"><a class="lineno" href="#L192">192</a></span>
<span id="L193" class="lineno"><a class="lineno" href="#L193">193</a></span>
<span id="L194" class="lineno"><a class="lineno" href="#L194">194</a></span>
<span id="L195" class="lineno"><a class="lineno" href="#L195">195</a></span>
<span id="L196" class="lineno"><a class="lineno" href="#L196">196</a></span>
<span id="L197" class="lineno"><a class="lineno" href="#L197">197</a></span>
<span id="L198" class="lineno"><a class="lineno" href="#L198">198</a></span>
<span id="L199" class="lineno"><a class="lineno" href="#L199">199</a></span>
<span id="L200" class="lineno"><a class="lineno" href="#L200">200</a></span>
<span id="L201" class="lineno"><a class="lineno" href="#L201">201</a></span>
<span id="L202" class="lineno"><a class="lineno" href="#L202">202</a></span>
<span id="L203" class="lineno"><a class="lineno" href="#L203">203</a></span>
<span id="L204" class="lineno"><a class="lineno" href="#L204">204</a></span>
<span id="L205" class="lineno"><a class="lineno" href="#L205">205</a></span>
<span id="L206" class="lineno"><a class="lineno" href="#L206">206</a></span>
<span id="L207" class="lineno"><a class="lineno" href="#L207">207</a></span>
<span id="L208" class="lineno"><a class="lineno" href="#L208">208</a></span>
<span id="L209" class="lineno"><a class="lineno" href="#L209">209</a></span>
<span id="L210" class="lineno"><a class="lineno" href="#L210">210</a></span>
<span id="L211" class="lineno"><a class="lineno" href="#L211">211</a></span>
<span id="L212" class="lineno"><a class="lineno" href="#L212">212</a></span>
<span id="L213" class="lineno"><a class="lineno" href="#L213">213</a></span>
<span id="L214" class="lineno"><a class="lineno" href="#L214">214</a></span>
<span id="L215" class="lineno"><a class="lineno" href="#L215">215</a></span>
<span id="L216" class="lineno"><a class="lineno" href="#L216">216</a></span>
<span id="L217" class="lineno"><a class="lineno" href="#L217">217</a></span>
<span id="L218" class="lineno"><a class="lineno" href="#L218">218</a></span>
<span id="L219" class="lineno"><a class="lineno" href="#L219">219</a></span>
<span id="L220" class="lineno"><a class="lineno" href="#L220">220</a></span>
<span id="L221" class="lineno"><a class="lineno" href="#L221">221</a></span>
<span id="L222" class="lineno"><a class="lineno" href="#L222">222</a></span>
<span id="L223" class="lineno"><a class="lineno" href="#L223">223</a></span>
<span id="L224" class="lineno"><a class="lineno" href="#L224">224</a></span>
<span id="L225" class="lineno"><a class="lineno" href="#L225">225</a></span>
<span id="L226" class="lineno"><a class="lineno" href="#L226">226</a></span>
<span id="L227" class="lineno"><a class="lineno" href="#L227">227</a></span>
<span id="L228" class="lineno"><a class="lineno" href="#L228">228</a></span>
<span id="L229" class="lineno"><a class="lineno" href="#L229">229</a></span>
<span id="L230" class="lineno"><a class="lineno" href="#L230">230</a></span>
<span id="L231" class="lineno"><a class="lineno" href="#L231">231</a></span>
<span id="L232" class="lineno"><a class="lineno" href="#L232">232</a></span>
<span id="L233" class="lineno"><a class="lineno" href="#L233">233</a></span>
<span id="L234" class="lineno"><a class="lineno" href="#L234">234</a></span>
<span id="L235" class="lineno"><a class="lineno" href="#L235">235</a></span>
<span id="L236" class="lineno"><a class="lineno" href="#L236">236</a></span>
<span id="L237" class="lineno"><a class="lineno" href="#L237">237</a></span>
<span id="L238" class="lineno"><a class="lineno" href="#L238">238</a></span>
<span id="L239" class="lineno"><a class="lineno" href="#L239">239</a></span>
<span id="L240" class="lineno"><a class="lineno" href="#L240">240</a></span>
<span id="L241" class="lineno"><a class="lineno" href="#L241">241</a></span>
<span id="L242" class="lineno"><a class="lineno" href="#L242">242</a></span>
<span id="L243" class="lineno"><a class="lineno" href="#L243">243</a></span>
<span id="L244" class="lineno"><a class="lineno" href="#L244">244</a></span>
<span id="L245" class="lineno"><a class="lineno" href="#L245">245</a></span>
<span id="L246" class="lineno"><a class="lineno" href="#L246">246</a></span>
<span id="L247" class="lineno"><a class="lineno" href="#L247">247</a></span>
<span id="L248" class="lineno"><a class="lineno" href="#L248">248</a></span>
<span id="L249" class="lineno"><a class="lineno" href="#L249">249</a></span>
<span id="L250" class="lineno"><a class="lineno" href="#L250">250</a></span>
<span id="L251" class="lineno"><a class="lineno" href="#L251">251</a></span>
<span id="L252" class="lineno"><a class="lineno" href="#L252">252</a></span>
<span id="L253" class="lineno"><a class="lineno" href="#L253">253</a></span>
<span id="L254" class="lineno"><a class="lineno" href="#L254">254</a></span>
<span id="L255" class="lineno"><a class="lineno" href="#L255">255</a></span>
<span id="L256" class="lineno"><a class="lineno" href="#L256">256</a></span>
<span id="L257" class="lineno"><a class="lineno" href="#L257">257</a></span>
<span id="L258" class="lineno"><a class="lineno" href="#L258">258</a></span>
<span id="L259" class="lineno"><a class="lineno" href="#L259">259</a></span>
<span id="L260" class="lineno"><a class="lineno" href="#L260">260</a></span>
<span id="L261" class="lineno"><a class="lineno" href="#L261">261</a></span>
<span id="L262" class="lineno"><a class="lineno" href="#L262">262</a></span>
<span id="L263" class="lineno"><a class="lineno" href="#L263">263</a></span>
<span id="L264" class="lineno"><a class="lineno" href="#L264">264</a></span>
<span id="L265" class="lineno"><a class="lineno" href="#L265">265</a></span>
<span id="L266" class="lineno"><a class="lineno" href="#L266">266</a></span>
<span id="L267" class="lineno"><a class="lineno" href="#L267">267</a></span>
<span id="L268" class="lineno"><a class="lineno" href="#L268">268</a></span>
<span id="L269" class="lineno"><a class="lineno" href="#L269">269</a></span>
<span id="L270" class="lineno"><a class="lineno" href="#L270">270</a></span>
<span id="L271" class="lineno"><a class="lineno" href="#L271">271</a></span>
<span id="L272" class="lineno"><a class="lineno" href="#L272">272</a></span>
<span id="L273" class="lineno"><a class="lineno" href="#L273">273</a></span>
<span id="L274" class="lineno"><a class="lineno" href="#L274">274</a></span>
<span id="L275" class="lineno"><a class="lineno" href="#L275">275</a></span>
<span id="L276" class="lineno"><a class="lineno" href="#L276">276</a></span>
<span id="L277" class="lineno"><a class="lineno" href="#L277">277</a></span>
<span id="L278" class="lineno"><a class="lineno" href="#L278">278</a></span>
<span id="L279" class="lineno"><a class="lineno" href="#L279">279</a></span>
<span id="L280" class="lineno"><a class="lineno" href="#L280">280</a></span>
<span id="L281" class="lineno"><a class="lineno" href="#L281">281</a></span>
<span id="L282" class="lineno"><a class="lineno" href="#L282">282</a></span>
<span id="L283" class="lineno"><a class="lineno" href="#L283">283</a></span>
<span id="L284" class="lineno"><a class="lineno" href="#L284">284</a></span>
<span id="L285" class="lineno"><a class="lineno" href="#L285">285</a></span>
<span id="L286" class="lineno"><a class="lineno" href="#L286">286</a></span>
<span id="L287" class="lineno"><a class="lineno" href="#L287">287</a></span>
<span id="L288" class="lineno"><a class="lineno" href="#L288">288</a></span>
<span id="L289" class="lineno"><a class="lineno" href="#L289">289</a></span>
<span id="L290" class="lineno"><a class="lineno" href="#L290">290</a></span>
<span id="L291" class="lineno"><a class="lineno" href="#L291">291</a></span>
<span id="L292" class="lineno"><a class="lineno" href="#L292">292</a></span>
<span id="L293" class="lineno"><a class="lineno" href="#L293">293</a></span>
<span id="L294" class="lineno"><a class="lineno" href="#L294">294</a></span>
<span id="L295" class="lineno"><a class="lineno" href="#L295">295</a></span>
<span id="L296" class="lineno"><a class="lineno" href="#L296">296</a></span>
<span id="L297" class="lineno"><a class="lineno" href="#L297">297</a></span>
<span id="L298" class="lineno"><a class="lineno" href="#L298">298</a></span>
<span id="L299" class="lineno"><a class="lineno" href="#L299">299</a></span>
<span id="L300" class="lineno"><a class="lineno" href="#L300">300</a></span>
<span id="L301" class="lineno"><a class="lineno" href="#L301">301</a></span>
<span id="L302" class="lineno"><a class="lineno" href="#L302">302</a></span>
<span id="L303" class="lineno"><a class="lineno" href="#L303">303</a></span>
<span id="L304" class="lineno"><a class="lineno" href="#L304">304</a></span>
<span id="L305" class="lineno"><a class="lineno" href="#L305">305</a></span>
<span id="L306" class="lineno"><a class="lineno" href="#L306">306</a></span>
<span id="L307" class="lineno"><a class="lineno" href="#L307">307</a></span>
<span id="L308" class="lineno"><a class="lineno" href="#L308">308</a></span>
<span id="L309" class="lineno"><a class="lineno" href="#L309">309</a></span>
<span id="L310" class="lineno"><a class="lineno" href="#L310">310</a></span>
<span id="L311" class="lineno"><a class="lineno" href="#L311">311</a></span>
<span id="L312" class="lineno"><a class="lineno" href="#L312">312</a></span>
<span id="L313" class="lineno"><a class="lineno" href="#L313">313</a></span>
<span id="L314" class="lineno"><a class="lineno" href="#L314">314</a></span>
<span id="L315" class="lineno"><a class="lineno" href="#L315">315</a></span>
<span id="L316" class="lineno"><a class="lineno" href="#L316">316</a></span>
</pre></td>
<td class="table-code"><pre><span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!">Campaign-related Pydantic models.</span>
<span class="line-empty" title="No Anys on this line!">Data structures for Google Ads campaigns, ad groups, ads, and keywords.</span>
<span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from datetime import datetime</span>
<span class="line-precise" title="No Anys on this line!">from typing import Any, Dict, List, Optional</span>
<span class="line-precise" title="No Anys on this line!">from enum import Enum</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from pydantic import BaseModel, Field, validator</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from .common import BaseEntity, BaseResponse, PaginatedResponse, MoneyAmount, Currency, Language, Country, Status</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class CampaignType(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Google Ads campaign types.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    SEARCH = "search"</span>
<span class="line-precise" title="No Anys on this line!">    DISPLAY = "display"</span>
<span class="line-precise" title="No Anys on this line!">    SHOPPING = "shopping"</span>
<span class="line-precise" title="No Anys on this line!">    VIDEO = "video"</span>
<span class="line-precise" title="No Anys on this line!">    PERFORMANCE_MAX = "performance_max"</span>
<span class="line-precise" title="No Anys on this line!">    DISCOVERY = "discovery"</span>
<span class="line-precise" title="No Anys on this line!">    LOCAL = "local"</span>
<span class="line-precise" title="No Anys on this line!">    SMART = "smart"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class CampaignStatus(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Campaign status enumeration.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    DRAFT = "draft"</span>
<span class="line-precise" title="No Anys on this line!">    ACTIVE = "active"</span>
<span class="line-precise" title="No Anys on this line!">    PAUSED = "paused"</span>
<span class="line-precise" title="No Anys on this line!">    REMOVED = "removed"</span>
<span class="line-precise" title="No Anys on this line!">    ENDED = "ended"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class BiddingStrategy(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Bidding strategy types.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    MANUAL_CPC = "manual_cpc"</span>
<span class="line-precise" title="No Anys on this line!">    ENHANCED_CPC = "enhanced_cpc"</span>
<span class="line-precise" title="No Anys on this line!">    TARGET_CPA = "target_cpa"</span>
<span class="line-precise" title="No Anys on this line!">    TARGET_ROAS = "target_roas"</span>
<span class="line-precise" title="No Anys on this line!">    MAXIMIZE_CLICKS = "maximize_clicks"</span>
<span class="line-precise" title="No Anys on this line!">    MAXIMIZE_CONVERSIONS = "maximize_conversions"</span>
<span class="line-precise" title="No Anys on this line!">    MAXIMIZE_CONVERSION_VALUE = "maximize_conversion_value"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class AdType(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Ad types enumeration.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    TEXT_AD = "text_ad"</span>
<span class="line-precise" title="No Anys on this line!">    EXPANDED_TEXT_AD = "expanded_text_ad"</span>
<span class="line-precise" title="No Anys on this line!">    RESPONSIVE_SEARCH_AD = "responsive_search_ad"</span>
<span class="line-precise" title="No Anys on this line!">    DISPLAY_AD = "display_ad"</span>
<span class="line-precise" title="No Anys on this line!">    IMAGE_AD = "image_ad"</span>
<span class="line-precise" title="No Anys on this line!">    VIDEO_AD = "video_ad"</span>
<span class="line-precise" title="No Anys on this line!">    SHOPPING_AD = "shopping_ad"</span>
<span class="line-precise" title="No Anys on this line!">    CALL_AD = "call_ad"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class KeywordMatchType(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Keyword match types.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    EXACT = "exact"</span>
<span class="line-precise" title="No Anys on this line!">    PHRASE = "phrase"</span>
<span class="line-precise" title="No Anys on this line!">    BROAD = "broad"</span>
<span class="line-precise" title="No Anys on this line!">    BROAD_MODIFIED = "broad_modified"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class AdStatus(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Ad status enumeration.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    ENABLED = "enabled"</span>
<span class="line-precise" title="No Anys on this line!">    PAUSED = "paused"</span>
<span class="line-precise" title="No Anys on this line!">    REMOVED = "removed"</span>
<span class="line-precise" title="No Anys on this line!">    PENDING_REVIEW = "pending_review"</span>
<span class="line-precise" title="No Anys on this line!">    UNDER_REVIEW = "under_review"</span>
<span class="line-precise" title="No Anys on this line!">    APPROVED = "approved"</span>
<span class="line-precise" title="No Anys on this line!">    DISAPPROVED = "disapproved"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class CampaignBudget(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Campaign budget configuration.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    daily_amount: float = Field(..., gt=0, description="Daily budget amount")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    currency: Currency = Field(Currency.USD, description="Budget currency")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    delivery_method: str = Field("standard", description="Budget delivery method (standard, accelerated)")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    total_amount: Optional[float] = Field(None, description="Total campaign budget limit")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class TargetingCriteria(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Campaign targeting criteria.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    locations: List[str] = Field(default=[], description="Targeted geographic locations")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    languages: List[Language] = Field(default=[], description="Targeted languages")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    age_ranges: Optional[List[str]] = Field(None, description="Targeted age ranges")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    genders: Optional[List[str]] = Field(None, description="Targeted genders")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    devices: Optional[List[str]] = Field(None, description="Targeted devices")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    audiences: Optional[List[str]] = Field(None, description="Targeted audience segments")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class Keyword(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Keyword model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    id: Optional[str] = Field(None, description="Keyword ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    text: str = Field(..., min_length=1, max_length=80, description="Keyword text")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    match_type: KeywordMatchType = Field(..., description="Keyword match type")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    max_cpc: Optional[float] = Field(None, gt=0, description="Maximum cost per click")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    quality_score: Optional[int] = Field(None, ge=1, le=10, description="Quality score (1-10)")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    status: AdStatus = Field(AdStatus.ENABLED, description="Keyword status")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    negative: bool = Field(False, description="Whether this is a negative keyword")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AdAsset(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Ad asset (headline, description, image, etc.).</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    id: Optional[str] = Field(None, description="Asset ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: str = Field(..., description="Asset type (headline, description, image, etc.)")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    content: str = Field(..., description="Asset content or URL")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    pinned_position: Optional[int] = Field(None, description="Pinned position for the asset")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    performance_label: Optional[str] = Field(None, description="Performance label (good, poor, etc.)")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class Ad(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Ad model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    id: Optional[str] = Field(None, description="Ad ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: AdType = Field(..., description="Ad type")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    status: AdStatus = Field(AdStatus.ENABLED, description="Ad status")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    headlines: List[AdAsset] = Field(default=[], description="Ad headlines")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    descriptions: List[AdAsset] = Field(default=[], description="Ad descriptions")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    display_url: Optional[str] = Field(None, description="Display URL")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    final_urls: List[str] = Field(default=[], description="Final landing page URLs")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    image_assets: Optional[List[AdAsset]] = Field(None, description="Image assets for display ads")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    video_assets: Optional[List[AdAsset]] = Field(None, description="Video assets for video ads")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AdGroup(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Ad group model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    id: Optional[str] = Field(None, description="Ad group ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: str = Field(..., min_length=1, max_length=255, description="Ad group name")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    status: AdStatus = Field(AdStatus.ENABLED, description="Ad group status")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    max_cpc: Optional[float] = Field(None, gt=0, description="Maximum cost per click")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    keywords: List[Keyword] = Field(default=[], description="Keywords in this ad group")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    ads: List[Ad] = Field(default=[], description="Ads in this ad group")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    targeting: Optional[TargetingCriteria] = Field(None, description="Additional targeting criteria")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class CampaignMetrics(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Campaign performance metrics.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    impressions: int = Field(0, ge=0, description="Number of impressions")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    clicks: int = Field(0, ge=0, description="Number of clicks")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    conversions: float = Field(0, ge=0, description="Number of conversions")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    cost: float = Field(0, ge=0, description="Total cost")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    revenue: Optional[float] = Field(None, ge=0, description="Total revenue")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Calculated metrics</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    ctr: Optional[float] = Field(None, ge=0, le=1, description="Click-through rate")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    cpc: Optional[float] = Field(None, ge=0, description="Cost per click")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    cpm: Optional[float] = Field(None, ge=0, description="Cost per thousand impressions")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    conversion_rate: Optional[float] = Field(None, ge=0, le=1, description="Conversion rate")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    cost_per_conversion: Optional[float] = Field(None, ge=0, description="Cost per conversion")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    roas: Optional[float] = Field(None, ge=0, description="Return on ad spend")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    roi: Optional[float] = Field(None, description="Return on investment")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class Campaign(BaseEntity):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Main campaign model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: str = Field(..., min_length=1, max_length=255, description="Campaign name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: Optional[str] = Field(None, max_length=1000, description="Campaign description")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: CampaignType = Field(..., description="Campaign type")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    status: CampaignStatus = Field(CampaignStatus.DRAFT, description="Campaign status")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Budget and bidding</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    budget: Optional[CampaignBudget] = Field(None, description="Campaign budget configuration")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    budget_amount: float = Field(..., gt=0, description="Daily budget amount (for backward compatibility)")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    bidding_strategy: BiddingStrategy = Field(BiddingStrategy.MANUAL_CPC, description="Bidding strategy")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    target_cpa: Optional[float] = Field(None, gt=0, description="Target cost per acquisition")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    target_roas: Optional[float] = Field(None, gt=0, description="Target return on ad spend")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Targeting</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    target_locations: List[str] = Field(default=[], description="Targeted locations")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    target_languages: List[Language] = Field(default=[], description="Targeted languages")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    targeting: Optional[TargetingCriteria] = Field(None, description="Advanced targeting criteria")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Campaign structure</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    ad_groups: List[AdGroup] = Field(default=[], description="Ad groups in this campaign")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    keywords: List[str] = Field(default=[], description="Campaign-level keywords")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    negative_keywords: List[str] = Field(default=[], description="Negative keywords")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Dates and scheduling</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    start_date: Optional[datetime] = Field(None, description="Campaign start date")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    end_date: Optional[datetime] = Field(None, description="Campaign end date")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    ad_schedule: Optional[Dict[str, Any]] = Field(None, description="Ad scheduling configuration")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Performance data</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    metrics: Optional[CampaignMetrics] = Field(None, description="Campaign performance metrics")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Google Ads specific</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    google_ads_id: Optional[str] = Field(None, description="Google Ads campaign ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    customer_id: Optional[str] = Field(None, description="Google Ads customer ID")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # AI optimization</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    auto_optimization_enabled: bool = Field(True, description="Enable automatic optimization")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    optimization_score: Optional[float] = Field(None, ge=0, le=1, description="Optimization score")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    last_optimized: Optional[datetime] = Field(None, description="Last optimization timestamp")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class CampaignCreate(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Campaign creation request model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: str = Field(..., min_length=1, max_length=255, description="Campaign name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: Optional[str] = Field(None, max_length=1000, description="Campaign description")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: CampaignType = Field(..., description="Campaign type")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    budget_amount: float = Field(..., gt=0, description="Daily budget amount")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    bidding_strategy: BiddingStrategy = Field(BiddingStrategy.MANUAL_CPC, description="Bidding strategy")</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)
Explicit (x7)
Omitted Generics (x5)">    target_locations: List[str] = Field(..., min_items=1, description="Targeted locations")</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)
Explicit (x7)
Omitted Generics (x5)">    target_languages: List[Language] = Field(..., min_items=1, description="Targeted languages")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    keywords: List[str] = Field(default=[], description="Initial keywords")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    start_date: Optional[datetime] = Field(None, description="Campaign start date")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    end_date: Optional[datetime] = Field(None, description="Campaign end date")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    auto_optimization_enabled: bool = Field(True, description="Enable automatic optimization")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class CampaignUpdate(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Campaign update request model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Campaign name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: Optional[str] = Field(None, max_length=1000, description="Campaign description")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    status: Optional[CampaignStatus] = Field(None, description="Campaign status")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    budget_amount: Optional[float] = Field(None, gt=0, description="Daily budget amount")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    bidding_strategy: Optional[BiddingStrategy] = Field(None, description="Bidding strategy")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    target_locations: Optional[List[str]] = Field(None, description="Targeted locations")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    target_languages: Optional[List[Language]] = Field(None, description="Targeted languages")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    keywords: Optional[List[str]] = Field(None, description="Campaign keywords")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    negative_keywords: Optional[List[str]] = Field(None, description="Negative keywords")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    start_date: Optional[datetime] = Field(None, description="Campaign start date")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    end_date: Optional[datetime] = Field(None, description="Campaign end date")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    auto_optimization_enabled: Optional[bool] = Field(None, description="Enable automatic optimization")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class CampaignResponse(BaseResponse[Campaign]):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Single campaign response model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    pass</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class CampaignListResponse(PaginatedResponse[Campaign]):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Campaign list response model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    pass</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class KeywordPerformance(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Keyword performance metrics.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    keyword: Keyword = Field(..., description="Keyword details")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    metrics: CampaignMetrics = Field(..., description="Performance metrics")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    search_volume: Optional[int] = Field(None, description="Average monthly search volume")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    competition: Optional[str] = Field(None, description="Competition level (low, medium, high)")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    suggested_bid: Optional[float] = Field(None, description="Suggested bid amount")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AdPerformance(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Ad performance metrics.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    ad: Ad = Field(..., description="Ad details")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    metrics: CampaignMetrics = Field(..., description="Performance metrics")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    approval_status: Optional[str] = Field(None, description="Ad approval status")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    policy_summary: Optional[str] = Field(None, description="Policy review summary")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class CampaignOptimizationSuggestion(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Campaign optimization suggestion.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    id: str = Field(..., description="Suggestion ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: str = Field(..., description="Suggestion type")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    priority: str = Field(..., description="Priority level (low, medium, high)")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    title: str = Field(..., description="Suggestion title")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: str = Field(..., description="Detailed description")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    estimated_impact: Dict[str, float] = Field(..., description="Estimated impact metrics")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    action_required: str = Field(..., description="Required action to implement")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    confidence: float = Field(..., ge=0, le=1, description="Confidence score")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">    @validator("priority")</span>
<span class="line-any" title="No Anys on this line!">    def validate_priority(cls, v):</span>
<span class="line-empty" title="No Anys on this line!">        """Validate priority level."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        if v not in ["low", "medium", "high"]:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">            raise ValueError("Priority must be low, medium, or high")</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        return v</span>
</pre></td>
</tr></tbody>
</table>
</body>
</html>
