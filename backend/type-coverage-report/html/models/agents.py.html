<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../mypy-html.css">
</head>
<body>
<h2>models.agents</h2>
<table>
<caption>models/agents.py</caption>
<tbody><tr>
<td class="table-lines"><pre><span id="L1" class="lineno"><a class="lineno" href="#L1">1</a></span>
<span id="L2" class="lineno"><a class="lineno" href="#L2">2</a></span>
<span id="L3" class="lineno"><a class="lineno" href="#L3">3</a></span>
<span id="L4" class="lineno"><a class="lineno" href="#L4">4</a></span>
<span id="L5" class="lineno"><a class="lineno" href="#L5">5</a></span>
<span id="L6" class="lineno"><a class="lineno" href="#L6">6</a></span>
<span id="L7" class="lineno"><a class="lineno" href="#L7">7</a></span>
<span id="L8" class="lineno"><a class="lineno" href="#L8">8</a></span>
<span id="L9" class="lineno"><a class="lineno" href="#L9">9</a></span>
<span id="L10" class="lineno"><a class="lineno" href="#L10">10</a></span>
<span id="L11" class="lineno"><a class="lineno" href="#L11">11</a></span>
<span id="L12" class="lineno"><a class="lineno" href="#L12">12</a></span>
<span id="L13" class="lineno"><a class="lineno" href="#L13">13</a></span>
<span id="L14" class="lineno"><a class="lineno" href="#L14">14</a></span>
<span id="L15" class="lineno"><a class="lineno" href="#L15">15</a></span>
<span id="L16" class="lineno"><a class="lineno" href="#L16">16</a></span>
<span id="L17" class="lineno"><a class="lineno" href="#L17">17</a></span>
<span id="L18" class="lineno"><a class="lineno" href="#L18">18</a></span>
<span id="L19" class="lineno"><a class="lineno" href="#L19">19</a></span>
<span id="L20" class="lineno"><a class="lineno" href="#L20">20</a></span>
<span id="L21" class="lineno"><a class="lineno" href="#L21">21</a></span>
<span id="L22" class="lineno"><a class="lineno" href="#L22">22</a></span>
<span id="L23" class="lineno"><a class="lineno" href="#L23">23</a></span>
<span id="L24" class="lineno"><a class="lineno" href="#L24">24</a></span>
<span id="L25" class="lineno"><a class="lineno" href="#L25">25</a></span>
<span id="L26" class="lineno"><a class="lineno" href="#L26">26</a></span>
<span id="L27" class="lineno"><a class="lineno" href="#L27">27</a></span>
<span id="L28" class="lineno"><a class="lineno" href="#L28">28</a></span>
<span id="L29" class="lineno"><a class="lineno" href="#L29">29</a></span>
<span id="L30" class="lineno"><a class="lineno" href="#L30">30</a></span>
<span id="L31" class="lineno"><a class="lineno" href="#L31">31</a></span>
<span id="L32" class="lineno"><a class="lineno" href="#L32">32</a></span>
<span id="L33" class="lineno"><a class="lineno" href="#L33">33</a></span>
<span id="L34" class="lineno"><a class="lineno" href="#L34">34</a></span>
<span id="L35" class="lineno"><a class="lineno" href="#L35">35</a></span>
<span id="L36" class="lineno"><a class="lineno" href="#L36">36</a></span>
<span id="L37" class="lineno"><a class="lineno" href="#L37">37</a></span>
<span id="L38" class="lineno"><a class="lineno" href="#L38">38</a></span>
<span id="L39" class="lineno"><a class="lineno" href="#L39">39</a></span>
<span id="L40" class="lineno"><a class="lineno" href="#L40">40</a></span>
<span id="L41" class="lineno"><a class="lineno" href="#L41">41</a></span>
<span id="L42" class="lineno"><a class="lineno" href="#L42">42</a></span>
<span id="L43" class="lineno"><a class="lineno" href="#L43">43</a></span>
<span id="L44" class="lineno"><a class="lineno" href="#L44">44</a></span>
<span id="L45" class="lineno"><a class="lineno" href="#L45">45</a></span>
<span id="L46" class="lineno"><a class="lineno" href="#L46">46</a></span>
<span id="L47" class="lineno"><a class="lineno" href="#L47">47</a></span>
<span id="L48" class="lineno"><a class="lineno" href="#L48">48</a></span>
<span id="L49" class="lineno"><a class="lineno" href="#L49">49</a></span>
<span id="L50" class="lineno"><a class="lineno" href="#L50">50</a></span>
<span id="L51" class="lineno"><a class="lineno" href="#L51">51</a></span>
<span id="L52" class="lineno"><a class="lineno" href="#L52">52</a></span>
<span id="L53" class="lineno"><a class="lineno" href="#L53">53</a></span>
<span id="L54" class="lineno"><a class="lineno" href="#L54">54</a></span>
<span id="L55" class="lineno"><a class="lineno" href="#L55">55</a></span>
<span id="L56" class="lineno"><a class="lineno" href="#L56">56</a></span>
<span id="L57" class="lineno"><a class="lineno" href="#L57">57</a></span>
<span id="L58" class="lineno"><a class="lineno" href="#L58">58</a></span>
<span id="L59" class="lineno"><a class="lineno" href="#L59">59</a></span>
<span id="L60" class="lineno"><a class="lineno" href="#L60">60</a></span>
<span id="L61" class="lineno"><a class="lineno" href="#L61">61</a></span>
<span id="L62" class="lineno"><a class="lineno" href="#L62">62</a></span>
<span id="L63" class="lineno"><a class="lineno" href="#L63">63</a></span>
<span id="L64" class="lineno"><a class="lineno" href="#L64">64</a></span>
<span id="L65" class="lineno"><a class="lineno" href="#L65">65</a></span>
<span id="L66" class="lineno"><a class="lineno" href="#L66">66</a></span>
<span id="L67" class="lineno"><a class="lineno" href="#L67">67</a></span>
<span id="L68" class="lineno"><a class="lineno" href="#L68">68</a></span>
<span id="L69" class="lineno"><a class="lineno" href="#L69">69</a></span>
<span id="L70" class="lineno"><a class="lineno" href="#L70">70</a></span>
<span id="L71" class="lineno"><a class="lineno" href="#L71">71</a></span>
<span id="L72" class="lineno"><a class="lineno" href="#L72">72</a></span>
<span id="L73" class="lineno"><a class="lineno" href="#L73">73</a></span>
<span id="L74" class="lineno"><a class="lineno" href="#L74">74</a></span>
<span id="L75" class="lineno"><a class="lineno" href="#L75">75</a></span>
<span id="L76" class="lineno"><a class="lineno" href="#L76">76</a></span>
<span id="L77" class="lineno"><a class="lineno" href="#L77">77</a></span>
<span id="L78" class="lineno"><a class="lineno" href="#L78">78</a></span>
<span id="L79" class="lineno"><a class="lineno" href="#L79">79</a></span>
<span id="L80" class="lineno"><a class="lineno" href="#L80">80</a></span>
<span id="L81" class="lineno"><a class="lineno" href="#L81">81</a></span>
<span id="L82" class="lineno"><a class="lineno" href="#L82">82</a></span>
<span id="L83" class="lineno"><a class="lineno" href="#L83">83</a></span>
<span id="L84" class="lineno"><a class="lineno" href="#L84">84</a></span>
<span id="L85" class="lineno"><a class="lineno" href="#L85">85</a></span>
<span id="L86" class="lineno"><a class="lineno" href="#L86">86</a></span>
<span id="L87" class="lineno"><a class="lineno" href="#L87">87</a></span>
<span id="L88" class="lineno"><a class="lineno" href="#L88">88</a></span>
<span id="L89" class="lineno"><a class="lineno" href="#L89">89</a></span>
<span id="L90" class="lineno"><a class="lineno" href="#L90">90</a></span>
<span id="L91" class="lineno"><a class="lineno" href="#L91">91</a></span>
<span id="L92" class="lineno"><a class="lineno" href="#L92">92</a></span>
<span id="L93" class="lineno"><a class="lineno" href="#L93">93</a></span>
<span id="L94" class="lineno"><a class="lineno" href="#L94">94</a></span>
<span id="L95" class="lineno"><a class="lineno" href="#L95">95</a></span>
<span id="L96" class="lineno"><a class="lineno" href="#L96">96</a></span>
<span id="L97" class="lineno"><a class="lineno" href="#L97">97</a></span>
<span id="L98" class="lineno"><a class="lineno" href="#L98">98</a></span>
<span id="L99" class="lineno"><a class="lineno" href="#L99">99</a></span>
<span id="L100" class="lineno"><a class="lineno" href="#L100">100</a></span>
<span id="L101" class="lineno"><a class="lineno" href="#L101">101</a></span>
<span id="L102" class="lineno"><a class="lineno" href="#L102">102</a></span>
<span id="L103" class="lineno"><a class="lineno" href="#L103">103</a></span>
<span id="L104" class="lineno"><a class="lineno" href="#L104">104</a></span>
<span id="L105" class="lineno"><a class="lineno" href="#L105">105</a></span>
<span id="L106" class="lineno"><a class="lineno" href="#L106">106</a></span>
<span id="L107" class="lineno"><a class="lineno" href="#L107">107</a></span>
<span id="L108" class="lineno"><a class="lineno" href="#L108">108</a></span>
<span id="L109" class="lineno"><a class="lineno" href="#L109">109</a></span>
<span id="L110" class="lineno"><a class="lineno" href="#L110">110</a></span>
<span id="L111" class="lineno"><a class="lineno" href="#L111">111</a></span>
<span id="L112" class="lineno"><a class="lineno" href="#L112">112</a></span>
<span id="L113" class="lineno"><a class="lineno" href="#L113">113</a></span>
<span id="L114" class="lineno"><a class="lineno" href="#L114">114</a></span>
<span id="L115" class="lineno"><a class="lineno" href="#L115">115</a></span>
<span id="L116" class="lineno"><a class="lineno" href="#L116">116</a></span>
<span id="L117" class="lineno"><a class="lineno" href="#L117">117</a></span>
<span id="L118" class="lineno"><a class="lineno" href="#L118">118</a></span>
<span id="L119" class="lineno"><a class="lineno" href="#L119">119</a></span>
<span id="L120" class="lineno"><a class="lineno" href="#L120">120</a></span>
<span id="L121" class="lineno"><a class="lineno" href="#L121">121</a></span>
<span id="L122" class="lineno"><a class="lineno" href="#L122">122</a></span>
<span id="L123" class="lineno"><a class="lineno" href="#L123">123</a></span>
<span id="L124" class="lineno"><a class="lineno" href="#L124">124</a></span>
<span id="L125" class="lineno"><a class="lineno" href="#L125">125</a></span>
<span id="L126" class="lineno"><a class="lineno" href="#L126">126</a></span>
<span id="L127" class="lineno"><a class="lineno" href="#L127">127</a></span>
<span id="L128" class="lineno"><a class="lineno" href="#L128">128</a></span>
<span id="L129" class="lineno"><a class="lineno" href="#L129">129</a></span>
<span id="L130" class="lineno"><a class="lineno" href="#L130">130</a></span>
<span id="L131" class="lineno"><a class="lineno" href="#L131">131</a></span>
<span id="L132" class="lineno"><a class="lineno" href="#L132">132</a></span>
<span id="L133" class="lineno"><a class="lineno" href="#L133">133</a></span>
<span id="L134" class="lineno"><a class="lineno" href="#L134">134</a></span>
<span id="L135" class="lineno"><a class="lineno" href="#L135">135</a></span>
<span id="L136" class="lineno"><a class="lineno" href="#L136">136</a></span>
<span id="L137" class="lineno"><a class="lineno" href="#L137">137</a></span>
<span id="L138" class="lineno"><a class="lineno" href="#L138">138</a></span>
<span id="L139" class="lineno"><a class="lineno" href="#L139">139</a></span>
<span id="L140" class="lineno"><a class="lineno" href="#L140">140</a></span>
<span id="L141" class="lineno"><a class="lineno" href="#L141">141</a></span>
<span id="L142" class="lineno"><a class="lineno" href="#L142">142</a></span>
<span id="L143" class="lineno"><a class="lineno" href="#L143">143</a></span>
<span id="L144" class="lineno"><a class="lineno" href="#L144">144</a></span>
<span id="L145" class="lineno"><a class="lineno" href="#L145">145</a></span>
<span id="L146" class="lineno"><a class="lineno" href="#L146">146</a></span>
<span id="L147" class="lineno"><a class="lineno" href="#L147">147</a></span>
<span id="L148" class="lineno"><a class="lineno" href="#L148">148</a></span>
<span id="L149" class="lineno"><a class="lineno" href="#L149">149</a></span>
<span id="L150" class="lineno"><a class="lineno" href="#L150">150</a></span>
<span id="L151" class="lineno"><a class="lineno" href="#L151">151</a></span>
<span id="L152" class="lineno"><a class="lineno" href="#L152">152</a></span>
<span id="L153" class="lineno"><a class="lineno" href="#L153">153</a></span>
<span id="L154" class="lineno"><a class="lineno" href="#L154">154</a></span>
<span id="L155" class="lineno"><a class="lineno" href="#L155">155</a></span>
<span id="L156" class="lineno"><a class="lineno" href="#L156">156</a></span>
<span id="L157" class="lineno"><a class="lineno" href="#L157">157</a></span>
<span id="L158" class="lineno"><a class="lineno" href="#L158">158</a></span>
<span id="L159" class="lineno"><a class="lineno" href="#L159">159</a></span>
<span id="L160" class="lineno"><a class="lineno" href="#L160">160</a></span>
<span id="L161" class="lineno"><a class="lineno" href="#L161">161</a></span>
<span id="L162" class="lineno"><a class="lineno" href="#L162">162</a></span>
<span id="L163" class="lineno"><a class="lineno" href="#L163">163</a></span>
<span id="L164" class="lineno"><a class="lineno" href="#L164">164</a></span>
<span id="L165" class="lineno"><a class="lineno" href="#L165">165</a></span>
<span id="L166" class="lineno"><a class="lineno" href="#L166">166</a></span>
<span id="L167" class="lineno"><a class="lineno" href="#L167">167</a></span>
<span id="L168" class="lineno"><a class="lineno" href="#L168">168</a></span>
<span id="L169" class="lineno"><a class="lineno" href="#L169">169</a></span>
<span id="L170" class="lineno"><a class="lineno" href="#L170">170</a></span>
<span id="L171" class="lineno"><a class="lineno" href="#L171">171</a></span>
<span id="L172" class="lineno"><a class="lineno" href="#L172">172</a></span>
<span id="L173" class="lineno"><a class="lineno" href="#L173">173</a></span>
<span id="L174" class="lineno"><a class="lineno" href="#L174">174</a></span>
<span id="L175" class="lineno"><a class="lineno" href="#L175">175</a></span>
<span id="L176" class="lineno"><a class="lineno" href="#L176">176</a></span>
<span id="L177" class="lineno"><a class="lineno" href="#L177">177</a></span>
<span id="L178" class="lineno"><a class="lineno" href="#L178">178</a></span>
<span id="L179" class="lineno"><a class="lineno" href="#L179">179</a></span>
<span id="L180" class="lineno"><a class="lineno" href="#L180">180</a></span>
<span id="L181" class="lineno"><a class="lineno" href="#L181">181</a></span>
<span id="L182" class="lineno"><a class="lineno" href="#L182">182</a></span>
<span id="L183" class="lineno"><a class="lineno" href="#L183">183</a></span>
<span id="L184" class="lineno"><a class="lineno" href="#L184">184</a></span>
<span id="L185" class="lineno"><a class="lineno" href="#L185">185</a></span>
<span id="L186" class="lineno"><a class="lineno" href="#L186">186</a></span>
<span id="L187" class="lineno"><a class="lineno" href="#L187">187</a></span>
<span id="L188" class="lineno"><a class="lineno" href="#L188">188</a></span>
<span id="L189" class="lineno"><a class="lineno" href="#L189">189</a></span>
<span id="L190" class="lineno"><a class="lineno" href="#L190">190</a></span>
<span id="L191" class="lineno"><a class="lineno" href="#L191">191</a></span>
<span id="L192" class="lineno"><a class="lineno" href="#L192">192</a></span>
<span id="L193" class="lineno"><a class="lineno" href="#L193">193</a></span>
<span id="L194" class="lineno"><a class="lineno" href="#L194">194</a></span>
<span id="L195" class="lineno"><a class="lineno" href="#L195">195</a></span>
<span id="L196" class="lineno"><a class="lineno" href="#L196">196</a></span>
<span id="L197" class="lineno"><a class="lineno" href="#L197">197</a></span>
<span id="L198" class="lineno"><a class="lineno" href="#L198">198</a></span>
<span id="L199" class="lineno"><a class="lineno" href="#L199">199</a></span>
<span id="L200" class="lineno"><a class="lineno" href="#L200">200</a></span>
<span id="L201" class="lineno"><a class="lineno" href="#L201">201</a></span>
<span id="L202" class="lineno"><a class="lineno" href="#L202">202</a></span>
<span id="L203" class="lineno"><a class="lineno" href="#L203">203</a></span>
<span id="L204" class="lineno"><a class="lineno" href="#L204">204</a></span>
<span id="L205" class="lineno"><a class="lineno" href="#L205">205</a></span>
<span id="L206" class="lineno"><a class="lineno" href="#L206">206</a></span>
<span id="L207" class="lineno"><a class="lineno" href="#L207">207</a></span>
<span id="L208" class="lineno"><a class="lineno" href="#L208">208</a></span>
<span id="L209" class="lineno"><a class="lineno" href="#L209">209</a></span>
<span id="L210" class="lineno"><a class="lineno" href="#L210">210</a></span>
<span id="L211" class="lineno"><a class="lineno" href="#L211">211</a></span>
<span id="L212" class="lineno"><a class="lineno" href="#L212">212</a></span>
<span id="L213" class="lineno"><a class="lineno" href="#L213">213</a></span>
<span id="L214" class="lineno"><a class="lineno" href="#L214">214</a></span>
<span id="L215" class="lineno"><a class="lineno" href="#L215">215</a></span>
<span id="L216" class="lineno"><a class="lineno" href="#L216">216</a></span>
<span id="L217" class="lineno"><a class="lineno" href="#L217">217</a></span>
<span id="L218" class="lineno"><a class="lineno" href="#L218">218</a></span>
<span id="L219" class="lineno"><a class="lineno" href="#L219">219</a></span>
<span id="L220" class="lineno"><a class="lineno" href="#L220">220</a></span>
<span id="L221" class="lineno"><a class="lineno" href="#L221">221</a></span>
<span id="L222" class="lineno"><a class="lineno" href="#L222">222</a></span>
<span id="L223" class="lineno"><a class="lineno" href="#L223">223</a></span>
<span id="L224" class="lineno"><a class="lineno" href="#L224">224</a></span>
<span id="L225" class="lineno"><a class="lineno" href="#L225">225</a></span>
<span id="L226" class="lineno"><a class="lineno" href="#L226">226</a></span>
<span id="L227" class="lineno"><a class="lineno" href="#L227">227</a></span>
<span id="L228" class="lineno"><a class="lineno" href="#L228">228</a></span>
<span id="L229" class="lineno"><a class="lineno" href="#L229">229</a></span>
<span id="L230" class="lineno"><a class="lineno" href="#L230">230</a></span>
<span id="L231" class="lineno"><a class="lineno" href="#L231">231</a></span>
<span id="L232" class="lineno"><a class="lineno" href="#L232">232</a></span>
<span id="L233" class="lineno"><a class="lineno" href="#L233">233</a></span>
<span id="L234" class="lineno"><a class="lineno" href="#L234">234</a></span>
<span id="L235" class="lineno"><a class="lineno" href="#L235">235</a></span>
<span id="L236" class="lineno"><a class="lineno" href="#L236">236</a></span>
<span id="L237" class="lineno"><a class="lineno" href="#L237">237</a></span>
<span id="L238" class="lineno"><a class="lineno" href="#L238">238</a></span>
<span id="L239" class="lineno"><a class="lineno" href="#L239">239</a></span>
<span id="L240" class="lineno"><a class="lineno" href="#L240">240</a></span>
<span id="L241" class="lineno"><a class="lineno" href="#L241">241</a></span>
<span id="L242" class="lineno"><a class="lineno" href="#L242">242</a></span>
<span id="L243" class="lineno"><a class="lineno" href="#L243">243</a></span>
<span id="L244" class="lineno"><a class="lineno" href="#L244">244</a></span>
<span id="L245" class="lineno"><a class="lineno" href="#L245">245</a></span>
<span id="L246" class="lineno"><a class="lineno" href="#L246">246</a></span>
<span id="L247" class="lineno"><a class="lineno" href="#L247">247</a></span>
<span id="L248" class="lineno"><a class="lineno" href="#L248">248</a></span>
<span id="L249" class="lineno"><a class="lineno" href="#L249">249</a></span>
<span id="L250" class="lineno"><a class="lineno" href="#L250">250</a></span>
<span id="L251" class="lineno"><a class="lineno" href="#L251">251</a></span>
<span id="L252" class="lineno"><a class="lineno" href="#L252">252</a></span>
<span id="L253" class="lineno"><a class="lineno" href="#L253">253</a></span>
<span id="L254" class="lineno"><a class="lineno" href="#L254">254</a></span>
<span id="L255" class="lineno"><a class="lineno" href="#L255">255</a></span>
<span id="L256" class="lineno"><a class="lineno" href="#L256">256</a></span>
<span id="L257" class="lineno"><a class="lineno" href="#L257">257</a></span>
<span id="L258" class="lineno"><a class="lineno" href="#L258">258</a></span>
<span id="L259" class="lineno"><a class="lineno" href="#L259">259</a></span>
<span id="L260" class="lineno"><a class="lineno" href="#L260">260</a></span>
<span id="L261" class="lineno"><a class="lineno" href="#L261">261</a></span>
<span id="L262" class="lineno"><a class="lineno" href="#L262">262</a></span>
<span id="L263" class="lineno"><a class="lineno" href="#L263">263</a></span>
<span id="L264" class="lineno"><a class="lineno" href="#L264">264</a></span>
<span id="L265" class="lineno"><a class="lineno" href="#L265">265</a></span>
<span id="L266" class="lineno"><a class="lineno" href="#L266">266</a></span>
<span id="L267" class="lineno"><a class="lineno" href="#L267">267</a></span>
<span id="L268" class="lineno"><a class="lineno" href="#L268">268</a></span>
<span id="L269" class="lineno"><a class="lineno" href="#L269">269</a></span>
<span id="L270" class="lineno"><a class="lineno" href="#L270">270</a></span>
<span id="L271" class="lineno"><a class="lineno" href="#L271">271</a></span>
<span id="L272" class="lineno"><a class="lineno" href="#L272">272</a></span>
<span id="L273" class="lineno"><a class="lineno" href="#L273">273</a></span>
<span id="L274" class="lineno"><a class="lineno" href="#L274">274</a></span>
<span id="L275" class="lineno"><a class="lineno" href="#L275">275</a></span>
<span id="L276" class="lineno"><a class="lineno" href="#L276">276</a></span>
<span id="L277" class="lineno"><a class="lineno" href="#L277">277</a></span>
<span id="L278" class="lineno"><a class="lineno" href="#L278">278</a></span>
<span id="L279" class="lineno"><a class="lineno" href="#L279">279</a></span>
<span id="L280" class="lineno"><a class="lineno" href="#L280">280</a></span>
<span id="L281" class="lineno"><a class="lineno" href="#L281">281</a></span>
<span id="L282" class="lineno"><a class="lineno" href="#L282">282</a></span>
<span id="L283" class="lineno"><a class="lineno" href="#L283">283</a></span>
<span id="L284" class="lineno"><a class="lineno" href="#L284">284</a></span>
<span id="L285" class="lineno"><a class="lineno" href="#L285">285</a></span>
<span id="L286" class="lineno"><a class="lineno" href="#L286">286</a></span>
<span id="L287" class="lineno"><a class="lineno" href="#L287">287</a></span>
<span id="L288" class="lineno"><a class="lineno" href="#L288">288</a></span>
<span id="L289" class="lineno"><a class="lineno" href="#L289">289</a></span>
<span id="L290" class="lineno"><a class="lineno" href="#L290">290</a></span>
<span id="L291" class="lineno"><a class="lineno" href="#L291">291</a></span>
<span id="L292" class="lineno"><a class="lineno" href="#L292">292</a></span>
<span id="L293" class="lineno"><a class="lineno" href="#L293">293</a></span>
<span id="L294" class="lineno"><a class="lineno" href="#L294">294</a></span>
<span id="L295" class="lineno"><a class="lineno" href="#L295">295</a></span>
<span id="L296" class="lineno"><a class="lineno" href="#L296">296</a></span>
<span id="L297" class="lineno"><a class="lineno" href="#L297">297</a></span>
<span id="L298" class="lineno"><a class="lineno" href="#L298">298</a></span>
<span id="L299" class="lineno"><a class="lineno" href="#L299">299</a></span>
<span id="L300" class="lineno"><a class="lineno" href="#L300">300</a></span>
<span id="L301" class="lineno"><a class="lineno" href="#L301">301</a></span>
<span id="L302" class="lineno"><a class="lineno" href="#L302">302</a></span>
<span id="L303" class="lineno"><a class="lineno" href="#L303">303</a></span>
<span id="L304" class="lineno"><a class="lineno" href="#L304">304</a></span>
<span id="L305" class="lineno"><a class="lineno" href="#L305">305</a></span>
<span id="L306" class="lineno"><a class="lineno" href="#L306">306</a></span>
<span id="L307" class="lineno"><a class="lineno" href="#L307">307</a></span>
<span id="L308" class="lineno"><a class="lineno" href="#L308">308</a></span>
<span id="L309" class="lineno"><a class="lineno" href="#L309">309</a></span>
<span id="L310" class="lineno"><a class="lineno" href="#L310">310</a></span>
<span id="L311" class="lineno"><a class="lineno" href="#L311">311</a></span>
<span id="L312" class="lineno"><a class="lineno" href="#L312">312</a></span>
<span id="L313" class="lineno"><a class="lineno" href="#L313">313</a></span>
<span id="L314" class="lineno"><a class="lineno" href="#L314">314</a></span>
<span id="L315" class="lineno"><a class="lineno" href="#L315">315</a></span>
<span id="L316" class="lineno"><a class="lineno" href="#L316">316</a></span>
<span id="L317" class="lineno"><a class="lineno" href="#L317">317</a></span>
<span id="L318" class="lineno"><a class="lineno" href="#L318">318</a></span>
<span id="L319" class="lineno"><a class="lineno" href="#L319">319</a></span>
<span id="L320" class="lineno"><a class="lineno" href="#L320">320</a></span>
<span id="L321" class="lineno"><a class="lineno" href="#L321">321</a></span>
<span id="L322" class="lineno"><a class="lineno" href="#L322">322</a></span>
<span id="L323" class="lineno"><a class="lineno" href="#L323">323</a></span>
<span id="L324" class="lineno"><a class="lineno" href="#L324">324</a></span>
<span id="L325" class="lineno"><a class="lineno" href="#L325">325</a></span>
<span id="L326" class="lineno"><a class="lineno" href="#L326">326</a></span>
<span id="L327" class="lineno"><a class="lineno" href="#L327">327</a></span>
<span id="L328" class="lineno"><a class="lineno" href="#L328">328</a></span>
<span id="L329" class="lineno"><a class="lineno" href="#L329">329</a></span>
<span id="L330" class="lineno"><a class="lineno" href="#L330">330</a></span>
<span id="L331" class="lineno"><a class="lineno" href="#L331">331</a></span>
<span id="L332" class="lineno"><a class="lineno" href="#L332">332</a></span>
<span id="L333" class="lineno"><a class="lineno" href="#L333">333</a></span>
<span id="L334" class="lineno"><a class="lineno" href="#L334">334</a></span>
<span id="L335" class="lineno"><a class="lineno" href="#L335">335</a></span>
<span id="L336" class="lineno"><a class="lineno" href="#L336">336</a></span>
<span id="L337" class="lineno"><a class="lineno" href="#L337">337</a></span>
<span id="L338" class="lineno"><a class="lineno" href="#L338">338</a></span>
<span id="L339" class="lineno"><a class="lineno" href="#L339">339</a></span>
<span id="L340" class="lineno"><a class="lineno" href="#L340">340</a></span>
<span id="L341" class="lineno"><a class="lineno" href="#L341">341</a></span>
<span id="L342" class="lineno"><a class="lineno" href="#L342">342</a></span>
<span id="L343" class="lineno"><a class="lineno" href="#L343">343</a></span>
<span id="L344" class="lineno"><a class="lineno" href="#L344">344</a></span>
<span id="L345" class="lineno"><a class="lineno" href="#L345">345</a></span>
<span id="L346" class="lineno"><a class="lineno" href="#L346">346</a></span>
<span id="L347" class="lineno"><a class="lineno" href="#L347">347</a></span>
<span id="L348" class="lineno"><a class="lineno" href="#L348">348</a></span>
<span id="L349" class="lineno"><a class="lineno" href="#L349">349</a></span>
<span id="L350" class="lineno"><a class="lineno" href="#L350">350</a></span>
<span id="L351" class="lineno"><a class="lineno" href="#L351">351</a></span>
<span id="L352" class="lineno"><a class="lineno" href="#L352">352</a></span>
<span id="L353" class="lineno"><a class="lineno" href="#L353">353</a></span>
<span id="L354" class="lineno"><a class="lineno" href="#L354">354</a></span>
<span id="L355" class="lineno"><a class="lineno" href="#L355">355</a></span>
<span id="L356" class="lineno"><a class="lineno" href="#L356">356</a></span>
<span id="L357" class="lineno"><a class="lineno" href="#L357">357</a></span>
<span id="L358" class="lineno"><a class="lineno" href="#L358">358</a></span>
<span id="L359" class="lineno"><a class="lineno" href="#L359">359</a></span>
<span id="L360" class="lineno"><a class="lineno" href="#L360">360</a></span>
<span id="L361" class="lineno"><a class="lineno" href="#L361">361</a></span>
<span id="L362" class="lineno"><a class="lineno" href="#L362">362</a></span>
<span id="L363" class="lineno"><a class="lineno" href="#L363">363</a></span>
<span id="L364" class="lineno"><a class="lineno" href="#L364">364</a></span>
<span id="L365" class="lineno"><a class="lineno" href="#L365">365</a></span>
<span id="L366" class="lineno"><a class="lineno" href="#L366">366</a></span>
<span id="L367" class="lineno"><a class="lineno" href="#L367">367</a></span>
</pre></td>
<td class="table-code"><pre><span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!">AI Agent-related Pydantic models.</span>
<span class="line-empty" title="No Anys on this line!">Data structures for CrewAI agents, tasks, and agent management.</span>
<span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from datetime import datetime</span>
<span class="line-precise" title="No Anys on this line!">from typing import Any, Dict, List, Optional</span>
<span class="line-precise" title="No Anys on this line!">from enum import Enum</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from pydantic import BaseModel, Field, validator</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from .common import BaseEntity, BaseResponse, PaginatedResponse, Priority, Status</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class AgentType(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    AI agent types in the system.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    CAMPAIGN_PLANNING = "campaign_planning"</span>
<span class="line-precise" title="No Anys on this line!">    AD_ASSET_GENERATION = "ad_asset_generation"</span>
<span class="line-precise" title="No Anys on this line!">    KEYWORD_RESEARCH = "keyword_research"</span>
<span class="line-precise" title="No Anys on this line!">    BID_OPTIMIZATION = "bid_optimization"</span>
<span class="line-precise" title="No Anys on this line!">    BUDGET_MANAGEMENT = "budget_management"</span>
<span class="line-precise" title="No Anys on this line!">    PERFORMANCE_ANALYSIS = "performance_analysis"</span>
<span class="line-precise" title="No Anys on this line!">    AUDIENCE_TARGETING = "audience_targeting"</span>
<span class="line-precise" title="No Anys on this line!">    COMPETITOR_ANALYSIS = "competitor_analysis"</span>
<span class="line-precise" title="No Anys on this line!">    CONTENT_OPTIMIZATION = "content_optimization"</span>
<span class="line-precise" title="No Anys on this line!">    QUALITY_ASSURANCE = "quality_assurance"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class AgentStatus(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Agent status enumeration.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    CREATED = "created"</span>
<span class="line-precise" title="No Anys on this line!">    INITIALIZING = "initializing"</span>
<span class="line-precise" title="No Anys on this line!">    ACTIVE = "active"</span>
<span class="line-precise" title="No Anys on this line!">    BUSY = "busy"</span>
<span class="line-precise" title="No Anys on this line!">    IDLE = "idle"</span>
<span class="line-precise" title="No Anys on this line!">    PAUSED = "paused"</span>
<span class="line-precise" title="No Anys on this line!">    STOPPED = "stopped"</span>
<span class="line-precise" title="No Anys on this line!">    ERROR = "error"</span>
<span class="line-precise" title="No Anys on this line!">    MAINTENANCE = "maintenance"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class TaskStatus(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Task status enumeration.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    PENDING = "pending"</span>
<span class="line-precise" title="No Anys on this line!">    QUEUED = "queued"</span>
<span class="line-precise" title="No Anys on this line!">    RUNNING = "running"</span>
<span class="line-precise" title="No Anys on this line!">    COMPLETED = "completed"</span>
<span class="line-precise" title="No Anys on this line!">    FAILED = "failed"</span>
<span class="line-precise" title="No Anys on this line!">    CANCELLED = "cancelled"</span>
<span class="line-precise" title="No Anys on this line!">    TIMEOUT = "timeout"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class TaskPriority(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Task priority levels.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    LOW = "low"</span>
<span class="line-precise" title="No Anys on this line!">    NORMAL = "normal"</span>
<span class="line-precise" title="No Anys on this line!">    HIGH = "high"</span>
<span class="line-precise" title="No Anys on this line!">    URGENT = "urgent"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class ModelProvider(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    AI model providers.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    OPENAI = "openai"</span>
<span class="line-precise" title="No Anys on this line!">    GOOGLE = "google"</span>
<span class="line-precise" title="No Anys on this line!">    ANTHROPIC = "anthropic"</span>
<span class="line-precise" title="No Anys on this line!">    AZURE_OPENAI = "azure_openai"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AgentCapability(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Agent capability definition.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: str = Field(..., description="Capability name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: str = Field(..., description="Capability description")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    input_types: List[str] = Field(..., description="Supported input types")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    output_types: List[str] = Field(..., description="Supported output types")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    prerequisites: List[str] = Field(default=[], description="Required prerequisites")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AgentModel(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    AI model configuration for an agent.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    provider: ModelProvider = Field(..., description="Model provider")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    model_name: str = Field(..., description="Model name/identifier")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    temperature: float = Field(0.7, ge=0, le=2, description="Model temperature")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    max_tokens: int = Field(2000, ge=1, le=8192, description="Maximum tokens")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    top_p: Optional[float] = Field(None, ge=0, le=1, description="Top-p sampling parameter")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    frequency_penalty: Optional[float] = Field(None, ge=-2, le=2, description="Frequency penalty")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    presence_penalty: Optional[float] = Field(None, ge=-2, le=2, description="Presence penalty")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AgentMemory(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Agent memory configuration.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    enabled: bool = Field(True, description="Enable memory for the agent")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    memory_type: str = Field("vector", description="Memory type (vector, episodic, semantic)")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    max_entries: int = Field(1000, description="Maximum memory entries")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    retention_days: int = Field(30, description="Memory retention period in days")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    similarity_threshold: float = Field(0.8, description="Similarity threshold for memory retrieval")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class AgentTool(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Tool available to an agent.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: str = Field(..., description="Tool name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: str = Field(..., description="Tool description")</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    parameters: Dict[str, Any] = Field(default={}, description="Tool parameters")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    enabled: bool = Field(True, description="Whether the tool is enabled")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    rate_limit: Optional[int] = Field(None, description="Rate limit per hour")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AgentConfig(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Agent configuration settings.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    model: AgentModel = Field(..., description="AI model configuration")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    memory: AgentMemory = Field(default_factory=AgentMemory, description="Memory configuration")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    tools: List[AgentTool] = Field(default=[], description="Available tools")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    max_iterations: int = Field(10, description="Maximum task iterations")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    timeout_seconds: int = Field(300, description="Task timeout in seconds")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    retry_attempts: int = Field(3, description="Number of retry attempts")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    verbose: bool = Field(False, description="Enable verbose logging")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    allow_delegation: bool = Field(True, description="Allow delegation to other agents")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    system_message: Optional[str] = Field(None, description="Custom system message")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class Agent(BaseEntity):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Main agent model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: str = Field(..., min_length=1, max_length=255, description="Agent name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: str = Field(..., max_length=1000, description="Agent description")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: AgentType = Field(..., description="Agent type")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    status: AgentStatus = Field(AgentStatus.CREATED, description="Current agent status")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Configuration</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    config: AgentConfig = Field(..., description="Agent configuration")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    capabilities: List[AgentCapability] = Field(default=[], description="Agent capabilities")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Assignment and context</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    campaign_id: Optional[str] = Field(None, description="Assigned campaign ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    user_id: Optional[str] = Field(None, description="Owner user ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    team_id: Optional[str] = Field(None, description="Team ID for collaboration")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Performance tracking</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    tasks_completed: int = Field(0, description="Number of completed tasks")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    tasks_failed: int = Field(0, description="Number of failed tasks")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    average_execution_time: Optional[float] = Field(None, description="Average task execution time")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    success_rate: Optional[float] = Field(None, description="Task success rate")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Status tracking</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    last_activity: Optional[datetime] = Field(None, description="Last activity timestamp")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    last_error: Optional[str] = Field(None, description="Last error message")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    uptime_hours: Optional[float] = Field(None, description="Agent uptime in hours")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Resource usage</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    memory_usage_mb: Optional[float] = Field(None, description="Current memory usage in MB")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    cpu_usage_percent: Optional[float] = Field(None, description="Current CPU usage percentage")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Version and updates</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    version: str = Field("1.0.0", description="Agent version")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    last_updated: Optional[datetime] = Field(None, description="Last update timestamp")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x8)">class AgentTask(BaseEntity):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Task assigned to an agent.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    agent_id: str = Field(..., description="Agent ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    campaign_id: Optional[str] = Field(None, description="Related campaign ID")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Task details</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: str = Field(..., description="Task name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: str = Field(..., description="Task description")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: str = Field(..., description="Task type")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    priority: TaskPriority = Field(TaskPriority.NORMAL, description="Task priority")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    status: TaskStatus = Field(TaskStatus.PENDING, description="Task status")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Task data</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    input_data: Dict[str, Any] = Field(default={}, description="Task input data")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    output_data: Optional[Dict[str, Any]] = Field(None, description="Task output data")</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    context: Dict[str, Any] = Field(default={}, description="Additional context")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Execution details</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    started_at: Optional[datetime] = Field(None, description="Task start timestamp")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    completed_at: Optional[datetime] = Field(None, description="Task completion timestamp")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    execution_time_seconds: Optional[float] = Field(None, description="Task execution time")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    retry_count: int = Field(0, description="Number of retries")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Results and errors</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    result: Optional[str] = Field(None, description="Task result summary")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    error_message: Optional[str] = Field(None, description="Error message if failed")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    logs: List[str] = Field(default=[], description="Task execution logs")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Dependencies</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    parent_task_id: Optional[str] = Field(None, description="Parent task ID")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    dependent_task_ids: List[str] = Field(default=[], description="Dependent task IDs")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Scheduling</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    scheduled_at: Optional[datetime] = Field(None, description="Scheduled execution time")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    deadline: Optional[datetime] = Field(None, description="Task deadline")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AgentMetrics(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Agent performance metrics.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    agent_id: str = Field(..., description="Agent ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    period_start: datetime = Field(..., description="Metrics period start")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    period_end: datetime = Field(..., description="Metrics period end")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Task metrics</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    total_tasks: int = Field(0, description="Total tasks assigned")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    completed_tasks: int = Field(0, description="Successfully completed tasks")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    failed_tasks: int = Field(0, description="Failed tasks")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    cancelled_tasks: int = Field(0, description="Cancelled tasks")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Performance metrics</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    success_rate: float = Field(0, description="Task success rate")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    average_execution_time: float = Field(0, description="Average execution time in seconds")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    throughput_per_hour: float = Field(0, description="Tasks completed per hour")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Resource metrics</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    avg_memory_usage_mb: float = Field(0, description="Average memory usage")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    avg_cpu_usage_percent: float = Field(0, description="Average CPU usage")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    uptime_percentage: float = Field(0, description="Uptime percentage")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Quality metrics</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    output_quality_score: Optional[float] = Field(None, description="Output quality score")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    user_satisfaction_score: Optional[float] = Field(None, description="User satisfaction score")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    error_rate: float = Field(0, description="Error rate")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AgentCreate(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Agent creation request model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: str = Field(..., min_length=1, max_length=255, description="Agent name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: str = Field(..., max_length=1000, description="Agent description")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: AgentType = Field(..., description="Agent type")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    campaign_id: Optional[str] = Field(None, description="Assigned campaign ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    config: AgentConfig = Field(..., description="Agent configuration")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AgentUpdate(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Agent update request model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Agent name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: Optional[str] = Field(None, max_length=1000, description="Agent description")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    status: Optional[AgentStatus] = Field(None, description="Agent status")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    campaign_id: Optional[str] = Field(None, description="Assigned campaign ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    config: Optional[AgentConfig] = Field(None, description="Agent configuration")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x6)">class TaskCreate(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Task creation request model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    agent_id: str = Field(..., description="Agent ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    name: str = Field(..., description="Task name")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    description: str = Field(..., description="Task description")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    type: str = Field(..., description="Task type")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    priority: TaskPriority = Field(TaskPriority.NORMAL, description="Task priority")</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    input_data: Dict[str, Any] = Field(default={}, description="Task input data")</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    context: Dict[str, Any] = Field(default={}, description="Additional context")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    scheduled_at: Optional[datetime] = Field(None, description="Scheduled execution time")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    deadline: Optional[datetime] = Field(None, description="Task deadline")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class TaskUpdate(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Task update request model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    status: Optional[TaskStatus] = Field(None, description="Task status")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    priority: Optional[TaskPriority] = Field(None, description="Task priority")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    output_data: Optional[Dict[str, Any]] = Field(None, description="Task output data")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    result: Optional[str] = Field(None, description="Task result summary")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    error_message: Optional[str] = Field(None, description="Error message")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    scheduled_at: Optional[datetime] = Field(None, description="Scheduled execution time")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    deadline: Optional[datetime] = Field(None, description="Task deadline")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AgentResponse(BaseResponse[Agent]):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Single agent response model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    pass</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AgentListResponse(PaginatedResponse[Agent]):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Agent list response model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    pass</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AgentTaskResponse(PaginatedResponse[AgentTask]):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Agent task list response model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    pass</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class TaskResponse(BaseResponse[AgentTask]):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Single task response model.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    pass</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class AgentCommand(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Command to be executed by an agent.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    command: str = Field(..., description="Command to execute")</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x5)">    parameters: Dict[str, Any] = Field(default={}, description="Command parameters")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    timeout_seconds: Optional[int] = Field(None, description="Command timeout")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    priority: TaskPriority = Field(TaskPriority.NORMAL, description="Command priority")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AgentCollaboration(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Agent collaboration configuration.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    enabled: bool = Field(True, description="Enable collaboration")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    max_collaborators: int = Field(5, description="Maximum number of collaborating agents")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    collaboration_strategy: str = Field("hierarchical", description="Collaboration strategy")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    communication_protocol: str = Field("direct", description="Communication protocol")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    shared_memory: bool = Field(False, description="Enable shared memory")</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AgentPerformanceReport(BaseModel):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Agent performance report.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    agent_id: str = Field(..., description="Agent ID")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    report_period: str = Field(..., description="Report period")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    generated_at: datetime = Field(..., description="Report generation timestamp")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    metrics: AgentMetrics = Field(..., description="Performance metrics")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    trends: Dict[str, float] = Field(..., description="Performance trends")</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    recommendations: List[str] = Field(..., description="Performance improvement recommendations")</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">    issues: List[str] = Field(default=[], description="Identified issues")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x8)
Omitted Generics (x5)">    comparison_period: Optional[AgentMetrics] = Field(None, description="Previous period metrics for comparison")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">    @validator("report_period")</span>
<span class="line-any" title="No Anys on this line!">    def validate_report_period(cls, v):</span>
<span class="line-empty" title="No Anys on this line!">        """Validate report period format."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        valid_periods = ["hourly", "daily", "weekly", "monthly"]</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">        if v not in valid_periods:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x6)">            raise ValueError(f"Report period must be one of: {valid_periods}")</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        return v</span>
</pre></td>
</tr></tbody>
</table>
</body>
</html>
