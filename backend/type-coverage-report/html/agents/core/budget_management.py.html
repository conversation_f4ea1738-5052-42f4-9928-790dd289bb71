<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../../mypy-html.css">
</head>
<body>
<h2>agents.core.budget_management</h2>
<table>
<caption>agents/core/budget_management.py</caption>
<tbody><tr>
<td class="table-lines"><pre><span id="L1" class="lineno"><a class="lineno" href="#L1">1</a></span>
<span id="L2" class="lineno"><a class="lineno" href="#L2">2</a></span>
<span id="L3" class="lineno"><a class="lineno" href="#L3">3</a></span>
<span id="L4" class="lineno"><a class="lineno" href="#L4">4</a></span>
<span id="L5" class="lineno"><a class="lineno" href="#L5">5</a></span>
<span id="L6" class="lineno"><a class="lineno" href="#L6">6</a></span>
<span id="L7" class="lineno"><a class="lineno" href="#L7">7</a></span>
<span id="L8" class="lineno"><a class="lineno" href="#L8">8</a></span>
<span id="L9" class="lineno"><a class="lineno" href="#L9">9</a></span>
<span id="L10" class="lineno"><a class="lineno" href="#L10">10</a></span>
<span id="L11" class="lineno"><a class="lineno" href="#L11">11</a></span>
<span id="L12" class="lineno"><a class="lineno" href="#L12">12</a></span>
<span id="L13" class="lineno"><a class="lineno" href="#L13">13</a></span>
<span id="L14" class="lineno"><a class="lineno" href="#L14">14</a></span>
<span id="L15" class="lineno"><a class="lineno" href="#L15">15</a></span>
<span id="L16" class="lineno"><a class="lineno" href="#L16">16</a></span>
<span id="L17" class="lineno"><a class="lineno" href="#L17">17</a></span>
<span id="L18" class="lineno"><a class="lineno" href="#L18">18</a></span>
<span id="L19" class="lineno"><a class="lineno" href="#L19">19</a></span>
<span id="L20" class="lineno"><a class="lineno" href="#L20">20</a></span>
<span id="L21" class="lineno"><a class="lineno" href="#L21">21</a></span>
<span id="L22" class="lineno"><a class="lineno" href="#L22">22</a></span>
<span id="L23" class="lineno"><a class="lineno" href="#L23">23</a></span>
<span id="L24" class="lineno"><a class="lineno" href="#L24">24</a></span>
<span id="L25" class="lineno"><a class="lineno" href="#L25">25</a></span>
<span id="L26" class="lineno"><a class="lineno" href="#L26">26</a></span>
<span id="L27" class="lineno"><a class="lineno" href="#L27">27</a></span>
<span id="L28" class="lineno"><a class="lineno" href="#L28">28</a></span>
<span id="L29" class="lineno"><a class="lineno" href="#L29">29</a></span>
<span id="L30" class="lineno"><a class="lineno" href="#L30">30</a></span>
<span id="L31" class="lineno"><a class="lineno" href="#L31">31</a></span>
<span id="L32" class="lineno"><a class="lineno" href="#L32">32</a></span>
<span id="L33" class="lineno"><a class="lineno" href="#L33">33</a></span>
<span id="L34" class="lineno"><a class="lineno" href="#L34">34</a></span>
<span id="L35" class="lineno"><a class="lineno" href="#L35">35</a></span>
<span id="L36" class="lineno"><a class="lineno" href="#L36">36</a></span>
<span id="L37" class="lineno"><a class="lineno" href="#L37">37</a></span>
<span id="L38" class="lineno"><a class="lineno" href="#L38">38</a></span>
<span id="L39" class="lineno"><a class="lineno" href="#L39">39</a></span>
<span id="L40" class="lineno"><a class="lineno" href="#L40">40</a></span>
<span id="L41" class="lineno"><a class="lineno" href="#L41">41</a></span>
<span id="L42" class="lineno"><a class="lineno" href="#L42">42</a></span>
<span id="L43" class="lineno"><a class="lineno" href="#L43">43</a></span>
<span id="L44" class="lineno"><a class="lineno" href="#L44">44</a></span>
<span id="L45" class="lineno"><a class="lineno" href="#L45">45</a></span>
<span id="L46" class="lineno"><a class="lineno" href="#L46">46</a></span>
<span id="L47" class="lineno"><a class="lineno" href="#L47">47</a></span>
<span id="L48" class="lineno"><a class="lineno" href="#L48">48</a></span>
<span id="L49" class="lineno"><a class="lineno" href="#L49">49</a></span>
<span id="L50" class="lineno"><a class="lineno" href="#L50">50</a></span>
<span id="L51" class="lineno"><a class="lineno" href="#L51">51</a></span>
<span id="L52" class="lineno"><a class="lineno" href="#L52">52</a></span>
<span id="L53" class="lineno"><a class="lineno" href="#L53">53</a></span>
<span id="L54" class="lineno"><a class="lineno" href="#L54">54</a></span>
<span id="L55" class="lineno"><a class="lineno" href="#L55">55</a></span>
<span id="L56" class="lineno"><a class="lineno" href="#L56">56</a></span>
<span id="L57" class="lineno"><a class="lineno" href="#L57">57</a></span>
<span id="L58" class="lineno"><a class="lineno" href="#L58">58</a></span>
<span id="L59" class="lineno"><a class="lineno" href="#L59">59</a></span>
<span id="L60" class="lineno"><a class="lineno" href="#L60">60</a></span>
<span id="L61" class="lineno"><a class="lineno" href="#L61">61</a></span>
<span id="L62" class="lineno"><a class="lineno" href="#L62">62</a></span>
<span id="L63" class="lineno"><a class="lineno" href="#L63">63</a></span>
<span id="L64" class="lineno"><a class="lineno" href="#L64">64</a></span>
<span id="L65" class="lineno"><a class="lineno" href="#L65">65</a></span>
<span id="L66" class="lineno"><a class="lineno" href="#L66">66</a></span>
<span id="L67" class="lineno"><a class="lineno" href="#L67">67</a></span>
<span id="L68" class="lineno"><a class="lineno" href="#L68">68</a></span>
<span id="L69" class="lineno"><a class="lineno" href="#L69">69</a></span>
<span id="L70" class="lineno"><a class="lineno" href="#L70">70</a></span>
<span id="L71" class="lineno"><a class="lineno" href="#L71">71</a></span>
<span id="L72" class="lineno"><a class="lineno" href="#L72">72</a></span>
<span id="L73" class="lineno"><a class="lineno" href="#L73">73</a></span>
<span id="L74" class="lineno"><a class="lineno" href="#L74">74</a></span>
<span id="L75" class="lineno"><a class="lineno" href="#L75">75</a></span>
<span id="L76" class="lineno"><a class="lineno" href="#L76">76</a></span>
<span id="L77" class="lineno"><a class="lineno" href="#L77">77</a></span>
<span id="L78" class="lineno"><a class="lineno" href="#L78">78</a></span>
<span id="L79" class="lineno"><a class="lineno" href="#L79">79</a></span>
<span id="L80" class="lineno"><a class="lineno" href="#L80">80</a></span>
<span id="L81" class="lineno"><a class="lineno" href="#L81">81</a></span>
<span id="L82" class="lineno"><a class="lineno" href="#L82">82</a></span>
<span id="L83" class="lineno"><a class="lineno" href="#L83">83</a></span>
<span id="L84" class="lineno"><a class="lineno" href="#L84">84</a></span>
<span id="L85" class="lineno"><a class="lineno" href="#L85">85</a></span>
<span id="L86" class="lineno"><a class="lineno" href="#L86">86</a></span>
<span id="L87" class="lineno"><a class="lineno" href="#L87">87</a></span>
<span id="L88" class="lineno"><a class="lineno" href="#L88">88</a></span>
<span id="L89" class="lineno"><a class="lineno" href="#L89">89</a></span>
<span id="L90" class="lineno"><a class="lineno" href="#L90">90</a></span>
<span id="L91" class="lineno"><a class="lineno" href="#L91">91</a></span>
<span id="L92" class="lineno"><a class="lineno" href="#L92">92</a></span>
<span id="L93" class="lineno"><a class="lineno" href="#L93">93</a></span>
<span id="L94" class="lineno"><a class="lineno" href="#L94">94</a></span>
<span id="L95" class="lineno"><a class="lineno" href="#L95">95</a></span>
<span id="L96" class="lineno"><a class="lineno" href="#L96">96</a></span>
<span id="L97" class="lineno"><a class="lineno" href="#L97">97</a></span>
<span id="L98" class="lineno"><a class="lineno" href="#L98">98</a></span>
<span id="L99" class="lineno"><a class="lineno" href="#L99">99</a></span>
<span id="L100" class="lineno"><a class="lineno" href="#L100">100</a></span>
<span id="L101" class="lineno"><a class="lineno" href="#L101">101</a></span>
<span id="L102" class="lineno"><a class="lineno" href="#L102">102</a></span>
<span id="L103" class="lineno"><a class="lineno" href="#L103">103</a></span>
<span id="L104" class="lineno"><a class="lineno" href="#L104">104</a></span>
<span id="L105" class="lineno"><a class="lineno" href="#L105">105</a></span>
<span id="L106" class="lineno"><a class="lineno" href="#L106">106</a></span>
<span id="L107" class="lineno"><a class="lineno" href="#L107">107</a></span>
<span id="L108" class="lineno"><a class="lineno" href="#L108">108</a></span>
<span id="L109" class="lineno"><a class="lineno" href="#L109">109</a></span>
<span id="L110" class="lineno"><a class="lineno" href="#L110">110</a></span>
<span id="L111" class="lineno"><a class="lineno" href="#L111">111</a></span>
<span id="L112" class="lineno"><a class="lineno" href="#L112">112</a></span>
<span id="L113" class="lineno"><a class="lineno" href="#L113">113</a></span>
<span id="L114" class="lineno"><a class="lineno" href="#L114">114</a></span>
<span id="L115" class="lineno"><a class="lineno" href="#L115">115</a></span>
<span id="L116" class="lineno"><a class="lineno" href="#L116">116</a></span>
<span id="L117" class="lineno"><a class="lineno" href="#L117">117</a></span>
<span id="L118" class="lineno"><a class="lineno" href="#L118">118</a></span>
<span id="L119" class="lineno"><a class="lineno" href="#L119">119</a></span>
<span id="L120" class="lineno"><a class="lineno" href="#L120">120</a></span>
<span id="L121" class="lineno"><a class="lineno" href="#L121">121</a></span>
<span id="L122" class="lineno"><a class="lineno" href="#L122">122</a></span>
<span id="L123" class="lineno"><a class="lineno" href="#L123">123</a></span>
<span id="L124" class="lineno"><a class="lineno" href="#L124">124</a></span>
<span id="L125" class="lineno"><a class="lineno" href="#L125">125</a></span>
<span id="L126" class="lineno"><a class="lineno" href="#L126">126</a></span>
<span id="L127" class="lineno"><a class="lineno" href="#L127">127</a></span>
<span id="L128" class="lineno"><a class="lineno" href="#L128">128</a></span>
<span id="L129" class="lineno"><a class="lineno" href="#L129">129</a></span>
<span id="L130" class="lineno"><a class="lineno" href="#L130">130</a></span>
<span id="L131" class="lineno"><a class="lineno" href="#L131">131</a></span>
<span id="L132" class="lineno"><a class="lineno" href="#L132">132</a></span>
<span id="L133" class="lineno"><a class="lineno" href="#L133">133</a></span>
<span id="L134" class="lineno"><a class="lineno" href="#L134">134</a></span>
<span id="L135" class="lineno"><a class="lineno" href="#L135">135</a></span>
<span id="L136" class="lineno"><a class="lineno" href="#L136">136</a></span>
<span id="L137" class="lineno"><a class="lineno" href="#L137">137</a></span>
<span id="L138" class="lineno"><a class="lineno" href="#L138">138</a></span>
<span id="L139" class="lineno"><a class="lineno" href="#L139">139</a></span>
<span id="L140" class="lineno"><a class="lineno" href="#L140">140</a></span>
<span id="L141" class="lineno"><a class="lineno" href="#L141">141</a></span>
<span id="L142" class="lineno"><a class="lineno" href="#L142">142</a></span>
<span id="L143" class="lineno"><a class="lineno" href="#L143">143</a></span>
<span id="L144" class="lineno"><a class="lineno" href="#L144">144</a></span>
<span id="L145" class="lineno"><a class="lineno" href="#L145">145</a></span>
<span id="L146" class="lineno"><a class="lineno" href="#L146">146</a></span>
<span id="L147" class="lineno"><a class="lineno" href="#L147">147</a></span>
<span id="L148" class="lineno"><a class="lineno" href="#L148">148</a></span>
<span id="L149" class="lineno"><a class="lineno" href="#L149">149</a></span>
<span id="L150" class="lineno"><a class="lineno" href="#L150">150</a></span>
<span id="L151" class="lineno"><a class="lineno" href="#L151">151</a></span>
<span id="L152" class="lineno"><a class="lineno" href="#L152">152</a></span>
<span id="L153" class="lineno"><a class="lineno" href="#L153">153</a></span>
<span id="L154" class="lineno"><a class="lineno" href="#L154">154</a></span>
<span id="L155" class="lineno"><a class="lineno" href="#L155">155</a></span>
<span id="L156" class="lineno"><a class="lineno" href="#L156">156</a></span>
<span id="L157" class="lineno"><a class="lineno" href="#L157">157</a></span>
<span id="L158" class="lineno"><a class="lineno" href="#L158">158</a></span>
<span id="L159" class="lineno"><a class="lineno" href="#L159">159</a></span>
<span id="L160" class="lineno"><a class="lineno" href="#L160">160</a></span>
<span id="L161" class="lineno"><a class="lineno" href="#L161">161</a></span>
<span id="L162" class="lineno"><a class="lineno" href="#L162">162</a></span>
<span id="L163" class="lineno"><a class="lineno" href="#L163">163</a></span>
<span id="L164" class="lineno"><a class="lineno" href="#L164">164</a></span>
<span id="L165" class="lineno"><a class="lineno" href="#L165">165</a></span>
<span id="L166" class="lineno"><a class="lineno" href="#L166">166</a></span>
<span id="L167" class="lineno"><a class="lineno" href="#L167">167</a></span>
<span id="L168" class="lineno"><a class="lineno" href="#L168">168</a></span>
<span id="L169" class="lineno"><a class="lineno" href="#L169">169</a></span>
<span id="L170" class="lineno"><a class="lineno" href="#L170">170</a></span>
<span id="L171" class="lineno"><a class="lineno" href="#L171">171</a></span>
<span id="L172" class="lineno"><a class="lineno" href="#L172">172</a></span>
<span id="L173" class="lineno"><a class="lineno" href="#L173">173</a></span>
<span id="L174" class="lineno"><a class="lineno" href="#L174">174</a></span>
<span id="L175" class="lineno"><a class="lineno" href="#L175">175</a></span>
<span id="L176" class="lineno"><a class="lineno" href="#L176">176</a></span>
<span id="L177" class="lineno"><a class="lineno" href="#L177">177</a></span>
<span id="L178" class="lineno"><a class="lineno" href="#L178">178</a></span>
<span id="L179" class="lineno"><a class="lineno" href="#L179">179</a></span>
<span id="L180" class="lineno"><a class="lineno" href="#L180">180</a></span>
<span id="L181" class="lineno"><a class="lineno" href="#L181">181</a></span>
<span id="L182" class="lineno"><a class="lineno" href="#L182">182</a></span>
<span id="L183" class="lineno"><a class="lineno" href="#L183">183</a></span>
<span id="L184" class="lineno"><a class="lineno" href="#L184">184</a></span>
<span id="L185" class="lineno"><a class="lineno" href="#L185">185</a></span>
<span id="L186" class="lineno"><a class="lineno" href="#L186">186</a></span>
<span id="L187" class="lineno"><a class="lineno" href="#L187">187</a></span>
<span id="L188" class="lineno"><a class="lineno" href="#L188">188</a></span>
<span id="L189" class="lineno"><a class="lineno" href="#L189">189</a></span>
<span id="L190" class="lineno"><a class="lineno" href="#L190">190</a></span>
<span id="L191" class="lineno"><a class="lineno" href="#L191">191</a></span>
<span id="L192" class="lineno"><a class="lineno" href="#L192">192</a></span>
<span id="L193" class="lineno"><a class="lineno" href="#L193">193</a></span>
<span id="L194" class="lineno"><a class="lineno" href="#L194">194</a></span>
<span id="L195" class="lineno"><a class="lineno" href="#L195">195</a></span>
<span id="L196" class="lineno"><a class="lineno" href="#L196">196</a></span>
<span id="L197" class="lineno"><a class="lineno" href="#L197">197</a></span>
<span id="L198" class="lineno"><a class="lineno" href="#L198">198</a></span>
<span id="L199" class="lineno"><a class="lineno" href="#L199">199</a></span>
<span id="L200" class="lineno"><a class="lineno" href="#L200">200</a></span>
<span id="L201" class="lineno"><a class="lineno" href="#L201">201</a></span>
<span id="L202" class="lineno"><a class="lineno" href="#L202">202</a></span>
<span id="L203" class="lineno"><a class="lineno" href="#L203">203</a></span>
<span id="L204" class="lineno"><a class="lineno" href="#L204">204</a></span>
<span id="L205" class="lineno"><a class="lineno" href="#L205">205</a></span>
<span id="L206" class="lineno"><a class="lineno" href="#L206">206</a></span>
<span id="L207" class="lineno"><a class="lineno" href="#L207">207</a></span>
<span id="L208" class="lineno"><a class="lineno" href="#L208">208</a></span>
<span id="L209" class="lineno"><a class="lineno" href="#L209">209</a></span>
<span id="L210" class="lineno"><a class="lineno" href="#L210">210</a></span>
<span id="L211" class="lineno"><a class="lineno" href="#L211">211</a></span>
<span id="L212" class="lineno"><a class="lineno" href="#L212">212</a></span>
<span id="L213" class="lineno"><a class="lineno" href="#L213">213</a></span>
<span id="L214" class="lineno"><a class="lineno" href="#L214">214</a></span>
<span id="L215" class="lineno"><a class="lineno" href="#L215">215</a></span>
<span id="L216" class="lineno"><a class="lineno" href="#L216">216</a></span>
<span id="L217" class="lineno"><a class="lineno" href="#L217">217</a></span>
<span id="L218" class="lineno"><a class="lineno" href="#L218">218</a></span>
<span id="L219" class="lineno"><a class="lineno" href="#L219">219</a></span>
<span id="L220" class="lineno"><a class="lineno" href="#L220">220</a></span>
<span id="L221" class="lineno"><a class="lineno" href="#L221">221</a></span>
<span id="L222" class="lineno"><a class="lineno" href="#L222">222</a></span>
<span id="L223" class="lineno"><a class="lineno" href="#L223">223</a></span>
<span id="L224" class="lineno"><a class="lineno" href="#L224">224</a></span>
<span id="L225" class="lineno"><a class="lineno" href="#L225">225</a></span>
<span id="L226" class="lineno"><a class="lineno" href="#L226">226</a></span>
<span id="L227" class="lineno"><a class="lineno" href="#L227">227</a></span>
<span id="L228" class="lineno"><a class="lineno" href="#L228">228</a></span>
<span id="L229" class="lineno"><a class="lineno" href="#L229">229</a></span>
<span id="L230" class="lineno"><a class="lineno" href="#L230">230</a></span>
<span id="L231" class="lineno"><a class="lineno" href="#L231">231</a></span>
<span id="L232" class="lineno"><a class="lineno" href="#L232">232</a></span>
<span id="L233" class="lineno"><a class="lineno" href="#L233">233</a></span>
<span id="L234" class="lineno"><a class="lineno" href="#L234">234</a></span>
<span id="L235" class="lineno"><a class="lineno" href="#L235">235</a></span>
<span id="L236" class="lineno"><a class="lineno" href="#L236">236</a></span>
<span id="L237" class="lineno"><a class="lineno" href="#L237">237</a></span>
<span id="L238" class="lineno"><a class="lineno" href="#L238">238</a></span>
<span id="L239" class="lineno"><a class="lineno" href="#L239">239</a></span>
<span id="L240" class="lineno"><a class="lineno" href="#L240">240</a></span>
<span id="L241" class="lineno"><a class="lineno" href="#L241">241</a></span>
<span id="L242" class="lineno"><a class="lineno" href="#L242">242</a></span>
<span id="L243" class="lineno"><a class="lineno" href="#L243">243</a></span>
<span id="L244" class="lineno"><a class="lineno" href="#L244">244</a></span>
<span id="L245" class="lineno"><a class="lineno" href="#L245">245</a></span>
<span id="L246" class="lineno"><a class="lineno" href="#L246">246</a></span>
<span id="L247" class="lineno"><a class="lineno" href="#L247">247</a></span>
<span id="L248" class="lineno"><a class="lineno" href="#L248">248</a></span>
<span id="L249" class="lineno"><a class="lineno" href="#L249">249</a></span>
<span id="L250" class="lineno"><a class="lineno" href="#L250">250</a></span>
<span id="L251" class="lineno"><a class="lineno" href="#L251">251</a></span>
<span id="L252" class="lineno"><a class="lineno" href="#L252">252</a></span>
<span id="L253" class="lineno"><a class="lineno" href="#L253">253</a></span>
<span id="L254" class="lineno"><a class="lineno" href="#L254">254</a></span>
<span id="L255" class="lineno"><a class="lineno" href="#L255">255</a></span>
<span id="L256" class="lineno"><a class="lineno" href="#L256">256</a></span>
<span id="L257" class="lineno"><a class="lineno" href="#L257">257</a></span>
<span id="L258" class="lineno"><a class="lineno" href="#L258">258</a></span>
<span id="L259" class="lineno"><a class="lineno" href="#L259">259</a></span>
<span id="L260" class="lineno"><a class="lineno" href="#L260">260</a></span>
<span id="L261" class="lineno"><a class="lineno" href="#L261">261</a></span>
<span id="L262" class="lineno"><a class="lineno" href="#L262">262</a></span>
<span id="L263" class="lineno"><a class="lineno" href="#L263">263</a></span>
<span id="L264" class="lineno"><a class="lineno" href="#L264">264</a></span>
<span id="L265" class="lineno"><a class="lineno" href="#L265">265</a></span>
<span id="L266" class="lineno"><a class="lineno" href="#L266">266</a></span>
<span id="L267" class="lineno"><a class="lineno" href="#L267">267</a></span>
<span id="L268" class="lineno"><a class="lineno" href="#L268">268</a></span>
<span id="L269" class="lineno"><a class="lineno" href="#L269">269</a></span>
<span id="L270" class="lineno"><a class="lineno" href="#L270">270</a></span>
<span id="L271" class="lineno"><a class="lineno" href="#L271">271</a></span>
<span id="L272" class="lineno"><a class="lineno" href="#L272">272</a></span>
<span id="L273" class="lineno"><a class="lineno" href="#L273">273</a></span>
<span id="L274" class="lineno"><a class="lineno" href="#L274">274</a></span>
<span id="L275" class="lineno"><a class="lineno" href="#L275">275</a></span>
<span id="L276" class="lineno"><a class="lineno" href="#L276">276</a></span>
<span id="L277" class="lineno"><a class="lineno" href="#L277">277</a></span>
<span id="L278" class="lineno"><a class="lineno" href="#L278">278</a></span>
<span id="L279" class="lineno"><a class="lineno" href="#L279">279</a></span>
<span id="L280" class="lineno"><a class="lineno" href="#L280">280</a></span>
<span id="L281" class="lineno"><a class="lineno" href="#L281">281</a></span>
<span id="L282" class="lineno"><a class="lineno" href="#L282">282</a></span>
<span id="L283" class="lineno"><a class="lineno" href="#L283">283</a></span>
<span id="L284" class="lineno"><a class="lineno" href="#L284">284</a></span>
<span id="L285" class="lineno"><a class="lineno" href="#L285">285</a></span>
<span id="L286" class="lineno"><a class="lineno" href="#L286">286</a></span>
<span id="L287" class="lineno"><a class="lineno" href="#L287">287</a></span>
<span id="L288" class="lineno"><a class="lineno" href="#L288">288</a></span>
<span id="L289" class="lineno"><a class="lineno" href="#L289">289</a></span>
<span id="L290" class="lineno"><a class="lineno" href="#L290">290</a></span>
<span id="L291" class="lineno"><a class="lineno" href="#L291">291</a></span>
<span id="L292" class="lineno"><a class="lineno" href="#L292">292</a></span>
<span id="L293" class="lineno"><a class="lineno" href="#L293">293</a></span>
<span id="L294" class="lineno"><a class="lineno" href="#L294">294</a></span>
<span id="L295" class="lineno"><a class="lineno" href="#L295">295</a></span>
<span id="L296" class="lineno"><a class="lineno" href="#L296">296</a></span>
<span id="L297" class="lineno"><a class="lineno" href="#L297">297</a></span>
<span id="L298" class="lineno"><a class="lineno" href="#L298">298</a></span>
<span id="L299" class="lineno"><a class="lineno" href="#L299">299</a></span>
<span id="L300" class="lineno"><a class="lineno" href="#L300">300</a></span>
<span id="L301" class="lineno"><a class="lineno" href="#L301">301</a></span>
<span id="L302" class="lineno"><a class="lineno" href="#L302">302</a></span>
<span id="L303" class="lineno"><a class="lineno" href="#L303">303</a></span>
<span id="L304" class="lineno"><a class="lineno" href="#L304">304</a></span>
<span id="L305" class="lineno"><a class="lineno" href="#L305">305</a></span>
<span id="L306" class="lineno"><a class="lineno" href="#L306">306</a></span>
<span id="L307" class="lineno"><a class="lineno" href="#L307">307</a></span>
<span id="L308" class="lineno"><a class="lineno" href="#L308">308</a></span>
<span id="L309" class="lineno"><a class="lineno" href="#L309">309</a></span>
<span id="L310" class="lineno"><a class="lineno" href="#L310">310</a></span>
<span id="L311" class="lineno"><a class="lineno" href="#L311">311</a></span>
<span id="L312" class="lineno"><a class="lineno" href="#L312">312</a></span>
<span id="L313" class="lineno"><a class="lineno" href="#L313">313</a></span>
<span id="L314" class="lineno"><a class="lineno" href="#L314">314</a></span>
<span id="L315" class="lineno"><a class="lineno" href="#L315">315</a></span>
<span id="L316" class="lineno"><a class="lineno" href="#L316">316</a></span>
<span id="L317" class="lineno"><a class="lineno" href="#L317">317</a></span>
<span id="L318" class="lineno"><a class="lineno" href="#L318">318</a></span>
<span id="L319" class="lineno"><a class="lineno" href="#L319">319</a></span>
<span id="L320" class="lineno"><a class="lineno" href="#L320">320</a></span>
<span id="L321" class="lineno"><a class="lineno" href="#L321">321</a></span>
<span id="L322" class="lineno"><a class="lineno" href="#L322">322</a></span>
<span id="L323" class="lineno"><a class="lineno" href="#L323">323</a></span>
<span id="L324" class="lineno"><a class="lineno" href="#L324">324</a></span>
<span id="L325" class="lineno"><a class="lineno" href="#L325">325</a></span>
<span id="L326" class="lineno"><a class="lineno" href="#L326">326</a></span>
<span id="L327" class="lineno"><a class="lineno" href="#L327">327</a></span>
<span id="L328" class="lineno"><a class="lineno" href="#L328">328</a></span>
<span id="L329" class="lineno"><a class="lineno" href="#L329">329</a></span>
<span id="L330" class="lineno"><a class="lineno" href="#L330">330</a></span>
<span id="L331" class="lineno"><a class="lineno" href="#L331">331</a></span>
<span id="L332" class="lineno"><a class="lineno" href="#L332">332</a></span>
<span id="L333" class="lineno"><a class="lineno" href="#L333">333</a></span>
<span id="L334" class="lineno"><a class="lineno" href="#L334">334</a></span>
<span id="L335" class="lineno"><a class="lineno" href="#L335">335</a></span>
<span id="L336" class="lineno"><a class="lineno" href="#L336">336</a></span>
<span id="L337" class="lineno"><a class="lineno" href="#L337">337</a></span>
<span id="L338" class="lineno"><a class="lineno" href="#L338">338</a></span>
<span id="L339" class="lineno"><a class="lineno" href="#L339">339</a></span>
<span id="L340" class="lineno"><a class="lineno" href="#L340">340</a></span>
<span id="L341" class="lineno"><a class="lineno" href="#L341">341</a></span>
<span id="L342" class="lineno"><a class="lineno" href="#L342">342</a></span>
<span id="L343" class="lineno"><a class="lineno" href="#L343">343</a></span>
<span id="L344" class="lineno"><a class="lineno" href="#L344">344</a></span>
<span id="L345" class="lineno"><a class="lineno" href="#L345">345</a></span>
<span id="L346" class="lineno"><a class="lineno" href="#L346">346</a></span>
<span id="L347" class="lineno"><a class="lineno" href="#L347">347</a></span>
<span id="L348" class="lineno"><a class="lineno" href="#L348">348</a></span>
<span id="L349" class="lineno"><a class="lineno" href="#L349">349</a></span>
<span id="L350" class="lineno"><a class="lineno" href="#L350">350</a></span>
<span id="L351" class="lineno"><a class="lineno" href="#L351">351</a></span>
<span id="L352" class="lineno"><a class="lineno" href="#L352">352</a></span>
<span id="L353" class="lineno"><a class="lineno" href="#L353">353</a></span>
<span id="L354" class="lineno"><a class="lineno" href="#L354">354</a></span>
<span id="L355" class="lineno"><a class="lineno" href="#L355">355</a></span>
<span id="L356" class="lineno"><a class="lineno" href="#L356">356</a></span>
<span id="L357" class="lineno"><a class="lineno" href="#L357">357</a></span>
<span id="L358" class="lineno"><a class="lineno" href="#L358">358</a></span>
<span id="L359" class="lineno"><a class="lineno" href="#L359">359</a></span>
<span id="L360" class="lineno"><a class="lineno" href="#L360">360</a></span>
<span id="L361" class="lineno"><a class="lineno" href="#L361">361</a></span>
<span id="L362" class="lineno"><a class="lineno" href="#L362">362</a></span>
<span id="L363" class="lineno"><a class="lineno" href="#L363">363</a></span>
<span id="L364" class="lineno"><a class="lineno" href="#L364">364</a></span>
<span id="L365" class="lineno"><a class="lineno" href="#L365">365</a></span>
<span id="L366" class="lineno"><a class="lineno" href="#L366">366</a></span>
<span id="L367" class="lineno"><a class="lineno" href="#L367">367</a></span>
<span id="L368" class="lineno"><a class="lineno" href="#L368">368</a></span>
<span id="L369" class="lineno"><a class="lineno" href="#L369">369</a></span>
<span id="L370" class="lineno"><a class="lineno" href="#L370">370</a></span>
<span id="L371" class="lineno"><a class="lineno" href="#L371">371</a></span>
<span id="L372" class="lineno"><a class="lineno" href="#L372">372</a></span>
<span id="L373" class="lineno"><a class="lineno" href="#L373">373</a></span>
<span id="L374" class="lineno"><a class="lineno" href="#L374">374</a></span>
<span id="L375" class="lineno"><a class="lineno" href="#L375">375</a></span>
<span id="L376" class="lineno"><a class="lineno" href="#L376">376</a></span>
<span id="L377" class="lineno"><a class="lineno" href="#L377">377</a></span>
<span id="L378" class="lineno"><a class="lineno" href="#L378">378</a></span>
<span id="L379" class="lineno"><a class="lineno" href="#L379">379</a></span>
<span id="L380" class="lineno"><a class="lineno" href="#L380">380</a></span>
<span id="L381" class="lineno"><a class="lineno" href="#L381">381</a></span>
<span id="L382" class="lineno"><a class="lineno" href="#L382">382</a></span>
<span id="L383" class="lineno"><a class="lineno" href="#L383">383</a></span>
<span id="L384" class="lineno"><a class="lineno" href="#L384">384</a></span>
<span id="L385" class="lineno"><a class="lineno" href="#L385">385</a></span>
<span id="L386" class="lineno"><a class="lineno" href="#L386">386</a></span>
<span id="L387" class="lineno"><a class="lineno" href="#L387">387</a></span>
<span id="L388" class="lineno"><a class="lineno" href="#L388">388</a></span>
<span id="L389" class="lineno"><a class="lineno" href="#L389">389</a></span>
<span id="L390" class="lineno"><a class="lineno" href="#L390">390</a></span>
<span id="L391" class="lineno"><a class="lineno" href="#L391">391</a></span>
<span id="L392" class="lineno"><a class="lineno" href="#L392">392</a></span>
<span id="L393" class="lineno"><a class="lineno" href="#L393">393</a></span>
<span id="L394" class="lineno"><a class="lineno" href="#L394">394</a></span>
<span id="L395" class="lineno"><a class="lineno" href="#L395">395</a></span>
<span id="L396" class="lineno"><a class="lineno" href="#L396">396</a></span>
<span id="L397" class="lineno"><a class="lineno" href="#L397">397</a></span>
<span id="L398" class="lineno"><a class="lineno" href="#L398">398</a></span>
<span id="L399" class="lineno"><a class="lineno" href="#L399">399</a></span>
<span id="L400" class="lineno"><a class="lineno" href="#L400">400</a></span>
<span id="L401" class="lineno"><a class="lineno" href="#L401">401</a></span>
<span id="L402" class="lineno"><a class="lineno" href="#L402">402</a></span>
<span id="L403" class="lineno"><a class="lineno" href="#L403">403</a></span>
<span id="L404" class="lineno"><a class="lineno" href="#L404">404</a></span>
<span id="L405" class="lineno"><a class="lineno" href="#L405">405</a></span>
<span id="L406" class="lineno"><a class="lineno" href="#L406">406</a></span>
<span id="L407" class="lineno"><a class="lineno" href="#L407">407</a></span>
<span id="L408" class="lineno"><a class="lineno" href="#L408">408</a></span>
<span id="L409" class="lineno"><a class="lineno" href="#L409">409</a></span>
<span id="L410" class="lineno"><a class="lineno" href="#L410">410</a></span>
<span id="L411" class="lineno"><a class="lineno" href="#L411">411</a></span>
<span id="L412" class="lineno"><a class="lineno" href="#L412">412</a></span>
<span id="L413" class="lineno"><a class="lineno" href="#L413">413</a></span>
<span id="L414" class="lineno"><a class="lineno" href="#L414">414</a></span>
<span id="L415" class="lineno"><a class="lineno" href="#L415">415</a></span>
<span id="L416" class="lineno"><a class="lineno" href="#L416">416</a></span>
<span id="L417" class="lineno"><a class="lineno" href="#L417">417</a></span>
<span id="L418" class="lineno"><a class="lineno" href="#L418">418</a></span>
<span id="L419" class="lineno"><a class="lineno" href="#L419">419</a></span>
<span id="L420" class="lineno"><a class="lineno" href="#L420">420</a></span>
<span id="L421" class="lineno"><a class="lineno" href="#L421">421</a></span>
<span id="L422" class="lineno"><a class="lineno" href="#L422">422</a></span>
<span id="L423" class="lineno"><a class="lineno" href="#L423">423</a></span>
<span id="L424" class="lineno"><a class="lineno" href="#L424">424</a></span>
<span id="L425" class="lineno"><a class="lineno" href="#L425">425</a></span>
<span id="L426" class="lineno"><a class="lineno" href="#L426">426</a></span>
<span id="L427" class="lineno"><a class="lineno" href="#L427">427</a></span>
<span id="L428" class="lineno"><a class="lineno" href="#L428">428</a></span>
<span id="L429" class="lineno"><a class="lineno" href="#L429">429</a></span>
<span id="L430" class="lineno"><a class="lineno" href="#L430">430</a></span>
<span id="L431" class="lineno"><a class="lineno" href="#L431">431</a></span>
<span id="L432" class="lineno"><a class="lineno" href="#L432">432</a></span>
<span id="L433" class="lineno"><a class="lineno" href="#L433">433</a></span>
<span id="L434" class="lineno"><a class="lineno" href="#L434">434</a></span>
<span id="L435" class="lineno"><a class="lineno" href="#L435">435</a></span>
<span id="L436" class="lineno"><a class="lineno" href="#L436">436</a></span>
<span id="L437" class="lineno"><a class="lineno" href="#L437">437</a></span>
<span id="L438" class="lineno"><a class="lineno" href="#L438">438</a></span>
<span id="L439" class="lineno"><a class="lineno" href="#L439">439</a></span>
<span id="L440" class="lineno"><a class="lineno" href="#L440">440</a></span>
<span id="L441" class="lineno"><a class="lineno" href="#L441">441</a></span>
<span id="L442" class="lineno"><a class="lineno" href="#L442">442</a></span>
<span id="L443" class="lineno"><a class="lineno" href="#L443">443</a></span>
<span id="L444" class="lineno"><a class="lineno" href="#L444">444</a></span>
<span id="L445" class="lineno"><a class="lineno" href="#L445">445</a></span>
<span id="L446" class="lineno"><a class="lineno" href="#L446">446</a></span>
<span id="L447" class="lineno"><a class="lineno" href="#L447">447</a></span>
<span id="L448" class="lineno"><a class="lineno" href="#L448">448</a></span>
<span id="L449" class="lineno"><a class="lineno" href="#L449">449</a></span>
<span id="L450" class="lineno"><a class="lineno" href="#L450">450</a></span>
<span id="L451" class="lineno"><a class="lineno" href="#L451">451</a></span>
<span id="L452" class="lineno"><a class="lineno" href="#L452">452</a></span>
<span id="L453" class="lineno"><a class="lineno" href="#L453">453</a></span>
<span id="L454" class="lineno"><a class="lineno" href="#L454">454</a></span>
<span id="L455" class="lineno"><a class="lineno" href="#L455">455</a></span>
<span id="L456" class="lineno"><a class="lineno" href="#L456">456</a></span>
<span id="L457" class="lineno"><a class="lineno" href="#L457">457</a></span>
<span id="L458" class="lineno"><a class="lineno" href="#L458">458</a></span>
<span id="L459" class="lineno"><a class="lineno" href="#L459">459</a></span>
<span id="L460" class="lineno"><a class="lineno" href="#L460">460</a></span>
<span id="L461" class="lineno"><a class="lineno" href="#L461">461</a></span>
<span id="L462" class="lineno"><a class="lineno" href="#L462">462</a></span>
<span id="L463" class="lineno"><a class="lineno" href="#L463">463</a></span>
<span id="L464" class="lineno"><a class="lineno" href="#L464">464</a></span>
<span id="L465" class="lineno"><a class="lineno" href="#L465">465</a></span>
<span id="L466" class="lineno"><a class="lineno" href="#L466">466</a></span>
<span id="L467" class="lineno"><a class="lineno" href="#L467">467</a></span>
<span id="L468" class="lineno"><a class="lineno" href="#L468">468</a></span>
<span id="L469" class="lineno"><a class="lineno" href="#L469">469</a></span>
<span id="L470" class="lineno"><a class="lineno" href="#L470">470</a></span>
<span id="L471" class="lineno"><a class="lineno" href="#L471">471</a></span>
<span id="L472" class="lineno"><a class="lineno" href="#L472">472</a></span>
<span id="L473" class="lineno"><a class="lineno" href="#L473">473</a></span>
<span id="L474" class="lineno"><a class="lineno" href="#L474">474</a></span>
<span id="L475" class="lineno"><a class="lineno" href="#L475">475</a></span>
<span id="L476" class="lineno"><a class="lineno" href="#L476">476</a></span>
<span id="L477" class="lineno"><a class="lineno" href="#L477">477</a></span>
<span id="L478" class="lineno"><a class="lineno" href="#L478">478</a></span>
<span id="L479" class="lineno"><a class="lineno" href="#L479">479</a></span>
<span id="L480" class="lineno"><a class="lineno" href="#L480">480</a></span>
<span id="L481" class="lineno"><a class="lineno" href="#L481">481</a></span>
<span id="L482" class="lineno"><a class="lineno" href="#L482">482</a></span>
<span id="L483" class="lineno"><a class="lineno" href="#L483">483</a></span>
<span id="L484" class="lineno"><a class="lineno" href="#L484">484</a></span>
<span id="L485" class="lineno"><a class="lineno" href="#L485">485</a></span>
<span id="L486" class="lineno"><a class="lineno" href="#L486">486</a></span>
<span id="L487" class="lineno"><a class="lineno" href="#L487">487</a></span>
<span id="L488" class="lineno"><a class="lineno" href="#L488">488</a></span>
<span id="L489" class="lineno"><a class="lineno" href="#L489">489</a></span>
<span id="L490" class="lineno"><a class="lineno" href="#L490">490</a></span>
<span id="L491" class="lineno"><a class="lineno" href="#L491">491</a></span>
<span id="L492" class="lineno"><a class="lineno" href="#L492">492</a></span>
<span id="L493" class="lineno"><a class="lineno" href="#L493">493</a></span>
<span id="L494" class="lineno"><a class="lineno" href="#L494">494</a></span>
<span id="L495" class="lineno"><a class="lineno" href="#L495">495</a></span>
<span id="L496" class="lineno"><a class="lineno" href="#L496">496</a></span>
<span id="L497" class="lineno"><a class="lineno" href="#L497">497</a></span>
<span id="L498" class="lineno"><a class="lineno" href="#L498">498</a></span>
<span id="L499" class="lineno"><a class="lineno" href="#L499">499</a></span>
<span id="L500" class="lineno"><a class="lineno" href="#L500">500</a></span>
<span id="L501" class="lineno"><a class="lineno" href="#L501">501</a></span>
<span id="L502" class="lineno"><a class="lineno" href="#L502">502</a></span>
<span id="L503" class="lineno"><a class="lineno" href="#L503">503</a></span>
<span id="L504" class="lineno"><a class="lineno" href="#L504">504</a></span>
<span id="L505" class="lineno"><a class="lineno" href="#L505">505</a></span>
<span id="L506" class="lineno"><a class="lineno" href="#L506">506</a></span>
<span id="L507" class="lineno"><a class="lineno" href="#L507">507</a></span>
<span id="L508" class="lineno"><a class="lineno" href="#L508">508</a></span>
<span id="L509" class="lineno"><a class="lineno" href="#L509">509</a></span>
<span id="L510" class="lineno"><a class="lineno" href="#L510">510</a></span>
<span id="L511" class="lineno"><a class="lineno" href="#L511">511</a></span>
<span id="L512" class="lineno"><a class="lineno" href="#L512">512</a></span>
<span id="L513" class="lineno"><a class="lineno" href="#L513">513</a></span>
<span id="L514" class="lineno"><a class="lineno" href="#L514">514</a></span>
<span id="L515" class="lineno"><a class="lineno" href="#L515">515</a></span>
<span id="L516" class="lineno"><a class="lineno" href="#L516">516</a></span>
<span id="L517" class="lineno"><a class="lineno" href="#L517">517</a></span>
<span id="L518" class="lineno"><a class="lineno" href="#L518">518</a></span>
<span id="L519" class="lineno"><a class="lineno" href="#L519">519</a></span>
<span id="L520" class="lineno"><a class="lineno" href="#L520">520</a></span>
<span id="L521" class="lineno"><a class="lineno" href="#L521">521</a></span>
<span id="L522" class="lineno"><a class="lineno" href="#L522">522</a></span>
<span id="L523" class="lineno"><a class="lineno" href="#L523">523</a></span>
<span id="L524" class="lineno"><a class="lineno" href="#L524">524</a></span>
<span id="L525" class="lineno"><a class="lineno" href="#L525">525</a></span>
<span id="L526" class="lineno"><a class="lineno" href="#L526">526</a></span>
<span id="L527" class="lineno"><a class="lineno" href="#L527">527</a></span>
<span id="L528" class="lineno"><a class="lineno" href="#L528">528</a></span>
<span id="L529" class="lineno"><a class="lineno" href="#L529">529</a></span>
<span id="L530" class="lineno"><a class="lineno" href="#L530">530</a></span>
<span id="L531" class="lineno"><a class="lineno" href="#L531">531</a></span>
<span id="L532" class="lineno"><a class="lineno" href="#L532">532</a></span>
<span id="L533" class="lineno"><a class="lineno" href="#L533">533</a></span>
<span id="L534" class="lineno"><a class="lineno" href="#L534">534</a></span>
<span id="L535" class="lineno"><a class="lineno" href="#L535">535</a></span>
<span id="L536" class="lineno"><a class="lineno" href="#L536">536</a></span>
<span id="L537" class="lineno"><a class="lineno" href="#L537">537</a></span>
<span id="L538" class="lineno"><a class="lineno" href="#L538">538</a></span>
<span id="L539" class="lineno"><a class="lineno" href="#L539">539</a></span>
<span id="L540" class="lineno"><a class="lineno" href="#L540">540</a></span>
<span id="L541" class="lineno"><a class="lineno" href="#L541">541</a></span>
<span id="L542" class="lineno"><a class="lineno" href="#L542">542</a></span>
<span id="L543" class="lineno"><a class="lineno" href="#L543">543</a></span>
<span id="L544" class="lineno"><a class="lineno" href="#L544">544</a></span>
<span id="L545" class="lineno"><a class="lineno" href="#L545">545</a></span>
<span id="L546" class="lineno"><a class="lineno" href="#L546">546</a></span>
<span id="L547" class="lineno"><a class="lineno" href="#L547">547</a></span>
<span id="L548" class="lineno"><a class="lineno" href="#L548">548</a></span>
<span id="L549" class="lineno"><a class="lineno" href="#L549">549</a></span>
<span id="L550" class="lineno"><a class="lineno" href="#L550">550</a></span>
<span id="L551" class="lineno"><a class="lineno" href="#L551">551</a></span>
<span id="L552" class="lineno"><a class="lineno" href="#L552">552</a></span>
<span id="L553" class="lineno"><a class="lineno" href="#L553">553</a></span>
<span id="L554" class="lineno"><a class="lineno" href="#L554">554</a></span>
<span id="L555" class="lineno"><a class="lineno" href="#L555">555</a></span>
<span id="L556" class="lineno"><a class="lineno" href="#L556">556</a></span>
<span id="L557" class="lineno"><a class="lineno" href="#L557">557</a></span>
<span id="L558" class="lineno"><a class="lineno" href="#L558">558</a></span>
<span id="L559" class="lineno"><a class="lineno" href="#L559">559</a></span>
<span id="L560" class="lineno"><a class="lineno" href="#L560">560</a></span>
<span id="L561" class="lineno"><a class="lineno" href="#L561">561</a></span>
<span id="L562" class="lineno"><a class="lineno" href="#L562">562</a></span>
<span id="L563" class="lineno"><a class="lineno" href="#L563">563</a></span>
<span id="L564" class="lineno"><a class="lineno" href="#L564">564</a></span>
<span id="L565" class="lineno"><a class="lineno" href="#L565">565</a></span>
<span id="L566" class="lineno"><a class="lineno" href="#L566">566</a></span>
<span id="L567" class="lineno"><a class="lineno" href="#L567">567</a></span>
<span id="L568" class="lineno"><a class="lineno" href="#L568">568</a></span>
<span id="L569" class="lineno"><a class="lineno" href="#L569">569</a></span>
<span id="L570" class="lineno"><a class="lineno" href="#L570">570</a></span>
<span id="L571" class="lineno"><a class="lineno" href="#L571">571</a></span>
<span id="L572" class="lineno"><a class="lineno" href="#L572">572</a></span>
<span id="L573" class="lineno"><a class="lineno" href="#L573">573</a></span>
<span id="L574" class="lineno"><a class="lineno" href="#L574">574</a></span>
<span id="L575" class="lineno"><a class="lineno" href="#L575">575</a></span>
<span id="L576" class="lineno"><a class="lineno" href="#L576">576</a></span>
<span id="L577" class="lineno"><a class="lineno" href="#L577">577</a></span>
<span id="L578" class="lineno"><a class="lineno" href="#L578">578</a></span>
<span id="L579" class="lineno"><a class="lineno" href="#L579">579</a></span>
<span id="L580" class="lineno"><a class="lineno" href="#L580">580</a></span>
<span id="L581" class="lineno"><a class="lineno" href="#L581">581</a></span>
<span id="L582" class="lineno"><a class="lineno" href="#L582">582</a></span>
<span id="L583" class="lineno"><a class="lineno" href="#L583">583</a></span>
<span id="L584" class="lineno"><a class="lineno" href="#L584">584</a></span>
<span id="L585" class="lineno"><a class="lineno" href="#L585">585</a></span>
<span id="L586" class="lineno"><a class="lineno" href="#L586">586</a></span>
<span id="L587" class="lineno"><a class="lineno" href="#L587">587</a></span>
<span id="L588" class="lineno"><a class="lineno" href="#L588">588</a></span>
<span id="L589" class="lineno"><a class="lineno" href="#L589">589</a></span>
<span id="L590" class="lineno"><a class="lineno" href="#L590">590</a></span>
<span id="L591" class="lineno"><a class="lineno" href="#L591">591</a></span>
<span id="L592" class="lineno"><a class="lineno" href="#L592">592</a></span>
<span id="L593" class="lineno"><a class="lineno" href="#L593">593</a></span>
<span id="L594" class="lineno"><a class="lineno" href="#L594">594</a></span>
<span id="L595" class="lineno"><a class="lineno" href="#L595">595</a></span>
<span id="L596" class="lineno"><a class="lineno" href="#L596">596</a></span>
<span id="L597" class="lineno"><a class="lineno" href="#L597">597</a></span>
<span id="L598" class="lineno"><a class="lineno" href="#L598">598</a></span>
<span id="L599" class="lineno"><a class="lineno" href="#L599">599</a></span>
<span id="L600" class="lineno"><a class="lineno" href="#L600">600</a></span>
<span id="L601" class="lineno"><a class="lineno" href="#L601">601</a></span>
<span id="L602" class="lineno"><a class="lineno" href="#L602">602</a></span>
<span id="L603" class="lineno"><a class="lineno" href="#L603">603</a></span>
<span id="L604" class="lineno"><a class="lineno" href="#L604">604</a></span>
<span id="L605" class="lineno"><a class="lineno" href="#L605">605</a></span>
<span id="L606" class="lineno"><a class="lineno" href="#L606">606</a></span>
<span id="L607" class="lineno"><a class="lineno" href="#L607">607</a></span>
<span id="L608" class="lineno"><a class="lineno" href="#L608">608</a></span>
<span id="L609" class="lineno"><a class="lineno" href="#L609">609</a></span>
<span id="L610" class="lineno"><a class="lineno" href="#L610">610</a></span>
<span id="L611" class="lineno"><a class="lineno" href="#L611">611</a></span>
<span id="L612" class="lineno"><a class="lineno" href="#L612">612</a></span>
<span id="L613" class="lineno"><a class="lineno" href="#L613">613</a></span>
<span id="L614" class="lineno"><a class="lineno" href="#L614">614</a></span>
<span id="L615" class="lineno"><a class="lineno" href="#L615">615</a></span>
<span id="L616" class="lineno"><a class="lineno" href="#L616">616</a></span>
<span id="L617" class="lineno"><a class="lineno" href="#L617">617</a></span>
<span id="L618" class="lineno"><a class="lineno" href="#L618">618</a></span>
<span id="L619" class="lineno"><a class="lineno" href="#L619">619</a></span>
<span id="L620" class="lineno"><a class="lineno" href="#L620">620</a></span>
<span id="L621" class="lineno"><a class="lineno" href="#L621">621</a></span>
<span id="L622" class="lineno"><a class="lineno" href="#L622">622</a></span>
<span id="L623" class="lineno"><a class="lineno" href="#L623">623</a></span>
<span id="L624" class="lineno"><a class="lineno" href="#L624">624</a></span>
<span id="L625" class="lineno"><a class="lineno" href="#L625">625</a></span>
<span id="L626" class="lineno"><a class="lineno" href="#L626">626</a></span>
<span id="L627" class="lineno"><a class="lineno" href="#L627">627</a></span>
<span id="L628" class="lineno"><a class="lineno" href="#L628">628</a></span>
<span id="L629" class="lineno"><a class="lineno" href="#L629">629</a></span>
<span id="L630" class="lineno"><a class="lineno" href="#L630">630</a></span>
<span id="L631" class="lineno"><a class="lineno" href="#L631">631</a></span>
<span id="L632" class="lineno"><a class="lineno" href="#L632">632</a></span>
<span id="L633" class="lineno"><a class="lineno" href="#L633">633</a></span>
<span id="L634" class="lineno"><a class="lineno" href="#L634">634</a></span>
<span id="L635" class="lineno"><a class="lineno" href="#L635">635</a></span>
<span id="L636" class="lineno"><a class="lineno" href="#L636">636</a></span>
<span id="L637" class="lineno"><a class="lineno" href="#L637">637</a></span>
<span id="L638" class="lineno"><a class="lineno" href="#L638">638</a></span>
<span id="L639" class="lineno"><a class="lineno" href="#L639">639</a></span>
<span id="L640" class="lineno"><a class="lineno" href="#L640">640</a></span>
<span id="L641" class="lineno"><a class="lineno" href="#L641">641</a></span>
<span id="L642" class="lineno"><a class="lineno" href="#L642">642</a></span>
<span id="L643" class="lineno"><a class="lineno" href="#L643">643</a></span>
<span id="L644" class="lineno"><a class="lineno" href="#L644">644</a></span>
<span id="L645" class="lineno"><a class="lineno" href="#L645">645</a></span>
<span id="L646" class="lineno"><a class="lineno" href="#L646">646</a></span>
<span id="L647" class="lineno"><a class="lineno" href="#L647">647</a></span>
<span id="L648" class="lineno"><a class="lineno" href="#L648">648</a></span>
<span id="L649" class="lineno"><a class="lineno" href="#L649">649</a></span>
<span id="L650" class="lineno"><a class="lineno" href="#L650">650</a></span>
<span id="L651" class="lineno"><a class="lineno" href="#L651">651</a></span>
<span id="L652" class="lineno"><a class="lineno" href="#L652">652</a></span>
<span id="L653" class="lineno"><a class="lineno" href="#L653">653</a></span>
<span id="L654" class="lineno"><a class="lineno" href="#L654">654</a></span>
<span id="L655" class="lineno"><a class="lineno" href="#L655">655</a></span>
<span id="L656" class="lineno"><a class="lineno" href="#L656">656</a></span>
<span id="L657" class="lineno"><a class="lineno" href="#L657">657</a></span>
<span id="L658" class="lineno"><a class="lineno" href="#L658">658</a></span>
<span id="L659" class="lineno"><a class="lineno" href="#L659">659</a></span>
<span id="L660" class="lineno"><a class="lineno" href="#L660">660</a></span>
<span id="L661" class="lineno"><a class="lineno" href="#L661">661</a></span>
<span id="L662" class="lineno"><a class="lineno" href="#L662">662</a></span>
<span id="L663" class="lineno"><a class="lineno" href="#L663">663</a></span>
<span id="L664" class="lineno"><a class="lineno" href="#L664">664</a></span>
<span id="L665" class="lineno"><a class="lineno" href="#L665">665</a></span>
<span id="L666" class="lineno"><a class="lineno" href="#L666">666</a></span>
<span id="L667" class="lineno"><a class="lineno" href="#L667">667</a></span>
<span id="L668" class="lineno"><a class="lineno" href="#L668">668</a></span>
<span id="L669" class="lineno"><a class="lineno" href="#L669">669</a></span>
<span id="L670" class="lineno"><a class="lineno" href="#L670">670</a></span>
<span id="L671" class="lineno"><a class="lineno" href="#L671">671</a></span>
<span id="L672" class="lineno"><a class="lineno" href="#L672">672</a></span>
<span id="L673" class="lineno"><a class="lineno" href="#L673">673</a></span>
<span id="L674" class="lineno"><a class="lineno" href="#L674">674</a></span>
<span id="L675" class="lineno"><a class="lineno" href="#L675">675</a></span>
<span id="L676" class="lineno"><a class="lineno" href="#L676">676</a></span>
<span id="L677" class="lineno"><a class="lineno" href="#L677">677</a></span>
<span id="L678" class="lineno"><a class="lineno" href="#L678">678</a></span>
<span id="L679" class="lineno"><a class="lineno" href="#L679">679</a></span>
<span id="L680" class="lineno"><a class="lineno" href="#L680">680</a></span>
<span id="L681" class="lineno"><a class="lineno" href="#L681">681</a></span>
<span id="L682" class="lineno"><a class="lineno" href="#L682">682</a></span>
<span id="L683" class="lineno"><a class="lineno" href="#L683">683</a></span>
<span id="L684" class="lineno"><a class="lineno" href="#L684">684</a></span>
<span id="L685" class="lineno"><a class="lineno" href="#L685">685</a></span>
<span id="L686" class="lineno"><a class="lineno" href="#L686">686</a></span>
<span id="L687" class="lineno"><a class="lineno" href="#L687">687</a></span>
<span id="L688" class="lineno"><a class="lineno" href="#L688">688</a></span>
<span id="L689" class="lineno"><a class="lineno" href="#L689">689</a></span>
<span id="L690" class="lineno"><a class="lineno" href="#L690">690</a></span>
<span id="L691" class="lineno"><a class="lineno" href="#L691">691</a></span>
<span id="L692" class="lineno"><a class="lineno" href="#L692">692</a></span>
<span id="L693" class="lineno"><a class="lineno" href="#L693">693</a></span>
<span id="L694" class="lineno"><a class="lineno" href="#L694">694</a></span>
<span id="L695" class="lineno"><a class="lineno" href="#L695">695</a></span>
<span id="L696" class="lineno"><a class="lineno" href="#L696">696</a></span>
<span id="L697" class="lineno"><a class="lineno" href="#L697">697</a></span>
<span id="L698" class="lineno"><a class="lineno" href="#L698">698</a></span>
<span id="L699" class="lineno"><a class="lineno" href="#L699">699</a></span>
<span id="L700" class="lineno"><a class="lineno" href="#L700">700</a></span>
<span id="L701" class="lineno"><a class="lineno" href="#L701">701</a></span>
<span id="L702" class="lineno"><a class="lineno" href="#L702">702</a></span>
<span id="L703" class="lineno"><a class="lineno" href="#L703">703</a></span>
<span id="L704" class="lineno"><a class="lineno" href="#L704">704</a></span>
<span id="L705" class="lineno"><a class="lineno" href="#L705">705</a></span>
<span id="L706" class="lineno"><a class="lineno" href="#L706">706</a></span>
<span id="L707" class="lineno"><a class="lineno" href="#L707">707</a></span>
<span id="L708" class="lineno"><a class="lineno" href="#L708">708</a></span>
<span id="L709" class="lineno"><a class="lineno" href="#L709">709</a></span>
<span id="L710" class="lineno"><a class="lineno" href="#L710">710</a></span>
<span id="L711" class="lineno"><a class="lineno" href="#L711">711</a></span>
<span id="L712" class="lineno"><a class="lineno" href="#L712">712</a></span>
<span id="L713" class="lineno"><a class="lineno" href="#L713">713</a></span>
<span id="L714" class="lineno"><a class="lineno" href="#L714">714</a></span>
<span id="L715" class="lineno"><a class="lineno" href="#L715">715</a></span>
<span id="L716" class="lineno"><a class="lineno" href="#L716">716</a></span>
<span id="L717" class="lineno"><a class="lineno" href="#L717">717</a></span>
<span id="L718" class="lineno"><a class="lineno" href="#L718">718</a></span>
<span id="L719" class="lineno"><a class="lineno" href="#L719">719</a></span>
<span id="L720" class="lineno"><a class="lineno" href="#L720">720</a></span>
<span id="L721" class="lineno"><a class="lineno" href="#L721">721</a></span>
<span id="L722" class="lineno"><a class="lineno" href="#L722">722</a></span>
<span id="L723" class="lineno"><a class="lineno" href="#L723">723</a></span>
<span id="L724" class="lineno"><a class="lineno" href="#L724">724</a></span>
<span id="L725" class="lineno"><a class="lineno" href="#L725">725</a></span>
<span id="L726" class="lineno"><a class="lineno" href="#L726">726</a></span>
<span id="L727" class="lineno"><a class="lineno" href="#L727">727</a></span>
<span id="L728" class="lineno"><a class="lineno" href="#L728">728</a></span>
<span id="L729" class="lineno"><a class="lineno" href="#L729">729</a></span>
<span id="L730" class="lineno"><a class="lineno" href="#L730">730</a></span>
<span id="L731" class="lineno"><a class="lineno" href="#L731">731</a></span>
<span id="L732" class="lineno"><a class="lineno" href="#L732">732</a></span>
<span id="L733" class="lineno"><a class="lineno" href="#L733">733</a></span>
<span id="L734" class="lineno"><a class="lineno" href="#L734">734</a></span>
<span id="L735" class="lineno"><a class="lineno" href="#L735">735</a></span>
<span id="L736" class="lineno"><a class="lineno" href="#L736">736</a></span>
<span id="L737" class="lineno"><a class="lineno" href="#L737">737</a></span>
<span id="L738" class="lineno"><a class="lineno" href="#L738">738</a></span>
<span id="L739" class="lineno"><a class="lineno" href="#L739">739</a></span>
<span id="L740" class="lineno"><a class="lineno" href="#L740">740</a></span>
<span id="L741" class="lineno"><a class="lineno" href="#L741">741</a></span>
<span id="L742" class="lineno"><a class="lineno" href="#L742">742</a></span>
<span id="L743" class="lineno"><a class="lineno" href="#L743">743</a></span>
<span id="L744" class="lineno"><a class="lineno" href="#L744">744</a></span>
<span id="L745" class="lineno"><a class="lineno" href="#L745">745</a></span>
<span id="L746" class="lineno"><a class="lineno" href="#L746">746</a></span>
<span id="L747" class="lineno"><a class="lineno" href="#L747">747</a></span>
<span id="L748" class="lineno"><a class="lineno" href="#L748">748</a></span>
<span id="L749" class="lineno"><a class="lineno" href="#L749">749</a></span>
<span id="L750" class="lineno"><a class="lineno" href="#L750">750</a></span>
<span id="L751" class="lineno"><a class="lineno" href="#L751">751</a></span>
<span id="L752" class="lineno"><a class="lineno" href="#L752">752</a></span>
<span id="L753" class="lineno"><a class="lineno" href="#L753">753</a></span>
<span id="L754" class="lineno"><a class="lineno" href="#L754">754</a></span>
<span id="L755" class="lineno"><a class="lineno" href="#L755">755</a></span>
<span id="L756" class="lineno"><a class="lineno" href="#L756">756</a></span>
<span id="L757" class="lineno"><a class="lineno" href="#L757">757</a></span>
<span id="L758" class="lineno"><a class="lineno" href="#L758">758</a></span>
<span id="L759" class="lineno"><a class="lineno" href="#L759">759</a></span>
<span id="L760" class="lineno"><a class="lineno" href="#L760">760</a></span>
<span id="L761" class="lineno"><a class="lineno" href="#L761">761</a></span>
<span id="L762" class="lineno"><a class="lineno" href="#L762">762</a></span>
<span id="L763" class="lineno"><a class="lineno" href="#L763">763</a></span>
<span id="L764" class="lineno"><a class="lineno" href="#L764">764</a></span>
<span id="L765" class="lineno"><a class="lineno" href="#L765">765</a></span>
<span id="L766" class="lineno"><a class="lineno" href="#L766">766</a></span>
<span id="L767" class="lineno"><a class="lineno" href="#L767">767</a></span>
<span id="L768" class="lineno"><a class="lineno" href="#L768">768</a></span>
<span id="L769" class="lineno"><a class="lineno" href="#L769">769</a></span>
<span id="L770" class="lineno"><a class="lineno" href="#L770">770</a></span>
<span id="L771" class="lineno"><a class="lineno" href="#L771">771</a></span>
<span id="L772" class="lineno"><a class="lineno" href="#L772">772</a></span>
<span id="L773" class="lineno"><a class="lineno" href="#L773">773</a></span>
<span id="L774" class="lineno"><a class="lineno" href="#L774">774</a></span>
<span id="L775" class="lineno"><a class="lineno" href="#L775">775</a></span>
<span id="L776" class="lineno"><a class="lineno" href="#L776">776</a></span>
<span id="L777" class="lineno"><a class="lineno" href="#L777">777</a></span>
<span id="L778" class="lineno"><a class="lineno" href="#L778">778</a></span>
<span id="L779" class="lineno"><a class="lineno" href="#L779">779</a></span>
<span id="L780" class="lineno"><a class="lineno" href="#L780">780</a></span>
<span id="L781" class="lineno"><a class="lineno" href="#L781">781</a></span>
<span id="L782" class="lineno"><a class="lineno" href="#L782">782</a></span>
<span id="L783" class="lineno"><a class="lineno" href="#L783">783</a></span>
<span id="L784" class="lineno"><a class="lineno" href="#L784">784</a></span>
<span id="L785" class="lineno"><a class="lineno" href="#L785">785</a></span>
<span id="L786" class="lineno"><a class="lineno" href="#L786">786</a></span>
<span id="L787" class="lineno"><a class="lineno" href="#L787">787</a></span>
<span id="L788" class="lineno"><a class="lineno" href="#L788">788</a></span>
<span id="L789" class="lineno"><a class="lineno" href="#L789">789</a></span>
<span id="L790" class="lineno"><a class="lineno" href="#L790">790</a></span>
<span id="L791" class="lineno"><a class="lineno" href="#L791">791</a></span>
<span id="L792" class="lineno"><a class="lineno" href="#L792">792</a></span>
<span id="L793" class="lineno"><a class="lineno" href="#L793">793</a></span>
<span id="L794" class="lineno"><a class="lineno" href="#L794">794</a></span>
<span id="L795" class="lineno"><a class="lineno" href="#L795">795</a></span>
<span id="L796" class="lineno"><a class="lineno" href="#L796">796</a></span>
<span id="L797" class="lineno"><a class="lineno" href="#L797">797</a></span>
<span id="L798" class="lineno"><a class="lineno" href="#L798">798</a></span>
<span id="L799" class="lineno"><a class="lineno" href="#L799">799</a></span>
<span id="L800" class="lineno"><a class="lineno" href="#L800">800</a></span>
<span id="L801" class="lineno"><a class="lineno" href="#L801">801</a></span>
<span id="L802" class="lineno"><a class="lineno" href="#L802">802</a></span>
<span id="L803" class="lineno"><a class="lineno" href="#L803">803</a></span>
<span id="L804" class="lineno"><a class="lineno" href="#L804">804</a></span>
<span id="L805" class="lineno"><a class="lineno" href="#L805">805</a></span>
<span id="L806" class="lineno"><a class="lineno" href="#L806">806</a></span>
<span id="L807" class="lineno"><a class="lineno" href="#L807">807</a></span>
<span id="L808" class="lineno"><a class="lineno" href="#L808">808</a></span>
<span id="L809" class="lineno"><a class="lineno" href="#L809">809</a></span>
<span id="L810" class="lineno"><a class="lineno" href="#L810">810</a></span>
<span id="L811" class="lineno"><a class="lineno" href="#L811">811</a></span>
<span id="L812" class="lineno"><a class="lineno" href="#L812">812</a></span>
<span id="L813" class="lineno"><a class="lineno" href="#L813">813</a></span>
<span id="L814" class="lineno"><a class="lineno" href="#L814">814</a></span>
<span id="L815" class="lineno"><a class="lineno" href="#L815">815</a></span>
<span id="L816" class="lineno"><a class="lineno" href="#L816">816</a></span>
<span id="L817" class="lineno"><a class="lineno" href="#L817">817</a></span>
<span id="L818" class="lineno"><a class="lineno" href="#L818">818</a></span>
<span id="L819" class="lineno"><a class="lineno" href="#L819">819</a></span>
<span id="L820" class="lineno"><a class="lineno" href="#L820">820</a></span>
<span id="L821" class="lineno"><a class="lineno" href="#L821">821</a></span>
<span id="L822" class="lineno"><a class="lineno" href="#L822">822</a></span>
<span id="L823" class="lineno"><a class="lineno" href="#L823">823</a></span>
<span id="L824" class="lineno"><a class="lineno" href="#L824">824</a></span>
<span id="L825" class="lineno"><a class="lineno" href="#L825">825</a></span>
<span id="L826" class="lineno"><a class="lineno" href="#L826">826</a></span>
<span id="L827" class="lineno"><a class="lineno" href="#L827">827</a></span>
<span id="L828" class="lineno"><a class="lineno" href="#L828">828</a></span>
<span id="L829" class="lineno"><a class="lineno" href="#L829">829</a></span>
<span id="L830" class="lineno"><a class="lineno" href="#L830">830</a></span>
<span id="L831" class="lineno"><a class="lineno" href="#L831">831</a></span>
<span id="L832" class="lineno"><a class="lineno" href="#L832">832</a></span>
<span id="L833" class="lineno"><a class="lineno" href="#L833">833</a></span>
<span id="L834" class="lineno"><a class="lineno" href="#L834">834</a></span>
<span id="L835" class="lineno"><a class="lineno" href="#L835">835</a></span>
<span id="L836" class="lineno"><a class="lineno" href="#L836">836</a></span>
<span id="L837" class="lineno"><a class="lineno" href="#L837">837</a></span>
<span id="L838" class="lineno"><a class="lineno" href="#L838">838</a></span>
<span id="L839" class="lineno"><a class="lineno" href="#L839">839</a></span>
<span id="L840" class="lineno"><a class="lineno" href="#L840">840</a></span>
<span id="L841" class="lineno"><a class="lineno" href="#L841">841</a></span>
<span id="L842" class="lineno"><a class="lineno" href="#L842">842</a></span>
<span id="L843" class="lineno"><a class="lineno" href="#L843">843</a></span>
<span id="L844" class="lineno"><a class="lineno" href="#L844">844</a></span>
<span id="L845" class="lineno"><a class="lineno" href="#L845">845</a></span>
<span id="L846" class="lineno"><a class="lineno" href="#L846">846</a></span>
<span id="L847" class="lineno"><a class="lineno" href="#L847">847</a></span>
<span id="L848" class="lineno"><a class="lineno" href="#L848">848</a></span>
<span id="L849" class="lineno"><a class="lineno" href="#L849">849</a></span>
<span id="L850" class="lineno"><a class="lineno" href="#L850">850</a></span>
<span id="L851" class="lineno"><a class="lineno" href="#L851">851</a></span>
<span id="L852" class="lineno"><a class="lineno" href="#L852">852</a></span>
<span id="L853" class="lineno"><a class="lineno" href="#L853">853</a></span>
<span id="L854" class="lineno"><a class="lineno" href="#L854">854</a></span>
<span id="L855" class="lineno"><a class="lineno" href="#L855">855</a></span>
<span id="L856" class="lineno"><a class="lineno" href="#L856">856</a></span>
<span id="L857" class="lineno"><a class="lineno" href="#L857">857</a></span>
<span id="L858" class="lineno"><a class="lineno" href="#L858">858</a></span>
<span id="L859" class="lineno"><a class="lineno" href="#L859">859</a></span>
<span id="L860" class="lineno"><a class="lineno" href="#L860">860</a></span>
<span id="L861" class="lineno"><a class="lineno" href="#L861">861</a></span>
<span id="L862" class="lineno"><a class="lineno" href="#L862">862</a></span>
<span id="L863" class="lineno"><a class="lineno" href="#L863">863</a></span>
<span id="L864" class="lineno"><a class="lineno" href="#L864">864</a></span>
<span id="L865" class="lineno"><a class="lineno" href="#L865">865</a></span>
<span id="L866" class="lineno"><a class="lineno" href="#L866">866</a></span>
<span id="L867" class="lineno"><a class="lineno" href="#L867">867</a></span>
<span id="L868" class="lineno"><a class="lineno" href="#L868">868</a></span>
<span id="L869" class="lineno"><a class="lineno" href="#L869">869</a></span>
<span id="L870" class="lineno"><a class="lineno" href="#L870">870</a></span>
<span id="L871" class="lineno"><a class="lineno" href="#L871">871</a></span>
<span id="L872" class="lineno"><a class="lineno" href="#L872">872</a></span>
<span id="L873" class="lineno"><a class="lineno" href="#L873">873</a></span>
<span id="L874" class="lineno"><a class="lineno" href="#L874">874</a></span>
<span id="L875" class="lineno"><a class="lineno" href="#L875">875</a></span>
<span id="L876" class="lineno"><a class="lineno" href="#L876">876</a></span>
<span id="L877" class="lineno"><a class="lineno" href="#L877">877</a></span>
<span id="L878" class="lineno"><a class="lineno" href="#L878">878</a></span>
<span id="L879" class="lineno"><a class="lineno" href="#L879">879</a></span>
<span id="L880" class="lineno"><a class="lineno" href="#L880">880</a></span>
<span id="L881" class="lineno"><a class="lineno" href="#L881">881</a></span>
<span id="L882" class="lineno"><a class="lineno" href="#L882">882</a></span>
<span id="L883" class="lineno"><a class="lineno" href="#L883">883</a></span>
<span id="L884" class="lineno"><a class="lineno" href="#L884">884</a></span>
<span id="L885" class="lineno"><a class="lineno" href="#L885">885</a></span>
<span id="L886" class="lineno"><a class="lineno" href="#L886">886</a></span>
<span id="L887" class="lineno"><a class="lineno" href="#L887">887</a></span>
<span id="L888" class="lineno"><a class="lineno" href="#L888">888</a></span>
<span id="L889" class="lineno"><a class="lineno" href="#L889">889</a></span>
<span id="L890" class="lineno"><a class="lineno" href="#L890">890</a></span>
<span id="L891" class="lineno"><a class="lineno" href="#L891">891</a></span>
<span id="L892" class="lineno"><a class="lineno" href="#L892">892</a></span>
<span id="L893" class="lineno"><a class="lineno" href="#L893">893</a></span>
<span id="L894" class="lineno"><a class="lineno" href="#L894">894</a></span>
<span id="L895" class="lineno"><a class="lineno" href="#L895">895</a></span>
<span id="L896" class="lineno"><a class="lineno" href="#L896">896</a></span>
<span id="L897" class="lineno"><a class="lineno" href="#L897">897</a></span>
<span id="L898" class="lineno"><a class="lineno" href="#L898">898</a></span>
<span id="L899" class="lineno"><a class="lineno" href="#L899">899</a></span>
<span id="L900" class="lineno"><a class="lineno" href="#L900">900</a></span>
<span id="L901" class="lineno"><a class="lineno" href="#L901">901</a></span>
<span id="L902" class="lineno"><a class="lineno" href="#L902">902</a></span>
<span id="L903" class="lineno"><a class="lineno" href="#L903">903</a></span>
<span id="L904" class="lineno"><a class="lineno" href="#L904">904</a></span>
<span id="L905" class="lineno"><a class="lineno" href="#L905">905</a></span>
<span id="L906" class="lineno"><a class="lineno" href="#L906">906</a></span>
<span id="L907" class="lineno"><a class="lineno" href="#L907">907</a></span>
<span id="L908" class="lineno"><a class="lineno" href="#L908">908</a></span>
<span id="L909" class="lineno"><a class="lineno" href="#L909">909</a></span>
<span id="L910" class="lineno"><a class="lineno" href="#L910">910</a></span>
<span id="L911" class="lineno"><a class="lineno" href="#L911">911</a></span>
<span id="L912" class="lineno"><a class="lineno" href="#L912">912</a></span>
<span id="L913" class="lineno"><a class="lineno" href="#L913">913</a></span>
<span id="L914" class="lineno"><a class="lineno" href="#L914">914</a></span>
<span id="L915" class="lineno"><a class="lineno" href="#L915">915</a></span>
<span id="L916" class="lineno"><a class="lineno" href="#L916">916</a></span>
<span id="L917" class="lineno"><a class="lineno" href="#L917">917</a></span>
<span id="L918" class="lineno"><a class="lineno" href="#L918">918</a></span>
<span id="L919" class="lineno"><a class="lineno" href="#L919">919</a></span>
<span id="L920" class="lineno"><a class="lineno" href="#L920">920</a></span>
<span id="L921" class="lineno"><a class="lineno" href="#L921">921</a></span>
<span id="L922" class="lineno"><a class="lineno" href="#L922">922</a></span>
<span id="L923" class="lineno"><a class="lineno" href="#L923">923</a></span>
<span id="L924" class="lineno"><a class="lineno" href="#L924">924</a></span>
<span id="L925" class="lineno"><a class="lineno" href="#L925">925</a></span>
<span id="L926" class="lineno"><a class="lineno" href="#L926">926</a></span>
<span id="L927" class="lineno"><a class="lineno" href="#L927">927</a></span>
<span id="L928" class="lineno"><a class="lineno" href="#L928">928</a></span>
<span id="L929" class="lineno"><a class="lineno" href="#L929">929</a></span>
<span id="L930" class="lineno"><a class="lineno" href="#L930">930</a></span>
<span id="L931" class="lineno"><a class="lineno" href="#L931">931</a></span>
<span id="L932" class="lineno"><a class="lineno" href="#L932">932</a></span>
<span id="L933" class="lineno"><a class="lineno" href="#L933">933</a></span>
<span id="L934" class="lineno"><a class="lineno" href="#L934">934</a></span>
<span id="L935" class="lineno"><a class="lineno" href="#L935">935</a></span>
<span id="L936" class="lineno"><a class="lineno" href="#L936">936</a></span>
<span id="L937" class="lineno"><a class="lineno" href="#L937">937</a></span>
<span id="L938" class="lineno"><a class="lineno" href="#L938">938</a></span>
<span id="L939" class="lineno"><a class="lineno" href="#L939">939</a></span>
<span id="L940" class="lineno"><a class="lineno" href="#L940">940</a></span>
<span id="L941" class="lineno"><a class="lineno" href="#L941">941</a></span>
<span id="L942" class="lineno"><a class="lineno" href="#L942">942</a></span>
<span id="L943" class="lineno"><a class="lineno" href="#L943">943</a></span>
<span id="L944" class="lineno"><a class="lineno" href="#L944">944</a></span>
<span id="L945" class="lineno"><a class="lineno" href="#L945">945</a></span>
<span id="L946" class="lineno"><a class="lineno" href="#L946">946</a></span>
<span id="L947" class="lineno"><a class="lineno" href="#L947">947</a></span>
<span id="L948" class="lineno"><a class="lineno" href="#L948">948</a></span>
<span id="L949" class="lineno"><a class="lineno" href="#L949">949</a></span>
<span id="L950" class="lineno"><a class="lineno" href="#L950">950</a></span>
<span id="L951" class="lineno"><a class="lineno" href="#L951">951</a></span>
<span id="L952" class="lineno"><a class="lineno" href="#L952">952</a></span>
<span id="L953" class="lineno"><a class="lineno" href="#L953">953</a></span>
<span id="L954" class="lineno"><a class="lineno" href="#L954">954</a></span>
<span id="L955" class="lineno"><a class="lineno" href="#L955">955</a></span>
<span id="L956" class="lineno"><a class="lineno" href="#L956">956</a></span>
<span id="L957" class="lineno"><a class="lineno" href="#L957">957</a></span>
<span id="L958" class="lineno"><a class="lineno" href="#L958">958</a></span>
<span id="L959" class="lineno"><a class="lineno" href="#L959">959</a></span>
<span id="L960" class="lineno"><a class="lineno" href="#L960">960</a></span>
<span id="L961" class="lineno"><a class="lineno" href="#L961">961</a></span>
<span id="L962" class="lineno"><a class="lineno" href="#L962">962</a></span>
<span id="L963" class="lineno"><a class="lineno" href="#L963">963</a></span>
<span id="L964" class="lineno"><a class="lineno" href="#L964">964</a></span>
<span id="L965" class="lineno"><a class="lineno" href="#L965">965</a></span>
<span id="L966" class="lineno"><a class="lineno" href="#L966">966</a></span>
<span id="L967" class="lineno"><a class="lineno" href="#L967">967</a></span>
<span id="L968" class="lineno"><a class="lineno" href="#L968">968</a></span>
<span id="L969" class="lineno"><a class="lineno" href="#L969">969</a></span>
<span id="L970" class="lineno"><a class="lineno" href="#L970">970</a></span>
<span id="L971" class="lineno"><a class="lineno" href="#L971">971</a></span>
<span id="L972" class="lineno"><a class="lineno" href="#L972">972</a></span>
<span id="L973" class="lineno"><a class="lineno" href="#L973">973</a></span>
<span id="L974" class="lineno"><a class="lineno" href="#L974">974</a></span>
<span id="L975" class="lineno"><a class="lineno" href="#L975">975</a></span>
<span id="L976" class="lineno"><a class="lineno" href="#L976">976</a></span>
<span id="L977" class="lineno"><a class="lineno" href="#L977">977</a></span>
<span id="L978" class="lineno"><a class="lineno" href="#L978">978</a></span>
<span id="L979" class="lineno"><a class="lineno" href="#L979">979</a></span>
<span id="L980" class="lineno"><a class="lineno" href="#L980">980</a></span>
<span id="L981" class="lineno"><a class="lineno" href="#L981">981</a></span>
<span id="L982" class="lineno"><a class="lineno" href="#L982">982</a></span>
<span id="L983" class="lineno"><a class="lineno" href="#L983">983</a></span>
<span id="L984" class="lineno"><a class="lineno" href="#L984">984</a></span>
<span id="L985" class="lineno"><a class="lineno" href="#L985">985</a></span>
<span id="L986" class="lineno"><a class="lineno" href="#L986">986</a></span>
<span id="L987" class="lineno"><a class="lineno" href="#L987">987</a></span>
<span id="L988" class="lineno"><a class="lineno" href="#L988">988</a></span>
<span id="L989" class="lineno"><a class="lineno" href="#L989">989</a></span>
<span id="L990" class="lineno"><a class="lineno" href="#L990">990</a></span>
<span id="L991" class="lineno"><a class="lineno" href="#L991">991</a></span>
<span id="L992" class="lineno"><a class="lineno" href="#L992">992</a></span>
<span id="L993" class="lineno"><a class="lineno" href="#L993">993</a></span>
<span id="L994" class="lineno"><a class="lineno" href="#L994">994</a></span>
<span id="L995" class="lineno"><a class="lineno" href="#L995">995</a></span>
<span id="L996" class="lineno"><a class="lineno" href="#L996">996</a></span>
<span id="L997" class="lineno"><a class="lineno" href="#L997">997</a></span>
<span id="L998" class="lineno"><a class="lineno" href="#L998">998</a></span>
<span id="L999" class="lineno"><a class="lineno" href="#L999">999</a></span>
<span id="L1000" class="lineno"><a class="lineno" href="#L1000">1000</a></span>
<span id="L1001" class="lineno"><a class="lineno" href="#L1001">1001</a></span>
<span id="L1002" class="lineno"><a class="lineno" href="#L1002">1002</a></span>
<span id="L1003" class="lineno"><a class="lineno" href="#L1003">1003</a></span>
<span id="L1004" class="lineno"><a class="lineno" href="#L1004">1004</a></span>
<span id="L1005" class="lineno"><a class="lineno" href="#L1005">1005</a></span>
<span id="L1006" class="lineno"><a class="lineno" href="#L1006">1006</a></span>
<span id="L1007" class="lineno"><a class="lineno" href="#L1007">1007</a></span>
<span id="L1008" class="lineno"><a class="lineno" href="#L1008">1008</a></span>
<span id="L1009" class="lineno"><a class="lineno" href="#L1009">1009</a></span>
<span id="L1010" class="lineno"><a class="lineno" href="#L1010">1010</a></span>
<span id="L1011" class="lineno"><a class="lineno" href="#L1011">1011</a></span>
<span id="L1012" class="lineno"><a class="lineno" href="#L1012">1012</a></span>
<span id="L1013" class="lineno"><a class="lineno" href="#L1013">1013</a></span>
<span id="L1014" class="lineno"><a class="lineno" href="#L1014">1014</a></span>
<span id="L1015" class="lineno"><a class="lineno" href="#L1015">1015</a></span>
<span id="L1016" class="lineno"><a class="lineno" href="#L1016">1016</a></span>
<span id="L1017" class="lineno"><a class="lineno" href="#L1017">1017</a></span>
<span id="L1018" class="lineno"><a class="lineno" href="#L1018">1018</a></span>
<span id="L1019" class="lineno"><a class="lineno" href="#L1019">1019</a></span>
<span id="L1020" class="lineno"><a class="lineno" href="#L1020">1020</a></span>
<span id="L1021" class="lineno"><a class="lineno" href="#L1021">1021</a></span>
<span id="L1022" class="lineno"><a class="lineno" href="#L1022">1022</a></span>
<span id="L1023" class="lineno"><a class="lineno" href="#L1023">1023</a></span>
<span id="L1024" class="lineno"><a class="lineno" href="#L1024">1024</a></span>
<span id="L1025" class="lineno"><a class="lineno" href="#L1025">1025</a></span>
<span id="L1026" class="lineno"><a class="lineno" href="#L1026">1026</a></span>
<span id="L1027" class="lineno"><a class="lineno" href="#L1027">1027</a></span>
<span id="L1028" class="lineno"><a class="lineno" href="#L1028">1028</a></span>
<span id="L1029" class="lineno"><a class="lineno" href="#L1029">1029</a></span>
<span id="L1030" class="lineno"><a class="lineno" href="#L1030">1030</a></span>
<span id="L1031" class="lineno"><a class="lineno" href="#L1031">1031</a></span>
<span id="L1032" class="lineno"><a class="lineno" href="#L1032">1032</a></span>
<span id="L1033" class="lineno"><a class="lineno" href="#L1033">1033</a></span>
<span id="L1034" class="lineno"><a class="lineno" href="#L1034">1034</a></span>
<span id="L1035" class="lineno"><a class="lineno" href="#L1035">1035</a></span>
<span id="L1036" class="lineno"><a class="lineno" href="#L1036">1036</a></span>
<span id="L1037" class="lineno"><a class="lineno" href="#L1037">1037</a></span>
<span id="L1038" class="lineno"><a class="lineno" href="#L1038">1038</a></span>
<span id="L1039" class="lineno"><a class="lineno" href="#L1039">1039</a></span>
<span id="L1040" class="lineno"><a class="lineno" href="#L1040">1040</a></span>
<span id="L1041" class="lineno"><a class="lineno" href="#L1041">1041</a></span>
<span id="L1042" class="lineno"><a class="lineno" href="#L1042">1042</a></span>
<span id="L1043" class="lineno"><a class="lineno" href="#L1043">1043</a></span>
<span id="L1044" class="lineno"><a class="lineno" href="#L1044">1044</a></span>
<span id="L1045" class="lineno"><a class="lineno" href="#L1045">1045</a></span>
<span id="L1046" class="lineno"><a class="lineno" href="#L1046">1046</a></span>
<span id="L1047" class="lineno"><a class="lineno" href="#L1047">1047</a></span>
<span id="L1048" class="lineno"><a class="lineno" href="#L1048">1048</a></span>
<span id="L1049" class="lineno"><a class="lineno" href="#L1049">1049</a></span>
<span id="L1050" class="lineno"><a class="lineno" href="#L1050">1050</a></span>
<span id="L1051" class="lineno"><a class="lineno" href="#L1051">1051</a></span>
<span id="L1052" class="lineno"><a class="lineno" href="#L1052">1052</a></span>
<span id="L1053" class="lineno"><a class="lineno" href="#L1053">1053</a></span>
<span id="L1054" class="lineno"><a class="lineno" href="#L1054">1054</a></span>
<span id="L1055" class="lineno"><a class="lineno" href="#L1055">1055</a></span>
<span id="L1056" class="lineno"><a class="lineno" href="#L1056">1056</a></span>
<span id="L1057" class="lineno"><a class="lineno" href="#L1057">1057</a></span>
<span id="L1058" class="lineno"><a class="lineno" href="#L1058">1058</a></span>
<span id="L1059" class="lineno"><a class="lineno" href="#L1059">1059</a></span>
<span id="L1060" class="lineno"><a class="lineno" href="#L1060">1060</a></span>
<span id="L1061" class="lineno"><a class="lineno" href="#L1061">1061</a></span>
<span id="L1062" class="lineno"><a class="lineno" href="#L1062">1062</a></span>
<span id="L1063" class="lineno"><a class="lineno" href="#L1063">1063</a></span>
<span id="L1064" class="lineno"><a class="lineno" href="#L1064">1064</a></span>
<span id="L1065" class="lineno"><a class="lineno" href="#L1065">1065</a></span>
<span id="L1066" class="lineno"><a class="lineno" href="#L1066">1066</a></span>
<span id="L1067" class="lineno"><a class="lineno" href="#L1067">1067</a></span>
<span id="L1068" class="lineno"><a class="lineno" href="#L1068">1068</a></span>
<span id="L1069" class="lineno"><a class="lineno" href="#L1069">1069</a></span>
<span id="L1070" class="lineno"><a class="lineno" href="#L1070">1070</a></span>
<span id="L1071" class="lineno"><a class="lineno" href="#L1071">1071</a></span>
<span id="L1072" class="lineno"><a class="lineno" href="#L1072">1072</a></span>
<span id="L1073" class="lineno"><a class="lineno" href="#L1073">1073</a></span>
<span id="L1074" class="lineno"><a class="lineno" href="#L1074">1074</a></span>
<span id="L1075" class="lineno"><a class="lineno" href="#L1075">1075</a></span>
<span id="L1076" class="lineno"><a class="lineno" href="#L1076">1076</a></span>
<span id="L1077" class="lineno"><a class="lineno" href="#L1077">1077</a></span>
<span id="L1078" class="lineno"><a class="lineno" href="#L1078">1078</a></span>
<span id="L1079" class="lineno"><a class="lineno" href="#L1079">1079</a></span>
<span id="L1080" class="lineno"><a class="lineno" href="#L1080">1080</a></span>
<span id="L1081" class="lineno"><a class="lineno" href="#L1081">1081</a></span>
<span id="L1082" class="lineno"><a class="lineno" href="#L1082">1082</a></span>
<span id="L1083" class="lineno"><a class="lineno" href="#L1083">1083</a></span>
<span id="L1084" class="lineno"><a class="lineno" href="#L1084">1084</a></span>
<span id="L1085" class="lineno"><a class="lineno" href="#L1085">1085</a></span>
<span id="L1086" class="lineno"><a class="lineno" href="#L1086">1086</a></span>
<span id="L1087" class="lineno"><a class="lineno" href="#L1087">1087</a></span>
<span id="L1088" class="lineno"><a class="lineno" href="#L1088">1088</a></span>
<span id="L1089" class="lineno"><a class="lineno" href="#L1089">1089</a></span>
<span id="L1090" class="lineno"><a class="lineno" href="#L1090">1090</a></span>
<span id="L1091" class="lineno"><a class="lineno" href="#L1091">1091</a></span>
<span id="L1092" class="lineno"><a class="lineno" href="#L1092">1092</a></span>
<span id="L1093" class="lineno"><a class="lineno" href="#L1093">1093</a></span>
<span id="L1094" class="lineno"><a class="lineno" href="#L1094">1094</a></span>
<span id="L1095" class="lineno"><a class="lineno" href="#L1095">1095</a></span>
<span id="L1096" class="lineno"><a class="lineno" href="#L1096">1096</a></span>
<span id="L1097" class="lineno"><a class="lineno" href="#L1097">1097</a></span>
<span id="L1098" class="lineno"><a class="lineno" href="#L1098">1098</a></span>
<span id="L1099" class="lineno"><a class="lineno" href="#L1099">1099</a></span>
<span id="L1100" class="lineno"><a class="lineno" href="#L1100">1100</a></span>
<span id="L1101" class="lineno"><a class="lineno" href="#L1101">1101</a></span>
<span id="L1102" class="lineno"><a class="lineno" href="#L1102">1102</a></span>
<span id="L1103" class="lineno"><a class="lineno" href="#L1103">1103</a></span>
<span id="L1104" class="lineno"><a class="lineno" href="#L1104">1104</a></span>
<span id="L1105" class="lineno"><a class="lineno" href="#L1105">1105</a></span>
<span id="L1106" class="lineno"><a class="lineno" href="#L1106">1106</a></span>
<span id="L1107" class="lineno"><a class="lineno" href="#L1107">1107</a></span>
<span id="L1108" class="lineno"><a class="lineno" href="#L1108">1108</a></span>
<span id="L1109" class="lineno"><a class="lineno" href="#L1109">1109</a></span>
</pre></td>
<td class="table-code"><pre><span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!">Budget Management Agent for Google Ads Campaign Optimization.</span>
<span class="line-empty" title="No Anys on this line!">Handles budget allocation, pacing, and optimization across campaigns and accounts.</span>
<span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">import asyncio</span>
<span class="line-precise" title="No Anys on this line!">import json</span>
<span class="line-precise" title="No Anys on this line!">from datetime import datetime, timedelta</span>
<span class="line-precise" title="No Anys on this line!">from typing import Any, Dict, List, Optional, Tuple, Union</span>
<span class="line-precise" title="No Anys on this line!">from dataclasses import dataclass, field</span>
<span class="line-precise" title="No Anys on this line!">from enum import Enum</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="No Anys on this line!">import structlog</span>
<span class="line-any" title="No Anys on this line!">import numpy as np</span>
<span class="line-any" title="No Anys on this line!">from crewai import Agent, Task</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from ..base import BaseAiLexAgent, AgentContext, AgentError</span>
<span class="line-precise" title="No Anys on this line!">from ..tracing import AgentTracer, create_agent_tracer</span>
<span class="line-precise" title="No Anys on this line!">from models.agents import AgentType, AgentConfig</span>
<span class="line-precise" title="No Anys on this line!">from services.google_ads import GoogleAdsService</span>
<span class="line-precise" title="No Anys on this line!">from services.openai_service import OpenAIService</span>
<span class="line-precise" title="No Anys on this line!">from utils.config import settings</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="Any Types on this line: 
Unimported (x4)">logger = structlog.get_logger(__name__)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class BudgetType(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """Types of budget configurations."""</span>
<span class="line-precise" title="No Anys on this line!">    DAILY = "daily"</span>
<span class="line-precise" title="No Anys on this line!">    MONTHLY = "monthly"</span>
<span class="line-precise" title="No Anys on this line!">    CAMPAIGN_TOTAL = "campaign_total"</span>
<span class="line-precise" title="No Anys on this line!">    SHARED = "shared"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class BudgetStrategy(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """Budget allocation strategies."""</span>
<span class="line-precise" title="No Anys on this line!">    EQUAL_DISTRIBUTION = "equal_distribution"</span>
<span class="line-precise" title="No Anys on this line!">    PERFORMANCE_BASED = "performance_based"</span>
<span class="line-precise" title="No Anys on this line!">    PRIORITY_BASED = "priority_based"</span>
<span class="line-precise" title="No Anys on this line!">    SEASONAL_ADJUSTED = "seasonal_adjusted"</span>
<span class="line-precise" title="No Anys on this line!">    ROI_OPTIMIZED = "roi_optimized"</span>
<span class="line-precise" title="No Anys on this line!">    COMPETITIVE_RESPONSE = "competitive_response"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class SpendPacing(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """Campaign spend pacing status."""</span>
<span class="line-precise" title="No Anys on this line!">    UNDER_PACING = "under_pacing"</span>
<span class="line-precise" title="No Anys on this line!">    ON_PACE = "on_pace"</span>
<span class="line-precise" title="No Anys on this line!">    OVER_PACING = "over_pacing"</span>
<span class="line-precise" title="No Anys on this line!">    BUDGET_LIMITED = "budget_limited"</span>
<span class="line-precise" title="No Anys on this line!">    ACCELERATED = "accelerated"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@dataclass</span>
<span class="line-precise" title="No Anys on this line!">class BudgetAllocation:</span>
<span class="line-empty" title="No Anys on this line!">    """Budget allocation recommendation."""</span>
<span class="line-precise" title="No Anys on this line!">    allocation_id: str</span>
<span class="line-precise" title="No Anys on this line!">    campaign_id: str</span>
<span class="line-precise" title="No Anys on this line!">    campaign_name: str</span>
<span class="line-precise" title="No Anys on this line!">    current_budget: float</span>
<span class="line-precise" title="No Anys on this line!">    recommended_budget: float</span>
<span class="line-precise" title="No Anys on this line!">    budget_change_percentage: float</span>
<span class="line-precise" title="No Anys on this line!">    budget_type: BudgetType</span>
<span class="line-precise" title="No Anys on this line!">    rationale: str</span>
<span class="line-precise" title="No Anys on this line!">    expected_impact: Dict[str, float]</span>
<span class="line-precise" title="No Anys on this line!">    confidence_score: float</span>
<span class="line-precise" title="No Anys on this line!">    priority: str  # critical, high, medium, low</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)
Explicit (x7)">    effective_date: datetime = field(default_factory=datetime.utcnow)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)
Explicit (x7)">    created_at: datetime = field(default_factory=datetime.utcnow)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@dataclass</span>
<span class="line-precise" title="No Anys on this line!">class SpendAnalysis:</span>
<span class="line-empty" title="No Anys on this line!">    """Campaign spend analysis and pacing information."""</span>
<span class="line-precise" title="No Anys on this line!">    campaign_id: str</span>
<span class="line-precise" title="No Anys on this line!">    campaign_name: str</span>
<span class="line-precise" title="No Anys on this line!">    budget_period_start: datetime</span>
<span class="line-precise" title="No Anys on this line!">    budget_period_end: datetime</span>
<span class="line-precise" title="No Anys on this line!">    total_budget: float</span>
<span class="line-precise" title="No Anys on this line!">    spent_to_date: float</span>
<span class="line-precise" title="No Anys on this line!">    remaining_budget: float</span>
<span class="line-precise" title="No Anys on this line!">    spend_percentage: float</span>
<span class="line-precise" title="No Anys on this line!">    days_elapsed: int</span>
<span class="line-precise" title="No Anys on this line!">    days_remaining: int</span>
<span class="line-precise" title="No Anys on this line!">    expected_spend_percentage: float</span>
<span class="line-precise" title="No Anys on this line!">    pacing_status: SpendPacing</span>
<span class="line-precise" title="No Anys on this line!">    daily_spend_average: float</span>
<span class="line-precise" title="No Anys on this line!">    projected_end_spend: float</span>
<span class="line-precise" title="No Anys on this line!">    impression_share_lost_budget: Optional[float] = None</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x7)
Explicit (x7)">    recommendations: List[str] = field(default_factory=list)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@dataclass</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class BudgetOptimizationGoal:</span>
<span class="line-empty" title="No Anys on this line!">    """Budget optimization goal configuration."""</span>
<span class="line-precise" title="No Anys on this line!">    goal_id: str</span>
<span class="line-precise" title="No Anys on this line!">    goal_type: str  # maximize_conversions, target_cpa, target_roas, etc.</span>
<span class="line-precise" title="No Anys on this line!">    target_value: Optional[float] = None</span>
<span class="line-precise" title="No Anys on this line!">    priority_weight: float = 1.0</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    constraints: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x7)
Explicit (x7)">    success_metrics: List[str] = field(default_factory=list)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@dataclass</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x8)">class BudgetRecommendationReport:</span>
<span class="line-empty" title="No Anys on this line!">    """Comprehensive budget management report."""</span>
<span class="line-precise" title="No Anys on this line!">    report_id: str</span>
<span class="line-precise" title="No Anys on this line!">    account_id: str</span>
<span class="line-precise" title="No Anys on this line!">    analysis_period: Tuple[datetime, datetime]</span>
<span class="line-precise" title="No Anys on this line!">    total_budget_analyzed: float</span>
<span class="line-precise" title="No Anys on this line!">    current_allocations: List[BudgetAllocation]</span>
<span class="line-precise" title="No Anys on this line!">    recommended_allocations: List[BudgetAllocation]</span>
<span class="line-precise" title="No Anys on this line!">    spend_analysis: List[SpendAnalysis]</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    optimization_opportunities: List[Dict[str, Any]]</span>
<span class="line-precise" title="No Anys on this line!">    projected_performance: Dict[str, float]</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    risk_assessment: Dict[str, Any]</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    implementation_timeline: Dict[str, Any]</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    monitoring_plan: Dict[str, Any]</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)
Explicit (x7)">    created_at: datetime = field(default_factory=datetime.utcnow)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class BudgetManagementAgent(BaseAiLexAgent):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    AI agent specialized in Google Ads budget management, allocation, and optimization.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __init__(self, agent_id: str, config: AgentConfig):</span>
<span class="line-precise" title="No Anys on this line!">        super().__init__(</span>
<span class="line-precise" title="No Anys on this line!">            agent_id=agent_id,</span>
<span class="line-precise" title="No Anys on this line!">            name="Budget Management Agent",</span>
<span class="line-precise" title="No Anys on this line!">            description="Specialized AI agent for Google Ads budget allocation, spend optimization, and budget pacing management",</span>
<span class="line-precise" title="No Anys on this line!">            agent_type=AgentType.BUDGET_MANAGEMENT,</span>
<span class="line-precise" title="No Anys on this line!">            config=config</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Initialize services</span>
<span class="line-precise" title="No Anys on this line!">        self.google_ads_service: Optional[GoogleAdsService] = None</span>
<span class="line-precise" title="No Anys on this line!">        self.openai_service: Optional[OpenAIService] = None</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Initialize tracer</span>
<span class="line-precise" title="No Anys on this line!">        self.tracer = create_agent_tracer(self.agent_id, self.name)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Budget management parameters</span>
<span class="line-precise" title="No Anys on this line!">        self.budget_thresholds = {</span>
<span class="line-precise" title="No Anys on this line!">            "min_daily_budget": 10.0,</span>
<span class="line-precise" title="No Anys on this line!">            "max_budget_increase_percentage": 100.0,</span>
<span class="line-precise" title="No Anys on this line!">            "max_budget_decrease_percentage": 50.0,</span>
<span class="line-precise" title="No Anys on this line!">            "pacing_tolerance": 0.1,  # 10% tolerance for pacing</span>
<span class="line-precise" title="No Anys on this line!">            "impression_share_threshold": 0.8,</span>
<span class="line-precise" title="No Anys on this line!">            "min_spend_history_days": 14,</span>
<span class="line-precise" title="No Anys on this line!">            "statistical_significance_threshold": 0.95</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Performance evaluation weights</span>
<span class="line-precise" title="No Anys on this line!">        self.performance_weights = {</span>
<span class="line-precise" title="No Anys on this line!">            "roas": 0.3,</span>
<span class="line-precise" title="No Anys on this line!">            "conversion_rate": 0.25,</span>
<span class="line-precise" title="No Anys on this line!">            "cost_per_conversion": 0.2,</span>
<span class="line-precise" title="No Anys on this line!">            "click_through_rate": 0.15,</span>
<span class="line-precise" title="No Anys on this line!">            "impression_share": 0.1</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Seasonal adjustment factors</span>
<span class="line-precise" title="No Anys on this line!">        self.seasonal_factors = {</span>
<span class="line-precise" title="No Anys on this line!">            "retail": {</span>
<span class="line-precise" title="No Anys on this line!">                "Q1": 0.85, "Q2": 0.95, "Q3": 0.90, "Q4": 1.30</span>
<span class="line-empty" title="No Anys on this line!">            },</span>
<span class="line-precise" title="No Anys on this line!">            "b2b": {</span>
<span class="line-precise" title="No Anys on this line!">                "Q1": 1.05, "Q2": 1.00, "Q3": 0.85, "Q4": 1.10</span>
<span class="line-empty" title="No Anys on this line!">            },</span>
<span class="line-precise" title="No Anys on this line!">            "default": {</span>
<span class="line-precise" title="No Anys on this line!">                "Q1": 0.95, "Q2": 1.00, "Q3": 0.95, "Q4": 1.10</span>
<span class="line-empty" title="No Anys on this line!">            }</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Data storage</span>
<span class="line-precise" title="No Anys on this line!">        self.budget_cache: Dict[str, float] = {}</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">        self.performance_cache: Dict[str, Dict[str, Any]] = {}</span>
<span class="line-precise" title="No Anys on this line!">        self.allocation_history: List[BudgetAllocation] = []</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _custom_initialize(self) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Custom initialization for budget management agent."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-empty" title="No Anys on this line!">            # Initialize Google Ads service</span>
<span class="line-precise" title="No Anys on this line!">            if all([</span>
<span class="line-precise" title="No Anys on this line!">                settings.GOOGLE_ADS_DEVELOPER_TOKEN,</span>
<span class="line-precise" title="No Anys on this line!">                settings.GOOGLE_ADS_CLIENT_ID,</span>
<span class="line-precise" title="No Anys on this line!">                settings.GOOGLE_ADS_CLIENT_SECRET,</span>
<span class="line-precise" title="No Anys on this line!">                settings.GOOGLE_ADS_REFRESH_TOKEN</span>
<span class="line-empty" title="No Anys on this line!">            ]):</span>
<span class="line-precise" title="No Anys on this line!">                self.google_ads_service = GoogleAdsService()</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Initialize OpenAI service</span>
<span class="line-precise" title="No Anys on this line!">            if settings.OPENAI_API_KEY:</span>
<span class="line-precise" title="No Anys on this line!">                self.openai_service = OpenAIService()</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Budget management agent initialized",</span>
<span class="line-precise" title="No Anys on this line!">                has_google_ads=bool(self.google_ads_service),</span>
<span class="line-precise" title="No Anys on this line!">                has_openai=bool(self.openai_service),</span>
<span class="line-precise" title="No Anys on this line!">                budget_thresholds=len(self.budget_thresholds)</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-precise" title="No Anys on this line!">            raise AgentError(f"Failed to initialize budget management agent: {str(e)}")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def optimize_budget_allocation(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        account_id: str,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_ids: List[str],</span>
<span class="line-empty" title="No Anys on this line!">        total_budget: float,</span>
<span class="line-empty" title="No Anys on this line!">        optimization_goals: List[BudgetOptimizationGoal],</span>
<span class="line-precise" title="No Anys on this line!">        strategy: BudgetStrategy = BudgetStrategy.PERFORMANCE_BASED,</span>
<span class="line-precise" title="No Anys on this line!">        analysis_period_days: int = 30</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; BudgetRecommendationReport:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Optimize budget allocation across campaigns based on performance and goals.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            account_id: Google Ads account ID</span>
<span class="line-empty" title="No Anys on this line!">            campaign_ids: Campaigns to optimize budget for</span>
<span class="line-empty" title="No Anys on this line!">            total_budget: Total budget to allocate</span>
<span class="line-empty" title="No Anys on this line!">            optimization_goals: Budget optimization objectives</span>
<span class="line-empty" title="No Anys on this line!">            strategy: Budget allocation strategy</span>
<span class="line-empty" title="No Anys on this line!">            analysis_period_days: Days of historical data to analyze</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            BudgetRecommendationReport: Comprehensive budget optimization report</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x2)
Explicit (x1)">        async with self.tracer.trace_task_execution(</span>
<span class="line-precise" title="No Anys on this line!">            task_id=f"optimize_budget_{account_id}_{hash(str(campaign_ids))}",</span>
<span class="line-precise" title="No Anys on this line!">            task_name="Budget Allocation Optimization",</span>
<span class="line-empty" title="No Anys on this line!">            task_data={</span>
<span class="line-precise" title="No Anys on this line!">                "account_id": account_id,</span>
<span class="line-precise" title="No Anys on this line!">                "campaign_count": len(campaign_ids),</span>
<span class="line-precise" title="No Anys on this line!">                "total_budget": total_budget,</span>
<span class="line-precise" title="No Anys on this line!">                "strategy": strategy.value</span>
<span class="line-empty" title="No Anys on this line!">            }</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">        ) as span:</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                    "Starting budget allocation optimization",</span>
<span class="line-precise" title="No Anys on this line!">                    account_id=account_id,</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_count=len(campaign_ids),</span>
<span class="line-precise" title="No Anys on this line!">                    total_budget=total_budget,</span>
<span class="line-precise" title="No Anys on this line!">                    strategy=strategy.value</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Define analysis period</span>
<span class="line-precise" title="No Anys on this line!">                end_date = datetime.utcnow()</span>
<span class="line-precise" title="No Anys on this line!">                start_date = end_date - timedelta(days=analysis_period_days)</span>
<span class="line-precise" title="No Anys on this line!">                analysis_period = (start_date, end_date)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Collect current budget allocations and performance</span>
<span class="line-precise" title="No Anys on this line!">                current_allocations = await self._collect_current_budget_data(</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_ids, analysis_period</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Analyze spend patterns and pacing</span>
<span class="line-precise" title="No Anys on this line!">                spend_analysis = await self._analyze_spend_patterns(</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_ids, analysis_period</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Collect performance data for allocation decisions</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x3)">                performance_data = await self._collect_performance_for_budget_decisions(</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_ids, analysis_period</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Generate budget recommendations based on strategy</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                recommended_allocations = await self._generate_budget_recommendations(</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    campaign_ids, current_allocations, performance_data,</span>
<span class="line-precise" title="No Anys on this line!">                    total_budget, optimization_goals, strategy</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Identify optimization opportunities</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">                optimization_opportunities = await self._identify_budget_opportunities(</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    current_allocations, recommended_allocations, performance_data</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Project performance impact</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                projected_performance = await self._project_budget_performance_impact(</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    recommended_allocations, performance_data</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Assess risks of budget changes</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                risk_assessment = await self._assess_budget_change_risks(</span>
<span class="line-precise" title="No Anys on this line!">                    current_allocations, recommended_allocations</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Create implementation timeline</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                implementation_timeline = await self._create_budget_implementation_timeline(</span>
<span class="line-precise" title="No Anys on this line!">                    recommended_allocations</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Create monitoring plan</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                monitoring_plan = await self._create_budget_monitoring_plan(</span>
<span class="line-precise" title="No Anys on this line!">                    recommended_allocations, optimization_goals</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Create comprehensive report</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">                report = BudgetRecommendationReport(</span>
<span class="line-precise" title="No Anys on this line!">                    report_id=f"budget_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",</span>
<span class="line-precise" title="No Anys on this line!">                    account_id=account_id,</span>
<span class="line-precise" title="No Anys on this line!">                    analysis_period=analysis_period,</span>
<span class="line-precise" title="No Anys on this line!">                    total_budget_analyzed=total_budget,</span>
<span class="line-precise" title="No Anys on this line!">                    current_allocations=current_allocations,</span>
<span class="line-precise" title="No Anys on this line!">                    recommended_allocations=recommended_allocations,</span>
<span class="line-precise" title="No Anys on this line!">                    spend_analysis=spend_analysis,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    optimization_opportunities=optimization_opportunities,</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    projected_performance=projected_performance,</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    risk_assessment=risk_assessment,</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    implementation_timeline=implementation_timeline,</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    monitoring_plan=monitoring_plan</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unimported (x2)">                self.tracer.record_task_result(span, {</span>
<span class="line-precise" title="No Anys on this line!">                    "report_id": report.report_id,</span>
<span class="line-precise" title="No Anys on this line!">                    "campaigns_analyzed": len(campaign_ids),</span>
<span class="line-precise" title="No Anys on this line!">                    "budget_changes_recommended": len([a for a in recommended_allocations if abs(a.budget_change_percentage) &gt; 5]),</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x6)">                    "total_budget_reallocation": sum(abs(a.budget_change_percentage) for a in recommended_allocations),</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                    "projected_performance_improvement": projected_performance.get("overall_improvement", 0)</span>
<span class="line-precise" title="No Anys on this line!">                }, True)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                    "Budget allocation optimization completed",</span>
<span class="line-precise" title="No Anys on this line!">                    report_id=report.report_id,</span>
<span class="line-precise" title="No Anys on this line!">                    budget_changes_recommended=len([a for a in recommended_allocations if abs(a.budget_change_percentage) &gt; 5]),</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                    projected_improvement=projected_performance.get("overall_improvement", 0)</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                return report</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">            except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.error("Budget allocation optimization failed", error=str(e))</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unimported (x2)">                self.tracer.record_task_result(span, {"error": str(e)}, False)</span>
<span class="line-precise" title="No Anys on this line!">                raise AgentError(f"Budget allocation optimization failed: {str(e)}")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def monitor_spend_pacing(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_ids: List[str],</span>
<span class="line-precise" title="No Anys on this line!">        monitoring_period_days: int = 7</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[SpendAnalysis]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Monitor campaign spend pacing and identify budget adjustments needed.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            campaign_ids: Campaigns to monitor</span>
<span class="line-empty" title="No Anys on this line!">            monitoring_period_days: Days to analyze for pacing</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            List[SpendAnalysis]: Spend analysis results for each campaign</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x2)
Explicit (x1)">        async with self.tracer.trace_task_execution(</span>
<span class="line-precise" title="No Anys on this line!">            task_id=f"monitor_pacing_{hash(str(campaign_ids))}",</span>
<span class="line-precise" title="No Anys on this line!">            task_name="Spend Pacing Monitoring",</span>
<span class="line-empty" title="No Anys on this line!">            task_data={</span>
<span class="line-precise" title="No Anys on this line!">                "campaign_count": len(campaign_ids),</span>
<span class="line-precise" title="No Anys on this line!">                "monitoring_days": monitoring_period_days</span>
<span class="line-empty" title="No Anys on this line!">            }</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">        ) as span:</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                    "Starting spend pacing monitoring",</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_count=len(campaign_ids),</span>
<span class="line-precise" title="No Anys on this line!">                    monitoring_days=monitoring_period_days</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                spend_analyses = []</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                for campaign_id in campaign_ids:</span>
<span class="line-empty" title="No Anys on this line!">                    try:</span>
<span class="line-precise" title="No Anys on this line!">                        analysis = await self._analyze_campaign_pacing(</span>
<span class="line-precise" title="No Anys on this line!">                            campaign_id, monitoring_period_days</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-precise" title="No Anys on this line!">                        if analysis:</span>
<span class="line-precise" title="No Anys on this line!">                            spend_analyses.append(analysis)</span>
<span class="line-precise" title="No Anys on this line!">                    except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                        self.logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">                            "Failed to analyze campaign pacing",</span>
<span class="line-precise" title="No Anys on this line!">                            campaign_id=campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                            error=str(e)</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Sort by pacing issues (most critical first)</span>
<span class="line-precise" title="No Anys on this line!">                pacing_severity = {</span>
<span class="line-precise" title="No Anys on this line!">                    SpendPacing.BUDGET_LIMITED: 4,</span>
<span class="line-precise" title="No Anys on this line!">                    SpendPacing.OVER_PACING: 3,</span>
<span class="line-precise" title="No Anys on this line!">                    SpendPacing.UNDER_PACING: 2,</span>
<span class="line-precise" title="No Anys on this line!">                    SpendPacing.ACCELERATED: 1,</span>
<span class="line-precise" title="No Anys on this line!">                    SpendPacing.ON_PACE: 0</span>
<span class="line-empty" title="No Anys on this line!">                }</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                spend_analyses.sort(</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                    key=lambda x: pacing_severity.get(x.pacing_status, 0),</span>
<span class="line-precise" title="No Anys on this line!">                    reverse=True</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unimported (x2)">                self.tracer.record_task_result(span, {</span>
<span class="line-precise" title="No Anys on this line!">                    "campaigns_analyzed": len(spend_analyses),</span>
<span class="line-precise" title="No Anys on this line!">                    "pacing_issues": len([a for a in spend_analyses if a.pacing_status != SpendPacing.ON_PACE]),</span>
<span class="line-precise" title="No Anys on this line!">                    "budget_limited_campaigns": len([a for a in spend_analyses if a.pacing_status == SpendPacing.BUDGET_LIMITED]),</span>
<span class="line-precise" title="No Anys on this line!">                    "over_pacing_campaigns": len([a for a in spend_analyses if a.pacing_status == SpendPacing.OVER_PACING])</span>
<span class="line-precise" title="No Anys on this line!">                }, True)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                    "Spend pacing monitoring completed",</span>
<span class="line-precise" title="No Anys on this line!">                    campaigns_analyzed=len(spend_analyses),</span>
<span class="line-precise" title="No Anys on this line!">                    pacing_issues=len([a for a in spend_analyses if a.pacing_status != SpendPacing.ON_PACE])</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                return spend_analyses</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">            except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.error("Spend pacing monitoring failed", error=str(e))</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unimported (x2)">                self.tracer.record_task_result(span, {"error": str(e)}, False)</span>
<span class="line-precise" title="No Anys on this line!">                raise AgentError(f"Spend pacing monitoring failed: {str(e)}")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def implement_budget_adjustments(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        budget_allocations: List[BudgetAllocation],</span>
<span class="line-precise" title="No Anys on this line!">        apply_immediately: bool = False,</span>
<span class="line-precise" title="No Anys on this line!">        validation_checks: bool = True</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; Dict[str, Any]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Implement budget allocation changes in Google Ads campaigns.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            budget_allocations: Budget allocation changes to implement</span>
<span class="line-empty" title="No Anys on this line!">            apply_immediately: Whether to apply changes immediately or schedule</span>
<span class="line-empty" title="No Anys on this line!">            validation_checks: Whether to perform validation before applying</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            Dict[str, Any]: Implementation results and status</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x2)
Explicit (x1)">        async with self.tracer.trace_task_execution(</span>
<span class="line-precise" title="No Anys on this line!">            task_id=f"implement_budget_{hash(str([a.allocation_id for a in budget_allocations]))}",</span>
<span class="line-precise" title="No Anys on this line!">            task_name="Budget Adjustments Implementation",</span>
<span class="line-empty" title="No Anys on this line!">            task_data={</span>
<span class="line-precise" title="No Anys on this line!">                "allocations_count": len(budget_allocations),</span>
<span class="line-precise" title="No Anys on this line!">                "apply_immediately": apply_immediately,</span>
<span class="line-precise" title="No Anys on this line!">                "validation_checks": validation_checks</span>
<span class="line-empty" title="No Anys on this line!">            }</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">        ) as span:</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                    "Starting budget adjustments implementation",</span>
<span class="line-precise" title="No Anys on this line!">                    allocations_count=len(budget_allocations),</span>
<span class="line-precise" title="No Anys on this line!">                    apply_immediately=apply_immediately</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                implementation_results = {</span>
<span class="line-precise" title="No Anys on this line!">                    "total_allocations": len(budget_allocations),</span>
<span class="line-precise" title="No Anys on this line!">                    "successful_implementations": 0,</span>
<span class="line-precise" title="No Anys on this line!">                    "failed_implementations": 0,</span>
<span class="line-precise" title="No Anys on this line!">                    "validation_failures": 0,</span>
<span class="line-precise" title="No Anys on this line!">                    "scheduled_implementations": 0,</span>
<span class="line-precise" title="No Anys on this line!">                    "results": []</span>
<span class="line-empty" title="No Anys on this line!">                }</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                for allocation in budget_allocations:</span>
<span class="line-empty" title="No Anys on this line!">                    try:</span>
<span class="line-empty" title="No Anys on this line!">                        # Perform validation checks if requested</span>
<span class="line-precise" title="No Anys on this line!">                        if validation_checks:</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                            validation_result = await self._validate_budget_allocation(allocation)</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                            if not validation_result["valid"]:</span>
<span class="line-precise" title="No Anys on this line!">                                implementation_results["validation_failures"] += 1</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                                implementation_results["results"].append({</span>
<span class="line-precise" title="No Anys on this line!">                                    "allocation_id": allocation.allocation_id,</span>
<span class="line-precise" title="No Anys on this line!">                                    "campaign_id": allocation.campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                                    "status": "validation_failed",</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                                    "error": validation_result["error"],</span>
<span class="line-precise" title="No Anys on this line!">                                    "current_budget": allocation.current_budget,</span>
<span class="line-precise" title="No Anys on this line!">                                    "recommended_budget": allocation.recommended_budget</span>
<span class="line-empty" title="No Anys on this line!">                                })</span>
<span class="line-precise" title="No Anys on this line!">                                continue</span>
<span class="line-empty" title="No Anys on this line!">                        </span>
<span class="line-empty" title="No Anys on this line!">                        # Apply budget change</span>
<span class="line-precise" title="No Anys on this line!">                        if apply_immediately:</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                            result = await self._apply_budget_change(allocation)</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                            if result["success"]:</span>
<span class="line-precise" title="No Anys on this line!">                                implementation_results["successful_implementations"] += 1</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                                implementation_results["results"].append({</span>
<span class="line-precise" title="No Anys on this line!">                                    "allocation_id": allocation.allocation_id,</span>
<span class="line-precise" title="No Anys on this line!">                                    "campaign_id": allocation.campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                                    "status": "applied",</span>
<span class="line-precise" title="No Anys on this line!">                                    "old_budget": allocation.current_budget,</span>
<span class="line-precise" title="No Anys on this line!">                                    "new_budget": allocation.recommended_budget,</span>
<span class="line-precise" title="No Anys on this line!">                                    "change_percentage": allocation.budget_change_percentage,</span>
<span class="line-precise" title="No Anys on this line!">                                    "effective_date": datetime.utcnow().isoformat()</span>
<span class="line-empty" title="No Anys on this line!">                                })</span>
<span class="line-empty" title="No Anys on this line!">                            else:</span>
<span class="line-precise" title="No Anys on this line!">                                implementation_results["failed_implementations"] += 1</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                                implementation_results["results"].append({</span>
<span class="line-precise" title="No Anys on this line!">                                    "allocation_id": allocation.allocation_id,</span>
<span class="line-precise" title="No Anys on this line!">                                    "campaign_id": allocation.campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                                    "status": "failed",</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                                    "error": result["error"]</span>
<span class="line-empty" title="No Anys on this line!">                                })</span>
<span class="line-empty" title="No Anys on this line!">                        else:</span>
<span class="line-empty" title="No Anys on this line!">                            # Schedule for later implementation</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                            schedule_result = await self._schedule_budget_change(allocation)</span>
<span class="line-precise" title="No Anys on this line!">                            implementation_results["scheduled_implementations"] += 1</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                            implementation_results["results"].append({</span>
<span class="line-precise" title="No Anys on this line!">                                "allocation_id": allocation.allocation_id,</span>
<span class="line-precise" title="No Anys on this line!">                                "campaign_id": allocation.campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                                "status": "scheduled",</span>
<span class="line-precise" title="No Anys on this line!">                                "scheduled_date": allocation.effective_date.isoformat(),</span>
<span class="line-precise" title="No Anys on this line!">                                "old_budget": allocation.current_budget,</span>
<span class="line-precise" title="No Anys on this line!">                                "new_budget": allocation.recommended_budget</span>
<span class="line-empty" title="No Anys on this line!">                            })</span>
<span class="line-empty" title="No Anys on this line!">                        </span>
<span class="line-precise" title="No Anys on this line!">                    except Exception as e:</span>
<span class="line-precise" title="No Anys on this line!">                        implementation_results["failed_implementations"] += 1</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                        implementation_results["results"].append({</span>
<span class="line-precise" title="No Anys on this line!">                            "allocation_id": allocation.allocation_id,</span>
<span class="line-precise" title="No Anys on this line!">                            "campaign_id": allocation.campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                            "status": "error",</span>
<span class="line-precise" title="No Anys on this line!">                            "error": str(e)</span>
<span class="line-empty" title="No Anys on this line!">                        })</span>
<span class="line-empty" title="No Anys on this line!">                        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                        self.logger.error(</span>
<span class="line-precise" title="No Anys on this line!">                            "Budget allocation implementation failed",</span>
<span class="line-precise" title="No Anys on this line!">                            allocation_id=allocation.allocation_id,</span>
<span class="line-precise" title="No Anys on this line!">                            error=str(e)</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Calculate success rate</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                total_processed = (implementation_results["successful_implementations"] + </span>
<span class="line-precise" title="No Anys on this line!">                                 implementation_results["failed_implementations"] +</span>
<span class="line-precise" title="No Anys on this line!">                                 implementation_results["scheduled_implementations"])</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                success_rate = (implementation_results["successful_implementations"] + </span>
<span class="line-precise" title="No Anys on this line!">                               implementation_results["scheduled_implementations"]) / len(budget_allocations) if budget_allocations else 0</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x1)">                implementation_results["success_rate"] = success_rate</span>
<span class="line-precise" title="No Anys on this line!">                implementation_results["summary"] = {</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x6)
Explicit (x8)">                    "total_budget_changes": sum(abs(a.budget_change_percentage) for a in budget_allocations),</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">                    "largest_increase": max([a.budget_change_percentage for a in budget_allocations if a.budget_change_percentage &gt; 0], default=0),</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">                    "largest_decrease": min([a.budget_change_percentage for a in budget_allocations if a.budget_change_percentage &lt; 0], default=0)</span>
<span class="line-empty" title="No Anys on this line!">                }</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unimported (x2)">                self.tracer.record_task_result(span, {</span>
<span class="line-precise" title="No Anys on this line!">                    "total_allocations": len(budget_allocations),</span>
<span class="line-precise" title="No Anys on this line!">                    "successful_implementations": implementation_results["successful_implementations"],</span>
<span class="line-precise" title="No Anys on this line!">                    "failed_implementations": implementation_results["failed_implementations"],</span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x1)">                    "success_rate": success_rate</span>
<span class="line-precise" title="No Anys on this line!">                }, True)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                    "Budget adjustments implementation completed",</span>
<span class="line-precise" title="No Anys on this line!">                    total_allocations=len(budget_allocations),</span>
<span class="line-precise" title="No Anys on this line!">                    successful=implementation_results["successful_implementations"],</span>
<span class="line-precise" title="No Anys on this line!">                    failed=implementation_results["failed_implementations"],</span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x1)">                    success_rate=success_rate</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                return implementation_results</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">            except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.error("Budget adjustments implementation failed", error=str(e))</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unimported (x2)">                self.tracer.record_task_result(span, {"error": str(e)}, False)</span>
<span class="line-precise" title="No Anys on this line!">                raise AgentError(f"Budget adjustments implementation failed: {str(e)}")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Core budget management methods</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _collect_current_budget_data(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_ids: List[str],</span>
<span class="line-empty" title="No Anys on this line!">        analysis_period: Tuple[datetime, datetime]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[BudgetAllocation]:</span>
<span class="line-empty" title="No Anys on this line!">        """Collect current budget allocation data for campaigns."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            current_allocations = []</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            if self.google_ads_service:</span>
<span class="line-precise" title="No Anys on this line!">                for campaign_id in campaign_ids:</span>
<span class="line-empty" title="No Anys on this line!">                    try:</span>
<span class="line-empty" title="No Anys on this line!">                        # Get campaign budget information</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                        campaign_info = await self.google_ads_service.get_campaign_info(campaign_id)</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                        budget_info = await self.google_ads_service.get_campaign_budget(campaign_id)</span>
<span class="line-empty" title="No Anys on this line!">                        </span>
<span class="line-precise" title="No Anys on this line!">                        allocation = BudgetAllocation(</span>
<span class="line-precise" title="No Anys on this line!">                            allocation_id=f"current_{campaign_id}",</span>
<span class="line-precise" title="No Anys on this line!">                            campaign_id=campaign_id,</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                            campaign_name=campaign_info.get("name", f"Campaign {campaign_id}"),</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                            current_budget=budget_info.get("daily_budget", 0),</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                            recommended_budget=budget_info.get("daily_budget", 0),  # Will be updated later</span>
<span class="line-precise" title="No Anys on this line!">                            budget_change_percentage=0.0,</span>
<span class="line-precise" title="No Anys on this line!">                            budget_type=BudgetType.DAILY,</span>
<span class="line-precise" title="No Anys on this line!">                            rationale="Current allocation",</span>
<span class="line-empty" title="No Anys on this line!">                            expected_impact={},</span>
<span class="line-precise" title="No Anys on this line!">                            confidence_score=1.0,</span>
<span class="line-precise" title="No Anys on this line!">                            priority="current"</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-empty" title="No Anys on this line!">                        </span>
<span class="line-precise" title="No Anys on this line!">                        current_allocations.append(allocation)</span>
<span class="line-empty" title="No Anys on this line!">                        </span>
<span class="line-precise" title="No Anys on this line!">                    except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                        self.logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">                            "Failed to get budget data for campaign",</span>
<span class="line-precise" title="No Anys on this line!">                            campaign_id=campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                            error=str(e)</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-empty" title="No Anys on this line!">            else:</span>
<span class="line-empty" title="No Anys on this line!">                # Mock data for development</span>
<span class="line-precise" title="No Anys on this line!">                for i, campaign_id in enumerate(campaign_ids):</span>
<span class="line-precise" title="No Anys on this line!">                    allocation = BudgetAllocation(</span>
<span class="line-precise" title="No Anys on this line!">                        allocation_id=f"current_{campaign_id}",</span>
<span class="line-precise" title="No Anys on this line!">                        campaign_id=campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                        campaign_name=f"Campaign {i+1}",</span>
<span class="line-precise" title="No Anys on this line!">                        current_budget=100.0 + (i * 50),  # Varying budgets</span>
<span class="line-precise" title="No Anys on this line!">                        recommended_budget=100.0 + (i * 50),</span>
<span class="line-precise" title="No Anys on this line!">                        budget_change_percentage=0.0,</span>
<span class="line-precise" title="No Anys on this line!">                        budget_type=BudgetType.DAILY,</span>
<span class="line-precise" title="No Anys on this line!">                        rationale="Current allocation",</span>
<span class="line-empty" title="No Anys on this line!">                        expected_impact={},</span>
<span class="line-precise" title="No Anys on this line!">                        confidence_score=1.0,</span>
<span class="line-precise" title="No Anys on this line!">                        priority="current"</span>
<span class="line-empty" title="No Anys on this line!">                    )</span>
<span class="line-precise" title="No Anys on this line!">                    current_allocations.append(allocation)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return current_allocations</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to collect current budget data", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">            return []</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _analyze_spend_patterns(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_ids: List[str],</span>
<span class="line-empty" title="No Anys on this line!">        analysis_period: Tuple[datetime, datetime]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[SpendAnalysis]:</span>
<span class="line-empty" title="No Anys on this line!">        """Analyze spend patterns and pacing for campaigns."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            spend_analyses = []</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            for campaign_id in campaign_ids:</span>
<span class="line-precise" title="No Anys on this line!">                analysis = await self._analyze_campaign_pacing(</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_id, (analysis_period[1] - analysis_period[0]).days</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-precise" title="No Anys on this line!">                if analysis:</span>
<span class="line-precise" title="No Anys on this line!">                    spend_analyses.append(analysis)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return spend_analyses</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to analyze spend patterns", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">            return []</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _analyze_campaign_pacing(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_id: str,</span>
<span class="line-empty" title="No Anys on this line!">        monitoring_days: int</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; Optional[SpendAnalysis]:</span>
<span class="line-empty" title="No Anys on this line!">        """Analyze individual campaign spend pacing."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-empty" title="No Anys on this line!">            # Define budget period (assuming monthly budgets)</span>
<span class="line-precise" title="No Anys on this line!">            today = datetime.utcnow().date()</span>
<span class="line-precise" title="No Anys on this line!">            period_start = today.replace(day=1)</span>
<span class="line-precise" title="No Anys on this line!">            period_end = (period_start.replace(month=period_start.month + 1) - timedelta(days=1))</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            days_in_month = (period_end - period_start).days + 1</span>
<span class="line-precise" title="No Anys on this line!">            days_elapsed = (today - period_start).days + 1</span>
<span class="line-precise" title="No Anys on this line!">            days_remaining = (period_end - today).days</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            if self.google_ads_service:</span>
<span class="line-empty" title="No Anys on this line!">                # Get actual spend data</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                spend_data = await self.google_ads_service.get_campaign_spend_data(</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_id, datetime.combine(period_start, datetime.min.time()),</span>
<span class="line-precise" title="No Anys on this line!">                    datetime.combine(period_end, datetime.max.time())</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Error (x4)">                total_budget = spend_data.get("monthly_budget", 3000.0)</span>
<span class="line-any" title="Any Types on this line: 
Error (x4)">                spent_to_date = spend_data.get("spent_to_date", 0.0)</span>
<span class="line-any" title="Any Types on this line: 
Error (x4)">                daily_spend_average = spend_data.get("daily_average", 0.0)</span>
<span class="line-any" title="Any Types on this line: 
Error (x4)">                impression_share_lost_budget = spend_data.get("impression_share_lost_budget")</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">            else:</span>
<span class="line-empty" title="No Anys on this line!">                # Mock data for development</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                total_budget = 3000.0</span>
<span class="line-empty" title="No Anys on this line!">                # Simulate various pacing scenarios</span>
<span class="line-precise" title="No Anys on this line!">                if "campaign_1" in campaign_id:</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    spent_to_date = total_budget * 0.25  # Under-pacing</span>
<span class="line-precise" title="No Anys on this line!">                elif "campaign_2" in campaign_id:</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    spent_to_date = total_budget * 0.85  # Over-pacing</span>
<span class="line-empty" title="No Anys on this line!">                else:</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    spent_to_date = total_budget * (days_elapsed / days_in_month) * 1.1  # Slightly over-pace</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                daily_spend_average = spent_to_date / days_elapsed if days_elapsed &gt; 0 else 0</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                impression_share_lost_budget = 15.2 if spent_to_date &gt; total_budget * 0.8 else None</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Calculate metrics</span>
<span class="line-any" title="Any Types on this line: 
Error (x5)
Omitted Generics (x23)">            remaining_budget = max(0, total_budget - spent_to_date)</span>
<span class="line-any" title="Any Types on this line: 
Error (x7)">            spend_percentage = (spent_to_date / total_budget * 100) if total_budget &gt; 0 else 0</span>
<span class="line-precise" title="No Anys on this line!">            expected_spend_percentage = (days_elapsed / days_in_month * 100)</span>
<span class="line-any" title="Any Types on this line: 
Error (x5)">            projected_end_spend = spent_to_date + (daily_spend_average * days_remaining)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Determine pacing status</span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x3)">            pacing_variance = spend_percentage - expected_spend_percentage</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Error (x4)">            if impression_share_lost_budget and impression_share_lost_budget &gt; 10:</span>
<span class="line-precise" title="No Anys on this line!">                pacing_status = SpendPacing.BUDGET_LIMITED</span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x2)">            elif pacing_variance &gt; 15:</span>
<span class="line-precise" title="No Anys on this line!">                pacing_status = SpendPacing.OVER_PACING</span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x2)">            elif pacing_variance &lt; -15:</span>
<span class="line-precise" title="No Anys on this line!">                pacing_status = SpendPacing.UNDER_PACING</span>
<span class="line-any" title="Any Types on this line: 
Error (x4)">            elif projected_end_spend &gt; total_budget * 1.2:</span>
<span class="line-precise" title="No Anys on this line!">                pacing_status = SpendPacing.ACCELERATED</span>
<span class="line-empty" title="No Anys on this line!">            else:</span>
<span class="line-precise" title="No Anys on this line!">                pacing_status = SpendPacing.ON_PACE</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Generate recommendations based on pacing</span>
<span class="line-precise" title="No Anys on this line!">            recommendations = []</span>
<span class="line-precise" title="No Anys on this line!">            if pacing_status == SpendPacing.BUDGET_LIMITED:</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                recommendations.append(f"Increase budget by {impression_share_lost_budget:.0f}% to capture missed opportunities")</span>
<span class="line-precise" title="No Anys on this line!">            elif pacing_status == SpendPacing.OVER_PACING:</span>
<span class="line-precise" title="No Anys on this line!">                recommendations.append("Reduce daily budget or pause campaign to avoid overspending")</span>
<span class="line-precise" title="No Anys on this line!">            elif pacing_status == SpendPacing.UNDER_PACING:</span>
<span class="line-precise" title="No Anys on this line!">                recommendations.append("Increase bids or expand targeting to improve spend pacing")</span>
<span class="line-precise" title="No Anys on this line!">            elif pacing_status == SpendPacing.ACCELERATED:</span>
<span class="line-precise" title="No Anys on this line!">                recommendations.append("Monitor closely and consider budget adjustments to avoid month-end budget exhaustion")</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return SpendAnalysis(</span>
<span class="line-precise" title="No Anys on this line!">                campaign_id=campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                campaign_name=f"Campaign {campaign_id[-3:]}",</span>
<span class="line-precise" title="No Anys on this line!">                budget_period_start=datetime.combine(period_start, datetime.min.time()),</span>
<span class="line-precise" title="No Anys on this line!">                budget_period_end=datetime.combine(period_end, datetime.max.time()),</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                total_budget=total_budget,</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                spent_to_date=spent_to_date,</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                remaining_budget=remaining_budget,</span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x1)">                spend_percentage=spend_percentage,</span>
<span class="line-precise" title="No Anys on this line!">                days_elapsed=days_elapsed,</span>
<span class="line-precise" title="No Anys on this line!">                days_remaining=days_remaining,</span>
<span class="line-precise" title="No Anys on this line!">                expected_spend_percentage=expected_spend_percentage,</span>
<span class="line-precise" title="No Anys on this line!">                pacing_status=pacing_status,</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                daily_spend_average=daily_spend_average,</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                projected_end_spend=projected_end_spend,</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                impression_share_lost_budget=impression_share_lost_budget,</span>
<span class="line-precise" title="No Anys on this line!">                recommendations=recommendations</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.warning("Failed to analyze campaign pacing", campaign_id=campaign_id, error=str(e))</span>
<span class="line-precise" title="No Anys on this line!">            return None</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _collect_performance_for_budget_decisions(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_ids: List[str],</span>
<span class="line-empty" title="No Anys on this line!">        analysis_period: Tuple[datetime, datetime]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; Dict[str, Dict[str, Any]]:</span>
<span class="line-empty" title="No Anys on this line!">        """Collect performance data needed for budget allocation decisions."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            performance_data = {}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            for campaign_id in campaign_ids:</span>
<span class="line-precise" title="No Anys on this line!">                if self.google_ads_service:</span>
<span class="line-empty" title="No Anys on this line!">                    try:</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                        campaign_performance = await self.google_ads_service.get_campaign_performance(</span>
<span class="line-precise" title="No Anys on this line!">                            campaign_id, analysis_period[0], analysis_period[1]</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                        performance_data[campaign_id] = campaign_performance</span>
<span class="line-precise" title="No Anys on this line!">                    except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                        self.logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">                            "Failed to get performance data for campaign",</span>
<span class="line-precise" title="No Anys on this line!">                            campaign_id=campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                            error=str(e)</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-empty" title="No Anys on this line!">                else:</span>
<span class="line-empty" title="No Anys on this line!">                    # Mock performance data</span>
<span class="line-precise" title="No Anys on this line!">                    base_performance = {</span>
<span class="line-precise" title="No Anys on this line!">                        "impressions": 10000,</span>
<span class="line-precise" title="No Anys on this line!">                        "clicks": 250,</span>
<span class="line-precise" title="No Anys on this line!">                        "conversions": 15,</span>
<span class="line-precise" title="No Anys on this line!">                        "cost": 1250.0,</span>
<span class="line-precise" title="No Anys on this line!">                        "revenue": 3000.0</span>
<span class="line-empty" title="No Anys on this line!">                    }</span>
<span class="line-empty" title="No Anys on this line!">                    </span>
<span class="line-empty" title="No Anys on this line!">                    # Vary performance by campaign</span>
<span class="line-precise" title="No Anys on this line!">                    if "high_performer" in campaign_id:</span>
<span class="line-precise" title="No Anys on this line!">                        multiplier = 1.5</span>
<span class="line-precise" title="No Anys on this line!">                    elif "low_performer" in campaign_id:</span>
<span class="line-precise" title="No Anys on this line!">                        multiplier = 0.6</span>
<span class="line-empty" title="No Anys on this line!">                    else:</span>
<span class="line-precise" title="No Anys on this line!">                        multiplier = 1.0</span>
<span class="line-empty" title="No Anys on this line!">                    </span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x1)">                    performance_data[campaign_id] = {</span>
<span class="line-precise" title="No Anys on this line!">                        key: value * multiplier for key, value in base_performance.items()</span>
<span class="line-empty" title="No Anys on this line!">                    }</span>
<span class="line-empty" title="No Anys on this line!">                    </span>
<span class="line-empty" title="No Anys on this line!">                    # Calculate derived metrics</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                    campaign_data = performance_data[campaign_id]</span>
<span class="line-any" title="Any Types on this line: 
Error (x7)">                    campaign_data["ctr"] = (campaign_data["clicks"] / campaign_data["impressions"]) * 100</span>
<span class="line-any" title="Any Types on this line: 
Error (x7)">                    campaign_data["conversion_rate"] = (campaign_data["conversions"] / campaign_data["clicks"]) * 100</span>
<span class="line-any" title="Any Types on this line: 
Error (x6)">                    campaign_data["cpc"] = campaign_data["cost"] / campaign_data["clicks"]</span>
<span class="line-any" title="Any Types on this line: 
Error (x6)">                    campaign_data["cpa"] = campaign_data["cost"] / campaign_data["conversions"]</span>
<span class="line-any" title="Any Types on this line: 
Error (x6)">                    campaign_data["roas"] = campaign_data["revenue"] / campaign_data["cost"]</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x1)">            return performance_data</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to collect performance data for budget decisions", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">            return {}</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _generate_budget_recommendations(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_ids: List[str],</span>
<span class="line-empty" title="No Anys on this line!">        current_allocations: List[BudgetAllocation],</span>
<span class="line-empty" title="No Anys on this line!">        performance_data: Dict[str, Dict[str, Any]],</span>
<span class="line-empty" title="No Anys on this line!">        total_budget: float,</span>
<span class="line-empty" title="No Anys on this line!">        optimization_goals: List[BudgetOptimizationGoal],</span>
<span class="line-empty" title="No Anys on this line!">        strategy: BudgetStrategy</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[BudgetAllocation]:</span>
<span class="line-empty" title="No Anys on this line!">        """Generate budget allocation recommendations based on strategy and performance."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            if strategy == BudgetStrategy.PERFORMANCE_BASED:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                return await self._generate_performance_based_allocations(</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    current_allocations, performance_data, total_budget, optimization_goals</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-precise" title="No Anys on this line!">            elif strategy == BudgetStrategy.ROI_OPTIMIZED:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                return await self._generate_roi_optimized_allocations(</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    current_allocations, performance_data, total_budget, optimization_goals</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-precise" title="No Anys on this line!">            elif strategy == BudgetStrategy.EQUAL_DISTRIBUTION:</span>
<span class="line-precise" title="No Anys on this line!">                return await self._generate_equal_distribution_allocations(</span>
<span class="line-precise" title="No Anys on this line!">                    current_allocations, total_budget</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-precise" title="No Anys on this line!">            elif strategy == BudgetStrategy.PRIORITY_BASED:</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                return await self._generate_priority_based_allocations(</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    current_allocations, performance_data, total_budget, optimization_goals</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-precise" title="No Anys on this line!">            elif strategy == BudgetStrategy.SEASONAL_ADJUSTED:</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                return await self._generate_seasonal_adjusted_allocations(</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    current_allocations, performance_data, total_budget, optimization_goals</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">            else:</span>
<span class="line-empty" title="No Anys on this line!">                # Default to performance-based</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                return await self._generate_performance_based_allocations(</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    current_allocations, performance_data, total_budget, optimization_goals</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to generate budget recommendations", error=str(e))</span>
<span class="line-precise" title="No Anys on this line!">            return current_allocations</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _generate_performance_based_allocations(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        current_allocations: List[BudgetAllocation],</span>
<span class="line-empty" title="No Anys on this line!">        performance_data: Dict[str, Dict[str, Any]],</span>
<span class="line-empty" title="No Anys on this line!">        total_budget: float,</span>
<span class="line-empty" title="No Anys on this line!">        optimization_goals: List[BudgetOptimizationGoal]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[BudgetAllocation]:</span>
<span class="line-empty" title="No Anys on this line!">        """Generate performance-based budget allocations."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            recommendations = []</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Calculate performance scores for each campaign</span>
<span class="line-precise" title="No Anys on this line!">            campaign_scores = {}</span>
<span class="line-precise" title="No Anys on this line!">            for allocation in current_allocations:</span>
<span class="line-precise" title="No Anys on this line!">                campaign_id = allocation.campaign_id</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">                perf_data = performance_data.get(campaign_id, {})</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Calculate weighted performance score</span>
<span class="line-precise" title="No Anys on this line!">                score = 0.0</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                if perf_data:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">                    roas = perf_data.get("roas", 0)</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">                    conversion_rate = perf_data.get("conversion_rate", 0)</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">                    cpa = perf_data.get("cpa", float('inf'))</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">                    ctr = perf_data.get("ctr", 0)</span>
<span class="line-empty" title="No Anys on this line!">                    </span>
<span class="line-empty" title="No Anys on this line!">                    # Normalize and weight metrics</span>
<span class="line-precise" title="No Anys on this line!">                    score = (</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x23)">                        min(1.0, roas / 4.0) * self.performance_weights["roas"] +</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)
Omitted Generics (x23)">                        min(1.0, conversion_rate / 5.0) * self.performance_weights["conversion_rate"] +</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x5)
Omitted Generics (x46)">                        min(1.0, 100.0 / max(1.0, cpa)) * self.performance_weights["cost_per_conversion"] +</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)
Omitted Generics (x23)">                        min(1.0, ctr / 5.0) * self.performance_weights["click_through_rate"]</span>
<span class="line-empty" title="No Anys on this line!">                    )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">                campaign_scores[campaign_id] = max(0.1, score)  # Minimum score to avoid zero allocation</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Calculate total weighted score</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x6)
Explicit (x8)">            total_score = sum(campaign_scores.values())</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Allocate budget proportionally based on performance scores</span>
<span class="line-precise" title="No Anys on this line!">            for allocation in current_allocations:</span>
<span class="line-precise" title="No Anys on this line!">                campaign_id = allocation.campaign_id</span>
<span class="line-precise" title="No Anys on this line!">                performance_ratio = campaign_scores[campaign_id] / total_score</span>
<span class="line-precise" title="No Anys on this line!">                recommended_daily_budget = (total_budget / 30) * performance_ratio  # Convert monthly to daily</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Apply constraints</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">                min_budget = max(self.budget_thresholds["min_daily_budget"], </span>
<span class="line-precise" title="No Anys on this line!">                               allocation.current_budget * 0.5)  # Don't reduce by more than 50%</span>
<span class="line-precise" title="No Anys on this line!">                max_budget = allocation.current_budget * (1 + self.budget_thresholds["max_budget_increase_percentage"] / 100)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x46)">                recommended_daily_budget = max(min_budget, min(max_budget, recommended_daily_budget))</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Calculate change percentage</span>
<span class="line-precise" title="No Anys on this line!">                budget_change_percentage = ((recommended_daily_budget - allocation.current_budget) / </span>
<span class="line-precise" title="No Anys on this line!">                                          allocation.current_budget * 100) if allocation.current_budget &gt; 0 else 0</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Generate rationale</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">                perf_data = performance_data.get(campaign_id, {})</span>
<span class="line-precise" title="No Anys on this line!">                rationale = f"Performance score: {campaign_scores[campaign_id]:.2f}. "</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">                if perf_data.get("roas", 0) &gt; 3.0:</span>
<span class="line-precise" title="No Anys on this line!">                    rationale += "High ROAS performance justifies increased budget allocation. "</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">                if perf_data.get("conversion_rate", 0) &gt; 3.0:</span>
<span class="line-precise" title="No Anys on this line!">                    rationale += "Strong conversion rate indicates efficient spending. "</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Calculate expected impact</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                expected_impact = {</span>
<span class="line-precise" title="No Anys on this line!">                    "performance_score": campaign_scores[campaign_id],</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">                    "projected_roas": perf_data.get("roas", 0) * (1 + budget_change_percentage / 200),  # Conservative projection</span>
<span class="line-precise" title="No Anys on this line!">                    "projected_conversions_change": budget_change_percentage * 0.8,  # Assume 80% efficiency</span>
<span class="line-empty" title="No Anys on this line!">                }</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                recommendation = BudgetAllocation(</span>
<span class="line-precise" title="No Anys on this line!">                    allocation_id=f"perf_based_{campaign_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_id=campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_name=allocation.campaign_name,</span>
<span class="line-precise" title="No Anys on this line!">                    current_budget=allocation.current_budget,</span>
<span class="line-precise" title="No Anys on this line!">                    recommended_budget=recommended_daily_budget,</span>
<span class="line-precise" title="No Anys on this line!">                    budget_change_percentage=budget_change_percentage,</span>
<span class="line-precise" title="No Anys on this line!">                    budget_type=BudgetType.DAILY,</span>
<span class="line-precise" title="No Anys on this line!">                    rationale=rationale.strip(),</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    expected_impact=expected_impact,</span>
<span class="line-any" title="Any Types on this line: 
Omitted Generics (x23)
Explicit (x6)">                    confidence_score=min(1.0, len([k for k, v in perf_data.items() if v &gt; 0]) / 6),  # Based on data completeness</span>
<span class="line-precise" title="No Anys on this line!">                    priority="high" if abs(budget_change_percentage) &gt; 25 else "medium"</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                recommendations.append(recommendation)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return recommendations</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to generate performance-based allocations", error=str(e))</span>
<span class="line-precise" title="No Anys on this line!">            return current_allocations</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _generate_roi_optimized_allocations(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        current_allocations: List[BudgetAllocation],</span>
<span class="line-empty" title="No Anys on this line!">        performance_data: Dict[str, Dict[str, Any]],</span>
<span class="line-empty" title="No Anys on this line!">        total_budget: float,</span>
<span class="line-empty" title="No Anys on this line!">        optimization_goals: List[BudgetOptimizationGoal]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[BudgetAllocation]:</span>
<span class="line-empty" title="No Anys on this line!">        """Generate ROI-optimized budget allocations."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            recommendations = []</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Calculate ROI for each campaign</span>
<span class="line-precise" title="No Anys on this line!">            campaign_rois = {}</span>
<span class="line-precise" title="No Anys on this line!">            for allocation in current_allocations:</span>
<span class="line-precise" title="No Anys on this line!">                campaign_id = allocation.campaign_id</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">                perf_data = performance_data.get(campaign_id, {})</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">                roas = perf_data.get("roas", 0)</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)
Omitted Generics (x23)">                roi = max(0.1, roas - 1.0)  # ROI = ROAS - 1, minimum 0.1</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">                campaign_rois[campaign_id] = roi</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Sort campaigns by ROI</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x5)">            sorted_campaigns = sorted(campaign_rois.items(), key=lambda x: x[1], reverse=True)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Allocate more budget to higher ROI campaigns</span>
<span class="line-precise" title="No Anys on this line!">            remaining_budget = total_budget / 30  # Daily budget</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x5)">            for i, (campaign_id, roi) in enumerate(sorted_campaigns):</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x6)">                allocation = next(a for a in current_allocations if a.campaign_id == campaign_id)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Allocate budget based on ROI ranking</span>
<span class="line-precise" title="No Anys on this line!">                if i == 0:  # Highest ROI</span>
<span class="line-precise" title="No Anys on this line!">                    budget_multiplier = 1.5</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                elif i &lt; len(sorted_campaigns) / 2:  # Top half</span>
<span class="line-precise" title="No Anys on this line!">                    budget_multiplier = 1.2</span>
<span class="line-empty" title="No Anys on this line!">                else:  # Bottom half</span>
<span class="line-precise" title="No Anys on this line!">                    budget_multiplier = 0.8</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                base_allocation = remaining_budget / len(sorted_campaigns)</span>
<span class="line-precise" title="No Anys on this line!">                recommended_budget = base_allocation * budget_multiplier</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Apply constraints</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">                recommended_budget = max(</span>
<span class="line-precise" title="No Anys on this line!">                    self.budget_thresholds["min_daily_budget"],</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">                    min(allocation.current_budget * 2, recommended_budget)</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                budget_change_percentage = ((recommended_budget - allocation.current_budget) / </span>
<span class="line-precise" title="No Anys on this line!">                                          allocation.current_budget * 100) if allocation.current_budget &gt; 0 else 0</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                rationale = f"ROI-based allocation. Campaign ROI: {roi:.2f}. "</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">                if roi &gt; 2.0:</span>
<span class="line-precise" title="No Anys on this line!">                    rationale += "Excellent ROI justifies increased investment. "</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">                elif roi &lt; 0.5:</span>
<span class="line-precise" title="No Anys on this line!">                    rationale += "Low ROI suggests budget reduction or optimization needed. "</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                recommendation = BudgetAllocation(</span>
<span class="line-precise" title="No Anys on this line!">                    allocation_id=f"roi_opt_{campaign_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_id=campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_name=allocation.campaign_name,</span>
<span class="line-precise" title="No Anys on this line!">                    current_budget=allocation.current_budget,</span>
<span class="line-precise" title="No Anys on this line!">                    recommended_budget=recommended_budget,</span>
<span class="line-precise" title="No Anys on this line!">                    budget_change_percentage=budget_change_percentage,</span>
<span class="line-precise" title="No Anys on this line!">                    budget_type=BudgetType.DAILY,</span>
<span class="line-precise" title="No Anys on this line!">                    rationale=rationale,</span>
<span class="line-empty" title="No Anys on this line!">                    expected_impact={</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                        "roi": roi,</span>
<span class="line-precise" title="No Anys on this line!">                        "roi_ranking": i + 1,</span>
<span class="line-precise" title="No Anys on this line!">                        "projected_roi_improvement": budget_change_percentage * 0.5</span>
<span class="line-empty" title="No Anys on this line!">                    },</span>
<span class="line-precise" title="No Anys on this line!">                    confidence_score=0.8,</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">                    priority="high" if roi &gt; 2.0 else "medium"</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                recommendations.append(recommendation)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return recommendations</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to generate ROI-optimized allocations", error=str(e))</span>
<span class="line-precise" title="No Anys on this line!">            return current_allocations</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _generate_equal_distribution_allocations(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        current_allocations: List[BudgetAllocation],</span>
<span class="line-empty" title="No Anys on this line!">        total_budget: float</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[BudgetAllocation]:</span>
<span class="line-empty" title="No Anys on this line!">        """Generate equal budget distribution allocations."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            recommendations = []</span>
<span class="line-precise" title="No Anys on this line!">            daily_budget_per_campaign = (total_budget / 30) / len(current_allocations)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            for allocation in current_allocations:</span>
<span class="line-precise" title="No Anys on this line!">                budget_change_percentage = ((daily_budget_per_campaign - allocation.current_budget) / </span>
<span class="line-precise" title="No Anys on this line!">                                          allocation.current_budget * 100) if allocation.current_budget &gt; 0 else 0</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                recommendation = BudgetAllocation(</span>
<span class="line-precise" title="No Anys on this line!">                    allocation_id=f"equal_dist_{allocation.campaign_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_id=allocation.campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_name=allocation.campaign_name,</span>
<span class="line-precise" title="No Anys on this line!">                    current_budget=allocation.current_budget,</span>
<span class="line-precise" title="No Anys on this line!">                    recommended_budget=daily_budget_per_campaign,</span>
<span class="line-precise" title="No Anys on this line!">                    budget_change_percentage=budget_change_percentage,</span>
<span class="line-precise" title="No Anys on this line!">                    budget_type=BudgetType.DAILY,</span>
<span class="line-precise" title="No Anys on this line!">                    rationale="Equal distribution strategy - allocating budget evenly across all campaigns",</span>
<span class="line-precise" title="No Anys on this line!">                    expected_impact={"distribution_method": "equal"},</span>
<span class="line-precise" title="No Anys on this line!">                    confidence_score=1.0,</span>
<span class="line-precise" title="No Anys on this line!">                    priority="medium"</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                recommendations.append(recommendation)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return recommendations</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to generate equal distribution allocations", error=str(e))</span>
<span class="line-precise" title="No Anys on this line!">            return current_allocations</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Additional helper methods for other allocation strategies...</span>
<span class="line-empty" title="No Anys on this line!">    # (Implementation continues with similar patterns for other strategies)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">    async def _identify_budget_opportunities(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        current_allocations: List[BudgetAllocation],</span>
<span class="line-empty" title="No Anys on this line!">        recommended_allocations: List[BudgetAllocation],</span>
<span class="line-empty" title="No Anys on this line!">        performance_data: Dict[str, Dict[str, Any]]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[Dict[str, Any]]:</span>
<span class="line-empty" title="No Anys on this line!">        """Identify budget optimization opportunities."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            opportunities = []</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Compare current vs recommended allocations</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x30)">            for current, recommended in zip(current_allocations, recommended_allocations):</span>
<span class="line-precise" title="No Anys on this line!">                budget_change = abs(recommended.budget_change_percentage)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                if budget_change &gt; 20:  # Significant change opportunity</span>
<span class="line-precise" title="No Anys on this line!">                    opportunity = {</span>
<span class="line-precise" title="No Anys on this line!">                        "type": "budget_reallocation",</span>
<span class="line-precise" title="No Anys on this line!">                        "campaign_id": current.campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                        "campaign_name": current.campaign_name,</span>
<span class="line-precise" title="No Anys on this line!">                        "current_budget": current.current_budget,</span>
<span class="line-precise" title="No Anys on this line!">                        "recommended_budget": recommended.recommended_budget,</span>
<span class="line-precise" title="No Anys on this line!">                        "change_percentage": recommended.budget_change_percentage,</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">                        "opportunity_score": min(100, budget_change * recommended.confidence_score),</span>
<span class="line-precise" title="No Anys on this line!">                        "rationale": recommended.rationale,</span>
<span class="line-precise" title="No Anys on this line!">                        "expected_impact": recommended.expected_impact</span>
<span class="line-empty" title="No Anys on this line!">                    }</span>
<span class="line-precise" title="No Anys on this line!">                    opportunities.append(opportunity)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Look for budget-limited campaigns</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">            for campaign_id, perf_data in performance_data.items():</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">                impression_share_lost = perf_data.get("impression_share_lost_budget", 0)</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">                if impression_share_lost &gt; 10:</span>
<span class="line-precise" title="No Anys on this line!">                    opportunities.append({</span>
<span class="line-precise" title="No Anys on this line!">                        "type": "budget_constraint",</span>
<span class="line-precise" title="No Anys on this line!">                        "campaign_id": campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                        "issue": "impression_share_lost_budget",</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                        "lost_share_percentage": impression_share_lost,</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">                        "opportunity_score": impression_share_lost * 2,</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                        "recommended_action": f"Increase budget by {impression_share_lost:.0f}% to capture missed impressions"</span>
<span class="line-empty" title="No Anys on this line!">                    })</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Sort by opportunity score</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x1)">            opportunities.sort(key=lambda x: x.get("opportunity_score", 0), reverse=True)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return opportunities</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to identify budget opportunities", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">            return []</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Additional implementation methods...</span>
<span class="line-empty" title="No Anys on this line!">    # (Continue with similar patterns for other required methods)</span>
</pre></td>
</tr></tbody>
</table>
</body>
</html>
