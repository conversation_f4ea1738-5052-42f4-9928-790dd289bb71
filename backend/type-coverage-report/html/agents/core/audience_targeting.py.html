<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../../mypy-html.css">
</head>
<body>
<h2>agents.core.audience_targeting</h2>
<table>
<caption>agents/core/audience_targeting.py</caption>
<tbody><tr>
<td class="table-lines"><pre><span id="L1" class="lineno"><a class="lineno" href="#L1">1</a></span>
<span id="L2" class="lineno"><a class="lineno" href="#L2">2</a></span>
<span id="L3" class="lineno"><a class="lineno" href="#L3">3</a></span>
<span id="L4" class="lineno"><a class="lineno" href="#L4">4</a></span>
<span id="L5" class="lineno"><a class="lineno" href="#L5">5</a></span>
<span id="L6" class="lineno"><a class="lineno" href="#L6">6</a></span>
<span id="L7" class="lineno"><a class="lineno" href="#L7">7</a></span>
<span id="L8" class="lineno"><a class="lineno" href="#L8">8</a></span>
<span id="L9" class="lineno"><a class="lineno" href="#L9">9</a></span>
<span id="L10" class="lineno"><a class="lineno" href="#L10">10</a></span>
<span id="L11" class="lineno"><a class="lineno" href="#L11">11</a></span>
<span id="L12" class="lineno"><a class="lineno" href="#L12">12</a></span>
<span id="L13" class="lineno"><a class="lineno" href="#L13">13</a></span>
<span id="L14" class="lineno"><a class="lineno" href="#L14">14</a></span>
<span id="L15" class="lineno"><a class="lineno" href="#L15">15</a></span>
<span id="L16" class="lineno"><a class="lineno" href="#L16">16</a></span>
<span id="L17" class="lineno"><a class="lineno" href="#L17">17</a></span>
<span id="L18" class="lineno"><a class="lineno" href="#L18">18</a></span>
<span id="L19" class="lineno"><a class="lineno" href="#L19">19</a></span>
<span id="L20" class="lineno"><a class="lineno" href="#L20">20</a></span>
<span id="L21" class="lineno"><a class="lineno" href="#L21">21</a></span>
<span id="L22" class="lineno"><a class="lineno" href="#L22">22</a></span>
<span id="L23" class="lineno"><a class="lineno" href="#L23">23</a></span>
<span id="L24" class="lineno"><a class="lineno" href="#L24">24</a></span>
<span id="L25" class="lineno"><a class="lineno" href="#L25">25</a></span>
<span id="L26" class="lineno"><a class="lineno" href="#L26">26</a></span>
<span id="L27" class="lineno"><a class="lineno" href="#L27">27</a></span>
<span id="L28" class="lineno"><a class="lineno" href="#L28">28</a></span>
<span id="L29" class="lineno"><a class="lineno" href="#L29">29</a></span>
<span id="L30" class="lineno"><a class="lineno" href="#L30">30</a></span>
<span id="L31" class="lineno"><a class="lineno" href="#L31">31</a></span>
<span id="L32" class="lineno"><a class="lineno" href="#L32">32</a></span>
<span id="L33" class="lineno"><a class="lineno" href="#L33">33</a></span>
<span id="L34" class="lineno"><a class="lineno" href="#L34">34</a></span>
<span id="L35" class="lineno"><a class="lineno" href="#L35">35</a></span>
<span id="L36" class="lineno"><a class="lineno" href="#L36">36</a></span>
<span id="L37" class="lineno"><a class="lineno" href="#L37">37</a></span>
<span id="L38" class="lineno"><a class="lineno" href="#L38">38</a></span>
<span id="L39" class="lineno"><a class="lineno" href="#L39">39</a></span>
<span id="L40" class="lineno"><a class="lineno" href="#L40">40</a></span>
<span id="L41" class="lineno"><a class="lineno" href="#L41">41</a></span>
<span id="L42" class="lineno"><a class="lineno" href="#L42">42</a></span>
<span id="L43" class="lineno"><a class="lineno" href="#L43">43</a></span>
<span id="L44" class="lineno"><a class="lineno" href="#L44">44</a></span>
<span id="L45" class="lineno"><a class="lineno" href="#L45">45</a></span>
<span id="L46" class="lineno"><a class="lineno" href="#L46">46</a></span>
<span id="L47" class="lineno"><a class="lineno" href="#L47">47</a></span>
<span id="L48" class="lineno"><a class="lineno" href="#L48">48</a></span>
<span id="L49" class="lineno"><a class="lineno" href="#L49">49</a></span>
<span id="L50" class="lineno"><a class="lineno" href="#L50">50</a></span>
<span id="L51" class="lineno"><a class="lineno" href="#L51">51</a></span>
<span id="L52" class="lineno"><a class="lineno" href="#L52">52</a></span>
<span id="L53" class="lineno"><a class="lineno" href="#L53">53</a></span>
<span id="L54" class="lineno"><a class="lineno" href="#L54">54</a></span>
<span id="L55" class="lineno"><a class="lineno" href="#L55">55</a></span>
<span id="L56" class="lineno"><a class="lineno" href="#L56">56</a></span>
<span id="L57" class="lineno"><a class="lineno" href="#L57">57</a></span>
<span id="L58" class="lineno"><a class="lineno" href="#L58">58</a></span>
<span id="L59" class="lineno"><a class="lineno" href="#L59">59</a></span>
<span id="L60" class="lineno"><a class="lineno" href="#L60">60</a></span>
<span id="L61" class="lineno"><a class="lineno" href="#L61">61</a></span>
<span id="L62" class="lineno"><a class="lineno" href="#L62">62</a></span>
<span id="L63" class="lineno"><a class="lineno" href="#L63">63</a></span>
<span id="L64" class="lineno"><a class="lineno" href="#L64">64</a></span>
<span id="L65" class="lineno"><a class="lineno" href="#L65">65</a></span>
<span id="L66" class="lineno"><a class="lineno" href="#L66">66</a></span>
<span id="L67" class="lineno"><a class="lineno" href="#L67">67</a></span>
<span id="L68" class="lineno"><a class="lineno" href="#L68">68</a></span>
<span id="L69" class="lineno"><a class="lineno" href="#L69">69</a></span>
<span id="L70" class="lineno"><a class="lineno" href="#L70">70</a></span>
<span id="L71" class="lineno"><a class="lineno" href="#L71">71</a></span>
<span id="L72" class="lineno"><a class="lineno" href="#L72">72</a></span>
<span id="L73" class="lineno"><a class="lineno" href="#L73">73</a></span>
<span id="L74" class="lineno"><a class="lineno" href="#L74">74</a></span>
<span id="L75" class="lineno"><a class="lineno" href="#L75">75</a></span>
<span id="L76" class="lineno"><a class="lineno" href="#L76">76</a></span>
<span id="L77" class="lineno"><a class="lineno" href="#L77">77</a></span>
<span id="L78" class="lineno"><a class="lineno" href="#L78">78</a></span>
<span id="L79" class="lineno"><a class="lineno" href="#L79">79</a></span>
<span id="L80" class="lineno"><a class="lineno" href="#L80">80</a></span>
<span id="L81" class="lineno"><a class="lineno" href="#L81">81</a></span>
<span id="L82" class="lineno"><a class="lineno" href="#L82">82</a></span>
<span id="L83" class="lineno"><a class="lineno" href="#L83">83</a></span>
<span id="L84" class="lineno"><a class="lineno" href="#L84">84</a></span>
<span id="L85" class="lineno"><a class="lineno" href="#L85">85</a></span>
<span id="L86" class="lineno"><a class="lineno" href="#L86">86</a></span>
<span id="L87" class="lineno"><a class="lineno" href="#L87">87</a></span>
<span id="L88" class="lineno"><a class="lineno" href="#L88">88</a></span>
<span id="L89" class="lineno"><a class="lineno" href="#L89">89</a></span>
<span id="L90" class="lineno"><a class="lineno" href="#L90">90</a></span>
<span id="L91" class="lineno"><a class="lineno" href="#L91">91</a></span>
<span id="L92" class="lineno"><a class="lineno" href="#L92">92</a></span>
<span id="L93" class="lineno"><a class="lineno" href="#L93">93</a></span>
<span id="L94" class="lineno"><a class="lineno" href="#L94">94</a></span>
<span id="L95" class="lineno"><a class="lineno" href="#L95">95</a></span>
<span id="L96" class="lineno"><a class="lineno" href="#L96">96</a></span>
<span id="L97" class="lineno"><a class="lineno" href="#L97">97</a></span>
<span id="L98" class="lineno"><a class="lineno" href="#L98">98</a></span>
<span id="L99" class="lineno"><a class="lineno" href="#L99">99</a></span>
<span id="L100" class="lineno"><a class="lineno" href="#L100">100</a></span>
<span id="L101" class="lineno"><a class="lineno" href="#L101">101</a></span>
<span id="L102" class="lineno"><a class="lineno" href="#L102">102</a></span>
<span id="L103" class="lineno"><a class="lineno" href="#L103">103</a></span>
<span id="L104" class="lineno"><a class="lineno" href="#L104">104</a></span>
<span id="L105" class="lineno"><a class="lineno" href="#L105">105</a></span>
<span id="L106" class="lineno"><a class="lineno" href="#L106">106</a></span>
<span id="L107" class="lineno"><a class="lineno" href="#L107">107</a></span>
<span id="L108" class="lineno"><a class="lineno" href="#L108">108</a></span>
<span id="L109" class="lineno"><a class="lineno" href="#L109">109</a></span>
<span id="L110" class="lineno"><a class="lineno" href="#L110">110</a></span>
<span id="L111" class="lineno"><a class="lineno" href="#L111">111</a></span>
<span id="L112" class="lineno"><a class="lineno" href="#L112">112</a></span>
<span id="L113" class="lineno"><a class="lineno" href="#L113">113</a></span>
<span id="L114" class="lineno"><a class="lineno" href="#L114">114</a></span>
<span id="L115" class="lineno"><a class="lineno" href="#L115">115</a></span>
<span id="L116" class="lineno"><a class="lineno" href="#L116">116</a></span>
<span id="L117" class="lineno"><a class="lineno" href="#L117">117</a></span>
<span id="L118" class="lineno"><a class="lineno" href="#L118">118</a></span>
<span id="L119" class="lineno"><a class="lineno" href="#L119">119</a></span>
<span id="L120" class="lineno"><a class="lineno" href="#L120">120</a></span>
<span id="L121" class="lineno"><a class="lineno" href="#L121">121</a></span>
<span id="L122" class="lineno"><a class="lineno" href="#L122">122</a></span>
<span id="L123" class="lineno"><a class="lineno" href="#L123">123</a></span>
<span id="L124" class="lineno"><a class="lineno" href="#L124">124</a></span>
<span id="L125" class="lineno"><a class="lineno" href="#L125">125</a></span>
<span id="L126" class="lineno"><a class="lineno" href="#L126">126</a></span>
<span id="L127" class="lineno"><a class="lineno" href="#L127">127</a></span>
<span id="L128" class="lineno"><a class="lineno" href="#L128">128</a></span>
<span id="L129" class="lineno"><a class="lineno" href="#L129">129</a></span>
<span id="L130" class="lineno"><a class="lineno" href="#L130">130</a></span>
<span id="L131" class="lineno"><a class="lineno" href="#L131">131</a></span>
<span id="L132" class="lineno"><a class="lineno" href="#L132">132</a></span>
<span id="L133" class="lineno"><a class="lineno" href="#L133">133</a></span>
<span id="L134" class="lineno"><a class="lineno" href="#L134">134</a></span>
<span id="L135" class="lineno"><a class="lineno" href="#L135">135</a></span>
<span id="L136" class="lineno"><a class="lineno" href="#L136">136</a></span>
<span id="L137" class="lineno"><a class="lineno" href="#L137">137</a></span>
<span id="L138" class="lineno"><a class="lineno" href="#L138">138</a></span>
<span id="L139" class="lineno"><a class="lineno" href="#L139">139</a></span>
<span id="L140" class="lineno"><a class="lineno" href="#L140">140</a></span>
<span id="L141" class="lineno"><a class="lineno" href="#L141">141</a></span>
<span id="L142" class="lineno"><a class="lineno" href="#L142">142</a></span>
<span id="L143" class="lineno"><a class="lineno" href="#L143">143</a></span>
<span id="L144" class="lineno"><a class="lineno" href="#L144">144</a></span>
<span id="L145" class="lineno"><a class="lineno" href="#L145">145</a></span>
<span id="L146" class="lineno"><a class="lineno" href="#L146">146</a></span>
<span id="L147" class="lineno"><a class="lineno" href="#L147">147</a></span>
<span id="L148" class="lineno"><a class="lineno" href="#L148">148</a></span>
<span id="L149" class="lineno"><a class="lineno" href="#L149">149</a></span>
<span id="L150" class="lineno"><a class="lineno" href="#L150">150</a></span>
<span id="L151" class="lineno"><a class="lineno" href="#L151">151</a></span>
<span id="L152" class="lineno"><a class="lineno" href="#L152">152</a></span>
<span id="L153" class="lineno"><a class="lineno" href="#L153">153</a></span>
<span id="L154" class="lineno"><a class="lineno" href="#L154">154</a></span>
<span id="L155" class="lineno"><a class="lineno" href="#L155">155</a></span>
<span id="L156" class="lineno"><a class="lineno" href="#L156">156</a></span>
<span id="L157" class="lineno"><a class="lineno" href="#L157">157</a></span>
<span id="L158" class="lineno"><a class="lineno" href="#L158">158</a></span>
<span id="L159" class="lineno"><a class="lineno" href="#L159">159</a></span>
<span id="L160" class="lineno"><a class="lineno" href="#L160">160</a></span>
<span id="L161" class="lineno"><a class="lineno" href="#L161">161</a></span>
<span id="L162" class="lineno"><a class="lineno" href="#L162">162</a></span>
<span id="L163" class="lineno"><a class="lineno" href="#L163">163</a></span>
<span id="L164" class="lineno"><a class="lineno" href="#L164">164</a></span>
<span id="L165" class="lineno"><a class="lineno" href="#L165">165</a></span>
<span id="L166" class="lineno"><a class="lineno" href="#L166">166</a></span>
<span id="L167" class="lineno"><a class="lineno" href="#L167">167</a></span>
<span id="L168" class="lineno"><a class="lineno" href="#L168">168</a></span>
<span id="L169" class="lineno"><a class="lineno" href="#L169">169</a></span>
<span id="L170" class="lineno"><a class="lineno" href="#L170">170</a></span>
<span id="L171" class="lineno"><a class="lineno" href="#L171">171</a></span>
<span id="L172" class="lineno"><a class="lineno" href="#L172">172</a></span>
<span id="L173" class="lineno"><a class="lineno" href="#L173">173</a></span>
<span id="L174" class="lineno"><a class="lineno" href="#L174">174</a></span>
<span id="L175" class="lineno"><a class="lineno" href="#L175">175</a></span>
<span id="L176" class="lineno"><a class="lineno" href="#L176">176</a></span>
<span id="L177" class="lineno"><a class="lineno" href="#L177">177</a></span>
<span id="L178" class="lineno"><a class="lineno" href="#L178">178</a></span>
<span id="L179" class="lineno"><a class="lineno" href="#L179">179</a></span>
<span id="L180" class="lineno"><a class="lineno" href="#L180">180</a></span>
<span id="L181" class="lineno"><a class="lineno" href="#L181">181</a></span>
<span id="L182" class="lineno"><a class="lineno" href="#L182">182</a></span>
<span id="L183" class="lineno"><a class="lineno" href="#L183">183</a></span>
<span id="L184" class="lineno"><a class="lineno" href="#L184">184</a></span>
<span id="L185" class="lineno"><a class="lineno" href="#L185">185</a></span>
<span id="L186" class="lineno"><a class="lineno" href="#L186">186</a></span>
<span id="L187" class="lineno"><a class="lineno" href="#L187">187</a></span>
<span id="L188" class="lineno"><a class="lineno" href="#L188">188</a></span>
<span id="L189" class="lineno"><a class="lineno" href="#L189">189</a></span>
<span id="L190" class="lineno"><a class="lineno" href="#L190">190</a></span>
<span id="L191" class="lineno"><a class="lineno" href="#L191">191</a></span>
<span id="L192" class="lineno"><a class="lineno" href="#L192">192</a></span>
<span id="L193" class="lineno"><a class="lineno" href="#L193">193</a></span>
<span id="L194" class="lineno"><a class="lineno" href="#L194">194</a></span>
<span id="L195" class="lineno"><a class="lineno" href="#L195">195</a></span>
<span id="L196" class="lineno"><a class="lineno" href="#L196">196</a></span>
<span id="L197" class="lineno"><a class="lineno" href="#L197">197</a></span>
<span id="L198" class="lineno"><a class="lineno" href="#L198">198</a></span>
<span id="L199" class="lineno"><a class="lineno" href="#L199">199</a></span>
<span id="L200" class="lineno"><a class="lineno" href="#L200">200</a></span>
<span id="L201" class="lineno"><a class="lineno" href="#L201">201</a></span>
<span id="L202" class="lineno"><a class="lineno" href="#L202">202</a></span>
<span id="L203" class="lineno"><a class="lineno" href="#L203">203</a></span>
<span id="L204" class="lineno"><a class="lineno" href="#L204">204</a></span>
<span id="L205" class="lineno"><a class="lineno" href="#L205">205</a></span>
<span id="L206" class="lineno"><a class="lineno" href="#L206">206</a></span>
<span id="L207" class="lineno"><a class="lineno" href="#L207">207</a></span>
<span id="L208" class="lineno"><a class="lineno" href="#L208">208</a></span>
<span id="L209" class="lineno"><a class="lineno" href="#L209">209</a></span>
<span id="L210" class="lineno"><a class="lineno" href="#L210">210</a></span>
<span id="L211" class="lineno"><a class="lineno" href="#L211">211</a></span>
<span id="L212" class="lineno"><a class="lineno" href="#L212">212</a></span>
<span id="L213" class="lineno"><a class="lineno" href="#L213">213</a></span>
<span id="L214" class="lineno"><a class="lineno" href="#L214">214</a></span>
<span id="L215" class="lineno"><a class="lineno" href="#L215">215</a></span>
<span id="L216" class="lineno"><a class="lineno" href="#L216">216</a></span>
<span id="L217" class="lineno"><a class="lineno" href="#L217">217</a></span>
<span id="L218" class="lineno"><a class="lineno" href="#L218">218</a></span>
<span id="L219" class="lineno"><a class="lineno" href="#L219">219</a></span>
<span id="L220" class="lineno"><a class="lineno" href="#L220">220</a></span>
<span id="L221" class="lineno"><a class="lineno" href="#L221">221</a></span>
<span id="L222" class="lineno"><a class="lineno" href="#L222">222</a></span>
<span id="L223" class="lineno"><a class="lineno" href="#L223">223</a></span>
<span id="L224" class="lineno"><a class="lineno" href="#L224">224</a></span>
<span id="L225" class="lineno"><a class="lineno" href="#L225">225</a></span>
<span id="L226" class="lineno"><a class="lineno" href="#L226">226</a></span>
<span id="L227" class="lineno"><a class="lineno" href="#L227">227</a></span>
<span id="L228" class="lineno"><a class="lineno" href="#L228">228</a></span>
<span id="L229" class="lineno"><a class="lineno" href="#L229">229</a></span>
<span id="L230" class="lineno"><a class="lineno" href="#L230">230</a></span>
<span id="L231" class="lineno"><a class="lineno" href="#L231">231</a></span>
<span id="L232" class="lineno"><a class="lineno" href="#L232">232</a></span>
<span id="L233" class="lineno"><a class="lineno" href="#L233">233</a></span>
<span id="L234" class="lineno"><a class="lineno" href="#L234">234</a></span>
<span id="L235" class="lineno"><a class="lineno" href="#L235">235</a></span>
<span id="L236" class="lineno"><a class="lineno" href="#L236">236</a></span>
<span id="L237" class="lineno"><a class="lineno" href="#L237">237</a></span>
<span id="L238" class="lineno"><a class="lineno" href="#L238">238</a></span>
<span id="L239" class="lineno"><a class="lineno" href="#L239">239</a></span>
<span id="L240" class="lineno"><a class="lineno" href="#L240">240</a></span>
<span id="L241" class="lineno"><a class="lineno" href="#L241">241</a></span>
<span id="L242" class="lineno"><a class="lineno" href="#L242">242</a></span>
<span id="L243" class="lineno"><a class="lineno" href="#L243">243</a></span>
<span id="L244" class="lineno"><a class="lineno" href="#L244">244</a></span>
<span id="L245" class="lineno"><a class="lineno" href="#L245">245</a></span>
<span id="L246" class="lineno"><a class="lineno" href="#L246">246</a></span>
<span id="L247" class="lineno"><a class="lineno" href="#L247">247</a></span>
<span id="L248" class="lineno"><a class="lineno" href="#L248">248</a></span>
<span id="L249" class="lineno"><a class="lineno" href="#L249">249</a></span>
<span id="L250" class="lineno"><a class="lineno" href="#L250">250</a></span>
<span id="L251" class="lineno"><a class="lineno" href="#L251">251</a></span>
<span id="L252" class="lineno"><a class="lineno" href="#L252">252</a></span>
<span id="L253" class="lineno"><a class="lineno" href="#L253">253</a></span>
<span id="L254" class="lineno"><a class="lineno" href="#L254">254</a></span>
<span id="L255" class="lineno"><a class="lineno" href="#L255">255</a></span>
<span id="L256" class="lineno"><a class="lineno" href="#L256">256</a></span>
<span id="L257" class="lineno"><a class="lineno" href="#L257">257</a></span>
<span id="L258" class="lineno"><a class="lineno" href="#L258">258</a></span>
<span id="L259" class="lineno"><a class="lineno" href="#L259">259</a></span>
<span id="L260" class="lineno"><a class="lineno" href="#L260">260</a></span>
<span id="L261" class="lineno"><a class="lineno" href="#L261">261</a></span>
<span id="L262" class="lineno"><a class="lineno" href="#L262">262</a></span>
<span id="L263" class="lineno"><a class="lineno" href="#L263">263</a></span>
<span id="L264" class="lineno"><a class="lineno" href="#L264">264</a></span>
<span id="L265" class="lineno"><a class="lineno" href="#L265">265</a></span>
<span id="L266" class="lineno"><a class="lineno" href="#L266">266</a></span>
<span id="L267" class="lineno"><a class="lineno" href="#L267">267</a></span>
<span id="L268" class="lineno"><a class="lineno" href="#L268">268</a></span>
<span id="L269" class="lineno"><a class="lineno" href="#L269">269</a></span>
<span id="L270" class="lineno"><a class="lineno" href="#L270">270</a></span>
<span id="L271" class="lineno"><a class="lineno" href="#L271">271</a></span>
<span id="L272" class="lineno"><a class="lineno" href="#L272">272</a></span>
<span id="L273" class="lineno"><a class="lineno" href="#L273">273</a></span>
<span id="L274" class="lineno"><a class="lineno" href="#L274">274</a></span>
<span id="L275" class="lineno"><a class="lineno" href="#L275">275</a></span>
<span id="L276" class="lineno"><a class="lineno" href="#L276">276</a></span>
<span id="L277" class="lineno"><a class="lineno" href="#L277">277</a></span>
<span id="L278" class="lineno"><a class="lineno" href="#L278">278</a></span>
<span id="L279" class="lineno"><a class="lineno" href="#L279">279</a></span>
<span id="L280" class="lineno"><a class="lineno" href="#L280">280</a></span>
<span id="L281" class="lineno"><a class="lineno" href="#L281">281</a></span>
<span id="L282" class="lineno"><a class="lineno" href="#L282">282</a></span>
<span id="L283" class="lineno"><a class="lineno" href="#L283">283</a></span>
<span id="L284" class="lineno"><a class="lineno" href="#L284">284</a></span>
<span id="L285" class="lineno"><a class="lineno" href="#L285">285</a></span>
<span id="L286" class="lineno"><a class="lineno" href="#L286">286</a></span>
<span id="L287" class="lineno"><a class="lineno" href="#L287">287</a></span>
<span id="L288" class="lineno"><a class="lineno" href="#L288">288</a></span>
<span id="L289" class="lineno"><a class="lineno" href="#L289">289</a></span>
<span id="L290" class="lineno"><a class="lineno" href="#L290">290</a></span>
<span id="L291" class="lineno"><a class="lineno" href="#L291">291</a></span>
<span id="L292" class="lineno"><a class="lineno" href="#L292">292</a></span>
<span id="L293" class="lineno"><a class="lineno" href="#L293">293</a></span>
<span id="L294" class="lineno"><a class="lineno" href="#L294">294</a></span>
<span id="L295" class="lineno"><a class="lineno" href="#L295">295</a></span>
<span id="L296" class="lineno"><a class="lineno" href="#L296">296</a></span>
<span id="L297" class="lineno"><a class="lineno" href="#L297">297</a></span>
<span id="L298" class="lineno"><a class="lineno" href="#L298">298</a></span>
<span id="L299" class="lineno"><a class="lineno" href="#L299">299</a></span>
<span id="L300" class="lineno"><a class="lineno" href="#L300">300</a></span>
<span id="L301" class="lineno"><a class="lineno" href="#L301">301</a></span>
<span id="L302" class="lineno"><a class="lineno" href="#L302">302</a></span>
<span id="L303" class="lineno"><a class="lineno" href="#L303">303</a></span>
<span id="L304" class="lineno"><a class="lineno" href="#L304">304</a></span>
<span id="L305" class="lineno"><a class="lineno" href="#L305">305</a></span>
<span id="L306" class="lineno"><a class="lineno" href="#L306">306</a></span>
<span id="L307" class="lineno"><a class="lineno" href="#L307">307</a></span>
<span id="L308" class="lineno"><a class="lineno" href="#L308">308</a></span>
<span id="L309" class="lineno"><a class="lineno" href="#L309">309</a></span>
<span id="L310" class="lineno"><a class="lineno" href="#L310">310</a></span>
<span id="L311" class="lineno"><a class="lineno" href="#L311">311</a></span>
<span id="L312" class="lineno"><a class="lineno" href="#L312">312</a></span>
<span id="L313" class="lineno"><a class="lineno" href="#L313">313</a></span>
<span id="L314" class="lineno"><a class="lineno" href="#L314">314</a></span>
<span id="L315" class="lineno"><a class="lineno" href="#L315">315</a></span>
<span id="L316" class="lineno"><a class="lineno" href="#L316">316</a></span>
<span id="L317" class="lineno"><a class="lineno" href="#L317">317</a></span>
<span id="L318" class="lineno"><a class="lineno" href="#L318">318</a></span>
<span id="L319" class="lineno"><a class="lineno" href="#L319">319</a></span>
<span id="L320" class="lineno"><a class="lineno" href="#L320">320</a></span>
<span id="L321" class="lineno"><a class="lineno" href="#L321">321</a></span>
<span id="L322" class="lineno"><a class="lineno" href="#L322">322</a></span>
<span id="L323" class="lineno"><a class="lineno" href="#L323">323</a></span>
<span id="L324" class="lineno"><a class="lineno" href="#L324">324</a></span>
<span id="L325" class="lineno"><a class="lineno" href="#L325">325</a></span>
<span id="L326" class="lineno"><a class="lineno" href="#L326">326</a></span>
<span id="L327" class="lineno"><a class="lineno" href="#L327">327</a></span>
<span id="L328" class="lineno"><a class="lineno" href="#L328">328</a></span>
<span id="L329" class="lineno"><a class="lineno" href="#L329">329</a></span>
<span id="L330" class="lineno"><a class="lineno" href="#L330">330</a></span>
<span id="L331" class="lineno"><a class="lineno" href="#L331">331</a></span>
<span id="L332" class="lineno"><a class="lineno" href="#L332">332</a></span>
<span id="L333" class="lineno"><a class="lineno" href="#L333">333</a></span>
<span id="L334" class="lineno"><a class="lineno" href="#L334">334</a></span>
<span id="L335" class="lineno"><a class="lineno" href="#L335">335</a></span>
<span id="L336" class="lineno"><a class="lineno" href="#L336">336</a></span>
<span id="L337" class="lineno"><a class="lineno" href="#L337">337</a></span>
<span id="L338" class="lineno"><a class="lineno" href="#L338">338</a></span>
<span id="L339" class="lineno"><a class="lineno" href="#L339">339</a></span>
<span id="L340" class="lineno"><a class="lineno" href="#L340">340</a></span>
<span id="L341" class="lineno"><a class="lineno" href="#L341">341</a></span>
<span id="L342" class="lineno"><a class="lineno" href="#L342">342</a></span>
<span id="L343" class="lineno"><a class="lineno" href="#L343">343</a></span>
<span id="L344" class="lineno"><a class="lineno" href="#L344">344</a></span>
<span id="L345" class="lineno"><a class="lineno" href="#L345">345</a></span>
<span id="L346" class="lineno"><a class="lineno" href="#L346">346</a></span>
<span id="L347" class="lineno"><a class="lineno" href="#L347">347</a></span>
<span id="L348" class="lineno"><a class="lineno" href="#L348">348</a></span>
<span id="L349" class="lineno"><a class="lineno" href="#L349">349</a></span>
<span id="L350" class="lineno"><a class="lineno" href="#L350">350</a></span>
<span id="L351" class="lineno"><a class="lineno" href="#L351">351</a></span>
<span id="L352" class="lineno"><a class="lineno" href="#L352">352</a></span>
<span id="L353" class="lineno"><a class="lineno" href="#L353">353</a></span>
<span id="L354" class="lineno"><a class="lineno" href="#L354">354</a></span>
<span id="L355" class="lineno"><a class="lineno" href="#L355">355</a></span>
<span id="L356" class="lineno"><a class="lineno" href="#L356">356</a></span>
<span id="L357" class="lineno"><a class="lineno" href="#L357">357</a></span>
<span id="L358" class="lineno"><a class="lineno" href="#L358">358</a></span>
<span id="L359" class="lineno"><a class="lineno" href="#L359">359</a></span>
<span id="L360" class="lineno"><a class="lineno" href="#L360">360</a></span>
<span id="L361" class="lineno"><a class="lineno" href="#L361">361</a></span>
<span id="L362" class="lineno"><a class="lineno" href="#L362">362</a></span>
<span id="L363" class="lineno"><a class="lineno" href="#L363">363</a></span>
<span id="L364" class="lineno"><a class="lineno" href="#L364">364</a></span>
<span id="L365" class="lineno"><a class="lineno" href="#L365">365</a></span>
<span id="L366" class="lineno"><a class="lineno" href="#L366">366</a></span>
<span id="L367" class="lineno"><a class="lineno" href="#L367">367</a></span>
<span id="L368" class="lineno"><a class="lineno" href="#L368">368</a></span>
<span id="L369" class="lineno"><a class="lineno" href="#L369">369</a></span>
<span id="L370" class="lineno"><a class="lineno" href="#L370">370</a></span>
<span id="L371" class="lineno"><a class="lineno" href="#L371">371</a></span>
<span id="L372" class="lineno"><a class="lineno" href="#L372">372</a></span>
<span id="L373" class="lineno"><a class="lineno" href="#L373">373</a></span>
<span id="L374" class="lineno"><a class="lineno" href="#L374">374</a></span>
<span id="L375" class="lineno"><a class="lineno" href="#L375">375</a></span>
<span id="L376" class="lineno"><a class="lineno" href="#L376">376</a></span>
<span id="L377" class="lineno"><a class="lineno" href="#L377">377</a></span>
<span id="L378" class="lineno"><a class="lineno" href="#L378">378</a></span>
<span id="L379" class="lineno"><a class="lineno" href="#L379">379</a></span>
<span id="L380" class="lineno"><a class="lineno" href="#L380">380</a></span>
<span id="L381" class="lineno"><a class="lineno" href="#L381">381</a></span>
<span id="L382" class="lineno"><a class="lineno" href="#L382">382</a></span>
<span id="L383" class="lineno"><a class="lineno" href="#L383">383</a></span>
<span id="L384" class="lineno"><a class="lineno" href="#L384">384</a></span>
<span id="L385" class="lineno"><a class="lineno" href="#L385">385</a></span>
<span id="L386" class="lineno"><a class="lineno" href="#L386">386</a></span>
<span id="L387" class="lineno"><a class="lineno" href="#L387">387</a></span>
<span id="L388" class="lineno"><a class="lineno" href="#L388">388</a></span>
<span id="L389" class="lineno"><a class="lineno" href="#L389">389</a></span>
<span id="L390" class="lineno"><a class="lineno" href="#L390">390</a></span>
<span id="L391" class="lineno"><a class="lineno" href="#L391">391</a></span>
<span id="L392" class="lineno"><a class="lineno" href="#L392">392</a></span>
<span id="L393" class="lineno"><a class="lineno" href="#L393">393</a></span>
<span id="L394" class="lineno"><a class="lineno" href="#L394">394</a></span>
<span id="L395" class="lineno"><a class="lineno" href="#L395">395</a></span>
<span id="L396" class="lineno"><a class="lineno" href="#L396">396</a></span>
<span id="L397" class="lineno"><a class="lineno" href="#L397">397</a></span>
<span id="L398" class="lineno"><a class="lineno" href="#L398">398</a></span>
<span id="L399" class="lineno"><a class="lineno" href="#L399">399</a></span>
<span id="L400" class="lineno"><a class="lineno" href="#L400">400</a></span>
<span id="L401" class="lineno"><a class="lineno" href="#L401">401</a></span>
<span id="L402" class="lineno"><a class="lineno" href="#L402">402</a></span>
<span id="L403" class="lineno"><a class="lineno" href="#L403">403</a></span>
<span id="L404" class="lineno"><a class="lineno" href="#L404">404</a></span>
<span id="L405" class="lineno"><a class="lineno" href="#L405">405</a></span>
<span id="L406" class="lineno"><a class="lineno" href="#L406">406</a></span>
<span id="L407" class="lineno"><a class="lineno" href="#L407">407</a></span>
<span id="L408" class="lineno"><a class="lineno" href="#L408">408</a></span>
<span id="L409" class="lineno"><a class="lineno" href="#L409">409</a></span>
<span id="L410" class="lineno"><a class="lineno" href="#L410">410</a></span>
<span id="L411" class="lineno"><a class="lineno" href="#L411">411</a></span>
<span id="L412" class="lineno"><a class="lineno" href="#L412">412</a></span>
<span id="L413" class="lineno"><a class="lineno" href="#L413">413</a></span>
<span id="L414" class="lineno"><a class="lineno" href="#L414">414</a></span>
<span id="L415" class="lineno"><a class="lineno" href="#L415">415</a></span>
<span id="L416" class="lineno"><a class="lineno" href="#L416">416</a></span>
<span id="L417" class="lineno"><a class="lineno" href="#L417">417</a></span>
<span id="L418" class="lineno"><a class="lineno" href="#L418">418</a></span>
<span id="L419" class="lineno"><a class="lineno" href="#L419">419</a></span>
<span id="L420" class="lineno"><a class="lineno" href="#L420">420</a></span>
<span id="L421" class="lineno"><a class="lineno" href="#L421">421</a></span>
<span id="L422" class="lineno"><a class="lineno" href="#L422">422</a></span>
<span id="L423" class="lineno"><a class="lineno" href="#L423">423</a></span>
<span id="L424" class="lineno"><a class="lineno" href="#L424">424</a></span>
<span id="L425" class="lineno"><a class="lineno" href="#L425">425</a></span>
<span id="L426" class="lineno"><a class="lineno" href="#L426">426</a></span>
<span id="L427" class="lineno"><a class="lineno" href="#L427">427</a></span>
<span id="L428" class="lineno"><a class="lineno" href="#L428">428</a></span>
<span id="L429" class="lineno"><a class="lineno" href="#L429">429</a></span>
<span id="L430" class="lineno"><a class="lineno" href="#L430">430</a></span>
<span id="L431" class="lineno"><a class="lineno" href="#L431">431</a></span>
<span id="L432" class="lineno"><a class="lineno" href="#L432">432</a></span>
<span id="L433" class="lineno"><a class="lineno" href="#L433">433</a></span>
<span id="L434" class="lineno"><a class="lineno" href="#L434">434</a></span>
<span id="L435" class="lineno"><a class="lineno" href="#L435">435</a></span>
<span id="L436" class="lineno"><a class="lineno" href="#L436">436</a></span>
<span id="L437" class="lineno"><a class="lineno" href="#L437">437</a></span>
<span id="L438" class="lineno"><a class="lineno" href="#L438">438</a></span>
<span id="L439" class="lineno"><a class="lineno" href="#L439">439</a></span>
<span id="L440" class="lineno"><a class="lineno" href="#L440">440</a></span>
<span id="L441" class="lineno"><a class="lineno" href="#L441">441</a></span>
<span id="L442" class="lineno"><a class="lineno" href="#L442">442</a></span>
<span id="L443" class="lineno"><a class="lineno" href="#L443">443</a></span>
<span id="L444" class="lineno"><a class="lineno" href="#L444">444</a></span>
<span id="L445" class="lineno"><a class="lineno" href="#L445">445</a></span>
<span id="L446" class="lineno"><a class="lineno" href="#L446">446</a></span>
<span id="L447" class="lineno"><a class="lineno" href="#L447">447</a></span>
<span id="L448" class="lineno"><a class="lineno" href="#L448">448</a></span>
<span id="L449" class="lineno"><a class="lineno" href="#L449">449</a></span>
<span id="L450" class="lineno"><a class="lineno" href="#L450">450</a></span>
<span id="L451" class="lineno"><a class="lineno" href="#L451">451</a></span>
<span id="L452" class="lineno"><a class="lineno" href="#L452">452</a></span>
<span id="L453" class="lineno"><a class="lineno" href="#L453">453</a></span>
<span id="L454" class="lineno"><a class="lineno" href="#L454">454</a></span>
<span id="L455" class="lineno"><a class="lineno" href="#L455">455</a></span>
<span id="L456" class="lineno"><a class="lineno" href="#L456">456</a></span>
<span id="L457" class="lineno"><a class="lineno" href="#L457">457</a></span>
<span id="L458" class="lineno"><a class="lineno" href="#L458">458</a></span>
<span id="L459" class="lineno"><a class="lineno" href="#L459">459</a></span>
<span id="L460" class="lineno"><a class="lineno" href="#L460">460</a></span>
<span id="L461" class="lineno"><a class="lineno" href="#L461">461</a></span>
<span id="L462" class="lineno"><a class="lineno" href="#L462">462</a></span>
<span id="L463" class="lineno"><a class="lineno" href="#L463">463</a></span>
<span id="L464" class="lineno"><a class="lineno" href="#L464">464</a></span>
<span id="L465" class="lineno"><a class="lineno" href="#L465">465</a></span>
<span id="L466" class="lineno"><a class="lineno" href="#L466">466</a></span>
<span id="L467" class="lineno"><a class="lineno" href="#L467">467</a></span>
<span id="L468" class="lineno"><a class="lineno" href="#L468">468</a></span>
<span id="L469" class="lineno"><a class="lineno" href="#L469">469</a></span>
<span id="L470" class="lineno"><a class="lineno" href="#L470">470</a></span>
<span id="L471" class="lineno"><a class="lineno" href="#L471">471</a></span>
<span id="L472" class="lineno"><a class="lineno" href="#L472">472</a></span>
<span id="L473" class="lineno"><a class="lineno" href="#L473">473</a></span>
<span id="L474" class="lineno"><a class="lineno" href="#L474">474</a></span>
<span id="L475" class="lineno"><a class="lineno" href="#L475">475</a></span>
<span id="L476" class="lineno"><a class="lineno" href="#L476">476</a></span>
<span id="L477" class="lineno"><a class="lineno" href="#L477">477</a></span>
<span id="L478" class="lineno"><a class="lineno" href="#L478">478</a></span>
<span id="L479" class="lineno"><a class="lineno" href="#L479">479</a></span>
<span id="L480" class="lineno"><a class="lineno" href="#L480">480</a></span>
<span id="L481" class="lineno"><a class="lineno" href="#L481">481</a></span>
<span id="L482" class="lineno"><a class="lineno" href="#L482">482</a></span>
<span id="L483" class="lineno"><a class="lineno" href="#L483">483</a></span>
<span id="L484" class="lineno"><a class="lineno" href="#L484">484</a></span>
<span id="L485" class="lineno"><a class="lineno" href="#L485">485</a></span>
<span id="L486" class="lineno"><a class="lineno" href="#L486">486</a></span>
<span id="L487" class="lineno"><a class="lineno" href="#L487">487</a></span>
<span id="L488" class="lineno"><a class="lineno" href="#L488">488</a></span>
<span id="L489" class="lineno"><a class="lineno" href="#L489">489</a></span>
<span id="L490" class="lineno"><a class="lineno" href="#L490">490</a></span>
<span id="L491" class="lineno"><a class="lineno" href="#L491">491</a></span>
<span id="L492" class="lineno"><a class="lineno" href="#L492">492</a></span>
<span id="L493" class="lineno"><a class="lineno" href="#L493">493</a></span>
<span id="L494" class="lineno"><a class="lineno" href="#L494">494</a></span>
<span id="L495" class="lineno"><a class="lineno" href="#L495">495</a></span>
<span id="L496" class="lineno"><a class="lineno" href="#L496">496</a></span>
<span id="L497" class="lineno"><a class="lineno" href="#L497">497</a></span>
<span id="L498" class="lineno"><a class="lineno" href="#L498">498</a></span>
<span id="L499" class="lineno"><a class="lineno" href="#L499">499</a></span>
<span id="L500" class="lineno"><a class="lineno" href="#L500">500</a></span>
<span id="L501" class="lineno"><a class="lineno" href="#L501">501</a></span>
<span id="L502" class="lineno"><a class="lineno" href="#L502">502</a></span>
<span id="L503" class="lineno"><a class="lineno" href="#L503">503</a></span>
<span id="L504" class="lineno"><a class="lineno" href="#L504">504</a></span>
<span id="L505" class="lineno"><a class="lineno" href="#L505">505</a></span>
<span id="L506" class="lineno"><a class="lineno" href="#L506">506</a></span>
<span id="L507" class="lineno"><a class="lineno" href="#L507">507</a></span>
<span id="L508" class="lineno"><a class="lineno" href="#L508">508</a></span>
<span id="L509" class="lineno"><a class="lineno" href="#L509">509</a></span>
<span id="L510" class="lineno"><a class="lineno" href="#L510">510</a></span>
<span id="L511" class="lineno"><a class="lineno" href="#L511">511</a></span>
<span id="L512" class="lineno"><a class="lineno" href="#L512">512</a></span>
<span id="L513" class="lineno"><a class="lineno" href="#L513">513</a></span>
<span id="L514" class="lineno"><a class="lineno" href="#L514">514</a></span>
<span id="L515" class="lineno"><a class="lineno" href="#L515">515</a></span>
<span id="L516" class="lineno"><a class="lineno" href="#L516">516</a></span>
<span id="L517" class="lineno"><a class="lineno" href="#L517">517</a></span>
<span id="L518" class="lineno"><a class="lineno" href="#L518">518</a></span>
<span id="L519" class="lineno"><a class="lineno" href="#L519">519</a></span>
<span id="L520" class="lineno"><a class="lineno" href="#L520">520</a></span>
<span id="L521" class="lineno"><a class="lineno" href="#L521">521</a></span>
<span id="L522" class="lineno"><a class="lineno" href="#L522">522</a></span>
<span id="L523" class="lineno"><a class="lineno" href="#L523">523</a></span>
<span id="L524" class="lineno"><a class="lineno" href="#L524">524</a></span>
<span id="L525" class="lineno"><a class="lineno" href="#L525">525</a></span>
<span id="L526" class="lineno"><a class="lineno" href="#L526">526</a></span>
<span id="L527" class="lineno"><a class="lineno" href="#L527">527</a></span>
<span id="L528" class="lineno"><a class="lineno" href="#L528">528</a></span>
<span id="L529" class="lineno"><a class="lineno" href="#L529">529</a></span>
<span id="L530" class="lineno"><a class="lineno" href="#L530">530</a></span>
<span id="L531" class="lineno"><a class="lineno" href="#L531">531</a></span>
<span id="L532" class="lineno"><a class="lineno" href="#L532">532</a></span>
<span id="L533" class="lineno"><a class="lineno" href="#L533">533</a></span>
<span id="L534" class="lineno"><a class="lineno" href="#L534">534</a></span>
<span id="L535" class="lineno"><a class="lineno" href="#L535">535</a></span>
<span id="L536" class="lineno"><a class="lineno" href="#L536">536</a></span>
<span id="L537" class="lineno"><a class="lineno" href="#L537">537</a></span>
<span id="L538" class="lineno"><a class="lineno" href="#L538">538</a></span>
<span id="L539" class="lineno"><a class="lineno" href="#L539">539</a></span>
<span id="L540" class="lineno"><a class="lineno" href="#L540">540</a></span>
<span id="L541" class="lineno"><a class="lineno" href="#L541">541</a></span>
<span id="L542" class="lineno"><a class="lineno" href="#L542">542</a></span>
<span id="L543" class="lineno"><a class="lineno" href="#L543">543</a></span>
<span id="L544" class="lineno"><a class="lineno" href="#L544">544</a></span>
<span id="L545" class="lineno"><a class="lineno" href="#L545">545</a></span>
<span id="L546" class="lineno"><a class="lineno" href="#L546">546</a></span>
<span id="L547" class="lineno"><a class="lineno" href="#L547">547</a></span>
<span id="L548" class="lineno"><a class="lineno" href="#L548">548</a></span>
<span id="L549" class="lineno"><a class="lineno" href="#L549">549</a></span>
<span id="L550" class="lineno"><a class="lineno" href="#L550">550</a></span>
<span id="L551" class="lineno"><a class="lineno" href="#L551">551</a></span>
<span id="L552" class="lineno"><a class="lineno" href="#L552">552</a></span>
<span id="L553" class="lineno"><a class="lineno" href="#L553">553</a></span>
<span id="L554" class="lineno"><a class="lineno" href="#L554">554</a></span>
<span id="L555" class="lineno"><a class="lineno" href="#L555">555</a></span>
<span id="L556" class="lineno"><a class="lineno" href="#L556">556</a></span>
<span id="L557" class="lineno"><a class="lineno" href="#L557">557</a></span>
<span id="L558" class="lineno"><a class="lineno" href="#L558">558</a></span>
<span id="L559" class="lineno"><a class="lineno" href="#L559">559</a></span>
<span id="L560" class="lineno"><a class="lineno" href="#L560">560</a></span>
<span id="L561" class="lineno"><a class="lineno" href="#L561">561</a></span>
<span id="L562" class="lineno"><a class="lineno" href="#L562">562</a></span>
<span id="L563" class="lineno"><a class="lineno" href="#L563">563</a></span>
<span id="L564" class="lineno"><a class="lineno" href="#L564">564</a></span>
<span id="L565" class="lineno"><a class="lineno" href="#L565">565</a></span>
<span id="L566" class="lineno"><a class="lineno" href="#L566">566</a></span>
<span id="L567" class="lineno"><a class="lineno" href="#L567">567</a></span>
<span id="L568" class="lineno"><a class="lineno" href="#L568">568</a></span>
<span id="L569" class="lineno"><a class="lineno" href="#L569">569</a></span>
<span id="L570" class="lineno"><a class="lineno" href="#L570">570</a></span>
<span id="L571" class="lineno"><a class="lineno" href="#L571">571</a></span>
<span id="L572" class="lineno"><a class="lineno" href="#L572">572</a></span>
<span id="L573" class="lineno"><a class="lineno" href="#L573">573</a></span>
<span id="L574" class="lineno"><a class="lineno" href="#L574">574</a></span>
<span id="L575" class="lineno"><a class="lineno" href="#L575">575</a></span>
<span id="L576" class="lineno"><a class="lineno" href="#L576">576</a></span>
<span id="L577" class="lineno"><a class="lineno" href="#L577">577</a></span>
<span id="L578" class="lineno"><a class="lineno" href="#L578">578</a></span>
<span id="L579" class="lineno"><a class="lineno" href="#L579">579</a></span>
<span id="L580" class="lineno"><a class="lineno" href="#L580">580</a></span>
<span id="L581" class="lineno"><a class="lineno" href="#L581">581</a></span>
<span id="L582" class="lineno"><a class="lineno" href="#L582">582</a></span>
<span id="L583" class="lineno"><a class="lineno" href="#L583">583</a></span>
<span id="L584" class="lineno"><a class="lineno" href="#L584">584</a></span>
<span id="L585" class="lineno"><a class="lineno" href="#L585">585</a></span>
<span id="L586" class="lineno"><a class="lineno" href="#L586">586</a></span>
<span id="L587" class="lineno"><a class="lineno" href="#L587">587</a></span>
<span id="L588" class="lineno"><a class="lineno" href="#L588">588</a></span>
<span id="L589" class="lineno"><a class="lineno" href="#L589">589</a></span>
<span id="L590" class="lineno"><a class="lineno" href="#L590">590</a></span>
<span id="L591" class="lineno"><a class="lineno" href="#L591">591</a></span>
<span id="L592" class="lineno"><a class="lineno" href="#L592">592</a></span>
<span id="L593" class="lineno"><a class="lineno" href="#L593">593</a></span>
<span id="L594" class="lineno"><a class="lineno" href="#L594">594</a></span>
<span id="L595" class="lineno"><a class="lineno" href="#L595">595</a></span>
<span id="L596" class="lineno"><a class="lineno" href="#L596">596</a></span>
<span id="L597" class="lineno"><a class="lineno" href="#L597">597</a></span>
<span id="L598" class="lineno"><a class="lineno" href="#L598">598</a></span>
<span id="L599" class="lineno"><a class="lineno" href="#L599">599</a></span>
<span id="L600" class="lineno"><a class="lineno" href="#L600">600</a></span>
<span id="L601" class="lineno"><a class="lineno" href="#L601">601</a></span>
<span id="L602" class="lineno"><a class="lineno" href="#L602">602</a></span>
<span id="L603" class="lineno"><a class="lineno" href="#L603">603</a></span>
<span id="L604" class="lineno"><a class="lineno" href="#L604">604</a></span>
<span id="L605" class="lineno"><a class="lineno" href="#L605">605</a></span>
<span id="L606" class="lineno"><a class="lineno" href="#L606">606</a></span>
<span id="L607" class="lineno"><a class="lineno" href="#L607">607</a></span>
<span id="L608" class="lineno"><a class="lineno" href="#L608">608</a></span>
<span id="L609" class="lineno"><a class="lineno" href="#L609">609</a></span>
<span id="L610" class="lineno"><a class="lineno" href="#L610">610</a></span>
<span id="L611" class="lineno"><a class="lineno" href="#L611">611</a></span>
<span id="L612" class="lineno"><a class="lineno" href="#L612">612</a></span>
<span id="L613" class="lineno"><a class="lineno" href="#L613">613</a></span>
<span id="L614" class="lineno"><a class="lineno" href="#L614">614</a></span>
<span id="L615" class="lineno"><a class="lineno" href="#L615">615</a></span>
<span id="L616" class="lineno"><a class="lineno" href="#L616">616</a></span>
<span id="L617" class="lineno"><a class="lineno" href="#L617">617</a></span>
<span id="L618" class="lineno"><a class="lineno" href="#L618">618</a></span>
<span id="L619" class="lineno"><a class="lineno" href="#L619">619</a></span>
<span id="L620" class="lineno"><a class="lineno" href="#L620">620</a></span>
<span id="L621" class="lineno"><a class="lineno" href="#L621">621</a></span>
<span id="L622" class="lineno"><a class="lineno" href="#L622">622</a></span>
<span id="L623" class="lineno"><a class="lineno" href="#L623">623</a></span>
<span id="L624" class="lineno"><a class="lineno" href="#L624">624</a></span>
<span id="L625" class="lineno"><a class="lineno" href="#L625">625</a></span>
<span id="L626" class="lineno"><a class="lineno" href="#L626">626</a></span>
<span id="L627" class="lineno"><a class="lineno" href="#L627">627</a></span>
<span id="L628" class="lineno"><a class="lineno" href="#L628">628</a></span>
<span id="L629" class="lineno"><a class="lineno" href="#L629">629</a></span>
<span id="L630" class="lineno"><a class="lineno" href="#L630">630</a></span>
<span id="L631" class="lineno"><a class="lineno" href="#L631">631</a></span>
<span id="L632" class="lineno"><a class="lineno" href="#L632">632</a></span>
<span id="L633" class="lineno"><a class="lineno" href="#L633">633</a></span>
<span id="L634" class="lineno"><a class="lineno" href="#L634">634</a></span>
<span id="L635" class="lineno"><a class="lineno" href="#L635">635</a></span>
<span id="L636" class="lineno"><a class="lineno" href="#L636">636</a></span>
<span id="L637" class="lineno"><a class="lineno" href="#L637">637</a></span>
<span id="L638" class="lineno"><a class="lineno" href="#L638">638</a></span>
<span id="L639" class="lineno"><a class="lineno" href="#L639">639</a></span>
<span id="L640" class="lineno"><a class="lineno" href="#L640">640</a></span>
<span id="L641" class="lineno"><a class="lineno" href="#L641">641</a></span>
<span id="L642" class="lineno"><a class="lineno" href="#L642">642</a></span>
<span id="L643" class="lineno"><a class="lineno" href="#L643">643</a></span>
<span id="L644" class="lineno"><a class="lineno" href="#L644">644</a></span>
<span id="L645" class="lineno"><a class="lineno" href="#L645">645</a></span>
<span id="L646" class="lineno"><a class="lineno" href="#L646">646</a></span>
<span id="L647" class="lineno"><a class="lineno" href="#L647">647</a></span>
<span id="L648" class="lineno"><a class="lineno" href="#L648">648</a></span>
<span id="L649" class="lineno"><a class="lineno" href="#L649">649</a></span>
<span id="L650" class="lineno"><a class="lineno" href="#L650">650</a></span>
<span id="L651" class="lineno"><a class="lineno" href="#L651">651</a></span>
<span id="L652" class="lineno"><a class="lineno" href="#L652">652</a></span>
<span id="L653" class="lineno"><a class="lineno" href="#L653">653</a></span>
<span id="L654" class="lineno"><a class="lineno" href="#L654">654</a></span>
<span id="L655" class="lineno"><a class="lineno" href="#L655">655</a></span>
<span id="L656" class="lineno"><a class="lineno" href="#L656">656</a></span>
<span id="L657" class="lineno"><a class="lineno" href="#L657">657</a></span>
<span id="L658" class="lineno"><a class="lineno" href="#L658">658</a></span>
<span id="L659" class="lineno"><a class="lineno" href="#L659">659</a></span>
<span id="L660" class="lineno"><a class="lineno" href="#L660">660</a></span>
<span id="L661" class="lineno"><a class="lineno" href="#L661">661</a></span>
<span id="L662" class="lineno"><a class="lineno" href="#L662">662</a></span>
<span id="L663" class="lineno"><a class="lineno" href="#L663">663</a></span>
<span id="L664" class="lineno"><a class="lineno" href="#L664">664</a></span>
<span id="L665" class="lineno"><a class="lineno" href="#L665">665</a></span>
<span id="L666" class="lineno"><a class="lineno" href="#L666">666</a></span>
<span id="L667" class="lineno"><a class="lineno" href="#L667">667</a></span>
<span id="L668" class="lineno"><a class="lineno" href="#L668">668</a></span>
<span id="L669" class="lineno"><a class="lineno" href="#L669">669</a></span>
<span id="L670" class="lineno"><a class="lineno" href="#L670">670</a></span>
<span id="L671" class="lineno"><a class="lineno" href="#L671">671</a></span>
<span id="L672" class="lineno"><a class="lineno" href="#L672">672</a></span>
<span id="L673" class="lineno"><a class="lineno" href="#L673">673</a></span>
<span id="L674" class="lineno"><a class="lineno" href="#L674">674</a></span>
<span id="L675" class="lineno"><a class="lineno" href="#L675">675</a></span>
<span id="L676" class="lineno"><a class="lineno" href="#L676">676</a></span>
<span id="L677" class="lineno"><a class="lineno" href="#L677">677</a></span>
<span id="L678" class="lineno"><a class="lineno" href="#L678">678</a></span>
<span id="L679" class="lineno"><a class="lineno" href="#L679">679</a></span>
<span id="L680" class="lineno"><a class="lineno" href="#L680">680</a></span>
<span id="L681" class="lineno"><a class="lineno" href="#L681">681</a></span>
<span id="L682" class="lineno"><a class="lineno" href="#L682">682</a></span>
<span id="L683" class="lineno"><a class="lineno" href="#L683">683</a></span>
<span id="L684" class="lineno"><a class="lineno" href="#L684">684</a></span>
<span id="L685" class="lineno"><a class="lineno" href="#L685">685</a></span>
<span id="L686" class="lineno"><a class="lineno" href="#L686">686</a></span>
<span id="L687" class="lineno"><a class="lineno" href="#L687">687</a></span>
<span id="L688" class="lineno"><a class="lineno" href="#L688">688</a></span>
<span id="L689" class="lineno"><a class="lineno" href="#L689">689</a></span>
<span id="L690" class="lineno"><a class="lineno" href="#L690">690</a></span>
<span id="L691" class="lineno"><a class="lineno" href="#L691">691</a></span>
<span id="L692" class="lineno"><a class="lineno" href="#L692">692</a></span>
<span id="L693" class="lineno"><a class="lineno" href="#L693">693</a></span>
<span id="L694" class="lineno"><a class="lineno" href="#L694">694</a></span>
<span id="L695" class="lineno"><a class="lineno" href="#L695">695</a></span>
<span id="L696" class="lineno"><a class="lineno" href="#L696">696</a></span>
<span id="L697" class="lineno"><a class="lineno" href="#L697">697</a></span>
<span id="L698" class="lineno"><a class="lineno" href="#L698">698</a></span>
<span id="L699" class="lineno"><a class="lineno" href="#L699">699</a></span>
<span id="L700" class="lineno"><a class="lineno" href="#L700">700</a></span>
<span id="L701" class="lineno"><a class="lineno" href="#L701">701</a></span>
<span id="L702" class="lineno"><a class="lineno" href="#L702">702</a></span>
<span id="L703" class="lineno"><a class="lineno" href="#L703">703</a></span>
<span id="L704" class="lineno"><a class="lineno" href="#L704">704</a></span>
<span id="L705" class="lineno"><a class="lineno" href="#L705">705</a></span>
<span id="L706" class="lineno"><a class="lineno" href="#L706">706</a></span>
<span id="L707" class="lineno"><a class="lineno" href="#L707">707</a></span>
<span id="L708" class="lineno"><a class="lineno" href="#L708">708</a></span>
<span id="L709" class="lineno"><a class="lineno" href="#L709">709</a></span>
<span id="L710" class="lineno"><a class="lineno" href="#L710">710</a></span>
<span id="L711" class="lineno"><a class="lineno" href="#L711">711</a></span>
<span id="L712" class="lineno"><a class="lineno" href="#L712">712</a></span>
<span id="L713" class="lineno"><a class="lineno" href="#L713">713</a></span>
<span id="L714" class="lineno"><a class="lineno" href="#L714">714</a></span>
<span id="L715" class="lineno"><a class="lineno" href="#L715">715</a></span>
<span id="L716" class="lineno"><a class="lineno" href="#L716">716</a></span>
<span id="L717" class="lineno"><a class="lineno" href="#L717">717</a></span>
<span id="L718" class="lineno"><a class="lineno" href="#L718">718</a></span>
<span id="L719" class="lineno"><a class="lineno" href="#L719">719</a></span>
<span id="L720" class="lineno"><a class="lineno" href="#L720">720</a></span>
<span id="L721" class="lineno"><a class="lineno" href="#L721">721</a></span>
<span id="L722" class="lineno"><a class="lineno" href="#L722">722</a></span>
<span id="L723" class="lineno"><a class="lineno" href="#L723">723</a></span>
<span id="L724" class="lineno"><a class="lineno" href="#L724">724</a></span>
<span id="L725" class="lineno"><a class="lineno" href="#L725">725</a></span>
<span id="L726" class="lineno"><a class="lineno" href="#L726">726</a></span>
<span id="L727" class="lineno"><a class="lineno" href="#L727">727</a></span>
<span id="L728" class="lineno"><a class="lineno" href="#L728">728</a></span>
<span id="L729" class="lineno"><a class="lineno" href="#L729">729</a></span>
<span id="L730" class="lineno"><a class="lineno" href="#L730">730</a></span>
<span id="L731" class="lineno"><a class="lineno" href="#L731">731</a></span>
<span id="L732" class="lineno"><a class="lineno" href="#L732">732</a></span>
<span id="L733" class="lineno"><a class="lineno" href="#L733">733</a></span>
<span id="L734" class="lineno"><a class="lineno" href="#L734">734</a></span>
<span id="L735" class="lineno"><a class="lineno" href="#L735">735</a></span>
<span id="L736" class="lineno"><a class="lineno" href="#L736">736</a></span>
<span id="L737" class="lineno"><a class="lineno" href="#L737">737</a></span>
<span id="L738" class="lineno"><a class="lineno" href="#L738">738</a></span>
<span id="L739" class="lineno"><a class="lineno" href="#L739">739</a></span>
<span id="L740" class="lineno"><a class="lineno" href="#L740">740</a></span>
<span id="L741" class="lineno"><a class="lineno" href="#L741">741</a></span>
<span id="L742" class="lineno"><a class="lineno" href="#L742">742</a></span>
<span id="L743" class="lineno"><a class="lineno" href="#L743">743</a></span>
<span id="L744" class="lineno"><a class="lineno" href="#L744">744</a></span>
<span id="L745" class="lineno"><a class="lineno" href="#L745">745</a></span>
<span id="L746" class="lineno"><a class="lineno" href="#L746">746</a></span>
<span id="L747" class="lineno"><a class="lineno" href="#L747">747</a></span>
<span id="L748" class="lineno"><a class="lineno" href="#L748">748</a></span>
<span id="L749" class="lineno"><a class="lineno" href="#L749">749</a></span>
<span id="L750" class="lineno"><a class="lineno" href="#L750">750</a></span>
<span id="L751" class="lineno"><a class="lineno" href="#L751">751</a></span>
<span id="L752" class="lineno"><a class="lineno" href="#L752">752</a></span>
<span id="L753" class="lineno"><a class="lineno" href="#L753">753</a></span>
<span id="L754" class="lineno"><a class="lineno" href="#L754">754</a></span>
<span id="L755" class="lineno"><a class="lineno" href="#L755">755</a></span>
<span id="L756" class="lineno"><a class="lineno" href="#L756">756</a></span>
<span id="L757" class="lineno"><a class="lineno" href="#L757">757</a></span>
<span id="L758" class="lineno"><a class="lineno" href="#L758">758</a></span>
<span id="L759" class="lineno"><a class="lineno" href="#L759">759</a></span>
<span id="L760" class="lineno"><a class="lineno" href="#L760">760</a></span>
<span id="L761" class="lineno"><a class="lineno" href="#L761">761</a></span>
<span id="L762" class="lineno"><a class="lineno" href="#L762">762</a></span>
<span id="L763" class="lineno"><a class="lineno" href="#L763">763</a></span>
<span id="L764" class="lineno"><a class="lineno" href="#L764">764</a></span>
<span id="L765" class="lineno"><a class="lineno" href="#L765">765</a></span>
<span id="L766" class="lineno"><a class="lineno" href="#L766">766</a></span>
<span id="L767" class="lineno"><a class="lineno" href="#L767">767</a></span>
<span id="L768" class="lineno"><a class="lineno" href="#L768">768</a></span>
<span id="L769" class="lineno"><a class="lineno" href="#L769">769</a></span>
<span id="L770" class="lineno"><a class="lineno" href="#L770">770</a></span>
<span id="L771" class="lineno"><a class="lineno" href="#L771">771</a></span>
<span id="L772" class="lineno"><a class="lineno" href="#L772">772</a></span>
<span id="L773" class="lineno"><a class="lineno" href="#L773">773</a></span>
<span id="L774" class="lineno"><a class="lineno" href="#L774">774</a></span>
<span id="L775" class="lineno"><a class="lineno" href="#L775">775</a></span>
<span id="L776" class="lineno"><a class="lineno" href="#L776">776</a></span>
<span id="L777" class="lineno"><a class="lineno" href="#L777">777</a></span>
<span id="L778" class="lineno"><a class="lineno" href="#L778">778</a></span>
<span id="L779" class="lineno"><a class="lineno" href="#L779">779</a></span>
<span id="L780" class="lineno"><a class="lineno" href="#L780">780</a></span>
<span id="L781" class="lineno"><a class="lineno" href="#L781">781</a></span>
<span id="L782" class="lineno"><a class="lineno" href="#L782">782</a></span>
<span id="L783" class="lineno"><a class="lineno" href="#L783">783</a></span>
<span id="L784" class="lineno"><a class="lineno" href="#L784">784</a></span>
<span id="L785" class="lineno"><a class="lineno" href="#L785">785</a></span>
<span id="L786" class="lineno"><a class="lineno" href="#L786">786</a></span>
<span id="L787" class="lineno"><a class="lineno" href="#L787">787</a></span>
<span id="L788" class="lineno"><a class="lineno" href="#L788">788</a></span>
<span id="L789" class="lineno"><a class="lineno" href="#L789">789</a></span>
<span id="L790" class="lineno"><a class="lineno" href="#L790">790</a></span>
<span id="L791" class="lineno"><a class="lineno" href="#L791">791</a></span>
<span id="L792" class="lineno"><a class="lineno" href="#L792">792</a></span>
<span id="L793" class="lineno"><a class="lineno" href="#L793">793</a></span>
<span id="L794" class="lineno"><a class="lineno" href="#L794">794</a></span>
<span id="L795" class="lineno"><a class="lineno" href="#L795">795</a></span>
<span id="L796" class="lineno"><a class="lineno" href="#L796">796</a></span>
<span id="L797" class="lineno"><a class="lineno" href="#L797">797</a></span>
<span id="L798" class="lineno"><a class="lineno" href="#L798">798</a></span>
<span id="L799" class="lineno"><a class="lineno" href="#L799">799</a></span>
<span id="L800" class="lineno"><a class="lineno" href="#L800">800</a></span>
<span id="L801" class="lineno"><a class="lineno" href="#L801">801</a></span>
<span id="L802" class="lineno"><a class="lineno" href="#L802">802</a></span>
<span id="L803" class="lineno"><a class="lineno" href="#L803">803</a></span>
<span id="L804" class="lineno"><a class="lineno" href="#L804">804</a></span>
<span id="L805" class="lineno"><a class="lineno" href="#L805">805</a></span>
<span id="L806" class="lineno"><a class="lineno" href="#L806">806</a></span>
<span id="L807" class="lineno"><a class="lineno" href="#L807">807</a></span>
<span id="L808" class="lineno"><a class="lineno" href="#L808">808</a></span>
<span id="L809" class="lineno"><a class="lineno" href="#L809">809</a></span>
<span id="L810" class="lineno"><a class="lineno" href="#L810">810</a></span>
<span id="L811" class="lineno"><a class="lineno" href="#L811">811</a></span>
<span id="L812" class="lineno"><a class="lineno" href="#L812">812</a></span>
<span id="L813" class="lineno"><a class="lineno" href="#L813">813</a></span>
<span id="L814" class="lineno"><a class="lineno" href="#L814">814</a></span>
<span id="L815" class="lineno"><a class="lineno" href="#L815">815</a></span>
<span id="L816" class="lineno"><a class="lineno" href="#L816">816</a></span>
<span id="L817" class="lineno"><a class="lineno" href="#L817">817</a></span>
<span id="L818" class="lineno"><a class="lineno" href="#L818">818</a></span>
<span id="L819" class="lineno"><a class="lineno" href="#L819">819</a></span>
<span id="L820" class="lineno"><a class="lineno" href="#L820">820</a></span>
<span id="L821" class="lineno"><a class="lineno" href="#L821">821</a></span>
<span id="L822" class="lineno"><a class="lineno" href="#L822">822</a></span>
<span id="L823" class="lineno"><a class="lineno" href="#L823">823</a></span>
<span id="L824" class="lineno"><a class="lineno" href="#L824">824</a></span>
<span id="L825" class="lineno"><a class="lineno" href="#L825">825</a></span>
<span id="L826" class="lineno"><a class="lineno" href="#L826">826</a></span>
<span id="L827" class="lineno"><a class="lineno" href="#L827">827</a></span>
<span id="L828" class="lineno"><a class="lineno" href="#L828">828</a></span>
<span id="L829" class="lineno"><a class="lineno" href="#L829">829</a></span>
<span id="L830" class="lineno"><a class="lineno" href="#L830">830</a></span>
<span id="L831" class="lineno"><a class="lineno" href="#L831">831</a></span>
<span id="L832" class="lineno"><a class="lineno" href="#L832">832</a></span>
<span id="L833" class="lineno"><a class="lineno" href="#L833">833</a></span>
<span id="L834" class="lineno"><a class="lineno" href="#L834">834</a></span>
<span id="L835" class="lineno"><a class="lineno" href="#L835">835</a></span>
<span id="L836" class="lineno"><a class="lineno" href="#L836">836</a></span>
<span id="L837" class="lineno"><a class="lineno" href="#L837">837</a></span>
<span id="L838" class="lineno"><a class="lineno" href="#L838">838</a></span>
<span id="L839" class="lineno"><a class="lineno" href="#L839">839</a></span>
<span id="L840" class="lineno"><a class="lineno" href="#L840">840</a></span>
<span id="L841" class="lineno"><a class="lineno" href="#L841">841</a></span>
<span id="L842" class="lineno"><a class="lineno" href="#L842">842</a></span>
<span id="L843" class="lineno"><a class="lineno" href="#L843">843</a></span>
<span id="L844" class="lineno"><a class="lineno" href="#L844">844</a></span>
<span id="L845" class="lineno"><a class="lineno" href="#L845">845</a></span>
<span id="L846" class="lineno"><a class="lineno" href="#L846">846</a></span>
<span id="L847" class="lineno"><a class="lineno" href="#L847">847</a></span>
<span id="L848" class="lineno"><a class="lineno" href="#L848">848</a></span>
<span id="L849" class="lineno"><a class="lineno" href="#L849">849</a></span>
<span id="L850" class="lineno"><a class="lineno" href="#L850">850</a></span>
<span id="L851" class="lineno"><a class="lineno" href="#L851">851</a></span>
<span id="L852" class="lineno"><a class="lineno" href="#L852">852</a></span>
<span id="L853" class="lineno"><a class="lineno" href="#L853">853</a></span>
<span id="L854" class="lineno"><a class="lineno" href="#L854">854</a></span>
<span id="L855" class="lineno"><a class="lineno" href="#L855">855</a></span>
<span id="L856" class="lineno"><a class="lineno" href="#L856">856</a></span>
<span id="L857" class="lineno"><a class="lineno" href="#L857">857</a></span>
<span id="L858" class="lineno"><a class="lineno" href="#L858">858</a></span>
<span id="L859" class="lineno"><a class="lineno" href="#L859">859</a></span>
<span id="L860" class="lineno"><a class="lineno" href="#L860">860</a></span>
<span id="L861" class="lineno"><a class="lineno" href="#L861">861</a></span>
<span id="L862" class="lineno"><a class="lineno" href="#L862">862</a></span>
<span id="L863" class="lineno"><a class="lineno" href="#L863">863</a></span>
<span id="L864" class="lineno"><a class="lineno" href="#L864">864</a></span>
<span id="L865" class="lineno"><a class="lineno" href="#L865">865</a></span>
<span id="L866" class="lineno"><a class="lineno" href="#L866">866</a></span>
<span id="L867" class="lineno"><a class="lineno" href="#L867">867</a></span>
<span id="L868" class="lineno"><a class="lineno" href="#L868">868</a></span>
<span id="L869" class="lineno"><a class="lineno" href="#L869">869</a></span>
<span id="L870" class="lineno"><a class="lineno" href="#L870">870</a></span>
<span id="L871" class="lineno"><a class="lineno" href="#L871">871</a></span>
<span id="L872" class="lineno"><a class="lineno" href="#L872">872</a></span>
<span id="L873" class="lineno"><a class="lineno" href="#L873">873</a></span>
<span id="L874" class="lineno"><a class="lineno" href="#L874">874</a></span>
<span id="L875" class="lineno"><a class="lineno" href="#L875">875</a></span>
<span id="L876" class="lineno"><a class="lineno" href="#L876">876</a></span>
<span id="L877" class="lineno"><a class="lineno" href="#L877">877</a></span>
<span id="L878" class="lineno"><a class="lineno" href="#L878">878</a></span>
<span id="L879" class="lineno"><a class="lineno" href="#L879">879</a></span>
<span id="L880" class="lineno"><a class="lineno" href="#L880">880</a></span>
<span id="L881" class="lineno"><a class="lineno" href="#L881">881</a></span>
<span id="L882" class="lineno"><a class="lineno" href="#L882">882</a></span>
<span id="L883" class="lineno"><a class="lineno" href="#L883">883</a></span>
<span id="L884" class="lineno"><a class="lineno" href="#L884">884</a></span>
<span id="L885" class="lineno"><a class="lineno" href="#L885">885</a></span>
<span id="L886" class="lineno"><a class="lineno" href="#L886">886</a></span>
<span id="L887" class="lineno"><a class="lineno" href="#L887">887</a></span>
<span id="L888" class="lineno"><a class="lineno" href="#L888">888</a></span>
<span id="L889" class="lineno"><a class="lineno" href="#L889">889</a></span>
<span id="L890" class="lineno"><a class="lineno" href="#L890">890</a></span>
<span id="L891" class="lineno"><a class="lineno" href="#L891">891</a></span>
<span id="L892" class="lineno"><a class="lineno" href="#L892">892</a></span>
<span id="L893" class="lineno"><a class="lineno" href="#L893">893</a></span>
<span id="L894" class="lineno"><a class="lineno" href="#L894">894</a></span>
<span id="L895" class="lineno"><a class="lineno" href="#L895">895</a></span>
<span id="L896" class="lineno"><a class="lineno" href="#L896">896</a></span>
<span id="L897" class="lineno"><a class="lineno" href="#L897">897</a></span>
<span id="L898" class="lineno"><a class="lineno" href="#L898">898</a></span>
<span id="L899" class="lineno"><a class="lineno" href="#L899">899</a></span>
<span id="L900" class="lineno"><a class="lineno" href="#L900">900</a></span>
<span id="L901" class="lineno"><a class="lineno" href="#L901">901</a></span>
<span id="L902" class="lineno"><a class="lineno" href="#L902">902</a></span>
<span id="L903" class="lineno"><a class="lineno" href="#L903">903</a></span>
<span id="L904" class="lineno"><a class="lineno" href="#L904">904</a></span>
<span id="L905" class="lineno"><a class="lineno" href="#L905">905</a></span>
<span id="L906" class="lineno"><a class="lineno" href="#L906">906</a></span>
<span id="L907" class="lineno"><a class="lineno" href="#L907">907</a></span>
<span id="L908" class="lineno"><a class="lineno" href="#L908">908</a></span>
<span id="L909" class="lineno"><a class="lineno" href="#L909">909</a></span>
<span id="L910" class="lineno"><a class="lineno" href="#L910">910</a></span>
<span id="L911" class="lineno"><a class="lineno" href="#L911">911</a></span>
<span id="L912" class="lineno"><a class="lineno" href="#L912">912</a></span>
<span id="L913" class="lineno"><a class="lineno" href="#L913">913</a></span>
<span id="L914" class="lineno"><a class="lineno" href="#L914">914</a></span>
<span id="L915" class="lineno"><a class="lineno" href="#L915">915</a></span>
<span id="L916" class="lineno"><a class="lineno" href="#L916">916</a></span>
<span id="L917" class="lineno"><a class="lineno" href="#L917">917</a></span>
<span id="L918" class="lineno"><a class="lineno" href="#L918">918</a></span>
<span id="L919" class="lineno"><a class="lineno" href="#L919">919</a></span>
<span id="L920" class="lineno"><a class="lineno" href="#L920">920</a></span>
<span id="L921" class="lineno"><a class="lineno" href="#L921">921</a></span>
<span id="L922" class="lineno"><a class="lineno" href="#L922">922</a></span>
<span id="L923" class="lineno"><a class="lineno" href="#L923">923</a></span>
<span id="L924" class="lineno"><a class="lineno" href="#L924">924</a></span>
<span id="L925" class="lineno"><a class="lineno" href="#L925">925</a></span>
<span id="L926" class="lineno"><a class="lineno" href="#L926">926</a></span>
<span id="L927" class="lineno"><a class="lineno" href="#L927">927</a></span>
<span id="L928" class="lineno"><a class="lineno" href="#L928">928</a></span>
<span id="L929" class="lineno"><a class="lineno" href="#L929">929</a></span>
<span id="L930" class="lineno"><a class="lineno" href="#L930">930</a></span>
<span id="L931" class="lineno"><a class="lineno" href="#L931">931</a></span>
<span id="L932" class="lineno"><a class="lineno" href="#L932">932</a></span>
<span id="L933" class="lineno"><a class="lineno" href="#L933">933</a></span>
<span id="L934" class="lineno"><a class="lineno" href="#L934">934</a></span>
<span id="L935" class="lineno"><a class="lineno" href="#L935">935</a></span>
<span id="L936" class="lineno"><a class="lineno" href="#L936">936</a></span>
<span id="L937" class="lineno"><a class="lineno" href="#L937">937</a></span>
<span id="L938" class="lineno"><a class="lineno" href="#L938">938</a></span>
<span id="L939" class="lineno"><a class="lineno" href="#L939">939</a></span>
<span id="L940" class="lineno"><a class="lineno" href="#L940">940</a></span>
<span id="L941" class="lineno"><a class="lineno" href="#L941">941</a></span>
<span id="L942" class="lineno"><a class="lineno" href="#L942">942</a></span>
<span id="L943" class="lineno"><a class="lineno" href="#L943">943</a></span>
<span id="L944" class="lineno"><a class="lineno" href="#L944">944</a></span>
<span id="L945" class="lineno"><a class="lineno" href="#L945">945</a></span>
<span id="L946" class="lineno"><a class="lineno" href="#L946">946</a></span>
<span id="L947" class="lineno"><a class="lineno" href="#L947">947</a></span>
<span id="L948" class="lineno"><a class="lineno" href="#L948">948</a></span>
<span id="L949" class="lineno"><a class="lineno" href="#L949">949</a></span>
<span id="L950" class="lineno"><a class="lineno" href="#L950">950</a></span>
<span id="L951" class="lineno"><a class="lineno" href="#L951">951</a></span>
<span id="L952" class="lineno"><a class="lineno" href="#L952">952</a></span>
<span id="L953" class="lineno"><a class="lineno" href="#L953">953</a></span>
<span id="L954" class="lineno"><a class="lineno" href="#L954">954</a></span>
<span id="L955" class="lineno"><a class="lineno" href="#L955">955</a></span>
<span id="L956" class="lineno"><a class="lineno" href="#L956">956</a></span>
<span id="L957" class="lineno"><a class="lineno" href="#L957">957</a></span>
<span id="L958" class="lineno"><a class="lineno" href="#L958">958</a></span>
<span id="L959" class="lineno"><a class="lineno" href="#L959">959</a></span>
<span id="L960" class="lineno"><a class="lineno" href="#L960">960</a></span>
<span id="L961" class="lineno"><a class="lineno" href="#L961">961</a></span>
<span id="L962" class="lineno"><a class="lineno" href="#L962">962</a></span>
<span id="L963" class="lineno"><a class="lineno" href="#L963">963</a></span>
<span id="L964" class="lineno"><a class="lineno" href="#L964">964</a></span>
<span id="L965" class="lineno"><a class="lineno" href="#L965">965</a></span>
<span id="L966" class="lineno"><a class="lineno" href="#L966">966</a></span>
<span id="L967" class="lineno"><a class="lineno" href="#L967">967</a></span>
<span id="L968" class="lineno"><a class="lineno" href="#L968">968</a></span>
<span id="L969" class="lineno"><a class="lineno" href="#L969">969</a></span>
<span id="L970" class="lineno"><a class="lineno" href="#L970">970</a></span>
<span id="L971" class="lineno"><a class="lineno" href="#L971">971</a></span>
<span id="L972" class="lineno"><a class="lineno" href="#L972">972</a></span>
<span id="L973" class="lineno"><a class="lineno" href="#L973">973</a></span>
<span id="L974" class="lineno"><a class="lineno" href="#L974">974</a></span>
<span id="L975" class="lineno"><a class="lineno" href="#L975">975</a></span>
<span id="L976" class="lineno"><a class="lineno" href="#L976">976</a></span>
<span id="L977" class="lineno"><a class="lineno" href="#L977">977</a></span>
<span id="L978" class="lineno"><a class="lineno" href="#L978">978</a></span>
<span id="L979" class="lineno"><a class="lineno" href="#L979">979</a></span>
<span id="L980" class="lineno"><a class="lineno" href="#L980">980</a></span>
<span id="L981" class="lineno"><a class="lineno" href="#L981">981</a></span>
<span id="L982" class="lineno"><a class="lineno" href="#L982">982</a></span>
<span id="L983" class="lineno"><a class="lineno" href="#L983">983</a></span>
<span id="L984" class="lineno"><a class="lineno" href="#L984">984</a></span>
<span id="L985" class="lineno"><a class="lineno" href="#L985">985</a></span>
<span id="L986" class="lineno"><a class="lineno" href="#L986">986</a></span>
<span id="L987" class="lineno"><a class="lineno" href="#L987">987</a></span>
<span id="L988" class="lineno"><a class="lineno" href="#L988">988</a></span>
<span id="L989" class="lineno"><a class="lineno" href="#L989">989</a></span>
<span id="L990" class="lineno"><a class="lineno" href="#L990">990</a></span>
<span id="L991" class="lineno"><a class="lineno" href="#L991">991</a></span>
<span id="L992" class="lineno"><a class="lineno" href="#L992">992</a></span>
<span id="L993" class="lineno"><a class="lineno" href="#L993">993</a></span>
<span id="L994" class="lineno"><a class="lineno" href="#L994">994</a></span>
<span id="L995" class="lineno"><a class="lineno" href="#L995">995</a></span>
<span id="L996" class="lineno"><a class="lineno" href="#L996">996</a></span>
<span id="L997" class="lineno"><a class="lineno" href="#L997">997</a></span>
<span id="L998" class="lineno"><a class="lineno" href="#L998">998</a></span>
<span id="L999" class="lineno"><a class="lineno" href="#L999">999</a></span>
<span id="L1000" class="lineno"><a class="lineno" href="#L1000">1000</a></span>
<span id="L1001" class="lineno"><a class="lineno" href="#L1001">1001</a></span>
<span id="L1002" class="lineno"><a class="lineno" href="#L1002">1002</a></span>
<span id="L1003" class="lineno"><a class="lineno" href="#L1003">1003</a></span>
<span id="L1004" class="lineno"><a class="lineno" href="#L1004">1004</a></span>
<span id="L1005" class="lineno"><a class="lineno" href="#L1005">1005</a></span>
<span id="L1006" class="lineno"><a class="lineno" href="#L1006">1006</a></span>
<span id="L1007" class="lineno"><a class="lineno" href="#L1007">1007</a></span>
<span id="L1008" class="lineno"><a class="lineno" href="#L1008">1008</a></span>
<span id="L1009" class="lineno"><a class="lineno" href="#L1009">1009</a></span>
<span id="L1010" class="lineno"><a class="lineno" href="#L1010">1010</a></span>
<span id="L1011" class="lineno"><a class="lineno" href="#L1011">1011</a></span>
<span id="L1012" class="lineno"><a class="lineno" href="#L1012">1012</a></span>
<span id="L1013" class="lineno"><a class="lineno" href="#L1013">1013</a></span>
<span id="L1014" class="lineno"><a class="lineno" href="#L1014">1014</a></span>
<span id="L1015" class="lineno"><a class="lineno" href="#L1015">1015</a></span>
<span id="L1016" class="lineno"><a class="lineno" href="#L1016">1016</a></span>
<span id="L1017" class="lineno"><a class="lineno" href="#L1017">1017</a></span>
<span id="L1018" class="lineno"><a class="lineno" href="#L1018">1018</a></span>
<span id="L1019" class="lineno"><a class="lineno" href="#L1019">1019</a></span>
<span id="L1020" class="lineno"><a class="lineno" href="#L1020">1020</a></span>
<span id="L1021" class="lineno"><a class="lineno" href="#L1021">1021</a></span>
<span id="L1022" class="lineno"><a class="lineno" href="#L1022">1022</a></span>
<span id="L1023" class="lineno"><a class="lineno" href="#L1023">1023</a></span>
<span id="L1024" class="lineno"><a class="lineno" href="#L1024">1024</a></span>
<span id="L1025" class="lineno"><a class="lineno" href="#L1025">1025</a></span>
<span id="L1026" class="lineno"><a class="lineno" href="#L1026">1026</a></span>
<span id="L1027" class="lineno"><a class="lineno" href="#L1027">1027</a></span>
<span id="L1028" class="lineno"><a class="lineno" href="#L1028">1028</a></span>
<span id="L1029" class="lineno"><a class="lineno" href="#L1029">1029</a></span>
<span id="L1030" class="lineno"><a class="lineno" href="#L1030">1030</a></span>
<span id="L1031" class="lineno"><a class="lineno" href="#L1031">1031</a></span>
<span id="L1032" class="lineno"><a class="lineno" href="#L1032">1032</a></span>
<span id="L1033" class="lineno"><a class="lineno" href="#L1033">1033</a></span>
<span id="L1034" class="lineno"><a class="lineno" href="#L1034">1034</a></span>
<span id="L1035" class="lineno"><a class="lineno" href="#L1035">1035</a></span>
<span id="L1036" class="lineno"><a class="lineno" href="#L1036">1036</a></span>
<span id="L1037" class="lineno"><a class="lineno" href="#L1037">1037</a></span>
<span id="L1038" class="lineno"><a class="lineno" href="#L1038">1038</a></span>
<span id="L1039" class="lineno"><a class="lineno" href="#L1039">1039</a></span>
<span id="L1040" class="lineno"><a class="lineno" href="#L1040">1040</a></span>
<span id="L1041" class="lineno"><a class="lineno" href="#L1041">1041</a></span>
<span id="L1042" class="lineno"><a class="lineno" href="#L1042">1042</a></span>
<span id="L1043" class="lineno"><a class="lineno" href="#L1043">1043</a></span>
<span id="L1044" class="lineno"><a class="lineno" href="#L1044">1044</a></span>
<span id="L1045" class="lineno"><a class="lineno" href="#L1045">1045</a></span>
<span id="L1046" class="lineno"><a class="lineno" href="#L1046">1046</a></span>
<span id="L1047" class="lineno"><a class="lineno" href="#L1047">1047</a></span>
<span id="L1048" class="lineno"><a class="lineno" href="#L1048">1048</a></span>
<span id="L1049" class="lineno"><a class="lineno" href="#L1049">1049</a></span>
<span id="L1050" class="lineno"><a class="lineno" href="#L1050">1050</a></span>
<span id="L1051" class="lineno"><a class="lineno" href="#L1051">1051</a></span>
<span id="L1052" class="lineno"><a class="lineno" href="#L1052">1052</a></span>
<span id="L1053" class="lineno"><a class="lineno" href="#L1053">1053</a></span>
<span id="L1054" class="lineno"><a class="lineno" href="#L1054">1054</a></span>
<span id="L1055" class="lineno"><a class="lineno" href="#L1055">1055</a></span>
<span id="L1056" class="lineno"><a class="lineno" href="#L1056">1056</a></span>
<span id="L1057" class="lineno"><a class="lineno" href="#L1057">1057</a></span>
<span id="L1058" class="lineno"><a class="lineno" href="#L1058">1058</a></span>
<span id="L1059" class="lineno"><a class="lineno" href="#L1059">1059</a></span>
<span id="L1060" class="lineno"><a class="lineno" href="#L1060">1060</a></span>
<span id="L1061" class="lineno"><a class="lineno" href="#L1061">1061</a></span>
<span id="L1062" class="lineno"><a class="lineno" href="#L1062">1062</a></span>
<span id="L1063" class="lineno"><a class="lineno" href="#L1063">1063</a></span>
<span id="L1064" class="lineno"><a class="lineno" href="#L1064">1064</a></span>
<span id="L1065" class="lineno"><a class="lineno" href="#L1065">1065</a></span>
<span id="L1066" class="lineno"><a class="lineno" href="#L1066">1066</a></span>
<span id="L1067" class="lineno"><a class="lineno" href="#L1067">1067</a></span>
<span id="L1068" class="lineno"><a class="lineno" href="#L1068">1068</a></span>
<span id="L1069" class="lineno"><a class="lineno" href="#L1069">1069</a></span>
<span id="L1070" class="lineno"><a class="lineno" href="#L1070">1070</a></span>
<span id="L1071" class="lineno"><a class="lineno" href="#L1071">1071</a></span>
<span id="L1072" class="lineno"><a class="lineno" href="#L1072">1072</a></span>
<span id="L1073" class="lineno"><a class="lineno" href="#L1073">1073</a></span>
<span id="L1074" class="lineno"><a class="lineno" href="#L1074">1074</a></span>
<span id="L1075" class="lineno"><a class="lineno" href="#L1075">1075</a></span>
<span id="L1076" class="lineno"><a class="lineno" href="#L1076">1076</a></span>
<span id="L1077" class="lineno"><a class="lineno" href="#L1077">1077</a></span>
<span id="L1078" class="lineno"><a class="lineno" href="#L1078">1078</a></span>
<span id="L1079" class="lineno"><a class="lineno" href="#L1079">1079</a></span>
<span id="L1080" class="lineno"><a class="lineno" href="#L1080">1080</a></span>
<span id="L1081" class="lineno"><a class="lineno" href="#L1081">1081</a></span>
<span id="L1082" class="lineno"><a class="lineno" href="#L1082">1082</a></span>
<span id="L1083" class="lineno"><a class="lineno" href="#L1083">1083</a></span>
<span id="L1084" class="lineno"><a class="lineno" href="#L1084">1084</a></span>
<span id="L1085" class="lineno"><a class="lineno" href="#L1085">1085</a></span>
<span id="L1086" class="lineno"><a class="lineno" href="#L1086">1086</a></span>
<span id="L1087" class="lineno"><a class="lineno" href="#L1087">1087</a></span>
<span id="L1088" class="lineno"><a class="lineno" href="#L1088">1088</a></span>
<span id="L1089" class="lineno"><a class="lineno" href="#L1089">1089</a></span>
<span id="L1090" class="lineno"><a class="lineno" href="#L1090">1090</a></span>
<span id="L1091" class="lineno"><a class="lineno" href="#L1091">1091</a></span>
<span id="L1092" class="lineno"><a class="lineno" href="#L1092">1092</a></span>
<span id="L1093" class="lineno"><a class="lineno" href="#L1093">1093</a></span>
<span id="L1094" class="lineno"><a class="lineno" href="#L1094">1094</a></span>
<span id="L1095" class="lineno"><a class="lineno" href="#L1095">1095</a></span>
<span id="L1096" class="lineno"><a class="lineno" href="#L1096">1096</a></span>
<span id="L1097" class="lineno"><a class="lineno" href="#L1097">1097</a></span>
<span id="L1098" class="lineno"><a class="lineno" href="#L1098">1098</a></span>
<span id="L1099" class="lineno"><a class="lineno" href="#L1099">1099</a></span>
<span id="L1100" class="lineno"><a class="lineno" href="#L1100">1100</a></span>
</pre></td>
<td class="table-code"><pre><span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!">Audience Targeting Agent for Google Ads Campaign Optimization.</span>
<span class="line-empty" title="No Anys on this line!">Handles audience research, segmentation, and targeting optimization.</span>
<span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">import asyncio</span>
<span class="line-precise" title="No Anys on this line!">import json</span>
<span class="line-precise" title="No Anys on this line!">from datetime import datetime, timedelta</span>
<span class="line-precise" title="No Anys on this line!">from typing import Any, Dict, List, Optional, Tuple, Union, Set</span>
<span class="line-precise" title="No Anys on this line!">from dataclasses import dataclass, field</span>
<span class="line-precise" title="No Anys on this line!">from enum import Enum</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="No Anys on this line!">import structlog</span>
<span class="line-any" title="No Anys on this line!">from crewai import Agent, Task</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from ..base import BaseAiLexAgent, AgentContext, AgentError</span>
<span class="line-precise" title="No Anys on this line!">from ..tracing import AgentTracer, create_agent_tracer</span>
<span class="line-precise" title="No Anys on this line!">from models.agents import AgentType, AgentConfig</span>
<span class="line-precise" title="No Anys on this line!">from services.google_ads import GoogleAdsService</span>
<span class="line-precise" title="No Anys on this line!">from services.openai_service import OpenAIService</span>
<span class="line-precise" title="No Anys on this line!">from utils.config import settings</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="Any Types on this line: 
Unimported (x4)">logger = structlog.get_logger(__name__)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class AudienceType(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """Types of audience targeting."""</span>
<span class="line-precise" title="No Anys on this line!">    DEMOGRAPHIC = "demographic"</span>
<span class="line-precise" title="No Anys on this line!">    AFFINITY = "affinity"</span>
<span class="line-precise" title="No Anys on this line!">    CUSTOM_INTENT = "custom_intent"</span>
<span class="line-precise" title="No Anys on this line!">    IN_MARKET = "in_market"</span>
<span class="line-precise" title="No Anys on this line!">    REMARKETING = "remarketing"</span>
<span class="line-precise" title="No Anys on this line!">    SIMILAR = "similar"</span>
<span class="line-precise" title="No Anys on this line!">    CUSTOMER_MATCH = "customer_match"</span>
<span class="line-precise" title="No Anys on this line!">    DETAILED_DEMOGRAPHICS = "detailed_demographics"</span>
<span class="line-precise" title="No Anys on this line!">    COMBINED = "combined"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class TargetingStrategy(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """Audience targeting strategies."""</span>
<span class="line-precise" title="No Anys on this line!">    BROAD_REACH = "broad_reach"</span>
<span class="line-precise" title="No Anys on this line!">    PRECISION_TARGETING = "precision_targeting"</span>
<span class="line-precise" title="No Anys on this line!">    LAYERED_TARGETING = "layered_targeting"</span>
<span class="line-precise" title="No Anys on this line!">    EXCLUSION_FOCUSED = "exclusion_focused"</span>
<span class="line-precise" title="No Anys on this line!">    LOOKALIKE_EXPANSION = "lookalike_expansion"</span>
<span class="line-precise" title="No Anys on this line!">    BEHAVIORAL_TARGETING = "behavioral_targeting"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@dataclass</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class AudienceSegment:</span>
<span class="line-empty" title="No Anys on this line!">    """Individual audience segment definition."""</span>
<span class="line-precise" title="No Anys on this line!">    segment_id: str</span>
<span class="line-precise" title="No Anys on this line!">    name: str</span>
<span class="line-precise" title="No Anys on this line!">    audience_type: AudienceType</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    targeting_criteria: Dict[str, Any]</span>
<span class="line-precise" title="No Anys on this line!">    estimated_reach: Optional[int] = None</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x22)
Explicit (x7)">    overlap_analysis: Dict[str, float] = field(default_factory=dict)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x22)
Explicit (x7)">    performance_metrics: Dict[str, float] = field(default_factory=dict)</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    demographic_profile: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x7)
Explicit (x7)">    behavioral_insights: List[str] = field(default_factory=list)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)
Explicit (x7)">    created_at: datetime = field(default_factory=datetime.utcnow)</span>
<span class="line-precise" title="No Anys on this line!">    is_active: bool = True</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@dataclass</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class AudienceInsight:</span>
<span class="line-empty" title="No Anys on this line!">    """Audience performance and behavioral insight."""</span>
<span class="line-precise" title="No Anys on this line!">    insight_id: str</span>
<span class="line-precise" title="No Anys on this line!">    audience_segment_id: str</span>
<span class="line-precise" title="No Anys on this line!">    insight_type: str  # performance, demographic, behavioral, competitive</span>
<span class="line-precise" title="No Anys on this line!">    title: str</span>
<span class="line-precise" title="No Anys on this line!">    description: str</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    supporting_data: Dict[str, Any]</span>
<span class="line-precise" title="No Anys on this line!">    confidence_score: float</span>
<span class="line-precise" title="No Anys on this line!">    actionable_recommendations: List[str]</span>
<span class="line-precise" title="No Anys on this line!">    business_impact: str  # high, medium, low</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)
Explicit (x7)">    created_at: datetime = field(default_factory=datetime.utcnow)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@dataclass</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class TargetingRecommendation:</span>
<span class="line-empty" title="No Anys on this line!">    """Audience targeting optimization recommendation."""</span>
<span class="line-precise" title="No Anys on this line!">    recommendation_id: str</span>
<span class="line-precise" title="No Anys on this line!">    campaign_id: str</span>
<span class="line-precise" title="No Anys on this line!">    recommendation_type: str  # add, remove, modify, exclude</span>
<span class="line-precise" title="No Anys on this line!">    audience_segment: AudienceSegment</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    current_targeting: Optional[Dict[str, Any]] = None</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    recommended_targeting: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x22)
Explicit (x7)">    expected_impact: Dict[str, float] = field(default_factory=dict)</span>
<span class="line-precise" title="No Anys on this line!">    implementation_effort: str = "medium"  # easy, medium, hard</span>
<span class="line-precise" title="No Anys on this line!">    priority: str = "medium"  # critical, high, medium, low</span>
<span class="line-precise" title="No Anys on this line!">    rationale: str = ""</span>
<span class="line-precise" title="No Anys on this line!">    confidence_score: float = 0.7</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)
Explicit (x7)">    created_at: datetime = field(default_factory=datetime.utcnow)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@dataclass</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class AudienceAnalysisReport:</span>
<span class="line-empty" title="No Anys on this line!">    """Comprehensive audience analysis report."""</span>
<span class="line-precise" title="No Anys on this line!">    report_id: str</span>
<span class="line-precise" title="No Anys on this line!">    campaign_ids: List[str]</span>
<span class="line-precise" title="No Anys on this line!">    analysis_period: Tuple[datetime, datetime]</span>
<span class="line-precise" title="No Anys on this line!">    audience_segments: List[AudienceSegment]</span>
<span class="line-precise" title="No Anys on this line!">    audience_insights: List[AudienceInsight]</span>
<span class="line-precise" title="No Anys on this line!">    targeting_recommendations: List[TargetingRecommendation]</span>
<span class="line-precise" title="No Anys on this line!">    overlap_matrix: Dict[str, Dict[str, float]]</span>
<span class="line-precise" title="No Anys on this line!">    performance_comparison: Dict[str, Dict[str, float]]</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    expansion_opportunities: List[Dict[str, Any]]</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    exclusion_recommendations: List[Dict[str, Any]]</span>
<span class="line-precise" title="No Anys on this line!">    budget_allocation_suggestions: Dict[str, float]</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)
Explicit (x7)">    created_at: datetime = field(default_factory=datetime.utcnow)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class AudienceTargetingAgent(BaseAiLexAgent):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    AI agent specialized in Google Ads audience research, targeting, and optimization.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __init__(self, agent_id: str, config: AgentConfig):</span>
<span class="line-precise" title="No Anys on this line!">        super().__init__(</span>
<span class="line-precise" title="No Anys on this line!">            agent_id=agent_id,</span>
<span class="line-precise" title="No Anys on this line!">            name="Audience Targeting Agent",</span>
<span class="line-precise" title="No Anys on this line!">            description="Specialized AI agent for Google Ads audience research, segmentation, targeting optimization, and audience performance analysis",</span>
<span class="line-precise" title="No Anys on this line!">            agent_type=AgentType.AUDIENCE_TARGETING,</span>
<span class="line-precise" title="No Anys on this line!">            config=config</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Initialize services</span>
<span class="line-precise" title="No Anys on this line!">        self.google_ads_service: Optional[GoogleAdsService] = None</span>
<span class="line-precise" title="No Anys on this line!">        self.openai_service: Optional[OpenAIService] = None</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Initialize tracer</span>
<span class="line-precise" title="No Anys on this line!">        self.tracer = create_agent_tracer(self.agent_id, self.name)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Audience analysis parameters</span>
<span class="line-precise" title="No Anys on this line!">        self.targeting_thresholds = {</span>
<span class="line-precise" title="No Anys on this line!">            "min_audience_size": 1000,</span>
<span class="line-precise" title="No Anys on this line!">            "max_audience_size": 10000000,</span>
<span class="line-precise" title="No Anys on this line!">            "performance_significance_threshold": 0.05,</span>
<span class="line-precise" title="No Anys on this line!">            "overlap_threshold": 0.3,  # 30% overlap considered high</span>
<span class="line-precise" title="No Anys on this line!">            "conversion_rate_threshold": 0.02,</span>
<span class="line-precise" title="No Anys on this line!">            "click_through_rate_threshold": 0.02,</span>
<span class="line-precise" title="No Anys on this line!">            "cost_efficiency_threshold": 1.2  # 20% above average</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Demographic categories and weights</span>
<span class="line-precise" title="No Anys on this line!">        self.demographic_weights = {</span>
<span class="line-precise" title="No Anys on this line!">            "age": 0.25,</span>
<span class="line-precise" title="No Anys on this line!">            "gender": 0.15,</span>
<span class="line-precise" title="No Anys on this line!">            "income": 0.20,</span>
<span class="line-precise" title="No Anys on this line!">            "education": 0.15,</span>
<span class="line-precise" title="No Anys on this line!">            "location": 0.25</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Behavioral indicators</span>
<span class="line-precise" title="No Anys on this line!">        self.behavioral_indicators = {</span>
<span class="line-precise" title="No Anys on this line!">            "high_intent": ["recently_searched", "visited_competitor", "price_compared"],</span>
<span class="line-precise" title="No Anys on this line!">            "consideration": ["research_mode", "content_engaged", "time_spent"],</span>
<span class="line-precise" title="No Anys on this line!">            "loyalty": ["repeat_visitor", "long_session", "high_engagement"],</span>
<span class="line-precise" title="No Anys on this line!">            "price_sensitive": ["coupon_seeker", "deal_hunter", "comparison_shopper"]</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Data storage</span>
<span class="line-precise" title="No Anys on this line!">        self.audience_cache: Dict[str, AudienceSegment] = {}</span>
<span class="line-precise" title="No Anys on this line!">        self.insight_cache: Dict[str, List[AudienceInsight]] = {}</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">        self.performance_cache: Dict[str, Dict[str, Any]] = {}</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _custom_initialize(self) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Custom initialization for audience targeting agent."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-empty" title="No Anys on this line!">            # Initialize Google Ads service</span>
<span class="line-precise" title="No Anys on this line!">            if all([</span>
<span class="line-precise" title="No Anys on this line!">                settings.GOOGLE_ADS_DEVELOPER_TOKEN,</span>
<span class="line-precise" title="No Anys on this line!">                settings.GOOGLE_ADS_CLIENT_ID,</span>
<span class="line-precise" title="No Anys on this line!">                settings.GOOGLE_ADS_CLIENT_SECRET,</span>
<span class="line-precise" title="No Anys on this line!">                settings.GOOGLE_ADS_REFRESH_TOKEN</span>
<span class="line-empty" title="No Anys on this line!">            ]):</span>
<span class="line-precise" title="No Anys on this line!">                self.google_ads_service = GoogleAdsService()</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Initialize OpenAI service</span>
<span class="line-precise" title="No Anys on this line!">            if settings.OPENAI_API_KEY:</span>
<span class="line-precise" title="No Anys on this line!">                self.openai_service = OpenAIService()</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Audience targeting agent initialized",</span>
<span class="line-precise" title="No Anys on this line!">                has_google_ads=bool(self.google_ads_service),</span>
<span class="line-precise" title="No Anys on this line!">                has_openai=bool(self.openai_service),</span>
<span class="line-precise" title="No Anys on this line!">                targeting_thresholds=len(self.targeting_thresholds)</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-precise" title="No Anys on this line!">            raise AgentError(f"Failed to initialize audience targeting agent: {str(e)}")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def analyze_audience_performance(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_ids: List[str],</span>
<span class="line-precise" title="No Anys on this line!">        analysis_period_days: int = 30,</span>
<span class="line-precise" title="No Anys on this line!">        include_demographic_analysis: bool = True,</span>
<span class="line-precise" title="No Anys on this line!">        include_behavioral_analysis: bool = True</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; AudienceAnalysisReport:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Analyze audience performance across campaigns and generate optimization recommendations.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            campaign_ids: Campaigns to analyze</span>
<span class="line-empty" title="No Anys on this line!">            analysis_period_days: Days of historical data to analyze</span>
<span class="line-empty" title="No Anys on this line!">            include_demographic_analysis: Whether to include demographic analysis</span>
<span class="line-empty" title="No Anys on this line!">            include_behavioral_analysis: Whether to include behavioral analysis</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            AudienceAnalysisReport: Comprehensive audience analysis</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x2)
Explicit (x1)">        async with self.tracer.trace_task_execution(</span>
<span class="line-precise" title="No Anys on this line!">            task_id=f"analyze_audience_{hash(str(campaign_ids))}",</span>
<span class="line-precise" title="No Anys on this line!">            task_name="Audience Performance Analysis",</span>
<span class="line-empty" title="No Anys on this line!">            task_data={</span>
<span class="line-precise" title="No Anys on this line!">                "campaign_ids": campaign_ids,</span>
<span class="line-precise" title="No Anys on this line!">                "analysis_period_days": analysis_period_days,</span>
<span class="line-precise" title="No Anys on this line!">                "include_demographic": include_demographic_analysis,</span>
<span class="line-precise" title="No Anys on this line!">                "include_behavioral": include_behavioral_analysis</span>
<span class="line-empty" title="No Anys on this line!">            }</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">        ) as span:</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                    "Starting audience performance analysis",</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_ids=campaign_ids,</span>
<span class="line-precise" title="No Anys on this line!">                    analysis_period_days=analysis_period_days</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Define analysis period</span>
<span class="line-precise" title="No Anys on this line!">                end_date = datetime.utcnow()</span>
<span class="line-precise" title="No Anys on this line!">                start_date = end_date - timedelta(days=analysis_period_days)</span>
<span class="line-precise" title="No Anys on this line!">                analysis_period = (start_date, end_date)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Collect current audience segments</span>
<span class="line-precise" title="No Anys on this line!">                audience_segments = await self._collect_current_audience_segments(</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_ids, analysis_period</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Analyze audience performance</span>
<span class="line-precise" title="No Anys on this line!">                performance_analysis = await self._analyze_audience_segment_performance(</span>
<span class="line-precise" title="No Anys on this line!">                    audience_segments, analysis_period</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Generate audience insights</span>
<span class="line-precise" title="No Anys on this line!">                audience_insights = await self._generate_audience_insights(</span>
<span class="line-precise" title="No Anys on this line!">                    audience_segments, performance_analysis, include_demographic_analysis, include_behavioral_analysis</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Calculate audience overlap matrix</span>
<span class="line-precise" title="No Anys on this line!">                overlap_matrix = await self._calculate_audience_overlap_matrix(audience_segments)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Generate targeting recommendations</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                targeting_recommendations = await self._generate_targeting_recommendations(</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_ids, audience_segments, performance_analysis, audience_insights</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Identify expansion opportunities</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                expansion_opportunities = await self._identify_audience_expansion_opportunities(</span>
<span class="line-precise" title="No Anys on this line!">                    audience_segments, performance_analysis</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Generate exclusion recommendations</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                exclusion_recommendations = await self._generate_exclusion_recommendations(</span>
<span class="line-precise" title="No Anys on this line!">                    audience_segments, performance_analysis</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Suggest budget allocation adjustments</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                budget_allocation_suggestions = await self._suggest_audience_budget_allocation(</span>
<span class="line-precise" title="No Anys on this line!">                    audience_segments, performance_analysis</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Create comprehensive report</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">                report = AudienceAnalysisReport(</span>
<span class="line-precise" title="No Anys on this line!">                    report_id=f"audience_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_ids=campaign_ids,</span>
<span class="line-precise" title="No Anys on this line!">                    analysis_period=analysis_period,</span>
<span class="line-precise" title="No Anys on this line!">                    audience_segments=audience_segments,</span>
<span class="line-precise" title="No Anys on this line!">                    audience_insights=audience_insights,</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    targeting_recommendations=targeting_recommendations,</span>
<span class="line-precise" title="No Anys on this line!">                    overlap_matrix=overlap_matrix,</span>
<span class="line-precise" title="No Anys on this line!">                    performance_comparison=performance_analysis,</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    expansion_opportunities=expansion_opportunities,</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    exclusion_recommendations=exclusion_recommendations,</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    budget_allocation_suggestions=budget_allocation_suggestions</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unimported (x2)">                self.tracer.record_task_result(span, {</span>
<span class="line-precise" title="No Anys on this line!">                    "report_id": report.report_id,</span>
<span class="line-precise" title="No Anys on this line!">                    "audience_segments_analyzed": len(audience_segments),</span>
<span class="line-precise" title="No Anys on this line!">                    "insights_generated": len(audience_insights),</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    "targeting_recommendations": len(targeting_recommendations),</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    "expansion_opportunities": len(expansion_opportunities)</span>
<span class="line-precise" title="No Anys on this line!">                }, True)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                    "Audience performance analysis completed",</span>
<span class="line-precise" title="No Anys on this line!">                    report_id=report.report_id,</span>
<span class="line-precise" title="No Anys on this line!">                    segments_analyzed=len(audience_segments),</span>
<span class="line-precise" title="No Anys on this line!">                    insights_generated=len(audience_insights),</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    recommendations=len(targeting_recommendations)</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                return report</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">            except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.error("Audience performance analysis failed", error=str(e))</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unimported (x2)">                self.tracer.record_task_result(span, {"error": str(e)}, False)</span>
<span class="line-precise" title="No Anys on this line!">                raise AgentError(f"Audience performance analysis failed: {str(e)}")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def create_custom_audiences(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        business_description: str,</span>
<span class="line-empty" title="No Anys on this line!">        target_personas: List[Dict[str, Any]],</span>
<span class="line-empty" title="No Anys on this line!">        campaign_objectives: List[str],</span>
<span class="line-precise" title="No Anys on this line!">        budget_constraints: Optional[Dict[str, float]] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[AudienceSegment]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Create custom audience segments based on business context and personas.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            business_description: Description of the business</span>
<span class="line-empty" title="No Anys on this line!">            target_personas: Target customer personas</span>
<span class="line-empty" title="No Anys on this line!">            campaign_objectives: Campaign objectives</span>
<span class="line-empty" title="No Anys on this line!">            budget_constraints: Optional budget constraints</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            List[AudienceSegment]: Created custom audience segments</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x2)
Explicit (x1)">        async with self.tracer.trace_task_execution(</span>
<span class="line-precise" title="No Anys on this line!">            task_id=f"create_audiences_{hash(business_description)}",</span>
<span class="line-precise" title="No Anys on this line!">            task_name="Custom Audience Creation",</span>
<span class="line-empty" title="No Anys on this line!">            task_data={</span>
<span class="line-precise" title="No Anys on this line!">                "business_description": business_description,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                "personas_count": len(target_personas),</span>
<span class="line-precise" title="No Anys on this line!">                "objectives": campaign_objectives</span>
<span class="line-empty" title="No Anys on this line!">            }</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">        ) as span:</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                    "Creating custom audiences",</span>
<span class="line-precise" title="No Anys on this line!">                    business=business_description[:100],</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    personas_count=len(target_personas),</span>
<span class="line-precise" title="No Anys on this line!">                    objectives=campaign_objectives</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                custom_audiences = []</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Create audiences based on personas</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x5)">                for i, persona in enumerate(target_personas):</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    audience = await self._create_persona_based_audience(</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                        persona, business_description, campaign_objectives, i + 1</span>
<span class="line-empty" title="No Anys on this line!">                    )</span>
<span class="line-precise" title="No Anys on this line!">                    if audience:</span>
<span class="line-precise" title="No Anys on this line!">                        custom_audiences.append(audience)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Create objective-specific audiences</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                objective_audiences = await self._create_objective_based_audiences(</span>
<span class="line-precise" title="No Anys on this line!">                    business_description, campaign_objectives</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                custom_audiences.extend(objective_audiences)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Create behavioral audiences</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                behavioral_audiences = await self._create_behavioral_audiences(</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    business_description, target_personas</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                custom_audiences.extend(behavioral_audiences)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Estimate reach and validate audiences</span>
<span class="line-precise" title="No Anys on this line!">                validated_audiences = []</span>
<span class="line-precise" title="No Anys on this line!">                for audience in custom_audiences:</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                    validated_audience = await self._validate_and_enrich_audience(audience)</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    if validated_audience:</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                        validated_audiences.append(validated_audience)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unimported (x2)">                self.tracer.record_task_result(span, {</span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x1)">                    "audiences_created": len(validated_audiences),</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    "persona_based": len(target_personas),</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    "objective_based": len(objective_audiences),</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    "behavioral_based": len(behavioral_audiences)</span>
<span class="line-precise" title="No Anys on this line!">                }, True)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                    "Custom audience creation completed",</span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x1)">                    audiences_created=len(validated_audiences),</span>
<span class="line-any" title="Any Types on this line: 
Error (x6)
Omitted Generics (x6)
Explicit (x8)">                    total_estimated_reach=sum(a.estimated_reach or 0 for a in validated_audiences)</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x1)">                return validated_audiences</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">            except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.error("Custom audience creation failed", error=str(e))</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unimported (x2)">                self.tracer.record_task_result(span, {"error": str(e)}, False)</span>
<span class="line-precise" title="No Anys on this line!">                raise AgentError(f"Custom audience creation failed: {str(e)}")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def optimize_audience_targeting(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_ids: List[str],</span>
<span class="line-empty" title="No Anys on this line!">        optimization_goals: List[str],</span>
<span class="line-empty" title="No Anys on this line!">        current_performance: Dict[str, Any],</span>
<span class="line-precise" title="No Anys on this line!">        budget_allocation: Optional[Dict[str, float]] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[TargetingRecommendation]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Optimize audience targeting for existing campaigns.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            campaign_ids: Campaigns to optimize</span>
<span class="line-empty" title="No Anys on this line!">            optimization_goals: Goals like 'increase_conversions', 'reduce_cpa', etc.</span>
<span class="line-empty" title="No Anys on this line!">            current_performance: Current campaign performance data</span>
<span class="line-empty" title="No Anys on this line!">            budget_allocation: Optional budget allocation per campaign</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            List[TargetingRecommendation]: Targeting optimization recommendations</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x2)
Explicit (x1)">        async with self.tracer.trace_task_execution(</span>
<span class="line-precise" title="No Anys on this line!">            task_id=f"optimize_targeting_{hash(str(campaign_ids))}",</span>
<span class="line-precise" title="No Anys on this line!">            task_name="Audience Targeting Optimization",</span>
<span class="line-empty" title="No Anys on this line!">            task_data={</span>
<span class="line-precise" title="No Anys on this line!">                "campaign_ids": campaign_ids,</span>
<span class="line-precise" title="No Anys on this line!">                "optimization_goals": optimization_goals</span>
<span class="line-empty" title="No Anys on this line!">            }</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">        ) as span:</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                    "Starting audience targeting optimization",</span>
<span class="line-precise" title="No Anys on this line!">                    campaign_ids=campaign_ids,</span>
<span class="line-precise" title="No Anys on this line!">                    goals=optimization_goals</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                recommendations = []</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                for campaign_id in campaign_ids:</span>
<span class="line-empty" title="No Anys on this line!">                    try:</span>
<span class="line-empty" title="No Anys on this line!">                        # Get current audience targeting</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                        current_targeting = await self._get_current_audience_targeting(campaign_id)</span>
<span class="line-empty" title="No Anys on this line!">                        </span>
<span class="line-empty" title="No Anys on this line!">                        # Analyze performance by audience</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                        audience_performance = await self._analyze_campaign_audience_performance(</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">                            campaign_id, current_performance.get(campaign_id, {})</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-empty" title="No Anys on this line!">                        </span>
<span class="line-empty" title="No Anys on this line!">                        # Generate campaign-specific recommendations</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                        campaign_recommendations = await self._generate_campaign_targeting_recommendations(</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                            campaign_id, current_targeting, audience_performance, optimization_goals</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-empty" title="No Anys on this line!">                        </span>
<span class="line-any" title="Any Types on this line: 
Omitted Generics (x2)
Error (x1)">                        recommendations.extend(campaign_recommendations)</span>
<span class="line-empty" title="No Anys on this line!">                        </span>
<span class="line-precise" title="No Anys on this line!">                    except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                        self.logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">                            "Failed to optimize targeting for campaign",</span>
<span class="line-precise" title="No Anys on this line!">                            campaign_id=campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                            error=str(e)</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Prioritize recommendations</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                prioritized_recommendations = await self._prioritize_targeting_recommendations(</span>
<span class="line-imprecise" title="Any Types on this line: 
Omitted Generics (x1)">                    recommendations, optimization_goals</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unimported (x2)">                self.tracer.record_task_result(span, {</span>
<span class="line-precise" title="No Anys on this line!">                    "campaigns_optimized": len(campaign_ids),</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    "recommendations_generated": len(prioritized_recommendations),</span>
<span class="line-any" title="Any Types on this line: 
Error (x5)">                    "high_priority_recommendations": len([r for r in prioritized_recommendations if r.priority in ["critical", "high"]])</span>
<span class="line-precise" title="No Anys on this line!">                }, True)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                    "Audience targeting optimization completed",</span>
<span class="line-precise" title="No Anys on this line!">                    campaigns_optimized=len(campaign_ids),</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                    recommendations_generated=len(prioritized_recommendations),</span>
<span class="line-any" title="Any Types on this line: 
Error (x5)">                    high_priority=len([r for r in prioritized_recommendations if r.priority in ["critical", "high"]])</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                return prioritized_recommendations</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">            except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.error("Audience targeting optimization failed", error=str(e))</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unimported (x2)">                self.tracer.record_task_result(span, {"error": str(e)}, False)</span>
<span class="line-precise" title="No Anys on this line!">                raise AgentError(f"Audience targeting optimization failed: {str(e)}")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Core audience analysis methods</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _collect_current_audience_segments(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_ids: List[str],</span>
<span class="line-empty" title="No Anys on this line!">        analysis_period: Tuple[datetime, datetime]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[AudienceSegment]:</span>
<span class="line-empty" title="No Anys on this line!">        """Collect current audience segments from campaigns."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            audience_segments = []</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            if self.google_ads_service:</span>
<span class="line-precise" title="No Anys on this line!">                for campaign_id in campaign_ids:</span>
<span class="line-empty" title="No Anys on this line!">                    try:</span>
<span class="line-empty" title="No Anys on this line!">                        # Get audience targeting data from Google Ads</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                        targeting_data = await self.google_ads_service.get_campaign_audience_targeting(</span>
<span class="line-precise" title="No Anys on this line!">                            campaign_id</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-empty" title="No Anys on this line!">                        </span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">                        for audience_data in targeting_data:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">                            segment = AudienceSegment(</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                                segment_id=audience_data.get("audience_id", f"unknown_{len(audience_segments)}"),</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                                name=audience_data.get("name", "Unknown Audience"),</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                                audience_type=AudienceType(audience_data.get("type", "demographic")),</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                                targeting_criteria=audience_data.get("criteria", {}),</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                                estimated_reach=audience_data.get("estimated_reach"),</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                                performance_metrics=audience_data.get("performance_metrics", {})</span>
<span class="line-empty" title="No Anys on this line!">                            )</span>
<span class="line-precise" title="No Anys on this line!">                            audience_segments.append(segment)</span>
<span class="line-precise" title="No Anys on this line!">                    except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                        self.logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">                            "Failed to get audience data for campaign",</span>
<span class="line-precise" title="No Anys on this line!">                            campaign_id=campaign_id,</span>
<span class="line-precise" title="No Anys on this line!">                            error=str(e)</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-empty" title="No Anys on this line!">            else:</span>
<span class="line-empty" title="No Anys on this line!">                # Mock audience segments for development</span>
<span class="line-precise" title="No Anys on this line!">                mock_segments = [</span>
<span class="line-empty" title="No Anys on this line!">                    {</span>
<span class="line-precise" title="No Anys on this line!">                        "segment_id": "demo_25_45",</span>
<span class="line-precise" title="No Anys on this line!">                        "name": "Adults 25-45",</span>
<span class="line-precise" title="No Anys on this line!">                        "audience_type": AudienceType.DEMOGRAPHIC,</span>
<span class="line-precise" title="No Anys on this line!">                        "targeting_criteria": {"age_range": "25-45", "locations": ["US"]},</span>
<span class="line-precise" title="No Anys on this line!">                        "estimated_reach": 5000000,</span>
<span class="line-precise" title="No Anys on this line!">                        "performance_metrics": {"ctr": 2.8, "conversion_rate": 3.2, "cpa": 45.0}</span>
<span class="line-empty" title="No Anys on this line!">                    },</span>
<span class="line-empty" title="No Anys on this line!">                    {</span>
<span class="line-precise" title="No Anys on this line!">                        "segment_id": "affinity_tech",</span>
<span class="line-precise" title="No Anys on this line!">                        "name": "Technology Enthusiasts",</span>
<span class="line-precise" title="No Anys on this line!">                        "audience_type": AudienceType.AFFINITY,</span>
<span class="line-precise" title="No Anys on this line!">                        "targeting_criteria": {"interests": ["technology", "software", "gadgets"]},</span>
<span class="line-precise" title="No Anys on this line!">                        "estimated_reach": 2500000,</span>
<span class="line-precise" title="No Anys on this line!">                        "performance_metrics": {"ctr": 3.5, "conversion_rate": 4.1, "cpa": 38.0}</span>
<span class="line-empty" title="No Anys on this line!">                    },</span>
<span class="line-empty" title="No Anys on this line!">                    {</span>
<span class="line-precise" title="No Anys on this line!">                        "segment_id": "remarketing_website",</span>
<span class="line-precise" title="No Anys on this line!">                        "name": "Website Visitors",</span>
<span class="line-precise" title="No Anys on this line!">                        "audience_type": AudienceType.REMARKETING,</span>
<span class="line-precise" title="No Anys on this line!">                        "targeting_criteria": {"website_visitors": "30_days", "pages_viewed": "&gt;= 2"},</span>
<span class="line-precise" title="No Anys on this line!">                        "estimated_reach": 150000,</span>
<span class="line-precise" title="No Anys on this line!">                        "performance_metrics": {"ctr": 4.2, "conversion_rate": 6.8, "cpa": 28.0}</span>
<span class="line-empty" title="No Anys on this line!">                    }</span>
<span class="line-empty" title="No Anys on this line!">                ]</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                for mock_data in mock_segments:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">                    segment = AudienceSegment(</span>
<span class="line-precise" title="No Anys on this line!">                        segment_id=mock_data["segment_id"],</span>
<span class="line-precise" title="No Anys on this line!">                        name=mock_data["name"],</span>
<span class="line-precise" title="No Anys on this line!">                        audience_type=mock_data["audience_type"],</span>
<span class="line-precise" title="No Anys on this line!">                        targeting_criteria=mock_data["targeting_criteria"],</span>
<span class="line-precise" title="No Anys on this line!">                        estimated_reach=mock_data["estimated_reach"],</span>
<span class="line-precise" title="No Anys on this line!">                        performance_metrics=mock_data["performance_metrics"]</span>
<span class="line-empty" title="No Anys on this line!">                    )</span>
<span class="line-precise" title="No Anys on this line!">                    audience_segments.append(segment)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return audience_segments</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to collect current audience segments", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">            return []</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _analyze_audience_segment_performance(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        audience_segments: List[AudienceSegment],</span>
<span class="line-empty" title="No Anys on this line!">        analysis_period: Tuple[datetime, datetime]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; Dict[str, Dict[str, float]]:</span>
<span class="line-empty" title="No Anys on this line!">        """Analyze performance of audience segments."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            performance_analysis = {}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            for segment in audience_segments:</span>
<span class="line-precise" title="No Anys on this line!">                segment_id = segment.segment_id</span>
<span class="line-precise" title="No Anys on this line!">                metrics = segment.performance_metrics</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Calculate performance scores</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                ctr = metrics.get("ctr", 0)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                conversion_rate = metrics.get("conversion_rate", 0)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                cpa = metrics.get("cpa", float('inf'))</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Normalize metrics (0-1 scale)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">                ctr_score = min(1.0, ctr / 5.0)  # Assume 5% is excellent CTR</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">                cr_score = min(1.0, conversion_rate / 10.0)  # Assume 10% is excellent CR</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">                cpa_score = max(0.0, 1.0 - (cpa / 100.0))  # Assume $100 is high CPA</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Calculate overall performance score</span>
<span class="line-precise" title="No Anys on this line!">                overall_score = (ctr_score * 0.3 + cr_score * 0.4 + cpa_score * 0.3)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                performance_analysis[segment_id] = {</span>
<span class="line-precise" title="No Anys on this line!">                    "ctr": ctr,</span>
<span class="line-precise" title="No Anys on this line!">                    "conversion_rate": conversion_rate,</span>
<span class="line-precise" title="No Anys on this line!">                    "cpa": cpa,</span>
<span class="line-precise" title="No Anys on this line!">                    "ctr_score": ctr_score,</span>
<span class="line-precise" title="No Anys on this line!">                    "conversion_rate_score": cr_score,</span>
<span class="line-precise" title="No Anys on this line!">                    "cpa_score": cpa_score,</span>
<span class="line-precise" title="No Anys on this line!">                    "overall_performance_score": overall_score,</span>
<span class="line-precise" title="No Anys on this line!">                    "estimated_reach": segment.estimated_reach or 0,</span>
<span class="line-precise" title="No Anys on this line!">                    "performance_rank": 0  # Will be calculated after all segments are processed</span>
<span class="line-empty" title="No Anys on this line!">                }</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Rank segments by overall performance</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x5)">            sorted_segments = sorted(</span>
<span class="line-precise" title="No Anys on this line!">                performance_analysis.items(),</span>
<span class="line-precise" title="No Anys on this line!">                key=lambda x: x[1]["overall_performance_score"],</span>
<span class="line-precise" title="No Anys on this line!">                reverse=True</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            for rank, (segment_id, data) in enumerate(sorted_segments, 1):</span>
<span class="line-precise" title="No Anys on this line!">                performance_analysis[segment_id]["performance_rank"] = rank</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return performance_analysis</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to analyze audience segment performance", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">            return {}</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _generate_audience_insights(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        audience_segments: List[AudienceSegment],</span>
<span class="line-empty" title="No Anys on this line!">        performance_analysis: Dict[str, Dict[str, float]],</span>
<span class="line-empty" title="No Anys on this line!">        include_demographic: bool,</span>
<span class="line-empty" title="No Anys on this line!">        include_behavioral: bool</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[AudienceInsight]:</span>
<span class="line-empty" title="No Anys on this line!">        """Generate insights about audience segments."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            insights = []</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Performance-based insights</span>
<span class="line-precise" title="No Anys on this line!">            for segment in audience_segments:</span>
<span class="line-precise" title="No Anys on this line!">                segment_id = segment.segment_id</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                performance = performance_analysis.get(segment_id, {})</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # High-performing audience insight</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                if performance.get("overall_performance_score", 0) &gt; 0.7:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    insight = AudienceInsight(</span>
<span class="line-precise" title="No Anys on this line!">                        insight_id=f"high_perf_{segment_id}",</span>
<span class="line-precise" title="No Anys on this line!">                        audience_segment_id=segment_id,</span>
<span class="line-precise" title="No Anys on this line!">                        insight_type="performance",</span>
<span class="line-precise" title="No Anys on this line!">                        title=f"High-Performing Audience: {segment.name}",</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">                        description=f"This audience segment shows excellent performance with {performance.get('conversion_rate', 0):.1f}% conversion rate and ${performance.get('cpa', 0):.0f} CPA.",</span>
<span class="line-precise" title="No Anys on this line!">                        supporting_data=performance,</span>
<span class="line-precise" title="No Anys on this line!">                        confidence_score=0.9,</span>
<span class="line-empty" title="No Anys on this line!">                        actionable_recommendations=[</span>
<span class="line-precise" title="No Anys on this line!">                            f"Increase budget allocation to {segment.name}",</span>
<span class="line-precise" title="No Anys on this line!">                            f"Create similar audiences based on {segment.name}",</span>
<span class="line-precise" title="No Anys on this line!">                            f"Expand targeting criteria for {segment.name}"</span>
<span class="line-empty" title="No Anys on this line!">                        ],</span>
<span class="line-precise" title="No Anys on this line!">                        business_impact="high"</span>
<span class="line-empty" title="No Anys on this line!">                    )</span>
<span class="line-precise" title="No Anys on this line!">                    insights.append(insight)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Underperforming audience insight</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                elif performance.get("overall_performance_score", 0) &lt; 0.3:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    insight = AudienceInsight(</span>
<span class="line-precise" title="No Anys on this line!">                        insight_id=f"low_perf_{segment_id}",</span>
<span class="line-precise" title="No Anys on this line!">                        audience_segment_id=segment_id,</span>
<span class="line-precise" title="No Anys on this line!">                        insight_type="performance",</span>
<span class="line-precise" title="No Anys on this line!">                        title=f"Underperforming Audience: {segment.name}",</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">                        description=f"This audience segment shows poor performance with {performance.get('conversion_rate', 0):.1f}% conversion rate and ${performance.get('cpa', 0):.0f} CPA.",</span>
<span class="line-precise" title="No Anys on this line!">                        supporting_data=performance,</span>
<span class="line-precise" title="No Anys on this line!">                        confidence_score=0.8,</span>
<span class="line-empty" title="No Anys on this line!">                        actionable_recommendations=[</span>
<span class="line-precise" title="No Anys on this line!">                            f"Consider excluding {segment.name} from campaigns",</span>
<span class="line-precise" title="No Anys on this line!">                            f"Reduce budget allocation to {segment.name}",</span>
<span class="line-precise" title="No Anys on this line!">                            f"Refine targeting criteria for {segment.name}"</span>
<span class="line-empty" title="No Anys on this line!">                        ],</span>
<span class="line-precise" title="No Anys on this line!">                        business_impact="medium"</span>
<span class="line-empty" title="No Anys on this line!">                    )</span>
<span class="line-precise" title="No Anys on this line!">                    insights.append(insight)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Demographic insights (if requested)</span>
<span class="line-precise" title="No Anys on this line!">            if include_demographic:</span>
<span class="line-precise" title="No Anys on this line!">                demographic_insights = await self._generate_demographic_insights(</span>
<span class="line-precise" title="No Anys on this line!">                    audience_segments, performance_analysis</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-precise" title="No Anys on this line!">                insights.extend(demographic_insights)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Behavioral insights (if requested)</span>
<span class="line-precise" title="No Anys on this line!">            if include_behavioral:</span>
<span class="line-precise" title="No Anys on this line!">                behavioral_insights = await self._generate_behavioral_insights(</span>
<span class="line-precise" title="No Anys on this line!">                    audience_segments, performance_analysis</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-precise" title="No Anys on this line!">                insights.extend(behavioral_insights)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # AI-powered insights</span>
<span class="line-precise" title="No Anys on this line!">            if self.openai_service:</span>
<span class="line-precise" title="No Anys on this line!">                ai_insights = await self._generate_ai_audience_insights(</span>
<span class="line-precise" title="No Anys on this line!">                    audience_segments, performance_analysis</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-precise" title="No Anys on this line!">                insights.extend(ai_insights)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Sort insights by business impact and confidence</span>
<span class="line-precise" title="No Anys on this line!">            impact_order = {"high": 3, "medium": 2, "low": 1}</span>
<span class="line-precise" title="No Anys on this line!">            insights.sort(</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                key=lambda x: (impact_order.get(x.business_impact, 0), x.confidence_score),</span>
<span class="line-precise" title="No Anys on this line!">                reverse=True</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return insights</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to generate audience insights", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">            return []</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _generate_demographic_insights(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        audience_segments: List[AudienceSegment],</span>
<span class="line-empty" title="No Anys on this line!">        performance_analysis: Dict[str, Dict[str, float]]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[AudienceInsight]:</span>
<span class="line-empty" title="No Anys on this line!">        """Generate demographic-based insights."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            insights = []</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Analyze age group performance</span>
<span class="line-precise" title="No Anys on this line!">            age_performance = {}</span>
<span class="line-precise" title="No Anys on this line!">            for segment in audience_segments:</span>
<span class="line-precise" title="No Anys on this line!">                if segment.audience_type == AudienceType.DEMOGRAPHIC:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">                    age_range = segment.targeting_criteria.get("age_range", "unknown")</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                    performance = performance_analysis.get(segment.segment_id, {})</span>
<span class="line-empty" title="No Anys on this line!">                    </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unannotated (x2)">                    if age_range not in age_performance:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)
Explicit (x1)">                        age_performance[age_range] = []</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x5)
Explicit (x1)
Omitted Generics (x2)">                    age_performance[age_range].append(performance.get("overall_performance_score", 0))</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Find best performing age group</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x2)">            if age_performance:</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x1)">                avg_performance_by_age = {</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)
Omitted Generics (x6)
Explicit (x8)">                    age: sum(scores) / len(scores)</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x8)">                    for age, scores in age_performance.items()</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                    if scores</span>
<span class="line-empty" title="No Anys on this line!">                }</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x7)
Omitted Generics (x25)">                best_age_group = max(avg_performance_by_age, key=avg_performance_by_age.get)</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">                best_performance = avg_performance_by_age[best_age_group]</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                if best_performance &gt; 0.6:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    insight = AudienceInsight(</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">                        insight_id=f"demo_age_{best_age_group.replace('-', '_')}",</span>
<span class="line-precise" title="No Anys on this line!">                        audience_segment_id="demographic_analysis",</span>
<span class="line-precise" title="No Anys on this line!">                        insight_type="demographic",</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                        title=f"Top Performing Age Group: {best_age_group}",</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                        description=f"The {best_age_group} age group shows the strongest performance with an average score of {best_performance:.2f}.",</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x1)">                        supporting_data={"age_performance": avg_performance_by_age},</span>
<span class="line-precise" title="No Anys on this line!">                        confidence_score=0.8,</span>
<span class="line-empty" title="No Anys on this line!">                        actionable_recommendations=[</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                            f"Focus more budget on {best_age_group} demographic",</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                            f"Create age-specific creative for {best_age_group}",</span>
<span class="line-precise" title="No Anys on this line!">                            f"Expand similar age-adjacent targeting"</span>
<span class="line-empty" title="No Anys on this line!">                        ],</span>
<span class="line-precise" title="No Anys on this line!">                        business_impact="medium"</span>
<span class="line-empty" title="No Anys on this line!">                    )</span>
<span class="line-precise" title="No Anys on this line!">                    insights.append(insight)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return insights</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to generate demographic insights", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">            return []</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _generate_behavioral_insights(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        audience_segments: List[AudienceSegment],</span>
<span class="line-empty" title="No Anys on this line!">        performance_analysis: Dict[str, Dict[str, float]]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[AudienceInsight]:</span>
<span class="line-empty" title="No Anys on this line!">        """Generate behavioral-based insights."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            insights = []</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Analyze remarketing vs. cold audience performance</span>
<span class="line-precise" title="No Anys on this line!">            remarketing_performance = []</span>
<span class="line-precise" title="No Anys on this line!">            cold_audience_performance = []</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            for segment in audience_segments:</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">                performance_score = performance_analysis.get(segment.segment_id, {}).get("overall_performance_score", 0)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                if segment.audience_type == AudienceType.REMARKETING:</span>
<span class="line-precise" title="No Anys on this line!">                    remarketing_performance.append(performance_score)</span>
<span class="line-precise" title="No Anys on this line!">                elif segment.audience_type in [AudienceType.DEMOGRAPHIC, AudienceType.AFFINITY]:</span>
<span class="line-precise" title="No Anys on this line!">                    cold_audience_performance.append(performance_score)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            if remarketing_performance and cold_audience_performance:</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x6)
Explicit (x8)">                avg_remarketing = sum(remarketing_performance) / len(remarketing_performance)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x6)
Explicit (x8)">                avg_cold = sum(cold_audience_performance) / len(cold_audience_performance)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                if avg_remarketing &gt; avg_cold * 1.3:  # 30% better performance</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    insight = AudienceInsight(</span>
<span class="line-precise" title="No Anys on this line!">                        insight_id="behavioral_remarketing_advantage",</span>
<span class="line-precise" title="No Anys on this line!">                        audience_segment_id="behavioral_analysis",</span>
<span class="line-precise" title="No Anys on this line!">                        insight_type="behavioral",</span>
<span class="line-precise" title="No Anys on this line!">                        title="Remarketing Audiences Significantly Outperform Cold Audiences",</span>
<span class="line-precise" title="No Anys on this line!">                        description=f"Remarketing audiences show {((avg_remarketing/avg_cold - 1) * 100):.0f}% better performance than cold audiences.",</span>
<span class="line-empty" title="No Anys on this line!">                        supporting_data={</span>
<span class="line-precise" title="No Anys on this line!">                            "remarketing_avg_score": avg_remarketing,</span>
<span class="line-precise" title="No Anys on this line!">                            "cold_audience_avg_score": avg_cold,</span>
<span class="line-precise" title="No Anys on this line!">                            "performance_lift": avg_remarketing / avg_cold</span>
<span class="line-empty" title="No Anys on this line!">                        },</span>
<span class="line-precise" title="No Anys on this line!">                        confidence_score=0.85,</span>
<span class="line-empty" title="No Anys on this line!">                        actionable_recommendations=[</span>
<span class="line-precise" title="No Anys on this line!">                            "Increase remarketing budget allocation",</span>
<span class="line-precise" title="No Anys on this line!">                            "Create more comprehensive remarketing lists",</span>
<span class="line-precise" title="No Anys on this line!">                            "Implement sequential remarketing campaigns",</span>
<span class="line-precise" title="No Anys on this line!">                            "Reduce cold audience budget in favor of remarketing"</span>
<span class="line-empty" title="No Anys on this line!">                        ],</span>
<span class="line-precise" title="No Anys on this line!">                        business_impact="high"</span>
<span class="line-empty" title="No Anys on this line!">                    )</span>
<span class="line-precise" title="No Anys on this line!">                    insights.append(insight)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return insights</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to generate behavioral insights", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">            return []</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _generate_ai_audience_insights(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        audience_segments: List[AudienceSegment],</span>
<span class="line-empty" title="No Anys on this line!">        performance_analysis: Dict[str, Dict[str, float]]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[AudienceInsight]:</span>
<span class="line-empty" title="No Anys on this line!">        """Generate AI-powered audience insights."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            if not self.openai_service:</span>
<span class="line-empty" title="No Anys on this line!">                return []</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Prepare data for AI analysis</span>
<span class="line-precise" title="No Anys on this line!">            segments_data = []</span>
<span class="line-precise" title="No Anys on this line!">            for segment in audience_segments:</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                performance = performance_analysis.get(segment.segment_id, {})</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">                segments_data.append({</span>
<span class="line-precise" title="No Anys on this line!">                    "name": segment.name,</span>
<span class="line-precise" title="No Anys on this line!">                    "type": segment.audience_type.value,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    "targeting_criteria": segment.targeting_criteria,</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                    "performance_score": performance.get("overall_performance_score", 0),</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                    "ctr": performance.get("ctr", 0),</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                    "conversion_rate": performance.get("conversion_rate", 0),</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                    "cpa": performance.get("cpa", 0),</span>
<span class="line-precise" title="No Anys on this line!">                    "estimated_reach": segment.estimated_reach or 0</span>
<span class="line-empty" title="No Anys on this line!">                })</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            prompt = f"""</span>
<span class="line-empty" title="No Anys on this line!">            Analyze the following audience segments and their performance data to identify key insights and patterns:</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            Audience Segments:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x5)">            {json.dumps(segments_data, indent=2)}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            Please identify:</span>
<span class="line-empty" title="No Anys on this line!">            1. Performance patterns across different audience types</span>
<span class="line-empty" title="No Anys on this line!">            2. Opportunities for audience expansion or refinement</span>
<span class="line-empty" title="No Anys on this line!">            3. Budget reallocation recommendations</span>
<span class="line-empty" title="No Anys on this line!">            4. Targeting strategy improvements</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            Focus on actionable insights that can improve campaign performance. Provide specific recommendations based on the data.</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            Return insights in JSON format with the following structure:</span>
<span class="line-empty" title="No Anys on this line!">            {{</span>
<span class="line-empty" title="No Anys on this line!">                "insights": [</span>
<span class="line-empty" title="No Anys on this line!">                    {{</span>
<span class="line-empty" title="No Anys on this line!">                        "title": "Insight Title",</span>
<span class="line-empty" title="No Anys on this line!">                        "description": "Detailed description",</span>
<span class="line-empty" title="No Anys on this line!">                        "recommendations": ["Recommendation 1", "Recommendation 2"],</span>
<span class="line-empty" title="No Anys on this line!">                        "confidence": 0.8,</span>
<span class="line-empty" title="No Anys on this line!">                        "impact": "high/medium/low"</span>
<span class="line-empty" title="No Anys on this line!">                    }}</span>
<span class="line-empty" title="No Anys on this line!">                ]</span>
<span class="line-empty" title="No Anys on this line!">            }}</span>
<span class="line-empty" title="No Anys on this line!">            """</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">            response = await self.openai_service.generate_completion(</span>
<span class="line-precise" title="No Anys on this line!">                prompt=prompt,</span>
<span class="line-precise" title="No Anys on this line!">                model=self.config.model.model_name,</span>
<span class="line-precise" title="No Anys on this line!">                temperature=0.3,</span>
<span class="line-precise" title="No Anys on this line!">                max_tokens=1500</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x13)
Error (x1)">                ai_analysis = json.loads(response)</span>
<span class="line-precise" title="No Anys on this line!">                insights = []</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)">                for i, insight_data in enumerate(ai_analysis.get("insights", [])):</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    insight = AudienceInsight(</span>
<span class="line-precise" title="No Anys on this line!">                        insight_id=f"ai_insight_{i+1}",</span>
<span class="line-precise" title="No Anys on this line!">                        audience_segment_id="ai_analysis",</span>
<span class="line-precise" title="No Anys on this line!">                        insight_type="ai_powered",</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                        title=insight_data.get("title", f"AI Insight {i+1}"),</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                        description=insight_data.get("description", "AI-generated insight"),</span>
<span class="line-precise" title="No Anys on this line!">                        supporting_data={"ai_analysis": True},</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                        confidence_score=insight_data.get("confidence", 0.7),</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                        actionable_recommendations=insight_data.get("recommendations", []),</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                        business_impact=insight_data.get("impact", "medium")</span>
<span class="line-empty" title="No Anys on this line!">                    )</span>
<span class="line-precise" title="No Anys on this line!">                    insights.append(insight)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                return insights</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">            except json.JSONDecodeError:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.warning("Failed to parse AI insights JSON response")</span>
<span class="line-empty" title="No Anys on this line!">                return []</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to generate AI audience insights", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">            return []</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Additional methods for audience creation, optimization, etc.</span>
<span class="line-empty" title="No Anys on this line!">    # (Implementation continues with similar patterns...)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _create_persona_based_audience(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        persona: Dict[str, Any],</span>
<span class="line-empty" title="No Anys on this line!">        business_description: str,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_objectives: List[str],</span>
<span class="line-empty" title="No Anys on this line!">        persona_index: int</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; Optional[AudienceSegment]:</span>
<span class="line-empty" title="No Anys on this line!">        """Create audience segment based on customer persona."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">            persona_name = persona.get("name", f"Persona {persona_index}")</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Extract targeting criteria from persona</span>
<span class="line-precise" title="No Anys on this line!">            targeting_criteria = {}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Demographics</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            if "age_range" in persona:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                targeting_criteria["age_range"] = persona["age_range"]</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            if "gender" in persona:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                targeting_criteria["gender"] = persona["gender"]</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            if "income_level" in persona:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                targeting_criteria["income_level"] = persona["income_level"]</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            if "location" in persona:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                targeting_criteria["locations"] = persona["location"]</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Interests and behaviors</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            if "interests" in persona:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                targeting_criteria["interests"] = persona["interests"]</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            if "behaviors" in persona:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                targeting_criteria["behaviors"] = persona["behaviors"]</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            if "life_events" in persona:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                targeting_criteria["life_events"] = persona["life_events"]</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Estimate reach (mock implementation)</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            estimated_reach = self._estimate_audience_reach(targeting_criteria)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            segment = AudienceSegment(</span>
<span class="line-precise" title="No Anys on this line!">                segment_id=f"persona_{persona_index}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                name=f"{persona_name} - Target Persona",</span>
<span class="line-precise" title="No Anys on this line!">                audience_type=AudienceType.DEMOGRAPHIC,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                targeting_criteria=targeting_criteria,</span>
<span class="line-precise" title="No Anys on this line!">                estimated_reach=estimated_reach,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                demographic_profile=persona,</span>
<span class="line-empty" title="No Anys on this line!">                behavioral_insights=[</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">                    f"Primary motivation: {persona.get('primary_motivation', 'Unknown')}",</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">                    f"Pain points: {', '.join(persona.get('pain_points', []))}",</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">                    f"Preferred channels: {', '.join(persona.get('preferred_channels', []))}"</span>
<span class="line-empty" title="No Anys on this line!">                ]</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return segment</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.warning("Failed to create persona-based audience", error=str(e))</span>
<span class="line-precise" title="No Anys on this line!">            return None</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    def _estimate_audience_reach(self, targeting_criteria: Dict[str, Any]) -&gt; int:</span>
<span class="line-empty" title="No Anys on this line!">        """Estimate audience reach based on targeting criteria (simplified)."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            base_reach = 100000000  # Start with 100M (US population approximation)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Apply demographic filters</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            if "age_range" in targeting_criteria:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                age_range = targeting_criteria["age_range"]</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                if "18-24" in age_range:</span>
<span class="line-precise" title="No Anys on this line!">                    base_reach *= 0.12</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                elif "25-34" in age_range:</span>
<span class="line-precise" title="No Anys on this line!">                    base_reach *= 0.18</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                elif "35-44" in age_range:</span>
<span class="line-precise" title="No Anys on this line!">                    base_reach *= 0.16</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                elif "45-54" in age_range:</span>
<span class="line-precise" title="No Anys on this line!">                    base_reach *= 0.15</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                elif "55-64" in age_range:</span>
<span class="line-precise" title="No Anys on this line!">                    base_reach *= 0.13</span>
<span class="line-empty" title="No Anys on this line!">                else:</span>
<span class="line-precise" title="No Anys on this line!">                    base_reach *= 0.3  # Broader age range</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            if "gender" in targeting_criteria:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                gender = targeting_criteria["gender"]</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                if gender in ["male", "female"]:</span>
<span class="line-precise" title="No Anys on this line!">                    base_reach *= 0.5  # Roughly half the population</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Apply interest filters</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            if "interests" in targeting_criteria:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">                interest_count = len(targeting_criteria["interests"])</span>
<span class="line-empty" title="No Anys on this line!">                # More interests = more specific = smaller audience</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">                base_reach *= max(0.1, 1.0 - (interest_count * 0.1))</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Apply location filters</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            if "locations" in targeting_criteria:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                locations = targeting_criteria["locations"]</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Omitted Generics (x3)">                if isinstance(locations, list) and len(locations) &lt; 5:</span>
<span class="line-precise" title="No Anys on this line!">                    base_reach *= 0.3  # Specific locations</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">            return max(1000, int(base_reach))  # Minimum audience size</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.warning("Failed to estimate audience reach", error=str(e))</span>
<span class="line-precise" title="No Anys on this line!">            return 50000  # Default estimate</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _calculate_audience_overlap_matrix(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        audience_segments: List[AudienceSegment]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; Dict[str, Dict[str, float]]:</span>
<span class="line-empty" title="No Anys on this line!">        """Calculate overlap matrix between audience segments."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            overlap_matrix = {}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            for segment1 in audience_segments:</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x2)">                overlap_matrix[segment1.segment_id] = {}</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                for segment2 in audience_segments:</span>
<span class="line-precise" title="No Anys on this line!">                    if segment1.segment_id == segment2.segment_id:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">                        overlap_matrix[segment1.segment_id][segment2.segment_id] = 1.0</span>
<span class="line-empty" title="No Anys on this line!">                    else:</span>
<span class="line-empty" title="No Anys on this line!">                        # Calculate overlap based on targeting criteria similarity</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">                        overlap = self._calculate_targeting_overlap(</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                            segment1.targeting_criteria,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                            segment2.targeting_criteria</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">                        overlap_matrix[segment1.segment_id][segment2.segment_id] = overlap</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x2)">            return overlap_matrix</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("Failed to calculate audience overlap matrix", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">            return {}</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">    def _calculate_targeting_overlap(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        criteria1: Dict[str, Any],</span>
<span class="line-empty" title="No Anys on this line!">        criteria2: Dict[str, Any]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; float:</span>
<span class="line-empty" title="No Anys on this line!">        """Calculate overlap between two targeting criteria sets."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            if not criteria1 or not criteria2:</span>
<span class="line-precise" title="No Anys on this line!">                return 0.0</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            overlap_score = 0.0</span>
<span class="line-precise" title="No Anys on this line!">            total_criteria = 0</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Check demographic overlap</span>
<span class="line-precise" title="No Anys on this line!">            demographic_fields = ["age_range", "gender", "income_level"]</span>
<span class="line-precise" title="No Anys on this line!">            for field in demographic_fields:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">                if field in criteria1 and field in criteria2:</span>
<span class="line-precise" title="No Anys on this line!">                    total_criteria += 1</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x5)">                    if criteria1[field] == criteria2[field]:</span>
<span class="line-precise" title="No Anys on this line!">                        overlap_score += 1.0</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)">                    elif self._has_partial_overlap(criteria1[field], criteria2[field]):</span>
<span class="line-precise" title="No Anys on this line!">                        overlap_score += 0.5</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Check interest overlap</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            if "interests" in criteria1 and "interests" in criteria2:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)
Omitted Generics (x3)">                interests1 = set(criteria1["interests"])</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)
Omitted Generics (x3)">                interests2 = set(criteria2["interests"])</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x3)">                if interests1 and interests2:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x10)">                    interest_overlap = len(interests1.intersection(interests2)) / len(interests1.union(interests2))</span>
<span class="line-precise" title="No Anys on this line!">                    overlap_score += interest_overlap</span>
<span class="line-precise" title="No Anys on this line!">                    total_criteria += 1</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Check location overlap</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            if "locations" in criteria1 and "locations" in criteria2:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x5)
Omitted Generics (x6)">                locations1 = set(criteria1["locations"]) if isinstance(criteria1["locations"], list) else {criteria1["locations"]}</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x5)
Omitted Generics (x6)">                locations2 = set(criteria2["locations"]) if isinstance(criteria2["locations"], list) else {criteria2["locations"]}</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                location_overlap = len(locations1.intersection(locations2)) / len(locations1.union(locations2))</span>
<span class="line-precise" title="No Anys on this line!">                overlap_score += location_overlap</span>
<span class="line-precise" title="No Anys on this line!">                total_criteria += 1</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return overlap_score / total_criteria if total_criteria &gt; 0 else 0.0</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.warning("Failed to calculate targeting overlap", error=str(e))</span>
<span class="line-precise" title="No Anys on this line!">            return 0.0</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">    def _has_partial_overlap(self, value1: Any, value2: Any) -&gt; bool:</span>
<span class="line-empty" title="No Anys on this line!">        """Check if two values have partial overlap (e.g., age ranges)."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-empty" title="No Anys on this line!">            # Simple age range overlap check</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">            if isinstance(value1, str) and isinstance(value2, str) and "-" in value1 and "-" in value2:</span>
<span class="line-empty" title="No Anys on this line!">                # Parse age ranges like "25-34", "30-45"</span>
<span class="line-precise" title="No Anys on this line!">                range1 = value1.split("-")</span>
<span class="line-precise" title="No Anys on this line!">                range2 = value2.split("-")</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                if len(range1) == 2 and len(range2) == 2:</span>
<span class="line-precise" title="No Anys on this line!">                    start1, end1 = int(range1[0]), int(range1[1])</span>
<span class="line-precise" title="No Anys on this line!">                    start2, end2 = int(range2[0]), int(range2[1])</span>
<span class="line-empty" title="No Anys on this line!">                    </span>
<span class="line-empty" title="No Anys on this line!">                    # Check for any overlap</span>
<span class="line-precise" title="No Anys on this line!">                    return not (end1 &lt; start2 or end2 &lt; start1)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return False</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except (ValueError, IndexError):</span>
<span class="line-precise" title="No Anys on this line!">            return False</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Additional implementation methods continue...</span>
<span class="line-empty" title="No Anys on this line!">    # (Similar patterns for other required functionality)</span>
</pre></td>
</tr></tbody>
</table>
</body>
</html>
