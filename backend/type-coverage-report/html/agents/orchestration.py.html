<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../mypy-html.css">
</head>
<body>
<h2>agents.orchestration</h2>
<table>
<caption>agents/orchestration.py</caption>
<tbody><tr>
<td class="table-lines"><pre><span id="L1" class="lineno"><a class="lineno" href="#L1">1</a></span>
<span id="L2" class="lineno"><a class="lineno" href="#L2">2</a></span>
<span id="L3" class="lineno"><a class="lineno" href="#L3">3</a></span>
<span id="L4" class="lineno"><a class="lineno" href="#L4">4</a></span>
<span id="L5" class="lineno"><a class="lineno" href="#L5">5</a></span>
<span id="L6" class="lineno"><a class="lineno" href="#L6">6</a></span>
<span id="L7" class="lineno"><a class="lineno" href="#L7">7</a></span>
<span id="L8" class="lineno"><a class="lineno" href="#L8">8</a></span>
<span id="L9" class="lineno"><a class="lineno" href="#L9">9</a></span>
<span id="L10" class="lineno"><a class="lineno" href="#L10">10</a></span>
<span id="L11" class="lineno"><a class="lineno" href="#L11">11</a></span>
<span id="L12" class="lineno"><a class="lineno" href="#L12">12</a></span>
<span id="L13" class="lineno"><a class="lineno" href="#L13">13</a></span>
<span id="L14" class="lineno"><a class="lineno" href="#L14">14</a></span>
<span id="L15" class="lineno"><a class="lineno" href="#L15">15</a></span>
<span id="L16" class="lineno"><a class="lineno" href="#L16">16</a></span>
<span id="L17" class="lineno"><a class="lineno" href="#L17">17</a></span>
<span id="L18" class="lineno"><a class="lineno" href="#L18">18</a></span>
<span id="L19" class="lineno"><a class="lineno" href="#L19">19</a></span>
<span id="L20" class="lineno"><a class="lineno" href="#L20">20</a></span>
<span id="L21" class="lineno"><a class="lineno" href="#L21">21</a></span>
<span id="L22" class="lineno"><a class="lineno" href="#L22">22</a></span>
<span id="L23" class="lineno"><a class="lineno" href="#L23">23</a></span>
<span id="L24" class="lineno"><a class="lineno" href="#L24">24</a></span>
<span id="L25" class="lineno"><a class="lineno" href="#L25">25</a></span>
<span id="L26" class="lineno"><a class="lineno" href="#L26">26</a></span>
<span id="L27" class="lineno"><a class="lineno" href="#L27">27</a></span>
<span id="L28" class="lineno"><a class="lineno" href="#L28">28</a></span>
<span id="L29" class="lineno"><a class="lineno" href="#L29">29</a></span>
<span id="L30" class="lineno"><a class="lineno" href="#L30">30</a></span>
<span id="L31" class="lineno"><a class="lineno" href="#L31">31</a></span>
<span id="L32" class="lineno"><a class="lineno" href="#L32">32</a></span>
<span id="L33" class="lineno"><a class="lineno" href="#L33">33</a></span>
<span id="L34" class="lineno"><a class="lineno" href="#L34">34</a></span>
<span id="L35" class="lineno"><a class="lineno" href="#L35">35</a></span>
<span id="L36" class="lineno"><a class="lineno" href="#L36">36</a></span>
<span id="L37" class="lineno"><a class="lineno" href="#L37">37</a></span>
<span id="L38" class="lineno"><a class="lineno" href="#L38">38</a></span>
<span id="L39" class="lineno"><a class="lineno" href="#L39">39</a></span>
<span id="L40" class="lineno"><a class="lineno" href="#L40">40</a></span>
<span id="L41" class="lineno"><a class="lineno" href="#L41">41</a></span>
<span id="L42" class="lineno"><a class="lineno" href="#L42">42</a></span>
<span id="L43" class="lineno"><a class="lineno" href="#L43">43</a></span>
<span id="L44" class="lineno"><a class="lineno" href="#L44">44</a></span>
<span id="L45" class="lineno"><a class="lineno" href="#L45">45</a></span>
<span id="L46" class="lineno"><a class="lineno" href="#L46">46</a></span>
<span id="L47" class="lineno"><a class="lineno" href="#L47">47</a></span>
<span id="L48" class="lineno"><a class="lineno" href="#L48">48</a></span>
<span id="L49" class="lineno"><a class="lineno" href="#L49">49</a></span>
<span id="L50" class="lineno"><a class="lineno" href="#L50">50</a></span>
<span id="L51" class="lineno"><a class="lineno" href="#L51">51</a></span>
<span id="L52" class="lineno"><a class="lineno" href="#L52">52</a></span>
<span id="L53" class="lineno"><a class="lineno" href="#L53">53</a></span>
<span id="L54" class="lineno"><a class="lineno" href="#L54">54</a></span>
<span id="L55" class="lineno"><a class="lineno" href="#L55">55</a></span>
<span id="L56" class="lineno"><a class="lineno" href="#L56">56</a></span>
<span id="L57" class="lineno"><a class="lineno" href="#L57">57</a></span>
<span id="L58" class="lineno"><a class="lineno" href="#L58">58</a></span>
<span id="L59" class="lineno"><a class="lineno" href="#L59">59</a></span>
<span id="L60" class="lineno"><a class="lineno" href="#L60">60</a></span>
<span id="L61" class="lineno"><a class="lineno" href="#L61">61</a></span>
<span id="L62" class="lineno"><a class="lineno" href="#L62">62</a></span>
<span id="L63" class="lineno"><a class="lineno" href="#L63">63</a></span>
<span id="L64" class="lineno"><a class="lineno" href="#L64">64</a></span>
<span id="L65" class="lineno"><a class="lineno" href="#L65">65</a></span>
<span id="L66" class="lineno"><a class="lineno" href="#L66">66</a></span>
<span id="L67" class="lineno"><a class="lineno" href="#L67">67</a></span>
<span id="L68" class="lineno"><a class="lineno" href="#L68">68</a></span>
<span id="L69" class="lineno"><a class="lineno" href="#L69">69</a></span>
<span id="L70" class="lineno"><a class="lineno" href="#L70">70</a></span>
<span id="L71" class="lineno"><a class="lineno" href="#L71">71</a></span>
<span id="L72" class="lineno"><a class="lineno" href="#L72">72</a></span>
<span id="L73" class="lineno"><a class="lineno" href="#L73">73</a></span>
<span id="L74" class="lineno"><a class="lineno" href="#L74">74</a></span>
<span id="L75" class="lineno"><a class="lineno" href="#L75">75</a></span>
<span id="L76" class="lineno"><a class="lineno" href="#L76">76</a></span>
<span id="L77" class="lineno"><a class="lineno" href="#L77">77</a></span>
<span id="L78" class="lineno"><a class="lineno" href="#L78">78</a></span>
<span id="L79" class="lineno"><a class="lineno" href="#L79">79</a></span>
<span id="L80" class="lineno"><a class="lineno" href="#L80">80</a></span>
<span id="L81" class="lineno"><a class="lineno" href="#L81">81</a></span>
<span id="L82" class="lineno"><a class="lineno" href="#L82">82</a></span>
<span id="L83" class="lineno"><a class="lineno" href="#L83">83</a></span>
<span id="L84" class="lineno"><a class="lineno" href="#L84">84</a></span>
<span id="L85" class="lineno"><a class="lineno" href="#L85">85</a></span>
<span id="L86" class="lineno"><a class="lineno" href="#L86">86</a></span>
<span id="L87" class="lineno"><a class="lineno" href="#L87">87</a></span>
<span id="L88" class="lineno"><a class="lineno" href="#L88">88</a></span>
<span id="L89" class="lineno"><a class="lineno" href="#L89">89</a></span>
<span id="L90" class="lineno"><a class="lineno" href="#L90">90</a></span>
<span id="L91" class="lineno"><a class="lineno" href="#L91">91</a></span>
<span id="L92" class="lineno"><a class="lineno" href="#L92">92</a></span>
<span id="L93" class="lineno"><a class="lineno" href="#L93">93</a></span>
<span id="L94" class="lineno"><a class="lineno" href="#L94">94</a></span>
<span id="L95" class="lineno"><a class="lineno" href="#L95">95</a></span>
<span id="L96" class="lineno"><a class="lineno" href="#L96">96</a></span>
<span id="L97" class="lineno"><a class="lineno" href="#L97">97</a></span>
<span id="L98" class="lineno"><a class="lineno" href="#L98">98</a></span>
<span id="L99" class="lineno"><a class="lineno" href="#L99">99</a></span>
<span id="L100" class="lineno"><a class="lineno" href="#L100">100</a></span>
<span id="L101" class="lineno"><a class="lineno" href="#L101">101</a></span>
<span id="L102" class="lineno"><a class="lineno" href="#L102">102</a></span>
<span id="L103" class="lineno"><a class="lineno" href="#L103">103</a></span>
<span id="L104" class="lineno"><a class="lineno" href="#L104">104</a></span>
<span id="L105" class="lineno"><a class="lineno" href="#L105">105</a></span>
<span id="L106" class="lineno"><a class="lineno" href="#L106">106</a></span>
<span id="L107" class="lineno"><a class="lineno" href="#L107">107</a></span>
<span id="L108" class="lineno"><a class="lineno" href="#L108">108</a></span>
<span id="L109" class="lineno"><a class="lineno" href="#L109">109</a></span>
<span id="L110" class="lineno"><a class="lineno" href="#L110">110</a></span>
<span id="L111" class="lineno"><a class="lineno" href="#L111">111</a></span>
<span id="L112" class="lineno"><a class="lineno" href="#L112">112</a></span>
<span id="L113" class="lineno"><a class="lineno" href="#L113">113</a></span>
<span id="L114" class="lineno"><a class="lineno" href="#L114">114</a></span>
<span id="L115" class="lineno"><a class="lineno" href="#L115">115</a></span>
<span id="L116" class="lineno"><a class="lineno" href="#L116">116</a></span>
<span id="L117" class="lineno"><a class="lineno" href="#L117">117</a></span>
<span id="L118" class="lineno"><a class="lineno" href="#L118">118</a></span>
<span id="L119" class="lineno"><a class="lineno" href="#L119">119</a></span>
<span id="L120" class="lineno"><a class="lineno" href="#L120">120</a></span>
<span id="L121" class="lineno"><a class="lineno" href="#L121">121</a></span>
<span id="L122" class="lineno"><a class="lineno" href="#L122">122</a></span>
<span id="L123" class="lineno"><a class="lineno" href="#L123">123</a></span>
<span id="L124" class="lineno"><a class="lineno" href="#L124">124</a></span>
<span id="L125" class="lineno"><a class="lineno" href="#L125">125</a></span>
<span id="L126" class="lineno"><a class="lineno" href="#L126">126</a></span>
<span id="L127" class="lineno"><a class="lineno" href="#L127">127</a></span>
<span id="L128" class="lineno"><a class="lineno" href="#L128">128</a></span>
<span id="L129" class="lineno"><a class="lineno" href="#L129">129</a></span>
<span id="L130" class="lineno"><a class="lineno" href="#L130">130</a></span>
<span id="L131" class="lineno"><a class="lineno" href="#L131">131</a></span>
<span id="L132" class="lineno"><a class="lineno" href="#L132">132</a></span>
<span id="L133" class="lineno"><a class="lineno" href="#L133">133</a></span>
<span id="L134" class="lineno"><a class="lineno" href="#L134">134</a></span>
<span id="L135" class="lineno"><a class="lineno" href="#L135">135</a></span>
<span id="L136" class="lineno"><a class="lineno" href="#L136">136</a></span>
<span id="L137" class="lineno"><a class="lineno" href="#L137">137</a></span>
<span id="L138" class="lineno"><a class="lineno" href="#L138">138</a></span>
<span id="L139" class="lineno"><a class="lineno" href="#L139">139</a></span>
<span id="L140" class="lineno"><a class="lineno" href="#L140">140</a></span>
<span id="L141" class="lineno"><a class="lineno" href="#L141">141</a></span>
<span id="L142" class="lineno"><a class="lineno" href="#L142">142</a></span>
<span id="L143" class="lineno"><a class="lineno" href="#L143">143</a></span>
<span id="L144" class="lineno"><a class="lineno" href="#L144">144</a></span>
<span id="L145" class="lineno"><a class="lineno" href="#L145">145</a></span>
<span id="L146" class="lineno"><a class="lineno" href="#L146">146</a></span>
<span id="L147" class="lineno"><a class="lineno" href="#L147">147</a></span>
<span id="L148" class="lineno"><a class="lineno" href="#L148">148</a></span>
<span id="L149" class="lineno"><a class="lineno" href="#L149">149</a></span>
<span id="L150" class="lineno"><a class="lineno" href="#L150">150</a></span>
<span id="L151" class="lineno"><a class="lineno" href="#L151">151</a></span>
<span id="L152" class="lineno"><a class="lineno" href="#L152">152</a></span>
<span id="L153" class="lineno"><a class="lineno" href="#L153">153</a></span>
<span id="L154" class="lineno"><a class="lineno" href="#L154">154</a></span>
<span id="L155" class="lineno"><a class="lineno" href="#L155">155</a></span>
<span id="L156" class="lineno"><a class="lineno" href="#L156">156</a></span>
<span id="L157" class="lineno"><a class="lineno" href="#L157">157</a></span>
<span id="L158" class="lineno"><a class="lineno" href="#L158">158</a></span>
<span id="L159" class="lineno"><a class="lineno" href="#L159">159</a></span>
<span id="L160" class="lineno"><a class="lineno" href="#L160">160</a></span>
<span id="L161" class="lineno"><a class="lineno" href="#L161">161</a></span>
<span id="L162" class="lineno"><a class="lineno" href="#L162">162</a></span>
<span id="L163" class="lineno"><a class="lineno" href="#L163">163</a></span>
<span id="L164" class="lineno"><a class="lineno" href="#L164">164</a></span>
<span id="L165" class="lineno"><a class="lineno" href="#L165">165</a></span>
<span id="L166" class="lineno"><a class="lineno" href="#L166">166</a></span>
<span id="L167" class="lineno"><a class="lineno" href="#L167">167</a></span>
<span id="L168" class="lineno"><a class="lineno" href="#L168">168</a></span>
<span id="L169" class="lineno"><a class="lineno" href="#L169">169</a></span>
<span id="L170" class="lineno"><a class="lineno" href="#L170">170</a></span>
<span id="L171" class="lineno"><a class="lineno" href="#L171">171</a></span>
<span id="L172" class="lineno"><a class="lineno" href="#L172">172</a></span>
<span id="L173" class="lineno"><a class="lineno" href="#L173">173</a></span>
<span id="L174" class="lineno"><a class="lineno" href="#L174">174</a></span>
<span id="L175" class="lineno"><a class="lineno" href="#L175">175</a></span>
<span id="L176" class="lineno"><a class="lineno" href="#L176">176</a></span>
<span id="L177" class="lineno"><a class="lineno" href="#L177">177</a></span>
<span id="L178" class="lineno"><a class="lineno" href="#L178">178</a></span>
<span id="L179" class="lineno"><a class="lineno" href="#L179">179</a></span>
<span id="L180" class="lineno"><a class="lineno" href="#L180">180</a></span>
<span id="L181" class="lineno"><a class="lineno" href="#L181">181</a></span>
<span id="L182" class="lineno"><a class="lineno" href="#L182">182</a></span>
<span id="L183" class="lineno"><a class="lineno" href="#L183">183</a></span>
<span id="L184" class="lineno"><a class="lineno" href="#L184">184</a></span>
<span id="L185" class="lineno"><a class="lineno" href="#L185">185</a></span>
<span id="L186" class="lineno"><a class="lineno" href="#L186">186</a></span>
<span id="L187" class="lineno"><a class="lineno" href="#L187">187</a></span>
<span id="L188" class="lineno"><a class="lineno" href="#L188">188</a></span>
<span id="L189" class="lineno"><a class="lineno" href="#L189">189</a></span>
<span id="L190" class="lineno"><a class="lineno" href="#L190">190</a></span>
<span id="L191" class="lineno"><a class="lineno" href="#L191">191</a></span>
<span id="L192" class="lineno"><a class="lineno" href="#L192">192</a></span>
<span id="L193" class="lineno"><a class="lineno" href="#L193">193</a></span>
<span id="L194" class="lineno"><a class="lineno" href="#L194">194</a></span>
<span id="L195" class="lineno"><a class="lineno" href="#L195">195</a></span>
<span id="L196" class="lineno"><a class="lineno" href="#L196">196</a></span>
<span id="L197" class="lineno"><a class="lineno" href="#L197">197</a></span>
<span id="L198" class="lineno"><a class="lineno" href="#L198">198</a></span>
<span id="L199" class="lineno"><a class="lineno" href="#L199">199</a></span>
<span id="L200" class="lineno"><a class="lineno" href="#L200">200</a></span>
<span id="L201" class="lineno"><a class="lineno" href="#L201">201</a></span>
<span id="L202" class="lineno"><a class="lineno" href="#L202">202</a></span>
<span id="L203" class="lineno"><a class="lineno" href="#L203">203</a></span>
<span id="L204" class="lineno"><a class="lineno" href="#L204">204</a></span>
<span id="L205" class="lineno"><a class="lineno" href="#L205">205</a></span>
<span id="L206" class="lineno"><a class="lineno" href="#L206">206</a></span>
<span id="L207" class="lineno"><a class="lineno" href="#L207">207</a></span>
<span id="L208" class="lineno"><a class="lineno" href="#L208">208</a></span>
<span id="L209" class="lineno"><a class="lineno" href="#L209">209</a></span>
<span id="L210" class="lineno"><a class="lineno" href="#L210">210</a></span>
<span id="L211" class="lineno"><a class="lineno" href="#L211">211</a></span>
<span id="L212" class="lineno"><a class="lineno" href="#L212">212</a></span>
<span id="L213" class="lineno"><a class="lineno" href="#L213">213</a></span>
<span id="L214" class="lineno"><a class="lineno" href="#L214">214</a></span>
<span id="L215" class="lineno"><a class="lineno" href="#L215">215</a></span>
<span id="L216" class="lineno"><a class="lineno" href="#L216">216</a></span>
<span id="L217" class="lineno"><a class="lineno" href="#L217">217</a></span>
<span id="L218" class="lineno"><a class="lineno" href="#L218">218</a></span>
<span id="L219" class="lineno"><a class="lineno" href="#L219">219</a></span>
<span id="L220" class="lineno"><a class="lineno" href="#L220">220</a></span>
<span id="L221" class="lineno"><a class="lineno" href="#L221">221</a></span>
<span id="L222" class="lineno"><a class="lineno" href="#L222">222</a></span>
<span id="L223" class="lineno"><a class="lineno" href="#L223">223</a></span>
<span id="L224" class="lineno"><a class="lineno" href="#L224">224</a></span>
<span id="L225" class="lineno"><a class="lineno" href="#L225">225</a></span>
<span id="L226" class="lineno"><a class="lineno" href="#L226">226</a></span>
<span id="L227" class="lineno"><a class="lineno" href="#L227">227</a></span>
<span id="L228" class="lineno"><a class="lineno" href="#L228">228</a></span>
<span id="L229" class="lineno"><a class="lineno" href="#L229">229</a></span>
<span id="L230" class="lineno"><a class="lineno" href="#L230">230</a></span>
<span id="L231" class="lineno"><a class="lineno" href="#L231">231</a></span>
<span id="L232" class="lineno"><a class="lineno" href="#L232">232</a></span>
<span id="L233" class="lineno"><a class="lineno" href="#L233">233</a></span>
<span id="L234" class="lineno"><a class="lineno" href="#L234">234</a></span>
<span id="L235" class="lineno"><a class="lineno" href="#L235">235</a></span>
<span id="L236" class="lineno"><a class="lineno" href="#L236">236</a></span>
<span id="L237" class="lineno"><a class="lineno" href="#L237">237</a></span>
<span id="L238" class="lineno"><a class="lineno" href="#L238">238</a></span>
<span id="L239" class="lineno"><a class="lineno" href="#L239">239</a></span>
<span id="L240" class="lineno"><a class="lineno" href="#L240">240</a></span>
<span id="L241" class="lineno"><a class="lineno" href="#L241">241</a></span>
<span id="L242" class="lineno"><a class="lineno" href="#L242">242</a></span>
<span id="L243" class="lineno"><a class="lineno" href="#L243">243</a></span>
<span id="L244" class="lineno"><a class="lineno" href="#L244">244</a></span>
<span id="L245" class="lineno"><a class="lineno" href="#L245">245</a></span>
<span id="L246" class="lineno"><a class="lineno" href="#L246">246</a></span>
<span id="L247" class="lineno"><a class="lineno" href="#L247">247</a></span>
<span id="L248" class="lineno"><a class="lineno" href="#L248">248</a></span>
<span id="L249" class="lineno"><a class="lineno" href="#L249">249</a></span>
<span id="L250" class="lineno"><a class="lineno" href="#L250">250</a></span>
<span id="L251" class="lineno"><a class="lineno" href="#L251">251</a></span>
<span id="L252" class="lineno"><a class="lineno" href="#L252">252</a></span>
<span id="L253" class="lineno"><a class="lineno" href="#L253">253</a></span>
<span id="L254" class="lineno"><a class="lineno" href="#L254">254</a></span>
<span id="L255" class="lineno"><a class="lineno" href="#L255">255</a></span>
<span id="L256" class="lineno"><a class="lineno" href="#L256">256</a></span>
<span id="L257" class="lineno"><a class="lineno" href="#L257">257</a></span>
<span id="L258" class="lineno"><a class="lineno" href="#L258">258</a></span>
<span id="L259" class="lineno"><a class="lineno" href="#L259">259</a></span>
<span id="L260" class="lineno"><a class="lineno" href="#L260">260</a></span>
<span id="L261" class="lineno"><a class="lineno" href="#L261">261</a></span>
<span id="L262" class="lineno"><a class="lineno" href="#L262">262</a></span>
<span id="L263" class="lineno"><a class="lineno" href="#L263">263</a></span>
<span id="L264" class="lineno"><a class="lineno" href="#L264">264</a></span>
<span id="L265" class="lineno"><a class="lineno" href="#L265">265</a></span>
<span id="L266" class="lineno"><a class="lineno" href="#L266">266</a></span>
<span id="L267" class="lineno"><a class="lineno" href="#L267">267</a></span>
<span id="L268" class="lineno"><a class="lineno" href="#L268">268</a></span>
<span id="L269" class="lineno"><a class="lineno" href="#L269">269</a></span>
<span id="L270" class="lineno"><a class="lineno" href="#L270">270</a></span>
<span id="L271" class="lineno"><a class="lineno" href="#L271">271</a></span>
<span id="L272" class="lineno"><a class="lineno" href="#L272">272</a></span>
<span id="L273" class="lineno"><a class="lineno" href="#L273">273</a></span>
<span id="L274" class="lineno"><a class="lineno" href="#L274">274</a></span>
<span id="L275" class="lineno"><a class="lineno" href="#L275">275</a></span>
<span id="L276" class="lineno"><a class="lineno" href="#L276">276</a></span>
<span id="L277" class="lineno"><a class="lineno" href="#L277">277</a></span>
<span id="L278" class="lineno"><a class="lineno" href="#L278">278</a></span>
<span id="L279" class="lineno"><a class="lineno" href="#L279">279</a></span>
<span id="L280" class="lineno"><a class="lineno" href="#L280">280</a></span>
<span id="L281" class="lineno"><a class="lineno" href="#L281">281</a></span>
<span id="L282" class="lineno"><a class="lineno" href="#L282">282</a></span>
<span id="L283" class="lineno"><a class="lineno" href="#L283">283</a></span>
<span id="L284" class="lineno"><a class="lineno" href="#L284">284</a></span>
<span id="L285" class="lineno"><a class="lineno" href="#L285">285</a></span>
<span id="L286" class="lineno"><a class="lineno" href="#L286">286</a></span>
<span id="L287" class="lineno"><a class="lineno" href="#L287">287</a></span>
<span id="L288" class="lineno"><a class="lineno" href="#L288">288</a></span>
<span id="L289" class="lineno"><a class="lineno" href="#L289">289</a></span>
<span id="L290" class="lineno"><a class="lineno" href="#L290">290</a></span>
<span id="L291" class="lineno"><a class="lineno" href="#L291">291</a></span>
<span id="L292" class="lineno"><a class="lineno" href="#L292">292</a></span>
<span id="L293" class="lineno"><a class="lineno" href="#L293">293</a></span>
<span id="L294" class="lineno"><a class="lineno" href="#L294">294</a></span>
<span id="L295" class="lineno"><a class="lineno" href="#L295">295</a></span>
<span id="L296" class="lineno"><a class="lineno" href="#L296">296</a></span>
<span id="L297" class="lineno"><a class="lineno" href="#L297">297</a></span>
<span id="L298" class="lineno"><a class="lineno" href="#L298">298</a></span>
<span id="L299" class="lineno"><a class="lineno" href="#L299">299</a></span>
<span id="L300" class="lineno"><a class="lineno" href="#L300">300</a></span>
<span id="L301" class="lineno"><a class="lineno" href="#L301">301</a></span>
<span id="L302" class="lineno"><a class="lineno" href="#L302">302</a></span>
<span id="L303" class="lineno"><a class="lineno" href="#L303">303</a></span>
<span id="L304" class="lineno"><a class="lineno" href="#L304">304</a></span>
<span id="L305" class="lineno"><a class="lineno" href="#L305">305</a></span>
<span id="L306" class="lineno"><a class="lineno" href="#L306">306</a></span>
<span id="L307" class="lineno"><a class="lineno" href="#L307">307</a></span>
<span id="L308" class="lineno"><a class="lineno" href="#L308">308</a></span>
<span id="L309" class="lineno"><a class="lineno" href="#L309">309</a></span>
<span id="L310" class="lineno"><a class="lineno" href="#L310">310</a></span>
<span id="L311" class="lineno"><a class="lineno" href="#L311">311</a></span>
<span id="L312" class="lineno"><a class="lineno" href="#L312">312</a></span>
<span id="L313" class="lineno"><a class="lineno" href="#L313">313</a></span>
<span id="L314" class="lineno"><a class="lineno" href="#L314">314</a></span>
<span id="L315" class="lineno"><a class="lineno" href="#L315">315</a></span>
<span id="L316" class="lineno"><a class="lineno" href="#L316">316</a></span>
<span id="L317" class="lineno"><a class="lineno" href="#L317">317</a></span>
<span id="L318" class="lineno"><a class="lineno" href="#L318">318</a></span>
<span id="L319" class="lineno"><a class="lineno" href="#L319">319</a></span>
<span id="L320" class="lineno"><a class="lineno" href="#L320">320</a></span>
<span id="L321" class="lineno"><a class="lineno" href="#L321">321</a></span>
<span id="L322" class="lineno"><a class="lineno" href="#L322">322</a></span>
<span id="L323" class="lineno"><a class="lineno" href="#L323">323</a></span>
<span id="L324" class="lineno"><a class="lineno" href="#L324">324</a></span>
<span id="L325" class="lineno"><a class="lineno" href="#L325">325</a></span>
<span id="L326" class="lineno"><a class="lineno" href="#L326">326</a></span>
<span id="L327" class="lineno"><a class="lineno" href="#L327">327</a></span>
<span id="L328" class="lineno"><a class="lineno" href="#L328">328</a></span>
<span id="L329" class="lineno"><a class="lineno" href="#L329">329</a></span>
<span id="L330" class="lineno"><a class="lineno" href="#L330">330</a></span>
<span id="L331" class="lineno"><a class="lineno" href="#L331">331</a></span>
<span id="L332" class="lineno"><a class="lineno" href="#L332">332</a></span>
<span id="L333" class="lineno"><a class="lineno" href="#L333">333</a></span>
<span id="L334" class="lineno"><a class="lineno" href="#L334">334</a></span>
<span id="L335" class="lineno"><a class="lineno" href="#L335">335</a></span>
<span id="L336" class="lineno"><a class="lineno" href="#L336">336</a></span>
<span id="L337" class="lineno"><a class="lineno" href="#L337">337</a></span>
<span id="L338" class="lineno"><a class="lineno" href="#L338">338</a></span>
<span id="L339" class="lineno"><a class="lineno" href="#L339">339</a></span>
<span id="L340" class="lineno"><a class="lineno" href="#L340">340</a></span>
<span id="L341" class="lineno"><a class="lineno" href="#L341">341</a></span>
<span id="L342" class="lineno"><a class="lineno" href="#L342">342</a></span>
<span id="L343" class="lineno"><a class="lineno" href="#L343">343</a></span>
<span id="L344" class="lineno"><a class="lineno" href="#L344">344</a></span>
<span id="L345" class="lineno"><a class="lineno" href="#L345">345</a></span>
<span id="L346" class="lineno"><a class="lineno" href="#L346">346</a></span>
<span id="L347" class="lineno"><a class="lineno" href="#L347">347</a></span>
<span id="L348" class="lineno"><a class="lineno" href="#L348">348</a></span>
<span id="L349" class="lineno"><a class="lineno" href="#L349">349</a></span>
<span id="L350" class="lineno"><a class="lineno" href="#L350">350</a></span>
<span id="L351" class="lineno"><a class="lineno" href="#L351">351</a></span>
<span id="L352" class="lineno"><a class="lineno" href="#L352">352</a></span>
<span id="L353" class="lineno"><a class="lineno" href="#L353">353</a></span>
<span id="L354" class="lineno"><a class="lineno" href="#L354">354</a></span>
<span id="L355" class="lineno"><a class="lineno" href="#L355">355</a></span>
<span id="L356" class="lineno"><a class="lineno" href="#L356">356</a></span>
<span id="L357" class="lineno"><a class="lineno" href="#L357">357</a></span>
<span id="L358" class="lineno"><a class="lineno" href="#L358">358</a></span>
<span id="L359" class="lineno"><a class="lineno" href="#L359">359</a></span>
<span id="L360" class="lineno"><a class="lineno" href="#L360">360</a></span>
<span id="L361" class="lineno"><a class="lineno" href="#L361">361</a></span>
<span id="L362" class="lineno"><a class="lineno" href="#L362">362</a></span>
<span id="L363" class="lineno"><a class="lineno" href="#L363">363</a></span>
<span id="L364" class="lineno"><a class="lineno" href="#L364">364</a></span>
<span id="L365" class="lineno"><a class="lineno" href="#L365">365</a></span>
<span id="L366" class="lineno"><a class="lineno" href="#L366">366</a></span>
<span id="L367" class="lineno"><a class="lineno" href="#L367">367</a></span>
<span id="L368" class="lineno"><a class="lineno" href="#L368">368</a></span>
<span id="L369" class="lineno"><a class="lineno" href="#L369">369</a></span>
<span id="L370" class="lineno"><a class="lineno" href="#L370">370</a></span>
<span id="L371" class="lineno"><a class="lineno" href="#L371">371</a></span>
<span id="L372" class="lineno"><a class="lineno" href="#L372">372</a></span>
<span id="L373" class="lineno"><a class="lineno" href="#L373">373</a></span>
<span id="L374" class="lineno"><a class="lineno" href="#L374">374</a></span>
<span id="L375" class="lineno"><a class="lineno" href="#L375">375</a></span>
<span id="L376" class="lineno"><a class="lineno" href="#L376">376</a></span>
<span id="L377" class="lineno"><a class="lineno" href="#L377">377</a></span>
<span id="L378" class="lineno"><a class="lineno" href="#L378">378</a></span>
<span id="L379" class="lineno"><a class="lineno" href="#L379">379</a></span>
<span id="L380" class="lineno"><a class="lineno" href="#L380">380</a></span>
<span id="L381" class="lineno"><a class="lineno" href="#L381">381</a></span>
<span id="L382" class="lineno"><a class="lineno" href="#L382">382</a></span>
<span id="L383" class="lineno"><a class="lineno" href="#L383">383</a></span>
<span id="L384" class="lineno"><a class="lineno" href="#L384">384</a></span>
<span id="L385" class="lineno"><a class="lineno" href="#L385">385</a></span>
<span id="L386" class="lineno"><a class="lineno" href="#L386">386</a></span>
<span id="L387" class="lineno"><a class="lineno" href="#L387">387</a></span>
<span id="L388" class="lineno"><a class="lineno" href="#L388">388</a></span>
<span id="L389" class="lineno"><a class="lineno" href="#L389">389</a></span>
<span id="L390" class="lineno"><a class="lineno" href="#L390">390</a></span>
<span id="L391" class="lineno"><a class="lineno" href="#L391">391</a></span>
<span id="L392" class="lineno"><a class="lineno" href="#L392">392</a></span>
<span id="L393" class="lineno"><a class="lineno" href="#L393">393</a></span>
<span id="L394" class="lineno"><a class="lineno" href="#L394">394</a></span>
<span id="L395" class="lineno"><a class="lineno" href="#L395">395</a></span>
<span id="L396" class="lineno"><a class="lineno" href="#L396">396</a></span>
<span id="L397" class="lineno"><a class="lineno" href="#L397">397</a></span>
<span id="L398" class="lineno"><a class="lineno" href="#L398">398</a></span>
<span id="L399" class="lineno"><a class="lineno" href="#L399">399</a></span>
<span id="L400" class="lineno"><a class="lineno" href="#L400">400</a></span>
<span id="L401" class="lineno"><a class="lineno" href="#L401">401</a></span>
<span id="L402" class="lineno"><a class="lineno" href="#L402">402</a></span>
<span id="L403" class="lineno"><a class="lineno" href="#L403">403</a></span>
<span id="L404" class="lineno"><a class="lineno" href="#L404">404</a></span>
<span id="L405" class="lineno"><a class="lineno" href="#L405">405</a></span>
<span id="L406" class="lineno"><a class="lineno" href="#L406">406</a></span>
<span id="L407" class="lineno"><a class="lineno" href="#L407">407</a></span>
<span id="L408" class="lineno"><a class="lineno" href="#L408">408</a></span>
<span id="L409" class="lineno"><a class="lineno" href="#L409">409</a></span>
<span id="L410" class="lineno"><a class="lineno" href="#L410">410</a></span>
<span id="L411" class="lineno"><a class="lineno" href="#L411">411</a></span>
<span id="L412" class="lineno"><a class="lineno" href="#L412">412</a></span>
<span id="L413" class="lineno"><a class="lineno" href="#L413">413</a></span>
<span id="L414" class="lineno"><a class="lineno" href="#L414">414</a></span>
<span id="L415" class="lineno"><a class="lineno" href="#L415">415</a></span>
<span id="L416" class="lineno"><a class="lineno" href="#L416">416</a></span>
<span id="L417" class="lineno"><a class="lineno" href="#L417">417</a></span>
<span id="L418" class="lineno"><a class="lineno" href="#L418">418</a></span>
<span id="L419" class="lineno"><a class="lineno" href="#L419">419</a></span>
<span id="L420" class="lineno"><a class="lineno" href="#L420">420</a></span>
<span id="L421" class="lineno"><a class="lineno" href="#L421">421</a></span>
<span id="L422" class="lineno"><a class="lineno" href="#L422">422</a></span>
<span id="L423" class="lineno"><a class="lineno" href="#L423">423</a></span>
<span id="L424" class="lineno"><a class="lineno" href="#L424">424</a></span>
<span id="L425" class="lineno"><a class="lineno" href="#L425">425</a></span>
<span id="L426" class="lineno"><a class="lineno" href="#L426">426</a></span>
<span id="L427" class="lineno"><a class="lineno" href="#L427">427</a></span>
<span id="L428" class="lineno"><a class="lineno" href="#L428">428</a></span>
<span id="L429" class="lineno"><a class="lineno" href="#L429">429</a></span>
<span id="L430" class="lineno"><a class="lineno" href="#L430">430</a></span>
<span id="L431" class="lineno"><a class="lineno" href="#L431">431</a></span>
<span id="L432" class="lineno"><a class="lineno" href="#L432">432</a></span>
<span id="L433" class="lineno"><a class="lineno" href="#L433">433</a></span>
<span id="L434" class="lineno"><a class="lineno" href="#L434">434</a></span>
<span id="L435" class="lineno"><a class="lineno" href="#L435">435</a></span>
<span id="L436" class="lineno"><a class="lineno" href="#L436">436</a></span>
<span id="L437" class="lineno"><a class="lineno" href="#L437">437</a></span>
<span id="L438" class="lineno"><a class="lineno" href="#L438">438</a></span>
<span id="L439" class="lineno"><a class="lineno" href="#L439">439</a></span>
<span id="L440" class="lineno"><a class="lineno" href="#L440">440</a></span>
<span id="L441" class="lineno"><a class="lineno" href="#L441">441</a></span>
<span id="L442" class="lineno"><a class="lineno" href="#L442">442</a></span>
<span id="L443" class="lineno"><a class="lineno" href="#L443">443</a></span>
<span id="L444" class="lineno"><a class="lineno" href="#L444">444</a></span>
<span id="L445" class="lineno"><a class="lineno" href="#L445">445</a></span>
<span id="L446" class="lineno"><a class="lineno" href="#L446">446</a></span>
<span id="L447" class="lineno"><a class="lineno" href="#L447">447</a></span>
<span id="L448" class="lineno"><a class="lineno" href="#L448">448</a></span>
<span id="L449" class="lineno"><a class="lineno" href="#L449">449</a></span>
<span id="L450" class="lineno"><a class="lineno" href="#L450">450</a></span>
<span id="L451" class="lineno"><a class="lineno" href="#L451">451</a></span>
<span id="L452" class="lineno"><a class="lineno" href="#L452">452</a></span>
<span id="L453" class="lineno"><a class="lineno" href="#L453">453</a></span>
<span id="L454" class="lineno"><a class="lineno" href="#L454">454</a></span>
<span id="L455" class="lineno"><a class="lineno" href="#L455">455</a></span>
<span id="L456" class="lineno"><a class="lineno" href="#L456">456</a></span>
<span id="L457" class="lineno"><a class="lineno" href="#L457">457</a></span>
<span id="L458" class="lineno"><a class="lineno" href="#L458">458</a></span>
<span id="L459" class="lineno"><a class="lineno" href="#L459">459</a></span>
<span id="L460" class="lineno"><a class="lineno" href="#L460">460</a></span>
<span id="L461" class="lineno"><a class="lineno" href="#L461">461</a></span>
<span id="L462" class="lineno"><a class="lineno" href="#L462">462</a></span>
<span id="L463" class="lineno"><a class="lineno" href="#L463">463</a></span>
<span id="L464" class="lineno"><a class="lineno" href="#L464">464</a></span>
<span id="L465" class="lineno"><a class="lineno" href="#L465">465</a></span>
<span id="L466" class="lineno"><a class="lineno" href="#L466">466</a></span>
<span id="L467" class="lineno"><a class="lineno" href="#L467">467</a></span>
<span id="L468" class="lineno"><a class="lineno" href="#L468">468</a></span>
<span id="L469" class="lineno"><a class="lineno" href="#L469">469</a></span>
<span id="L470" class="lineno"><a class="lineno" href="#L470">470</a></span>
<span id="L471" class="lineno"><a class="lineno" href="#L471">471</a></span>
<span id="L472" class="lineno"><a class="lineno" href="#L472">472</a></span>
<span id="L473" class="lineno"><a class="lineno" href="#L473">473</a></span>
<span id="L474" class="lineno"><a class="lineno" href="#L474">474</a></span>
<span id="L475" class="lineno"><a class="lineno" href="#L475">475</a></span>
<span id="L476" class="lineno"><a class="lineno" href="#L476">476</a></span>
<span id="L477" class="lineno"><a class="lineno" href="#L477">477</a></span>
<span id="L478" class="lineno"><a class="lineno" href="#L478">478</a></span>
<span id="L479" class="lineno"><a class="lineno" href="#L479">479</a></span>
<span id="L480" class="lineno"><a class="lineno" href="#L480">480</a></span>
<span id="L481" class="lineno"><a class="lineno" href="#L481">481</a></span>
<span id="L482" class="lineno"><a class="lineno" href="#L482">482</a></span>
<span id="L483" class="lineno"><a class="lineno" href="#L483">483</a></span>
<span id="L484" class="lineno"><a class="lineno" href="#L484">484</a></span>
<span id="L485" class="lineno"><a class="lineno" href="#L485">485</a></span>
<span id="L486" class="lineno"><a class="lineno" href="#L486">486</a></span>
<span id="L487" class="lineno"><a class="lineno" href="#L487">487</a></span>
<span id="L488" class="lineno"><a class="lineno" href="#L488">488</a></span>
<span id="L489" class="lineno"><a class="lineno" href="#L489">489</a></span>
<span id="L490" class="lineno"><a class="lineno" href="#L490">490</a></span>
<span id="L491" class="lineno"><a class="lineno" href="#L491">491</a></span>
<span id="L492" class="lineno"><a class="lineno" href="#L492">492</a></span>
<span id="L493" class="lineno"><a class="lineno" href="#L493">493</a></span>
<span id="L494" class="lineno"><a class="lineno" href="#L494">494</a></span>
<span id="L495" class="lineno"><a class="lineno" href="#L495">495</a></span>
<span id="L496" class="lineno"><a class="lineno" href="#L496">496</a></span>
<span id="L497" class="lineno"><a class="lineno" href="#L497">497</a></span>
<span id="L498" class="lineno"><a class="lineno" href="#L498">498</a></span>
<span id="L499" class="lineno"><a class="lineno" href="#L499">499</a></span>
<span id="L500" class="lineno"><a class="lineno" href="#L500">500</a></span>
<span id="L501" class="lineno"><a class="lineno" href="#L501">501</a></span>
<span id="L502" class="lineno"><a class="lineno" href="#L502">502</a></span>
<span id="L503" class="lineno"><a class="lineno" href="#L503">503</a></span>
<span id="L504" class="lineno"><a class="lineno" href="#L504">504</a></span>
<span id="L505" class="lineno"><a class="lineno" href="#L505">505</a></span>
<span id="L506" class="lineno"><a class="lineno" href="#L506">506</a></span>
<span id="L507" class="lineno"><a class="lineno" href="#L507">507</a></span>
<span id="L508" class="lineno"><a class="lineno" href="#L508">508</a></span>
<span id="L509" class="lineno"><a class="lineno" href="#L509">509</a></span>
<span id="L510" class="lineno"><a class="lineno" href="#L510">510</a></span>
<span id="L511" class="lineno"><a class="lineno" href="#L511">511</a></span>
<span id="L512" class="lineno"><a class="lineno" href="#L512">512</a></span>
<span id="L513" class="lineno"><a class="lineno" href="#L513">513</a></span>
<span id="L514" class="lineno"><a class="lineno" href="#L514">514</a></span>
<span id="L515" class="lineno"><a class="lineno" href="#L515">515</a></span>
<span id="L516" class="lineno"><a class="lineno" href="#L516">516</a></span>
<span id="L517" class="lineno"><a class="lineno" href="#L517">517</a></span>
<span id="L518" class="lineno"><a class="lineno" href="#L518">518</a></span>
<span id="L519" class="lineno"><a class="lineno" href="#L519">519</a></span>
<span id="L520" class="lineno"><a class="lineno" href="#L520">520</a></span>
<span id="L521" class="lineno"><a class="lineno" href="#L521">521</a></span>
<span id="L522" class="lineno"><a class="lineno" href="#L522">522</a></span>
<span id="L523" class="lineno"><a class="lineno" href="#L523">523</a></span>
<span id="L524" class="lineno"><a class="lineno" href="#L524">524</a></span>
<span id="L525" class="lineno"><a class="lineno" href="#L525">525</a></span>
<span id="L526" class="lineno"><a class="lineno" href="#L526">526</a></span>
<span id="L527" class="lineno"><a class="lineno" href="#L527">527</a></span>
<span id="L528" class="lineno"><a class="lineno" href="#L528">528</a></span>
<span id="L529" class="lineno"><a class="lineno" href="#L529">529</a></span>
<span id="L530" class="lineno"><a class="lineno" href="#L530">530</a></span>
<span id="L531" class="lineno"><a class="lineno" href="#L531">531</a></span>
<span id="L532" class="lineno"><a class="lineno" href="#L532">532</a></span>
<span id="L533" class="lineno"><a class="lineno" href="#L533">533</a></span>
<span id="L534" class="lineno"><a class="lineno" href="#L534">534</a></span>
<span id="L535" class="lineno"><a class="lineno" href="#L535">535</a></span>
<span id="L536" class="lineno"><a class="lineno" href="#L536">536</a></span>
<span id="L537" class="lineno"><a class="lineno" href="#L537">537</a></span>
<span id="L538" class="lineno"><a class="lineno" href="#L538">538</a></span>
<span id="L539" class="lineno"><a class="lineno" href="#L539">539</a></span>
<span id="L540" class="lineno"><a class="lineno" href="#L540">540</a></span>
<span id="L541" class="lineno"><a class="lineno" href="#L541">541</a></span>
<span id="L542" class="lineno"><a class="lineno" href="#L542">542</a></span>
<span id="L543" class="lineno"><a class="lineno" href="#L543">543</a></span>
<span id="L544" class="lineno"><a class="lineno" href="#L544">544</a></span>
<span id="L545" class="lineno"><a class="lineno" href="#L545">545</a></span>
<span id="L546" class="lineno"><a class="lineno" href="#L546">546</a></span>
<span id="L547" class="lineno"><a class="lineno" href="#L547">547</a></span>
<span id="L548" class="lineno"><a class="lineno" href="#L548">548</a></span>
<span id="L549" class="lineno"><a class="lineno" href="#L549">549</a></span>
<span id="L550" class="lineno"><a class="lineno" href="#L550">550</a></span>
<span id="L551" class="lineno"><a class="lineno" href="#L551">551</a></span>
<span id="L552" class="lineno"><a class="lineno" href="#L552">552</a></span>
<span id="L553" class="lineno"><a class="lineno" href="#L553">553</a></span>
<span id="L554" class="lineno"><a class="lineno" href="#L554">554</a></span>
<span id="L555" class="lineno"><a class="lineno" href="#L555">555</a></span>
<span id="L556" class="lineno"><a class="lineno" href="#L556">556</a></span>
<span id="L557" class="lineno"><a class="lineno" href="#L557">557</a></span>
<span id="L558" class="lineno"><a class="lineno" href="#L558">558</a></span>
<span id="L559" class="lineno"><a class="lineno" href="#L559">559</a></span>
<span id="L560" class="lineno"><a class="lineno" href="#L560">560</a></span>
<span id="L561" class="lineno"><a class="lineno" href="#L561">561</a></span>
<span id="L562" class="lineno"><a class="lineno" href="#L562">562</a></span>
<span id="L563" class="lineno"><a class="lineno" href="#L563">563</a></span>
<span id="L564" class="lineno"><a class="lineno" href="#L564">564</a></span>
<span id="L565" class="lineno"><a class="lineno" href="#L565">565</a></span>
<span id="L566" class="lineno"><a class="lineno" href="#L566">566</a></span>
<span id="L567" class="lineno"><a class="lineno" href="#L567">567</a></span>
<span id="L568" class="lineno"><a class="lineno" href="#L568">568</a></span>
<span id="L569" class="lineno"><a class="lineno" href="#L569">569</a></span>
<span id="L570" class="lineno"><a class="lineno" href="#L570">570</a></span>
<span id="L571" class="lineno"><a class="lineno" href="#L571">571</a></span>
<span id="L572" class="lineno"><a class="lineno" href="#L572">572</a></span>
<span id="L573" class="lineno"><a class="lineno" href="#L573">573</a></span>
<span id="L574" class="lineno"><a class="lineno" href="#L574">574</a></span>
<span id="L575" class="lineno"><a class="lineno" href="#L575">575</a></span>
<span id="L576" class="lineno"><a class="lineno" href="#L576">576</a></span>
<span id="L577" class="lineno"><a class="lineno" href="#L577">577</a></span>
<span id="L578" class="lineno"><a class="lineno" href="#L578">578</a></span>
<span id="L579" class="lineno"><a class="lineno" href="#L579">579</a></span>
<span id="L580" class="lineno"><a class="lineno" href="#L580">580</a></span>
<span id="L581" class="lineno"><a class="lineno" href="#L581">581</a></span>
<span id="L582" class="lineno"><a class="lineno" href="#L582">582</a></span>
<span id="L583" class="lineno"><a class="lineno" href="#L583">583</a></span>
<span id="L584" class="lineno"><a class="lineno" href="#L584">584</a></span>
<span id="L585" class="lineno"><a class="lineno" href="#L585">585</a></span>
<span id="L586" class="lineno"><a class="lineno" href="#L586">586</a></span>
<span id="L587" class="lineno"><a class="lineno" href="#L587">587</a></span>
<span id="L588" class="lineno"><a class="lineno" href="#L588">588</a></span>
<span id="L589" class="lineno"><a class="lineno" href="#L589">589</a></span>
<span id="L590" class="lineno"><a class="lineno" href="#L590">590</a></span>
<span id="L591" class="lineno"><a class="lineno" href="#L591">591</a></span>
<span id="L592" class="lineno"><a class="lineno" href="#L592">592</a></span>
<span id="L593" class="lineno"><a class="lineno" href="#L593">593</a></span>
<span id="L594" class="lineno"><a class="lineno" href="#L594">594</a></span>
<span id="L595" class="lineno"><a class="lineno" href="#L595">595</a></span>
<span id="L596" class="lineno"><a class="lineno" href="#L596">596</a></span>
<span id="L597" class="lineno"><a class="lineno" href="#L597">597</a></span>
<span id="L598" class="lineno"><a class="lineno" href="#L598">598</a></span>
<span id="L599" class="lineno"><a class="lineno" href="#L599">599</a></span>
<span id="L600" class="lineno"><a class="lineno" href="#L600">600</a></span>
<span id="L601" class="lineno"><a class="lineno" href="#L601">601</a></span>
<span id="L602" class="lineno"><a class="lineno" href="#L602">602</a></span>
<span id="L603" class="lineno"><a class="lineno" href="#L603">603</a></span>
<span id="L604" class="lineno"><a class="lineno" href="#L604">604</a></span>
<span id="L605" class="lineno"><a class="lineno" href="#L605">605</a></span>
<span id="L606" class="lineno"><a class="lineno" href="#L606">606</a></span>
<span id="L607" class="lineno"><a class="lineno" href="#L607">607</a></span>
<span id="L608" class="lineno"><a class="lineno" href="#L608">608</a></span>
<span id="L609" class="lineno"><a class="lineno" href="#L609">609</a></span>
<span id="L610" class="lineno"><a class="lineno" href="#L610">610</a></span>
<span id="L611" class="lineno"><a class="lineno" href="#L611">611</a></span>
<span id="L612" class="lineno"><a class="lineno" href="#L612">612</a></span>
<span id="L613" class="lineno"><a class="lineno" href="#L613">613</a></span>
<span id="L614" class="lineno"><a class="lineno" href="#L614">614</a></span>
<span id="L615" class="lineno"><a class="lineno" href="#L615">615</a></span>
<span id="L616" class="lineno"><a class="lineno" href="#L616">616</a></span>
<span id="L617" class="lineno"><a class="lineno" href="#L617">617</a></span>
<span id="L618" class="lineno"><a class="lineno" href="#L618">618</a></span>
<span id="L619" class="lineno"><a class="lineno" href="#L619">619</a></span>
<span id="L620" class="lineno"><a class="lineno" href="#L620">620</a></span>
<span id="L621" class="lineno"><a class="lineno" href="#L621">621</a></span>
<span id="L622" class="lineno"><a class="lineno" href="#L622">622</a></span>
<span id="L623" class="lineno"><a class="lineno" href="#L623">623</a></span>
<span id="L624" class="lineno"><a class="lineno" href="#L624">624</a></span>
<span id="L625" class="lineno"><a class="lineno" href="#L625">625</a></span>
<span id="L626" class="lineno"><a class="lineno" href="#L626">626</a></span>
<span id="L627" class="lineno"><a class="lineno" href="#L627">627</a></span>
<span id="L628" class="lineno"><a class="lineno" href="#L628">628</a></span>
<span id="L629" class="lineno"><a class="lineno" href="#L629">629</a></span>
<span id="L630" class="lineno"><a class="lineno" href="#L630">630</a></span>
<span id="L631" class="lineno"><a class="lineno" href="#L631">631</a></span>
<span id="L632" class="lineno"><a class="lineno" href="#L632">632</a></span>
<span id="L633" class="lineno"><a class="lineno" href="#L633">633</a></span>
<span id="L634" class="lineno"><a class="lineno" href="#L634">634</a></span>
<span id="L635" class="lineno"><a class="lineno" href="#L635">635</a></span>
<span id="L636" class="lineno"><a class="lineno" href="#L636">636</a></span>
<span id="L637" class="lineno"><a class="lineno" href="#L637">637</a></span>
<span id="L638" class="lineno"><a class="lineno" href="#L638">638</a></span>
<span id="L639" class="lineno"><a class="lineno" href="#L639">639</a></span>
<span id="L640" class="lineno"><a class="lineno" href="#L640">640</a></span>
<span id="L641" class="lineno"><a class="lineno" href="#L641">641</a></span>
<span id="L642" class="lineno"><a class="lineno" href="#L642">642</a></span>
<span id="L643" class="lineno"><a class="lineno" href="#L643">643</a></span>
<span id="L644" class="lineno"><a class="lineno" href="#L644">644</a></span>
<span id="L645" class="lineno"><a class="lineno" href="#L645">645</a></span>
<span id="L646" class="lineno"><a class="lineno" href="#L646">646</a></span>
<span id="L647" class="lineno"><a class="lineno" href="#L647">647</a></span>
<span id="L648" class="lineno"><a class="lineno" href="#L648">648</a></span>
<span id="L649" class="lineno"><a class="lineno" href="#L649">649</a></span>
<span id="L650" class="lineno"><a class="lineno" href="#L650">650</a></span>
<span id="L651" class="lineno"><a class="lineno" href="#L651">651</a></span>
<span id="L652" class="lineno"><a class="lineno" href="#L652">652</a></span>
<span id="L653" class="lineno"><a class="lineno" href="#L653">653</a></span>
<span id="L654" class="lineno"><a class="lineno" href="#L654">654</a></span>
<span id="L655" class="lineno"><a class="lineno" href="#L655">655</a></span>
<span id="L656" class="lineno"><a class="lineno" href="#L656">656</a></span>
<span id="L657" class="lineno"><a class="lineno" href="#L657">657</a></span>
<span id="L658" class="lineno"><a class="lineno" href="#L658">658</a></span>
<span id="L659" class="lineno"><a class="lineno" href="#L659">659</a></span>
<span id="L660" class="lineno"><a class="lineno" href="#L660">660</a></span>
<span id="L661" class="lineno"><a class="lineno" href="#L661">661</a></span>
<span id="L662" class="lineno"><a class="lineno" href="#L662">662</a></span>
<span id="L663" class="lineno"><a class="lineno" href="#L663">663</a></span>
<span id="L664" class="lineno"><a class="lineno" href="#L664">664</a></span>
<span id="L665" class="lineno"><a class="lineno" href="#L665">665</a></span>
<span id="L666" class="lineno"><a class="lineno" href="#L666">666</a></span>
<span id="L667" class="lineno"><a class="lineno" href="#L667">667</a></span>
<span id="L668" class="lineno"><a class="lineno" href="#L668">668</a></span>
<span id="L669" class="lineno"><a class="lineno" href="#L669">669</a></span>
<span id="L670" class="lineno"><a class="lineno" href="#L670">670</a></span>
<span id="L671" class="lineno"><a class="lineno" href="#L671">671</a></span>
<span id="L672" class="lineno"><a class="lineno" href="#L672">672</a></span>
<span id="L673" class="lineno"><a class="lineno" href="#L673">673</a></span>
<span id="L674" class="lineno"><a class="lineno" href="#L674">674</a></span>
<span id="L675" class="lineno"><a class="lineno" href="#L675">675</a></span>
<span id="L676" class="lineno"><a class="lineno" href="#L676">676</a></span>
<span id="L677" class="lineno"><a class="lineno" href="#L677">677</a></span>
<span id="L678" class="lineno"><a class="lineno" href="#L678">678</a></span>
<span id="L679" class="lineno"><a class="lineno" href="#L679">679</a></span>
<span id="L680" class="lineno"><a class="lineno" href="#L680">680</a></span>
<span id="L681" class="lineno"><a class="lineno" href="#L681">681</a></span>
<span id="L682" class="lineno"><a class="lineno" href="#L682">682</a></span>
<span id="L683" class="lineno"><a class="lineno" href="#L683">683</a></span>
<span id="L684" class="lineno"><a class="lineno" href="#L684">684</a></span>
<span id="L685" class="lineno"><a class="lineno" href="#L685">685</a></span>
<span id="L686" class="lineno"><a class="lineno" href="#L686">686</a></span>
<span id="L687" class="lineno"><a class="lineno" href="#L687">687</a></span>
<span id="L688" class="lineno"><a class="lineno" href="#L688">688</a></span>
<span id="L689" class="lineno"><a class="lineno" href="#L689">689</a></span>
<span id="L690" class="lineno"><a class="lineno" href="#L690">690</a></span>
<span id="L691" class="lineno"><a class="lineno" href="#L691">691</a></span>
<span id="L692" class="lineno"><a class="lineno" href="#L692">692</a></span>
<span id="L693" class="lineno"><a class="lineno" href="#L693">693</a></span>
<span id="L694" class="lineno"><a class="lineno" href="#L694">694</a></span>
<span id="L695" class="lineno"><a class="lineno" href="#L695">695</a></span>
<span id="L696" class="lineno"><a class="lineno" href="#L696">696</a></span>
<span id="L697" class="lineno"><a class="lineno" href="#L697">697</a></span>
<span id="L698" class="lineno"><a class="lineno" href="#L698">698</a></span>
<span id="L699" class="lineno"><a class="lineno" href="#L699">699</a></span>
<span id="L700" class="lineno"><a class="lineno" href="#L700">700</a></span>
<span id="L701" class="lineno"><a class="lineno" href="#L701">701</a></span>
<span id="L702" class="lineno"><a class="lineno" href="#L702">702</a></span>
<span id="L703" class="lineno"><a class="lineno" href="#L703">703</a></span>
<span id="L704" class="lineno"><a class="lineno" href="#L704">704</a></span>
<span id="L705" class="lineno"><a class="lineno" href="#L705">705</a></span>
<span id="L706" class="lineno"><a class="lineno" href="#L706">706</a></span>
<span id="L707" class="lineno"><a class="lineno" href="#L707">707</a></span>
<span id="L708" class="lineno"><a class="lineno" href="#L708">708</a></span>
<span id="L709" class="lineno"><a class="lineno" href="#L709">709</a></span>
<span id="L710" class="lineno"><a class="lineno" href="#L710">710</a></span>
<span id="L711" class="lineno"><a class="lineno" href="#L711">711</a></span>
<span id="L712" class="lineno"><a class="lineno" href="#L712">712</a></span>
<span id="L713" class="lineno"><a class="lineno" href="#L713">713</a></span>
<span id="L714" class="lineno"><a class="lineno" href="#L714">714</a></span>
<span id="L715" class="lineno"><a class="lineno" href="#L715">715</a></span>
<span id="L716" class="lineno"><a class="lineno" href="#L716">716</a></span>
<span id="L717" class="lineno"><a class="lineno" href="#L717">717</a></span>
<span id="L718" class="lineno"><a class="lineno" href="#L718">718</a></span>
<span id="L719" class="lineno"><a class="lineno" href="#L719">719</a></span>
<span id="L720" class="lineno"><a class="lineno" href="#L720">720</a></span>
<span id="L721" class="lineno"><a class="lineno" href="#L721">721</a></span>
<span id="L722" class="lineno"><a class="lineno" href="#L722">722</a></span>
<span id="L723" class="lineno"><a class="lineno" href="#L723">723</a></span>
<span id="L724" class="lineno"><a class="lineno" href="#L724">724</a></span>
<span id="L725" class="lineno"><a class="lineno" href="#L725">725</a></span>
<span id="L726" class="lineno"><a class="lineno" href="#L726">726</a></span>
<span id="L727" class="lineno"><a class="lineno" href="#L727">727</a></span>
<span id="L728" class="lineno"><a class="lineno" href="#L728">728</a></span>
<span id="L729" class="lineno"><a class="lineno" href="#L729">729</a></span>
<span id="L730" class="lineno"><a class="lineno" href="#L730">730</a></span>
<span id="L731" class="lineno"><a class="lineno" href="#L731">731</a></span>
<span id="L732" class="lineno"><a class="lineno" href="#L732">732</a></span>
<span id="L733" class="lineno"><a class="lineno" href="#L733">733</a></span>
<span id="L734" class="lineno"><a class="lineno" href="#L734">734</a></span>
<span id="L735" class="lineno"><a class="lineno" href="#L735">735</a></span>
<span id="L736" class="lineno"><a class="lineno" href="#L736">736</a></span>
<span id="L737" class="lineno"><a class="lineno" href="#L737">737</a></span>
<span id="L738" class="lineno"><a class="lineno" href="#L738">738</a></span>
<span id="L739" class="lineno"><a class="lineno" href="#L739">739</a></span>
<span id="L740" class="lineno"><a class="lineno" href="#L740">740</a></span>
<span id="L741" class="lineno"><a class="lineno" href="#L741">741</a></span>
<span id="L742" class="lineno"><a class="lineno" href="#L742">742</a></span>
<span id="L743" class="lineno"><a class="lineno" href="#L743">743</a></span>
<span id="L744" class="lineno"><a class="lineno" href="#L744">744</a></span>
<span id="L745" class="lineno"><a class="lineno" href="#L745">745</a></span>
<span id="L746" class="lineno"><a class="lineno" href="#L746">746</a></span>
<span id="L747" class="lineno"><a class="lineno" href="#L747">747</a></span>
<span id="L748" class="lineno"><a class="lineno" href="#L748">748</a></span>
<span id="L749" class="lineno"><a class="lineno" href="#L749">749</a></span>
<span id="L750" class="lineno"><a class="lineno" href="#L750">750</a></span>
<span id="L751" class="lineno"><a class="lineno" href="#L751">751</a></span>
<span id="L752" class="lineno"><a class="lineno" href="#L752">752</a></span>
<span id="L753" class="lineno"><a class="lineno" href="#L753">753</a></span>
<span id="L754" class="lineno"><a class="lineno" href="#L754">754</a></span>
<span id="L755" class="lineno"><a class="lineno" href="#L755">755</a></span>
<span id="L756" class="lineno"><a class="lineno" href="#L756">756</a></span>
<span id="L757" class="lineno"><a class="lineno" href="#L757">757</a></span>
<span id="L758" class="lineno"><a class="lineno" href="#L758">758</a></span>
<span id="L759" class="lineno"><a class="lineno" href="#L759">759</a></span>
<span id="L760" class="lineno"><a class="lineno" href="#L760">760</a></span>
<span id="L761" class="lineno"><a class="lineno" href="#L761">761</a></span>
<span id="L762" class="lineno"><a class="lineno" href="#L762">762</a></span>
<span id="L763" class="lineno"><a class="lineno" href="#L763">763</a></span>
<span id="L764" class="lineno"><a class="lineno" href="#L764">764</a></span>
<span id="L765" class="lineno"><a class="lineno" href="#L765">765</a></span>
<span id="L766" class="lineno"><a class="lineno" href="#L766">766</a></span>
<span id="L767" class="lineno"><a class="lineno" href="#L767">767</a></span>
<span id="L768" class="lineno"><a class="lineno" href="#L768">768</a></span>
<span id="L769" class="lineno"><a class="lineno" href="#L769">769</a></span>
<span id="L770" class="lineno"><a class="lineno" href="#L770">770</a></span>
<span id="L771" class="lineno"><a class="lineno" href="#L771">771</a></span>
<span id="L772" class="lineno"><a class="lineno" href="#L772">772</a></span>
<span id="L773" class="lineno"><a class="lineno" href="#L773">773</a></span>
<span id="L774" class="lineno"><a class="lineno" href="#L774">774</a></span>
<span id="L775" class="lineno"><a class="lineno" href="#L775">775</a></span>
<span id="L776" class="lineno"><a class="lineno" href="#L776">776</a></span>
<span id="L777" class="lineno"><a class="lineno" href="#L777">777</a></span>
<span id="L778" class="lineno"><a class="lineno" href="#L778">778</a></span>
<span id="L779" class="lineno"><a class="lineno" href="#L779">779</a></span>
<span id="L780" class="lineno"><a class="lineno" href="#L780">780</a></span>
<span id="L781" class="lineno"><a class="lineno" href="#L781">781</a></span>
<span id="L782" class="lineno"><a class="lineno" href="#L782">782</a></span>
<span id="L783" class="lineno"><a class="lineno" href="#L783">783</a></span>
<span id="L784" class="lineno"><a class="lineno" href="#L784">784</a></span>
<span id="L785" class="lineno"><a class="lineno" href="#L785">785</a></span>
<span id="L786" class="lineno"><a class="lineno" href="#L786">786</a></span>
<span id="L787" class="lineno"><a class="lineno" href="#L787">787</a></span>
<span id="L788" class="lineno"><a class="lineno" href="#L788">788</a></span>
<span id="L789" class="lineno"><a class="lineno" href="#L789">789</a></span>
<span id="L790" class="lineno"><a class="lineno" href="#L790">790</a></span>
<span id="L791" class="lineno"><a class="lineno" href="#L791">791</a></span>
<span id="L792" class="lineno"><a class="lineno" href="#L792">792</a></span>
<span id="L793" class="lineno"><a class="lineno" href="#L793">793</a></span>
<span id="L794" class="lineno"><a class="lineno" href="#L794">794</a></span>
<span id="L795" class="lineno"><a class="lineno" href="#L795">795</a></span>
<span id="L796" class="lineno"><a class="lineno" href="#L796">796</a></span>
<span id="L797" class="lineno"><a class="lineno" href="#L797">797</a></span>
<span id="L798" class="lineno"><a class="lineno" href="#L798">798</a></span>
<span id="L799" class="lineno"><a class="lineno" href="#L799">799</a></span>
<span id="L800" class="lineno"><a class="lineno" href="#L800">800</a></span>
<span id="L801" class="lineno"><a class="lineno" href="#L801">801</a></span>
<span id="L802" class="lineno"><a class="lineno" href="#L802">802</a></span>
<span id="L803" class="lineno"><a class="lineno" href="#L803">803</a></span>
<span id="L804" class="lineno"><a class="lineno" href="#L804">804</a></span>
<span id="L805" class="lineno"><a class="lineno" href="#L805">805</a></span>
<span id="L806" class="lineno"><a class="lineno" href="#L806">806</a></span>
<span id="L807" class="lineno"><a class="lineno" href="#L807">807</a></span>
<span id="L808" class="lineno"><a class="lineno" href="#L808">808</a></span>
<span id="L809" class="lineno"><a class="lineno" href="#L809">809</a></span>
<span id="L810" class="lineno"><a class="lineno" href="#L810">810</a></span>
<span id="L811" class="lineno"><a class="lineno" href="#L811">811</a></span>
<span id="L812" class="lineno"><a class="lineno" href="#L812">812</a></span>
<span id="L813" class="lineno"><a class="lineno" href="#L813">813</a></span>
<span id="L814" class="lineno"><a class="lineno" href="#L814">814</a></span>
<span id="L815" class="lineno"><a class="lineno" href="#L815">815</a></span>
<span id="L816" class="lineno"><a class="lineno" href="#L816">816</a></span>
<span id="L817" class="lineno"><a class="lineno" href="#L817">817</a></span>
<span id="L818" class="lineno"><a class="lineno" href="#L818">818</a></span>
<span id="L819" class="lineno"><a class="lineno" href="#L819">819</a></span>
<span id="L820" class="lineno"><a class="lineno" href="#L820">820</a></span>
<span id="L821" class="lineno"><a class="lineno" href="#L821">821</a></span>
<span id="L822" class="lineno"><a class="lineno" href="#L822">822</a></span>
<span id="L823" class="lineno"><a class="lineno" href="#L823">823</a></span>
<span id="L824" class="lineno"><a class="lineno" href="#L824">824</a></span>
<span id="L825" class="lineno"><a class="lineno" href="#L825">825</a></span>
<span id="L826" class="lineno"><a class="lineno" href="#L826">826</a></span>
<span id="L827" class="lineno"><a class="lineno" href="#L827">827</a></span>
<span id="L828" class="lineno"><a class="lineno" href="#L828">828</a></span>
<span id="L829" class="lineno"><a class="lineno" href="#L829">829</a></span>
<span id="L830" class="lineno"><a class="lineno" href="#L830">830</a></span>
<span id="L831" class="lineno"><a class="lineno" href="#L831">831</a></span>
<span id="L832" class="lineno"><a class="lineno" href="#L832">832</a></span>
<span id="L833" class="lineno"><a class="lineno" href="#L833">833</a></span>
<span id="L834" class="lineno"><a class="lineno" href="#L834">834</a></span>
<span id="L835" class="lineno"><a class="lineno" href="#L835">835</a></span>
<span id="L836" class="lineno"><a class="lineno" href="#L836">836</a></span>
<span id="L837" class="lineno"><a class="lineno" href="#L837">837</a></span>
<span id="L838" class="lineno"><a class="lineno" href="#L838">838</a></span>
<span id="L839" class="lineno"><a class="lineno" href="#L839">839</a></span>
<span id="L840" class="lineno"><a class="lineno" href="#L840">840</a></span>
<span id="L841" class="lineno"><a class="lineno" href="#L841">841</a></span>
<span id="L842" class="lineno"><a class="lineno" href="#L842">842</a></span>
<span id="L843" class="lineno"><a class="lineno" href="#L843">843</a></span>
<span id="L844" class="lineno"><a class="lineno" href="#L844">844</a></span>
<span id="L845" class="lineno"><a class="lineno" href="#L845">845</a></span>
<span id="L846" class="lineno"><a class="lineno" href="#L846">846</a></span>
<span id="L847" class="lineno"><a class="lineno" href="#L847">847</a></span>
<span id="L848" class="lineno"><a class="lineno" href="#L848">848</a></span>
<span id="L849" class="lineno"><a class="lineno" href="#L849">849</a></span>
<span id="L850" class="lineno"><a class="lineno" href="#L850">850</a></span>
<span id="L851" class="lineno"><a class="lineno" href="#L851">851</a></span>
<span id="L852" class="lineno"><a class="lineno" href="#L852">852</a></span>
<span id="L853" class="lineno"><a class="lineno" href="#L853">853</a></span>
<span id="L854" class="lineno"><a class="lineno" href="#L854">854</a></span>
<span id="L855" class="lineno"><a class="lineno" href="#L855">855</a></span>
<span id="L856" class="lineno"><a class="lineno" href="#L856">856</a></span>
<span id="L857" class="lineno"><a class="lineno" href="#L857">857</a></span>
<span id="L858" class="lineno"><a class="lineno" href="#L858">858</a></span>
<span id="L859" class="lineno"><a class="lineno" href="#L859">859</a></span>
<span id="L860" class="lineno"><a class="lineno" href="#L860">860</a></span>
<span id="L861" class="lineno"><a class="lineno" href="#L861">861</a></span>
<span id="L862" class="lineno"><a class="lineno" href="#L862">862</a></span>
<span id="L863" class="lineno"><a class="lineno" href="#L863">863</a></span>
<span id="L864" class="lineno"><a class="lineno" href="#L864">864</a></span>
<span id="L865" class="lineno"><a class="lineno" href="#L865">865</a></span>
<span id="L866" class="lineno"><a class="lineno" href="#L866">866</a></span>
<span id="L867" class="lineno"><a class="lineno" href="#L867">867</a></span>
<span id="L868" class="lineno"><a class="lineno" href="#L868">868</a></span>
<span id="L869" class="lineno"><a class="lineno" href="#L869">869</a></span>
<span id="L870" class="lineno"><a class="lineno" href="#L870">870</a></span>
<span id="L871" class="lineno"><a class="lineno" href="#L871">871</a></span>
<span id="L872" class="lineno"><a class="lineno" href="#L872">872</a></span>
<span id="L873" class="lineno"><a class="lineno" href="#L873">873</a></span>
<span id="L874" class="lineno"><a class="lineno" href="#L874">874</a></span>
<span id="L875" class="lineno"><a class="lineno" href="#L875">875</a></span>
<span id="L876" class="lineno"><a class="lineno" href="#L876">876</a></span>
<span id="L877" class="lineno"><a class="lineno" href="#L877">877</a></span>
<span id="L878" class="lineno"><a class="lineno" href="#L878">878</a></span>
<span id="L879" class="lineno"><a class="lineno" href="#L879">879</a></span>
<span id="L880" class="lineno"><a class="lineno" href="#L880">880</a></span>
<span id="L881" class="lineno"><a class="lineno" href="#L881">881</a></span>
<span id="L882" class="lineno"><a class="lineno" href="#L882">882</a></span>
<span id="L883" class="lineno"><a class="lineno" href="#L883">883</a></span>
<span id="L884" class="lineno"><a class="lineno" href="#L884">884</a></span>
<span id="L885" class="lineno"><a class="lineno" href="#L885">885</a></span>
<span id="L886" class="lineno"><a class="lineno" href="#L886">886</a></span>
<span id="L887" class="lineno"><a class="lineno" href="#L887">887</a></span>
<span id="L888" class="lineno"><a class="lineno" href="#L888">888</a></span>
<span id="L889" class="lineno"><a class="lineno" href="#L889">889</a></span>
<span id="L890" class="lineno"><a class="lineno" href="#L890">890</a></span>
<span id="L891" class="lineno"><a class="lineno" href="#L891">891</a></span>
<span id="L892" class="lineno"><a class="lineno" href="#L892">892</a></span>
<span id="L893" class="lineno"><a class="lineno" href="#L893">893</a></span>
<span id="L894" class="lineno"><a class="lineno" href="#L894">894</a></span>
<span id="L895" class="lineno"><a class="lineno" href="#L895">895</a></span>
<span id="L896" class="lineno"><a class="lineno" href="#L896">896</a></span>
<span id="L897" class="lineno"><a class="lineno" href="#L897">897</a></span>
<span id="L898" class="lineno"><a class="lineno" href="#L898">898</a></span>
<span id="L899" class="lineno"><a class="lineno" href="#L899">899</a></span>
<span id="L900" class="lineno"><a class="lineno" href="#L900">900</a></span>
<span id="L901" class="lineno"><a class="lineno" href="#L901">901</a></span>
<span id="L902" class="lineno"><a class="lineno" href="#L902">902</a></span>
<span id="L903" class="lineno"><a class="lineno" href="#L903">903</a></span>
<span id="L904" class="lineno"><a class="lineno" href="#L904">904</a></span>
<span id="L905" class="lineno"><a class="lineno" href="#L905">905</a></span>
<span id="L906" class="lineno"><a class="lineno" href="#L906">906</a></span>
<span id="L907" class="lineno"><a class="lineno" href="#L907">907</a></span>
<span id="L908" class="lineno"><a class="lineno" href="#L908">908</a></span>
<span id="L909" class="lineno"><a class="lineno" href="#L909">909</a></span>
<span id="L910" class="lineno"><a class="lineno" href="#L910">910</a></span>
<span id="L911" class="lineno"><a class="lineno" href="#L911">911</a></span>
<span id="L912" class="lineno"><a class="lineno" href="#L912">912</a></span>
<span id="L913" class="lineno"><a class="lineno" href="#L913">913</a></span>
<span id="L914" class="lineno"><a class="lineno" href="#L914">914</a></span>
<span id="L915" class="lineno"><a class="lineno" href="#L915">915</a></span>
<span id="L916" class="lineno"><a class="lineno" href="#L916">916</a></span>
<span id="L917" class="lineno"><a class="lineno" href="#L917">917</a></span>
<span id="L918" class="lineno"><a class="lineno" href="#L918">918</a></span>
<span id="L919" class="lineno"><a class="lineno" href="#L919">919</a></span>
<span id="L920" class="lineno"><a class="lineno" href="#L920">920</a></span>
<span id="L921" class="lineno"><a class="lineno" href="#L921">921</a></span>
<span id="L922" class="lineno"><a class="lineno" href="#L922">922</a></span>
<span id="L923" class="lineno"><a class="lineno" href="#L923">923</a></span>
<span id="L924" class="lineno"><a class="lineno" href="#L924">924</a></span>
<span id="L925" class="lineno"><a class="lineno" href="#L925">925</a></span>
<span id="L926" class="lineno"><a class="lineno" href="#L926">926</a></span>
<span id="L927" class="lineno"><a class="lineno" href="#L927">927</a></span>
<span id="L928" class="lineno"><a class="lineno" href="#L928">928</a></span>
<span id="L929" class="lineno"><a class="lineno" href="#L929">929</a></span>
<span id="L930" class="lineno"><a class="lineno" href="#L930">930</a></span>
<span id="L931" class="lineno"><a class="lineno" href="#L931">931</a></span>
<span id="L932" class="lineno"><a class="lineno" href="#L932">932</a></span>
<span id="L933" class="lineno"><a class="lineno" href="#L933">933</a></span>
<span id="L934" class="lineno"><a class="lineno" href="#L934">934</a></span>
<span id="L935" class="lineno"><a class="lineno" href="#L935">935</a></span>
<span id="L936" class="lineno"><a class="lineno" href="#L936">936</a></span>
<span id="L937" class="lineno"><a class="lineno" href="#L937">937</a></span>
<span id="L938" class="lineno"><a class="lineno" href="#L938">938</a></span>
<span id="L939" class="lineno"><a class="lineno" href="#L939">939</a></span>
<span id="L940" class="lineno"><a class="lineno" href="#L940">940</a></span>
<span id="L941" class="lineno"><a class="lineno" href="#L941">941</a></span>
<span id="L942" class="lineno"><a class="lineno" href="#L942">942</a></span>
<span id="L943" class="lineno"><a class="lineno" href="#L943">943</a></span>
<span id="L944" class="lineno"><a class="lineno" href="#L944">944</a></span>
<span id="L945" class="lineno"><a class="lineno" href="#L945">945</a></span>
<span id="L946" class="lineno"><a class="lineno" href="#L946">946</a></span>
<span id="L947" class="lineno"><a class="lineno" href="#L947">947</a></span>
<span id="L948" class="lineno"><a class="lineno" href="#L948">948</a></span>
<span id="L949" class="lineno"><a class="lineno" href="#L949">949</a></span>
<span id="L950" class="lineno"><a class="lineno" href="#L950">950</a></span>
<span id="L951" class="lineno"><a class="lineno" href="#L951">951</a></span>
<span id="L952" class="lineno"><a class="lineno" href="#L952">952</a></span>
<span id="L953" class="lineno"><a class="lineno" href="#L953">953</a></span>
<span id="L954" class="lineno"><a class="lineno" href="#L954">954</a></span>
<span id="L955" class="lineno"><a class="lineno" href="#L955">955</a></span>
<span id="L956" class="lineno"><a class="lineno" href="#L956">956</a></span>
<span id="L957" class="lineno"><a class="lineno" href="#L957">957</a></span>
<span id="L958" class="lineno"><a class="lineno" href="#L958">958</a></span>
<span id="L959" class="lineno"><a class="lineno" href="#L959">959</a></span>
<span id="L960" class="lineno"><a class="lineno" href="#L960">960</a></span>
<span id="L961" class="lineno"><a class="lineno" href="#L961">961</a></span>
<span id="L962" class="lineno"><a class="lineno" href="#L962">962</a></span>
<span id="L963" class="lineno"><a class="lineno" href="#L963">963</a></span>
<span id="L964" class="lineno"><a class="lineno" href="#L964">964</a></span>
<span id="L965" class="lineno"><a class="lineno" href="#L965">965</a></span>
<span id="L966" class="lineno"><a class="lineno" href="#L966">966</a></span>
<span id="L967" class="lineno"><a class="lineno" href="#L967">967</a></span>
<span id="L968" class="lineno"><a class="lineno" href="#L968">968</a></span>
<span id="L969" class="lineno"><a class="lineno" href="#L969">969</a></span>
<span id="L970" class="lineno"><a class="lineno" href="#L970">970</a></span>
<span id="L971" class="lineno"><a class="lineno" href="#L971">971</a></span>
<span id="L972" class="lineno"><a class="lineno" href="#L972">972</a></span>
<span id="L973" class="lineno"><a class="lineno" href="#L973">973</a></span>
<span id="L974" class="lineno"><a class="lineno" href="#L974">974</a></span>
<span id="L975" class="lineno"><a class="lineno" href="#L975">975</a></span>
<span id="L976" class="lineno"><a class="lineno" href="#L976">976</a></span>
<span id="L977" class="lineno"><a class="lineno" href="#L977">977</a></span>
<span id="L978" class="lineno"><a class="lineno" href="#L978">978</a></span>
<span id="L979" class="lineno"><a class="lineno" href="#L979">979</a></span>
<span id="L980" class="lineno"><a class="lineno" href="#L980">980</a></span>
<span id="L981" class="lineno"><a class="lineno" href="#L981">981</a></span>
<span id="L982" class="lineno"><a class="lineno" href="#L982">982</a></span>
<span id="L983" class="lineno"><a class="lineno" href="#L983">983</a></span>
<span id="L984" class="lineno"><a class="lineno" href="#L984">984</a></span>
<span id="L985" class="lineno"><a class="lineno" href="#L985">985</a></span>
<span id="L986" class="lineno"><a class="lineno" href="#L986">986</a></span>
<span id="L987" class="lineno"><a class="lineno" href="#L987">987</a></span>
<span id="L988" class="lineno"><a class="lineno" href="#L988">988</a></span>
<span id="L989" class="lineno"><a class="lineno" href="#L989">989</a></span>
<span id="L990" class="lineno"><a class="lineno" href="#L990">990</a></span>
<span id="L991" class="lineno"><a class="lineno" href="#L991">991</a></span>
<span id="L992" class="lineno"><a class="lineno" href="#L992">992</a></span>
<span id="L993" class="lineno"><a class="lineno" href="#L993">993</a></span>
<span id="L994" class="lineno"><a class="lineno" href="#L994">994</a></span>
<span id="L995" class="lineno"><a class="lineno" href="#L995">995</a></span>
<span id="L996" class="lineno"><a class="lineno" href="#L996">996</a></span>
<span id="L997" class="lineno"><a class="lineno" href="#L997">997</a></span>
<span id="L998" class="lineno"><a class="lineno" href="#L998">998</a></span>
<span id="L999" class="lineno"><a class="lineno" href="#L999">999</a></span>
<span id="L1000" class="lineno"><a class="lineno" href="#L1000">1000</a></span>
<span id="L1001" class="lineno"><a class="lineno" href="#L1001">1001</a></span>
<span id="L1002" class="lineno"><a class="lineno" href="#L1002">1002</a></span>
<span id="L1003" class="lineno"><a class="lineno" href="#L1003">1003</a></span>
<span id="L1004" class="lineno"><a class="lineno" href="#L1004">1004</a></span>
<span id="L1005" class="lineno"><a class="lineno" href="#L1005">1005</a></span>
<span id="L1006" class="lineno"><a class="lineno" href="#L1006">1006</a></span>
<span id="L1007" class="lineno"><a class="lineno" href="#L1007">1007</a></span>
<span id="L1008" class="lineno"><a class="lineno" href="#L1008">1008</a></span>
<span id="L1009" class="lineno"><a class="lineno" href="#L1009">1009</a></span>
<span id="L1010" class="lineno"><a class="lineno" href="#L1010">1010</a></span>
<span id="L1011" class="lineno"><a class="lineno" href="#L1011">1011</a></span>
<span id="L1012" class="lineno"><a class="lineno" href="#L1012">1012</a></span>
<span id="L1013" class="lineno"><a class="lineno" href="#L1013">1013</a></span>
<span id="L1014" class="lineno"><a class="lineno" href="#L1014">1014</a></span>
<span id="L1015" class="lineno"><a class="lineno" href="#L1015">1015</a></span>
<span id="L1016" class="lineno"><a class="lineno" href="#L1016">1016</a></span>
<span id="L1017" class="lineno"><a class="lineno" href="#L1017">1017</a></span>
<span id="L1018" class="lineno"><a class="lineno" href="#L1018">1018</a></span>
<span id="L1019" class="lineno"><a class="lineno" href="#L1019">1019</a></span>
<span id="L1020" class="lineno"><a class="lineno" href="#L1020">1020</a></span>
<span id="L1021" class="lineno"><a class="lineno" href="#L1021">1021</a></span>
<span id="L1022" class="lineno"><a class="lineno" href="#L1022">1022</a></span>
<span id="L1023" class="lineno"><a class="lineno" href="#L1023">1023</a></span>
<span id="L1024" class="lineno"><a class="lineno" href="#L1024">1024</a></span>
<span id="L1025" class="lineno"><a class="lineno" href="#L1025">1025</a></span>
<span id="L1026" class="lineno"><a class="lineno" href="#L1026">1026</a></span>
<span id="L1027" class="lineno"><a class="lineno" href="#L1027">1027</a></span>
<span id="L1028" class="lineno"><a class="lineno" href="#L1028">1028</a></span>
<span id="L1029" class="lineno"><a class="lineno" href="#L1029">1029</a></span>
<span id="L1030" class="lineno"><a class="lineno" href="#L1030">1030</a></span>
<span id="L1031" class="lineno"><a class="lineno" href="#L1031">1031</a></span>
<span id="L1032" class="lineno"><a class="lineno" href="#L1032">1032</a></span>
<span id="L1033" class="lineno"><a class="lineno" href="#L1033">1033</a></span>
<span id="L1034" class="lineno"><a class="lineno" href="#L1034">1034</a></span>
<span id="L1035" class="lineno"><a class="lineno" href="#L1035">1035</a></span>
<span id="L1036" class="lineno"><a class="lineno" href="#L1036">1036</a></span>
<span id="L1037" class="lineno"><a class="lineno" href="#L1037">1037</a></span>
<span id="L1038" class="lineno"><a class="lineno" href="#L1038">1038</a></span>
<span id="L1039" class="lineno"><a class="lineno" href="#L1039">1039</a></span>
<span id="L1040" class="lineno"><a class="lineno" href="#L1040">1040</a></span>
<span id="L1041" class="lineno"><a class="lineno" href="#L1041">1041</a></span>
<span id="L1042" class="lineno"><a class="lineno" href="#L1042">1042</a></span>
<span id="L1043" class="lineno"><a class="lineno" href="#L1043">1043</a></span>
<span id="L1044" class="lineno"><a class="lineno" href="#L1044">1044</a></span>
<span id="L1045" class="lineno"><a class="lineno" href="#L1045">1045</a></span>
<span id="L1046" class="lineno"><a class="lineno" href="#L1046">1046</a></span>
<span id="L1047" class="lineno"><a class="lineno" href="#L1047">1047</a></span>
<span id="L1048" class="lineno"><a class="lineno" href="#L1048">1048</a></span>
<span id="L1049" class="lineno"><a class="lineno" href="#L1049">1049</a></span>
<span id="L1050" class="lineno"><a class="lineno" href="#L1050">1050</a></span>
<span id="L1051" class="lineno"><a class="lineno" href="#L1051">1051</a></span>
<span id="L1052" class="lineno"><a class="lineno" href="#L1052">1052</a></span>
<span id="L1053" class="lineno"><a class="lineno" href="#L1053">1053</a></span>
<span id="L1054" class="lineno"><a class="lineno" href="#L1054">1054</a></span>
<span id="L1055" class="lineno"><a class="lineno" href="#L1055">1055</a></span>
<span id="L1056" class="lineno"><a class="lineno" href="#L1056">1056</a></span>
<span id="L1057" class="lineno"><a class="lineno" href="#L1057">1057</a></span>
<span id="L1058" class="lineno"><a class="lineno" href="#L1058">1058</a></span>
<span id="L1059" class="lineno"><a class="lineno" href="#L1059">1059</a></span>
<span id="L1060" class="lineno"><a class="lineno" href="#L1060">1060</a></span>
<span id="L1061" class="lineno"><a class="lineno" href="#L1061">1061</a></span>
<span id="L1062" class="lineno"><a class="lineno" href="#L1062">1062</a></span>
<span id="L1063" class="lineno"><a class="lineno" href="#L1063">1063</a></span>
<span id="L1064" class="lineno"><a class="lineno" href="#L1064">1064</a></span>
<span id="L1065" class="lineno"><a class="lineno" href="#L1065">1065</a></span>
<span id="L1066" class="lineno"><a class="lineno" href="#L1066">1066</a></span>
<span id="L1067" class="lineno"><a class="lineno" href="#L1067">1067</a></span>
<span id="L1068" class="lineno"><a class="lineno" href="#L1068">1068</a></span>
<span id="L1069" class="lineno"><a class="lineno" href="#L1069">1069</a></span>
<span id="L1070" class="lineno"><a class="lineno" href="#L1070">1070</a></span>
<span id="L1071" class="lineno"><a class="lineno" href="#L1071">1071</a></span>
<span id="L1072" class="lineno"><a class="lineno" href="#L1072">1072</a></span>
<span id="L1073" class="lineno"><a class="lineno" href="#L1073">1073</a></span>
<span id="L1074" class="lineno"><a class="lineno" href="#L1074">1074</a></span>
<span id="L1075" class="lineno"><a class="lineno" href="#L1075">1075</a></span>
<span id="L1076" class="lineno"><a class="lineno" href="#L1076">1076</a></span>
<span id="L1077" class="lineno"><a class="lineno" href="#L1077">1077</a></span>
<span id="L1078" class="lineno"><a class="lineno" href="#L1078">1078</a></span>
<span id="L1079" class="lineno"><a class="lineno" href="#L1079">1079</a></span>
<span id="L1080" class="lineno"><a class="lineno" href="#L1080">1080</a></span>
<span id="L1081" class="lineno"><a class="lineno" href="#L1081">1081</a></span>
<span id="L1082" class="lineno"><a class="lineno" href="#L1082">1082</a></span>
<span id="L1083" class="lineno"><a class="lineno" href="#L1083">1083</a></span>
<span id="L1084" class="lineno"><a class="lineno" href="#L1084">1084</a></span>
<span id="L1085" class="lineno"><a class="lineno" href="#L1085">1085</a></span>
<span id="L1086" class="lineno"><a class="lineno" href="#L1086">1086</a></span>
<span id="L1087" class="lineno"><a class="lineno" href="#L1087">1087</a></span>
<span id="L1088" class="lineno"><a class="lineno" href="#L1088">1088</a></span>
<span id="L1089" class="lineno"><a class="lineno" href="#L1089">1089</a></span>
<span id="L1090" class="lineno"><a class="lineno" href="#L1090">1090</a></span>
<span id="L1091" class="lineno"><a class="lineno" href="#L1091">1091</a></span>
<span id="L1092" class="lineno"><a class="lineno" href="#L1092">1092</a></span>
<span id="L1093" class="lineno"><a class="lineno" href="#L1093">1093</a></span>
<span id="L1094" class="lineno"><a class="lineno" href="#L1094">1094</a></span>
<span id="L1095" class="lineno"><a class="lineno" href="#L1095">1095</a></span>
<span id="L1096" class="lineno"><a class="lineno" href="#L1096">1096</a></span>
<span id="L1097" class="lineno"><a class="lineno" href="#L1097">1097</a></span>
<span id="L1098" class="lineno"><a class="lineno" href="#L1098">1098</a></span>
<span id="L1099" class="lineno"><a class="lineno" href="#L1099">1099</a></span>
<span id="L1100" class="lineno"><a class="lineno" href="#L1100">1100</a></span>
<span id="L1101" class="lineno"><a class="lineno" href="#L1101">1101</a></span>
<span id="L1102" class="lineno"><a class="lineno" href="#L1102">1102</a></span>
<span id="L1103" class="lineno"><a class="lineno" href="#L1103">1103</a></span>
<span id="L1104" class="lineno"><a class="lineno" href="#L1104">1104</a></span>
<span id="L1105" class="lineno"><a class="lineno" href="#L1105">1105</a></span>
<span id="L1106" class="lineno"><a class="lineno" href="#L1106">1106</a></span>
<span id="L1107" class="lineno"><a class="lineno" href="#L1107">1107</a></span>
<span id="L1108" class="lineno"><a class="lineno" href="#L1108">1108</a></span>
<span id="L1109" class="lineno"><a class="lineno" href="#L1109">1109</a></span>
<span id="L1110" class="lineno"><a class="lineno" href="#L1110">1110</a></span>
<span id="L1111" class="lineno"><a class="lineno" href="#L1111">1111</a></span>
<span id="L1112" class="lineno"><a class="lineno" href="#L1112">1112</a></span>
<span id="L1113" class="lineno"><a class="lineno" href="#L1113">1113</a></span>
<span id="L1114" class="lineno"><a class="lineno" href="#L1114">1114</a></span>
<span id="L1115" class="lineno"><a class="lineno" href="#L1115">1115</a></span>
<span id="L1116" class="lineno"><a class="lineno" href="#L1116">1116</a></span>
<span id="L1117" class="lineno"><a class="lineno" href="#L1117">1117</a></span>
<span id="L1118" class="lineno"><a class="lineno" href="#L1118">1118</a></span>
<span id="L1119" class="lineno"><a class="lineno" href="#L1119">1119</a></span>
<span id="L1120" class="lineno"><a class="lineno" href="#L1120">1120</a></span>
<span id="L1121" class="lineno"><a class="lineno" href="#L1121">1121</a></span>
<span id="L1122" class="lineno"><a class="lineno" href="#L1122">1122</a></span>
<span id="L1123" class="lineno"><a class="lineno" href="#L1123">1123</a></span>
<span id="L1124" class="lineno"><a class="lineno" href="#L1124">1124</a></span>
<span id="L1125" class="lineno"><a class="lineno" href="#L1125">1125</a></span>
<span id="L1126" class="lineno"><a class="lineno" href="#L1126">1126</a></span>
<span id="L1127" class="lineno"><a class="lineno" href="#L1127">1127</a></span>
<span id="L1128" class="lineno"><a class="lineno" href="#L1128">1128</a></span>
<span id="L1129" class="lineno"><a class="lineno" href="#L1129">1129</a></span>
<span id="L1130" class="lineno"><a class="lineno" href="#L1130">1130</a></span>
<span id="L1131" class="lineno"><a class="lineno" href="#L1131">1131</a></span>
<span id="L1132" class="lineno"><a class="lineno" href="#L1132">1132</a></span>
<span id="L1133" class="lineno"><a class="lineno" href="#L1133">1133</a></span>
<span id="L1134" class="lineno"><a class="lineno" href="#L1134">1134</a></span>
<span id="L1135" class="lineno"><a class="lineno" href="#L1135">1135</a></span>
<span id="L1136" class="lineno"><a class="lineno" href="#L1136">1136</a></span>
<span id="L1137" class="lineno"><a class="lineno" href="#L1137">1137</a></span>
<span id="L1138" class="lineno"><a class="lineno" href="#L1138">1138</a></span>
<span id="L1139" class="lineno"><a class="lineno" href="#L1139">1139</a></span>
<span id="L1140" class="lineno"><a class="lineno" href="#L1140">1140</a></span>
<span id="L1141" class="lineno"><a class="lineno" href="#L1141">1141</a></span>
<span id="L1142" class="lineno"><a class="lineno" href="#L1142">1142</a></span>
<span id="L1143" class="lineno"><a class="lineno" href="#L1143">1143</a></span>
<span id="L1144" class="lineno"><a class="lineno" href="#L1144">1144</a></span>
<span id="L1145" class="lineno"><a class="lineno" href="#L1145">1145</a></span>
<span id="L1146" class="lineno"><a class="lineno" href="#L1146">1146</a></span>
<span id="L1147" class="lineno"><a class="lineno" href="#L1147">1147</a></span>
<span id="L1148" class="lineno"><a class="lineno" href="#L1148">1148</a></span>
<span id="L1149" class="lineno"><a class="lineno" href="#L1149">1149</a></span>
<span id="L1150" class="lineno"><a class="lineno" href="#L1150">1150</a></span>
<span id="L1151" class="lineno"><a class="lineno" href="#L1151">1151</a></span>
<span id="L1152" class="lineno"><a class="lineno" href="#L1152">1152</a></span>
<span id="L1153" class="lineno"><a class="lineno" href="#L1153">1153</a></span>
<span id="L1154" class="lineno"><a class="lineno" href="#L1154">1154</a></span>
<span id="L1155" class="lineno"><a class="lineno" href="#L1155">1155</a></span>
<span id="L1156" class="lineno"><a class="lineno" href="#L1156">1156</a></span>
<span id="L1157" class="lineno"><a class="lineno" href="#L1157">1157</a></span>
<span id="L1158" class="lineno"><a class="lineno" href="#L1158">1158</a></span>
<span id="L1159" class="lineno"><a class="lineno" href="#L1159">1159</a></span>
<span id="L1160" class="lineno"><a class="lineno" href="#L1160">1160</a></span>
<span id="L1161" class="lineno"><a class="lineno" href="#L1161">1161</a></span>
<span id="L1162" class="lineno"><a class="lineno" href="#L1162">1162</a></span>
<span id="L1163" class="lineno"><a class="lineno" href="#L1163">1163</a></span>
<span id="L1164" class="lineno"><a class="lineno" href="#L1164">1164</a></span>
<span id="L1165" class="lineno"><a class="lineno" href="#L1165">1165</a></span>
<span id="L1166" class="lineno"><a class="lineno" href="#L1166">1166</a></span>
<span id="L1167" class="lineno"><a class="lineno" href="#L1167">1167</a></span>
<span id="L1168" class="lineno"><a class="lineno" href="#L1168">1168</a></span>
<span id="L1169" class="lineno"><a class="lineno" href="#L1169">1169</a></span>
<span id="L1170" class="lineno"><a class="lineno" href="#L1170">1170</a></span>
<span id="L1171" class="lineno"><a class="lineno" href="#L1171">1171</a></span>
<span id="L1172" class="lineno"><a class="lineno" href="#L1172">1172</a></span>
<span id="L1173" class="lineno"><a class="lineno" href="#L1173">1173</a></span>
<span id="L1174" class="lineno"><a class="lineno" href="#L1174">1174</a></span>
<span id="L1175" class="lineno"><a class="lineno" href="#L1175">1175</a></span>
<span id="L1176" class="lineno"><a class="lineno" href="#L1176">1176</a></span>
<span id="L1177" class="lineno"><a class="lineno" href="#L1177">1177</a></span>
<span id="L1178" class="lineno"><a class="lineno" href="#L1178">1178</a></span>
<span id="L1179" class="lineno"><a class="lineno" href="#L1179">1179</a></span>
<span id="L1180" class="lineno"><a class="lineno" href="#L1180">1180</a></span>
<span id="L1181" class="lineno"><a class="lineno" href="#L1181">1181</a></span>
<span id="L1182" class="lineno"><a class="lineno" href="#L1182">1182</a></span>
<span id="L1183" class="lineno"><a class="lineno" href="#L1183">1183</a></span>
<span id="L1184" class="lineno"><a class="lineno" href="#L1184">1184</a></span>
<span id="L1185" class="lineno"><a class="lineno" href="#L1185">1185</a></span>
<span id="L1186" class="lineno"><a class="lineno" href="#L1186">1186</a></span>
<span id="L1187" class="lineno"><a class="lineno" href="#L1187">1187</a></span>
<span id="L1188" class="lineno"><a class="lineno" href="#L1188">1188</a></span>
<span id="L1189" class="lineno"><a class="lineno" href="#L1189">1189</a></span>
<span id="L1190" class="lineno"><a class="lineno" href="#L1190">1190</a></span>
<span id="L1191" class="lineno"><a class="lineno" href="#L1191">1191</a></span>
<span id="L1192" class="lineno"><a class="lineno" href="#L1192">1192</a></span>
<span id="L1193" class="lineno"><a class="lineno" href="#L1193">1193</a></span>
<span id="L1194" class="lineno"><a class="lineno" href="#L1194">1194</a></span>
<span id="L1195" class="lineno"><a class="lineno" href="#L1195">1195</a></span>
<span id="L1196" class="lineno"><a class="lineno" href="#L1196">1196</a></span>
<span id="L1197" class="lineno"><a class="lineno" href="#L1197">1197</a></span>
<span id="L1198" class="lineno"><a class="lineno" href="#L1198">1198</a></span>
<span id="L1199" class="lineno"><a class="lineno" href="#L1199">1199</a></span>
<span id="L1200" class="lineno"><a class="lineno" href="#L1200">1200</a></span>
<span id="L1201" class="lineno"><a class="lineno" href="#L1201">1201</a></span>
<span id="L1202" class="lineno"><a class="lineno" href="#L1202">1202</a></span>
<span id="L1203" class="lineno"><a class="lineno" href="#L1203">1203</a></span>
<span id="L1204" class="lineno"><a class="lineno" href="#L1204">1204</a></span>
<span id="L1205" class="lineno"><a class="lineno" href="#L1205">1205</a></span>
<span id="L1206" class="lineno"><a class="lineno" href="#L1206">1206</a></span>
<span id="L1207" class="lineno"><a class="lineno" href="#L1207">1207</a></span>
<span id="L1208" class="lineno"><a class="lineno" href="#L1208">1208</a></span>
<span id="L1209" class="lineno"><a class="lineno" href="#L1209">1209</a></span>
<span id="L1210" class="lineno"><a class="lineno" href="#L1210">1210</a></span>
<span id="L1211" class="lineno"><a class="lineno" href="#L1211">1211</a></span>
<span id="L1212" class="lineno"><a class="lineno" href="#L1212">1212</a></span>
<span id="L1213" class="lineno"><a class="lineno" href="#L1213">1213</a></span>
<span id="L1214" class="lineno"><a class="lineno" href="#L1214">1214</a></span>
<span id="L1215" class="lineno"><a class="lineno" href="#L1215">1215</a></span>
<span id="L1216" class="lineno"><a class="lineno" href="#L1216">1216</a></span>
<span id="L1217" class="lineno"><a class="lineno" href="#L1217">1217</a></span>
<span id="L1218" class="lineno"><a class="lineno" href="#L1218">1218</a></span>
<span id="L1219" class="lineno"><a class="lineno" href="#L1219">1219</a></span>
<span id="L1220" class="lineno"><a class="lineno" href="#L1220">1220</a></span>
<span id="L1221" class="lineno"><a class="lineno" href="#L1221">1221</a></span>
<span id="L1222" class="lineno"><a class="lineno" href="#L1222">1222</a></span>
<span id="L1223" class="lineno"><a class="lineno" href="#L1223">1223</a></span>
<span id="L1224" class="lineno"><a class="lineno" href="#L1224">1224</a></span>
<span id="L1225" class="lineno"><a class="lineno" href="#L1225">1225</a></span>
<span id="L1226" class="lineno"><a class="lineno" href="#L1226">1226</a></span>
<span id="L1227" class="lineno"><a class="lineno" href="#L1227">1227</a></span>
<span id="L1228" class="lineno"><a class="lineno" href="#L1228">1228</a></span>
<span id="L1229" class="lineno"><a class="lineno" href="#L1229">1229</a></span>
<span id="L1230" class="lineno"><a class="lineno" href="#L1230">1230</a></span>
<span id="L1231" class="lineno"><a class="lineno" href="#L1231">1231</a></span>
<span id="L1232" class="lineno"><a class="lineno" href="#L1232">1232</a></span>
<span id="L1233" class="lineno"><a class="lineno" href="#L1233">1233</a></span>
<span id="L1234" class="lineno"><a class="lineno" href="#L1234">1234</a></span>
<span id="L1235" class="lineno"><a class="lineno" href="#L1235">1235</a></span>
<span id="L1236" class="lineno"><a class="lineno" href="#L1236">1236</a></span>
<span id="L1237" class="lineno"><a class="lineno" href="#L1237">1237</a></span>
<span id="L1238" class="lineno"><a class="lineno" href="#L1238">1238</a></span>
<span id="L1239" class="lineno"><a class="lineno" href="#L1239">1239</a></span>
<span id="L1240" class="lineno"><a class="lineno" href="#L1240">1240</a></span>
<span id="L1241" class="lineno"><a class="lineno" href="#L1241">1241</a></span>
<span id="L1242" class="lineno"><a class="lineno" href="#L1242">1242</a></span>
<span id="L1243" class="lineno"><a class="lineno" href="#L1243">1243</a></span>
<span id="L1244" class="lineno"><a class="lineno" href="#L1244">1244</a></span>
<span id="L1245" class="lineno"><a class="lineno" href="#L1245">1245</a></span>
<span id="L1246" class="lineno"><a class="lineno" href="#L1246">1246</a></span>
<span id="L1247" class="lineno"><a class="lineno" href="#L1247">1247</a></span>
<span id="L1248" class="lineno"><a class="lineno" href="#L1248">1248</a></span>
<span id="L1249" class="lineno"><a class="lineno" href="#L1249">1249</a></span>
<span id="L1250" class="lineno"><a class="lineno" href="#L1250">1250</a></span>
<span id="L1251" class="lineno"><a class="lineno" href="#L1251">1251</a></span>
<span id="L1252" class="lineno"><a class="lineno" href="#L1252">1252</a></span>
<span id="L1253" class="lineno"><a class="lineno" href="#L1253">1253</a></span>
<span id="L1254" class="lineno"><a class="lineno" href="#L1254">1254</a></span>
<span id="L1255" class="lineno"><a class="lineno" href="#L1255">1255</a></span>
<span id="L1256" class="lineno"><a class="lineno" href="#L1256">1256</a></span>
<span id="L1257" class="lineno"><a class="lineno" href="#L1257">1257</a></span>
<span id="L1258" class="lineno"><a class="lineno" href="#L1258">1258</a></span>
<span id="L1259" class="lineno"><a class="lineno" href="#L1259">1259</a></span>
<span id="L1260" class="lineno"><a class="lineno" href="#L1260">1260</a></span>
<span id="L1261" class="lineno"><a class="lineno" href="#L1261">1261</a></span>
<span id="L1262" class="lineno"><a class="lineno" href="#L1262">1262</a></span>
<span id="L1263" class="lineno"><a class="lineno" href="#L1263">1263</a></span>
<span id="L1264" class="lineno"><a class="lineno" href="#L1264">1264</a></span>
<span id="L1265" class="lineno"><a class="lineno" href="#L1265">1265</a></span>
<span id="L1266" class="lineno"><a class="lineno" href="#L1266">1266</a></span>
<span id="L1267" class="lineno"><a class="lineno" href="#L1267">1267</a></span>
<span id="L1268" class="lineno"><a class="lineno" href="#L1268">1268</a></span>
<span id="L1269" class="lineno"><a class="lineno" href="#L1269">1269</a></span>
<span id="L1270" class="lineno"><a class="lineno" href="#L1270">1270</a></span>
<span id="L1271" class="lineno"><a class="lineno" href="#L1271">1271</a></span>
<span id="L1272" class="lineno"><a class="lineno" href="#L1272">1272</a></span>
<span id="L1273" class="lineno"><a class="lineno" href="#L1273">1273</a></span>
<span id="L1274" class="lineno"><a class="lineno" href="#L1274">1274</a></span>
<span id="L1275" class="lineno"><a class="lineno" href="#L1275">1275</a></span>
<span id="L1276" class="lineno"><a class="lineno" href="#L1276">1276</a></span>
<span id="L1277" class="lineno"><a class="lineno" href="#L1277">1277</a></span>
<span id="L1278" class="lineno"><a class="lineno" href="#L1278">1278</a></span>
<span id="L1279" class="lineno"><a class="lineno" href="#L1279">1279</a></span>
<span id="L1280" class="lineno"><a class="lineno" href="#L1280">1280</a></span>
<span id="L1281" class="lineno"><a class="lineno" href="#L1281">1281</a></span>
<span id="L1282" class="lineno"><a class="lineno" href="#L1282">1282</a></span>
<span id="L1283" class="lineno"><a class="lineno" href="#L1283">1283</a></span>
<span id="L1284" class="lineno"><a class="lineno" href="#L1284">1284</a></span>
<span id="L1285" class="lineno"><a class="lineno" href="#L1285">1285</a></span>
<span id="L1286" class="lineno"><a class="lineno" href="#L1286">1286</a></span>
<span id="L1287" class="lineno"><a class="lineno" href="#L1287">1287</a></span>
<span id="L1288" class="lineno"><a class="lineno" href="#L1288">1288</a></span>
<span id="L1289" class="lineno"><a class="lineno" href="#L1289">1289</a></span>
<span id="L1290" class="lineno"><a class="lineno" href="#L1290">1290</a></span>
<span id="L1291" class="lineno"><a class="lineno" href="#L1291">1291</a></span>
<span id="L1292" class="lineno"><a class="lineno" href="#L1292">1292</a></span>
<span id="L1293" class="lineno"><a class="lineno" href="#L1293">1293</a></span>
<span id="L1294" class="lineno"><a class="lineno" href="#L1294">1294</a></span>
<span id="L1295" class="lineno"><a class="lineno" href="#L1295">1295</a></span>
<span id="L1296" class="lineno"><a class="lineno" href="#L1296">1296</a></span>
<span id="L1297" class="lineno"><a class="lineno" href="#L1297">1297</a></span>
<span id="L1298" class="lineno"><a class="lineno" href="#L1298">1298</a></span>
<span id="L1299" class="lineno"><a class="lineno" href="#L1299">1299</a></span>
<span id="L1300" class="lineno"><a class="lineno" href="#L1300">1300</a></span>
<span id="L1301" class="lineno"><a class="lineno" href="#L1301">1301</a></span>
<span id="L1302" class="lineno"><a class="lineno" href="#L1302">1302</a></span>
<span id="L1303" class="lineno"><a class="lineno" href="#L1303">1303</a></span>
<span id="L1304" class="lineno"><a class="lineno" href="#L1304">1304</a></span>
<span id="L1305" class="lineno"><a class="lineno" href="#L1305">1305</a></span>
<span id="L1306" class="lineno"><a class="lineno" href="#L1306">1306</a></span>
<span id="L1307" class="lineno"><a class="lineno" href="#L1307">1307</a></span>
<span id="L1308" class="lineno"><a class="lineno" href="#L1308">1308</a></span>
<span id="L1309" class="lineno"><a class="lineno" href="#L1309">1309</a></span>
<span id="L1310" class="lineno"><a class="lineno" href="#L1310">1310</a></span>
<span id="L1311" class="lineno"><a class="lineno" href="#L1311">1311</a></span>
<span id="L1312" class="lineno"><a class="lineno" href="#L1312">1312</a></span>
<span id="L1313" class="lineno"><a class="lineno" href="#L1313">1313</a></span>
<span id="L1314" class="lineno"><a class="lineno" href="#L1314">1314</a></span>
<span id="L1315" class="lineno"><a class="lineno" href="#L1315">1315</a></span>
<span id="L1316" class="lineno"><a class="lineno" href="#L1316">1316</a></span>
<span id="L1317" class="lineno"><a class="lineno" href="#L1317">1317</a></span>
<span id="L1318" class="lineno"><a class="lineno" href="#L1318">1318</a></span>
<span id="L1319" class="lineno"><a class="lineno" href="#L1319">1319</a></span>
<span id="L1320" class="lineno"><a class="lineno" href="#L1320">1320</a></span>
<span id="L1321" class="lineno"><a class="lineno" href="#L1321">1321</a></span>
<span id="L1322" class="lineno"><a class="lineno" href="#L1322">1322</a></span>
<span id="L1323" class="lineno"><a class="lineno" href="#L1323">1323</a></span>
<span id="L1324" class="lineno"><a class="lineno" href="#L1324">1324</a></span>
<span id="L1325" class="lineno"><a class="lineno" href="#L1325">1325</a></span>
<span id="L1326" class="lineno"><a class="lineno" href="#L1326">1326</a></span>
<span id="L1327" class="lineno"><a class="lineno" href="#L1327">1327</a></span>
<span id="L1328" class="lineno"><a class="lineno" href="#L1328">1328</a></span>
<span id="L1329" class="lineno"><a class="lineno" href="#L1329">1329</a></span>
<span id="L1330" class="lineno"><a class="lineno" href="#L1330">1330</a></span>
<span id="L1331" class="lineno"><a class="lineno" href="#L1331">1331</a></span>
<span id="L1332" class="lineno"><a class="lineno" href="#L1332">1332</a></span>
<span id="L1333" class="lineno"><a class="lineno" href="#L1333">1333</a></span>
<span id="L1334" class="lineno"><a class="lineno" href="#L1334">1334</a></span>
<span id="L1335" class="lineno"><a class="lineno" href="#L1335">1335</a></span>
<span id="L1336" class="lineno"><a class="lineno" href="#L1336">1336</a></span>
<span id="L1337" class="lineno"><a class="lineno" href="#L1337">1337</a></span>
<span id="L1338" class="lineno"><a class="lineno" href="#L1338">1338</a></span>
<span id="L1339" class="lineno"><a class="lineno" href="#L1339">1339</a></span>
<span id="L1340" class="lineno"><a class="lineno" href="#L1340">1340</a></span>
<span id="L1341" class="lineno"><a class="lineno" href="#L1341">1341</a></span>
<span id="L1342" class="lineno"><a class="lineno" href="#L1342">1342</a></span>
<span id="L1343" class="lineno"><a class="lineno" href="#L1343">1343</a></span>
<span id="L1344" class="lineno"><a class="lineno" href="#L1344">1344</a></span>
<span id="L1345" class="lineno"><a class="lineno" href="#L1345">1345</a></span>
<span id="L1346" class="lineno"><a class="lineno" href="#L1346">1346</a></span>
<span id="L1347" class="lineno"><a class="lineno" href="#L1347">1347</a></span>
<span id="L1348" class="lineno"><a class="lineno" href="#L1348">1348</a></span>
<span id="L1349" class="lineno"><a class="lineno" href="#L1349">1349</a></span>
<span id="L1350" class="lineno"><a class="lineno" href="#L1350">1350</a></span>
<span id="L1351" class="lineno"><a class="lineno" href="#L1351">1351</a></span>
<span id="L1352" class="lineno"><a class="lineno" href="#L1352">1352</a></span>
<span id="L1353" class="lineno"><a class="lineno" href="#L1353">1353</a></span>
<span id="L1354" class="lineno"><a class="lineno" href="#L1354">1354</a></span>
<span id="L1355" class="lineno"><a class="lineno" href="#L1355">1355</a></span>
<span id="L1356" class="lineno"><a class="lineno" href="#L1356">1356</a></span>
<span id="L1357" class="lineno"><a class="lineno" href="#L1357">1357</a></span>
<span id="L1358" class="lineno"><a class="lineno" href="#L1358">1358</a></span>
<span id="L1359" class="lineno"><a class="lineno" href="#L1359">1359</a></span>
<span id="L1360" class="lineno"><a class="lineno" href="#L1360">1360</a></span>
<span id="L1361" class="lineno"><a class="lineno" href="#L1361">1361</a></span>
<span id="L1362" class="lineno"><a class="lineno" href="#L1362">1362</a></span>
<span id="L1363" class="lineno"><a class="lineno" href="#L1363">1363</a></span>
<span id="L1364" class="lineno"><a class="lineno" href="#L1364">1364</a></span>
<span id="L1365" class="lineno"><a class="lineno" href="#L1365">1365</a></span>
<span id="L1366" class="lineno"><a class="lineno" href="#L1366">1366</a></span>
<span id="L1367" class="lineno"><a class="lineno" href="#L1367">1367</a></span>
<span id="L1368" class="lineno"><a class="lineno" href="#L1368">1368</a></span>
<span id="L1369" class="lineno"><a class="lineno" href="#L1369">1369</a></span>
<span id="L1370" class="lineno"><a class="lineno" href="#L1370">1370</a></span>
<span id="L1371" class="lineno"><a class="lineno" href="#L1371">1371</a></span>
<span id="L1372" class="lineno"><a class="lineno" href="#L1372">1372</a></span>
<span id="L1373" class="lineno"><a class="lineno" href="#L1373">1373</a></span>
<span id="L1374" class="lineno"><a class="lineno" href="#L1374">1374</a></span>
<span id="L1375" class="lineno"><a class="lineno" href="#L1375">1375</a></span>
<span id="L1376" class="lineno"><a class="lineno" href="#L1376">1376</a></span>
<span id="L1377" class="lineno"><a class="lineno" href="#L1377">1377</a></span>
<span id="L1378" class="lineno"><a class="lineno" href="#L1378">1378</a></span>
<span id="L1379" class="lineno"><a class="lineno" href="#L1379">1379</a></span>
<span id="L1380" class="lineno"><a class="lineno" href="#L1380">1380</a></span>
<span id="L1381" class="lineno"><a class="lineno" href="#L1381">1381</a></span>
<span id="L1382" class="lineno"><a class="lineno" href="#L1382">1382</a></span>
<span id="L1383" class="lineno"><a class="lineno" href="#L1383">1383</a></span>
<span id="L1384" class="lineno"><a class="lineno" href="#L1384">1384</a></span>
<span id="L1385" class="lineno"><a class="lineno" href="#L1385">1385</a></span>
<span id="L1386" class="lineno"><a class="lineno" href="#L1386">1386</a></span>
<span id="L1387" class="lineno"><a class="lineno" href="#L1387">1387</a></span>
<span id="L1388" class="lineno"><a class="lineno" href="#L1388">1388</a></span>
<span id="L1389" class="lineno"><a class="lineno" href="#L1389">1389</a></span>
<span id="L1390" class="lineno"><a class="lineno" href="#L1390">1390</a></span>
<span id="L1391" class="lineno"><a class="lineno" href="#L1391">1391</a></span>
<span id="L1392" class="lineno"><a class="lineno" href="#L1392">1392</a></span>
<span id="L1393" class="lineno"><a class="lineno" href="#L1393">1393</a></span>
<span id="L1394" class="lineno"><a class="lineno" href="#L1394">1394</a></span>
<span id="L1395" class="lineno"><a class="lineno" href="#L1395">1395</a></span>
<span id="L1396" class="lineno"><a class="lineno" href="#L1396">1396</a></span>
<span id="L1397" class="lineno"><a class="lineno" href="#L1397">1397</a></span>
<span id="L1398" class="lineno"><a class="lineno" href="#L1398">1398</a></span>
<span id="L1399" class="lineno"><a class="lineno" href="#L1399">1399</a></span>
<span id="L1400" class="lineno"><a class="lineno" href="#L1400">1400</a></span>
<span id="L1401" class="lineno"><a class="lineno" href="#L1401">1401</a></span>
<span id="L1402" class="lineno"><a class="lineno" href="#L1402">1402</a></span>
<span id="L1403" class="lineno"><a class="lineno" href="#L1403">1403</a></span>
<span id="L1404" class="lineno"><a class="lineno" href="#L1404">1404</a></span>
<span id="L1405" class="lineno"><a class="lineno" href="#L1405">1405</a></span>
<span id="L1406" class="lineno"><a class="lineno" href="#L1406">1406</a></span>
<span id="L1407" class="lineno"><a class="lineno" href="#L1407">1407</a></span>
<span id="L1408" class="lineno"><a class="lineno" href="#L1408">1408</a></span>
<span id="L1409" class="lineno"><a class="lineno" href="#L1409">1409</a></span>
<span id="L1410" class="lineno"><a class="lineno" href="#L1410">1410</a></span>
<span id="L1411" class="lineno"><a class="lineno" href="#L1411">1411</a></span>
<span id="L1412" class="lineno"><a class="lineno" href="#L1412">1412</a></span>
<span id="L1413" class="lineno"><a class="lineno" href="#L1413">1413</a></span>
<span id="L1414" class="lineno"><a class="lineno" href="#L1414">1414</a></span>
<span id="L1415" class="lineno"><a class="lineno" href="#L1415">1415</a></span>
<span id="L1416" class="lineno"><a class="lineno" href="#L1416">1416</a></span>
<span id="L1417" class="lineno"><a class="lineno" href="#L1417">1417</a></span>
<span id="L1418" class="lineno"><a class="lineno" href="#L1418">1418</a></span>
<span id="L1419" class="lineno"><a class="lineno" href="#L1419">1419</a></span>
<span id="L1420" class="lineno"><a class="lineno" href="#L1420">1420</a></span>
<span id="L1421" class="lineno"><a class="lineno" href="#L1421">1421</a></span>
<span id="L1422" class="lineno"><a class="lineno" href="#L1422">1422</a></span>
<span id="L1423" class="lineno"><a class="lineno" href="#L1423">1423</a></span>
<span id="L1424" class="lineno"><a class="lineno" href="#L1424">1424</a></span>
<span id="L1425" class="lineno"><a class="lineno" href="#L1425">1425</a></span>
<span id="L1426" class="lineno"><a class="lineno" href="#L1426">1426</a></span>
<span id="L1427" class="lineno"><a class="lineno" href="#L1427">1427</a></span>
<span id="L1428" class="lineno"><a class="lineno" href="#L1428">1428</a></span>
<span id="L1429" class="lineno"><a class="lineno" href="#L1429">1429</a></span>
<span id="L1430" class="lineno"><a class="lineno" href="#L1430">1430</a></span>
<span id="L1431" class="lineno"><a class="lineno" href="#L1431">1431</a></span>
<span id="L1432" class="lineno"><a class="lineno" href="#L1432">1432</a></span>
<span id="L1433" class="lineno"><a class="lineno" href="#L1433">1433</a></span>
<span id="L1434" class="lineno"><a class="lineno" href="#L1434">1434</a></span>
<span id="L1435" class="lineno"><a class="lineno" href="#L1435">1435</a></span>
<span id="L1436" class="lineno"><a class="lineno" href="#L1436">1436</a></span>
<span id="L1437" class="lineno"><a class="lineno" href="#L1437">1437</a></span>
<span id="L1438" class="lineno"><a class="lineno" href="#L1438">1438</a></span>
<span id="L1439" class="lineno"><a class="lineno" href="#L1439">1439</a></span>
<span id="L1440" class="lineno"><a class="lineno" href="#L1440">1440</a></span>
<span id="L1441" class="lineno"><a class="lineno" href="#L1441">1441</a></span>
<span id="L1442" class="lineno"><a class="lineno" href="#L1442">1442</a></span>
<span id="L1443" class="lineno"><a class="lineno" href="#L1443">1443</a></span>
<span id="L1444" class="lineno"><a class="lineno" href="#L1444">1444</a></span>
<span id="L1445" class="lineno"><a class="lineno" href="#L1445">1445</a></span>
<span id="L1446" class="lineno"><a class="lineno" href="#L1446">1446</a></span>
<span id="L1447" class="lineno"><a class="lineno" href="#L1447">1447</a></span>
<span id="L1448" class="lineno"><a class="lineno" href="#L1448">1448</a></span>
<span id="L1449" class="lineno"><a class="lineno" href="#L1449">1449</a></span>
<span id="L1450" class="lineno"><a class="lineno" href="#L1450">1450</a></span>
<span id="L1451" class="lineno"><a class="lineno" href="#L1451">1451</a></span>
<span id="L1452" class="lineno"><a class="lineno" href="#L1452">1452</a></span>
<span id="L1453" class="lineno"><a class="lineno" href="#L1453">1453</a></span>
<span id="L1454" class="lineno"><a class="lineno" href="#L1454">1454</a></span>
<span id="L1455" class="lineno"><a class="lineno" href="#L1455">1455</a></span>
<span id="L1456" class="lineno"><a class="lineno" href="#L1456">1456</a></span>
<span id="L1457" class="lineno"><a class="lineno" href="#L1457">1457</a></span>
<span id="L1458" class="lineno"><a class="lineno" href="#L1458">1458</a></span>
<span id="L1459" class="lineno"><a class="lineno" href="#L1459">1459</a></span>
<span id="L1460" class="lineno"><a class="lineno" href="#L1460">1460</a></span>
<span id="L1461" class="lineno"><a class="lineno" href="#L1461">1461</a></span>
<span id="L1462" class="lineno"><a class="lineno" href="#L1462">1462</a></span>
<span id="L1463" class="lineno"><a class="lineno" href="#L1463">1463</a></span>
<span id="L1464" class="lineno"><a class="lineno" href="#L1464">1464</a></span>
<span id="L1465" class="lineno"><a class="lineno" href="#L1465">1465</a></span>
<span id="L1466" class="lineno"><a class="lineno" href="#L1466">1466</a></span>
<span id="L1467" class="lineno"><a class="lineno" href="#L1467">1467</a></span>
<span id="L1468" class="lineno"><a class="lineno" href="#L1468">1468</a></span>
<span id="L1469" class="lineno"><a class="lineno" href="#L1469">1469</a></span>
<span id="L1470" class="lineno"><a class="lineno" href="#L1470">1470</a></span>
<span id="L1471" class="lineno"><a class="lineno" href="#L1471">1471</a></span>
<span id="L1472" class="lineno"><a class="lineno" href="#L1472">1472</a></span>
<span id="L1473" class="lineno"><a class="lineno" href="#L1473">1473</a></span>
<span id="L1474" class="lineno"><a class="lineno" href="#L1474">1474</a></span>
<span id="L1475" class="lineno"><a class="lineno" href="#L1475">1475</a></span>
<span id="L1476" class="lineno"><a class="lineno" href="#L1476">1476</a></span>
<span id="L1477" class="lineno"><a class="lineno" href="#L1477">1477</a></span>
<span id="L1478" class="lineno"><a class="lineno" href="#L1478">1478</a></span>
<span id="L1479" class="lineno"><a class="lineno" href="#L1479">1479</a></span>
<span id="L1480" class="lineno"><a class="lineno" href="#L1480">1480</a></span>
<span id="L1481" class="lineno"><a class="lineno" href="#L1481">1481</a></span>
<span id="L1482" class="lineno"><a class="lineno" href="#L1482">1482</a></span>
<span id="L1483" class="lineno"><a class="lineno" href="#L1483">1483</a></span>
<span id="L1484" class="lineno"><a class="lineno" href="#L1484">1484</a></span>
<span id="L1485" class="lineno"><a class="lineno" href="#L1485">1485</a></span>
<span id="L1486" class="lineno"><a class="lineno" href="#L1486">1486</a></span>
<span id="L1487" class="lineno"><a class="lineno" href="#L1487">1487</a></span>
<span id="L1488" class="lineno"><a class="lineno" href="#L1488">1488</a></span>
<span id="L1489" class="lineno"><a class="lineno" href="#L1489">1489</a></span>
<span id="L1490" class="lineno"><a class="lineno" href="#L1490">1490</a></span>
<span id="L1491" class="lineno"><a class="lineno" href="#L1491">1491</a></span>
<span id="L1492" class="lineno"><a class="lineno" href="#L1492">1492</a></span>
<span id="L1493" class="lineno"><a class="lineno" href="#L1493">1493</a></span>
<span id="L1494" class="lineno"><a class="lineno" href="#L1494">1494</a></span>
<span id="L1495" class="lineno"><a class="lineno" href="#L1495">1495</a></span>
<span id="L1496" class="lineno"><a class="lineno" href="#L1496">1496</a></span>
<span id="L1497" class="lineno"><a class="lineno" href="#L1497">1497</a></span>
<span id="L1498" class="lineno"><a class="lineno" href="#L1498">1498</a></span>
<span id="L1499" class="lineno"><a class="lineno" href="#L1499">1499</a></span>
<span id="L1500" class="lineno"><a class="lineno" href="#L1500">1500</a></span>
<span id="L1501" class="lineno"><a class="lineno" href="#L1501">1501</a></span>
<span id="L1502" class="lineno"><a class="lineno" href="#L1502">1502</a></span>
<span id="L1503" class="lineno"><a class="lineno" href="#L1503">1503</a></span>
<span id="L1504" class="lineno"><a class="lineno" href="#L1504">1504</a></span>
<span id="L1505" class="lineno"><a class="lineno" href="#L1505">1505</a></span>
<span id="L1506" class="lineno"><a class="lineno" href="#L1506">1506</a></span>
<span id="L1507" class="lineno"><a class="lineno" href="#L1507">1507</a></span>
<span id="L1508" class="lineno"><a class="lineno" href="#L1508">1508</a></span>
<span id="L1509" class="lineno"><a class="lineno" href="#L1509">1509</a></span>
<span id="L1510" class="lineno"><a class="lineno" href="#L1510">1510</a></span>
<span id="L1511" class="lineno"><a class="lineno" href="#L1511">1511</a></span>
<span id="L1512" class="lineno"><a class="lineno" href="#L1512">1512</a></span>
<span id="L1513" class="lineno"><a class="lineno" href="#L1513">1513</a></span>
<span id="L1514" class="lineno"><a class="lineno" href="#L1514">1514</a></span>
<span id="L1515" class="lineno"><a class="lineno" href="#L1515">1515</a></span>
<span id="L1516" class="lineno"><a class="lineno" href="#L1516">1516</a></span>
<span id="L1517" class="lineno"><a class="lineno" href="#L1517">1517</a></span>
<span id="L1518" class="lineno"><a class="lineno" href="#L1518">1518</a></span>
<span id="L1519" class="lineno"><a class="lineno" href="#L1519">1519</a></span>
<span id="L1520" class="lineno"><a class="lineno" href="#L1520">1520</a></span>
<span id="L1521" class="lineno"><a class="lineno" href="#L1521">1521</a></span>
<span id="L1522" class="lineno"><a class="lineno" href="#L1522">1522</a></span>
<span id="L1523" class="lineno"><a class="lineno" href="#L1523">1523</a></span>
<span id="L1524" class="lineno"><a class="lineno" href="#L1524">1524</a></span>
<span id="L1525" class="lineno"><a class="lineno" href="#L1525">1525</a></span>
<span id="L1526" class="lineno"><a class="lineno" href="#L1526">1526</a></span>
<span id="L1527" class="lineno"><a class="lineno" href="#L1527">1527</a></span>
<span id="L1528" class="lineno"><a class="lineno" href="#L1528">1528</a></span>
<span id="L1529" class="lineno"><a class="lineno" href="#L1529">1529</a></span>
<span id="L1530" class="lineno"><a class="lineno" href="#L1530">1530</a></span>
<span id="L1531" class="lineno"><a class="lineno" href="#L1531">1531</a></span>
<span id="L1532" class="lineno"><a class="lineno" href="#L1532">1532</a></span>
<span id="L1533" class="lineno"><a class="lineno" href="#L1533">1533</a></span>
<span id="L1534" class="lineno"><a class="lineno" href="#L1534">1534</a></span>
<span id="L1535" class="lineno"><a class="lineno" href="#L1535">1535</a></span>
<span id="L1536" class="lineno"><a class="lineno" href="#L1536">1536</a></span>
<span id="L1537" class="lineno"><a class="lineno" href="#L1537">1537</a></span>
<span id="L1538" class="lineno"><a class="lineno" href="#L1538">1538</a></span>
<span id="L1539" class="lineno"><a class="lineno" href="#L1539">1539</a></span>
<span id="L1540" class="lineno"><a class="lineno" href="#L1540">1540</a></span>
<span id="L1541" class="lineno"><a class="lineno" href="#L1541">1541</a></span>
<span id="L1542" class="lineno"><a class="lineno" href="#L1542">1542</a></span>
<span id="L1543" class="lineno"><a class="lineno" href="#L1543">1543</a></span>
<span id="L1544" class="lineno"><a class="lineno" href="#L1544">1544</a></span>
<span id="L1545" class="lineno"><a class="lineno" href="#L1545">1545</a></span>
<span id="L1546" class="lineno"><a class="lineno" href="#L1546">1546</a></span>
<span id="L1547" class="lineno"><a class="lineno" href="#L1547">1547</a></span>
<span id="L1548" class="lineno"><a class="lineno" href="#L1548">1548</a></span>
<span id="L1549" class="lineno"><a class="lineno" href="#L1549">1549</a></span>
<span id="L1550" class="lineno"><a class="lineno" href="#L1550">1550</a></span>
<span id="L1551" class="lineno"><a class="lineno" href="#L1551">1551</a></span>
<span id="L1552" class="lineno"><a class="lineno" href="#L1552">1552</a></span>
<span id="L1553" class="lineno"><a class="lineno" href="#L1553">1553</a></span>
<span id="L1554" class="lineno"><a class="lineno" href="#L1554">1554</a></span>
<span id="L1555" class="lineno"><a class="lineno" href="#L1555">1555</a></span>
<span id="L1556" class="lineno"><a class="lineno" href="#L1556">1556</a></span>
<span id="L1557" class="lineno"><a class="lineno" href="#L1557">1557</a></span>
<span id="L1558" class="lineno"><a class="lineno" href="#L1558">1558</a></span>
<span id="L1559" class="lineno"><a class="lineno" href="#L1559">1559</a></span>
<span id="L1560" class="lineno"><a class="lineno" href="#L1560">1560</a></span>
<span id="L1561" class="lineno"><a class="lineno" href="#L1561">1561</a></span>
<span id="L1562" class="lineno"><a class="lineno" href="#L1562">1562</a></span>
<span id="L1563" class="lineno"><a class="lineno" href="#L1563">1563</a></span>
<span id="L1564" class="lineno"><a class="lineno" href="#L1564">1564</a></span>
<span id="L1565" class="lineno"><a class="lineno" href="#L1565">1565</a></span>
<span id="L1566" class="lineno"><a class="lineno" href="#L1566">1566</a></span>
<span id="L1567" class="lineno"><a class="lineno" href="#L1567">1567</a></span>
<span id="L1568" class="lineno"><a class="lineno" href="#L1568">1568</a></span>
<span id="L1569" class="lineno"><a class="lineno" href="#L1569">1569</a></span>
<span id="L1570" class="lineno"><a class="lineno" href="#L1570">1570</a></span>
<span id="L1571" class="lineno"><a class="lineno" href="#L1571">1571</a></span>
<span id="L1572" class="lineno"><a class="lineno" href="#L1572">1572</a></span>
<span id="L1573" class="lineno"><a class="lineno" href="#L1573">1573</a></span>
<span id="L1574" class="lineno"><a class="lineno" href="#L1574">1574</a></span>
<span id="L1575" class="lineno"><a class="lineno" href="#L1575">1575</a></span>
<span id="L1576" class="lineno"><a class="lineno" href="#L1576">1576</a></span>
<span id="L1577" class="lineno"><a class="lineno" href="#L1577">1577</a></span>
<span id="L1578" class="lineno"><a class="lineno" href="#L1578">1578</a></span>
<span id="L1579" class="lineno"><a class="lineno" href="#L1579">1579</a></span>
<span id="L1580" class="lineno"><a class="lineno" href="#L1580">1580</a></span>
<span id="L1581" class="lineno"><a class="lineno" href="#L1581">1581</a></span>
<span id="L1582" class="lineno"><a class="lineno" href="#L1582">1582</a></span>
<span id="L1583" class="lineno"><a class="lineno" href="#L1583">1583</a></span>
<span id="L1584" class="lineno"><a class="lineno" href="#L1584">1584</a></span>
<span id="L1585" class="lineno"><a class="lineno" href="#L1585">1585</a></span>
<span id="L1586" class="lineno"><a class="lineno" href="#L1586">1586</a></span>
<span id="L1587" class="lineno"><a class="lineno" href="#L1587">1587</a></span>
<span id="L1588" class="lineno"><a class="lineno" href="#L1588">1588</a></span>
<span id="L1589" class="lineno"><a class="lineno" href="#L1589">1589</a></span>
<span id="L1590" class="lineno"><a class="lineno" href="#L1590">1590</a></span>
<span id="L1591" class="lineno"><a class="lineno" href="#L1591">1591</a></span>
<span id="L1592" class="lineno"><a class="lineno" href="#L1592">1592</a></span>
<span id="L1593" class="lineno"><a class="lineno" href="#L1593">1593</a></span>
<span id="L1594" class="lineno"><a class="lineno" href="#L1594">1594</a></span>
<span id="L1595" class="lineno"><a class="lineno" href="#L1595">1595</a></span>
<span id="L1596" class="lineno"><a class="lineno" href="#L1596">1596</a></span>
<span id="L1597" class="lineno"><a class="lineno" href="#L1597">1597</a></span>
<span id="L1598" class="lineno"><a class="lineno" href="#L1598">1598</a></span>
<span id="L1599" class="lineno"><a class="lineno" href="#L1599">1599</a></span>
<span id="L1600" class="lineno"><a class="lineno" href="#L1600">1600</a></span>
<span id="L1601" class="lineno"><a class="lineno" href="#L1601">1601</a></span>
<span id="L1602" class="lineno"><a class="lineno" href="#L1602">1602</a></span>
<span id="L1603" class="lineno"><a class="lineno" href="#L1603">1603</a></span>
<span id="L1604" class="lineno"><a class="lineno" href="#L1604">1604</a></span>
<span id="L1605" class="lineno"><a class="lineno" href="#L1605">1605</a></span>
<span id="L1606" class="lineno"><a class="lineno" href="#L1606">1606</a></span>
<span id="L1607" class="lineno"><a class="lineno" href="#L1607">1607</a></span>
<span id="L1608" class="lineno"><a class="lineno" href="#L1608">1608</a></span>
<span id="L1609" class="lineno"><a class="lineno" href="#L1609">1609</a></span>
<span id="L1610" class="lineno"><a class="lineno" href="#L1610">1610</a></span>
<span id="L1611" class="lineno"><a class="lineno" href="#L1611">1611</a></span>
<span id="L1612" class="lineno"><a class="lineno" href="#L1612">1612</a></span>
<span id="L1613" class="lineno"><a class="lineno" href="#L1613">1613</a></span>
<span id="L1614" class="lineno"><a class="lineno" href="#L1614">1614</a></span>
<span id="L1615" class="lineno"><a class="lineno" href="#L1615">1615</a></span>
<span id="L1616" class="lineno"><a class="lineno" href="#L1616">1616</a></span>
<span id="L1617" class="lineno"><a class="lineno" href="#L1617">1617</a></span>
<span id="L1618" class="lineno"><a class="lineno" href="#L1618">1618</a></span>
<span id="L1619" class="lineno"><a class="lineno" href="#L1619">1619</a></span>
<span id="L1620" class="lineno"><a class="lineno" href="#L1620">1620</a></span>
<span id="L1621" class="lineno"><a class="lineno" href="#L1621">1621</a></span>
<span id="L1622" class="lineno"><a class="lineno" href="#L1622">1622</a></span>
<span id="L1623" class="lineno"><a class="lineno" href="#L1623">1623</a></span>
<span id="L1624" class="lineno"><a class="lineno" href="#L1624">1624</a></span>
<span id="L1625" class="lineno"><a class="lineno" href="#L1625">1625</a></span>
<span id="L1626" class="lineno"><a class="lineno" href="#L1626">1626</a></span>
<span id="L1627" class="lineno"><a class="lineno" href="#L1627">1627</a></span>
<span id="L1628" class="lineno"><a class="lineno" href="#L1628">1628</a></span>
<span id="L1629" class="lineno"><a class="lineno" href="#L1629">1629</a></span>
<span id="L1630" class="lineno"><a class="lineno" href="#L1630">1630</a></span>
<span id="L1631" class="lineno"><a class="lineno" href="#L1631">1631</a></span>
<span id="L1632" class="lineno"><a class="lineno" href="#L1632">1632</a></span>
<span id="L1633" class="lineno"><a class="lineno" href="#L1633">1633</a></span>
<span id="L1634" class="lineno"><a class="lineno" href="#L1634">1634</a></span>
<span id="L1635" class="lineno"><a class="lineno" href="#L1635">1635</a></span>
<span id="L1636" class="lineno"><a class="lineno" href="#L1636">1636</a></span>
<span id="L1637" class="lineno"><a class="lineno" href="#L1637">1637</a></span>
<span id="L1638" class="lineno"><a class="lineno" href="#L1638">1638</a></span>
<span id="L1639" class="lineno"><a class="lineno" href="#L1639">1639</a></span>
<span id="L1640" class="lineno"><a class="lineno" href="#L1640">1640</a></span>
<span id="L1641" class="lineno"><a class="lineno" href="#L1641">1641</a></span>
<span id="L1642" class="lineno"><a class="lineno" href="#L1642">1642</a></span>
<span id="L1643" class="lineno"><a class="lineno" href="#L1643">1643</a></span>
<span id="L1644" class="lineno"><a class="lineno" href="#L1644">1644</a></span>
<span id="L1645" class="lineno"><a class="lineno" href="#L1645">1645</a></span>
<span id="L1646" class="lineno"><a class="lineno" href="#L1646">1646</a></span>
<span id="L1647" class="lineno"><a class="lineno" href="#L1647">1647</a></span>
<span id="L1648" class="lineno"><a class="lineno" href="#L1648">1648</a></span>
<span id="L1649" class="lineno"><a class="lineno" href="#L1649">1649</a></span>
<span id="L1650" class="lineno"><a class="lineno" href="#L1650">1650</a></span>
<span id="L1651" class="lineno"><a class="lineno" href="#L1651">1651</a></span>
<span id="L1652" class="lineno"><a class="lineno" href="#L1652">1652</a></span>
<span id="L1653" class="lineno"><a class="lineno" href="#L1653">1653</a></span>
<span id="L1654" class="lineno"><a class="lineno" href="#L1654">1654</a></span>
<span id="L1655" class="lineno"><a class="lineno" href="#L1655">1655</a></span>
<span id="L1656" class="lineno"><a class="lineno" href="#L1656">1656</a></span>
<span id="L1657" class="lineno"><a class="lineno" href="#L1657">1657</a></span>
<span id="L1658" class="lineno"><a class="lineno" href="#L1658">1658</a></span>
<span id="L1659" class="lineno"><a class="lineno" href="#L1659">1659</a></span>
<span id="L1660" class="lineno"><a class="lineno" href="#L1660">1660</a></span>
<span id="L1661" class="lineno"><a class="lineno" href="#L1661">1661</a></span>
<span id="L1662" class="lineno"><a class="lineno" href="#L1662">1662</a></span>
<span id="L1663" class="lineno"><a class="lineno" href="#L1663">1663</a></span>
<span id="L1664" class="lineno"><a class="lineno" href="#L1664">1664</a></span>
<span id="L1665" class="lineno"><a class="lineno" href="#L1665">1665</a></span>
<span id="L1666" class="lineno"><a class="lineno" href="#L1666">1666</a></span>
<span id="L1667" class="lineno"><a class="lineno" href="#L1667">1667</a></span>
<span id="L1668" class="lineno"><a class="lineno" href="#L1668">1668</a></span>
<span id="L1669" class="lineno"><a class="lineno" href="#L1669">1669</a></span>
<span id="L1670" class="lineno"><a class="lineno" href="#L1670">1670</a></span>
<span id="L1671" class="lineno"><a class="lineno" href="#L1671">1671</a></span>
<span id="L1672" class="lineno"><a class="lineno" href="#L1672">1672</a></span>
<span id="L1673" class="lineno"><a class="lineno" href="#L1673">1673</a></span>
<span id="L1674" class="lineno"><a class="lineno" href="#L1674">1674</a></span>
<span id="L1675" class="lineno"><a class="lineno" href="#L1675">1675</a></span>
<span id="L1676" class="lineno"><a class="lineno" href="#L1676">1676</a></span>
<span id="L1677" class="lineno"><a class="lineno" href="#L1677">1677</a></span>
<span id="L1678" class="lineno"><a class="lineno" href="#L1678">1678</a></span>
<span id="L1679" class="lineno"><a class="lineno" href="#L1679">1679</a></span>
<span id="L1680" class="lineno"><a class="lineno" href="#L1680">1680</a></span>
<span id="L1681" class="lineno"><a class="lineno" href="#L1681">1681</a></span>
<span id="L1682" class="lineno"><a class="lineno" href="#L1682">1682</a></span>
<span id="L1683" class="lineno"><a class="lineno" href="#L1683">1683</a></span>
<span id="L1684" class="lineno"><a class="lineno" href="#L1684">1684</a></span>
<span id="L1685" class="lineno"><a class="lineno" href="#L1685">1685</a></span>
<span id="L1686" class="lineno"><a class="lineno" href="#L1686">1686</a></span>
<span id="L1687" class="lineno"><a class="lineno" href="#L1687">1687</a></span>
<span id="L1688" class="lineno"><a class="lineno" href="#L1688">1688</a></span>
<span id="L1689" class="lineno"><a class="lineno" href="#L1689">1689</a></span>
<span id="L1690" class="lineno"><a class="lineno" href="#L1690">1690</a></span>
<span id="L1691" class="lineno"><a class="lineno" href="#L1691">1691</a></span>
<span id="L1692" class="lineno"><a class="lineno" href="#L1692">1692</a></span>
<span id="L1693" class="lineno"><a class="lineno" href="#L1693">1693</a></span>
<span id="L1694" class="lineno"><a class="lineno" href="#L1694">1694</a></span>
<span id="L1695" class="lineno"><a class="lineno" href="#L1695">1695</a></span>
<span id="L1696" class="lineno"><a class="lineno" href="#L1696">1696</a></span>
<span id="L1697" class="lineno"><a class="lineno" href="#L1697">1697</a></span>
<span id="L1698" class="lineno"><a class="lineno" href="#L1698">1698</a></span>
<span id="L1699" class="lineno"><a class="lineno" href="#L1699">1699</a></span>
<span id="L1700" class="lineno"><a class="lineno" href="#L1700">1700</a></span>
<span id="L1701" class="lineno"><a class="lineno" href="#L1701">1701</a></span>
<span id="L1702" class="lineno"><a class="lineno" href="#L1702">1702</a></span>
<span id="L1703" class="lineno"><a class="lineno" href="#L1703">1703</a></span>
<span id="L1704" class="lineno"><a class="lineno" href="#L1704">1704</a></span>
<span id="L1705" class="lineno"><a class="lineno" href="#L1705">1705</a></span>
<span id="L1706" class="lineno"><a class="lineno" href="#L1706">1706</a></span>
<span id="L1707" class="lineno"><a class="lineno" href="#L1707">1707</a></span>
<span id="L1708" class="lineno"><a class="lineno" href="#L1708">1708</a></span>
<span id="L1709" class="lineno"><a class="lineno" href="#L1709">1709</a></span>
<span id="L1710" class="lineno"><a class="lineno" href="#L1710">1710</a></span>
<span id="L1711" class="lineno"><a class="lineno" href="#L1711">1711</a></span>
<span id="L1712" class="lineno"><a class="lineno" href="#L1712">1712</a></span>
<span id="L1713" class="lineno"><a class="lineno" href="#L1713">1713</a></span>
<span id="L1714" class="lineno"><a class="lineno" href="#L1714">1714</a></span>
<span id="L1715" class="lineno"><a class="lineno" href="#L1715">1715</a></span>
<span id="L1716" class="lineno"><a class="lineno" href="#L1716">1716</a></span>
<span id="L1717" class="lineno"><a class="lineno" href="#L1717">1717</a></span>
<span id="L1718" class="lineno"><a class="lineno" href="#L1718">1718</a></span>
</pre></td>
<td class="table-code"><pre><span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!">Flow orchestration system for AiLex AI agents.</span>
<span class="line-empty" title="No Anys on this line!">Manages agent coordination, workflow execution, and resource allocation.</span>
<span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">import asyncio</span>
<span class="line-precise" title="No Anys on this line!">import uuid</span>
<span class="line-precise" title="No Anys on this line!">from datetime import datetime, timedelta</span>
<span class="line-precise" title="No Anys on this line!">from enum import Enum</span>
<span class="line-precise" title="No Anys on this line!">from typing import Any, Dict, List, Optional, Callable, Union, Set</span>
<span class="line-precise" title="No Anys on this line!">from dataclasses import dataclass, field</span>
<span class="line-precise" title="No Anys on this line!">from collections import defaultdict</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="No Anys on this line!">import structlog</span>
<span class="line-any" title="No Anys on this line!">from crewai import Crew, Task, Agent</span>
<span class="line-any" title="No Anys on this line!">from crewai.flow import Flow, start, listen, or_, and_, router</span>
<span class="line-any" title="No Anys on this line!">from crewai.process import Process</span>
<span class="line-any" title="No Anys on this line!">from crewai.memory import LongTermMemory, ShortTermMemory</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from .base import BaseAiLexAgent, AgentMessage, AgentContext, AgentError</span>
<span class="line-precise" title="No Anys on this line!">from .tracing import PhoenixTracer, create_agent_tracer</span>
<span class="line-precise" title="No Anys on this line!">from .google_ads_config import GoogleAdsAgentSpecialization, GoogleAdsWorkflowTemplates, WORKFLOW_TEMPLATES</span>
<span class="line-precise" title="No Anys on this line!">from models.agents import AgentType, AgentStatus, TaskStatus, TaskPriority</span>
<span class="line-precise" title="No Anys on this line!">from utils.config import settings</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="Any Types on this line: 
Unimported (x4)">logger = structlog.get_logger(__name__)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class WorkflowStatus(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """Workflow execution status."""</span>
<span class="line-precise" title="No Anys on this line!">    PENDING = "pending"</span>
<span class="line-precise" title="No Anys on this line!">    RUNNING = "running"</span>
<span class="line-precise" title="No Anys on this line!">    PAUSED = "paused"</span>
<span class="line-precise" title="No Anys on this line!">    COMPLETED = "completed"</span>
<span class="line-precise" title="No Anys on this line!">    FAILED = "failed"</span>
<span class="line-precise" title="No Anys on this line!">    CANCELLED = "cancelled"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class ExecutionStrategy(str, Enum):</span>
<span class="line-empty" title="No Anys on this line!">    """Workflow execution strategies."""</span>
<span class="line-precise" title="No Anys on this line!">    SEQUENTIAL = "sequential"</span>
<span class="line-precise" title="No Anys on this line!">    PARALLEL = "parallel"</span>
<span class="line-precise" title="No Anys on this line!">    CONDITIONAL = "conditional"</span>
<span class="line-precise" title="No Anys on this line!">    PIPELINE = "pipeline"</span>
<span class="line-precise" title="No Anys on this line!">    HYBRID = "hybrid"</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@dataclass</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x6)">class WorkflowStep:</span>
<span class="line-empty" title="No Anys on this line!">    """Individual workflow step definition."""</span>
<span class="line-precise" title="No Anys on this line!">    id: str</span>
<span class="line-precise" title="No Anys on this line!">    name: str</span>
<span class="line-precise" title="No Anys on this line!">    agent_type: AgentType</span>
<span class="line-precise" title="No Anys on this line!">    description: str</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    input_schema: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    output_schema: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x7)
Explicit (x7)">    dependencies: List[str] = field(default_factory=list)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x7)
Explicit (x7)">    conditions: List[str] = field(default_factory=list)</span>
<span class="line-precise" title="No Anys on this line!">    timeout_seconds: int = 300</span>
<span class="line-precise" title="No Anys on this line!">    retry_attempts: int = 3</span>
<span class="line-precise" title="No Anys on this line!">    priority: TaskPriority = TaskPriority.NORMAL</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    metadata: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@dataclass</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">class WorkflowDefinition:</span>
<span class="line-empty" title="No Anys on this line!">    """Complete workflow definition."""</span>
<span class="line-precise" title="No Anys on this line!">    id: str</span>
<span class="line-precise" title="No Anys on this line!">    name: str</span>
<span class="line-precise" title="No Anys on this line!">    description: str</span>
<span class="line-precise" title="No Anys on this line!">    version: str = "1.0.0"</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x7)
Explicit (x7)">    steps: List[WorkflowStep] = field(default_factory=list)</span>
<span class="line-precise" title="No Anys on this line!">    strategy: ExecutionStrategy = ExecutionStrategy.SEQUENTIAL</span>
<span class="line-precise" title="No Anys on this line!">    max_duration_seconds: int = 3600</span>
<span class="line-precise" title="No Anys on this line!">    error_handling: str = "stop"  # stop, continue, retry</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    metadata: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@dataclass</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x2)
Explicit (x8)">class CrewExecution:</span>
<span class="line-empty" title="No Anys on this line!">    """CrewAI crew execution state and tracking."""</span>
<span class="line-precise" title="No Anys on this line!">    id: str</span>
<span class="line-precise" title="No Anys on this line!">    crew_id: str</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x1)">    crew: Optional[Crew] = None</span>
<span class="line-precise" title="No Anys on this line!">    status: WorkflowStatus = WorkflowStatus.PENDING</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)
Explicit (x7)">    created_at: datetime = field(default_factory=datetime.utcnow)</span>
<span class="line-precise" title="No Anys on this line!">    started_at: Optional[datetime] = None</span>
<span class="line-precise" title="No Anys on this line!">    completed_at: Optional[datetime] = None</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)
Explicit (x8)">    context: AgentContext = field(default_factory=AgentContext)</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    input_data: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    output_data: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    task_results: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x7)
Explicit (x7)">    error_log: List[str] = field(default_factory=list)</span>
<span class="line-precise" title="No Anys on this line!">    current_task: Optional[str] = None</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x7)
Explicit (x7)">    completed_tasks: List[str] = field(default_factory=list)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x7)
Explicit (x7)">    failed_tasks: List[str] = field(default_factory=list)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x22)
Explicit (x7)">    retry_counts: Dict[str, int] = field(default_factory=dict)</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    metadata: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@dataclass</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x8)">class WorkflowExecution:</span>
<span class="line-empty" title="No Anys on this line!">    """Workflow execution state and tracking."""</span>
<span class="line-precise" title="No Anys on this line!">    id: str</span>
<span class="line-precise" title="No Anys on this line!">    workflow_id: str</span>
<span class="line-precise" title="No Anys on this line!">    status: WorkflowStatus</span>
<span class="line-precise" title="No Anys on this line!">    created_at: datetime</span>
<span class="line-precise" title="No Anys on this line!">    started_at: Optional[datetime] = None</span>
<span class="line-precise" title="No Anys on this line!">    completed_at: Optional[datetime] = None</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)
Explicit (x8)">    context: AgentContext = field(default_factory=AgentContext)</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    input_data: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    output_data: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    step_results: Dict[str, Dict[str, Any]] = field(default_factory=dict)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x7)
Explicit (x7)">    error_log: List[str] = field(default_factory=list)</span>
<span class="line-precise" title="No Anys on this line!">    current_step: Optional[str] = None</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x7)
Explicit (x7)">    completed_steps: List[str] = field(default_factory=list)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x7)
Explicit (x7)">    failed_steps: List[str] = field(default_factory=list)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x22)
Explicit (x7)">    retry_counts: Dict[str, int] = field(default_factory=dict)</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x9)
Omitted Generics (x22)">    metadata: Dict[str, Any] = field(default_factory=dict)</span>
<span class="line-precise" title="No Anys on this line!">    crew_execution: Optional[CrewExecution] = None</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class CrewOrchestrator:</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    CrewAI-based orchestrator for Google Ads agent coordination.</span>
<span class="line-empty" title="No Anys on this line!">    Manages crew creation, task delegation, and agent collaboration.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __init__(self, tracer: Optional[PhoenixTracer] = None):</span>
<span class="line-precise" title="No Anys on this line!">        self.tracer = tracer</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x1)">        self.crews: Dict[str, Crew] = {}</span>
<span class="line-precise" title="No Anys on this line!">        self.crew_executions: Dict[str, CrewExecution] = {}</span>
<span class="line-precise" title="No Anys on this line!">        self.agents: Dict[str, BaseAiLexAgent] = {}</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x1)">        self.crew_agents: Dict[str, Agent] = {}  # CrewAI Agent instances</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x3)">        self.running_crews: Set[str] = set()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Google Ads specialization</span>
<span class="line-precise" title="No Anys on this line!">        self.specialization = GoogleAdsAgentSpecialization()</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">        self.workflow_templates = WORKFLOW_TEMPLATES</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Resource management</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x31)">        self.agent_pools: Dict[AgentType, List[str]] = defaultdict(list)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x28)">        self.agent_workloads: Dict[str, int] = defaultdict(int)</span>
<span class="line-precise" title="No Anys on this line!">        self.max_concurrent_crews = 5</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Memory systems</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.long_term_memory = LongTermMemory()</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.short_term_memory = ShortTermMemory()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Event system</span>
<span class="line-precise" title="Any Types on this line: 
Error (x6)
Omitted Generics (x31)">        self.event_callbacks: Dict[str, List[Callable]] = defaultdict(list)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x6)">        self.logger = structlog.get_logger().bind(component="crew_orchestrator")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def register_agent(self, agent: BaseAiLexAgent) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Register an agent with the crew orchestrator.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            agent: Agent instance to register</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        self.agents[agent.agent_id] = agent</span>
<span class="line-precise" title="No Anys on this line!">        self.agent_pools[agent.agent_type].append(agent.agent_id)</span>
<span class="line-precise" title="No Anys on this line!">        self.agent_workloads[agent.agent_id] = 0</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Create CrewAI Agent instance</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        crew_agent = self._create_crew_agent(agent)</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x2)">        self.crew_agents[agent.agent_id] = crew_agent</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">            "Agent registered with crew orchestrator",</span>
<span class="line-precise" title="No Anys on this line!">            agent_id=agent.agent_id,</span>
<span class="line-precise" title="No Anys on this line!">            agent_name=agent.name,</span>
<span class="line-precise" title="No Anys on this line!">            agent_type=agent.agent_type.value</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">    def _create_crew_agent(self, ailex_agent: BaseAiLexAgent) -&gt; Agent:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Create a CrewAI Agent from an AiLex agent.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            ailex_agent: AiLex agent instance</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            CrewAI Agent instance</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        # Get Google Ads specialization for this agent type</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)
Explicit (x6)
Omitted Generics (x2)">        specialization = self.specialization.get_agent_tasks_mapping().get(</span>
<span class="line-precise" title="No Anys on this line!">            ailex_agent.agent_type, {}</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x2)">        return Agent(</span>
<span class="line-precise" title="No Anys on this line!">            role=ailex_agent.name,</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">            goal=f"Execute {ailex_agent.agent_type.value} tasks for Google Ads campaigns with expertise in {', '.join(specialization.get('google_ads_focus', []))}",</span>
<span class="line-precise" title="No Anys on this line!">            backstory=f"You are a specialized AI agent for Google Ads campaign management with deep expertise in {ailex_agent.agent_type.value}. "</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                     f"Your primary responsibilities include: {', '.join(specialization.get('primary_tasks', []))}. "</span>
<span class="line-any" title="Any Types on this line: 
Error (x3)">                     f"You excel at Google Ads-specific tasks such as: {', '.join(specialization.get('google_ads_focus', []))}.",</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x14)
Omitted Generics (x2)">            tools=getattr(ailex_agent, 'tools', []),</span>
<span class="line-precise" title="No Anys on this line!">            verbose=settings.DEBUG,</span>
<span class="line-precise" title="No Anys on this line!">            memory=True,</span>
<span class="line-precise" title="No Anys on this line!">            max_iter=5,</span>
<span class="line-precise" title="No Anys on this line!">            max_execution_time=300,  # 5 minutes timeout per task</span>
<span class="line-precise" title="No Anys on this line!">            allow_delegation=True,</span>
<span class="line-precise" title="No Anys on this line!">            system_template=f"""You are {ailex_agent.name}, a specialized AI agent for Google Ads campaign management.</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">Your Role: {ailex_agent.agent_type.value}</span>
<span class="line-empty" title="No Anys on this line!">Your Goal: Execute high-quality Google Ads optimization tasks with precision and expertise.</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!">Key Responsibilities:</span>
<span class="line-any" title="Any Types on this line: 
Error (x5)">{chr(10).join([f"- {task}" for task in specialization.get('primary_tasks', [])])}</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!">Google Ads Expertise:</span>
<span class="line-any" title="Any Types on this line: 
Error (x5)">{chr(10).join([f"- {focus}" for focus in specialization.get('google_ads_focus', [])])}</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!">Always provide detailed, actionable insights and maintain focus on campaign performance optimization."""</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def create_google_ads_crew(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        workflow_type: str,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_config: Dict[str, Any],</span>
<span class="line-precise" title="No Anys on this line!">        required_agents: Optional[List[AgentType]] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; str:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Create a specialized crew for Google Ads campaign management.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            workflow_type: Type of workflow (e.g., 'new_search_campaign', 'campaign_optimization')</span>
<span class="line-empty" title="No Anys on this line!">            campaign_config: Campaign configuration data</span>
<span class="line-empty" title="No Anys on this line!">            required_agents: Specific agents required for this crew</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            str: Crew ID</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Raises:</span>
<span class="line-empty" title="No Anys on this line!">            AgentError: If crew cannot be created</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        if len(self.running_crews) &gt;= self.max_concurrent_crews:</span>
<span class="line-precise" title="No Anys on this line!">            raise AgentError("Maximum concurrent crews reached")</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">        workflow_template = self.workflow_templates.get(workflow_type)</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">        if not workflow_template:</span>
<span class="line-precise" title="No Anys on this line!">            raise AgentError(f"Unknown workflow type: {workflow_type}")</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        crew_id = str(uuid.uuid4())</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Determine required agents from workflow template or explicit list</span>
<span class="line-precise" title="No Anys on this line!">        if required_agents is None:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            required_agents = self._extract_required_agents(workflow_template)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Select agents for the crew</span>
<span class="line-precise" title="No Anys on this line!">        selected_agents = []</span>
<span class="line-precise" title="No Anys on this line!">        crew_agents = []</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        for agent_type in required_agents:</span>
<span class="line-precise" title="No Anys on this line!">            agent = await self._find_available_agent(agent_type)</span>
<span class="line-precise" title="No Anys on this line!">            if not agent:</span>
<span class="line-precise" title="No Anys on this line!">                raise AgentError(f"No available agent for type: {agent_type}")</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            selected_agents.append(agent)</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x4)">            crew_agents.append(self.crew_agents[agent.agent_id])</span>
<span class="line-precise" title="No Anys on this line!">            self.agent_workloads[agent.agent_id] += 1</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Create tasks for the crew</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x3)
Explicit (x4)">        tasks = self._create_crew_tasks(workflow_template, campaign_config)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Create the crew</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        crew = Crew(</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x1)">            agents=crew_agents,</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x1)">            tasks=tasks,</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x5)">            process=Process.hierarchical if len(crew_agents) &gt; 3 else Process.sequential,</span>
<span class="line-precise" title="No Anys on this line!">            verbose=settings.DEBUG,</span>
<span class="line-precise" title="No Anys on this line!">            memory=True,</span>
<span class="line-precise" title="No Anys on this line!">            manager_llm=settings.OPENAI_MODEL,</span>
<span class="line-precise" title="No Anys on this line!">            max_rpm=30,  # Rate limiting</span>
<span class="line-precise" title="No Anys on this line!">            language="en",</span>
<span class="line-precise" title="No Anys on this line!">            full_output=True,</span>
<span class="line-precise" title="No Anys on this line!">            share_crew=False,</span>
<span class="line-precise" title="Any Types on this line: 
Unannotated (x2)">            step_callback=self._crew_step_callback,</span>
<span class="line-precise" title="Any Types on this line: 
Unannotated (x2)">            task_callback=self._crew_task_callback</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x2)">        self.crews[crew_id] = crew</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Create crew execution tracking</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)
Unimported (x1)">        crew_execution = CrewExecution(</span>
<span class="line-precise" title="No Anys on this line!">            id=crew_id,</span>
<span class="line-precise" title="No Anys on this line!">            crew_id=crew_id,</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">            crew=crew,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            context=AgentContext(</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">                user_id=campaign_config.get('user_id'),</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">                campaign_id=campaign_config.get('campaign_id'),</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                metadata=campaign_config</span>
<span class="line-empty" title="No Anys on this line!">            ),</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            input_data=campaign_config,</span>
<span class="line-empty" title="No Anys on this line!">            metadata={</span>
<span class="line-precise" title="No Anys on this line!">                'workflow_type': workflow_type,</span>
<span class="line-precise" title="No Anys on this line!">                'selected_agents': [agent.agent_id for agent in selected_agents],</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">                'estimated_duration': workflow_template.get('estimated_duration_hours', 0)</span>
<span class="line-empty" title="No Anys on this line!">            }</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        self.crew_executions[crew_id] = crew_execution</span>
<span class="line-precise" title="No Anys on this line!">        self.running_crews.add(crew_id)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">            "Google Ads crew created",</span>
<span class="line-precise" title="No Anys on this line!">            crew_id=crew_id,</span>
<span class="line-precise" title="No Anys on this line!">            workflow_type=workflow_type,</span>
<span class="line-precise" title="No Anys on this line!">            agents_count=len(selected_agents),</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x1)">            tasks_count=len(tasks)</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return crew_id</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    def _extract_required_agents(self, workflow_template: Dict[str, Any]) -&gt; List[AgentType]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Extract required agent types from workflow template.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            workflow_template: Workflow template configuration</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            List of required agent types</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x3)">        required_agents = set()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">        for stage in workflow_template.get('stages', []):</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)">            for agent_role in stage.get('agents', []):</span>
<span class="line-empty" title="No Anys on this line!">                # Convert agent role to AgentType</span>
<span class="line-empty" title="No Anys on this line!">                try:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                    if hasattr(agent_role, 'value'):</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x6)">                        agent_type = next(</span>
<span class="line-precise" title="No Anys on this line!">                            at for at in AgentType </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x5)">                            if at.value.lower() == agent_role.value.lower()</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-empty" title="No Anys on this line!">                    else:</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x6)">                        agent_type = next(</span>
<span class="line-precise" title="No Anys on this line!">                            at for at in AgentType </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                            if at.value.lower() == str(agent_role).lower()</span>
<span class="line-empty" title="No Anys on this line!">                        )</span>
<span class="line-precise" title="No Anys on this line!">                    required_agents.add(agent_type)</span>
<span class="line-precise" title="No Anys on this line!">                except StopIteration:</span>
<span class="line-empty" title="No Anys on this line!">                    # If agent role doesn't match AgentType, use default mapping</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                    self.logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">                        "Unknown agent role in workflow template",</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                        agent_role=str(agent_role)</span>
<span class="line-empty" title="No Anys on this line!">                    )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x3)">        return list(required_agents)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)
Unimported (x1)">    def _create_crew_tasks(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        workflow_template: Dict[str, Any],</span>
<span class="line-empty" title="No Anys on this line!">        campaign_config: Dict[str, Any]</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[Task]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Create CrewAI tasks from workflow template.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            workflow_template: Workflow template configuration</span>
<span class="line-empty" title="No Anys on this line!">            campaign_config: Campaign configuration data</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            List of CrewAI Task instances</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        tasks = []</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">        for stage in workflow_template.get('stages', []):</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)">            stage_name = stage.get('stage', 'unknown')</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)">            for task_name in stage.get('tasks', []):</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                task = Task(</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                    description=self._generate_task_description(task_name, campaign_config),</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                    expected_output=self._generate_expected_output(task_name),</span>
<span class="line-precise" title="No Anys on this line!">                    agent=None,  # Will be assigned by CrewAI based on agents list</span>
<span class="line-empty" title="No Anys on this line!">                    tools=[],</span>
<span class="line-precise" title="No Anys on this line!">                    async_execution=False,</span>
<span class="line-empty" title="No Anys on this line!">                    context=[],  # Dependencies will be set up later</span>
<span class="line-precise" title="No Anys on this line!">                    output_json=None,</span>
<span class="line-precise" title="No Anys on this line!">                    output_pydantic=None,</span>
<span class="line-precise" title="No Anys on this line!">                    output_file=None,</span>
<span class="line-precise" title="No Anys on this line!">                    callback=None</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                tasks.append(task)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x1)">        return tasks</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    def _generate_task_description(self, task_name: str, campaign_config: Dict[str, Any]) -&gt; str:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Generate detailed task description based on task name and campaign config.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            task_name: Name of the task</span>
<span class="line-empty" title="No Anys on this line!">            campaign_config: Campaign configuration data</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            Detailed task description</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        base_descriptions = {</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x12)
Omitted Generics (x4)">            "market_research": f"Conduct comprehensive market research for {campaign_config.get('business_description', 'the business')} campaign targeting {campaign_config.get('target_audience', 'defined audience')}. Analyze industry trends, market size, competitive landscape, and identify opportunities and threats.",</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">            "competitor_analysis": f"Perform detailed competitor analysis for {campaign_config.get('campaign_name', 'the campaign')}. Identify top competitors, analyze their advertising strategies, keywords, ad copy, and positioning. Provide actionable insights for competitive advantage.",</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">            "keyword_research": f"Execute comprehensive keyword research for {campaign_config.get('campaign_type', 'search')} campaign. Identify high-value primary and secondary keywords, long-tail variations, and negative keywords. Include search volume, competition, and cost estimates.",</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">            "audience_targeting": f"Develop precise audience targeting strategy for {campaign_config.get('objective', 'conversions')} campaign. Define demographic, geographic, behavioral, and interest-based targeting parameters. Create audience personas and recommend targeting optimizations.",</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x12)
Omitted Generics (x4)">            "campaign_structure_design": f"Design optimal campaign structure for {campaign_config.get('campaign_type', 'search')} campaign with budget of ${campaign_config.get('budget_daily', 0)}/day. Create campaign hierarchy, ad group organization, and keyword distribution strategy.",</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x18)
Omitted Generics (x6)">            "ad_copy_creation": f"Create compelling ad copy for {campaign_config.get('campaign_type', 'search')} campaign targeting {campaign_config.get('target_keywords', [])}. Develop multiple headline and description variations optimized for {campaign_config.get('objective', 'conversions')}.",</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">            "creative_asset_generation": f"Generate creative assets for {campaign_config.get('campaign_type', 'display')} campaign. Create visual assets, video content, and multimedia elements aligned with brand guidelines and campaign objectives.",</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">            "landing_page_optimization": f"Optimize landing page for {campaign_config.get('landing_page_url', 'campaign landing page')}. Analyze page performance, user experience, conversion optimization, and provide actionable improvement recommendations.",</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">            "google_ads_api_integration": f"Implement Google Ads API integration for {campaign_config.get('campaign_name', 'the campaign')}. Set up campaign creation, management, and optimization automation using Google Ads API.",</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">            "conversion_tracking_setup": f"Set up comprehensive conversion tracking for {campaign_config.get('conversion_goals', [])}. Implement Google Analytics integration, conversion pixels, and attribution modeling.",</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">            "performance_monitoring": f"Establish performance monitoring system for {campaign_config.get('campaign_name', 'the campaign')}. Create dashboards, alerts, and automated reporting for key metrics and KPIs.",</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">            "optimization_recommendations": f"Analyze campaign performance and provide optimization recommendations. Focus on improving {campaign_config.get('primary_kpis', ['ROAS', 'CPA', 'CTR'])} based on current performance data.",</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            "security_assessment": f"Conduct security assessment for Google Ads campaign setup. Verify data protection, access controls, API security, and compliance with privacy regulations.",</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">            "deployment": f"Deploy {campaign_config.get('campaign_name', 'the campaign')} to Google Ads platform. Execute launch sequence, verify tracking, and confirm all components are functioning correctly."</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">        return base_descriptions.get(</span>
<span class="line-precise" title="No Anys on this line!">            task_name,</span>
<span class="line-precise" title="No Anys on this line!">            f"Execute {task_name} task for Google Ads campaign with focus on achieving campaign objectives and performance targets."</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _generate_expected_output(self, task_name: str) -&gt; str:</span>
<span class="line-empty" title="No Anys on this line!">        """Generate expected output description for a task."""</span>
<span class="line-precise" title="No Anys on this line!">        expected_outputs = {</span>
<span class="line-precise" title="No Anys on this line!">            "market_research": "Comprehensive market research report with industry analysis, target audience insights, competitive landscape overview, and strategic recommendations.",</span>
<span class="line-precise" title="No Anys on this line!">            "competitor_analysis": "Detailed competitor analysis report with competitor profiles, strategy analysis, keyword gaps, and competitive positioning recommendations.",</span>
<span class="line-precise" title="No Anys on this line!">            "keyword_research": "Complete keyword research report with primary/secondary keywords, search volumes, competition analysis, cost estimates, and negative keyword recommendations.",</span>
<span class="line-precise" title="No Anys on this line!">            "audience_targeting": "Audience targeting strategy document with detailed persona definitions, targeting parameters, audience segments, and optimization recommendations.",</span>
<span class="line-precise" title="No Anys on this line!">            "campaign_structure_design": "Campaign structure blueprint with hierarchy design, ad group organization, budget allocation, and setup guidelines.",</span>
<span class="line-precise" title="No Anys on this line!">            "ad_copy_creation": "Collection of optimized ad copy variations including headlines, descriptions, and ad extensions with performance predictions.",</span>
<span class="line-precise" title="No Anys on this line!">            "creative_asset_generation": "Set of creative assets including images, videos, graphics, and multimedia content optimized for campaign objectives.",</span>
<span class="line-precise" title="No Anys on this line!">            "landing_page_optimization": "Landing page optimization report with analysis, recommendations, and implementation guidelines for conversion improvement.",</span>
<span class="line-precise" title="No Anys on this line!">            "google_ads_api_integration": "Functional Google Ads API integration with automated campaign management capabilities and documentation.",</span>
<span class="line-precise" title="No Anys on this line!">            "conversion_tracking_setup": "Complete conversion tracking implementation with analytics integration, pixel setup, and attribution configuration.",</span>
<span class="line-precise" title="No Anys on this line!">            "performance_monitoring": "Performance monitoring dashboard with real-time metrics, alerts, and automated reporting system.",</span>
<span class="line-precise" title="No Anys on this line!">            "optimization_recommendations": "Performance optimization report with specific recommendations, expected impact analysis, and implementation priorities.",</span>
<span class="line-precise" title="No Anys on this line!">            "security_assessment": "Security assessment report with compliance verification, risk analysis, and security recommendations.",</span>
<span class="line-precise" title="No Anys on this line!">            "deployment": "Successful campaign deployment with launch confirmation, tracking verification, and go-live report."</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">        return expected_outputs.get(</span>
<span class="line-precise" title="No Anys on this line!">            task_name,</span>
<span class="line-precise" title="No Anys on this line!">            f"Completed {task_name} deliverable with detailed analysis and actionable recommendations."</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def execute_crew(self, crew_id: str) -&gt; str:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Execute a Google Ads crew.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            crew_id: ID of the crew to execute</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            str: Execution ID</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Raises:</span>
<span class="line-empty" title="No Anys on this line!">            AgentError: If crew cannot be executed</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x1)">        if crew_id not in self.crews:</span>
<span class="line-precise" title="No Anys on this line!">            raise AgentError(f"Crew not found: {crew_id}")</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        crew_execution = self.crew_executions[crew_id]</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        crew = self.crews[crew_id]</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        execution_id = str(uuid.uuid4())</span>
<span class="line-precise" title="No Anys on this line!">        crew_execution.started_at = datetime.utcnow()</span>
<span class="line-precise" title="No Anys on this line!">        crew_execution.status = WorkflowStatus.RUNNING</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">            "Starting crew execution",</span>
<span class="line-precise" title="No Anys on this line!">            crew_id=crew_id,</span>
<span class="line-precise" title="No Anys on this line!">            execution_id=execution_id,</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">            workflow_type=crew_execution.metadata.get('workflow_type')</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-empty" title="No Anys on this line!">            # Execute the crew</span>
<span class="line-precise" title="No Anys on this line!">            if self.tracer:</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x3)
Explicit (x1)">                async with self.tracer.trace_operation(</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">                    operation_name=f"crew_execution_{crew_execution.metadata.get('workflow_type')}",</span>
<span class="line-empty" title="No Anys on this line!">                    attributes={</span>
<span class="line-precise" title="No Anys on this line!">                        "crew.id": crew_id,</span>
<span class="line-precise" title="No Anys on this line!">                        "execution.id": execution_id,</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x2)">                        "agents.count": len(crew.agents),</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x2)">                        "tasks.count": len(crew.tasks)</span>
<span class="line-empty" title="No Anys on this line!">                    }</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">                ) as span:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)
Unimported (x3)">                    result = await self._execute_crew_async(crew, crew_execution, span)</span>
<span class="line-empty" title="No Anys on this line!">            else:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)
Unimported (x2)">                result = await self._execute_crew_async(crew, crew_execution)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            crew_execution.output_data = result</span>
<span class="line-precise" title="No Anys on this line!">            crew_execution.status = WorkflowStatus.COMPLETED</span>
<span class="line-precise" title="No Anys on this line!">            crew_execution.completed_at = datetime.utcnow()</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            await self._emit_event("crew_completed", {</span>
<span class="line-precise" title="No Anys on this line!">                "crew_id": crew_id,</span>
<span class="line-precise" title="No Anys on this line!">                "execution_id": execution_id,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                "output_data": result,</span>
<span class="line-precise" title="No Anys on this line!">                "duration": (crew_execution.completed_at - crew_execution.started_at).total_seconds()</span>
<span class="line-empty" title="No Anys on this line!">            })</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Crew execution completed",</span>
<span class="line-precise" title="No Anys on this line!">                crew_id=crew_id,</span>
<span class="line-precise" title="No Anys on this line!">                execution_id=execution_id,</span>
<span class="line-precise" title="No Anys on this line!">                duration=(crew_execution.completed_at - crew_execution.started_at).total_seconds()</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-precise" title="No Anys on this line!">            crew_execution.status = WorkflowStatus.FAILED</span>
<span class="line-precise" title="No Anys on this line!">            crew_execution.completed_at = datetime.utcnow()</span>
<span class="line-precise" title="No Anys on this line!">            crew_execution.error_log.append(f"Crew execution failed: {str(e)}")</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            await self._emit_event("crew_failed", {</span>
<span class="line-precise" title="No Anys on this line!">                "crew_id": crew_id,</span>
<span class="line-precise" title="No Anys on this line!">                "execution_id": execution_id,</span>
<span class="line-precise" title="No Anys on this line!">                "error": str(e)</span>
<span class="line-empty" title="No Anys on this line!">            })</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error(</span>
<span class="line-precise" title="No Anys on this line!">                "Crew execution failed",</span>
<span class="line-precise" title="No Anys on this line!">                crew_id=crew_id,</span>
<span class="line-precise" title="No Anys on this line!">                execution_id=execution_id,</span>
<span class="line-precise" title="No Anys on this line!">                error=str(e)</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        finally:</span>
<span class="line-precise" title="No Anys on this line!">            self.running_crews.discard(crew_id)</span>
<span class="line-empty" title="No Anys on this line!">            # Reduce agent workloads</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">            for agent_id in crew_execution.metadata.get('selected_agents', []):</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                if agent_id in self.agent_workloads:</span>
<span class="line-any" title="Any Types on this line: 
Omitted Generics (x23)
Explicit (x2)">                    self.agent_workloads[agent_id] = max(0, self.agent_workloads[agent_id] - 1)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return execution_id</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)
Explicit (x2)">    async def _execute_crew_async(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        crew: Crew,</span>
<span class="line-empty" title="No Anys on this line!">        crew_execution: CrewExecution,</span>
<span class="line-precise" title="No Anys on this line!">        span: Optional[Any] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; Dict[str, Any]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Execute crew asynchronously.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            crew: CrewAI Crew instance</span>
<span class="line-empty" title="No Anys on this line!">            crew_execution: Crew execution tracking</span>
<span class="line-empty" title="No Anys on this line!">            span: Optional tracing span</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            Crew execution results</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        # Execute the crew using CrewAI</span>
<span class="line-precise" title="No Anys on this line!">        loop = asyncio.get_event_loop()</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x4)
Explicit (x2)">        result = await loop.run_in_executor(</span>
<span class="line-precise" title="No Anys on this line!">            None,</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x2)">            crew.kickoff,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            crew_execution.input_data</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Process and structure the results</span>
<span class="line-precise" title="No Anys on this line!">        structured_result = {</span>
<span class="line-precise" title="No Anys on this line!">            "status": "completed",</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">            "raw_output": str(result),</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x2)">            "tasks_completed": len(crew.tasks),</span>
<span class="line-precise" title="No Anys on this line!">            "execution_time": (datetime.utcnow() - crew_execution.started_at).total_seconds(),</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            "metadata": crew_execution.metadata</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">        if hasattr(result, 'tasks_output'):</span>
<span class="line-precise" title="No Anys on this line!">            structured_result["task_outputs"] = [</span>
<span class="line-empty" title="No Anys on this line!">                {</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x2)">                    "task_description": task.description,</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">                    "output": str(output),</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                    "agent": output.agent if hasattr(output, 'agent') else None</span>
<span class="line-empty" title="No Anys on this line!">                }</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x8)
Explicit (x9)
Omitted Generics (x30)">                for task, output in zip(crew.tasks, result.tasks_output)</span>
<span class="line-empty" title="No Anys on this line!">            ]</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return structured_result</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="No Anys on this line!">    def _crew_step_callback(self, step_output):</span>
<span class="line-empty" title="No Anys on this line!">        """Callback for crew step execution."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x5)">        self.logger.debug("Crew step completed", step_output=str(step_output))</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="No Anys on this line!">    def _crew_task_callback(self, task_output):</span>
<span class="line-empty" title="No Anys on this line!">        """Callback for crew task execution."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x5)">        self.logger.debug("Crew task completed", task_output=str(task_output))</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _find_available_agent(self, agent_type: AgentType) -&gt; Optional[BaseAiLexAgent]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Find an available agent of the specified type.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            agent_type: Required agent type</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            Available agent or None</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        if agent_type not in self.agent_pools:</span>
<span class="line-precise" title="No Anys on this line!">            return None</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Find agent with lowest workload</span>
<span class="line-precise" title="No Anys on this line!">        best_agent_id = None</span>
<span class="line-precise" title="No Anys on this line!">        min_workload = float('inf')</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        for agent_id in self.agent_pools[agent_type]:</span>
<span class="line-precise" title="No Anys on this line!">            agent = self.agents[agent_id]</span>
<span class="line-precise" title="No Anys on this line!">            workload = self.agent_workloads[agent_id]</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            if (agent.status in [AgentStatus.IDLE, AgentStatus.ACTIVE] and </span>
<span class="line-precise" title="No Anys on this line!">                workload &lt; min_workload):</span>
<span class="line-precise" title="No Anys on this line!">                best_agent_id = agent_id</span>
<span class="line-precise" title="No Anys on this line!">                min_workload = workload</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        if best_agent_id:</span>
<span class="line-precise" title="No Anys on this line!">            return self.agents[best_agent_id]</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return None</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def get_crew_status(self, crew_id: str) -&gt; Optional[CrewExecution]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Get crew execution status.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            crew_id: Crew ID</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            Crew execution status or None if not found</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">        return self.crew_executions.get(crew_id)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def cancel_crew(self, crew_id: str) -&gt; bool:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Cancel crew execution.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            crew_id: Crew ID</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            True if cancelled successfully</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        if crew_id not in self.crew_executions:</span>
<span class="line-precise" title="No Anys on this line!">            return False</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        crew_execution = self.crew_executions[crew_id]</span>
<span class="line-precise" title="No Anys on this line!">        crew_execution.status = WorkflowStatus.CANCELLED</span>
<span class="line-precise" title="No Anys on this line!">        crew_execution.completed_at = datetime.utcnow()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        self.running_crews.discard(crew_id)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Reduce agent workloads</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x7)
Omitted Generics (x2)">        for agent_id in crew_execution.metadata.get('selected_agents', []):</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">            if agent_id in self.agent_workloads:</span>
<span class="line-any" title="Any Types on this line: 
Omitted Generics (x23)
Explicit (x2)">                self.agent_workloads[agent_id] = max(0, self.agent_workloads[agent_id] - 1)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">        await self._emit_event("crew_cancelled", {</span>
<span class="line-precise" title="No Anys on this line!">            "crew_id": crew_id</span>
<span class="line-empty" title="No Anys on this line!">        })</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.logger.info("Crew execution cancelled", crew_id=crew_id)</span>
<span class="line-precise" title="No Anys on this line!">        return True</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _emit_event(self, event_type: str, data: Dict[str, Any]) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Emit event to registered callbacks.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            event_type: Event type</span>
<span class="line-empty" title="No Anys on this line!">            data: Event data</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="Any Types on this line: 
Error (x9)">        for callback in self.event_callbacks[event_type]:</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x5)
Omitted Generics (x5)
Error (x3)">                if asyncio.iscoroutinefunction(callback):</span>
<span class="line-any" title="Any Types on this line: 
Error (x4)
Explicit (x1)">                    await callback(event_type, data)</span>
<span class="line-empty" title="No Anys on this line!">                else:</span>
<span class="line-any" title="Any Types on this line: 
Error (x4)
Explicit (x1)">                    callback(event_type, data)</span>
<span class="line-precise" title="No Anys on this line!">            except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.error("Event callback failed", event_type=event_type, error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    def get_crew_metrics(self) -&gt; Dict[str, Any]:</span>
<span class="line-empty" title="No Anys on this line!">        """Get crew orchestrator metrics."""</span>
<span class="line-precise" title="No Anys on this line!">        completed_crews = [</span>
<span class="line-precise" title="No Anys on this line!">            e for e in self.crew_executions.values()</span>
<span class="line-precise" title="No Anys on this line!">            if e.status == WorkflowStatus.COMPLETED</span>
<span class="line-empty" title="No Anys on this line!">        ]</span>
<span class="line-precise" title="No Anys on this line!">        failed_crews = [</span>
<span class="line-precise" title="No Anys on this line!">            e for e in self.crew_executions.values()</span>
<span class="line-precise" title="No Anys on this line!">            if e.status == WorkflowStatus.FAILED</span>
<span class="line-empty" title="No Anys on this line!">        ]</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        return {</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x1)">            "total_crews": len(self.crews),</span>
<span class="line-precise" title="No Anys on this line!">            "total_agents": len(self.agents),</span>
<span class="line-precise" title="No Anys on this line!">            "total_executions": len(self.crew_executions),</span>
<span class="line-precise" title="No Anys on this line!">            "running_crews": len(self.running_crews),</span>
<span class="line-precise" title="No Anys on this line!">            "completed_crews": len(completed_crews),</span>
<span class="line-precise" title="No Anys on this line!">            "failed_crews": len(failed_crews),</span>
<span class="line-precise" title="No Anys on this line!">            "success_rate": (</span>
<span class="line-precise" title="No Anys on this line!">                len(completed_crews) / len(self.crew_executions)</span>
<span class="line-precise" title="No Anys on this line!">                if self.crew_executions else 0</span>
<span class="line-empty" title="No Anys on this line!">            ),</span>
<span class="line-precise" title="No Anys on this line!">            "agent_pools": {</span>
<span class="line-precise" title="No Anys on this line!">                agent_type.value: len(agents)</span>
<span class="line-precise" title="No Anys on this line!">                for agent_type, agents in self.agent_pools.items()</span>
<span class="line-empty" title="No Anys on this line!">            },</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)
Omitted Generics (x18)">            "agent_workloads": dict(self.agent_workloads)</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class FlowOrchestrator:</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Main orchestrator for agent workflows and coordination.</span>
<span class="line-empty" title="No Anys on this line!">    Manages workflow execution, agent coordination, and resource allocation.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __init__(self, tracer: Optional[PhoenixTracer] = None):</span>
<span class="line-precise" title="No Anys on this line!">        self.tracer = tracer</span>
<span class="line-precise" title="No Anys on this line!">        self.workflows: Dict[str, WorkflowDefinition] = {}</span>
<span class="line-precise" title="No Anys on this line!">        self.executions: Dict[str, WorkflowExecution] = {}</span>
<span class="line-precise" title="No Anys on this line!">        self.agents: Dict[str, BaseAiLexAgent] = {}</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x3)">        self.running_executions: Set[str] = set()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # CrewAI integration</span>
<span class="line-precise" title="No Anys on this line!">        self.crew_orchestrator = CrewOrchestrator(tracer)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Resource management</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x31)">        self.agent_pools: Dict[AgentType, List[str]] = defaultdict(list)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x28)">        self.agent_workloads: Dict[str, int] = defaultdict(int)</span>
<span class="line-precise" title="No Anys on this line!">        self.max_concurrent_executions = 10</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Event system</span>
<span class="line-precise" title="Any Types on this line: 
Error (x6)
Omitted Generics (x31)">        self.event_callbacks: Dict[str, List[Callable]] = defaultdict(list)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x6)">        self.logger = structlog.get_logger().bind(component="flow_orchestrator")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def register_workflow(self, workflow: WorkflowDefinition) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Register a workflow definition.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            workflow: Workflow definition to register</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        self.workflows[workflow.id] = workflow</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">            "Workflow registered",</span>
<span class="line-precise" title="No Anys on this line!">            workflow_id=workflow.id,</span>
<span class="line-precise" title="No Anys on this line!">            workflow_name=workflow.name,</span>
<span class="line-precise" title="No Anys on this line!">            steps_count=len(workflow.steps)</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def register_agent(self, agent: BaseAiLexAgent) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Register an agent with the orchestrator.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            agent: Agent instance to register</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        self.agents[agent.agent_id] = agent</span>
<span class="line-precise" title="No Anys on this line!">        self.agent_pools[agent.agent_type].append(agent.agent_id)</span>
<span class="line-precise" title="No Anys on this line!">        self.agent_workloads[agent.agent_id] = 0</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Also register with crew orchestrator</span>
<span class="line-precise" title="No Anys on this line!">        self.crew_orchestrator.register_agent(agent)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">            "Agent registered",</span>
<span class="line-precise" title="No Anys on this line!">            agent_id=agent.agent_id,</span>
<span class="line-precise" title="No Anys on this line!">            agent_name=agent.name,</span>
<span class="line-precise" title="No Anys on this line!">            agent_type=agent.agent_type.value</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def execute_workflow(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        workflow_id: str,</span>
<span class="line-empty" title="No Anys on this line!">        input_data: Dict[str, Any],</span>
<span class="line-precise" title="No Anys on this line!">        context: Optional[AgentContext] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; str:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Start workflow execution.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            workflow_id: ID of the workflow to execute</span>
<span class="line-empty" title="No Anys on this line!">            input_data: Input data for the workflow</span>
<span class="line-empty" title="No Anys on this line!">            context: Execution context</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            str: Execution ID</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Raises:</span>
<span class="line-empty" title="No Anys on this line!">            AgentError: If workflow cannot be started</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        if workflow_id not in self.workflows:</span>
<span class="line-precise" title="No Anys on this line!">            raise AgentError(f"Workflow not found: {workflow_id}")</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        if len(self.running_executions) &gt;= self.max_concurrent_executions:</span>
<span class="line-precise" title="No Anys on this line!">            raise AgentError("Maximum concurrent executions reached")</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        workflow = self.workflows[workflow_id]</span>
<span class="line-precise" title="No Anys on this line!">        execution_id = str(uuid.uuid4())</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">        execution = WorkflowExecution(</span>
<span class="line-precise" title="No Anys on this line!">            id=execution_id,</span>
<span class="line-precise" title="No Anys on this line!">            workflow_id=workflow_id,</span>
<span class="line-precise" title="No Anys on this line!">            status=WorkflowStatus.PENDING,</span>
<span class="line-precise" title="No Anys on this line!">            created_at=datetime.utcnow(),</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x1)">            context=context or AgentContext(),</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            input_data=input_data</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        self.executions[execution_id] = execution</span>
<span class="line-precise" title="No Anys on this line!">        self.running_executions.add(execution_id)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">            "Workflow execution started",</span>
<span class="line-precise" title="No Anys on this line!">            execution_id=execution_id,</span>
<span class="line-precise" title="No Anys on this line!">            workflow_id=workflow_id,</span>
<span class="line-precise" title="No Anys on this line!">            workflow_name=workflow.name</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Start execution in background</span>
<span class="line-precise" title="No Anys on this line!">        asyncio.create_task(self._execute_workflow_async(execution_id))</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">        await self._emit_event("workflow_started", {</span>
<span class="line-precise" title="No Anys on this line!">            "execution_id": execution_id,</span>
<span class="line-precise" title="No Anys on this line!">            "workflow_id": workflow_id,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            "input_data": input_data</span>
<span class="line-empty" title="No Anys on this line!">        })</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return execution_id</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def execute_google_ads_crew(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        workflow_type: str,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_config: Dict[str, Any],</span>
<span class="line-precise" title="No Anys on this line!">        required_agents: Optional[List[AgentType]] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; str:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Execute a Google Ads workflow using CrewAI coordination.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            workflow_type: Type of Google Ads workflow</span>
<span class="line-empty" title="No Anys on this line!">            campaign_config: Campaign configuration data</span>
<span class="line-empty" title="No Anys on this line!">            required_agents: Specific agents required for this workflow</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            str: Crew execution ID</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        # Create the crew</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">        crew_id = await self.crew_orchestrator.create_google_ads_crew(</span>
<span class="line-precise" title="No Anys on this line!">            workflow_type=workflow_type,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            campaign_config=campaign_config,</span>
<span class="line-precise" title="No Anys on this line!">            required_agents=required_agents</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Execute the crew</span>
<span class="line-precise" title="No Anys on this line!">        execution_id = await self.crew_orchestrator.execute_crew(crew_id)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Create workflow execution tracking that references the crew</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">        workflow_execution = WorkflowExecution(</span>
<span class="line-precise" title="No Anys on this line!">            id=execution_id,</span>
<span class="line-precise" title="No Anys on this line!">            workflow_id=workflow_type,</span>
<span class="line-precise" title="No Anys on this line!">            status=WorkflowStatus.RUNNING,</span>
<span class="line-precise" title="No Anys on this line!">            created_at=datetime.utcnow(),</span>
<span class="line-precise" title="No Anys on this line!">            started_at=datetime.utcnow(),</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            input_data=campaign_config,</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">            crew_execution=self.crew_orchestrator.crew_executions.get(crew_id)</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        self.executions[execution_id] = workflow_execution</span>
<span class="line-precise" title="No Anys on this line!">        self.running_executions.add(execution_id)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return execution_id</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def get_google_ads_crew_status(self, execution_id: str) -&gt; Optional[Dict[str, Any]]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Get status of a Google Ads crew execution.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            execution_id: Execution ID</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            Crew status information</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">        execution = self.executions.get(execution_id)</span>
<span class="line-precise" title="No Anys on this line!">        if not execution or not execution.crew_execution:</span>
<span class="line-precise" title="No Anys on this line!">            return None</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        crew_execution = execution.crew_execution</span>
<span class="line-empty" title="No Anys on this line!">        return {</span>
<span class="line-precise" title="No Anys on this line!">            "execution_id": execution_id,</span>
<span class="line-precise" title="No Anys on this line!">            "crew_id": crew_execution.crew_id,</span>
<span class="line-precise" title="No Anys on this line!">            "status": crew_execution.status.value,</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">            "workflow_type": crew_execution.metadata.get("workflow_type"),</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">            "agents": crew_execution.metadata.get("selected_agents", []),</span>
<span class="line-precise" title="No Anys on this line!">            "started_at": crew_execution.started_at.isoformat() if crew_execution.started_at else None,</span>
<span class="line-precise" title="No Anys on this line!">            "completed_at": crew_execution.completed_at.isoformat() if crew_execution.completed_at else None,</span>
<span class="line-precise" title="No Anys on this line!">            "progress": {</span>
<span class="line-precise" title="No Anys on this line!">                "completed_tasks": len(crew_execution.completed_tasks),</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                "total_tasks": len(crew_execution.task_results),</span>
<span class="line-precise" title="No Anys on this line!">                "current_task": crew_execution.current_task,</span>
<span class="line-precise" title="No Anys on this line!">                "failed_tasks": len(crew_execution.failed_tasks)</span>
<span class="line-empty" title="No Anys on this line!">            },</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            "output_data": crew_execution.output_data,</span>
<span class="line-precise" title="No Anys on this line!">            "errors": crew_execution.error_log</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def cancel_google_ads_crew(self, execution_id: str) -&gt; bool:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Cancel a Google Ads crew execution.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            execution_id: Execution ID</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            True if cancelled successfully</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">        execution = self.executions.get(execution_id)</span>
<span class="line-precise" title="No Anys on this line!">        if not execution or not execution.crew_execution:</span>
<span class="line-precise" title="No Anys on this line!">            return False</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Cancel the crew</span>
<span class="line-precise" title="No Anys on this line!">        success = await self.crew_orchestrator.cancel_crew(execution.crew_execution.crew_id)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        if success:</span>
<span class="line-precise" title="No Anys on this line!">            execution.status = WorkflowStatus.CANCELLED</span>
<span class="line-precise" title="No Anys on this line!">            execution.completed_at = datetime.utcnow()</span>
<span class="line-precise" title="No Anys on this line!">            self.running_executions.discard(execution_id)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return success</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _execute_workflow_async(self, execution_id: str) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Execute workflow asynchronously.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            execution_id: Execution ID</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        execution = self.executions[execution_id]</span>
<span class="line-precise" title="No Anys on this line!">        workflow = self.workflows[execution.workflow_id]</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            execution.status = WorkflowStatus.RUNNING</span>
<span class="line-precise" title="No Anys on this line!">            execution.started_at = datetime.utcnow()</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            if self.tracer:</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x3)
Explicit (x1)">                async with self.tracer.trace_operation(</span>
<span class="line-precise" title="No Anys on this line!">                    operation_name=f"workflow_execution_{workflow.name}",</span>
<span class="line-empty" title="No Anys on this line!">                    attributes={</span>
<span class="line-precise" title="No Anys on this line!">                        "workflow.id": workflow.id,</span>
<span class="line-precise" title="No Anys on this line!">                        "workflow.name": workflow.name,</span>
<span class="line-precise" title="No Anys on this line!">                        "execution.id": execution_id,</span>
<span class="line-precise" title="No Anys on this line!">                        "steps.count": len(workflow.steps)</span>
<span class="line-empty" title="No Anys on this line!">                    }</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">                ) as span:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unimported (x1)">                    await self._execute_workflow_steps(execution, workflow, span)</span>
<span class="line-empty" title="No Anys on this line!">            else:</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x1)">                await self._execute_workflow_steps(execution, workflow)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            execution.status = WorkflowStatus.COMPLETED</span>
<span class="line-precise" title="No Anys on this line!">            execution.completed_at = datetime.utcnow()</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            await self._emit_event("workflow_completed", {</span>
<span class="line-precise" title="No Anys on this line!">                "execution_id": execution_id,</span>
<span class="line-precise" title="No Anys on this line!">                "workflow_id": workflow.id,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                "output_data": execution.output_data,</span>
<span class="line-precise" title="No Anys on this line!">                "duration": (execution.completed_at - execution.started_at).total_seconds()</span>
<span class="line-empty" title="No Anys on this line!">            })</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Workflow execution completed",</span>
<span class="line-precise" title="No Anys on this line!">                execution_id=execution_id,</span>
<span class="line-precise" title="No Anys on this line!">                workflow_id=workflow.id,</span>
<span class="line-precise" title="No Anys on this line!">                duration=(execution.completed_at - execution.started_at).total_seconds()</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-precise" title="No Anys on this line!">            execution.status = WorkflowStatus.FAILED</span>
<span class="line-precise" title="No Anys on this line!">            execution.completed_at = datetime.utcnow()</span>
<span class="line-precise" title="No Anys on this line!">            execution.error_log.append(f"Workflow failed: {str(e)}")</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            await self._emit_event("workflow_failed", {</span>
<span class="line-precise" title="No Anys on this line!">                "execution_id": execution_id,</span>
<span class="line-precise" title="No Anys on this line!">                "workflow_id": workflow.id,</span>
<span class="line-precise" title="No Anys on this line!">                "error": str(e)</span>
<span class="line-empty" title="No Anys on this line!">            })</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error(</span>
<span class="line-precise" title="No Anys on this line!">                "Workflow execution failed",</span>
<span class="line-precise" title="No Anys on this line!">                execution_id=execution_id,</span>
<span class="line-precise" title="No Anys on this line!">                workflow_id=workflow.id,</span>
<span class="line-precise" title="No Anys on this line!">                error=str(e)</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        finally:</span>
<span class="line-precise" title="No Anys on this line!">            self.running_executions.discard(execution_id)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _execute_workflow_steps(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        execution: WorkflowExecution,</span>
<span class="line-empty" title="No Anys on this line!">        workflow: WorkflowDefinition,</span>
<span class="line-precise" title="No Anys on this line!">        span: Optional[Any] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Execute workflow steps based on strategy.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            execution: Workflow execution instance</span>
<span class="line-empty" title="No Anys on this line!">            workflow: Workflow definition</span>
<span class="line-empty" title="No Anys on this line!">            span: Optional tracing span</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        if workflow.strategy == ExecutionStrategy.SEQUENTIAL:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            await self._execute_sequential(execution, workflow, span)</span>
<span class="line-precise" title="No Anys on this line!">        elif workflow.strategy == ExecutionStrategy.PARALLEL:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            await self._execute_parallel(execution, workflow, span)</span>
<span class="line-precise" title="No Anys on this line!">        elif workflow.strategy == ExecutionStrategy.CONDITIONAL:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            await self._execute_conditional(execution, workflow, span)</span>
<span class="line-precise" title="No Anys on this line!">        elif workflow.strategy == ExecutionStrategy.PIPELINE:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            await self._execute_pipeline(execution, workflow, span)</span>
<span class="line-precise" title="No Anys on this line!">        elif workflow.strategy == ExecutionStrategy.HYBRID:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            await self._execute_hybrid(execution, workflow, span)</span>
<span class="line-empty" title="No Anys on this line!">        else:</span>
<span class="line-unanalyzed" title="No Anys on this line!">            raise AgentError(f"Unsupported execution strategy: {workflow.strategy}")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _execute_sequential(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        execution: WorkflowExecution,</span>
<span class="line-empty" title="No Anys on this line!">        workflow: WorkflowDefinition,</span>
<span class="line-precise" title="No Anys on this line!">        span: Optional[Any] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Execute steps sequentially."""</span>
<span class="line-precise" title="No Anys on this line!">        for step in workflow.steps:</span>
<span class="line-precise" title="No Anys on this line!">            if execution.status != WorkflowStatus.RUNNING:</span>
<span class="line-precise" title="No Anys on this line!">                break</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            await self._execute_step(execution, step, span)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            if step.id in execution.failed_steps and workflow.error_handling == "stop":</span>
<span class="line-precise" title="No Anys on this line!">                raise AgentError(f"Step {step.id} failed and error handling is set to stop")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _execute_parallel(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        execution: WorkflowExecution,</span>
<span class="line-empty" title="No Anys on this line!">        workflow: WorkflowDefinition,</span>
<span class="line-precise" title="No Anys on this line!">        span: Optional[Any] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Execute steps in parallel."""</span>
<span class="line-precise" title="No Anys on this line!">        tasks = []</span>
<span class="line-precise" title="No Anys on this line!">        for step in workflow.steps:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            task = asyncio.create_task(self._execute_step(execution, step, span))</span>
<span class="line-precise" title="No Anys on this line!">            tasks.append(task)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Wait for all tasks to complete</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x176)">        await asyncio.gather(*tasks, return_exceptions=True)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _execute_conditional(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        execution: WorkflowExecution,</span>
<span class="line-empty" title="No Anys on this line!">        workflow: WorkflowDefinition,</span>
<span class="line-precise" title="No Anys on this line!">        span: Optional[Any] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Execute steps based on conditions."""</span>
<span class="line-precise" title="No Anys on this line!">        for step in workflow.steps:</span>
<span class="line-precise" title="No Anys on this line!">            if execution.status != WorkflowStatus.RUNNING:</span>
<span class="line-precise" title="No Anys on this line!">                break</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Check step conditions</span>
<span class="line-precise" title="No Anys on this line!">            if await self._check_step_conditions(execution, step):</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">                await self._execute_step(execution, step, span)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _execute_pipeline(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        execution: WorkflowExecution,</span>
<span class="line-empty" title="No Anys on this line!">        workflow: WorkflowDefinition,</span>
<span class="line-precise" title="No Anys on this line!">        span: Optional[Any] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Execute steps in pipeline mode with data flow."""</span>
<span class="line-empty" title="No Anys on this line!">        # Build dependency graph</span>
<span class="line-precise" title="No Anys on this line!">        dependency_graph = self._build_dependency_graph(workflow.steps)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Execute in topological order</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x3)">        executed = set()</span>
<span class="line-precise" title="No Anys on this line!">        while len(executed) &lt; len(workflow.steps):</span>
<span class="line-precise" title="No Anys on this line!">            ready_steps = [</span>
<span class="line-precise" title="No Anys on this line!">                step for step in workflow.steps</span>
<span class="line-precise" title="No Anys on this line!">                if step.id not in executed and all(dep in executed for dep in step.dependencies)</span>
<span class="line-empty" title="No Anys on this line!">            ]</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            if not ready_steps:</span>
<span class="line-precise" title="No Anys on this line!">                break</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Execute ready steps in parallel</span>
<span class="line-precise" title="No Anys on this line!">            tasks = []</span>
<span class="line-precise" title="No Anys on this line!">            for step in ready_steps:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">                task = asyncio.create_task(self._execute_step(execution, step, span))</span>
<span class="line-precise" title="No Anys on this line!">                tasks.append((step.id, task))</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Wait for completion</span>
<span class="line-precise" title="No Anys on this line!">            for step_id, task in tasks:</span>
<span class="line-precise" title="No Anys on this line!">                await task</span>
<span class="line-precise" title="No Anys on this line!">                executed.add(step_id)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _execute_hybrid(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        execution: WorkflowExecution,</span>
<span class="line-empty" title="No Anys on this line!">        workflow: WorkflowDefinition,</span>
<span class="line-precise" title="No Anys on this line!">        span: Optional[Any] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Execute with hybrid strategy combining other strategies."""</span>
<span class="line-empty" title="No Anys on this line!">        # For now, use pipeline strategy as default hybrid approach</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">        await self._execute_pipeline(execution, workflow, span)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _execute_step(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        execution: WorkflowExecution,</span>
<span class="line-empty" title="No Anys on this line!">        step: WorkflowStep,</span>
<span class="line-precise" title="No Anys on this line!">        span: Optional[Any] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Execute a single workflow step.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            execution: Workflow execution</span>
<span class="line-empty" title="No Anys on this line!">            step: Step to execute</span>
<span class="line-empty" title="No Anys on this line!">            span: Optional tracing span</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        step_start_time = datetime.utcnow()</span>
<span class="line-precise" title="No Anys on this line!">        execution.current_step = step.id</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Executing workflow step",</span>
<span class="line-precise" title="No Anys on this line!">                execution_id=execution.id,</span>
<span class="line-precise" title="No Anys on this line!">                step_id=step.id,</span>
<span class="line-precise" title="No Anys on this line!">                step_name=step.name,</span>
<span class="line-precise" title="No Anys on this line!">                agent_type=step.agent_type.value</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Find available agent</span>
<span class="line-precise" title="No Anys on this line!">            agent = await self._find_available_agent(step.agent_type)</span>
<span class="line-precise" title="No Anys on this line!">            if not agent:</span>
<span class="line-precise" title="No Anys on this line!">                raise AgentError(f"No available agent for type: {step.agent_type}")</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Prepare step input data</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x3)">            step_input = await self._prepare_step_input(execution, step)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Create task</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            task = Task(</span>
<span class="line-precise" title="No Anys on this line!">                description=step.description,</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">                expected_output=step.output_schema.get("description", "Task output"),</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">                agent=agent._crew_agent if hasattr(agent, '_crew_agent') else None,</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Execute step with timeout</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x6)">                step_result = await asyncio.wait_for(</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)
Unimported (x2)">                    agent.execute_task(task, execution.context),</span>
<span class="line-precise" title="No Anys on this line!">                    timeout=step.timeout_seconds</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">                execution.step_results[step.id] = step_result</span>
<span class="line-precise" title="No Anys on this line!">                execution.completed_steps.append(step.id)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Update agent workload</span>
<span class="line-precise" title="No Anys on this line!">                self.agent_workloads[agent.agent_id] -= 1</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">                step_duration = (datetime.utcnow() - step_start_time).total_seconds()</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                    "Workflow step completed",</span>
<span class="line-precise" title="No Anys on this line!">                    execution_id=execution.id,</span>
<span class="line-precise" title="No Anys on this line!">                    step_id=step.id,</span>
<span class="line-precise" title="No Anys on this line!">                    agent_id=agent.agent_id,</span>
<span class="line-precise" title="No Anys on this line!">                    duration=step_duration</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                if span:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                    span.add_event(f"step_completed_{step.id}", {</span>
<span class="line-precise" title="No Anys on this line!">                        "step.name": step.name,</span>
<span class="line-precise" title="No Anys on this line!">                        "step.duration": step_duration,</span>
<span class="line-precise" title="No Anys on this line!">                        "agent.id": agent.agent_id</span>
<span class="line-empty" title="No Anys on this line!">                    })</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">            except asyncio.TimeoutError:</span>
<span class="line-precise" title="No Anys on this line!">                raise AgentError(f"Step {step.id} timed out after {step.timeout_seconds} seconds")</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-precise" title="No Anys on this line!">            execution.failed_steps.append(step.id)</span>
<span class="line-precise" title="No Anys on this line!">            execution.error_log.append(f"Step {step.id} failed: {str(e)}")</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Handle retries</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">            retry_count = execution.retry_counts.get(step.id, 0)</span>
<span class="line-precise" title="No Anys on this line!">            if retry_count &lt; step.retry_attempts:</span>
<span class="line-precise" title="No Anys on this line!">                execution.retry_counts[step.id] = retry_count + 1</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">                    "Retrying failed step",</span>
<span class="line-precise" title="No Anys on this line!">                    execution_id=execution.id,</span>
<span class="line-precise" title="No Anys on this line!">                    step_id=step.id,</span>
<span class="line-precise" title="No Anys on this line!">                    retry_count=retry_count + 1,</span>
<span class="line-precise" title="No Anys on this line!">                    max_retries=step.retry_attempts</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-any" title="Any Types on this line: 
Omitted Generics (x2)
Explicit (x1)">                await asyncio.sleep(2 ** retry_count)  # Exponential backoff</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">                await self._execute_step(execution, step, span)</span>
<span class="line-empty" title="No Anys on this line!">            else:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.error(</span>
<span class="line-precise" title="No Anys on this line!">                    "Workflow step failed after retries",</span>
<span class="line-precise" title="No Anys on this line!">                    execution_id=execution.id,</span>
<span class="line-precise" title="No Anys on this line!">                    step_id=step.id,</span>
<span class="line-precise" title="No Anys on this line!">                    error=str(e),</span>
<span class="line-precise" title="No Anys on this line!">                    retry_count=retry_count</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                raise</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        finally:</span>
<span class="line-precise" title="No Anys on this line!">            execution.current_step = None</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _find_available_agent(self, agent_type: AgentType) -&gt; Optional[BaseAiLexAgent]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Find an available agent of the specified type.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            agent_type: Required agent type</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            Available agent or None</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        if agent_type not in self.agent_pools:</span>
<span class="line-precise" title="No Anys on this line!">            return None</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Find agent with lowest workload</span>
<span class="line-precise" title="No Anys on this line!">        best_agent_id = None</span>
<span class="line-precise" title="No Anys on this line!">        min_workload = float('inf')</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        for agent_id in self.agent_pools[agent_type]:</span>
<span class="line-precise" title="No Anys on this line!">            agent = self.agents[agent_id]</span>
<span class="line-precise" title="No Anys on this line!">            workload = self.agent_workloads[agent_id]</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            if (agent.status in [AgentStatus.IDLE, AgentStatus.ACTIVE] and </span>
<span class="line-precise" title="No Anys on this line!">                workload &lt; min_workload):</span>
<span class="line-precise" title="No Anys on this line!">                best_agent_id = agent_id</span>
<span class="line-precise" title="No Anys on this line!">                min_workload = workload</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        if best_agent_id:</span>
<span class="line-precise" title="No Anys on this line!">            self.agent_workloads[best_agent_id] += 1</span>
<span class="line-precise" title="No Anys on this line!">            return self.agents[best_agent_id]</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return None</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _prepare_step_input(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        execution: WorkflowExecution,</span>
<span class="line-empty" title="No Anys on this line!">        step: WorkflowStep</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; Dict[str, Any]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Prepare input data for a workflow step.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            execution: Workflow execution</span>
<span class="line-empty" title="No Anys on this line!">            step: Workflow step</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            Input data for the step</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">        step_input = execution.input_data.copy()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Add results from dependency steps</span>
<span class="line-precise" title="No Anys on this line!">        for dep_step_id in step.dependencies:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            if dep_step_id in execution.step_results:</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x3)">                step_input[f"{dep_step_id}_result"] = execution.step_results[dep_step_id]</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Add metadata</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">        step_input["_step_metadata"] = {</span>
<span class="line-precise" title="No Anys on this line!">            "step_id": step.id,</span>
<span class="line-precise" title="No Anys on this line!">            "step_name": step.name,</span>
<span class="line-precise" title="No Anys on this line!">            "execution_id": execution.id,</span>
<span class="line-precise" title="No Anys on this line!">            "workflow_id": execution.workflow_id</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">        return step_input</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _check_step_conditions(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        execution: WorkflowExecution,</span>
<span class="line-empty" title="No Anys on this line!">        step: WorkflowStep</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; bool:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Check if step conditions are met.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            execution: Workflow execution</span>
<span class="line-empty" title="No Anys on this line!">            step: Workflow step</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            True if conditions are met</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        if not step.conditions:</span>
<span class="line-precise" title="No Anys on this line!">            return True</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Simple condition evaluation (can be extended)</span>
<span class="line-precise" title="No Anys on this line!">        for condition in step.conditions:</span>
<span class="line-precise" title="No Anys on this line!">            if not await self._evaluate_condition(execution, condition):</span>
<span class="line-precise" title="No Anys on this line!">                return False</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return True</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _evaluate_condition(self, execution: WorkflowExecution, condition: str) -&gt; bool:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Evaluate a single condition.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            execution: Workflow execution</span>
<span class="line-empty" title="No Anys on this line!">            condition: Condition string</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            True if condition is met</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        # Simple condition evaluation - can be extended with a proper expression parser</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-empty" title="No Anys on this line!">            # Example: "step1_result.success == true"</span>
<span class="line-empty" title="No Anys on this line!">            # For now, just check if referenced steps completed successfully</span>
<span class="line-precise" title="No Anys on this line!">            if "completed" in condition.lower():</span>
<span class="line-precise" title="No Anys on this line!">                step_id = condition.split(".")[0].replace("_result", "")</span>
<span class="line-precise" title="No Anys on this line!">                return step_id in execution.completed_steps</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            return True</span>
<span class="line-precise" title="No Anys on this line!">        except Exception:</span>
<span class="line-precise" title="No Anys on this line!">            return False</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _build_dependency_graph(self, steps: List[WorkflowStep]) -&gt; Dict[str, List[str]]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Build dependency graph for steps.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            steps: List of workflow steps</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            Dependency graph</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        graph = {}</span>
<span class="line-precise" title="No Anys on this line!">        for step in steps:</span>
<span class="line-precise" title="No Anys on this line!">            graph[step.id] = step.dependencies.copy()</span>
<span class="line-precise" title="No Anys on this line!">        return graph</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def get_execution_status(self, execution_id: str) -&gt; Optional[WorkflowExecution]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Get workflow execution status.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            execution_id: Execution ID</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            Execution status or None if not found</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">        return self.executions.get(execution_id)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def cancel_execution(self, execution_id: str) -&gt; bool:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Cancel workflow execution.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            execution_id: Execution ID</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            True if cancelled successfully</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        if execution_id not in self.executions:</span>
<span class="line-precise" title="No Anys on this line!">            return False</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        execution = self.executions[execution_id]</span>
<span class="line-precise" title="No Anys on this line!">        execution.status = WorkflowStatus.CANCELLED</span>
<span class="line-precise" title="No Anys on this line!">        execution.completed_at = datetime.utcnow()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        self.running_executions.discard(execution_id)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">        await self._emit_event("workflow_cancelled", {</span>
<span class="line-precise" title="No Anys on this line!">            "execution_id": execution_id,</span>
<span class="line-precise" title="No Anys on this line!">            "workflow_id": execution.workflow_id</span>
<span class="line-empty" title="No Anys on this line!">        })</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.logger.info("Workflow execution cancelled", execution_id=execution_id)</span>
<span class="line-precise" title="No Anys on this line!">        return True</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def pause_execution(self, execution_id: str) -&gt; bool:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Pause workflow execution.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            execution_id: Execution ID</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            True if paused successfully</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        if execution_id not in self.executions:</span>
<span class="line-precise" title="No Anys on this line!">            return False</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        execution = self.executions[execution_id]</span>
<span class="line-precise" title="No Anys on this line!">        if execution.status == WorkflowStatus.RUNNING:</span>
<span class="line-precise" title="No Anys on this line!">            execution.status = WorkflowStatus.PAUSED</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            await self._emit_event("workflow_paused", {</span>
<span class="line-precise" title="No Anys on this line!">                "execution_id": execution_id,</span>
<span class="line-precise" title="No Anys on this line!">                "workflow_id": execution.workflow_id</span>
<span class="line-empty" title="No Anys on this line!">            })</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info("Workflow execution paused", execution_id=execution_id)</span>
<span class="line-precise" title="No Anys on this line!">            return True</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return False</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def resume_execution(self, execution_id: str) -&gt; bool:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Resume paused workflow execution.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            execution_id: Execution ID</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            True if resumed successfully</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        if execution_id not in self.executions:</span>
<span class="line-precise" title="No Anys on this line!">            return False</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        execution = self.executions[execution_id]</span>
<span class="line-precise" title="No Anys on this line!">        if execution.status == WorkflowStatus.PAUSED:</span>
<span class="line-precise" title="No Anys on this line!">            execution.status = WorkflowStatus.RUNNING</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            await self._emit_event("workflow_resumed", {</span>
<span class="line-precise" title="No Anys on this line!">                "execution_id": execution_id,</span>
<span class="line-precise" title="No Anys on this line!">                "workflow_id": execution.workflow_id</span>
<span class="line-empty" title="No Anys on this line!">            })</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info("Workflow execution resumed", execution_id=execution_id)</span>
<span class="line-precise" title="No Anys on this line!">            return True</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return False</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="Any Types on this line: 
Error (x3)">    def add_event_callback(self, event_type: str, callback: Callable) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Add event callback.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            event_type: Event type to listen for</span>
<span class="line-empty" title="No Anys on this line!">            callback: Callback function</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x12)">        self.event_callbacks[event_type].append(callback)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def _emit_event(self, event_type: str, data: Dict[str, Any]) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Emit event to registered callbacks.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            event_type: Event type</span>
<span class="line-empty" title="No Anys on this line!">            data: Event data</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="Any Types on this line: 
Error (x9)">        for callback in self.event_callbacks[event_type]:</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x5)
Omitted Generics (x5)
Error (x3)">                if asyncio.iscoroutinefunction(callback):</span>
<span class="line-any" title="Any Types on this line: 
Error (x4)
Explicit (x1)">                    await callback(event_type, data)</span>
<span class="line-empty" title="No Anys on this line!">                else:</span>
<span class="line-any" title="Any Types on this line: 
Error (x4)
Explicit (x1)">                    callback(event_type, data)</span>
<span class="line-precise" title="No Anys on this line!">            except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.error("Event callback failed", event_type=event_type, error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    def get_metrics(self) -&gt; Dict[str, Any]:</span>
<span class="line-empty" title="No Anys on this line!">        """Get orchestrator metrics including CrewAI crew metrics."""</span>
<span class="line-precise" title="No Anys on this line!">        completed_executions = [</span>
<span class="line-precise" title="No Anys on this line!">            e for e in self.executions.values()</span>
<span class="line-precise" title="No Anys on this line!">            if e.status == WorkflowStatus.COMPLETED</span>
<span class="line-empty" title="No Anys on this line!">        ]</span>
<span class="line-precise" title="No Anys on this line!">        failed_executions = [</span>
<span class="line-precise" title="No Anys on this line!">            e for e in self.executions.values()</span>
<span class="line-precise" title="No Anys on this line!">            if e.status == WorkflowStatus.FAILED</span>
<span class="line-empty" title="No Anys on this line!">        ]</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Get crew metrics</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x3)">        crew_metrics = self.crew_orchestrator.get_crew_metrics()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        return {</span>
<span class="line-precise" title="No Anys on this line!">            "total_workflows": len(self.workflows),</span>
<span class="line-precise" title="No Anys on this line!">            "total_agents": len(self.agents),</span>
<span class="line-precise" title="No Anys on this line!">            "total_executions": len(self.executions),</span>
<span class="line-precise" title="No Anys on this line!">            "running_executions": len(self.running_executions),</span>
<span class="line-precise" title="No Anys on this line!">            "completed_executions": len(completed_executions),</span>
<span class="line-precise" title="No Anys on this line!">            "failed_executions": len(failed_executions),</span>
<span class="line-precise" title="No Anys on this line!">            "success_rate": (</span>
<span class="line-precise" title="No Anys on this line!">                len(completed_executions) / len(self.executions)</span>
<span class="line-precise" title="No Anys on this line!">                if self.executions else 0</span>
<span class="line-empty" title="No Anys on this line!">            ),</span>
<span class="line-precise" title="No Anys on this line!">            "agent_pools": {</span>
<span class="line-precise" title="No Anys on this line!">                agent_type.value: len(agents)</span>
<span class="line-precise" title="No Anys on this line!">                for agent_type, agents in self.agent_pools.items()</span>
<span class="line-empty" title="No Anys on this line!">            },</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)
Omitted Generics (x18)">            "agent_workloads": dict(self.agent_workloads),</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            "crew_metrics": crew_metrics</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class AgentCommunicator:</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Handles communication between agents in the orchestration system.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __init__(self, orchestrator: FlowOrchestrator):</span>
<span class="line-precise" title="No Anys on this line!">        self.orchestrator = orchestrator</span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x3)">        self.message_queue: asyncio.Queue = asyncio.Queue()</span>
<span class="line-precise" title="Any Types on this line: 
Error (x3)">        self.message_handlers: Dict[str, Callable] = {}</span>
<span class="line-precise" title="No Anys on this line!">        self.running = False</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x6)">        self.logger = structlog.get_logger().bind(component="agent_communicator")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def start(self) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Start the communicator service."""</span>
<span class="line-precise" title="No Anys on this line!">        self.running = True</span>
<span class="line-precise" title="No Anys on this line!">        asyncio.create_task(self._process_messages())</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.logger.info("Agent communicator started")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def stop(self) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Stop the communicator service."""</span>
<span class="line-precise" title="No Anys on this line!">        self.running = False</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.logger.info("Agent communicator stopped")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _process_messages(self) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Process messages from the queue."""</span>
<span class="line-precise" title="No Anys on this line!">        while self.running:</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-empty" title="No Anys on this line!">                # Wait for message with timeout</span>
<span class="line-any" title="Any Types on this line: 
Error (x6)">                message = await asyncio.wait_for(</span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x3)">                    self.message_queue.get(),</span>
<span class="line-precise" title="No Anys on this line!">                    timeout=1.0</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">                await self._handle_message(message)</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-precise" title="No Anys on this line!">            except asyncio.TimeoutError:</span>
<span class="line-precise" title="No Anys on this line!">                continue</span>
<span class="line-precise" title="No Anys on this line!">            except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.error("Error processing message", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _handle_message(self, message: AgentMessage) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Handle a message.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            message: Message to handle</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-empty" title="No Anys on this line!">            # Find recipient agent</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">            recipient_agent = self.orchestrator.agents.get(message.recipient_id)</span>
<span class="line-precise" title="No Anys on this line!">            if not recipient_agent:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">                    "Message recipient not found",</span>
<span class="line-precise" title="No Anys on this line!">                    recipient_id=message.recipient_id,</span>
<span class="line-precise" title="No Anys on this line!">                    message_id=message.id</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">                return</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Deliver message to agent</span>
<span class="line-precise" title="No Anys on this line!">            response = await recipient_agent.handle_message(message)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Handle response if provided</span>
<span class="line-precise" title="No Anys on this line!">            if response and message.requires_response:</span>
<span class="line-precise" title="No Anys on this line!">                await self.send_message(response)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.debug(</span>
<span class="line-precise" title="No Anys on this line!">                "Message delivered",</span>
<span class="line-precise" title="No Anys on this line!">                message_id=message.id,</span>
<span class="line-precise" title="No Anys on this line!">                sender_id=message.sender_id,</span>
<span class="line-precise" title="No Anys on this line!">                recipient_id=message.recipient_id,</span>
<span class="line-precise" title="No Anys on this line!">                has_response=bool(response)</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error(</span>
<span class="line-precise" title="No Anys on this line!">                "Failed to handle message",</span>
<span class="line-precise" title="No Anys on this line!">                message_id=message.id,</span>
<span class="line-precise" title="No Anys on this line!">                error=str(e)</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def send_message(self, message: AgentMessage) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Send a message.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            message: Message to send</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)">        await self.message_queue.put(message)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.logger.debug(</span>
<span class="line-precise" title="No Anys on this line!">            "Message queued",</span>
<span class="line-precise" title="No Anys on this line!">            message_id=message.id,</span>
<span class="line-precise" title="No Anys on this line!">            sender_id=message.sender_id,</span>
<span class="line-precise" title="No Anys on this line!">            recipient_id=message.recipient_id,</span>
<span class="line-precise" title="No Anys on this line!">            message_type=message.message_type</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def broadcast_message(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        sender_id: str,</span>
<span class="line-empty" title="No Anys on this line!">        message_type: str,</span>
<span class="line-empty" title="No Anys on this line!">        content: Dict[str, Any],</span>
<span class="line-precise" title="No Anys on this line!">        agent_filter: Optional[Callable[[BaseAiLexAgent], bool]] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Broadcast message to multiple agents.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            sender_id: Sender agent ID</span>
<span class="line-empty" title="No Anys on this line!">            message_type: Message type</span>
<span class="line-empty" title="No Anys on this line!">            content: Message content</span>
<span class="line-empty" title="No Anys on this line!">            agent_filter: Optional filter function for recipients</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        recipients = self.orchestrator.agents.values()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        if agent_filter:</span>
<span class="line-precise" title="No Anys on this line!">            recipients = [agent for agent in recipients if agent_filter(agent)]</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        for agent in recipients:</span>
<span class="line-precise" title="No Anys on this line!">            if agent.agent_id != sender_id:  # Don't send to self</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                message = AgentMessage(</span>
<span class="line-precise" title="No Anys on this line!">                    sender_id=sender_id,</span>
<span class="line-precise" title="No Anys on this line!">                    recipient_id=agent.agent_id,</span>
<span class="line-precise" title="No Anys on this line!">                    message_type=message_type,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    content=content</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-precise" title="No Anys on this line!">                await self.send_message(message)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">            "Message broadcasted",</span>
<span class="line-precise" title="No Anys on this line!">            sender_id=sender_id,</span>
<span class="line-precise" title="No Anys on this line!">            message_type=message_type,</span>
<span class="line-precise" title="No Anys on this line!">            recipient_count=len([a for a in recipients if a.agent_id != sender_id])</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="Any Types on this line: 
Error (x3)">    def register_message_handler(self, message_type: str, handler: Callable) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Register a message handler.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            message_type: Message type to handle</span>
<span class="line-empty" title="No Anys on this line!">            handler: Handler function</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="Any Types on this line: 
Error (x6)">        self.message_handlers[message_type] = handler</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        self.logger.debug("Message handler registered", message_type=message_type)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def request_response(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        sender_id: str,</span>
<span class="line-empty" title="No Anys on this line!">        recipient_id: str,</span>
<span class="line-empty" title="No Anys on this line!">        message_type: str,</span>
<span class="line-empty" title="No Anys on this line!">        content: Dict[str, Any],</span>
<span class="line-precise" title="No Anys on this line!">        timeout_seconds: int = 30</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; Optional[AgentMessage]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Send message and wait for response.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            sender_id: Sender agent ID</span>
<span class="line-empty" title="No Anys on this line!">            recipient_id: Recipient agent ID</span>
<span class="line-empty" title="No Anys on this line!">            message_type: Message type</span>
<span class="line-empty" title="No Anys on this line!">            content: Message content</span>
<span class="line-empty" title="No Anys on this line!">            timeout_seconds: Response timeout</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            Response message or None if timeout</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        correlation_id = str(uuid.uuid4())</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">        message = AgentMessage(</span>
<span class="line-precise" title="No Anys on this line!">            sender_id=sender_id,</span>
<span class="line-precise" title="No Anys on this line!">            recipient_id=recipient_id,</span>
<span class="line-precise" title="No Anys on this line!">            message_type=message_type,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">            content=content,</span>
<span class="line-precise" title="No Anys on this line!">            requires_response=True,</span>
<span class="line-precise" title="No Anys on this line!">            correlation_id=correlation_id</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Create response future</span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x1)">        response_future = asyncio.Future()</span>
<span class="line-any" title="Any Types on this line: 
Error (x2)
Unannotated (x1)">        response_handler = lambda msg: response_future.set_result(msg) if msg.correlation_id == correlation_id else None</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Register temporary handler</span>
<span class="line-precise" title="Any Types on this line: 
Error (x21)
Omitted Generics (x2)">        original_handler = self.message_handlers.get(f"{message_type}_response")</span>
<span class="line-precise" title="Any Types on this line: 
Unannotated (x1)
Error (x3)">        self.message_handlers[f"{message_type}_response"] = response_handler</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            await self.send_message(message)</span>
<span class="line-any" title="Any Types on this line: 
Error (x7)">            response = await asyncio.wait_for(response_future, timeout=timeout_seconds)</span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">            return response</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except asyncio.TimeoutError:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">                "Request-response timeout",</span>
<span class="line-precise" title="No Anys on this line!">                sender_id=sender_id,</span>
<span class="line-precise" title="No Anys on this line!">                recipient_id=recipient_id,</span>
<span class="line-precise" title="No Anys on this line!">                correlation_id=correlation_id</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-precise" title="No Anys on this line!">            return None</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        finally:</span>
<span class="line-empty" title="No Anys on this line!">            # Restore original handler</span>
<span class="line-precise" title="Any Types on this line: 
Error (x3)">            if original_handler:</span>
<span class="line-precise" title="Any Types on this line: 
Error (x6)">                self.message_handlers[f"{message_type}_response"] = original_handler</span>
<span class="line-empty" title="No Anys on this line!">            else:</span>
<span class="line-precise" title="Any Types on this line: 
Error (x18)
Omitted Generics (x2)">                self.message_handlers.pop(f"{message_type}_response", None)</span>
</pre></td>
</tr></tbody>
</table>
</body>
</html>
