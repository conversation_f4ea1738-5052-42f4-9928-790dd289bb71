<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../mypy-html.css">
</head>
<body>
<h2>services.openai_service</h2>
<table>
<caption>services/openai_service.py</caption>
<tbody><tr>
<td class="table-lines"><pre><span id="L1" class="lineno"><a class="lineno" href="#L1">1</a></span>
<span id="L2" class="lineno"><a class="lineno" href="#L2">2</a></span>
<span id="L3" class="lineno"><a class="lineno" href="#L3">3</a></span>
<span id="L4" class="lineno"><a class="lineno" href="#L4">4</a></span>
<span id="L5" class="lineno"><a class="lineno" href="#L5">5</a></span>
<span id="L6" class="lineno"><a class="lineno" href="#L6">6</a></span>
<span id="L7" class="lineno"><a class="lineno" href="#L7">7</a></span>
<span id="L8" class="lineno"><a class="lineno" href="#L8">8</a></span>
<span id="L9" class="lineno"><a class="lineno" href="#L9">9</a></span>
<span id="L10" class="lineno"><a class="lineno" href="#L10">10</a></span>
<span id="L11" class="lineno"><a class="lineno" href="#L11">11</a></span>
<span id="L12" class="lineno"><a class="lineno" href="#L12">12</a></span>
<span id="L13" class="lineno"><a class="lineno" href="#L13">13</a></span>
<span id="L14" class="lineno"><a class="lineno" href="#L14">14</a></span>
<span id="L15" class="lineno"><a class="lineno" href="#L15">15</a></span>
<span id="L16" class="lineno"><a class="lineno" href="#L16">16</a></span>
<span id="L17" class="lineno"><a class="lineno" href="#L17">17</a></span>
<span id="L18" class="lineno"><a class="lineno" href="#L18">18</a></span>
<span id="L19" class="lineno"><a class="lineno" href="#L19">19</a></span>
<span id="L20" class="lineno"><a class="lineno" href="#L20">20</a></span>
<span id="L21" class="lineno"><a class="lineno" href="#L21">21</a></span>
<span id="L22" class="lineno"><a class="lineno" href="#L22">22</a></span>
<span id="L23" class="lineno"><a class="lineno" href="#L23">23</a></span>
<span id="L24" class="lineno"><a class="lineno" href="#L24">24</a></span>
<span id="L25" class="lineno"><a class="lineno" href="#L25">25</a></span>
<span id="L26" class="lineno"><a class="lineno" href="#L26">26</a></span>
<span id="L27" class="lineno"><a class="lineno" href="#L27">27</a></span>
<span id="L28" class="lineno"><a class="lineno" href="#L28">28</a></span>
<span id="L29" class="lineno"><a class="lineno" href="#L29">29</a></span>
<span id="L30" class="lineno"><a class="lineno" href="#L30">30</a></span>
<span id="L31" class="lineno"><a class="lineno" href="#L31">31</a></span>
<span id="L32" class="lineno"><a class="lineno" href="#L32">32</a></span>
<span id="L33" class="lineno"><a class="lineno" href="#L33">33</a></span>
<span id="L34" class="lineno"><a class="lineno" href="#L34">34</a></span>
<span id="L35" class="lineno"><a class="lineno" href="#L35">35</a></span>
<span id="L36" class="lineno"><a class="lineno" href="#L36">36</a></span>
<span id="L37" class="lineno"><a class="lineno" href="#L37">37</a></span>
<span id="L38" class="lineno"><a class="lineno" href="#L38">38</a></span>
<span id="L39" class="lineno"><a class="lineno" href="#L39">39</a></span>
<span id="L40" class="lineno"><a class="lineno" href="#L40">40</a></span>
<span id="L41" class="lineno"><a class="lineno" href="#L41">41</a></span>
<span id="L42" class="lineno"><a class="lineno" href="#L42">42</a></span>
<span id="L43" class="lineno"><a class="lineno" href="#L43">43</a></span>
<span id="L44" class="lineno"><a class="lineno" href="#L44">44</a></span>
<span id="L45" class="lineno"><a class="lineno" href="#L45">45</a></span>
<span id="L46" class="lineno"><a class="lineno" href="#L46">46</a></span>
<span id="L47" class="lineno"><a class="lineno" href="#L47">47</a></span>
<span id="L48" class="lineno"><a class="lineno" href="#L48">48</a></span>
<span id="L49" class="lineno"><a class="lineno" href="#L49">49</a></span>
<span id="L50" class="lineno"><a class="lineno" href="#L50">50</a></span>
<span id="L51" class="lineno"><a class="lineno" href="#L51">51</a></span>
<span id="L52" class="lineno"><a class="lineno" href="#L52">52</a></span>
<span id="L53" class="lineno"><a class="lineno" href="#L53">53</a></span>
<span id="L54" class="lineno"><a class="lineno" href="#L54">54</a></span>
<span id="L55" class="lineno"><a class="lineno" href="#L55">55</a></span>
<span id="L56" class="lineno"><a class="lineno" href="#L56">56</a></span>
<span id="L57" class="lineno"><a class="lineno" href="#L57">57</a></span>
<span id="L58" class="lineno"><a class="lineno" href="#L58">58</a></span>
<span id="L59" class="lineno"><a class="lineno" href="#L59">59</a></span>
<span id="L60" class="lineno"><a class="lineno" href="#L60">60</a></span>
<span id="L61" class="lineno"><a class="lineno" href="#L61">61</a></span>
<span id="L62" class="lineno"><a class="lineno" href="#L62">62</a></span>
<span id="L63" class="lineno"><a class="lineno" href="#L63">63</a></span>
<span id="L64" class="lineno"><a class="lineno" href="#L64">64</a></span>
<span id="L65" class="lineno"><a class="lineno" href="#L65">65</a></span>
<span id="L66" class="lineno"><a class="lineno" href="#L66">66</a></span>
<span id="L67" class="lineno"><a class="lineno" href="#L67">67</a></span>
<span id="L68" class="lineno"><a class="lineno" href="#L68">68</a></span>
<span id="L69" class="lineno"><a class="lineno" href="#L69">69</a></span>
<span id="L70" class="lineno"><a class="lineno" href="#L70">70</a></span>
<span id="L71" class="lineno"><a class="lineno" href="#L71">71</a></span>
<span id="L72" class="lineno"><a class="lineno" href="#L72">72</a></span>
<span id="L73" class="lineno"><a class="lineno" href="#L73">73</a></span>
<span id="L74" class="lineno"><a class="lineno" href="#L74">74</a></span>
<span id="L75" class="lineno"><a class="lineno" href="#L75">75</a></span>
<span id="L76" class="lineno"><a class="lineno" href="#L76">76</a></span>
<span id="L77" class="lineno"><a class="lineno" href="#L77">77</a></span>
<span id="L78" class="lineno"><a class="lineno" href="#L78">78</a></span>
<span id="L79" class="lineno"><a class="lineno" href="#L79">79</a></span>
<span id="L80" class="lineno"><a class="lineno" href="#L80">80</a></span>
<span id="L81" class="lineno"><a class="lineno" href="#L81">81</a></span>
<span id="L82" class="lineno"><a class="lineno" href="#L82">82</a></span>
<span id="L83" class="lineno"><a class="lineno" href="#L83">83</a></span>
<span id="L84" class="lineno"><a class="lineno" href="#L84">84</a></span>
<span id="L85" class="lineno"><a class="lineno" href="#L85">85</a></span>
<span id="L86" class="lineno"><a class="lineno" href="#L86">86</a></span>
<span id="L87" class="lineno"><a class="lineno" href="#L87">87</a></span>
<span id="L88" class="lineno"><a class="lineno" href="#L88">88</a></span>
<span id="L89" class="lineno"><a class="lineno" href="#L89">89</a></span>
<span id="L90" class="lineno"><a class="lineno" href="#L90">90</a></span>
<span id="L91" class="lineno"><a class="lineno" href="#L91">91</a></span>
<span id="L92" class="lineno"><a class="lineno" href="#L92">92</a></span>
<span id="L93" class="lineno"><a class="lineno" href="#L93">93</a></span>
<span id="L94" class="lineno"><a class="lineno" href="#L94">94</a></span>
<span id="L95" class="lineno"><a class="lineno" href="#L95">95</a></span>
<span id="L96" class="lineno"><a class="lineno" href="#L96">96</a></span>
<span id="L97" class="lineno"><a class="lineno" href="#L97">97</a></span>
<span id="L98" class="lineno"><a class="lineno" href="#L98">98</a></span>
<span id="L99" class="lineno"><a class="lineno" href="#L99">99</a></span>
<span id="L100" class="lineno"><a class="lineno" href="#L100">100</a></span>
<span id="L101" class="lineno"><a class="lineno" href="#L101">101</a></span>
<span id="L102" class="lineno"><a class="lineno" href="#L102">102</a></span>
<span id="L103" class="lineno"><a class="lineno" href="#L103">103</a></span>
<span id="L104" class="lineno"><a class="lineno" href="#L104">104</a></span>
<span id="L105" class="lineno"><a class="lineno" href="#L105">105</a></span>
<span id="L106" class="lineno"><a class="lineno" href="#L106">106</a></span>
<span id="L107" class="lineno"><a class="lineno" href="#L107">107</a></span>
<span id="L108" class="lineno"><a class="lineno" href="#L108">108</a></span>
<span id="L109" class="lineno"><a class="lineno" href="#L109">109</a></span>
<span id="L110" class="lineno"><a class="lineno" href="#L110">110</a></span>
<span id="L111" class="lineno"><a class="lineno" href="#L111">111</a></span>
<span id="L112" class="lineno"><a class="lineno" href="#L112">112</a></span>
<span id="L113" class="lineno"><a class="lineno" href="#L113">113</a></span>
<span id="L114" class="lineno"><a class="lineno" href="#L114">114</a></span>
<span id="L115" class="lineno"><a class="lineno" href="#L115">115</a></span>
<span id="L116" class="lineno"><a class="lineno" href="#L116">116</a></span>
<span id="L117" class="lineno"><a class="lineno" href="#L117">117</a></span>
<span id="L118" class="lineno"><a class="lineno" href="#L118">118</a></span>
<span id="L119" class="lineno"><a class="lineno" href="#L119">119</a></span>
<span id="L120" class="lineno"><a class="lineno" href="#L120">120</a></span>
<span id="L121" class="lineno"><a class="lineno" href="#L121">121</a></span>
<span id="L122" class="lineno"><a class="lineno" href="#L122">122</a></span>
<span id="L123" class="lineno"><a class="lineno" href="#L123">123</a></span>
<span id="L124" class="lineno"><a class="lineno" href="#L124">124</a></span>
<span id="L125" class="lineno"><a class="lineno" href="#L125">125</a></span>
<span id="L126" class="lineno"><a class="lineno" href="#L126">126</a></span>
<span id="L127" class="lineno"><a class="lineno" href="#L127">127</a></span>
<span id="L128" class="lineno"><a class="lineno" href="#L128">128</a></span>
<span id="L129" class="lineno"><a class="lineno" href="#L129">129</a></span>
<span id="L130" class="lineno"><a class="lineno" href="#L130">130</a></span>
<span id="L131" class="lineno"><a class="lineno" href="#L131">131</a></span>
<span id="L132" class="lineno"><a class="lineno" href="#L132">132</a></span>
<span id="L133" class="lineno"><a class="lineno" href="#L133">133</a></span>
<span id="L134" class="lineno"><a class="lineno" href="#L134">134</a></span>
<span id="L135" class="lineno"><a class="lineno" href="#L135">135</a></span>
<span id="L136" class="lineno"><a class="lineno" href="#L136">136</a></span>
<span id="L137" class="lineno"><a class="lineno" href="#L137">137</a></span>
<span id="L138" class="lineno"><a class="lineno" href="#L138">138</a></span>
<span id="L139" class="lineno"><a class="lineno" href="#L139">139</a></span>
<span id="L140" class="lineno"><a class="lineno" href="#L140">140</a></span>
<span id="L141" class="lineno"><a class="lineno" href="#L141">141</a></span>
<span id="L142" class="lineno"><a class="lineno" href="#L142">142</a></span>
<span id="L143" class="lineno"><a class="lineno" href="#L143">143</a></span>
<span id="L144" class="lineno"><a class="lineno" href="#L144">144</a></span>
<span id="L145" class="lineno"><a class="lineno" href="#L145">145</a></span>
<span id="L146" class="lineno"><a class="lineno" href="#L146">146</a></span>
<span id="L147" class="lineno"><a class="lineno" href="#L147">147</a></span>
<span id="L148" class="lineno"><a class="lineno" href="#L148">148</a></span>
<span id="L149" class="lineno"><a class="lineno" href="#L149">149</a></span>
<span id="L150" class="lineno"><a class="lineno" href="#L150">150</a></span>
<span id="L151" class="lineno"><a class="lineno" href="#L151">151</a></span>
<span id="L152" class="lineno"><a class="lineno" href="#L152">152</a></span>
<span id="L153" class="lineno"><a class="lineno" href="#L153">153</a></span>
<span id="L154" class="lineno"><a class="lineno" href="#L154">154</a></span>
<span id="L155" class="lineno"><a class="lineno" href="#L155">155</a></span>
<span id="L156" class="lineno"><a class="lineno" href="#L156">156</a></span>
<span id="L157" class="lineno"><a class="lineno" href="#L157">157</a></span>
<span id="L158" class="lineno"><a class="lineno" href="#L158">158</a></span>
<span id="L159" class="lineno"><a class="lineno" href="#L159">159</a></span>
<span id="L160" class="lineno"><a class="lineno" href="#L160">160</a></span>
<span id="L161" class="lineno"><a class="lineno" href="#L161">161</a></span>
<span id="L162" class="lineno"><a class="lineno" href="#L162">162</a></span>
<span id="L163" class="lineno"><a class="lineno" href="#L163">163</a></span>
<span id="L164" class="lineno"><a class="lineno" href="#L164">164</a></span>
<span id="L165" class="lineno"><a class="lineno" href="#L165">165</a></span>
<span id="L166" class="lineno"><a class="lineno" href="#L166">166</a></span>
<span id="L167" class="lineno"><a class="lineno" href="#L167">167</a></span>
<span id="L168" class="lineno"><a class="lineno" href="#L168">168</a></span>
<span id="L169" class="lineno"><a class="lineno" href="#L169">169</a></span>
<span id="L170" class="lineno"><a class="lineno" href="#L170">170</a></span>
<span id="L171" class="lineno"><a class="lineno" href="#L171">171</a></span>
<span id="L172" class="lineno"><a class="lineno" href="#L172">172</a></span>
<span id="L173" class="lineno"><a class="lineno" href="#L173">173</a></span>
<span id="L174" class="lineno"><a class="lineno" href="#L174">174</a></span>
<span id="L175" class="lineno"><a class="lineno" href="#L175">175</a></span>
<span id="L176" class="lineno"><a class="lineno" href="#L176">176</a></span>
<span id="L177" class="lineno"><a class="lineno" href="#L177">177</a></span>
<span id="L178" class="lineno"><a class="lineno" href="#L178">178</a></span>
<span id="L179" class="lineno"><a class="lineno" href="#L179">179</a></span>
<span id="L180" class="lineno"><a class="lineno" href="#L180">180</a></span>
<span id="L181" class="lineno"><a class="lineno" href="#L181">181</a></span>
<span id="L182" class="lineno"><a class="lineno" href="#L182">182</a></span>
<span id="L183" class="lineno"><a class="lineno" href="#L183">183</a></span>
<span id="L184" class="lineno"><a class="lineno" href="#L184">184</a></span>
<span id="L185" class="lineno"><a class="lineno" href="#L185">185</a></span>
<span id="L186" class="lineno"><a class="lineno" href="#L186">186</a></span>
<span id="L187" class="lineno"><a class="lineno" href="#L187">187</a></span>
<span id="L188" class="lineno"><a class="lineno" href="#L188">188</a></span>
<span id="L189" class="lineno"><a class="lineno" href="#L189">189</a></span>
<span id="L190" class="lineno"><a class="lineno" href="#L190">190</a></span>
<span id="L191" class="lineno"><a class="lineno" href="#L191">191</a></span>
<span id="L192" class="lineno"><a class="lineno" href="#L192">192</a></span>
<span id="L193" class="lineno"><a class="lineno" href="#L193">193</a></span>
<span id="L194" class="lineno"><a class="lineno" href="#L194">194</a></span>
<span id="L195" class="lineno"><a class="lineno" href="#L195">195</a></span>
<span id="L196" class="lineno"><a class="lineno" href="#L196">196</a></span>
<span id="L197" class="lineno"><a class="lineno" href="#L197">197</a></span>
<span id="L198" class="lineno"><a class="lineno" href="#L198">198</a></span>
<span id="L199" class="lineno"><a class="lineno" href="#L199">199</a></span>
<span id="L200" class="lineno"><a class="lineno" href="#L200">200</a></span>
<span id="L201" class="lineno"><a class="lineno" href="#L201">201</a></span>
<span id="L202" class="lineno"><a class="lineno" href="#L202">202</a></span>
<span id="L203" class="lineno"><a class="lineno" href="#L203">203</a></span>
<span id="L204" class="lineno"><a class="lineno" href="#L204">204</a></span>
<span id="L205" class="lineno"><a class="lineno" href="#L205">205</a></span>
<span id="L206" class="lineno"><a class="lineno" href="#L206">206</a></span>
<span id="L207" class="lineno"><a class="lineno" href="#L207">207</a></span>
<span id="L208" class="lineno"><a class="lineno" href="#L208">208</a></span>
<span id="L209" class="lineno"><a class="lineno" href="#L209">209</a></span>
<span id="L210" class="lineno"><a class="lineno" href="#L210">210</a></span>
<span id="L211" class="lineno"><a class="lineno" href="#L211">211</a></span>
<span id="L212" class="lineno"><a class="lineno" href="#L212">212</a></span>
<span id="L213" class="lineno"><a class="lineno" href="#L213">213</a></span>
<span id="L214" class="lineno"><a class="lineno" href="#L214">214</a></span>
<span id="L215" class="lineno"><a class="lineno" href="#L215">215</a></span>
<span id="L216" class="lineno"><a class="lineno" href="#L216">216</a></span>
<span id="L217" class="lineno"><a class="lineno" href="#L217">217</a></span>
<span id="L218" class="lineno"><a class="lineno" href="#L218">218</a></span>
<span id="L219" class="lineno"><a class="lineno" href="#L219">219</a></span>
<span id="L220" class="lineno"><a class="lineno" href="#L220">220</a></span>
<span id="L221" class="lineno"><a class="lineno" href="#L221">221</a></span>
<span id="L222" class="lineno"><a class="lineno" href="#L222">222</a></span>
<span id="L223" class="lineno"><a class="lineno" href="#L223">223</a></span>
<span id="L224" class="lineno"><a class="lineno" href="#L224">224</a></span>
<span id="L225" class="lineno"><a class="lineno" href="#L225">225</a></span>
<span id="L226" class="lineno"><a class="lineno" href="#L226">226</a></span>
<span id="L227" class="lineno"><a class="lineno" href="#L227">227</a></span>
<span id="L228" class="lineno"><a class="lineno" href="#L228">228</a></span>
<span id="L229" class="lineno"><a class="lineno" href="#L229">229</a></span>
<span id="L230" class="lineno"><a class="lineno" href="#L230">230</a></span>
<span id="L231" class="lineno"><a class="lineno" href="#L231">231</a></span>
<span id="L232" class="lineno"><a class="lineno" href="#L232">232</a></span>
<span id="L233" class="lineno"><a class="lineno" href="#L233">233</a></span>
<span id="L234" class="lineno"><a class="lineno" href="#L234">234</a></span>
<span id="L235" class="lineno"><a class="lineno" href="#L235">235</a></span>
<span id="L236" class="lineno"><a class="lineno" href="#L236">236</a></span>
<span id="L237" class="lineno"><a class="lineno" href="#L237">237</a></span>
<span id="L238" class="lineno"><a class="lineno" href="#L238">238</a></span>
<span id="L239" class="lineno"><a class="lineno" href="#L239">239</a></span>
<span id="L240" class="lineno"><a class="lineno" href="#L240">240</a></span>
<span id="L241" class="lineno"><a class="lineno" href="#L241">241</a></span>
<span id="L242" class="lineno"><a class="lineno" href="#L242">242</a></span>
<span id="L243" class="lineno"><a class="lineno" href="#L243">243</a></span>
<span id="L244" class="lineno"><a class="lineno" href="#L244">244</a></span>
<span id="L245" class="lineno"><a class="lineno" href="#L245">245</a></span>
<span id="L246" class="lineno"><a class="lineno" href="#L246">246</a></span>
<span id="L247" class="lineno"><a class="lineno" href="#L247">247</a></span>
<span id="L248" class="lineno"><a class="lineno" href="#L248">248</a></span>
<span id="L249" class="lineno"><a class="lineno" href="#L249">249</a></span>
<span id="L250" class="lineno"><a class="lineno" href="#L250">250</a></span>
<span id="L251" class="lineno"><a class="lineno" href="#L251">251</a></span>
<span id="L252" class="lineno"><a class="lineno" href="#L252">252</a></span>
<span id="L253" class="lineno"><a class="lineno" href="#L253">253</a></span>
<span id="L254" class="lineno"><a class="lineno" href="#L254">254</a></span>
<span id="L255" class="lineno"><a class="lineno" href="#L255">255</a></span>
<span id="L256" class="lineno"><a class="lineno" href="#L256">256</a></span>
<span id="L257" class="lineno"><a class="lineno" href="#L257">257</a></span>
<span id="L258" class="lineno"><a class="lineno" href="#L258">258</a></span>
<span id="L259" class="lineno"><a class="lineno" href="#L259">259</a></span>
<span id="L260" class="lineno"><a class="lineno" href="#L260">260</a></span>
<span id="L261" class="lineno"><a class="lineno" href="#L261">261</a></span>
<span id="L262" class="lineno"><a class="lineno" href="#L262">262</a></span>
<span id="L263" class="lineno"><a class="lineno" href="#L263">263</a></span>
<span id="L264" class="lineno"><a class="lineno" href="#L264">264</a></span>
<span id="L265" class="lineno"><a class="lineno" href="#L265">265</a></span>
<span id="L266" class="lineno"><a class="lineno" href="#L266">266</a></span>
<span id="L267" class="lineno"><a class="lineno" href="#L267">267</a></span>
<span id="L268" class="lineno"><a class="lineno" href="#L268">268</a></span>
<span id="L269" class="lineno"><a class="lineno" href="#L269">269</a></span>
<span id="L270" class="lineno"><a class="lineno" href="#L270">270</a></span>
<span id="L271" class="lineno"><a class="lineno" href="#L271">271</a></span>
<span id="L272" class="lineno"><a class="lineno" href="#L272">272</a></span>
<span id="L273" class="lineno"><a class="lineno" href="#L273">273</a></span>
<span id="L274" class="lineno"><a class="lineno" href="#L274">274</a></span>
<span id="L275" class="lineno"><a class="lineno" href="#L275">275</a></span>
<span id="L276" class="lineno"><a class="lineno" href="#L276">276</a></span>
<span id="L277" class="lineno"><a class="lineno" href="#L277">277</a></span>
<span id="L278" class="lineno"><a class="lineno" href="#L278">278</a></span>
<span id="L279" class="lineno"><a class="lineno" href="#L279">279</a></span>
<span id="L280" class="lineno"><a class="lineno" href="#L280">280</a></span>
<span id="L281" class="lineno"><a class="lineno" href="#L281">281</a></span>
<span id="L282" class="lineno"><a class="lineno" href="#L282">282</a></span>
<span id="L283" class="lineno"><a class="lineno" href="#L283">283</a></span>
<span id="L284" class="lineno"><a class="lineno" href="#L284">284</a></span>
<span id="L285" class="lineno"><a class="lineno" href="#L285">285</a></span>
<span id="L286" class="lineno"><a class="lineno" href="#L286">286</a></span>
<span id="L287" class="lineno"><a class="lineno" href="#L287">287</a></span>
<span id="L288" class="lineno"><a class="lineno" href="#L288">288</a></span>
<span id="L289" class="lineno"><a class="lineno" href="#L289">289</a></span>
<span id="L290" class="lineno"><a class="lineno" href="#L290">290</a></span>
<span id="L291" class="lineno"><a class="lineno" href="#L291">291</a></span>
<span id="L292" class="lineno"><a class="lineno" href="#L292">292</a></span>
<span id="L293" class="lineno"><a class="lineno" href="#L293">293</a></span>
<span id="L294" class="lineno"><a class="lineno" href="#L294">294</a></span>
<span id="L295" class="lineno"><a class="lineno" href="#L295">295</a></span>
<span id="L296" class="lineno"><a class="lineno" href="#L296">296</a></span>
<span id="L297" class="lineno"><a class="lineno" href="#L297">297</a></span>
<span id="L298" class="lineno"><a class="lineno" href="#L298">298</a></span>
<span id="L299" class="lineno"><a class="lineno" href="#L299">299</a></span>
<span id="L300" class="lineno"><a class="lineno" href="#L300">300</a></span>
<span id="L301" class="lineno"><a class="lineno" href="#L301">301</a></span>
<span id="L302" class="lineno"><a class="lineno" href="#L302">302</a></span>
<span id="L303" class="lineno"><a class="lineno" href="#L303">303</a></span>
<span id="L304" class="lineno"><a class="lineno" href="#L304">304</a></span>
<span id="L305" class="lineno"><a class="lineno" href="#L305">305</a></span>
<span id="L306" class="lineno"><a class="lineno" href="#L306">306</a></span>
<span id="L307" class="lineno"><a class="lineno" href="#L307">307</a></span>
<span id="L308" class="lineno"><a class="lineno" href="#L308">308</a></span>
<span id="L309" class="lineno"><a class="lineno" href="#L309">309</a></span>
<span id="L310" class="lineno"><a class="lineno" href="#L310">310</a></span>
<span id="L311" class="lineno"><a class="lineno" href="#L311">311</a></span>
<span id="L312" class="lineno"><a class="lineno" href="#L312">312</a></span>
<span id="L313" class="lineno"><a class="lineno" href="#L313">313</a></span>
<span id="L314" class="lineno"><a class="lineno" href="#L314">314</a></span>
<span id="L315" class="lineno"><a class="lineno" href="#L315">315</a></span>
<span id="L316" class="lineno"><a class="lineno" href="#L316">316</a></span>
<span id="L317" class="lineno"><a class="lineno" href="#L317">317</a></span>
<span id="L318" class="lineno"><a class="lineno" href="#L318">318</a></span>
<span id="L319" class="lineno"><a class="lineno" href="#L319">319</a></span>
<span id="L320" class="lineno"><a class="lineno" href="#L320">320</a></span>
<span id="L321" class="lineno"><a class="lineno" href="#L321">321</a></span>
<span id="L322" class="lineno"><a class="lineno" href="#L322">322</a></span>
<span id="L323" class="lineno"><a class="lineno" href="#L323">323</a></span>
<span id="L324" class="lineno"><a class="lineno" href="#L324">324</a></span>
<span id="L325" class="lineno"><a class="lineno" href="#L325">325</a></span>
<span id="L326" class="lineno"><a class="lineno" href="#L326">326</a></span>
<span id="L327" class="lineno"><a class="lineno" href="#L327">327</a></span>
<span id="L328" class="lineno"><a class="lineno" href="#L328">328</a></span>
<span id="L329" class="lineno"><a class="lineno" href="#L329">329</a></span>
<span id="L330" class="lineno"><a class="lineno" href="#L330">330</a></span>
<span id="L331" class="lineno"><a class="lineno" href="#L331">331</a></span>
<span id="L332" class="lineno"><a class="lineno" href="#L332">332</a></span>
<span id="L333" class="lineno"><a class="lineno" href="#L333">333</a></span>
<span id="L334" class="lineno"><a class="lineno" href="#L334">334</a></span>
<span id="L335" class="lineno"><a class="lineno" href="#L335">335</a></span>
<span id="L336" class="lineno"><a class="lineno" href="#L336">336</a></span>
<span id="L337" class="lineno"><a class="lineno" href="#L337">337</a></span>
<span id="L338" class="lineno"><a class="lineno" href="#L338">338</a></span>
<span id="L339" class="lineno"><a class="lineno" href="#L339">339</a></span>
<span id="L340" class="lineno"><a class="lineno" href="#L340">340</a></span>
<span id="L341" class="lineno"><a class="lineno" href="#L341">341</a></span>
<span id="L342" class="lineno"><a class="lineno" href="#L342">342</a></span>
<span id="L343" class="lineno"><a class="lineno" href="#L343">343</a></span>
<span id="L344" class="lineno"><a class="lineno" href="#L344">344</a></span>
<span id="L345" class="lineno"><a class="lineno" href="#L345">345</a></span>
<span id="L346" class="lineno"><a class="lineno" href="#L346">346</a></span>
<span id="L347" class="lineno"><a class="lineno" href="#L347">347</a></span>
<span id="L348" class="lineno"><a class="lineno" href="#L348">348</a></span>
<span id="L349" class="lineno"><a class="lineno" href="#L349">349</a></span>
<span id="L350" class="lineno"><a class="lineno" href="#L350">350</a></span>
<span id="L351" class="lineno"><a class="lineno" href="#L351">351</a></span>
<span id="L352" class="lineno"><a class="lineno" href="#L352">352</a></span>
<span id="L353" class="lineno"><a class="lineno" href="#L353">353</a></span>
<span id="L354" class="lineno"><a class="lineno" href="#L354">354</a></span>
<span id="L355" class="lineno"><a class="lineno" href="#L355">355</a></span>
<span id="L356" class="lineno"><a class="lineno" href="#L356">356</a></span>
<span id="L357" class="lineno"><a class="lineno" href="#L357">357</a></span>
<span id="L358" class="lineno"><a class="lineno" href="#L358">358</a></span>
<span id="L359" class="lineno"><a class="lineno" href="#L359">359</a></span>
<span id="L360" class="lineno"><a class="lineno" href="#L360">360</a></span>
<span id="L361" class="lineno"><a class="lineno" href="#L361">361</a></span>
<span id="L362" class="lineno"><a class="lineno" href="#L362">362</a></span>
<span id="L363" class="lineno"><a class="lineno" href="#L363">363</a></span>
<span id="L364" class="lineno"><a class="lineno" href="#L364">364</a></span>
<span id="L365" class="lineno"><a class="lineno" href="#L365">365</a></span>
<span id="L366" class="lineno"><a class="lineno" href="#L366">366</a></span>
<span id="L367" class="lineno"><a class="lineno" href="#L367">367</a></span>
<span id="L368" class="lineno"><a class="lineno" href="#L368">368</a></span>
<span id="L369" class="lineno"><a class="lineno" href="#L369">369</a></span>
<span id="L370" class="lineno"><a class="lineno" href="#L370">370</a></span>
<span id="L371" class="lineno"><a class="lineno" href="#L371">371</a></span>
<span id="L372" class="lineno"><a class="lineno" href="#L372">372</a></span>
<span id="L373" class="lineno"><a class="lineno" href="#L373">373</a></span>
<span id="L374" class="lineno"><a class="lineno" href="#L374">374</a></span>
<span id="L375" class="lineno"><a class="lineno" href="#L375">375</a></span>
<span id="L376" class="lineno"><a class="lineno" href="#L376">376</a></span>
<span id="L377" class="lineno"><a class="lineno" href="#L377">377</a></span>
<span id="L378" class="lineno"><a class="lineno" href="#L378">378</a></span>
<span id="L379" class="lineno"><a class="lineno" href="#L379">379</a></span>
<span id="L380" class="lineno"><a class="lineno" href="#L380">380</a></span>
<span id="L381" class="lineno"><a class="lineno" href="#L381">381</a></span>
<span id="L382" class="lineno"><a class="lineno" href="#L382">382</a></span>
<span id="L383" class="lineno"><a class="lineno" href="#L383">383</a></span>
<span id="L384" class="lineno"><a class="lineno" href="#L384">384</a></span>
<span id="L385" class="lineno"><a class="lineno" href="#L385">385</a></span>
<span id="L386" class="lineno"><a class="lineno" href="#L386">386</a></span>
<span id="L387" class="lineno"><a class="lineno" href="#L387">387</a></span>
<span id="L388" class="lineno"><a class="lineno" href="#L388">388</a></span>
<span id="L389" class="lineno"><a class="lineno" href="#L389">389</a></span>
<span id="L390" class="lineno"><a class="lineno" href="#L390">390</a></span>
<span id="L391" class="lineno"><a class="lineno" href="#L391">391</a></span>
<span id="L392" class="lineno"><a class="lineno" href="#L392">392</a></span>
<span id="L393" class="lineno"><a class="lineno" href="#L393">393</a></span>
<span id="L394" class="lineno"><a class="lineno" href="#L394">394</a></span>
<span id="L395" class="lineno"><a class="lineno" href="#L395">395</a></span>
<span id="L396" class="lineno"><a class="lineno" href="#L396">396</a></span>
<span id="L397" class="lineno"><a class="lineno" href="#L397">397</a></span>
<span id="L398" class="lineno"><a class="lineno" href="#L398">398</a></span>
<span id="L399" class="lineno"><a class="lineno" href="#L399">399</a></span>
<span id="L400" class="lineno"><a class="lineno" href="#L400">400</a></span>
<span id="L401" class="lineno"><a class="lineno" href="#L401">401</a></span>
<span id="L402" class="lineno"><a class="lineno" href="#L402">402</a></span>
<span id="L403" class="lineno"><a class="lineno" href="#L403">403</a></span>
<span id="L404" class="lineno"><a class="lineno" href="#L404">404</a></span>
<span id="L405" class="lineno"><a class="lineno" href="#L405">405</a></span>
<span id="L406" class="lineno"><a class="lineno" href="#L406">406</a></span>
<span id="L407" class="lineno"><a class="lineno" href="#L407">407</a></span>
<span id="L408" class="lineno"><a class="lineno" href="#L408">408</a></span>
<span id="L409" class="lineno"><a class="lineno" href="#L409">409</a></span>
<span id="L410" class="lineno"><a class="lineno" href="#L410">410</a></span>
<span id="L411" class="lineno"><a class="lineno" href="#L411">411</a></span>
<span id="L412" class="lineno"><a class="lineno" href="#L412">412</a></span>
<span id="L413" class="lineno"><a class="lineno" href="#L413">413</a></span>
<span id="L414" class="lineno"><a class="lineno" href="#L414">414</a></span>
<span id="L415" class="lineno"><a class="lineno" href="#L415">415</a></span>
<span id="L416" class="lineno"><a class="lineno" href="#L416">416</a></span>
<span id="L417" class="lineno"><a class="lineno" href="#L417">417</a></span>
<span id="L418" class="lineno"><a class="lineno" href="#L418">418</a></span>
<span id="L419" class="lineno"><a class="lineno" href="#L419">419</a></span>
<span id="L420" class="lineno"><a class="lineno" href="#L420">420</a></span>
<span id="L421" class="lineno"><a class="lineno" href="#L421">421</a></span>
<span id="L422" class="lineno"><a class="lineno" href="#L422">422</a></span>
<span id="L423" class="lineno"><a class="lineno" href="#L423">423</a></span>
<span id="L424" class="lineno"><a class="lineno" href="#L424">424</a></span>
<span id="L425" class="lineno"><a class="lineno" href="#L425">425</a></span>
<span id="L426" class="lineno"><a class="lineno" href="#L426">426</a></span>
<span id="L427" class="lineno"><a class="lineno" href="#L427">427</a></span>
<span id="L428" class="lineno"><a class="lineno" href="#L428">428</a></span>
<span id="L429" class="lineno"><a class="lineno" href="#L429">429</a></span>
<span id="L430" class="lineno"><a class="lineno" href="#L430">430</a></span>
<span id="L431" class="lineno"><a class="lineno" href="#L431">431</a></span>
<span id="L432" class="lineno"><a class="lineno" href="#L432">432</a></span>
<span id="L433" class="lineno"><a class="lineno" href="#L433">433</a></span>
<span id="L434" class="lineno"><a class="lineno" href="#L434">434</a></span>
<span id="L435" class="lineno"><a class="lineno" href="#L435">435</a></span>
<span id="L436" class="lineno"><a class="lineno" href="#L436">436</a></span>
<span id="L437" class="lineno"><a class="lineno" href="#L437">437</a></span>
<span id="L438" class="lineno"><a class="lineno" href="#L438">438</a></span>
<span id="L439" class="lineno"><a class="lineno" href="#L439">439</a></span>
<span id="L440" class="lineno"><a class="lineno" href="#L440">440</a></span>
<span id="L441" class="lineno"><a class="lineno" href="#L441">441</a></span>
<span id="L442" class="lineno"><a class="lineno" href="#L442">442</a></span>
<span id="L443" class="lineno"><a class="lineno" href="#L443">443</a></span>
<span id="L444" class="lineno"><a class="lineno" href="#L444">444</a></span>
<span id="L445" class="lineno"><a class="lineno" href="#L445">445</a></span>
<span id="L446" class="lineno"><a class="lineno" href="#L446">446</a></span>
<span id="L447" class="lineno"><a class="lineno" href="#L447">447</a></span>
<span id="L448" class="lineno"><a class="lineno" href="#L448">448</a></span>
<span id="L449" class="lineno"><a class="lineno" href="#L449">449</a></span>
<span id="L450" class="lineno"><a class="lineno" href="#L450">450</a></span>
<span id="L451" class="lineno"><a class="lineno" href="#L451">451</a></span>
<span id="L452" class="lineno"><a class="lineno" href="#L452">452</a></span>
<span id="L453" class="lineno"><a class="lineno" href="#L453">453</a></span>
<span id="L454" class="lineno"><a class="lineno" href="#L454">454</a></span>
<span id="L455" class="lineno"><a class="lineno" href="#L455">455</a></span>
<span id="L456" class="lineno"><a class="lineno" href="#L456">456</a></span>
<span id="L457" class="lineno"><a class="lineno" href="#L457">457</a></span>
<span id="L458" class="lineno"><a class="lineno" href="#L458">458</a></span>
<span id="L459" class="lineno"><a class="lineno" href="#L459">459</a></span>
<span id="L460" class="lineno"><a class="lineno" href="#L460">460</a></span>
<span id="L461" class="lineno"><a class="lineno" href="#L461">461</a></span>
<span id="L462" class="lineno"><a class="lineno" href="#L462">462</a></span>
<span id="L463" class="lineno"><a class="lineno" href="#L463">463</a></span>
<span id="L464" class="lineno"><a class="lineno" href="#L464">464</a></span>
<span id="L465" class="lineno"><a class="lineno" href="#L465">465</a></span>
<span id="L466" class="lineno"><a class="lineno" href="#L466">466</a></span>
<span id="L467" class="lineno"><a class="lineno" href="#L467">467</a></span>
<span id="L468" class="lineno"><a class="lineno" href="#L468">468</a></span>
<span id="L469" class="lineno"><a class="lineno" href="#L469">469</a></span>
<span id="L470" class="lineno"><a class="lineno" href="#L470">470</a></span>
<span id="L471" class="lineno"><a class="lineno" href="#L471">471</a></span>
<span id="L472" class="lineno"><a class="lineno" href="#L472">472</a></span>
<span id="L473" class="lineno"><a class="lineno" href="#L473">473</a></span>
<span id="L474" class="lineno"><a class="lineno" href="#L474">474</a></span>
<span id="L475" class="lineno"><a class="lineno" href="#L475">475</a></span>
<span id="L476" class="lineno"><a class="lineno" href="#L476">476</a></span>
<span id="L477" class="lineno"><a class="lineno" href="#L477">477</a></span>
<span id="L478" class="lineno"><a class="lineno" href="#L478">478</a></span>
<span id="L479" class="lineno"><a class="lineno" href="#L479">479</a></span>
<span id="L480" class="lineno"><a class="lineno" href="#L480">480</a></span>
<span id="L481" class="lineno"><a class="lineno" href="#L481">481</a></span>
<span id="L482" class="lineno"><a class="lineno" href="#L482">482</a></span>
<span id="L483" class="lineno"><a class="lineno" href="#L483">483</a></span>
<span id="L484" class="lineno"><a class="lineno" href="#L484">484</a></span>
<span id="L485" class="lineno"><a class="lineno" href="#L485">485</a></span>
<span id="L486" class="lineno"><a class="lineno" href="#L486">486</a></span>
<span id="L487" class="lineno"><a class="lineno" href="#L487">487</a></span>
<span id="L488" class="lineno"><a class="lineno" href="#L488">488</a></span>
<span id="L489" class="lineno"><a class="lineno" href="#L489">489</a></span>
<span id="L490" class="lineno"><a class="lineno" href="#L490">490</a></span>
<span id="L491" class="lineno"><a class="lineno" href="#L491">491</a></span>
<span id="L492" class="lineno"><a class="lineno" href="#L492">492</a></span>
<span id="L493" class="lineno"><a class="lineno" href="#L493">493</a></span>
<span id="L494" class="lineno"><a class="lineno" href="#L494">494</a></span>
<span id="L495" class="lineno"><a class="lineno" href="#L495">495</a></span>
<span id="L496" class="lineno"><a class="lineno" href="#L496">496</a></span>
<span id="L497" class="lineno"><a class="lineno" href="#L497">497</a></span>
<span id="L498" class="lineno"><a class="lineno" href="#L498">498</a></span>
<span id="L499" class="lineno"><a class="lineno" href="#L499">499</a></span>
<span id="L500" class="lineno"><a class="lineno" href="#L500">500</a></span>
<span id="L501" class="lineno"><a class="lineno" href="#L501">501</a></span>
<span id="L502" class="lineno"><a class="lineno" href="#L502">502</a></span>
<span id="L503" class="lineno"><a class="lineno" href="#L503">503</a></span>
<span id="L504" class="lineno"><a class="lineno" href="#L504">504</a></span>
<span id="L505" class="lineno"><a class="lineno" href="#L505">505</a></span>
<span id="L506" class="lineno"><a class="lineno" href="#L506">506</a></span>
<span id="L507" class="lineno"><a class="lineno" href="#L507">507</a></span>
<span id="L508" class="lineno"><a class="lineno" href="#L508">508</a></span>
<span id="L509" class="lineno"><a class="lineno" href="#L509">509</a></span>
<span id="L510" class="lineno"><a class="lineno" href="#L510">510</a></span>
<span id="L511" class="lineno"><a class="lineno" href="#L511">511</a></span>
<span id="L512" class="lineno"><a class="lineno" href="#L512">512</a></span>
<span id="L513" class="lineno"><a class="lineno" href="#L513">513</a></span>
<span id="L514" class="lineno"><a class="lineno" href="#L514">514</a></span>
<span id="L515" class="lineno"><a class="lineno" href="#L515">515</a></span>
<span id="L516" class="lineno"><a class="lineno" href="#L516">516</a></span>
<span id="L517" class="lineno"><a class="lineno" href="#L517">517</a></span>
<span id="L518" class="lineno"><a class="lineno" href="#L518">518</a></span>
<span id="L519" class="lineno"><a class="lineno" href="#L519">519</a></span>
<span id="L520" class="lineno"><a class="lineno" href="#L520">520</a></span>
<span id="L521" class="lineno"><a class="lineno" href="#L521">521</a></span>
<span id="L522" class="lineno"><a class="lineno" href="#L522">522</a></span>
<span id="L523" class="lineno"><a class="lineno" href="#L523">523</a></span>
<span id="L524" class="lineno"><a class="lineno" href="#L524">524</a></span>
<span id="L525" class="lineno"><a class="lineno" href="#L525">525</a></span>
<span id="L526" class="lineno"><a class="lineno" href="#L526">526</a></span>
<span id="L527" class="lineno"><a class="lineno" href="#L527">527</a></span>
<span id="L528" class="lineno"><a class="lineno" href="#L528">528</a></span>
<span id="L529" class="lineno"><a class="lineno" href="#L529">529</a></span>
<span id="L530" class="lineno"><a class="lineno" href="#L530">530</a></span>
<span id="L531" class="lineno"><a class="lineno" href="#L531">531</a></span>
<span id="L532" class="lineno"><a class="lineno" href="#L532">532</a></span>
<span id="L533" class="lineno"><a class="lineno" href="#L533">533</a></span>
<span id="L534" class="lineno"><a class="lineno" href="#L534">534</a></span>
<span id="L535" class="lineno"><a class="lineno" href="#L535">535</a></span>
<span id="L536" class="lineno"><a class="lineno" href="#L536">536</a></span>
<span id="L537" class="lineno"><a class="lineno" href="#L537">537</a></span>
<span id="L538" class="lineno"><a class="lineno" href="#L538">538</a></span>
<span id="L539" class="lineno"><a class="lineno" href="#L539">539</a></span>
<span id="L540" class="lineno"><a class="lineno" href="#L540">540</a></span>
<span id="L541" class="lineno"><a class="lineno" href="#L541">541</a></span>
</pre></td>
<td class="table-code"><pre><span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!">OpenAI API service integration.</span>
<span class="line-empty" title="No Anys on this line!">Handles AI content generation, analysis, and agent interactions.</span>
<span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">import asyncio</span>
<span class="line-precise" title="No Anys on this line!">from typing import Any, Dict, List, Optional, Union</span>
<span class="line-precise" title="No Anys on this line!">from datetime import datetime</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="No Anys on this line!">import openai</span>
<span class="line-any" title="No Anys on this line!">from openai import AsyncOpenAI</span>
<span class="line-any" title="No Anys on this line!">import structlog</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from .base import AuthenticatedService, CacheableService</span>
<span class="line-precise" title="No Anys on this line!">from utils.config import settings</span>
<span class="line-precise" title="No Anys on this line!">from utils.exceptions import OpenAIException</span>
<span class="line-precise" title="No Anys on this line!">from utils.helpers import retry_async</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class OpenAIService(AuthenticatedService, CacheableService):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Service for OpenAI API operations.</span>
<span class="line-empty" title="No Anys on this line!">    Handles text generation, analysis, and AI-powered features.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="No Anys on this line!">    def __init__(self):</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        super().__init__(</span>
<span class="line-any" title="No Anys on this line!">            service_name="OpenAI API",</span>
<span class="line-any" title="No Anys on this line!">            rate_limit_per_minute=3000,  # OpenAI rate limit (depends on tier)</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">            auth_token=settings.OPENAI_API_KEY,</span>
<span class="line-any" title="No Anys on this line!">            cache_ttl_seconds=1800,  # 30 minutes cache for expensive generations</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">        self._client: Optional[AsyncOpenAI] = None</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">        self._model = settings.OPENAI_MODEL</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Default model parameters</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        self._default_params = {</span>
<span class="line-any" title="No Anys on this line!">            "temperature": 0.7,</span>
<span class="line-any" title="No Anys on this line!">            "max_tokens": 2000,</span>
<span class="line-any" title="No Anys on this line!">            "top_p": 0.9,</span>
<span class="line-any" title="No Anys on this line!">            "frequency_penalty": 0.0,</span>
<span class="line-any" title="No Anys on this line!">            "presence_penalty": 0.0,</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _perform_authentication(self) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Initialize OpenAI client with API key."""</span>
<span class="line-precise" title="No Anys on this line!">        if not self._auth_token:</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x1)">            raise OpenAIException("OpenAI API key not configured")</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self._client = AsyncOpenAI(api_key=self._auth_token)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Test authentication with a simple request</span>
<span class="line-precise" title="No Anys on this line!">            await self._test_authentication()</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info("OpenAI client initialized successfully")</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("OpenAI authentication failed", error=str(e))</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x1)">            raise OpenAIException(f"Authentication failed: {str(e)}")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _test_authentication(self) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Test authentication by making a simple API call."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x5)">            models = await self._client.models.list()</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x6)">            available_models = [model.id for model in models.data]</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)
Unimported (x1)">            if self._model not in available_models:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                self.logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">                    "Configured model not available",</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                    configured_model=self._model,</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x2)">                    available_models=available_models[:5],  # Log first 5 models</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "OpenAI authentication test successful",</span>
<span class="line-imprecise" title="Any Types on this line: 
Unimported (x1)">                model_count=len(available_models),</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                configured_model=self._model,</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("OpenAI authentication test failed", error=str(e))</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x1)">            raise OpenAIException(f"Authentication test failed: {str(e)}")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _get_auth_headers(self) -&gt; Dict[str, str]:</span>
<span class="line-empty" title="No Anys on this line!">        """Get authentication headers (handled by OpenAI client)."""</span>
<span class="line-empty" title="No Anys on this line!">        return {}</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">    async def _execute_request(self, method: str, endpoint: str, **kwargs: Any) -&gt; Any:</span>
<span class="line-empty" title="No Anys on this line!">        """Execute OpenAI API request using the SDK."""</span>
<span class="line-empty" title="No Anys on this line!">        # This method is not used for OpenAI SDK</span>
<span class="line-empty" title="No Anys on this line!">        # Individual methods handle SDK calls directly</span>
<span class="line-precise" title="No Anys on this line!">        pass</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    async def health_check(self) -&gt; Dict[str, Any]:</span>
<span class="line-empty" title="No Anys on this line!">        """Perform OpenAI API health check."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            await self.authenticate()</span>
<span class="line-precise" title="No Anys on this line!">            await self._test_authentication()</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            return {</span>
<span class="line-precise" title="No Anys on this line!">                "status": "healthy",</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                "model": self._model,</span>
<span class="line-precise" title="No Anys on this line!">                "authenticated": True,</span>
<span class="line-precise" title="No Anys on this line!">                "last_check": datetime.utcnow().isoformat(),</span>
<span class="line-empty" title="No Anys on this line!">            }</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-empty" title="No Anys on this line!">            return {</span>
<span class="line-precise" title="No Anys on this line!">                "status": "unhealthy",</span>
<span class="line-precise" title="No Anys on this line!">                "error": str(e),</span>
<span class="line-precise" title="No Anys on this line!">                "authenticated": False,</span>
<span class="line-precise" title="No Anys on this line!">                "last_check": datetime.utcnow().isoformat(),</span>
<span class="line-empty" title="No Anys on this line!">            }</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">    async def generate_text(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        prompt: str,</span>
<span class="line-precise" title="No Anys on this line!">        model: Optional[str] = None,</span>
<span class="line-precise" title="No Anys on this line!">        temperature: Optional[float] = None,</span>
<span class="line-precise" title="No Anys on this line!">        max_tokens: Optional[int] = None,</span>
<span class="line-precise" title="No Anys on this line!">        system_message: Optional[str] = None,</span>
<span class="line-empty" title="No Anys on this line!">        **kwargs: Any</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; str:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Generate text using OpenAI's chat completion API.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            prompt: User prompt for text generation</span>
<span class="line-empty" title="No Anys on this line!">            model: Model to use (defaults to configured model)</span>
<span class="line-empty" title="No Anys on this line!">            temperature: Sampling temperature (0-2)</span>
<span class="line-empty" title="No Anys on this line!">            max_tokens: Maximum tokens to generate</span>
<span class="line-empty" title="No Anys on this line!">            system_message: System message to set context</span>
<span class="line-empty" title="No Anys on this line!">            **kwargs: Additional parameters</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            str: Generated text</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Raises:</span>
<span class="line-empty" title="No Anys on this line!">            OpenAIException: If text generation fails</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        await self.authenticate()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-empty" title="No Anys on this line!">            # Prepare parameters</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x4)">            params = self._default_params.copy()</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)
Explicit (x1)">            params.update(kwargs)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            if temperature is not None:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                params["temperature"] = temperature</span>
<span class="line-precise" title="No Anys on this line!">            if max_tokens is not None:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                params["max_tokens"] = max_tokens</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Prepare messages</span>
<span class="line-precise" title="No Anys on this line!">            messages = []</span>
<span class="line-precise" title="No Anys on this line!">            if system_message:</span>
<span class="line-precise" title="No Anys on this line!">                messages.append({"role": "system", "content": system_message})</span>
<span class="line-precise" title="No Anys on this line!">            messages.append({"role": "user", "content": prompt})</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Generating text with OpenAI",</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">                model=model or self._model,</span>
<span class="line-precise" title="No Anys on this line!">                prompt_length=len(prompt),</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">                temperature=params["temperature"],</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Make the API call</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x6)">            response = await self._client.chat.completions.create(</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">                model=model or self._model,</span>
<span class="line-precise" title="No Anys on this line!">                messages=messages,</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                **params</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x6)">            generated_text = response.choices[0].message.content</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Text generation successful",</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">                generated_length=len(generated_text),</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                tokens_used=response.usage.total_tokens,</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                prompt_tokens=response.usage.prompt_tokens,</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                completion_tokens=response.usage.completion_tokens,</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x1)">            return generated_text</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-precise" title="No Anys on this line!">            error_message = f"Text generation failed: {str(e)}"</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("OpenAI text generation failed", error=error_message)</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x1)">            raise OpenAIException(error_message)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">    async def generate_ad_copy(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        business_description: str,</span>
<span class="line-empty" title="No Anys on this line!">        target_keywords: List[str],</span>
<span class="line-empty" title="No Anys on this line!">        target_audience: str,</span>
<span class="line-precise" title="No Anys on this line!">        ad_type: str = "search",</span>
<span class="line-precise" title="No Anys on this line!">        num_variations: int = 3,</span>
<span class="line-empty" title="No Anys on this line!">        **kwargs: Any</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[Dict[str, str]]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Generate ad copy variations for Google Ads campaigns.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            business_description: Description of the business/service</span>
<span class="line-empty" title="No Anys on this line!">            target_keywords: List of target keywords</span>
<span class="line-empty" title="No Anys on this line!">            target_audience: Description of target audience</span>
<span class="line-empty" title="No Anys on this line!">            ad_type: Type of ad (search, display, etc.)</span>
<span class="line-empty" title="No Anys on this line!">            num_variations: Number of ad variations to generate</span>
<span class="line-empty" title="No Anys on this line!">            **kwargs: Additional parameters</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            List[Dict[str, str]]: List of ad copy variations</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Raises:</span>
<span class="line-empty" title="No Anys on this line!">            OpenAIException: If ad copy generation fails</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        await self.authenticate()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            system_message = f"""</span>
<span class="line-precise" title="No Anys on this line!">            You are an expert Google Ads copywriter specializing in {ad_type} campaigns.</span>
<span class="line-empty" title="No Anys on this line!">            Create compelling ad copy that:</span>
<span class="line-empty" title="No Anys on this line!">            1. Incorporates the target keywords naturally</span>
<span class="line-empty" title="No Anys on this line!">            2. Appeals to the target audience</span>
<span class="line-empty" title="No Anys on this line!">            3. Includes a strong call-to-action</span>
<span class="line-empty" title="No Anys on this line!">            4. Follows Google Ads policies and best practices</span>
<span class="line-empty" title="No Anys on this line!">            5. Is concise and impactful</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            For search ads, provide:</span>
<span class="line-empty" title="No Anys on this line!">            - Headlines (max 30 characters each, provide 3-5 variations)</span>
<span class="line-empty" title="No Anys on this line!">            - Descriptions (max 90 characters each, provide 2-3 variations)</span>
<span class="line-empty" title="No Anys on this line!">            - Display URL path (optional)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            Return the response as JSON with this structure:</span>
<span class="line-empty" title="No Anys on this line!">            {{</span>
<span class="line-empty" title="No Anys on this line!">                "ad_variations": [</span>
<span class="line-empty" title="No Anys on this line!">                    {{</span>
<span class="line-empty" title="No Anys on this line!">                        "headlines": ["headline1", "headline2", "headline3"],</span>
<span class="line-empty" title="No Anys on this line!">                        "descriptions": ["description1", "description2"],</span>
<span class="line-empty" title="No Anys on this line!">                        "display_path": "path-if-needed"</span>
<span class="line-empty" title="No Anys on this line!">                    }}</span>
<span class="line-empty" title="No Anys on this line!">                ]</span>
<span class="line-empty" title="No Anys on this line!">            }}</span>
<span class="line-empty" title="No Anys on this line!">            """</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            prompt = f"""</span>
<span class="line-precise" title="No Anys on this line!">            Business Description: {business_description}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            Target Keywords: {', '.join(target_keywords)}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            Target Audience: {target_audience}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            Please generate {num_variations} high-converting ad copy variations for this {ad_type} campaign.</span>
<span class="line-empty" title="No Anys on this line!">            Focus on the unique value proposition and what makes this business stand out.</span>
<span class="line-empty" title="No Anys on this line!">            """</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Generating ad copy",</span>
<span class="line-precise" title="No Anys on this line!">                ad_type=ad_type,</span>
<span class="line-precise" title="No Anys on this line!">                num_variations=num_variations,</span>
<span class="line-precise" title="No Anys on this line!">                keywords_count=len(target_keywords),</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">            response_text = await self.generate_text(</span>
<span class="line-precise" title="No Anys on this line!">                prompt=prompt,</span>
<span class="line-precise" title="No Anys on this line!">                system_message=system_message,</span>
<span class="line-precise" title="No Anys on this line!">                temperature=0.8,  # Higher creativity for ad copy</span>
<span class="line-precise" title="No Anys on this line!">                max_tokens=1500,</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Parse the JSON response</span>
<span class="line-precise" title="No Anys on this line!">            import json</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x13)
Unannotated (x1)">                response_data = json.loads(response_text)</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)">                ad_variations = response_data.get("ad_variations", [])</span>
<span class="line-precise" title="No Anys on this line!">            except json.JSONDecodeError:</span>
<span class="line-empty" title="No Anys on this line!">                # Fallback: extract ad copy manually if JSON parsing fails</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)
Explicit (x1)">                ad_variations = self._parse_ad_copy_fallback(response_text, num_variations)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Ad copy generation successful",</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                variations_generated=len(ad_variations),</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">            return ad_variations</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-precise" title="No Anys on this line!">            error_message = f"Ad copy generation failed: {str(e)}"</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("OpenAI ad copy generation failed", error=error_message)</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x1)">            raise OpenAIException(error_message)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _parse_ad_copy_fallback(self, text: str, num_variations: int) -&gt; List[Dict[str, str]]:</span>
<span class="line-empty" title="No Anys on this line!">        """Fallback method to parse ad copy when JSON parsing fails."""</span>
<span class="line-empty" title="No Anys on this line!">        # Simple fallback implementation</span>
<span class="line-empty" title="No Anys on this line!">        # In production, this would be more sophisticated</span>
<span class="line-precise" title="No Anys on this line!">        lines = text.strip().split('\n')</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        variations = []</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">        for i in range(min(num_variations, 3)):  # Limit fallback to 3 variations</span>
<span class="line-precise" title="No Anys on this line!">            variations.append({</span>
<span class="line-precise" title="No Anys on this line!">                "headlines": [f"Generated Headline {i+1}"],</span>
<span class="line-precise" title="No Anys on this line!">                "descriptions": [f"Generated Description {i+1}"],</span>
<span class="line-precise" title="No Anys on this line!">                "display_path": ""</span>
<span class="line-empty" title="No Anys on this line!">            })</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return variations</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">    async def analyze_campaign_performance(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        campaign_data: Dict[str, Any],</span>
<span class="line-empty" title="No Anys on this line!">        metrics: Dict[str, float],</span>
<span class="line-precise" title="No Anys on this line!">        context: Optional[str] = None,</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; Dict[str, Any]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Analyze campaign performance and provide insights.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            campaign_data: Campaign configuration and details</span>
<span class="line-empty" title="No Anys on this line!">            metrics: Performance metrics</span>
<span class="line-empty" title="No Anys on this line!">            context: Additional context for analysis</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            Dict[str, Any]: Analysis results with insights and recommendations</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Raises:</span>
<span class="line-empty" title="No Anys on this line!">            OpenAIException: If analysis fails</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        await self.authenticate()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            system_message = """</span>
<span class="line-empty" title="No Anys on this line!">            You are an expert Google Ads performance analyst. Analyze the provided campaign data and metrics</span>
<span class="line-empty" title="No Anys on this line!">            to identify:</span>
<span class="line-empty" title="No Anys on this line!">            1. Performance strengths and weaknesses</span>
<span class="line-empty" title="No Anys on this line!">            2. Optimization opportunities</span>
<span class="line-empty" title="No Anys on this line!">            3. Specific actionable recommendations</span>
<span class="line-empty" title="No Anys on this line!">            4. Potential issues or concerns</span>
<span class="line-empty" title="No Anys on this line!">            5. Benchmarking against industry standards</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            Provide your analysis in JSON format:</span>
<span class="line-empty" title="No Anys on this line!">            {</span>
<span class="line-empty" title="No Anys on this line!">                "overall_performance": "excellent|good|average|poor",</span>
<span class="line-empty" title="No Anys on this line!">                "key_insights": ["insight1", "insight2", "insight3"],</span>
<span class="line-empty" title="No Anys on this line!">                "recommendations": [</span>
<span class="line-empty" title="No Anys on this line!">                    {</span>
<span class="line-empty" title="No Anys on this line!">                        "type": "optimization_type",</span>
<span class="line-empty" title="No Anys on this line!">                        "priority": "high|medium|low",</span>
<span class="line-empty" title="No Anys on this line!">                        "description": "detailed_recommendation",</span>
<span class="line-empty" title="No Anys on this line!">                        "estimated_impact": "impact_description"</span>
<span class="line-empty" title="No Anys on this line!">                    }</span>
<span class="line-empty" title="No Anys on this line!">                ],</span>
<span class="line-empty" title="No Anys on this line!">                "concerns": ["concern1", "concern2"],</span>
<span class="line-empty" title="No Anys on this line!">                "strengths": ["strength1", "strength2"]</span>
<span class="line-empty" title="No Anys on this line!">            }</span>
<span class="line-empty" title="No Anys on this line!">            """</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            prompt = f"""</span>
<span class="line-empty" title="No Anys on this line!">            Campaign Data:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x5)">            {json.dumps(campaign_data, indent=2)}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            Performance Metrics:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)">            {json.dumps(metrics, indent=2)}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            Additional Context: {context or "None provided"}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            Please provide a comprehensive analysis of this campaign's performance with actionable insights.</span>
<span class="line-empty" title="No Anys on this line!">            """</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Analyzing campaign performance",</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x6)
Omitted Generics (x2)">                campaign_id=campaign_data.get("id", "unknown"),</span>
<span class="line-precise" title="No Anys on this line!">                metrics_count=len(metrics),</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">            response_text = await self.generate_text(</span>
<span class="line-precise" title="No Anys on this line!">                prompt=prompt,</span>
<span class="line-precise" title="No Anys on this line!">                system_message=system_message,</span>
<span class="line-precise" title="No Anys on this line!">                temperature=0.3,  # Lower temperature for analytical tasks</span>
<span class="line-precise" title="No Anys on this line!">                max_tokens=2000,</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Parse the JSON response</span>
<span class="line-precise" title="No Anys on this line!">            import json</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x13)
Unannotated (x1)">                analysis_result = json.loads(response_text)</span>
<span class="line-precise" title="No Anys on this line!">            except json.JSONDecodeError:</span>
<span class="line-empty" title="No Anys on this line!">                # Fallback structure if JSON parsing fails</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                analysis_result = {</span>
<span class="line-precise" title="No Anys on this line!">                    "overall_performance": "unknown",</span>
<span class="line-precise" title="No Anys on this line!">                    "key_insights": ["Analysis parsing failed, manual review needed"],</span>
<span class="line-precise" title="No Anys on this line!">                    "recommendations": [],</span>
<span class="line-precise" title="No Anys on this line!">                    "concerns": ["Unable to parse AI analysis"],</span>
<span class="line-precise" title="No Anys on this line!">                    "strengths": []</span>
<span class="line-empty" title="No Anys on this line!">                }</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Campaign performance analysis successful",</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                overall_performance=analysis_result.get("overall_performance"),</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                insights_count=len(analysis_result.get("key_insights", [])),</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">                recommendations_count=len(analysis_result.get("recommendations", [])),</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">            return analysis_result</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-precise" title="No Anys on this line!">            error_message = f"Campaign performance analysis failed: {str(e)}"</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("OpenAI performance analysis failed", error=error_message)</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x1)">            raise OpenAIException(error_message)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">    async def generate_keywords(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        business_description: str,</span>
<span class="line-empty" title="No Anys on this line!">        target_audience: str,</span>
<span class="line-empty" title="No Anys on this line!">        industry: str,</span>
<span class="line-precise" title="No Anys on this line!">        location: Optional[str] = None,</span>
<span class="line-precise" title="No Anys on this line!">        max_keywords: int = 50,</span>
<span class="line-empty" title="No Anys on this line!">        **kwargs: Any</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; List[Dict[str, Any]]:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Generate keyword suggestions for campaigns.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            business_description: Description of the business/service</span>
<span class="line-empty" title="No Anys on this line!">            target_audience: Target audience description</span>
<span class="line-empty" title="No Anys on this line!">            industry: Industry category</span>
<span class="line-empty" title="No Anys on this line!">            location: Geographic location (optional)</span>
<span class="line-empty" title="No Anys on this line!">            max_keywords: Maximum number of keywords to generate</span>
<span class="line-empty" title="No Anys on this line!">            **kwargs: Additional parameters</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Returns:</span>
<span class="line-empty" title="No Anys on this line!">            List[Dict[str, Any]]: List of keyword suggestions with metadata</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">        Raises:</span>
<span class="line-empty" title="No Anys on this line!">            OpenAIException: If keyword generation fails</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-precise" title="No Anys on this line!">        await self.authenticate()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-precise" title="No Anys on this line!">            system_message = """</span>
<span class="line-empty" title="No Anys on this line!">            You are an expert keyword researcher for Google Ads campaigns. Generate relevant, high-intent</span>
<span class="line-empty" title="No Anys on this line!">            keywords that potential customers would use to find this business.</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            Consider:</span>
<span class="line-empty" title="No Anys on this line!">            1. Different keyword match types and their applications</span>
<span class="line-empty" title="No Anys on this line!">            2. Search intent (informational, navigational, transactional)</span>
<span class="line-empty" title="No Anys on this line!">            3. Competition level estimates</span>
<span class="line-empty" title="No Anys on this line!">            4. Local vs. national targeting</span>
<span class="line-empty" title="No Anys on this line!">            5. Long-tail and short-tail variations</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            Return results in JSON format:</span>
<span class="line-empty" title="No Anys on this line!">            {</span>
<span class="line-empty" title="No Anys on this line!">                "keywords": [</span>
<span class="line-empty" title="No Anys on this line!">                    {</span>
<span class="line-empty" title="No Anys on this line!">                        "keyword": "keyword phrase",</span>
<span class="line-empty" title="No Anys on this line!">                        "match_type": "exact|phrase|broad",</span>
<span class="line-empty" title="No Anys on this line!">                        "intent": "informational|navigational|transactional",</span>
<span class="line-empty" title="No Anys on this line!">                        "competition": "low|medium|high",</span>
<span class="line-empty" title="No Anys on this line!">                        "relevance_score": 0.95,</span>
<span class="line-empty" title="No Anys on this line!">                        "category": "primary|secondary|long-tail"</span>
<span class="line-empty" title="No Anys on this line!">                    }</span>
<span class="line-empty" title="No Anys on this line!">                ]</span>
<span class="line-empty" title="No Anys on this line!">            }</span>
<span class="line-empty" title="No Anys on this line!">            """</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            location_context = f"\nLocation: {location}" if location else ""</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            prompt = f"""</span>
<span class="line-precise" title="No Anys on this line!">            Business Description: {business_description}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            Target Audience: {target_audience}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            Industry: {industry}{location_context}</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            Generate up to {max_keywords} relevant keywords for a Google Ads campaign.</span>
<span class="line-empty" title="No Anys on this line!">            Focus on keywords that indicate purchase intent and relevance to the business.</span>
<span class="line-empty" title="No Anys on this line!">            Include a mix of broad, phrase, and exact match keywords.</span>
<span class="line-empty" title="No Anys on this line!">            """</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Generating keywords",</span>
<span class="line-precise" title="No Anys on this line!">                industry=industry,</span>
<span class="line-precise" title="No Anys on this line!">                max_keywords=max_keywords,</span>
<span class="line-precise" title="No Anys on this line!">                location=location,</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">            response_text = await self.generate_text(</span>
<span class="line-precise" title="No Anys on this line!">                prompt=prompt,</span>
<span class="line-precise" title="No Anys on this line!">                system_message=system_message,</span>
<span class="line-precise" title="No Anys on this line!">                temperature=0.6,</span>
<span class="line-precise" title="No Anys on this line!">                max_tokens=2500,</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Parse the JSON response</span>
<span class="line-precise" title="No Anys on this line!">            import json</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x13)
Unannotated (x1)">                response_data = json.loads(response_text)</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)">                keywords = response_data.get("keywords", [])</span>
<span class="line-precise" title="No Anys on this line!">            except json.JSONDecodeError:</span>
<span class="line-empty" title="No Anys on this line!">                # Fallback: extract keywords manually</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)
Unannotated (x1)">                keywords = self._parse_keywords_fallback(response_text, max_keywords)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Keyword generation successful",</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                keywords_generated=len(keywords),</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">            return keywords[:max_keywords]  # Ensure we don't exceed the limit</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-precise" title="No Anys on this line!">            error_message = f"Keyword generation failed: {str(e)}"</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            self.logger.error("OpenAI keyword generation failed", error=error_message)</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x1)">            raise OpenAIException(error_message)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    def _parse_keywords_fallback(self, text: str, max_keywords: int) -&gt; List[Dict[str, Any]]:</span>
<span class="line-empty" title="No Anys on this line!">        """Fallback method to parse keywords when JSON parsing fails."""</span>
<span class="line-precise" title="No Anys on this line!">        keywords = []</span>
<span class="line-precise" title="No Anys on this line!">        lines = text.strip().split('\n')</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        for line in lines[:max_keywords]:</span>
<span class="line-precise" title="No Anys on this line!">            if line.strip() and not line.startswith('#'):</span>
<span class="line-precise" title="No Anys on this line!">                keyword_text = line.strip().strip('-').strip('*').strip()</span>
<span class="line-precise" title="No Anys on this line!">                if keyword_text:</span>
<span class="line-precise" title="No Anys on this line!">                    keywords.append({</span>
<span class="line-precise" title="No Anys on this line!">                        "keyword": keyword_text,</span>
<span class="line-precise" title="No Anys on this line!">                        "match_type": "phrase",</span>
<span class="line-precise" title="No Anys on this line!">                        "intent": "transactional",</span>
<span class="line-precise" title="No Anys on this line!">                        "competition": "medium",</span>
<span class="line-precise" title="No Anys on this line!">                        "relevance_score": 0.7,</span>
<span class="line-precise" title="No Anys on this line!">                        "category": "primary"</span>
<span class="line-empty" title="No Anys on this line!">                    })</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return keywords</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"># Global OpenAI service instance</span>
<span class="line-precise" title="No Anys on this line!">openai_service = OpenAIService()</span>
</pre></td>
</tr></tbody>
</table>
</body>
</html>
