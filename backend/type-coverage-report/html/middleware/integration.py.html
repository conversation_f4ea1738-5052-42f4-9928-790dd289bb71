<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../mypy-html.css">
</head>
<body>
<h2>middleware.integration</h2>
<table>
<caption>middleware/integration.py</caption>
<tbody><tr>
<td class="table-lines"><pre><span id="L1" class="lineno"><a class="lineno" href="#L1">1</a></span>
<span id="L2" class="lineno"><a class="lineno" href="#L2">2</a></span>
<span id="L3" class="lineno"><a class="lineno" href="#L3">3</a></span>
<span id="L4" class="lineno"><a class="lineno" href="#L4">4</a></span>
<span id="L5" class="lineno"><a class="lineno" href="#L5">5</a></span>
<span id="L6" class="lineno"><a class="lineno" href="#L6">6</a></span>
<span id="L7" class="lineno"><a class="lineno" href="#L7">7</a></span>
<span id="L8" class="lineno"><a class="lineno" href="#L8">8</a></span>
<span id="L9" class="lineno"><a class="lineno" href="#L9">9</a></span>
<span id="L10" class="lineno"><a class="lineno" href="#L10">10</a></span>
<span id="L11" class="lineno"><a class="lineno" href="#L11">11</a></span>
<span id="L12" class="lineno"><a class="lineno" href="#L12">12</a></span>
<span id="L13" class="lineno"><a class="lineno" href="#L13">13</a></span>
<span id="L14" class="lineno"><a class="lineno" href="#L14">14</a></span>
<span id="L15" class="lineno"><a class="lineno" href="#L15">15</a></span>
<span id="L16" class="lineno"><a class="lineno" href="#L16">16</a></span>
<span id="L17" class="lineno"><a class="lineno" href="#L17">17</a></span>
<span id="L18" class="lineno"><a class="lineno" href="#L18">18</a></span>
<span id="L19" class="lineno"><a class="lineno" href="#L19">19</a></span>
<span id="L20" class="lineno"><a class="lineno" href="#L20">20</a></span>
<span id="L21" class="lineno"><a class="lineno" href="#L21">21</a></span>
<span id="L22" class="lineno"><a class="lineno" href="#L22">22</a></span>
<span id="L23" class="lineno"><a class="lineno" href="#L23">23</a></span>
<span id="L24" class="lineno"><a class="lineno" href="#L24">24</a></span>
<span id="L25" class="lineno"><a class="lineno" href="#L25">25</a></span>
<span id="L26" class="lineno"><a class="lineno" href="#L26">26</a></span>
<span id="L27" class="lineno"><a class="lineno" href="#L27">27</a></span>
<span id="L28" class="lineno"><a class="lineno" href="#L28">28</a></span>
<span id="L29" class="lineno"><a class="lineno" href="#L29">29</a></span>
<span id="L30" class="lineno"><a class="lineno" href="#L30">30</a></span>
<span id="L31" class="lineno"><a class="lineno" href="#L31">31</a></span>
<span id="L32" class="lineno"><a class="lineno" href="#L32">32</a></span>
<span id="L33" class="lineno"><a class="lineno" href="#L33">33</a></span>
<span id="L34" class="lineno"><a class="lineno" href="#L34">34</a></span>
<span id="L35" class="lineno"><a class="lineno" href="#L35">35</a></span>
<span id="L36" class="lineno"><a class="lineno" href="#L36">36</a></span>
<span id="L37" class="lineno"><a class="lineno" href="#L37">37</a></span>
<span id="L38" class="lineno"><a class="lineno" href="#L38">38</a></span>
<span id="L39" class="lineno"><a class="lineno" href="#L39">39</a></span>
<span id="L40" class="lineno"><a class="lineno" href="#L40">40</a></span>
<span id="L41" class="lineno"><a class="lineno" href="#L41">41</a></span>
<span id="L42" class="lineno"><a class="lineno" href="#L42">42</a></span>
<span id="L43" class="lineno"><a class="lineno" href="#L43">43</a></span>
<span id="L44" class="lineno"><a class="lineno" href="#L44">44</a></span>
<span id="L45" class="lineno"><a class="lineno" href="#L45">45</a></span>
<span id="L46" class="lineno"><a class="lineno" href="#L46">46</a></span>
<span id="L47" class="lineno"><a class="lineno" href="#L47">47</a></span>
<span id="L48" class="lineno"><a class="lineno" href="#L48">48</a></span>
<span id="L49" class="lineno"><a class="lineno" href="#L49">49</a></span>
<span id="L50" class="lineno"><a class="lineno" href="#L50">50</a></span>
<span id="L51" class="lineno"><a class="lineno" href="#L51">51</a></span>
<span id="L52" class="lineno"><a class="lineno" href="#L52">52</a></span>
<span id="L53" class="lineno"><a class="lineno" href="#L53">53</a></span>
<span id="L54" class="lineno"><a class="lineno" href="#L54">54</a></span>
<span id="L55" class="lineno"><a class="lineno" href="#L55">55</a></span>
<span id="L56" class="lineno"><a class="lineno" href="#L56">56</a></span>
<span id="L57" class="lineno"><a class="lineno" href="#L57">57</a></span>
<span id="L58" class="lineno"><a class="lineno" href="#L58">58</a></span>
<span id="L59" class="lineno"><a class="lineno" href="#L59">59</a></span>
<span id="L60" class="lineno"><a class="lineno" href="#L60">60</a></span>
<span id="L61" class="lineno"><a class="lineno" href="#L61">61</a></span>
<span id="L62" class="lineno"><a class="lineno" href="#L62">62</a></span>
<span id="L63" class="lineno"><a class="lineno" href="#L63">63</a></span>
<span id="L64" class="lineno"><a class="lineno" href="#L64">64</a></span>
<span id="L65" class="lineno"><a class="lineno" href="#L65">65</a></span>
<span id="L66" class="lineno"><a class="lineno" href="#L66">66</a></span>
<span id="L67" class="lineno"><a class="lineno" href="#L67">67</a></span>
<span id="L68" class="lineno"><a class="lineno" href="#L68">68</a></span>
<span id="L69" class="lineno"><a class="lineno" href="#L69">69</a></span>
<span id="L70" class="lineno"><a class="lineno" href="#L70">70</a></span>
<span id="L71" class="lineno"><a class="lineno" href="#L71">71</a></span>
<span id="L72" class="lineno"><a class="lineno" href="#L72">72</a></span>
<span id="L73" class="lineno"><a class="lineno" href="#L73">73</a></span>
<span id="L74" class="lineno"><a class="lineno" href="#L74">74</a></span>
<span id="L75" class="lineno"><a class="lineno" href="#L75">75</a></span>
<span id="L76" class="lineno"><a class="lineno" href="#L76">76</a></span>
<span id="L77" class="lineno"><a class="lineno" href="#L77">77</a></span>
<span id="L78" class="lineno"><a class="lineno" href="#L78">78</a></span>
<span id="L79" class="lineno"><a class="lineno" href="#L79">79</a></span>
<span id="L80" class="lineno"><a class="lineno" href="#L80">80</a></span>
<span id="L81" class="lineno"><a class="lineno" href="#L81">81</a></span>
<span id="L82" class="lineno"><a class="lineno" href="#L82">82</a></span>
<span id="L83" class="lineno"><a class="lineno" href="#L83">83</a></span>
<span id="L84" class="lineno"><a class="lineno" href="#L84">84</a></span>
<span id="L85" class="lineno"><a class="lineno" href="#L85">85</a></span>
<span id="L86" class="lineno"><a class="lineno" href="#L86">86</a></span>
<span id="L87" class="lineno"><a class="lineno" href="#L87">87</a></span>
<span id="L88" class="lineno"><a class="lineno" href="#L88">88</a></span>
<span id="L89" class="lineno"><a class="lineno" href="#L89">89</a></span>
<span id="L90" class="lineno"><a class="lineno" href="#L90">90</a></span>
<span id="L91" class="lineno"><a class="lineno" href="#L91">91</a></span>
<span id="L92" class="lineno"><a class="lineno" href="#L92">92</a></span>
<span id="L93" class="lineno"><a class="lineno" href="#L93">93</a></span>
<span id="L94" class="lineno"><a class="lineno" href="#L94">94</a></span>
<span id="L95" class="lineno"><a class="lineno" href="#L95">95</a></span>
<span id="L96" class="lineno"><a class="lineno" href="#L96">96</a></span>
<span id="L97" class="lineno"><a class="lineno" href="#L97">97</a></span>
<span id="L98" class="lineno"><a class="lineno" href="#L98">98</a></span>
<span id="L99" class="lineno"><a class="lineno" href="#L99">99</a></span>
<span id="L100" class="lineno"><a class="lineno" href="#L100">100</a></span>
<span id="L101" class="lineno"><a class="lineno" href="#L101">101</a></span>
<span id="L102" class="lineno"><a class="lineno" href="#L102">102</a></span>
<span id="L103" class="lineno"><a class="lineno" href="#L103">103</a></span>
<span id="L104" class="lineno"><a class="lineno" href="#L104">104</a></span>
<span id="L105" class="lineno"><a class="lineno" href="#L105">105</a></span>
<span id="L106" class="lineno"><a class="lineno" href="#L106">106</a></span>
<span id="L107" class="lineno"><a class="lineno" href="#L107">107</a></span>
<span id="L108" class="lineno"><a class="lineno" href="#L108">108</a></span>
<span id="L109" class="lineno"><a class="lineno" href="#L109">109</a></span>
<span id="L110" class="lineno"><a class="lineno" href="#L110">110</a></span>
<span id="L111" class="lineno"><a class="lineno" href="#L111">111</a></span>
<span id="L112" class="lineno"><a class="lineno" href="#L112">112</a></span>
<span id="L113" class="lineno"><a class="lineno" href="#L113">113</a></span>
<span id="L114" class="lineno"><a class="lineno" href="#L114">114</a></span>
<span id="L115" class="lineno"><a class="lineno" href="#L115">115</a></span>
<span id="L116" class="lineno"><a class="lineno" href="#L116">116</a></span>
<span id="L117" class="lineno"><a class="lineno" href="#L117">117</a></span>
<span id="L118" class="lineno"><a class="lineno" href="#L118">118</a></span>
<span id="L119" class="lineno"><a class="lineno" href="#L119">119</a></span>
<span id="L120" class="lineno"><a class="lineno" href="#L120">120</a></span>
<span id="L121" class="lineno"><a class="lineno" href="#L121">121</a></span>
<span id="L122" class="lineno"><a class="lineno" href="#L122">122</a></span>
<span id="L123" class="lineno"><a class="lineno" href="#L123">123</a></span>
<span id="L124" class="lineno"><a class="lineno" href="#L124">124</a></span>
<span id="L125" class="lineno"><a class="lineno" href="#L125">125</a></span>
<span id="L126" class="lineno"><a class="lineno" href="#L126">126</a></span>
<span id="L127" class="lineno"><a class="lineno" href="#L127">127</a></span>
<span id="L128" class="lineno"><a class="lineno" href="#L128">128</a></span>
<span id="L129" class="lineno"><a class="lineno" href="#L129">129</a></span>
<span id="L130" class="lineno"><a class="lineno" href="#L130">130</a></span>
<span id="L131" class="lineno"><a class="lineno" href="#L131">131</a></span>
<span id="L132" class="lineno"><a class="lineno" href="#L132">132</a></span>
<span id="L133" class="lineno"><a class="lineno" href="#L133">133</a></span>
<span id="L134" class="lineno"><a class="lineno" href="#L134">134</a></span>
<span id="L135" class="lineno"><a class="lineno" href="#L135">135</a></span>
<span id="L136" class="lineno"><a class="lineno" href="#L136">136</a></span>
<span id="L137" class="lineno"><a class="lineno" href="#L137">137</a></span>
<span id="L138" class="lineno"><a class="lineno" href="#L138">138</a></span>
<span id="L139" class="lineno"><a class="lineno" href="#L139">139</a></span>
<span id="L140" class="lineno"><a class="lineno" href="#L140">140</a></span>
<span id="L141" class="lineno"><a class="lineno" href="#L141">141</a></span>
<span id="L142" class="lineno"><a class="lineno" href="#L142">142</a></span>
<span id="L143" class="lineno"><a class="lineno" href="#L143">143</a></span>
<span id="L144" class="lineno"><a class="lineno" href="#L144">144</a></span>
<span id="L145" class="lineno"><a class="lineno" href="#L145">145</a></span>
<span id="L146" class="lineno"><a class="lineno" href="#L146">146</a></span>
<span id="L147" class="lineno"><a class="lineno" href="#L147">147</a></span>
<span id="L148" class="lineno"><a class="lineno" href="#L148">148</a></span>
<span id="L149" class="lineno"><a class="lineno" href="#L149">149</a></span>
<span id="L150" class="lineno"><a class="lineno" href="#L150">150</a></span>
<span id="L151" class="lineno"><a class="lineno" href="#L151">151</a></span>
<span id="L152" class="lineno"><a class="lineno" href="#L152">152</a></span>
<span id="L153" class="lineno"><a class="lineno" href="#L153">153</a></span>
<span id="L154" class="lineno"><a class="lineno" href="#L154">154</a></span>
<span id="L155" class="lineno"><a class="lineno" href="#L155">155</a></span>
<span id="L156" class="lineno"><a class="lineno" href="#L156">156</a></span>
<span id="L157" class="lineno"><a class="lineno" href="#L157">157</a></span>
<span id="L158" class="lineno"><a class="lineno" href="#L158">158</a></span>
<span id="L159" class="lineno"><a class="lineno" href="#L159">159</a></span>
<span id="L160" class="lineno"><a class="lineno" href="#L160">160</a></span>
<span id="L161" class="lineno"><a class="lineno" href="#L161">161</a></span>
<span id="L162" class="lineno"><a class="lineno" href="#L162">162</a></span>
<span id="L163" class="lineno"><a class="lineno" href="#L163">163</a></span>
<span id="L164" class="lineno"><a class="lineno" href="#L164">164</a></span>
<span id="L165" class="lineno"><a class="lineno" href="#L165">165</a></span>
<span id="L166" class="lineno"><a class="lineno" href="#L166">166</a></span>
<span id="L167" class="lineno"><a class="lineno" href="#L167">167</a></span>
<span id="L168" class="lineno"><a class="lineno" href="#L168">168</a></span>
<span id="L169" class="lineno"><a class="lineno" href="#L169">169</a></span>
<span id="L170" class="lineno"><a class="lineno" href="#L170">170</a></span>
<span id="L171" class="lineno"><a class="lineno" href="#L171">171</a></span>
<span id="L172" class="lineno"><a class="lineno" href="#L172">172</a></span>
<span id="L173" class="lineno"><a class="lineno" href="#L173">173</a></span>
<span id="L174" class="lineno"><a class="lineno" href="#L174">174</a></span>
<span id="L175" class="lineno"><a class="lineno" href="#L175">175</a></span>
<span id="L176" class="lineno"><a class="lineno" href="#L176">176</a></span>
<span id="L177" class="lineno"><a class="lineno" href="#L177">177</a></span>
<span id="L178" class="lineno"><a class="lineno" href="#L178">178</a></span>
<span id="L179" class="lineno"><a class="lineno" href="#L179">179</a></span>
<span id="L180" class="lineno"><a class="lineno" href="#L180">180</a></span>
<span id="L181" class="lineno"><a class="lineno" href="#L181">181</a></span>
<span id="L182" class="lineno"><a class="lineno" href="#L182">182</a></span>
<span id="L183" class="lineno"><a class="lineno" href="#L183">183</a></span>
<span id="L184" class="lineno"><a class="lineno" href="#L184">184</a></span>
<span id="L185" class="lineno"><a class="lineno" href="#L185">185</a></span>
<span id="L186" class="lineno"><a class="lineno" href="#L186">186</a></span>
<span id="L187" class="lineno"><a class="lineno" href="#L187">187</a></span>
<span id="L188" class="lineno"><a class="lineno" href="#L188">188</a></span>
<span id="L189" class="lineno"><a class="lineno" href="#L189">189</a></span>
<span id="L190" class="lineno"><a class="lineno" href="#L190">190</a></span>
<span id="L191" class="lineno"><a class="lineno" href="#L191">191</a></span>
<span id="L192" class="lineno"><a class="lineno" href="#L192">192</a></span>
<span id="L193" class="lineno"><a class="lineno" href="#L193">193</a></span>
<span id="L194" class="lineno"><a class="lineno" href="#L194">194</a></span>
<span id="L195" class="lineno"><a class="lineno" href="#L195">195</a></span>
<span id="L196" class="lineno"><a class="lineno" href="#L196">196</a></span>
<span id="L197" class="lineno"><a class="lineno" href="#L197">197</a></span>
<span id="L198" class="lineno"><a class="lineno" href="#L198">198</a></span>
<span id="L199" class="lineno"><a class="lineno" href="#L199">199</a></span>
<span id="L200" class="lineno"><a class="lineno" href="#L200">200</a></span>
<span id="L201" class="lineno"><a class="lineno" href="#L201">201</a></span>
<span id="L202" class="lineno"><a class="lineno" href="#L202">202</a></span>
<span id="L203" class="lineno"><a class="lineno" href="#L203">203</a></span>
<span id="L204" class="lineno"><a class="lineno" href="#L204">204</a></span>
<span id="L205" class="lineno"><a class="lineno" href="#L205">205</a></span>
<span id="L206" class="lineno"><a class="lineno" href="#L206">206</a></span>
<span id="L207" class="lineno"><a class="lineno" href="#L207">207</a></span>
<span id="L208" class="lineno"><a class="lineno" href="#L208">208</a></span>
<span id="L209" class="lineno"><a class="lineno" href="#L209">209</a></span>
<span id="L210" class="lineno"><a class="lineno" href="#L210">210</a></span>
<span id="L211" class="lineno"><a class="lineno" href="#L211">211</a></span>
<span id="L212" class="lineno"><a class="lineno" href="#L212">212</a></span>
<span id="L213" class="lineno"><a class="lineno" href="#L213">213</a></span>
<span id="L214" class="lineno"><a class="lineno" href="#L214">214</a></span>
<span id="L215" class="lineno"><a class="lineno" href="#L215">215</a></span>
<span id="L216" class="lineno"><a class="lineno" href="#L216">216</a></span>
<span id="L217" class="lineno"><a class="lineno" href="#L217">217</a></span>
<span id="L218" class="lineno"><a class="lineno" href="#L218">218</a></span>
<span id="L219" class="lineno"><a class="lineno" href="#L219">219</a></span>
<span id="L220" class="lineno"><a class="lineno" href="#L220">220</a></span>
<span id="L221" class="lineno"><a class="lineno" href="#L221">221</a></span>
<span id="L222" class="lineno"><a class="lineno" href="#L222">222</a></span>
<span id="L223" class="lineno"><a class="lineno" href="#L223">223</a></span>
<span id="L224" class="lineno"><a class="lineno" href="#L224">224</a></span>
<span id="L225" class="lineno"><a class="lineno" href="#L225">225</a></span>
<span id="L226" class="lineno"><a class="lineno" href="#L226">226</a></span>
<span id="L227" class="lineno"><a class="lineno" href="#L227">227</a></span>
<span id="L228" class="lineno"><a class="lineno" href="#L228">228</a></span>
<span id="L229" class="lineno"><a class="lineno" href="#L229">229</a></span>
<span id="L230" class="lineno"><a class="lineno" href="#L230">230</a></span>
<span id="L231" class="lineno"><a class="lineno" href="#L231">231</a></span>
<span id="L232" class="lineno"><a class="lineno" href="#L232">232</a></span>
<span id="L233" class="lineno"><a class="lineno" href="#L233">233</a></span>
<span id="L234" class="lineno"><a class="lineno" href="#L234">234</a></span>
<span id="L235" class="lineno"><a class="lineno" href="#L235">235</a></span>
<span id="L236" class="lineno"><a class="lineno" href="#L236">236</a></span>
<span id="L237" class="lineno"><a class="lineno" href="#L237">237</a></span>
<span id="L238" class="lineno"><a class="lineno" href="#L238">238</a></span>
<span id="L239" class="lineno"><a class="lineno" href="#L239">239</a></span>
<span id="L240" class="lineno"><a class="lineno" href="#L240">240</a></span>
<span id="L241" class="lineno"><a class="lineno" href="#L241">241</a></span>
<span id="L242" class="lineno"><a class="lineno" href="#L242">242</a></span>
<span id="L243" class="lineno"><a class="lineno" href="#L243">243</a></span>
<span id="L244" class="lineno"><a class="lineno" href="#L244">244</a></span>
<span id="L245" class="lineno"><a class="lineno" href="#L245">245</a></span>
<span id="L246" class="lineno"><a class="lineno" href="#L246">246</a></span>
<span id="L247" class="lineno"><a class="lineno" href="#L247">247</a></span>
<span id="L248" class="lineno"><a class="lineno" href="#L248">248</a></span>
<span id="L249" class="lineno"><a class="lineno" href="#L249">249</a></span>
<span id="L250" class="lineno"><a class="lineno" href="#L250">250</a></span>
<span id="L251" class="lineno"><a class="lineno" href="#L251">251</a></span>
<span id="L252" class="lineno"><a class="lineno" href="#L252">252</a></span>
<span id="L253" class="lineno"><a class="lineno" href="#L253">253</a></span>
<span id="L254" class="lineno"><a class="lineno" href="#L254">254</a></span>
<span id="L255" class="lineno"><a class="lineno" href="#L255">255</a></span>
<span id="L256" class="lineno"><a class="lineno" href="#L256">256</a></span>
<span id="L257" class="lineno"><a class="lineno" href="#L257">257</a></span>
<span id="L258" class="lineno"><a class="lineno" href="#L258">258</a></span>
<span id="L259" class="lineno"><a class="lineno" href="#L259">259</a></span>
<span id="L260" class="lineno"><a class="lineno" href="#L260">260</a></span>
<span id="L261" class="lineno"><a class="lineno" href="#L261">261</a></span>
<span id="L262" class="lineno"><a class="lineno" href="#L262">262</a></span>
<span id="L263" class="lineno"><a class="lineno" href="#L263">263</a></span>
<span id="L264" class="lineno"><a class="lineno" href="#L264">264</a></span>
<span id="L265" class="lineno"><a class="lineno" href="#L265">265</a></span>
<span id="L266" class="lineno"><a class="lineno" href="#L266">266</a></span>
<span id="L267" class="lineno"><a class="lineno" href="#L267">267</a></span>
<span id="L268" class="lineno"><a class="lineno" href="#L268">268</a></span>
<span id="L269" class="lineno"><a class="lineno" href="#L269">269</a></span>
<span id="L270" class="lineno"><a class="lineno" href="#L270">270</a></span>
<span id="L271" class="lineno"><a class="lineno" href="#L271">271</a></span>
<span id="L272" class="lineno"><a class="lineno" href="#L272">272</a></span>
<span id="L273" class="lineno"><a class="lineno" href="#L273">273</a></span>
<span id="L274" class="lineno"><a class="lineno" href="#L274">274</a></span>
<span id="L275" class="lineno"><a class="lineno" href="#L275">275</a></span>
<span id="L276" class="lineno"><a class="lineno" href="#L276">276</a></span>
<span id="L277" class="lineno"><a class="lineno" href="#L277">277</a></span>
<span id="L278" class="lineno"><a class="lineno" href="#L278">278</a></span>
<span id="L279" class="lineno"><a class="lineno" href="#L279">279</a></span>
<span id="L280" class="lineno"><a class="lineno" href="#L280">280</a></span>
<span id="L281" class="lineno"><a class="lineno" href="#L281">281</a></span>
<span id="L282" class="lineno"><a class="lineno" href="#L282">282</a></span>
<span id="L283" class="lineno"><a class="lineno" href="#L283">283</a></span>
<span id="L284" class="lineno"><a class="lineno" href="#L284">284</a></span>
<span id="L285" class="lineno"><a class="lineno" href="#L285">285</a></span>
<span id="L286" class="lineno"><a class="lineno" href="#L286">286</a></span>
<span id="L287" class="lineno"><a class="lineno" href="#L287">287</a></span>
<span id="L288" class="lineno"><a class="lineno" href="#L288">288</a></span>
<span id="L289" class="lineno"><a class="lineno" href="#L289">289</a></span>
<span id="L290" class="lineno"><a class="lineno" href="#L290">290</a></span>
<span id="L291" class="lineno"><a class="lineno" href="#L291">291</a></span>
<span id="L292" class="lineno"><a class="lineno" href="#L292">292</a></span>
<span id="L293" class="lineno"><a class="lineno" href="#L293">293</a></span>
<span id="L294" class="lineno"><a class="lineno" href="#L294">294</a></span>
<span id="L295" class="lineno"><a class="lineno" href="#L295">295</a></span>
<span id="L296" class="lineno"><a class="lineno" href="#L296">296</a></span>
<span id="L297" class="lineno"><a class="lineno" href="#L297">297</a></span>
<span id="L298" class="lineno"><a class="lineno" href="#L298">298</a></span>
<span id="L299" class="lineno"><a class="lineno" href="#L299">299</a></span>
<span id="L300" class="lineno"><a class="lineno" href="#L300">300</a></span>
<span id="L301" class="lineno"><a class="lineno" href="#L301">301</a></span>
<span id="L302" class="lineno"><a class="lineno" href="#L302">302</a></span>
<span id="L303" class="lineno"><a class="lineno" href="#L303">303</a></span>
<span id="L304" class="lineno"><a class="lineno" href="#L304">304</a></span>
<span id="L305" class="lineno"><a class="lineno" href="#L305">305</a></span>
<span id="L306" class="lineno"><a class="lineno" href="#L306">306</a></span>
<span id="L307" class="lineno"><a class="lineno" href="#L307">307</a></span>
<span id="L308" class="lineno"><a class="lineno" href="#L308">308</a></span>
<span id="L309" class="lineno"><a class="lineno" href="#L309">309</a></span>
<span id="L310" class="lineno"><a class="lineno" href="#L310">310</a></span>
<span id="L311" class="lineno"><a class="lineno" href="#L311">311</a></span>
<span id="L312" class="lineno"><a class="lineno" href="#L312">312</a></span>
<span id="L313" class="lineno"><a class="lineno" href="#L313">313</a></span>
<span id="L314" class="lineno"><a class="lineno" href="#L314">314</a></span>
<span id="L315" class="lineno"><a class="lineno" href="#L315">315</a></span>
<span id="L316" class="lineno"><a class="lineno" href="#L316">316</a></span>
<span id="L317" class="lineno"><a class="lineno" href="#L317">317</a></span>
<span id="L318" class="lineno"><a class="lineno" href="#L318">318</a></span>
<span id="L319" class="lineno"><a class="lineno" href="#L319">319</a></span>
<span id="L320" class="lineno"><a class="lineno" href="#L320">320</a></span>
<span id="L321" class="lineno"><a class="lineno" href="#L321">321</a></span>
<span id="L322" class="lineno"><a class="lineno" href="#L322">322</a></span>
<span id="L323" class="lineno"><a class="lineno" href="#L323">323</a></span>
<span id="L324" class="lineno"><a class="lineno" href="#L324">324</a></span>
<span id="L325" class="lineno"><a class="lineno" href="#L325">325</a></span>
<span id="L326" class="lineno"><a class="lineno" href="#L326">326</a></span>
<span id="L327" class="lineno"><a class="lineno" href="#L327">327</a></span>
<span id="L328" class="lineno"><a class="lineno" href="#L328">328</a></span>
<span id="L329" class="lineno"><a class="lineno" href="#L329">329</a></span>
<span id="L330" class="lineno"><a class="lineno" href="#L330">330</a></span>
<span id="L331" class="lineno"><a class="lineno" href="#L331">331</a></span>
<span id="L332" class="lineno"><a class="lineno" href="#L332">332</a></span>
<span id="L333" class="lineno"><a class="lineno" href="#L333">333</a></span>
<span id="L334" class="lineno"><a class="lineno" href="#L334">334</a></span>
<span id="L335" class="lineno"><a class="lineno" href="#L335">335</a></span>
<span id="L336" class="lineno"><a class="lineno" href="#L336">336</a></span>
<span id="L337" class="lineno"><a class="lineno" href="#L337">337</a></span>
<span id="L338" class="lineno"><a class="lineno" href="#L338">338</a></span>
<span id="L339" class="lineno"><a class="lineno" href="#L339">339</a></span>
<span id="L340" class="lineno"><a class="lineno" href="#L340">340</a></span>
<span id="L341" class="lineno"><a class="lineno" href="#L341">341</a></span>
<span id="L342" class="lineno"><a class="lineno" href="#L342">342</a></span>
<span id="L343" class="lineno"><a class="lineno" href="#L343">343</a></span>
<span id="L344" class="lineno"><a class="lineno" href="#L344">344</a></span>
<span id="L345" class="lineno"><a class="lineno" href="#L345">345</a></span>
<span id="L346" class="lineno"><a class="lineno" href="#L346">346</a></span>
<span id="L347" class="lineno"><a class="lineno" href="#L347">347</a></span>
<span id="L348" class="lineno"><a class="lineno" href="#L348">348</a></span>
<span id="L349" class="lineno"><a class="lineno" href="#L349">349</a></span>
<span id="L350" class="lineno"><a class="lineno" href="#L350">350</a></span>
<span id="L351" class="lineno"><a class="lineno" href="#L351">351</a></span>
<span id="L352" class="lineno"><a class="lineno" href="#L352">352</a></span>
<span id="L353" class="lineno"><a class="lineno" href="#L353">353</a></span>
<span id="L354" class="lineno"><a class="lineno" href="#L354">354</a></span>
<span id="L355" class="lineno"><a class="lineno" href="#L355">355</a></span>
<span id="L356" class="lineno"><a class="lineno" href="#L356">356</a></span>
<span id="L357" class="lineno"><a class="lineno" href="#L357">357</a></span>
<span id="L358" class="lineno"><a class="lineno" href="#L358">358</a></span>
<span id="L359" class="lineno"><a class="lineno" href="#L359">359</a></span>
<span id="L360" class="lineno"><a class="lineno" href="#L360">360</a></span>
<span id="L361" class="lineno"><a class="lineno" href="#L361">361</a></span>
<span id="L362" class="lineno"><a class="lineno" href="#L362">362</a></span>
<span id="L363" class="lineno"><a class="lineno" href="#L363">363</a></span>
<span id="L364" class="lineno"><a class="lineno" href="#L364">364</a></span>
<span id="L365" class="lineno"><a class="lineno" href="#L365">365</a></span>
<span id="L366" class="lineno"><a class="lineno" href="#L366">366</a></span>
<span id="L367" class="lineno"><a class="lineno" href="#L367">367</a></span>
<span id="L368" class="lineno"><a class="lineno" href="#L368">368</a></span>
<span id="L369" class="lineno"><a class="lineno" href="#L369">369</a></span>
<span id="L370" class="lineno"><a class="lineno" href="#L370">370</a></span>
<span id="L371" class="lineno"><a class="lineno" href="#L371">371</a></span>
<span id="L372" class="lineno"><a class="lineno" href="#L372">372</a></span>
<span id="L373" class="lineno"><a class="lineno" href="#L373">373</a></span>
<span id="L374" class="lineno"><a class="lineno" href="#L374">374</a></span>
<span id="L375" class="lineno"><a class="lineno" href="#L375">375</a></span>
<span id="L376" class="lineno"><a class="lineno" href="#L376">376</a></span>
<span id="L377" class="lineno"><a class="lineno" href="#L377">377</a></span>
<span id="L378" class="lineno"><a class="lineno" href="#L378">378</a></span>
<span id="L379" class="lineno"><a class="lineno" href="#L379">379</a></span>
<span id="L380" class="lineno"><a class="lineno" href="#L380">380</a></span>
<span id="L381" class="lineno"><a class="lineno" href="#L381">381</a></span>
<span id="L382" class="lineno"><a class="lineno" href="#L382">382</a></span>
<span id="L383" class="lineno"><a class="lineno" href="#L383">383</a></span>
<span id="L384" class="lineno"><a class="lineno" href="#L384">384</a></span>
<span id="L385" class="lineno"><a class="lineno" href="#L385">385</a></span>
<span id="L386" class="lineno"><a class="lineno" href="#L386">386</a></span>
<span id="L387" class="lineno"><a class="lineno" href="#L387">387</a></span>
<span id="L388" class="lineno"><a class="lineno" href="#L388">388</a></span>
<span id="L389" class="lineno"><a class="lineno" href="#L389">389</a></span>
<span id="L390" class="lineno"><a class="lineno" href="#L390">390</a></span>
<span id="L391" class="lineno"><a class="lineno" href="#L391">391</a></span>
<span id="L392" class="lineno"><a class="lineno" href="#L392">392</a></span>
<span id="L393" class="lineno"><a class="lineno" href="#L393">393</a></span>
<span id="L394" class="lineno"><a class="lineno" href="#L394">394</a></span>
<span id="L395" class="lineno"><a class="lineno" href="#L395">395</a></span>
<span id="L396" class="lineno"><a class="lineno" href="#L396">396</a></span>
<span id="L397" class="lineno"><a class="lineno" href="#L397">397</a></span>
<span id="L398" class="lineno"><a class="lineno" href="#L398">398</a></span>
<span id="L399" class="lineno"><a class="lineno" href="#L399">399</a></span>
<span id="L400" class="lineno"><a class="lineno" href="#L400">400</a></span>
<span id="L401" class="lineno"><a class="lineno" href="#L401">401</a></span>
<span id="L402" class="lineno"><a class="lineno" href="#L402">402</a></span>
<span id="L403" class="lineno"><a class="lineno" href="#L403">403</a></span>
<span id="L404" class="lineno"><a class="lineno" href="#L404">404</a></span>
<span id="L405" class="lineno"><a class="lineno" href="#L405">405</a></span>
<span id="L406" class="lineno"><a class="lineno" href="#L406">406</a></span>
<span id="L407" class="lineno"><a class="lineno" href="#L407">407</a></span>
<span id="L408" class="lineno"><a class="lineno" href="#L408">408</a></span>
<span id="L409" class="lineno"><a class="lineno" href="#L409">409</a></span>
<span id="L410" class="lineno"><a class="lineno" href="#L410">410</a></span>
<span id="L411" class="lineno"><a class="lineno" href="#L411">411</a></span>
<span id="L412" class="lineno"><a class="lineno" href="#L412">412</a></span>
<span id="L413" class="lineno"><a class="lineno" href="#L413">413</a></span>
<span id="L414" class="lineno"><a class="lineno" href="#L414">414</a></span>
<span id="L415" class="lineno"><a class="lineno" href="#L415">415</a></span>
<span id="L416" class="lineno"><a class="lineno" href="#L416">416</a></span>
<span id="L417" class="lineno"><a class="lineno" href="#L417">417</a></span>
<span id="L418" class="lineno"><a class="lineno" href="#L418">418</a></span>
<span id="L419" class="lineno"><a class="lineno" href="#L419">419</a></span>
<span id="L420" class="lineno"><a class="lineno" href="#L420">420</a></span>
<span id="L421" class="lineno"><a class="lineno" href="#L421">421</a></span>
<span id="L422" class="lineno"><a class="lineno" href="#L422">422</a></span>
<span id="L423" class="lineno"><a class="lineno" href="#L423">423</a></span>
<span id="L424" class="lineno"><a class="lineno" href="#L424">424</a></span>
<span id="L425" class="lineno"><a class="lineno" href="#L425">425</a></span>
<span id="L426" class="lineno"><a class="lineno" href="#L426">426</a></span>
<span id="L427" class="lineno"><a class="lineno" href="#L427">427</a></span>
<span id="L428" class="lineno"><a class="lineno" href="#L428">428</a></span>
<span id="L429" class="lineno"><a class="lineno" href="#L429">429</a></span>
<span id="L430" class="lineno"><a class="lineno" href="#L430">430</a></span>
<span id="L431" class="lineno"><a class="lineno" href="#L431">431</a></span>
<span id="L432" class="lineno"><a class="lineno" href="#L432">432</a></span>
<span id="L433" class="lineno"><a class="lineno" href="#L433">433</a></span>
<span id="L434" class="lineno"><a class="lineno" href="#L434">434</a></span>
<span id="L435" class="lineno"><a class="lineno" href="#L435">435</a></span>
<span id="L436" class="lineno"><a class="lineno" href="#L436">436</a></span>
<span id="L437" class="lineno"><a class="lineno" href="#L437">437</a></span>
<span id="L438" class="lineno"><a class="lineno" href="#L438">438</a></span>
<span id="L439" class="lineno"><a class="lineno" href="#L439">439</a></span>
<span id="L440" class="lineno"><a class="lineno" href="#L440">440</a></span>
<span id="L441" class="lineno"><a class="lineno" href="#L441">441</a></span>
<span id="L442" class="lineno"><a class="lineno" href="#L442">442</a></span>
<span id="L443" class="lineno"><a class="lineno" href="#L443">443</a></span>
<span id="L444" class="lineno"><a class="lineno" href="#L444">444</a></span>
<span id="L445" class="lineno"><a class="lineno" href="#L445">445</a></span>
<span id="L446" class="lineno"><a class="lineno" href="#L446">446</a></span>
<span id="L447" class="lineno"><a class="lineno" href="#L447">447</a></span>
<span id="L448" class="lineno"><a class="lineno" href="#L448">448</a></span>
<span id="L449" class="lineno"><a class="lineno" href="#L449">449</a></span>
<span id="L450" class="lineno"><a class="lineno" href="#L450">450</a></span>
<span id="L451" class="lineno"><a class="lineno" href="#L451">451</a></span>
<span id="L452" class="lineno"><a class="lineno" href="#L452">452</a></span>
<span id="L453" class="lineno"><a class="lineno" href="#L453">453</a></span>
<span id="L454" class="lineno"><a class="lineno" href="#L454">454</a></span>
<span id="L455" class="lineno"><a class="lineno" href="#L455">455</a></span>
<span id="L456" class="lineno"><a class="lineno" href="#L456">456</a></span>
<span id="L457" class="lineno"><a class="lineno" href="#L457">457</a></span>
<span id="L458" class="lineno"><a class="lineno" href="#L458">458</a></span>
<span id="L459" class="lineno"><a class="lineno" href="#L459">459</a></span>
<span id="L460" class="lineno"><a class="lineno" href="#L460">460</a></span>
<span id="L461" class="lineno"><a class="lineno" href="#L461">461</a></span>
<span id="L462" class="lineno"><a class="lineno" href="#L462">462</a></span>
<span id="L463" class="lineno"><a class="lineno" href="#L463">463</a></span>
<span id="L464" class="lineno"><a class="lineno" href="#L464">464</a></span>
<span id="L465" class="lineno"><a class="lineno" href="#L465">465</a></span>
<span id="L466" class="lineno"><a class="lineno" href="#L466">466</a></span>
<span id="L467" class="lineno"><a class="lineno" href="#L467">467</a></span>
<span id="L468" class="lineno"><a class="lineno" href="#L468">468</a></span>
<span id="L469" class="lineno"><a class="lineno" href="#L469">469</a></span>
<span id="L470" class="lineno"><a class="lineno" href="#L470">470</a></span>
<span id="L471" class="lineno"><a class="lineno" href="#L471">471</a></span>
<span id="L472" class="lineno"><a class="lineno" href="#L472">472</a></span>
<span id="L473" class="lineno"><a class="lineno" href="#L473">473</a></span>
<span id="L474" class="lineno"><a class="lineno" href="#L474">474</a></span>
<span id="L475" class="lineno"><a class="lineno" href="#L475">475</a></span>
<span id="L476" class="lineno"><a class="lineno" href="#L476">476</a></span>
<span id="L477" class="lineno"><a class="lineno" href="#L477">477</a></span>
<span id="L478" class="lineno"><a class="lineno" href="#L478">478</a></span>
<span id="L479" class="lineno"><a class="lineno" href="#L479">479</a></span>
<span id="L480" class="lineno"><a class="lineno" href="#L480">480</a></span>
<span id="L481" class="lineno"><a class="lineno" href="#L481">481</a></span>
<span id="L482" class="lineno"><a class="lineno" href="#L482">482</a></span>
<span id="L483" class="lineno"><a class="lineno" href="#L483">483</a></span>
<span id="L484" class="lineno"><a class="lineno" href="#L484">484</a></span>
<span id="L485" class="lineno"><a class="lineno" href="#L485">485</a></span>
<span id="L486" class="lineno"><a class="lineno" href="#L486">486</a></span>
<span id="L487" class="lineno"><a class="lineno" href="#L487">487</a></span>
<span id="L488" class="lineno"><a class="lineno" href="#L488">488</a></span>
<span id="L489" class="lineno"><a class="lineno" href="#L489">489</a></span>
<span id="L490" class="lineno"><a class="lineno" href="#L490">490</a></span>
<span id="L491" class="lineno"><a class="lineno" href="#L491">491</a></span>
<span id="L492" class="lineno"><a class="lineno" href="#L492">492</a></span>
<span id="L493" class="lineno"><a class="lineno" href="#L493">493</a></span>
<span id="L494" class="lineno"><a class="lineno" href="#L494">494</a></span>
<span id="L495" class="lineno"><a class="lineno" href="#L495">495</a></span>
<span id="L496" class="lineno"><a class="lineno" href="#L496">496</a></span>
<span id="L497" class="lineno"><a class="lineno" href="#L497">497</a></span>
<span id="L498" class="lineno"><a class="lineno" href="#L498">498</a></span>
<span id="L499" class="lineno"><a class="lineno" href="#L499">499</a></span>
<span id="L500" class="lineno"><a class="lineno" href="#L500">500</a></span>
<span id="L501" class="lineno"><a class="lineno" href="#L501">501</a></span>
<span id="L502" class="lineno"><a class="lineno" href="#L502">502</a></span>
<span id="L503" class="lineno"><a class="lineno" href="#L503">503</a></span>
<span id="L504" class="lineno"><a class="lineno" href="#L504">504</a></span>
<span id="L505" class="lineno"><a class="lineno" href="#L505">505</a></span>
<span id="L506" class="lineno"><a class="lineno" href="#L506">506</a></span>
<span id="L507" class="lineno"><a class="lineno" href="#L507">507</a></span>
</pre></td>
<td class="table-code"><pre><span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!">Middleware integration for the AiLex Ad Agent System.</span>
<span class="line-empty" title="No Anys on this line!">Combines authentication, validation, and other middleware components.</span>
<span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from typing import Optional, Set</span>
<span class="line-precise" title="No Anys on this line!">from fastapi import FastAPI, Request, HTTPException, status</span>
<span class="line-precise" title="No Anys on this line!">from fastapi.middleware.cors import CORSMiddleware</span>
<span class="line-precise" title="No Anys on this line!">from fastapi.middleware.trustedhost import TrustedHostMiddleware</span>
<span class="line-precise" title="No Anys on this line!">from fastapi.middleware.gzip import GZipMiddleware</span>
<span class="line-precise" title="No Anys on this line!">from starlette.middleware.sessions import SessionMiddleware</span>
<span class="line-any" title="No Anys on this line!">import structlog</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from utils.config import settings</span>
<span class="line-precise" title="No Anys on this line!">from services.redis_service import RedisService</span>
<span class="line-precise" title="No Anys on this line!">from middleware.auth import ClerkAuthMiddleware, APIKeyAuthMiddleware</span>
<span class="line-precise" title="No Anys on this line!">from middleware.validation import ValidationMiddleware, RateLimitConfig, ValidationConfig</span>
<span class="line-precise" title="No Anys on this line!">from middleware.security import SecurityMiddleware, create_security_middleware</span>
<span class="line-precise" title="No Anys on this line!">from middleware.transformation import ResponseTransformationMiddleware, TransformationConfig</span>
<span class="line-precise" title="No Anys on this line!">from middleware.logging import AdvancedLoggingMiddleware, LoggingConfig</span>
<span class="line-precise" title="No Anys on this line!">from middleware.tracing import RequestTracingMiddleware, TracingConfig</span>
<span class="line-precise" title="No Anys on this line!">from middleware.metrics import MetricsMiddleware, MetricsConfig</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="Any Types on this line: 
Unimported (x4)">logger = structlog.get_logger(__name__)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class MiddlewareManager:</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Manages all middleware components for the FastAPI application.</span>
<span class="line-empty" title="No Anys on this line!">    Provides centralized configuration and setup.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __init__(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-precise" title="No Anys on this line!">        redis_service: Optional[RedisService] = None,</span>
<span class="line-precise" title="No Anys on this line!">        enable_auth: bool = True,</span>
<span class="line-precise" title="No Anys on this line!">        enable_validation: bool = True,</span>
<span class="line-precise" title="No Anys on this line!">        enable_rate_limiting: bool = True,</span>
<span class="line-precise" title="No Anys on this line!">        enable_security: bool = True,</span>
<span class="line-precise" title="No Anys on this line!">        enable_transformation: bool = True,</span>
<span class="line-precise" title="No Anys on this line!">        enable_advanced_logging: bool = True,</span>
<span class="line-precise" title="No Anys on this line!">        enable_request_tracing: bool = True,</span>
<span class="line-precise" title="No Anys on this line!">        enable_metrics: bool = True,</span>
<span class="line-empty" title="No Anys on this line!">    ):</span>
<span class="line-precise" title="No Anys on this line!">        self.redis_service = redis_service</span>
<span class="line-precise" title="No Anys on this line!">        self.enable_auth = enable_auth</span>
<span class="line-precise" title="No Anys on this line!">        self.enable_validation = enable_validation</span>
<span class="line-precise" title="No Anys on this line!">        self.enable_rate_limiting = enable_rate_limiting</span>
<span class="line-precise" title="No Anys on this line!">        self.enable_security = enable_security</span>
<span class="line-precise" title="No Anys on this line!">        self.enable_transformation = enable_transformation</span>
<span class="line-precise" title="No Anys on this line!">        self.enable_advanced_logging = enable_advanced_logging</span>
<span class="line-precise" title="No Anys on this line!">        self.enable_request_tracing = enable_request_tracing</span>
<span class="line-precise" title="No Anys on this line!">        self.enable_metrics = enable_metrics</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Configure middleware settings</span>
<span class="line-precise" title="No Anys on this line!">        self.auth_excluded_paths = {</span>
<span class="line-precise" title="No Anys on this line!">            "/docs", "/redoc", "/openapi.json", "/health", "/", "/favicon.ico",</span>
<span class="line-precise" title="No Anys on this line!">            "/api/v1/health", "/api/v1/status", "/metrics"</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        self.auth_optional_paths = {</span>
<span class="line-precise" title="No Anys on this line!">            "/api/v1/campaigns/public",</span>
<span class="line-precise" title="No Anys on this line!">            "/api/v1/analytics/public"</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        self.validation_excluded_paths = {</span>
<span class="line-precise" title="No Anys on this line!">            "/docs", "/redoc", "/openapi.json", "/health", "/", "/favicon.ico",</span>
<span class="line-precise" title="No Anys on this line!">            "/static", "/assets"</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        self.rate_limit_excluded_paths = {</span>
<span class="line-precise" title="No Anys on this line!">            "/health", "/", "/favicon.ico", "/static", "/assets"</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def setup_middleware(self, app: FastAPI) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        Set up all middleware components for the FastAPI application.</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        Args:</span>
<span class="line-empty" title="No Anys on this line!">            app: FastAPI application instance</span>
<span class="line-empty" title="No Anys on this line!">        """</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-empty" title="No Anys on this line!">            # Advanced logging middleware (applied first for comprehensive logging)</span>
<span class="line-precise" title="No Anys on this line!">            if self.enable_advanced_logging:</span>
<span class="line-precise" title="No Anys on this line!">                self._setup_advanced_logging_middleware(app)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Security middleware (applied early for threat detection)</span>
<span class="line-precise" title="No Anys on this line!">            if self.enable_security:</span>
<span class="line-precise" title="No Anys on this line!">                self._setup_enhanced_security_middleware(app)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Basic security middleware (sessions, trusted hosts)</span>
<span class="line-precise" title="No Anys on this line!">            self._setup_basic_security_middleware(app)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Authentication middleware</span>
<span class="line-precise" title="No Anys on this line!">            if self.enable_auth:</span>
<span class="line-precise" title="No Anys on this line!">                self._setup_auth_middleware(app)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Validation and rate limiting middleware</span>
<span class="line-precise" title="No Anys on this line!">            if self.enable_validation:</span>
<span class="line-precise" title="No Anys on this line!">                self._setup_validation_middleware(app)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Request tracing middleware</span>
<span class="line-precise" title="No Anys on this line!">            if self.enable_request_tracing:</span>
<span class="line-precise" title="No Anys on this line!">                self._setup_tracing_middleware(app)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Response transformation middleware</span>
<span class="line-precise" title="No Anys on this line!">            if self.enable_transformation:</span>
<span class="line-precise" title="No Anys on this line!">                self._setup_transformation_middleware(app)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Metrics collection middleware</span>
<span class="line-precise" title="No Anys on this line!">            if self.enable_metrics:</span>
<span class="line-precise" title="No Anys on this line!">                self._setup_metrics_middleware(app)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Performance middleware</span>
<span class="line-precise" title="No Anys on this line!">            self._setup_performance_middleware(app)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # CORS middleware (applied last to be processed first)</span>
<span class="line-precise" title="No Anys on this line!">            self._setup_cors_middleware(app)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            logger.info(</span>
<span class="line-precise" title="No Anys on this line!">                "Middleware setup completed",</span>
<span class="line-precise" title="No Anys on this line!">                auth_enabled=self.enable_auth,</span>
<span class="line-precise" title="No Anys on this line!">                validation_enabled=self.enable_validation,</span>
<span class="line-precise" title="No Anys on this line!">                rate_limiting_enabled=self.enable_rate_limiting,</span>
<span class="line-precise" title="No Anys on this line!">                security_enabled=self.enable_security,</span>
<span class="line-precise" title="No Anys on this line!">                transformation_enabled=self.enable_transformation,</span>
<span class="line-precise" title="No Anys on this line!">                advanced_logging_enabled=self.enable_advanced_logging,</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            logger.error("Failed to setup middleware", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">            raise</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _setup_enhanced_security_middleware(self, app: FastAPI) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Set up enhanced security middleware with threat detection."""</span>
<span class="line-empty" title="No Anys on this line!">        # Create advanced security middleware</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        security_middleware_class = create_security_middleware(</span>
<span class="line-precise" title="No Anys on this line!">            redis_service=self.redis_service,</span>
<span class="line-precise" title="No Anys on this line!">            enable_csp=True,</span>
<span class="line-precise" title="No Anys on this line!">            enable_threat_detection=True,</span>
<span class="line-precise" title="No Anys on this line!">            enable_rate_limiting=True,</span>
<span class="line-precise" title="No Anys on this line!">            enable_ip_whitelist=False,</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x1)
Omitted Generics (x3)">            trusted_ips=set(),</span>
<span class="line-empty" title="No Anys on this line!">            excluded_paths={</span>
<span class="line-precise" title="No Anys on this line!">                "/docs", "/redoc", "/openapi.json", "/health", "/", "/favicon.ico",</span>
<span class="line-precise" title="No Anys on this line!">                "/static", "/assets", "/metrics"</span>
<span class="line-empty" title="No Anys on this line!">            },</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        app.add_middleware(security_middleware_class)</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        logger.info("Enhanced security middleware enabled")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _setup_basic_security_middleware(self, app: FastAPI) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Set up basic security-related middleware."""</span>
<span class="line-empty" title="No Anys on this line!">        # Session middleware for CSRF protection</span>
<span class="line-precise" title="No Anys on this line!">        app.add_middleware(</span>
<span class="line-precise" title="No Anys on this line!">            SessionMiddleware,</span>
<span class="line-precise" title="No Anys on this line!">            secret_key=settings.SECRET_KEY if hasattr(settings, 'SECRET_KEY') else "dev-secret-key-change-in-production",</span>
<span class="line-precise" title="No Anys on this line!">            max_age=3600,  # 1 hour</span>
<span class="line-precise" title="No Anys on this line!">            same_site="lax",</span>
<span class="line-precise" title="No Anys on this line!">            https_only=settings.is_production,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Trusted host middleware</span>
<span class="line-precise" title="No Anys on this line!">        if settings.TRUSTED_HOSTS:</span>
<span class="line-precise" title="No Anys on this line!">            app.add_middleware(</span>
<span class="line-precise" title="No Anys on this line!">                TrustedHostMiddleware,</span>
<span class="line-precise" title="No Anys on this line!">                allowed_hosts=settings.TRUSTED_HOSTS</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            logger.info("Trusted host middleware enabled", hosts=settings.TRUSTED_HOSTS)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _setup_advanced_logging_middleware(self, app: FastAPI) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Set up advanced logging middleware."""</span>
<span class="line-precise" title="No Anys on this line!">        logging_config = LoggingConfig(</span>
<span class="line-precise" title="No Anys on this line!">            log_requests=True,</span>
<span class="line-precise" title="No Anys on this line!">            log_responses=True,</span>
<span class="line-precise" title="No Anys on this line!">            log_request_body=settings.is_development,  # Only in development</span>
<span class="line-precise" title="No Anys on this line!">            log_response_body=False,  # Disabled for performance</span>
<span class="line-precise" title="No Anys on this line!">            log_headers=True,</span>
<span class="line-precise" title="No Anys on this line!">            log_query_params=True,</span>
<span class="line-precise" title="No Anys on this line!">            log_performance_metrics=True,</span>
<span class="line-precise" title="No Anys on this line!">            log_errors=True,</span>
<span class="line-precise" title="No Anys on this line!">            log_stack_traces=settings.is_development,</span>
<span class="line-precise" title="No Anys on this line!">            mask_sensitive_data=True,</span>
<span class="line-precise" title="No Anys on this line!">            max_body_size=10000,</span>
<span class="line-precise" title="No Anys on this line!">            async_logging=True,</span>
<span class="line-precise" title="No Anys on this line!">            buffer_size=100,</span>
<span class="line-precise" title="No Anys on this line!">            flush_interval=5.0,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        logging_middleware = AdvancedLoggingMiddleware(</span>
<span class="line-precise" title="No Anys on this line!">            app=None,  # Will be set by FastAPI</span>
<span class="line-precise" title="No Anys on this line!">            config=logging_config,</span>
<span class="line-precise" title="No Anys on this line!">            redis_service=self.redis_service,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        app.add_middleware(</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x2)">            type(logging_middleware),</span>
<span class="line-precise" title="No Anys on this line!">            config=logging_config,</span>
<span class="line-precise" title="No Anys on this line!">            redis_service=self.redis_service,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        logger.info("Advanced logging middleware enabled")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _setup_transformation_middleware(self, app: FastAPI) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Set up response transformation middleware."""</span>
<span class="line-precise" title="No Anys on this line!">        transformation_config = TransformationConfig(</span>
<span class="line-precise" title="No Anys on this line!">            enable_response_transformation=True,</span>
<span class="line-precise" title="No Anys on this line!">            enable_request_transformation=True,</span>
<span class="line-precise" title="No Anys on this line!">            enable_data_serialization=True,</span>
<span class="line-precise" title="No Anys on this line!">            enable_field_renaming=True,</span>
<span class="line-precise" title="No Anys on this line!">            enable_data_filtering=True,</span>
<span class="line-precise" title="No Anys on this line!">            default_api_version="v1",</span>
<span class="line-precise" title="No Anys on this line!">            strip_null_values=True,</span>
<span class="line-precise" title="No Anys on this line!">            convert_decimals=True,</span>
<span class="line-precise" title="No Anys on this line!">            format_timestamps=True,</span>
<span class="line-precise" title="No Anys on this line!">            include_metadata=True,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        transformation_middleware = ResponseTransformationMiddleware(</span>
<span class="line-precise" title="No Anys on this line!">            app=None,  # Will be set by FastAPI</span>
<span class="line-precise" title="No Anys on this line!">            config=transformation_config,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        app.add_middleware(</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x2)">            type(transformation_middleware),</span>
<span class="line-precise" title="No Anys on this line!">            config=transformation_config,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        logger.info("Response transformation middleware enabled")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _setup_tracing_middleware(self, app: FastAPI) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Set up distributed tracing middleware."""</span>
<span class="line-precise" title="No Anys on this line!">        tracing_config = TracingConfig(</span>
<span class="line-precise" title="No Anys on this line!">            enable_tracing=True,</span>
<span class="line-precise" title="No Anys on this line!">            enable_performance_monitoring=True,</span>
<span class="line-precise" title="No Anys on this line!">            enable_distributed_tracing=True,</span>
<span class="line-precise" title="No Anys on this line!">            sample_rate=1.0 if settings.is_development else 0.1,  # Sample more in dev</span>
<span class="line-precise" title="No Anys on this line!">            max_spans_per_trace=100,</span>
<span class="line-precise" title="No Anys on this line!">            span_export_batch_size=50,</span>
<span class="line-precise" title="No Anys on this line!">            span_export_timeout=30.0,</span>
<span class="line-precise" title="No Anys on this line!">            store_traces_in_redis=True,</span>
<span class="line-precise" title="No Anys on this line!">            trace_retention_hours=24,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        tracing_middleware = RequestTracingMiddleware(</span>
<span class="line-precise" title="No Anys on this line!">            app=None,  # Will be set by FastAPI</span>
<span class="line-precise" title="No Anys on this line!">            config=tracing_config,</span>
<span class="line-precise" title="No Anys on this line!">            redis_service=self.redis_service,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        app.add_middleware(</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x2)">            type(tracing_middleware),</span>
<span class="line-precise" title="No Anys on this line!">            config=tracing_config,</span>
<span class="line-precise" title="No Anys on this line!">            redis_service=self.redis_service,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        logger.info("Request tracing middleware enabled")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _setup_metrics_middleware(self, app: FastAPI) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Set up metrics collection middleware."""</span>
<span class="line-precise" title="No Anys on this line!">        metrics_config = MetricsConfig(</span>
<span class="line-precise" title="No Anys on this line!">            enable_metrics=True,</span>
<span class="line-precise" title="No Anys on this line!">            enable_request_metrics=True,</span>
<span class="line-precise" title="No Anys on this line!">            enable_performance_metrics=True,</span>
<span class="line-precise" title="No Anys on this line!">            enable_custom_metrics=True,</span>
<span class="line-precise" title="No Anys on this line!">            collect_detailed_metrics=settings.is_development,  # More detailed in dev</span>
<span class="line-precise" title="No Anys on this line!">            export_interval_seconds=60.0,</span>
<span class="line-precise" title="No Anys on this line!">            max_cardinality=10000,</span>
<span class="line-precise" title="No Anys on this line!">            slow_request_threshold_ms=1000.0,</span>
<span class="line-precise" title="No Anys on this line!">            error_sampling_rate=1.0,  # Sample all errors</span>
<span class="line-precise" title="No Anys on this line!">            success_sampling_rate=0.1,  # Sample 10% of successful requests</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        metrics_middleware = MetricsMiddleware(</span>
<span class="line-precise" title="No Anys on this line!">            app=None,  # Will be set by FastAPI</span>
<span class="line-precise" title="No Anys on this line!">            config=metrics_config,</span>
<span class="line-precise" title="No Anys on this line!">            redis_service=self.redis_service,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        app.add_middleware(</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x2)">            type(metrics_middleware),</span>
<span class="line-precise" title="No Anys on this line!">            config=metrics_config,</span>
<span class="line-precise" title="No Anys on this line!">            redis_service=self.redis_service,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        logger.info("Metrics collection middleware enabled")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _setup_auth_middleware(self, app: FastAPI) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Set up authentication middleware."""</span>
<span class="line-empty" title="No Anys on this line!">        # Primary authentication: Clerk</span>
<span class="line-precise" title="No Anys on this line!">        if settings.CLERK_PUBLISHABLE_KEY and settings.CLERK_SECRET_KEY:</span>
<span class="line-precise" title="No Anys on this line!">            clerk_middleware = ClerkAuthMiddleware(</span>
<span class="line-precise" title="No Anys on this line!">                excluded_paths=self.auth_excluded_paths,</span>
<span class="line-precise" title="No Anys on this line!">                optional_auth_paths=self.auth_optional_paths,</span>
<span class="line-precise" title="No Anys on this line!">                redis_service=self.redis_service,</span>
<span class="line-empty" title="No Anys on this line!">            )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x5)">            app.add_middleware(type(clerk_middleware), **clerk_middleware.__dict__)</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            logger.info("Clerk authentication middleware enabled")</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Secondary authentication: API Keys (for service-to-service)</span>
<span class="line-precise" title="No Anys on this line!">        api_key_middleware = APIKeyAuthMiddleware(</span>
<span class="line-precise" title="No Anys on this line!">            excluded_paths=self.auth_excluded_paths,</span>
<span class="line-precise" title="No Anys on this line!">            optional_auth_paths=self.auth_optional_paths,</span>
<span class="line-precise" title="No Anys on this line!">            redis_service=self.redis_service,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x5)">        app.add_middleware(type(api_key_middleware), **api_key_middleware.__dict__)</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        logger.info("API key authentication middleware enabled")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _setup_validation_middleware(self, app: FastAPI) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Set up validation and rate limiting middleware."""</span>
<span class="line-empty" title="No Anys on this line!">        # Configure rate limiting with hierarchical limits</span>
<span class="line-precise" title="No Anys on this line!">        rate_limit_config = RateLimitConfig(</span>
<span class="line-precise" title="No Anys on this line!">            requests_per_minute=settings.RATE_LIMIT_REQUESTS_PER_MINUTE,</span>
<span class="line-precise" title="No Anys on this line!">            requests_per_hour=settings.RATE_LIMIT_REQUESTS_PER_MINUTE * 60,</span>
<span class="line-precise" title="No Anys on this line!">            requests_per_day=settings.RATE_LIMIT_REQUESTS_PER_MINUTE * 60 * 24,</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x23)">            burst_limit=min(50, settings.RATE_LIMIT_REQUESTS_PER_MINUTE // 2),</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Configure validation with enhanced security</span>
<span class="line-precise" title="No Anys on this line!">        validation_config = ValidationConfig(</span>
<span class="line-precise" title="No Anys on this line!">            max_request_size_mb=10.0,</span>
<span class="line-precise" title="No Anys on this line!">            max_json_depth=10,</span>
<span class="line-precise" title="No Anys on this line!">            max_array_length=1000,</span>
<span class="line-precise" title="No Anys on this line!">            max_string_length=10000,</span>
<span class="line-precise" title="No Anys on this line!">            sanitize_html=True,</span>
<span class="line-precise" title="No Anys on this line!">            validate_sql_injection=True,</span>
<span class="line-precise" title="No Anys on this line!">            validate_xss=True,</span>
<span class="line-precise" title="No Anys on this line!">            block_suspicious_patterns=True,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Add validation middleware</span>
<span class="line-precise" title="Any Types on this line: 
Unannotated (x1)">        validation_middleware = ValidationMiddleware(</span>
<span class="line-precise" title="No Anys on this line!">            redis_service=self.redis_service if self.enable_rate_limiting else None,</span>
<span class="line-precise" title="No Anys on this line!">            rate_limit_config=rate_limit_config,</span>
<span class="line-precise" title="No Anys on this line!">            validation_config=validation_config,</span>
<span class="line-precise" title="No Anys on this line!">            excluded_paths=self.validation_excluded_paths,</span>
<span class="line-precise" title="No Anys on this line!">            rate_limit_excluded_paths=self.rate_limit_excluded_paths,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="Any Types on this line: 
Unannotated (x1)">        app.add_middleware(ValidationMiddleware, **{</span>
<span class="line-precise" title="No Anys on this line!">            'redis_service': validation_middleware.redis_service,</span>
<span class="line-precise" title="No Anys on this line!">            'rate_limit_config': validation_middleware.rate_limit_config,</span>
<span class="line-precise" title="No Anys on this line!">            'validation_config': validation_middleware.validation_config,</span>
<span class="line-precise" title="No Anys on this line!">            'excluded_paths': validation_middleware.excluded_paths,</span>
<span class="line-precise" title="No Anys on this line!">            'rate_limit_excluded_paths': validation_middleware.rate_limit_excluded_paths,</span>
<span class="line-empty" title="No Anys on this line!">        })</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        logger.info("Validation and rate limiting middleware enabled")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _setup_performance_middleware(self, app: FastAPI) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Set up performance-related middleware."""</span>
<span class="line-empty" title="No Anys on this line!">        # GZip compression</span>
<span class="line-precise" title="No Anys on this line!">        app.add_middleware(GZipMiddleware, minimum_size=1000)</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        logger.info("GZip compression middleware enabled")</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _setup_cors_middleware(self, app: FastAPI) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Set up CORS middleware."""</span>
<span class="line-precise" title="No Anys on this line!">        app.add_middleware(</span>
<span class="line-precise" title="No Anys on this line!">            CORSMiddleware,</span>
<span class="line-precise" title="No Anys on this line!">            allow_origins=settings.CORS_ORIGINS,</span>
<span class="line-precise" title="No Anys on this line!">            allow_credentials=True,</span>
<span class="line-precise" title="No Anys on this line!">            allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],</span>
<span class="line-empty" title="No Anys on this line!">            allow_headers=[</span>
<span class="line-precise" title="No Anys on this line!">                "Accept",</span>
<span class="line-precise" title="No Anys on this line!">                "Accept-Language",</span>
<span class="line-precise" title="No Anys on this line!">                "Content-Language",</span>
<span class="line-precise" title="No Anys on this line!">                "Content-Type",</span>
<span class="line-precise" title="No Anys on this line!">                "Authorization",</span>
<span class="line-precise" title="No Anys on this line!">                "X-API-Key",</span>
<span class="line-precise" title="No Anys on this line!">                "X-Request-ID",</span>
<span class="line-precise" title="No Anys on this line!">                "X-Correlation-ID",</span>
<span class="line-empty" title="No Anys on this line!">            ],</span>
<span class="line-empty" title="No Anys on this line!">            expose_headers=[</span>
<span class="line-precise" title="No Anys on this line!">                "X-Request-ID",</span>
<span class="line-precise" title="No Anys on this line!">                "X-Rate-Limit-Remaining",</span>
<span class="line-precise" title="No Anys on this line!">                "X-Rate-Limit-Reset",</span>
<span class="line-empty" title="No Anys on this line!">            ]</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        logger.info("CORS middleware enabled", origins=settings.CORS_ORIGINS)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"># Middleware dependency functions</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x1)">async def get_current_user_optional(request: Request):</span>
<span class="line-empty" title="No Anys on this line!">    """Get current user if authenticated, otherwise None."""</span>
<span class="line-precise" title="No Anys on this line!">    from middleware.auth import get_current_user</span>
<span class="line-precise" title="No Anys on this line!">    return get_current_user(request)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x1)">async def get_current_user_required(request: Request):</span>
<span class="line-empty" title="No Anys on this line!">    """Get current user, raise exception if not authenticated."""</span>
<span class="line-precise" title="No Anys on this line!">    from middleware.auth import require_auth</span>
<span class="line-precise" title="No Anys on this line!">    return require_auth(request)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x1)">async def get_sanitized_body_optional(request: Request):</span>
<span class="line-empty" title="No Anys on this line!">    """Get sanitized request body if available."""</span>
<span class="line-precise" title="No Anys on this line!">    from middleware.validation import get_sanitized_body</span>
<span class="line-imprecise" title="Any Types on this line: 
Error (x4)">    return get_sanitized_body(request)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"># Permission checking functions</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">def require_permission(permission: str):</span>
<span class="line-empty" title="No Anys on this line!">    """Decorator to require specific permission."""</span>
<span class="line-any" title="No Anys on this line!">    def decorator(func):</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">        async def wrapper(request: Request, *args, **kwargs):</span>
<span class="line-precise" title="No Anys on this line!">            from middleware.auth import require_auth</span>
<span class="line-precise" title="No Anys on this line!">            from utils.exceptions import AuthorizationException</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            user = require_auth(request)</span>
<span class="line-precise" title="No Anys on this line!">            if permission not in user.permissions:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">                raise AuthorizationException(f"Permission '{permission}' required")</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x4)">            return await func(request, *args, **kwargs)</span>
<span class="line-precise" title="Any Types on this line: 
Unannotated (x3)">        return wrapper</span>
<span class="line-precise" title="Any Types on this line: 
Unannotated (x2)">    return decorator</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="No Anys on this line!">def require_admin_role():</span>
<span class="line-empty" title="No Anys on this line!">    """Decorator to require admin role."""</span>
<span class="line-any" title="No Anys on this line!">    def decorator(func):</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">        async def wrapper(request: Request, *args, **kwargs):</span>
<span class="line-precise" title="No Anys on this line!">            from middleware.auth import require_admin</span>
<span class="line-precise" title="No Anys on this line!">            require_admin(request)</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x4)">            return await func(request, *args, **kwargs)</span>
<span class="line-precise" title="Any Types on this line: 
Unannotated (x3)">        return wrapper</span>
<span class="line-precise" title="Any Types on this line: 
Unannotated (x2)">    return decorator</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"># Global middleware manager instance</span>
<span class="line-precise" title="No Anys on this line!">middleware_manager = MiddlewareManager()</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"># Exception handlers for middleware errors</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">async def authentication_exception_handler(request: Request, exc):</span>
<span class="line-empty" title="No Anys on this line!">    """Handle authentication exceptions."""</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">    logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">        "Authentication failed",</span>
<span class="line-precise" title="No Anys on this line!">        path=request.url.path,</span>
<span class="line-precise" title="No Anys on this line!">        method=request.method,</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        error=str(exc),</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">    return HTTPException(</span>
<span class="line-precise" title="No Anys on this line!">        status_code=status.HTTP_401_UNAUTHORIZED,</span>
<span class="line-precise" title="No Anys on this line!">        detail="Authentication required",</span>
<span class="line-precise" title="No Anys on this line!">        headers={"WWW-Authenticate": "Bearer"},</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">async def authorization_exception_handler(request: Request, exc):</span>
<span class="line-empty" title="No Anys on this line!">    """Handle authorization exceptions."""</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">    logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">        "Authorization failed",</span>
<span class="line-precise" title="No Anys on this line!">        path=request.url.path,</span>
<span class="line-precise" title="No Anys on this line!">        method=request.method,</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        error=str(exc),</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">    return HTTPException(</span>
<span class="line-precise" title="No Anys on this line!">        status_code=status.HTTP_403_FORBIDDEN,</span>
<span class="line-precise" title="No Anys on this line!">        detail="Insufficient permissions",</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">async def validation_exception_handler(request: Request, exc):</span>
<span class="line-empty" title="No Anys on this line!">    """Handle validation exceptions."""</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">    logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">        "Validation failed",</span>
<span class="line-precise" title="No Anys on this line!">        path=request.url.path,</span>
<span class="line-precise" title="No Anys on this line!">        method=request.method,</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        error=str(exc),</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">    return HTTPException(</span>
<span class="line-precise" title="No Anys on this line!">        status_code=status.HTTP_400_BAD_REQUEST,</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        detail=str(exc),</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">async def rate_limit_exception_handler(request: Request, exc):</span>
<span class="line-empty" title="No Anys on this line!">    """Handle rate limit exceptions."""</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">    logger.warning(</span>
<span class="line-precise" title="No Anys on this line!">        "Rate limit exceeded",</span>
<span class="line-precise" title="No Anys on this line!">        path=request.url.path,</span>
<span class="line-precise" title="No Anys on this line!">        method=request.method,</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        error=str(exc),</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">    return HTTPException(</span>
<span class="line-precise" title="No Anys on this line!">        status_code=status.HTTP_429_TOO_MANY_REQUESTS,</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        detail=str(exc),</span>
<span class="line-precise" title="No Anys on this line!">        headers={"Retry-After": "60"},</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">def setup_exception_handlers(app: FastAPI) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">    """Set up exception handlers for middleware errors."""</span>
<span class="line-precise" title="No Anys on this line!">    from utils.exceptions import (</span>
<span class="line-empty" title="No Anys on this line!">        AuthenticationException,</span>
<span class="line-empty" title="No Anys on this line!">        AuthorizationException,</span>
<span class="line-empty" title="No Anys on this line!">        ValidationException,</span>
<span class="line-empty" title="No Anys on this line!">        RateLimitException,</span>
<span class="line-empty" title="No Anys on this line!">    )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x1)
Unannotated (x2)">    app.add_exception_handler(AuthenticationException, authentication_exception_handler)</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x1)
Unannotated (x2)">    app.add_exception_handler(AuthorizationException, authorization_exception_handler)</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x1)
Unannotated (x2)">    app.add_exception_handler(ValidationException, validation_exception_handler)</span>
<span class="line-precise" title="Any Types on this line: 
Explicit (x1)
Unannotated (x2)">    app.add_exception_handler(RateLimitException, rate_limit_exception_handler)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">    logger.info("Exception handlers for middleware setup completed")</span>
</pre></td>
</tr></tbody>
</table>
</body>
</html>
