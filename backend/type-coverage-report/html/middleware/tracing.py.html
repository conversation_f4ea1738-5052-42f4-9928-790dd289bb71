<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="../../mypy-html.css">
</head>
<body>
<h2>middleware.tracing</h2>
<table>
<caption>middleware/tracing.py</caption>
<tbody><tr>
<td class="table-lines"><pre><span id="L1" class="lineno"><a class="lineno" href="#L1">1</a></span>
<span id="L2" class="lineno"><a class="lineno" href="#L2">2</a></span>
<span id="L3" class="lineno"><a class="lineno" href="#L3">3</a></span>
<span id="L4" class="lineno"><a class="lineno" href="#L4">4</a></span>
<span id="L5" class="lineno"><a class="lineno" href="#L5">5</a></span>
<span id="L6" class="lineno"><a class="lineno" href="#L6">6</a></span>
<span id="L7" class="lineno"><a class="lineno" href="#L7">7</a></span>
<span id="L8" class="lineno"><a class="lineno" href="#L8">8</a></span>
<span id="L9" class="lineno"><a class="lineno" href="#L9">9</a></span>
<span id="L10" class="lineno"><a class="lineno" href="#L10">10</a></span>
<span id="L11" class="lineno"><a class="lineno" href="#L11">11</a></span>
<span id="L12" class="lineno"><a class="lineno" href="#L12">12</a></span>
<span id="L13" class="lineno"><a class="lineno" href="#L13">13</a></span>
<span id="L14" class="lineno"><a class="lineno" href="#L14">14</a></span>
<span id="L15" class="lineno"><a class="lineno" href="#L15">15</a></span>
<span id="L16" class="lineno"><a class="lineno" href="#L16">16</a></span>
<span id="L17" class="lineno"><a class="lineno" href="#L17">17</a></span>
<span id="L18" class="lineno"><a class="lineno" href="#L18">18</a></span>
<span id="L19" class="lineno"><a class="lineno" href="#L19">19</a></span>
<span id="L20" class="lineno"><a class="lineno" href="#L20">20</a></span>
<span id="L21" class="lineno"><a class="lineno" href="#L21">21</a></span>
<span id="L22" class="lineno"><a class="lineno" href="#L22">22</a></span>
<span id="L23" class="lineno"><a class="lineno" href="#L23">23</a></span>
<span id="L24" class="lineno"><a class="lineno" href="#L24">24</a></span>
<span id="L25" class="lineno"><a class="lineno" href="#L25">25</a></span>
<span id="L26" class="lineno"><a class="lineno" href="#L26">26</a></span>
<span id="L27" class="lineno"><a class="lineno" href="#L27">27</a></span>
<span id="L28" class="lineno"><a class="lineno" href="#L28">28</a></span>
<span id="L29" class="lineno"><a class="lineno" href="#L29">29</a></span>
<span id="L30" class="lineno"><a class="lineno" href="#L30">30</a></span>
<span id="L31" class="lineno"><a class="lineno" href="#L31">31</a></span>
<span id="L32" class="lineno"><a class="lineno" href="#L32">32</a></span>
<span id="L33" class="lineno"><a class="lineno" href="#L33">33</a></span>
<span id="L34" class="lineno"><a class="lineno" href="#L34">34</a></span>
<span id="L35" class="lineno"><a class="lineno" href="#L35">35</a></span>
<span id="L36" class="lineno"><a class="lineno" href="#L36">36</a></span>
<span id="L37" class="lineno"><a class="lineno" href="#L37">37</a></span>
<span id="L38" class="lineno"><a class="lineno" href="#L38">38</a></span>
<span id="L39" class="lineno"><a class="lineno" href="#L39">39</a></span>
<span id="L40" class="lineno"><a class="lineno" href="#L40">40</a></span>
<span id="L41" class="lineno"><a class="lineno" href="#L41">41</a></span>
<span id="L42" class="lineno"><a class="lineno" href="#L42">42</a></span>
<span id="L43" class="lineno"><a class="lineno" href="#L43">43</a></span>
<span id="L44" class="lineno"><a class="lineno" href="#L44">44</a></span>
<span id="L45" class="lineno"><a class="lineno" href="#L45">45</a></span>
<span id="L46" class="lineno"><a class="lineno" href="#L46">46</a></span>
<span id="L47" class="lineno"><a class="lineno" href="#L47">47</a></span>
<span id="L48" class="lineno"><a class="lineno" href="#L48">48</a></span>
<span id="L49" class="lineno"><a class="lineno" href="#L49">49</a></span>
<span id="L50" class="lineno"><a class="lineno" href="#L50">50</a></span>
<span id="L51" class="lineno"><a class="lineno" href="#L51">51</a></span>
<span id="L52" class="lineno"><a class="lineno" href="#L52">52</a></span>
<span id="L53" class="lineno"><a class="lineno" href="#L53">53</a></span>
<span id="L54" class="lineno"><a class="lineno" href="#L54">54</a></span>
<span id="L55" class="lineno"><a class="lineno" href="#L55">55</a></span>
<span id="L56" class="lineno"><a class="lineno" href="#L56">56</a></span>
<span id="L57" class="lineno"><a class="lineno" href="#L57">57</a></span>
<span id="L58" class="lineno"><a class="lineno" href="#L58">58</a></span>
<span id="L59" class="lineno"><a class="lineno" href="#L59">59</a></span>
<span id="L60" class="lineno"><a class="lineno" href="#L60">60</a></span>
<span id="L61" class="lineno"><a class="lineno" href="#L61">61</a></span>
<span id="L62" class="lineno"><a class="lineno" href="#L62">62</a></span>
<span id="L63" class="lineno"><a class="lineno" href="#L63">63</a></span>
<span id="L64" class="lineno"><a class="lineno" href="#L64">64</a></span>
<span id="L65" class="lineno"><a class="lineno" href="#L65">65</a></span>
<span id="L66" class="lineno"><a class="lineno" href="#L66">66</a></span>
<span id="L67" class="lineno"><a class="lineno" href="#L67">67</a></span>
<span id="L68" class="lineno"><a class="lineno" href="#L68">68</a></span>
<span id="L69" class="lineno"><a class="lineno" href="#L69">69</a></span>
<span id="L70" class="lineno"><a class="lineno" href="#L70">70</a></span>
<span id="L71" class="lineno"><a class="lineno" href="#L71">71</a></span>
<span id="L72" class="lineno"><a class="lineno" href="#L72">72</a></span>
<span id="L73" class="lineno"><a class="lineno" href="#L73">73</a></span>
<span id="L74" class="lineno"><a class="lineno" href="#L74">74</a></span>
<span id="L75" class="lineno"><a class="lineno" href="#L75">75</a></span>
<span id="L76" class="lineno"><a class="lineno" href="#L76">76</a></span>
<span id="L77" class="lineno"><a class="lineno" href="#L77">77</a></span>
<span id="L78" class="lineno"><a class="lineno" href="#L78">78</a></span>
<span id="L79" class="lineno"><a class="lineno" href="#L79">79</a></span>
<span id="L80" class="lineno"><a class="lineno" href="#L80">80</a></span>
<span id="L81" class="lineno"><a class="lineno" href="#L81">81</a></span>
<span id="L82" class="lineno"><a class="lineno" href="#L82">82</a></span>
<span id="L83" class="lineno"><a class="lineno" href="#L83">83</a></span>
<span id="L84" class="lineno"><a class="lineno" href="#L84">84</a></span>
<span id="L85" class="lineno"><a class="lineno" href="#L85">85</a></span>
<span id="L86" class="lineno"><a class="lineno" href="#L86">86</a></span>
<span id="L87" class="lineno"><a class="lineno" href="#L87">87</a></span>
<span id="L88" class="lineno"><a class="lineno" href="#L88">88</a></span>
<span id="L89" class="lineno"><a class="lineno" href="#L89">89</a></span>
<span id="L90" class="lineno"><a class="lineno" href="#L90">90</a></span>
<span id="L91" class="lineno"><a class="lineno" href="#L91">91</a></span>
<span id="L92" class="lineno"><a class="lineno" href="#L92">92</a></span>
<span id="L93" class="lineno"><a class="lineno" href="#L93">93</a></span>
<span id="L94" class="lineno"><a class="lineno" href="#L94">94</a></span>
<span id="L95" class="lineno"><a class="lineno" href="#L95">95</a></span>
<span id="L96" class="lineno"><a class="lineno" href="#L96">96</a></span>
<span id="L97" class="lineno"><a class="lineno" href="#L97">97</a></span>
<span id="L98" class="lineno"><a class="lineno" href="#L98">98</a></span>
<span id="L99" class="lineno"><a class="lineno" href="#L99">99</a></span>
<span id="L100" class="lineno"><a class="lineno" href="#L100">100</a></span>
<span id="L101" class="lineno"><a class="lineno" href="#L101">101</a></span>
<span id="L102" class="lineno"><a class="lineno" href="#L102">102</a></span>
<span id="L103" class="lineno"><a class="lineno" href="#L103">103</a></span>
<span id="L104" class="lineno"><a class="lineno" href="#L104">104</a></span>
<span id="L105" class="lineno"><a class="lineno" href="#L105">105</a></span>
<span id="L106" class="lineno"><a class="lineno" href="#L106">106</a></span>
<span id="L107" class="lineno"><a class="lineno" href="#L107">107</a></span>
<span id="L108" class="lineno"><a class="lineno" href="#L108">108</a></span>
<span id="L109" class="lineno"><a class="lineno" href="#L109">109</a></span>
<span id="L110" class="lineno"><a class="lineno" href="#L110">110</a></span>
<span id="L111" class="lineno"><a class="lineno" href="#L111">111</a></span>
<span id="L112" class="lineno"><a class="lineno" href="#L112">112</a></span>
<span id="L113" class="lineno"><a class="lineno" href="#L113">113</a></span>
<span id="L114" class="lineno"><a class="lineno" href="#L114">114</a></span>
<span id="L115" class="lineno"><a class="lineno" href="#L115">115</a></span>
<span id="L116" class="lineno"><a class="lineno" href="#L116">116</a></span>
<span id="L117" class="lineno"><a class="lineno" href="#L117">117</a></span>
<span id="L118" class="lineno"><a class="lineno" href="#L118">118</a></span>
<span id="L119" class="lineno"><a class="lineno" href="#L119">119</a></span>
<span id="L120" class="lineno"><a class="lineno" href="#L120">120</a></span>
<span id="L121" class="lineno"><a class="lineno" href="#L121">121</a></span>
<span id="L122" class="lineno"><a class="lineno" href="#L122">122</a></span>
<span id="L123" class="lineno"><a class="lineno" href="#L123">123</a></span>
<span id="L124" class="lineno"><a class="lineno" href="#L124">124</a></span>
<span id="L125" class="lineno"><a class="lineno" href="#L125">125</a></span>
<span id="L126" class="lineno"><a class="lineno" href="#L126">126</a></span>
<span id="L127" class="lineno"><a class="lineno" href="#L127">127</a></span>
<span id="L128" class="lineno"><a class="lineno" href="#L128">128</a></span>
<span id="L129" class="lineno"><a class="lineno" href="#L129">129</a></span>
<span id="L130" class="lineno"><a class="lineno" href="#L130">130</a></span>
<span id="L131" class="lineno"><a class="lineno" href="#L131">131</a></span>
<span id="L132" class="lineno"><a class="lineno" href="#L132">132</a></span>
<span id="L133" class="lineno"><a class="lineno" href="#L133">133</a></span>
<span id="L134" class="lineno"><a class="lineno" href="#L134">134</a></span>
<span id="L135" class="lineno"><a class="lineno" href="#L135">135</a></span>
<span id="L136" class="lineno"><a class="lineno" href="#L136">136</a></span>
<span id="L137" class="lineno"><a class="lineno" href="#L137">137</a></span>
<span id="L138" class="lineno"><a class="lineno" href="#L138">138</a></span>
<span id="L139" class="lineno"><a class="lineno" href="#L139">139</a></span>
<span id="L140" class="lineno"><a class="lineno" href="#L140">140</a></span>
<span id="L141" class="lineno"><a class="lineno" href="#L141">141</a></span>
<span id="L142" class="lineno"><a class="lineno" href="#L142">142</a></span>
<span id="L143" class="lineno"><a class="lineno" href="#L143">143</a></span>
<span id="L144" class="lineno"><a class="lineno" href="#L144">144</a></span>
<span id="L145" class="lineno"><a class="lineno" href="#L145">145</a></span>
<span id="L146" class="lineno"><a class="lineno" href="#L146">146</a></span>
<span id="L147" class="lineno"><a class="lineno" href="#L147">147</a></span>
<span id="L148" class="lineno"><a class="lineno" href="#L148">148</a></span>
<span id="L149" class="lineno"><a class="lineno" href="#L149">149</a></span>
<span id="L150" class="lineno"><a class="lineno" href="#L150">150</a></span>
<span id="L151" class="lineno"><a class="lineno" href="#L151">151</a></span>
<span id="L152" class="lineno"><a class="lineno" href="#L152">152</a></span>
<span id="L153" class="lineno"><a class="lineno" href="#L153">153</a></span>
<span id="L154" class="lineno"><a class="lineno" href="#L154">154</a></span>
<span id="L155" class="lineno"><a class="lineno" href="#L155">155</a></span>
<span id="L156" class="lineno"><a class="lineno" href="#L156">156</a></span>
<span id="L157" class="lineno"><a class="lineno" href="#L157">157</a></span>
<span id="L158" class="lineno"><a class="lineno" href="#L158">158</a></span>
<span id="L159" class="lineno"><a class="lineno" href="#L159">159</a></span>
<span id="L160" class="lineno"><a class="lineno" href="#L160">160</a></span>
<span id="L161" class="lineno"><a class="lineno" href="#L161">161</a></span>
<span id="L162" class="lineno"><a class="lineno" href="#L162">162</a></span>
<span id="L163" class="lineno"><a class="lineno" href="#L163">163</a></span>
<span id="L164" class="lineno"><a class="lineno" href="#L164">164</a></span>
<span id="L165" class="lineno"><a class="lineno" href="#L165">165</a></span>
<span id="L166" class="lineno"><a class="lineno" href="#L166">166</a></span>
<span id="L167" class="lineno"><a class="lineno" href="#L167">167</a></span>
<span id="L168" class="lineno"><a class="lineno" href="#L168">168</a></span>
<span id="L169" class="lineno"><a class="lineno" href="#L169">169</a></span>
<span id="L170" class="lineno"><a class="lineno" href="#L170">170</a></span>
<span id="L171" class="lineno"><a class="lineno" href="#L171">171</a></span>
<span id="L172" class="lineno"><a class="lineno" href="#L172">172</a></span>
<span id="L173" class="lineno"><a class="lineno" href="#L173">173</a></span>
<span id="L174" class="lineno"><a class="lineno" href="#L174">174</a></span>
<span id="L175" class="lineno"><a class="lineno" href="#L175">175</a></span>
<span id="L176" class="lineno"><a class="lineno" href="#L176">176</a></span>
<span id="L177" class="lineno"><a class="lineno" href="#L177">177</a></span>
<span id="L178" class="lineno"><a class="lineno" href="#L178">178</a></span>
<span id="L179" class="lineno"><a class="lineno" href="#L179">179</a></span>
<span id="L180" class="lineno"><a class="lineno" href="#L180">180</a></span>
<span id="L181" class="lineno"><a class="lineno" href="#L181">181</a></span>
<span id="L182" class="lineno"><a class="lineno" href="#L182">182</a></span>
<span id="L183" class="lineno"><a class="lineno" href="#L183">183</a></span>
<span id="L184" class="lineno"><a class="lineno" href="#L184">184</a></span>
<span id="L185" class="lineno"><a class="lineno" href="#L185">185</a></span>
<span id="L186" class="lineno"><a class="lineno" href="#L186">186</a></span>
<span id="L187" class="lineno"><a class="lineno" href="#L187">187</a></span>
<span id="L188" class="lineno"><a class="lineno" href="#L188">188</a></span>
<span id="L189" class="lineno"><a class="lineno" href="#L189">189</a></span>
<span id="L190" class="lineno"><a class="lineno" href="#L190">190</a></span>
<span id="L191" class="lineno"><a class="lineno" href="#L191">191</a></span>
<span id="L192" class="lineno"><a class="lineno" href="#L192">192</a></span>
<span id="L193" class="lineno"><a class="lineno" href="#L193">193</a></span>
<span id="L194" class="lineno"><a class="lineno" href="#L194">194</a></span>
<span id="L195" class="lineno"><a class="lineno" href="#L195">195</a></span>
<span id="L196" class="lineno"><a class="lineno" href="#L196">196</a></span>
<span id="L197" class="lineno"><a class="lineno" href="#L197">197</a></span>
<span id="L198" class="lineno"><a class="lineno" href="#L198">198</a></span>
<span id="L199" class="lineno"><a class="lineno" href="#L199">199</a></span>
<span id="L200" class="lineno"><a class="lineno" href="#L200">200</a></span>
<span id="L201" class="lineno"><a class="lineno" href="#L201">201</a></span>
<span id="L202" class="lineno"><a class="lineno" href="#L202">202</a></span>
<span id="L203" class="lineno"><a class="lineno" href="#L203">203</a></span>
<span id="L204" class="lineno"><a class="lineno" href="#L204">204</a></span>
<span id="L205" class="lineno"><a class="lineno" href="#L205">205</a></span>
<span id="L206" class="lineno"><a class="lineno" href="#L206">206</a></span>
<span id="L207" class="lineno"><a class="lineno" href="#L207">207</a></span>
<span id="L208" class="lineno"><a class="lineno" href="#L208">208</a></span>
<span id="L209" class="lineno"><a class="lineno" href="#L209">209</a></span>
<span id="L210" class="lineno"><a class="lineno" href="#L210">210</a></span>
<span id="L211" class="lineno"><a class="lineno" href="#L211">211</a></span>
<span id="L212" class="lineno"><a class="lineno" href="#L212">212</a></span>
<span id="L213" class="lineno"><a class="lineno" href="#L213">213</a></span>
<span id="L214" class="lineno"><a class="lineno" href="#L214">214</a></span>
<span id="L215" class="lineno"><a class="lineno" href="#L215">215</a></span>
<span id="L216" class="lineno"><a class="lineno" href="#L216">216</a></span>
<span id="L217" class="lineno"><a class="lineno" href="#L217">217</a></span>
<span id="L218" class="lineno"><a class="lineno" href="#L218">218</a></span>
<span id="L219" class="lineno"><a class="lineno" href="#L219">219</a></span>
<span id="L220" class="lineno"><a class="lineno" href="#L220">220</a></span>
<span id="L221" class="lineno"><a class="lineno" href="#L221">221</a></span>
<span id="L222" class="lineno"><a class="lineno" href="#L222">222</a></span>
<span id="L223" class="lineno"><a class="lineno" href="#L223">223</a></span>
<span id="L224" class="lineno"><a class="lineno" href="#L224">224</a></span>
<span id="L225" class="lineno"><a class="lineno" href="#L225">225</a></span>
<span id="L226" class="lineno"><a class="lineno" href="#L226">226</a></span>
<span id="L227" class="lineno"><a class="lineno" href="#L227">227</a></span>
<span id="L228" class="lineno"><a class="lineno" href="#L228">228</a></span>
<span id="L229" class="lineno"><a class="lineno" href="#L229">229</a></span>
<span id="L230" class="lineno"><a class="lineno" href="#L230">230</a></span>
<span id="L231" class="lineno"><a class="lineno" href="#L231">231</a></span>
<span id="L232" class="lineno"><a class="lineno" href="#L232">232</a></span>
<span id="L233" class="lineno"><a class="lineno" href="#L233">233</a></span>
<span id="L234" class="lineno"><a class="lineno" href="#L234">234</a></span>
<span id="L235" class="lineno"><a class="lineno" href="#L235">235</a></span>
<span id="L236" class="lineno"><a class="lineno" href="#L236">236</a></span>
<span id="L237" class="lineno"><a class="lineno" href="#L237">237</a></span>
<span id="L238" class="lineno"><a class="lineno" href="#L238">238</a></span>
<span id="L239" class="lineno"><a class="lineno" href="#L239">239</a></span>
<span id="L240" class="lineno"><a class="lineno" href="#L240">240</a></span>
<span id="L241" class="lineno"><a class="lineno" href="#L241">241</a></span>
<span id="L242" class="lineno"><a class="lineno" href="#L242">242</a></span>
<span id="L243" class="lineno"><a class="lineno" href="#L243">243</a></span>
<span id="L244" class="lineno"><a class="lineno" href="#L244">244</a></span>
<span id="L245" class="lineno"><a class="lineno" href="#L245">245</a></span>
<span id="L246" class="lineno"><a class="lineno" href="#L246">246</a></span>
<span id="L247" class="lineno"><a class="lineno" href="#L247">247</a></span>
<span id="L248" class="lineno"><a class="lineno" href="#L248">248</a></span>
<span id="L249" class="lineno"><a class="lineno" href="#L249">249</a></span>
<span id="L250" class="lineno"><a class="lineno" href="#L250">250</a></span>
<span id="L251" class="lineno"><a class="lineno" href="#L251">251</a></span>
<span id="L252" class="lineno"><a class="lineno" href="#L252">252</a></span>
<span id="L253" class="lineno"><a class="lineno" href="#L253">253</a></span>
<span id="L254" class="lineno"><a class="lineno" href="#L254">254</a></span>
<span id="L255" class="lineno"><a class="lineno" href="#L255">255</a></span>
<span id="L256" class="lineno"><a class="lineno" href="#L256">256</a></span>
<span id="L257" class="lineno"><a class="lineno" href="#L257">257</a></span>
<span id="L258" class="lineno"><a class="lineno" href="#L258">258</a></span>
<span id="L259" class="lineno"><a class="lineno" href="#L259">259</a></span>
<span id="L260" class="lineno"><a class="lineno" href="#L260">260</a></span>
<span id="L261" class="lineno"><a class="lineno" href="#L261">261</a></span>
<span id="L262" class="lineno"><a class="lineno" href="#L262">262</a></span>
<span id="L263" class="lineno"><a class="lineno" href="#L263">263</a></span>
<span id="L264" class="lineno"><a class="lineno" href="#L264">264</a></span>
<span id="L265" class="lineno"><a class="lineno" href="#L265">265</a></span>
<span id="L266" class="lineno"><a class="lineno" href="#L266">266</a></span>
<span id="L267" class="lineno"><a class="lineno" href="#L267">267</a></span>
<span id="L268" class="lineno"><a class="lineno" href="#L268">268</a></span>
<span id="L269" class="lineno"><a class="lineno" href="#L269">269</a></span>
<span id="L270" class="lineno"><a class="lineno" href="#L270">270</a></span>
<span id="L271" class="lineno"><a class="lineno" href="#L271">271</a></span>
<span id="L272" class="lineno"><a class="lineno" href="#L272">272</a></span>
<span id="L273" class="lineno"><a class="lineno" href="#L273">273</a></span>
<span id="L274" class="lineno"><a class="lineno" href="#L274">274</a></span>
<span id="L275" class="lineno"><a class="lineno" href="#L275">275</a></span>
<span id="L276" class="lineno"><a class="lineno" href="#L276">276</a></span>
<span id="L277" class="lineno"><a class="lineno" href="#L277">277</a></span>
<span id="L278" class="lineno"><a class="lineno" href="#L278">278</a></span>
<span id="L279" class="lineno"><a class="lineno" href="#L279">279</a></span>
<span id="L280" class="lineno"><a class="lineno" href="#L280">280</a></span>
<span id="L281" class="lineno"><a class="lineno" href="#L281">281</a></span>
<span id="L282" class="lineno"><a class="lineno" href="#L282">282</a></span>
<span id="L283" class="lineno"><a class="lineno" href="#L283">283</a></span>
<span id="L284" class="lineno"><a class="lineno" href="#L284">284</a></span>
<span id="L285" class="lineno"><a class="lineno" href="#L285">285</a></span>
<span id="L286" class="lineno"><a class="lineno" href="#L286">286</a></span>
<span id="L287" class="lineno"><a class="lineno" href="#L287">287</a></span>
<span id="L288" class="lineno"><a class="lineno" href="#L288">288</a></span>
<span id="L289" class="lineno"><a class="lineno" href="#L289">289</a></span>
<span id="L290" class="lineno"><a class="lineno" href="#L290">290</a></span>
<span id="L291" class="lineno"><a class="lineno" href="#L291">291</a></span>
<span id="L292" class="lineno"><a class="lineno" href="#L292">292</a></span>
<span id="L293" class="lineno"><a class="lineno" href="#L293">293</a></span>
<span id="L294" class="lineno"><a class="lineno" href="#L294">294</a></span>
<span id="L295" class="lineno"><a class="lineno" href="#L295">295</a></span>
<span id="L296" class="lineno"><a class="lineno" href="#L296">296</a></span>
<span id="L297" class="lineno"><a class="lineno" href="#L297">297</a></span>
<span id="L298" class="lineno"><a class="lineno" href="#L298">298</a></span>
<span id="L299" class="lineno"><a class="lineno" href="#L299">299</a></span>
<span id="L300" class="lineno"><a class="lineno" href="#L300">300</a></span>
<span id="L301" class="lineno"><a class="lineno" href="#L301">301</a></span>
<span id="L302" class="lineno"><a class="lineno" href="#L302">302</a></span>
<span id="L303" class="lineno"><a class="lineno" href="#L303">303</a></span>
<span id="L304" class="lineno"><a class="lineno" href="#L304">304</a></span>
<span id="L305" class="lineno"><a class="lineno" href="#L305">305</a></span>
<span id="L306" class="lineno"><a class="lineno" href="#L306">306</a></span>
<span id="L307" class="lineno"><a class="lineno" href="#L307">307</a></span>
<span id="L308" class="lineno"><a class="lineno" href="#L308">308</a></span>
<span id="L309" class="lineno"><a class="lineno" href="#L309">309</a></span>
<span id="L310" class="lineno"><a class="lineno" href="#L310">310</a></span>
<span id="L311" class="lineno"><a class="lineno" href="#L311">311</a></span>
<span id="L312" class="lineno"><a class="lineno" href="#L312">312</a></span>
<span id="L313" class="lineno"><a class="lineno" href="#L313">313</a></span>
<span id="L314" class="lineno"><a class="lineno" href="#L314">314</a></span>
<span id="L315" class="lineno"><a class="lineno" href="#L315">315</a></span>
<span id="L316" class="lineno"><a class="lineno" href="#L316">316</a></span>
<span id="L317" class="lineno"><a class="lineno" href="#L317">317</a></span>
<span id="L318" class="lineno"><a class="lineno" href="#L318">318</a></span>
<span id="L319" class="lineno"><a class="lineno" href="#L319">319</a></span>
<span id="L320" class="lineno"><a class="lineno" href="#L320">320</a></span>
<span id="L321" class="lineno"><a class="lineno" href="#L321">321</a></span>
<span id="L322" class="lineno"><a class="lineno" href="#L322">322</a></span>
<span id="L323" class="lineno"><a class="lineno" href="#L323">323</a></span>
<span id="L324" class="lineno"><a class="lineno" href="#L324">324</a></span>
<span id="L325" class="lineno"><a class="lineno" href="#L325">325</a></span>
<span id="L326" class="lineno"><a class="lineno" href="#L326">326</a></span>
<span id="L327" class="lineno"><a class="lineno" href="#L327">327</a></span>
<span id="L328" class="lineno"><a class="lineno" href="#L328">328</a></span>
<span id="L329" class="lineno"><a class="lineno" href="#L329">329</a></span>
<span id="L330" class="lineno"><a class="lineno" href="#L330">330</a></span>
<span id="L331" class="lineno"><a class="lineno" href="#L331">331</a></span>
<span id="L332" class="lineno"><a class="lineno" href="#L332">332</a></span>
<span id="L333" class="lineno"><a class="lineno" href="#L333">333</a></span>
<span id="L334" class="lineno"><a class="lineno" href="#L334">334</a></span>
<span id="L335" class="lineno"><a class="lineno" href="#L335">335</a></span>
<span id="L336" class="lineno"><a class="lineno" href="#L336">336</a></span>
<span id="L337" class="lineno"><a class="lineno" href="#L337">337</a></span>
<span id="L338" class="lineno"><a class="lineno" href="#L338">338</a></span>
<span id="L339" class="lineno"><a class="lineno" href="#L339">339</a></span>
<span id="L340" class="lineno"><a class="lineno" href="#L340">340</a></span>
<span id="L341" class="lineno"><a class="lineno" href="#L341">341</a></span>
<span id="L342" class="lineno"><a class="lineno" href="#L342">342</a></span>
<span id="L343" class="lineno"><a class="lineno" href="#L343">343</a></span>
<span id="L344" class="lineno"><a class="lineno" href="#L344">344</a></span>
<span id="L345" class="lineno"><a class="lineno" href="#L345">345</a></span>
<span id="L346" class="lineno"><a class="lineno" href="#L346">346</a></span>
<span id="L347" class="lineno"><a class="lineno" href="#L347">347</a></span>
<span id="L348" class="lineno"><a class="lineno" href="#L348">348</a></span>
<span id="L349" class="lineno"><a class="lineno" href="#L349">349</a></span>
<span id="L350" class="lineno"><a class="lineno" href="#L350">350</a></span>
<span id="L351" class="lineno"><a class="lineno" href="#L351">351</a></span>
<span id="L352" class="lineno"><a class="lineno" href="#L352">352</a></span>
<span id="L353" class="lineno"><a class="lineno" href="#L353">353</a></span>
<span id="L354" class="lineno"><a class="lineno" href="#L354">354</a></span>
<span id="L355" class="lineno"><a class="lineno" href="#L355">355</a></span>
<span id="L356" class="lineno"><a class="lineno" href="#L356">356</a></span>
<span id="L357" class="lineno"><a class="lineno" href="#L357">357</a></span>
<span id="L358" class="lineno"><a class="lineno" href="#L358">358</a></span>
<span id="L359" class="lineno"><a class="lineno" href="#L359">359</a></span>
<span id="L360" class="lineno"><a class="lineno" href="#L360">360</a></span>
<span id="L361" class="lineno"><a class="lineno" href="#L361">361</a></span>
<span id="L362" class="lineno"><a class="lineno" href="#L362">362</a></span>
<span id="L363" class="lineno"><a class="lineno" href="#L363">363</a></span>
<span id="L364" class="lineno"><a class="lineno" href="#L364">364</a></span>
<span id="L365" class="lineno"><a class="lineno" href="#L365">365</a></span>
<span id="L366" class="lineno"><a class="lineno" href="#L366">366</a></span>
<span id="L367" class="lineno"><a class="lineno" href="#L367">367</a></span>
<span id="L368" class="lineno"><a class="lineno" href="#L368">368</a></span>
<span id="L369" class="lineno"><a class="lineno" href="#L369">369</a></span>
<span id="L370" class="lineno"><a class="lineno" href="#L370">370</a></span>
<span id="L371" class="lineno"><a class="lineno" href="#L371">371</a></span>
<span id="L372" class="lineno"><a class="lineno" href="#L372">372</a></span>
<span id="L373" class="lineno"><a class="lineno" href="#L373">373</a></span>
<span id="L374" class="lineno"><a class="lineno" href="#L374">374</a></span>
<span id="L375" class="lineno"><a class="lineno" href="#L375">375</a></span>
<span id="L376" class="lineno"><a class="lineno" href="#L376">376</a></span>
<span id="L377" class="lineno"><a class="lineno" href="#L377">377</a></span>
<span id="L378" class="lineno"><a class="lineno" href="#L378">378</a></span>
<span id="L379" class="lineno"><a class="lineno" href="#L379">379</a></span>
<span id="L380" class="lineno"><a class="lineno" href="#L380">380</a></span>
<span id="L381" class="lineno"><a class="lineno" href="#L381">381</a></span>
<span id="L382" class="lineno"><a class="lineno" href="#L382">382</a></span>
<span id="L383" class="lineno"><a class="lineno" href="#L383">383</a></span>
<span id="L384" class="lineno"><a class="lineno" href="#L384">384</a></span>
<span id="L385" class="lineno"><a class="lineno" href="#L385">385</a></span>
<span id="L386" class="lineno"><a class="lineno" href="#L386">386</a></span>
<span id="L387" class="lineno"><a class="lineno" href="#L387">387</a></span>
<span id="L388" class="lineno"><a class="lineno" href="#L388">388</a></span>
<span id="L389" class="lineno"><a class="lineno" href="#L389">389</a></span>
<span id="L390" class="lineno"><a class="lineno" href="#L390">390</a></span>
<span id="L391" class="lineno"><a class="lineno" href="#L391">391</a></span>
<span id="L392" class="lineno"><a class="lineno" href="#L392">392</a></span>
<span id="L393" class="lineno"><a class="lineno" href="#L393">393</a></span>
<span id="L394" class="lineno"><a class="lineno" href="#L394">394</a></span>
<span id="L395" class="lineno"><a class="lineno" href="#L395">395</a></span>
<span id="L396" class="lineno"><a class="lineno" href="#L396">396</a></span>
<span id="L397" class="lineno"><a class="lineno" href="#L397">397</a></span>
<span id="L398" class="lineno"><a class="lineno" href="#L398">398</a></span>
<span id="L399" class="lineno"><a class="lineno" href="#L399">399</a></span>
<span id="L400" class="lineno"><a class="lineno" href="#L400">400</a></span>
<span id="L401" class="lineno"><a class="lineno" href="#L401">401</a></span>
<span id="L402" class="lineno"><a class="lineno" href="#L402">402</a></span>
<span id="L403" class="lineno"><a class="lineno" href="#L403">403</a></span>
<span id="L404" class="lineno"><a class="lineno" href="#L404">404</a></span>
<span id="L405" class="lineno"><a class="lineno" href="#L405">405</a></span>
<span id="L406" class="lineno"><a class="lineno" href="#L406">406</a></span>
<span id="L407" class="lineno"><a class="lineno" href="#L407">407</a></span>
<span id="L408" class="lineno"><a class="lineno" href="#L408">408</a></span>
<span id="L409" class="lineno"><a class="lineno" href="#L409">409</a></span>
<span id="L410" class="lineno"><a class="lineno" href="#L410">410</a></span>
<span id="L411" class="lineno"><a class="lineno" href="#L411">411</a></span>
<span id="L412" class="lineno"><a class="lineno" href="#L412">412</a></span>
<span id="L413" class="lineno"><a class="lineno" href="#L413">413</a></span>
<span id="L414" class="lineno"><a class="lineno" href="#L414">414</a></span>
<span id="L415" class="lineno"><a class="lineno" href="#L415">415</a></span>
<span id="L416" class="lineno"><a class="lineno" href="#L416">416</a></span>
<span id="L417" class="lineno"><a class="lineno" href="#L417">417</a></span>
<span id="L418" class="lineno"><a class="lineno" href="#L418">418</a></span>
<span id="L419" class="lineno"><a class="lineno" href="#L419">419</a></span>
<span id="L420" class="lineno"><a class="lineno" href="#L420">420</a></span>
<span id="L421" class="lineno"><a class="lineno" href="#L421">421</a></span>
<span id="L422" class="lineno"><a class="lineno" href="#L422">422</a></span>
<span id="L423" class="lineno"><a class="lineno" href="#L423">423</a></span>
<span id="L424" class="lineno"><a class="lineno" href="#L424">424</a></span>
<span id="L425" class="lineno"><a class="lineno" href="#L425">425</a></span>
<span id="L426" class="lineno"><a class="lineno" href="#L426">426</a></span>
<span id="L427" class="lineno"><a class="lineno" href="#L427">427</a></span>
<span id="L428" class="lineno"><a class="lineno" href="#L428">428</a></span>
<span id="L429" class="lineno"><a class="lineno" href="#L429">429</a></span>
<span id="L430" class="lineno"><a class="lineno" href="#L430">430</a></span>
<span id="L431" class="lineno"><a class="lineno" href="#L431">431</a></span>
<span id="L432" class="lineno"><a class="lineno" href="#L432">432</a></span>
<span id="L433" class="lineno"><a class="lineno" href="#L433">433</a></span>
<span id="L434" class="lineno"><a class="lineno" href="#L434">434</a></span>
<span id="L435" class="lineno"><a class="lineno" href="#L435">435</a></span>
<span id="L436" class="lineno"><a class="lineno" href="#L436">436</a></span>
<span id="L437" class="lineno"><a class="lineno" href="#L437">437</a></span>
<span id="L438" class="lineno"><a class="lineno" href="#L438">438</a></span>
<span id="L439" class="lineno"><a class="lineno" href="#L439">439</a></span>
<span id="L440" class="lineno"><a class="lineno" href="#L440">440</a></span>
<span id="L441" class="lineno"><a class="lineno" href="#L441">441</a></span>
<span id="L442" class="lineno"><a class="lineno" href="#L442">442</a></span>
<span id="L443" class="lineno"><a class="lineno" href="#L443">443</a></span>
<span id="L444" class="lineno"><a class="lineno" href="#L444">444</a></span>
<span id="L445" class="lineno"><a class="lineno" href="#L445">445</a></span>
<span id="L446" class="lineno"><a class="lineno" href="#L446">446</a></span>
<span id="L447" class="lineno"><a class="lineno" href="#L447">447</a></span>
<span id="L448" class="lineno"><a class="lineno" href="#L448">448</a></span>
<span id="L449" class="lineno"><a class="lineno" href="#L449">449</a></span>
<span id="L450" class="lineno"><a class="lineno" href="#L450">450</a></span>
<span id="L451" class="lineno"><a class="lineno" href="#L451">451</a></span>
<span id="L452" class="lineno"><a class="lineno" href="#L452">452</a></span>
<span id="L453" class="lineno"><a class="lineno" href="#L453">453</a></span>
<span id="L454" class="lineno"><a class="lineno" href="#L454">454</a></span>
<span id="L455" class="lineno"><a class="lineno" href="#L455">455</a></span>
<span id="L456" class="lineno"><a class="lineno" href="#L456">456</a></span>
<span id="L457" class="lineno"><a class="lineno" href="#L457">457</a></span>
<span id="L458" class="lineno"><a class="lineno" href="#L458">458</a></span>
<span id="L459" class="lineno"><a class="lineno" href="#L459">459</a></span>
<span id="L460" class="lineno"><a class="lineno" href="#L460">460</a></span>
<span id="L461" class="lineno"><a class="lineno" href="#L461">461</a></span>
<span id="L462" class="lineno"><a class="lineno" href="#L462">462</a></span>
<span id="L463" class="lineno"><a class="lineno" href="#L463">463</a></span>
<span id="L464" class="lineno"><a class="lineno" href="#L464">464</a></span>
<span id="L465" class="lineno"><a class="lineno" href="#L465">465</a></span>
<span id="L466" class="lineno"><a class="lineno" href="#L466">466</a></span>
<span id="L467" class="lineno"><a class="lineno" href="#L467">467</a></span>
<span id="L468" class="lineno"><a class="lineno" href="#L468">468</a></span>
<span id="L469" class="lineno"><a class="lineno" href="#L469">469</a></span>
<span id="L470" class="lineno"><a class="lineno" href="#L470">470</a></span>
<span id="L471" class="lineno"><a class="lineno" href="#L471">471</a></span>
<span id="L472" class="lineno"><a class="lineno" href="#L472">472</a></span>
<span id="L473" class="lineno"><a class="lineno" href="#L473">473</a></span>
<span id="L474" class="lineno"><a class="lineno" href="#L474">474</a></span>
<span id="L475" class="lineno"><a class="lineno" href="#L475">475</a></span>
<span id="L476" class="lineno"><a class="lineno" href="#L476">476</a></span>
<span id="L477" class="lineno"><a class="lineno" href="#L477">477</a></span>
<span id="L478" class="lineno"><a class="lineno" href="#L478">478</a></span>
<span id="L479" class="lineno"><a class="lineno" href="#L479">479</a></span>
<span id="L480" class="lineno"><a class="lineno" href="#L480">480</a></span>
<span id="L481" class="lineno"><a class="lineno" href="#L481">481</a></span>
<span id="L482" class="lineno"><a class="lineno" href="#L482">482</a></span>
<span id="L483" class="lineno"><a class="lineno" href="#L483">483</a></span>
<span id="L484" class="lineno"><a class="lineno" href="#L484">484</a></span>
<span id="L485" class="lineno"><a class="lineno" href="#L485">485</a></span>
<span id="L486" class="lineno"><a class="lineno" href="#L486">486</a></span>
<span id="L487" class="lineno"><a class="lineno" href="#L487">487</a></span>
<span id="L488" class="lineno"><a class="lineno" href="#L488">488</a></span>
<span id="L489" class="lineno"><a class="lineno" href="#L489">489</a></span>
<span id="L490" class="lineno"><a class="lineno" href="#L490">490</a></span>
<span id="L491" class="lineno"><a class="lineno" href="#L491">491</a></span>
<span id="L492" class="lineno"><a class="lineno" href="#L492">492</a></span>
<span id="L493" class="lineno"><a class="lineno" href="#L493">493</a></span>
<span id="L494" class="lineno"><a class="lineno" href="#L494">494</a></span>
<span id="L495" class="lineno"><a class="lineno" href="#L495">495</a></span>
<span id="L496" class="lineno"><a class="lineno" href="#L496">496</a></span>
<span id="L497" class="lineno"><a class="lineno" href="#L497">497</a></span>
<span id="L498" class="lineno"><a class="lineno" href="#L498">498</a></span>
<span id="L499" class="lineno"><a class="lineno" href="#L499">499</a></span>
<span id="L500" class="lineno"><a class="lineno" href="#L500">500</a></span>
<span id="L501" class="lineno"><a class="lineno" href="#L501">501</a></span>
<span id="L502" class="lineno"><a class="lineno" href="#L502">502</a></span>
<span id="L503" class="lineno"><a class="lineno" href="#L503">503</a></span>
<span id="L504" class="lineno"><a class="lineno" href="#L504">504</a></span>
<span id="L505" class="lineno"><a class="lineno" href="#L505">505</a></span>
<span id="L506" class="lineno"><a class="lineno" href="#L506">506</a></span>
<span id="L507" class="lineno"><a class="lineno" href="#L507">507</a></span>
<span id="L508" class="lineno"><a class="lineno" href="#L508">508</a></span>
<span id="L509" class="lineno"><a class="lineno" href="#L509">509</a></span>
<span id="L510" class="lineno"><a class="lineno" href="#L510">510</a></span>
<span id="L511" class="lineno"><a class="lineno" href="#L511">511</a></span>
<span id="L512" class="lineno"><a class="lineno" href="#L512">512</a></span>
<span id="L513" class="lineno"><a class="lineno" href="#L513">513</a></span>
<span id="L514" class="lineno"><a class="lineno" href="#L514">514</a></span>
<span id="L515" class="lineno"><a class="lineno" href="#L515">515</a></span>
<span id="L516" class="lineno"><a class="lineno" href="#L516">516</a></span>
<span id="L517" class="lineno"><a class="lineno" href="#L517">517</a></span>
<span id="L518" class="lineno"><a class="lineno" href="#L518">518</a></span>
<span id="L519" class="lineno"><a class="lineno" href="#L519">519</a></span>
<span id="L520" class="lineno"><a class="lineno" href="#L520">520</a></span>
<span id="L521" class="lineno"><a class="lineno" href="#L521">521</a></span>
<span id="L522" class="lineno"><a class="lineno" href="#L522">522</a></span>
<span id="L523" class="lineno"><a class="lineno" href="#L523">523</a></span>
<span id="L524" class="lineno"><a class="lineno" href="#L524">524</a></span>
<span id="L525" class="lineno"><a class="lineno" href="#L525">525</a></span>
<span id="L526" class="lineno"><a class="lineno" href="#L526">526</a></span>
<span id="L527" class="lineno"><a class="lineno" href="#L527">527</a></span>
<span id="L528" class="lineno"><a class="lineno" href="#L528">528</a></span>
<span id="L529" class="lineno"><a class="lineno" href="#L529">529</a></span>
<span id="L530" class="lineno"><a class="lineno" href="#L530">530</a></span>
<span id="L531" class="lineno"><a class="lineno" href="#L531">531</a></span>
<span id="L532" class="lineno"><a class="lineno" href="#L532">532</a></span>
<span id="L533" class="lineno"><a class="lineno" href="#L533">533</a></span>
<span id="L534" class="lineno"><a class="lineno" href="#L534">534</a></span>
<span id="L535" class="lineno"><a class="lineno" href="#L535">535</a></span>
<span id="L536" class="lineno"><a class="lineno" href="#L536">536</a></span>
<span id="L537" class="lineno"><a class="lineno" href="#L537">537</a></span>
<span id="L538" class="lineno"><a class="lineno" href="#L538">538</a></span>
<span id="L539" class="lineno"><a class="lineno" href="#L539">539</a></span>
<span id="L540" class="lineno"><a class="lineno" href="#L540">540</a></span>
<span id="L541" class="lineno"><a class="lineno" href="#L541">541</a></span>
<span id="L542" class="lineno"><a class="lineno" href="#L542">542</a></span>
<span id="L543" class="lineno"><a class="lineno" href="#L543">543</a></span>
<span id="L544" class="lineno"><a class="lineno" href="#L544">544</a></span>
<span id="L545" class="lineno"><a class="lineno" href="#L545">545</a></span>
<span id="L546" class="lineno"><a class="lineno" href="#L546">546</a></span>
<span id="L547" class="lineno"><a class="lineno" href="#L547">547</a></span>
<span id="L548" class="lineno"><a class="lineno" href="#L548">548</a></span>
<span id="L549" class="lineno"><a class="lineno" href="#L549">549</a></span>
<span id="L550" class="lineno"><a class="lineno" href="#L550">550</a></span>
<span id="L551" class="lineno"><a class="lineno" href="#L551">551</a></span>
<span id="L552" class="lineno"><a class="lineno" href="#L552">552</a></span>
<span id="L553" class="lineno"><a class="lineno" href="#L553">553</a></span>
<span id="L554" class="lineno"><a class="lineno" href="#L554">554</a></span>
<span id="L555" class="lineno"><a class="lineno" href="#L555">555</a></span>
<span id="L556" class="lineno"><a class="lineno" href="#L556">556</a></span>
</pre></td>
<td class="table-code"><pre><span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!">Request tracing and correlation ID middleware for the Google Ads AI Agent System.</span>
<span class="line-empty" title="No Anys on this line!">Provides distributed tracing, request correlation, and performance monitoring.</span>
<span class="line-empty" title="No Anys on this line!">"""</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">import time</span>
<span class="line-precise" title="No Anys on this line!">import uuid</span>
<span class="line-precise" title="No Anys on this line!">import json</span>
<span class="line-precise" title="No Anys on this line!">import asyncio</span>
<span class="line-precise" title="No Anys on this line!">from typing import Dict, List, Optional, Set, Callable, Any, Union</span>
<span class="line-precise" title="No Anys on this line!">from datetime import datetime, timezone</span>
<span class="line-precise" title="No Anys on this line!">from contextlib import asynccontextmanager</span>
<span class="line-precise" title="No Anys on this line!">from dataclasses import dataclass, asdict</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="No Anys on this line!">import structlog</span>
<span class="line-precise" title="No Anys on this line!">from fastapi import Request, Response</span>
<span class="line-precise" title="No Anys on this line!">from starlette.middleware.base import BaseHTTPMiddleware</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">from utils.config import settings</span>
<span class="line-precise" title="No Anys on this line!">from services.redis_service import RedisService</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="Any Types on this line: 
Unimported (x4)">logger = structlog.get_logger(__name__)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x4)">@dataclass</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x4)">class TraceSpan:</span>
<span class="line-empty" title="No Anys on this line!">    """Represents a trace span for distributed tracing."""</span>
<span class="line-precise" title="No Anys on this line!">    span_id: str</span>
<span class="line-precise" title="No Anys on this line!">    trace_id: str</span>
<span class="line-precise" title="No Anys on this line!">    parent_span_id: Optional[str]</span>
<span class="line-precise" title="No Anys on this line!">    operation_name: str</span>
<span class="line-precise" title="No Anys on this line!">    start_time: float</span>
<span class="line-precise" title="No Anys on this line!">    end_time: Optional[float] = None</span>
<span class="line-precise" title="No Anys on this line!">    duration_ms: Optional[float] = None</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    tags: Dict[str, Any] = None</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    logs: List[Dict[str, Any]] = None</span>
<span class="line-precise" title="No Anys on this line!">    status: str = "ok"  # ok, error, timeout</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="No Anys on this line!">    def __post_init__(self):</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        if self.tags is None:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">            self.tags = {}</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        if self.logs is None:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">            self.logs = []</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">    def finish(self, status: str = "ok"):</span>
<span class="line-empty" title="No Anys on this line!">        """Finish the span and calculate duration."""</span>
<span class="line-precise" title="No Anys on this line!">        self.end_time = time.time()</span>
<span class="line-precise" title="No Anys on this line!">        self.duration_ms = (self.end_time - self.start_time) * 1000</span>
<span class="line-precise" title="No Anys on this line!">        self.status = status</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unannotated (x1)">    def add_tag(self, key: str, value: Any):</span>
<span class="line-empty" title="No Anys on this line!">        """Add a tag to the span."""</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">        self.tags[key] = value</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">    def add_log(self, message: str, **fields):</span>
<span class="line-empty" title="No Anys on this line!">        """Add a log entry to the span."""</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x1)">        log_entry = {</span>
<span class="line-precise" title="No Anys on this line!">            "timestamp": time.time(),</span>
<span class="line-precise" title="No Anys on this line!">            "message": message,</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x1)">            **fields</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)
Unannotated (x1)">        self.logs.append(log_entry)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    def to_dict(self) -&gt; Dict[str, Any]:</span>
<span class="line-empty" title="No Anys on this line!">        """Convert span to dictionary."""</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x3)
Omitted Generics (x2)">        return asdict(self)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class TraceContext:</span>
<span class="line-empty" title="No Anys on this line!">    """Context for managing distributed tracing."""</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __init__(self, trace_id: Optional[str] = None, span_id: Optional[str] = None):</span>
<span class="line-precise" title="No Anys on this line!">        self.trace_id = trace_id or str(uuid.uuid4())</span>
<span class="line-precise" title="No Anys on this line!">        self.span_id = span_id or str(uuid.uuid4())</span>
<span class="line-precise" title="No Anys on this line!">        self.spans: Dict[str, TraceSpan] = {}</span>
<span class="line-precise" title="No Anys on this line!">        self.active_span: Optional[TraceSpan] = None</span>
<span class="line-precise" title="No Anys on this line!">        self.baggage: Dict[str, str] = {}</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    def create_span(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        operation_name: str,</span>
<span class="line-precise" title="No Anys on this line!">        parent_span_id: Optional[str] = None,</span>
<span class="line-precise" title="No Anys on this line!">        tags: Optional[Dict[str, Any]] = None</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; TraceSpan:</span>
<span class="line-empty" title="No Anys on this line!">        """Create a new span."""</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">        span = TraceSpan(</span>
<span class="line-precise" title="No Anys on this line!">            span_id=str(uuid.uuid4()),</span>
<span class="line-precise" title="No Anys on this line!">            trace_id=self.trace_id,</span>
<span class="line-precise" title="No Anys on this line!">            parent_span_id=parent_span_id or (self.active_span.span_id if self.active_span else None),</span>
<span class="line-precise" title="No Anys on this line!">            operation_name=operation_name,</span>
<span class="line-precise" title="No Anys on this line!">            start_time=time.time(),</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            tags=tags or {}</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        self.spans[span.span_id] = span</span>
<span class="line-precise" title="No Anys on this line!">        return span</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">    def set_active_span(self, span: TraceSpan):</span>
<span class="line-empty" title="No Anys on this line!">        """Set the active span."""</span>
<span class="line-precise" title="No Anys on this line!">        self.active_span = span</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def get_active_span(self) -&gt; Optional[TraceSpan]:</span>
<span class="line-empty" title="No Anys on this line!">        """Get the currently active span."""</span>
<span class="line-precise" title="No Anys on this line!">        return self.active_span</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">    def add_baggage(self, key: str, value: str):</span>
<span class="line-empty" title="No Anys on this line!">        """Add baggage item (propagated across service boundaries)."""</span>
<span class="line-precise" title="No Anys on this line!">        self.baggage[key] = value</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def get_baggage(self, key: str) -&gt; Optional[str]:</span>
<span class="line-empty" title="No Anys on this line!">        """Get baggage item."""</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">        return self.baggage.get(key)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">    def to_dict(self) -&gt; Dict[str, Any]:</span>
<span class="line-empty" title="No Anys on this line!">        """Convert trace context to dictionary."""</span>
<span class="line-empty" title="No Anys on this line!">        return {</span>
<span class="line-precise" title="No Anys on this line!">            "trace_id": self.trace_id,</span>
<span class="line-precise" title="No Anys on this line!">            "span_id": self.span_id,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x2)">            "spans": {span_id: span.to_dict() for span_id, span in self.spans.items()},</span>
<span class="line-precise" title="No Anys on this line!">            "baggage": self.baggage,</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class TracingConfig:</span>
<span class="line-empty" title="No Anys on this line!">    """Configuration for tracing middleware."""</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __init__(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-precise" title="No Anys on this line!">        enable_tracing: bool = True,</span>
<span class="line-precise" title="No Anys on this line!">        enable_performance_monitoring: bool = True,</span>
<span class="line-precise" title="No Anys on this line!">        enable_distributed_tracing: bool = True,</span>
<span class="line-precise" title="No Anys on this line!">        sample_rate: float = 1.0,  # 0.0 to 1.0</span>
<span class="line-precise" title="No Anys on this line!">        max_spans_per_trace: int = 100,</span>
<span class="line-precise" title="No Anys on this line!">        span_export_batch_size: int = 50,</span>
<span class="line-precise" title="No Anys on this line!">        span_export_timeout: float = 30.0,</span>
<span class="line-precise" title="No Anys on this line!">        excluded_paths: Optional[Set[str]] = None,</span>
<span class="line-precise" title="No Anys on this line!">        trace_header_name: str = "X-Trace-ID",</span>
<span class="line-precise" title="No Anys on this line!">        span_header_name: str = "X-Span-ID",</span>
<span class="line-precise" title="No Anys on this line!">        baggage_header_name: str = "X-Baggage",</span>
<span class="line-precise" title="No Anys on this line!">        store_traces_in_redis: bool = True,</span>
<span class="line-precise" title="No Anys on this line!">        trace_retention_hours: int = 24,</span>
<span class="line-empty" title="No Anys on this line!">    ):</span>
<span class="line-precise" title="No Anys on this line!">        self.enable_tracing = enable_tracing</span>
<span class="line-precise" title="No Anys on this line!">        self.enable_performance_monitoring = enable_performance_monitoring</span>
<span class="line-precise" title="No Anys on this line!">        self.enable_distributed_tracing = enable_distributed_tracing</span>
<span class="line-precise" title="No Anys on this line!">        self.sample_rate = sample_rate</span>
<span class="line-precise" title="No Anys on this line!">        self.max_spans_per_trace = max_spans_per_trace</span>
<span class="line-precise" title="No Anys on this line!">        self.span_export_batch_size = span_export_batch_size</span>
<span class="line-precise" title="No Anys on this line!">        self.span_export_timeout = span_export_timeout</span>
<span class="line-precise" title="No Anys on this line!">        self.trace_header_name = trace_header_name</span>
<span class="line-precise" title="No Anys on this line!">        self.span_header_name = span_header_name</span>
<span class="line-precise" title="No Anys on this line!">        self.baggage_header_name = baggage_header_name</span>
<span class="line-precise" title="No Anys on this line!">        self.store_traces_in_redis = store_traces_in_redis</span>
<span class="line-precise" title="No Anys on this line!">        self.trace_retention_hours = trace_retention_hours</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        self.excluded_paths = excluded_paths or {</span>
<span class="line-precise" title="No Anys on this line!">            "/health", "/metrics", "/favicon.ico", "/static", "/docs", "/redoc"</span>
<span class="line-empty" title="No Anys on this line!">        }</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class SpanExporter:</span>
<span class="line-empty" title="No Anys on this line!">    """Exports spans to external tracing systems."""</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def __init__(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-precise" title="No Anys on this line!">        redis_service: Optional[RedisService] = None,</span>
<span class="line-precise" title="No Anys on this line!">        batch_size: int = 50,</span>
<span class="line-precise" title="No Anys on this line!">        timeout: float = 30.0,</span>
<span class="line-empty" title="No Anys on this line!">    ):</span>
<span class="line-precise" title="No Anys on this line!">        self.redis_service = redis_service</span>
<span class="line-precise" title="No Anys on this line!">        self.batch_size = batch_size</span>
<span class="line-precise" title="No Anys on this line!">        self.timeout = timeout</span>
<span class="line-precise" title="No Anys on this line!">        self.span_buffer: List[TraceSpan] = []</span>
<span class="line-precise" title="No Anys on this line!">        self._lock = asyncio.Lock()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x1)">    async def export_span(self, span: TraceSpan):</span>
<span class="line-empty" title="No Anys on this line!">        """Export a single span."""</span>
<span class="line-precise" title="No Anys on this line!">        async with self._lock:</span>
<span class="line-precise" title="No Anys on this line!">            self.span_buffer.append(span)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">            if len(self.span_buffer) &gt;= self.batch_size:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">                await self._flush_spans()</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x1)">    async def export_spans(self, spans: List[TraceSpan]):</span>
<span class="line-empty" title="No Anys on this line!">        """Export multiple spans."""</span>
<span class="line-precise" title="No Anys on this line!">        for span in spans:</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x2)">            await self.export_span(span)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="No Anys on this line!">    async def _flush_spans(self):</span>
<span class="line-empty" title="No Anys on this line!">        """Flush buffered spans to external systems."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        if not self.span_buffer:</span>
<span class="line-empty" title="No Anys on this line!">            return</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x4)">        spans_to_export = self.span_buffer.copy()</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">        self.span_buffer.clear()</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-empty" title="No Anys on this line!">            # Export to Redis for storage and analytics</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">            if self.redis_service:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">                await self._export_to_redis(spans_to_export)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Export to external tracing systems (placeholder)</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">            await self._export_to_external_systems(spans_to_export)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x4)
Unimported (x1)">            logger.error("Failed to export spans", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x1)">    async def _export_to_redis(self, spans: List[TraceSpan]):</span>
<span class="line-empty" title="No Anys on this line!">        """Export spans to Redis."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-empty" title="No Anys on this line!">            # Group spans by trace ID</span>
<span class="line-precise" title="No Anys on this line!">            traces = {}</span>
<span class="line-precise" title="No Anys on this line!">            for span in spans:</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x2)">                if span.trace_id not in traces:</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x2)">                    traces[span.trace_id] = []</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x5)
Explicit (x2)">                traces[span.trace_id].append(span.to_dict())</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Store each trace</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x8)">            for trace_id, trace_spans in traces.items():</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                key = f"trace:{trace_id}"</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Get existing trace data</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">                existing_trace = await self.redis_service.get(key, default={})</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Add new spans</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                if "spans" not in existing_trace:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                    existing_trace["spans"] = []</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x5)">                existing_trace["spans"].extend(trace_spans)</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">                existing_trace["updated_at"] = datetime.now(timezone.utc).isoformat()</span>
<span class="line-empty" title="No Anys on this line!">                </span>
<span class="line-empty" title="No Anys on this line!">                # Store with TTL</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">                await self.redis_service.set(key, existing_trace, ttl=24 * 3600)  # 24 hours</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            logger.debug(f"Exported {len(spans)} spans to Redis")</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            logger.error("Failed to export spans to Redis", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _export_to_external_systems(self, spans: List[TraceSpan]) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Export spans to external tracing systems (e.g., Jaeger, Zipkin)."""</span>
<span class="line-empty" title="No Anys on this line!">        # Placeholder for external system integration</span>
<span class="line-empty" title="No Anys on this line!">        # This could be extended to support:</span>
<span class="line-empty" title="No Anys on this line!">        # - Jaeger</span>
<span class="line-empty" title="No Anys on this line!">        # - Zipkin</span>
<span class="line-empty" title="No Anys on this line!">        # - OpenTelemetry Collector</span>
<span class="line-empty" title="No Anys on this line!">        # - DataDog APM</span>
<span class="line-empty" title="No Anys on this line!">        # - New Relic</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        if settings.PHOENIX_COLLECTOR_ENDPOINT:</span>
<span class="line-precise" title="No Anys on this line!">            await self._export_to_phoenix(spans)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _export_to_phoenix(self, spans: List[TraceSpan]) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Export spans to Phoenix tracing system."""</span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-any" title="No Anys on this line!">            import httpx</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Convert spans to Phoenix format</span>
<span class="line-precise" title="No Anys on this line!">            phoenix_spans = []</span>
<span class="line-precise" title="No Anys on this line!">            for span in spans:</span>
<span class="line-precise" title="No Anys on this line!">                phoenix_span = {</span>
<span class="line-precise" title="No Anys on this line!">                    "traceID": span.trace_id,</span>
<span class="line-precise" title="No Anys on this line!">                    "spanID": span.span_id,</span>
<span class="line-precise" title="No Anys on this line!">                    "parentSpanID": span.parent_span_id,</span>
<span class="line-precise" title="No Anys on this line!">                    "operationName": span.operation_name,</span>
<span class="line-precise" title="No Anys on this line!">                    "startTime": int(span.start_time * 1000000),  # microseconds</span>
<span class="line-precise" title="No Anys on this line!">                    "duration": int((span.duration_ms or 0) * 1000),  # microseconds</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    "tags": span.tags,</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">                    "logs": span.logs,</span>
<span class="line-precise" title="No Anys on this line!">                    "process": {</span>
<span class="line-precise" title="No Anys on this line!">                        "serviceName": settings.APP_NAME,</span>
<span class="line-precise" title="No Anys on this line!">                        "tags": {</span>
<span class="line-precise" title="No Anys on this line!">                            "version": settings.VERSION,</span>
<span class="line-precise" title="No Anys on this line!">                            "environment": settings.ENVIRONMENT,</span>
<span class="line-empty" title="No Anys on this line!">                        }</span>
<span class="line-empty" title="No Anys on this line!">                    }</span>
<span class="line-empty" title="No Anys on this line!">                }</span>
<span class="line-precise" title="No Anys on this line!">                phoenix_spans.append(phoenix_span)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Send to Phoenix collector</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x4)">            async with httpx.AsyncClient() as client:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x4)">                response = await client.post(</span>
<span class="line-precise" title="No Anys on this line!">                    f"{settings.PHOENIX_COLLECTOR_ENDPOINT}/api/traces",</span>
<span class="line-precise" title="No Anys on this line!">                    json={"batch": phoenix_spans},</span>
<span class="line-precise" title="No Anys on this line!">                    timeout=self.timeout</span>
<span class="line-empty" title="No Anys on this line!">                )</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                response.raise_for_status()</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            logger.debug(f"Exported {len(spans)} spans to Phoenix")</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">            logger.warning("Failed to export spans to Phoenix", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def flush(self) -&gt; None:</span>
<span class="line-empty" title="No Anys on this line!">        """Flush all buffered spans."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">        await self._flush_spans()</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">class RequestTracingMiddleware(BaseHTTPMiddleware):</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Middleware for distributed tracing and request correlation.</span>
<span class="line-empty" title="No Anys on this line!">    Provides comprehensive tracing capabilities with performance monitoring.</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">    def __init__(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        app,</span>
<span class="line-precise" title="No Anys on this line!">        config: Optional[TracingConfig] = None,</span>
<span class="line-precise" title="No Anys on this line!">        redis_service: Optional[RedisService] = None,</span>
<span class="line-empty" title="No Anys on this line!">    ):</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        super().__init__(app)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        self.config = config or TracingConfig()</span>
<span class="line-precise" title="No Anys on this line!">        self.redis_service = redis_service</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Initialize span exporter</span>
<span class="line-precise" title="No Anys on this line!">        self.span_exporter = SpanExporter(</span>
<span class="line-precise" title="No Anys on this line!">            redis_service=redis_service,</span>
<span class="line-precise" title="No Anys on this line!">            batch_size=self.config.span_export_batch_size,</span>
<span class="line-precise" title="No Anys on this line!">            timeout=self.config.span_export_timeout,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">        logger.info(</span>
<span class="line-precise" title="No Anys on this line!">            "Request tracing middleware initialized",</span>
<span class="line-precise" title="No Anys on this line!">            sample_rate=self.config.sample_rate,</span>
<span class="line-precise" title="No Anys on this line!">            distributed_tracing=self.config.enable_distributed_tracing,</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _is_path_excluded(self, path: str) -&gt; bool:</span>
<span class="line-empty" title="No Anys on this line!">        """Check if path is excluded from tracing."""</span>
<span class="line-precise" title="No Anys on this line!">        return any(path.startswith(excluded) for excluded in self.config.excluded_paths)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _should_sample(self) -&gt; bool:</span>
<span class="line-empty" title="No Anys on this line!">        """Determine if request should be sampled for tracing."""</span>
<span class="line-precise" title="No Anys on this line!">        import random</span>
<span class="line-precise" title="No Anys on this line!">        return random.random() &lt; self.config.sample_rate</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    def _extract_trace_context(self, request: Request) -&gt; TraceContext:</span>
<span class="line-empty" title="No Anys on this line!">        """Extract trace context from request headers."""</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">        trace_id = request.headers.get(self.config.trace_header_name)</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">        span_id = request.headers.get(self.config.span_header_name)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        context = TraceContext(trace_id=trace_id, span_id=span_id)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Extract baggage</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">        baggage_header = request.headers.get(self.config.baggage_header_name)</span>
<span class="line-precise" title="No Anys on this line!">        if baggage_header:</span>
<span class="line-empty" title="No Anys on this line!">            try:</span>
<span class="line-precise" title="No Anys on this line!">                baggage_items = baggage_header.split(",")</span>
<span class="line-precise" title="No Anys on this line!">                for item in baggage_items:</span>
<span class="line-precise" title="No Anys on this line!">                    if "=" in item:</span>
<span class="line-precise" title="No Anys on this line!">                        key, value = item.split("=", 1)</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">                        context.add_baggage(key.strip(), value.strip())</span>
<span class="line-precise" title="No Anys on this line!">            except Exception as e:</span>
<span class="line-any" title="Any Types on this line: 
Unimported (x3)">                logger.warning("Failed to parse baggage header", error=str(e))</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        return context</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">    def _inject_trace_headers(self, response: Response, context: TraceContext):</span>
<span class="line-empty" title="No Anys on this line!">        """Inject trace headers into response."""</span>
<span class="line-precise" title="No Anys on this line!">        response.headers[self.config.trace_header_name] = context.trace_id</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-precise" title="No Anys on this line!">        if context.active_span:</span>
<span class="line-precise" title="No Anys on this line!">            response.headers[self.config.span_header_name] = context.active_span.span_id</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Inject baggage</span>
<span class="line-precise" title="No Anys on this line!">        if context.baggage:</span>
<span class="line-precise" title="No Anys on this line!">            baggage_items = [f"{k}={v}" for k, v in context.baggage.items()]</span>
<span class="line-precise" title="No Anys on this line!">            response.headers[self.config.baggage_header_name] = ",".join(baggage_items)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    async def _create_request_span(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        context: TraceContext,</span>
<span class="line-empty" title="No Anys on this line!">        request: Request</span>
<span class="line-empty" title="No Anys on this line!">    ) -&gt; TraceSpan:</span>
<span class="line-empty" title="No Anys on this line!">        """Create a span for the incoming request."""</span>
<span class="line-imprecise" title="Any Types on this line: 
Explicit (x1)">        span = context.create_span(</span>
<span class="line-precise" title="No Anys on this line!">            operation_name=f"{request.method} {request.url.path}",</span>
<span class="line-empty" title="No Anys on this line!">            tags={</span>
<span class="line-precise" title="No Anys on this line!">                "http.method": request.method,</span>
<span class="line-precise" title="No Anys on this line!">                "http.url": str(request.url),</span>
<span class="line-precise" title="No Anys on this line!">                "http.path": request.url.path,</span>
<span class="line-precise" title="No Anys on this line!">                "http.query": str(request.url.query) if request.url.query else None,</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x2)">                "http.user_agent": request.headers.get("user-agent"),</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x13)
Omitted Generics (x2)">                "http.remote_addr": getattr(request.client, "host", None) if request.client else None,</span>
<span class="line-precise" title="No Anys on this line!">                "component": "http",</span>
<span class="line-precise" title="No Anys on this line!">                "span.kind": "server",</span>
<span class="line-empty" title="No Anys on this line!">            }</span>
<span class="line-empty" title="No Anys on this line!">        )</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">        context.set_active_span(span)</span>
<span class="line-precise" title="No Anys on this line!">        return span</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x1)">    async def _finish_request_span(</span>
<span class="line-empty" title="No Anys on this line!">        self,</span>
<span class="line-empty" title="No Anys on this line!">        span: TraceSpan,</span>
<span class="line-empty" title="No Anys on this line!">        response: Response,</span>
<span class="line-precise" title="No Anys on this line!">        error: Optional[Exception] = None</span>
<span class="line-empty" title="No Anys on this line!">    ):</span>
<span class="line-empty" title="No Anys on this line!">        """Finish the request span with response information."""</span>
<span class="line-empty" title="No Anys on this line!">        # Add response tags</span>
<span class="line-precise" title="No Anys on this line!">        if response:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)
Explicit (x1)">            span.add_tag("http.status_code", response.status_code)</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)
Explicit (x14)
Omitted Generics (x2)">            span.add_tag("http.response_size", len(getattr(response, "body", b"")))</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Handle errors</span>
<span class="line-precise" title="No Anys on this line!">        if error:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)
Explicit (x1)">            span.add_tag("error", True)</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)
Explicit (x3)">            span.add_tag("error.type", type(error).__name__)</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)
Explicit (x1)">            span.add_tag("error.message", str(error))</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)
Explicit (x2)">            span.add_log("error", error=str(error), error_type=type(error).__name__)</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">            span.finish("error")</span>
<span class="line-empty" title="No Anys on this line!">        else:</span>
<span class="line-empty" title="No Anys on this line!">            # Determine status based on response code</span>
<span class="line-precise" title="No Anys on this line!">            if response and response.status_code &gt;= 400:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">                span.finish("error")</span>
<span class="line-empty" title="No Anys on this line!">            else:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">                span.finish("ok")</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Export span</span>
<span class="line-precise" title="No Anys on this line!">        if self.config.enable_tracing:</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x2)">            await self.span_exporter.export_span(span)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="Any Types on this line: 
Error (x3)">    async def dispatch(self, request: Request, call_next: Callable) -&gt; Response:</span>
<span class="line-empty" title="No Anys on this line!">        """Process tracing middleware."""</span>
<span class="line-empty" title="No Anys on this line!">        # Skip tracing for excluded paths</span>
<span class="line-precise" title="No Anys on this line!">        if self._is_path_excluded(request.url.path):</span>
<span class="line-any" title="Any Types on this line: 
Error (x4)">            return await call_next(request)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Check sampling</span>
<span class="line-precise" title="No Anys on this line!">        if not self._should_sample():</span>
<span class="line-any" title="Any Types on this line: 
Error (x4)">            return await call_next(request)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Extract or create trace context</span>
<span class="line-precise" title="No Anys on this line!">        context = self._extract_trace_context(request)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Store context in request state</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">        request.state.trace_context = context</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">        request.state.trace_id = context.trace_id</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        # Create request span</span>
<span class="line-precise" title="No Anys on this line!">        request_span = await self._create_request_span(context, request)</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">        try:</span>
<span class="line-empty" title="No Anys on this line!">            # Add custom baggage</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x14)
Omitted Generics (x2)">            user_id = getattr(request.state, "user_id", None)</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">            if user_id:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)
Explicit (x1)">                context.add_baggage("user.id", str(user_id))</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Process request</span>
<span class="line-any" title="Any Types on this line: 
Error (x5)">            response = await call_next(request)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Finish span</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)
Error (x1)">            await self._finish_request_span(request_span, response)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Inject trace headers</span>
<span class="line-precise" title="No Anys on this line!">            if self.config.enable_distributed_tracing:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)
Error (x1)">                self._inject_trace_headers(response, context)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-any" title="Any Types on this line: 
Error (x1)">            return response</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-precise" title="No Anys on this line!">        except Exception as error:</span>
<span class="line-empty" title="No Anys on this line!">            # Finish span with error</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x2)">            await self._finish_request_span(request_span, None, error)</span>
<span class="line-empty" title="No Anys on this line!">            </span>
<span class="line-empty" title="No Anys on this line!">            # Re-raise the error</span>
<span class="line-empty" title="No Anys on this line!">            raise</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-any" title="No Anys on this line!">    async def shutdown(self):</span>
<span class="line-empty" title="No Anys on this line!">        """Shutdown the tracing middleware."""</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">        if self.span_exporter:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x3)">            await self.span_exporter.flush()</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"># Context managers and utilities for custom tracing</span>
<span class="line-precise" title="Any Types on this line: 
Omitted Generics (x6)">@asynccontextmanager</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">async def trace_operation(</span>
<span class="line-empty" title="No Anys on this line!">    operation_name: str,</span>
<span class="line-precise" title="No Anys on this line!">    request: Optional[Request] = None,</span>
<span class="line-empty" title="No Anys on this line!">    **tags</span>
<span class="line-empty" title="No Anys on this line!">):</span>
<span class="line-empty" title="No Anys on this line!">    """Context manager for tracing operations."""</span>
<span class="line-empty" title="No Anys on this line!">    # Get trace context from request if available</span>
<span class="line-precise" title="No Anys on this line!">    if request and hasattr(request.state, "trace_context"):</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x2)">        context = request.state.trace_context</span>
<span class="line-empty" title="No Anys on this line!">    else:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">        context = TraceContext()</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    # Create span</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)
Unannotated (x1)">    span = context.create_span(operation_name, tags=tags)</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)">    original_active = context.get_active_span()</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)">    context.set_active_span(span)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    try:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)">        yield span</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">        span.finish("ok")</span>
<span class="line-precise" title="No Anys on this line!">    except Exception as error:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">        span.add_tag("error", True)</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">        span.add_tag("error.message", str(error))</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x3)">        span.finish("error")</span>
<span class="line-empty" title="No Anys on this line!">        raise</span>
<span class="line-empty" title="No Anys on this line!">    finally:</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x4)">        context.set_active_span(original_active)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">def get_current_trace_context(request: Request) -&gt; Optional[TraceContext]:</span>
<span class="line-empty" title="No Anys on this line!">    """Get the current trace context from request."""</span>
<span class="line-any" title="Any Types on this line: 
Explicit (x13)
Omitted Generics (x2)">    return getattr(request.state, "trace_context", None)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-precise" title="No Anys on this line!">def get_current_span(request: Request) -&gt; Optional[TraceSpan]:</span>
<span class="line-empty" title="No Anys on this line!">    """Get the current active span from request."""</span>
<span class="line-precise" title="No Anys on this line!">    context = get_current_trace_context(request)</span>
<span class="line-precise" title="No Anys on this line!">    return context.get_active_span() if context else None</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="Any Types on this line: 
Explicit (x1)
Unannotated (x1)">def add_span_tag(request: Request, key: str, value: Any):</span>
<span class="line-empty" title="No Anys on this line!">    """Add a tag to the current span."""</span>
<span class="line-precise" title="No Anys on this line!">    span = get_current_span(request)</span>
<span class="line-precise" title="No Anys on this line!">    if span:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)
Explicit (x2)">        span.add_tag(key, value)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">def add_span_log(request: Request, message: str, **fields):</span>
<span class="line-empty" title="No Anys on this line!">    """Add a log entry to the current span."""</span>
<span class="line-precise" title="No Anys on this line!">    span = get_current_span(request)</span>
<span class="line-precise" title="No Anys on this line!">    if span:</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x4)">        span.add_log(message, **fields)</span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"></span>
<span class="line-empty" title="No Anys on this line!"># Factory function</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x1)">def create_tracing_middleware(</span>
<span class="line-precise" title="No Anys on this line!">    config: Optional[TracingConfig] = None,</span>
<span class="line-precise" title="No Anys on this line!">    redis_service: Optional[RedisService] = None,</span>
<span class="line-empty" title="No Anys on this line!">    **kwargs</span>
<span class="line-empty" title="No Anys on this line!">) -&gt; type:</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-empty" title="No Anys on this line!">    Factory function to create tracing middleware with configuration.</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-empty" title="No Anys on this line!">    Args:</span>
<span class="line-empty" title="No Anys on this line!">        config: Tracing configuration</span>
<span class="line-empty" title="No Anys on this line!">        redis_service: Redis service instance</span>
<span class="line-empty" title="No Anys on this line!">        **kwargs: Additional configuration options</span>
<span class="line-empty" title="No Anys on this line!">        </span>
<span class="line-empty" title="No Anys on this line!">    Returns:</span>
<span class="line-empty" title="No Anys on this line!">        Configured tracing middleware class</span>
<span class="line-empty" title="No Anys on this line!">    """</span>
<span class="line-precise" title="No Anys on this line!">    if config is None:</span>
<span class="line-imprecise" title="Any Types on this line: 
Unannotated (x1)">        config = TracingConfig(**kwargs)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="No Anys on this line!">    class ConfiguredTracingMiddleware(RequestTracingMiddleware):</span>
<span class="line-any" title="No Anys on this line!">        def __init__(self, app):</span>
<span class="line-any" title="Any Types on this line: 
Unannotated (x2)">            super().__init__(app, config=config, redis_service=redis_service)</span>
<span class="line-empty" title="No Anys on this line!">    </span>
<span class="line-precise" title="Any Types on this line: 
Unannotated (x1)">    return ConfiguredTracingMiddleware</span>
</pre></td>
</tr></tbody>
</table>
</body>
</html>
