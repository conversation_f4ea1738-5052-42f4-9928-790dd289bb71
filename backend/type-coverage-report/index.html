<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" type="text/css" href="mypy-html.css">
</head>
<body>
<h1>Mypy Type Check Coverage Summary</h1>
<table class="summary">
<caption>Summary from index</caption>
<thead><tr class="summary">
<th class="summary">File</th>
<th class="summary">Imprecision</th>
<th class="summary">Lines</th>
</tr></thead>
<tfoot><tr class="summary summary-quality-1">
<th class="summary summary-filename">Total</th>
<th class="summary summary-precision">18.52% imprecise</th>
<th class="summary summary-lines">35275 LOC</th>
</tr></tfoot>
<tbody>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/__init__.py.html">agents</a></td>
<td class="summary summary-precision">4.76% imprecise</td>
<td class="summary summary-lines">42 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/base.py.html">agents.base</a></td>
<td class="summary summary-precision">13.11% imprecise</td>
<td class="summary summary-lines">595 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/communication.py.html">agents.communication</a></td>
<td class="summary summary-precision">14.83% imprecise</td>
<td class="summary summary-lines">843 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/__init__.py.html">agents.core</a></td>
<td class="summary summary-precision">12.50% imprecise</td>
<td class="summary summary-lines">16 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/ad_asset_generation.py.html">agents.core.ad_asset_generation</a></td>
<td class="summary summary-precision">17.83% imprecise</td>
<td class="summary summary-lines">1223 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/audience_targeting.py.html">agents.core.audience_targeting</a></td>
<td class="summary summary-precision">18.45% imprecise</td>
<td class="summary summary-lines">1100 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/agents/core/bid_optimization.py.html">agents.core.bid_optimization</a></td>
<td class="summary summary-precision">20.35% imprecise</td>
<td class="summary summary-lines">1258 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/budget_management.py.html">agents.core.budget_management</a></td>
<td class="summary summary-precision">15.96% imprecise</td>
<td class="summary summary-lines">1109 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/campaign_planning.py.html">agents.core.campaign_planning</a></td>
<td class="summary summary-precision">18.42% imprecise</td>
<td class="summary summary-lines">1026 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/crewai_implementation_specialist.py.html">agents.core.crewai_implementation_specialist</a></td>
<td class="summary summary-precision">14.67% imprecise</td>
<td class="summary summary-lines">75 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/frontend_ux_expert.py.html">agents.core.frontend_ux_expert</a></td>
<td class="summary summary-precision">13.64% imprecise</td>
<td class="summary summary-lines">66 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/infra_deployment_specialist.py.html">agents.core.infra_deployment_specialist</a></td>
<td class="summary summary-precision">13.51% imprecise</td>
<td class="summary summary-lines">74 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/keyword_research.py.html">agents.core.keyword_research</a></td>
<td class="summary summary-precision">14.45% imprecise</td>
<td class="summary summary-lines">1460 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/middleware_validation_expert.py.html">agents.core.middleware_validation_expert</a></td>
<td class="summary summary-precision">13.51% imprecise</td>
<td class="summary summary-lines">74 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/performance_analysis.py.html">agents.core.performance_analysis</a></td>
<td class="summary summary-precision">13.39% imprecise</td>
<td class="summary summary-lines">1688 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/project_orchestrator.py.html">agents.core.project_orchestrator</a></td>
<td class="summary summary-precision">17.60% imprecise</td>
<td class="summary summary-lines">875 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/quality_assurance.py.html">agents.core.quality_assurance</a></td>
<td class="summary summary-precision">13.48% imprecise</td>
<td class="summary summary-lines">1068 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/security_reviewer.py.html">agents.core.security_reviewer</a></td>
<td class="summary summary-precision">17.31% imprecise</td>
<td class="summary summary-lines">809 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/core/software_engineer.py.html">agents.core.software_engineer</a></td>
<td class="summary summary-precision">18.35% imprecise</td>
<td class="summary summary-lines">1068 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/factory.py.html">agents.factory</a></td>
<td class="summary summary-precision">9.90% imprecise</td>
<td class="summary summary-lines">717 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/google_ads_config.py.html">agents.google_ads_config</a></td>
<td class="summary summary-precision">3.11% imprecise</td>
<td class="summary summary-lines">514 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/orchestration.py.html">agents.orchestration</a></td>
<td class="summary summary-precision">14.38% imprecise</td>
<td class="summary summary-lines">1718 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/agents/orchestration_example.py.html">agents.orchestration_example</a></td>
<td class="summary summary-precision">49.12% imprecise</td>
<td class="summary summary-lines">340 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/agents/tracing.py.html">agents.tracing</a></td>
<td class="summary summary-precision">27.13% imprecise</td>
<td class="summary summary-lines">634 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/agents/workflows.py.html">agents.workflows</a></td>
<td class="summary summary-precision">6.13% imprecise</td>
<td class="summary summary-lines">652 LOC</td>
</tr>
<tr class="summary summary-quality-0">
<td class="summary summary-filename"><a href="html/api/__init__.py.html">api</a></td>
<td class="summary summary-precision">0.00% imprecise</td>
<td class="summary summary-lines">4 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/api/agents.py.html">api.agents</a></td>
<td class="summary summary-precision">10.45% imprecise</td>
<td class="summary summary-lines">555 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/api/analytics.py.html">api.analytics</a></td>
<td class="summary summary-precision">10.11% imprecise</td>
<td class="summary summary-lines">534 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/api/campaigns.py.html">api.campaigns</a></td>
<td class="summary summary-precision">28.16% imprecise</td>
<td class="summary summary-lines">664 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/api/health.py.html">api.health</a></td>
<td class="summary summary-precision">15.96% imprecise</td>
<td class="summary summary-lines">376 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/main.py.html">main</a></td>
<td class="summary summary-precision">16.67% imprecise</td>
<td class="summary summary-lines">228 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/middleware/__init__.py.html">middleware</a></td>
<td class="summary summary-precision">16.67% imprecise</td>
<td class="summary summary-lines">24 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/middleware/auth.py.html">middleware.auth</a></td>
<td class="summary summary-precision">21.50% imprecise</td>
<td class="summary summary-lines">693 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/middleware/integration.py.html">middleware.integration</a></td>
<td class="summary summary-precision">10.85% imprecise</td>
<td class="summary summary-lines">507 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/middleware/logging.py.html">middleware.logging</a></td>
<td class="summary summary-precision">22.76% imprecise</td>
<td class="summary summary-lines">659 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/middleware/metrics.py.html">middleware.metrics</a></td>
<td class="summary summary-precision">18.64% imprecise</td>
<td class="summary summary-lines">601 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/middleware/security.py.html">middleware.security</a></td>
<td class="summary summary-precision">17.40% imprecise</td>
<td class="summary summary-lines">638 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/middleware/tracing.py.html">middleware.tracing</a></td>
<td class="summary summary-precision">21.22% imprecise</td>
<td class="summary summary-lines">556 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/middleware/transformation.py.html">middleware.transformation</a></td>
<td class="summary summary-precision">20.39% imprecise</td>
<td class="summary summary-lines">569 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/middleware/validation.py.html">middleware.validation</a></td>
<td class="summary summary-precision">12.76% imprecise</td>
<td class="summary summary-lines">572 LOC</td>
</tr>
<tr class="summary summary-quality-0">
<td class="summary summary-filename"><a href="html/models/__init__.py.html">models</a></td>
<td class="summary summary-precision">0.00% imprecise</td>
<td class="summary summary-lines">4 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/models/agents.py.html">models.agents</a></td>
<td class="summary summary-precision">29.97% imprecise</td>
<td class="summary summary-lines">367 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/models/analytics.py.html">models.analytics</a></td>
<td class="summary summary-precision">29.07% imprecise</td>
<td class="summary summary-lines">399 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/models/campaigns.py.html">models.campaigns</a></td>
<td class="summary summary-precision">33.23% imprecise</td>
<td class="summary summary-lines">316 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/models/common.py.html">models.common</a></td>
<td class="summary summary-precision">26.37% imprecise</td>
<td class="summary summary-lines">273 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/models/validation_schemas.py.html">models.validation_schemas</a></td>
<td class="summary summary-precision">24.44% imprecise</td>
<td class="summary summary-lines">1461 LOC</td>
</tr>
<tr class="summary summary-quality-0">
<td class="summary summary-filename"><a href="html/services/__init__.py.html">services</a></td>
<td class="summary summary-precision">0.00% imprecise</td>
<td class="summary summary-lines">24 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/services/agent_service.py.html">services.agent_service</a></td>
<td class="summary summary-precision">23.01% imprecise</td>
<td class="summary summary-lines">1004 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/services/base.py.html">services.base</a></td>
<td class="summary summary-precision">12.63% imprecise</td>
<td class="summary summary-lines">467 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/services/database.py.html">services.database</a></td>
<td class="summary summary-precision">21.23% imprecise</td>
<td class="summary summary-lines">928 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/services/google_ads.py.html">services.google_ads</a></td>
<td class="summary summary-precision">29.65% imprecise</td>
<td class="summary summary-lines">543 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/services/openai_service.py.html">services.openai_service</a></td>
<td class="summary summary-precision">16.82% imprecise</td>
<td class="summary summary-lines">541 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/services/redis_service.py.html">services.redis_service</a></td>
<td class="summary summary-precision">20.88% imprecise</td>
<td class="summary summary-lines">982 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/setup_database.py.html">setup_database</a></td>
<td class="summary summary-precision">62.38% imprecise</td>
<td class="summary summary-lines">202 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/test_setup.py.html">test_setup</a></td>
<td class="summary summary-precision">58.85% imprecise</td>
<td class="summary summary-lines">192 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/test_standalone.py.html">test_standalone</a></td>
<td class="summary summary-precision">37.04% imprecise</td>
<td class="summary summary-lines">54 LOC</td>
</tr>
<tr class="summary summary-quality-0">
<td class="summary summary-filename"><a href="html/utils/__init__.py.html">utils</a></td>
<td class="summary summary-precision">0.00% imprecise</td>
<td class="summary summary-lines">4 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/utils/config.py.html">utils.config</a></td>
<td class="summary summary-precision">14.75% imprecise</td>
<td class="summary summary-lines">305 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/utils/exceptions.py.html">utils.exceptions</a></td>
<td class="summary summary-precision">21.69% imprecise</td>
<td class="summary summary-lines">355 LOC</td>
</tr>
<tr class="summary summary-quality-1">
<td class="summary summary-filename"><a href="html/utils/helpers.py.html">utils.helpers</a></td>
<td class="summary summary-precision">8.98% imprecise</td>
<td class="summary summary-lines">401 LOC</td>
</tr>
<tr class="summary summary-quality-2">
<td class="summary summary-filename"><a href="html/utils/logging.py.html">utils.logging</a></td>
<td class="summary summary-precision">22.71% imprecise</td>
<td class="summary summary-lines">229 LOC</td>
</tr>
</tbody>
</table>
</body>
</html>
