"""
Common Pydantic models and base classes.
Shared structures used across multiple modules.
"""

from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, TypeVar
from enum import Enum

from pydantic import BaseModel, Field


# Generic type for response data
DataType = TypeVar("DataType")


class BaseResponse(BaseModel, Generic[DataType]):
    """
    Base response model for all API responses.
    """
    success: bool = Field(..., description="Indicates if the request was successful")
    message: str = Field(..., description="Human-readable message describing the result")
    data: Optional[DataType] = Field(None, description="Response data payload")
    errors: Optional[List[str]] = Field(None, description="List of error messages if any")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


class PaginationInfo(BaseModel):
    """
    Pagination information for list responses.
    """
    skip: int = Field(..., ge=0, description="Number of records skipped")
    limit: int = Field(..., ge=1, description="Maximum number of records returned")
    total: int = Field(..., ge=0, description="Total number of records available")
    has_more: bool = Field(..., description="Indicates if more records are available")


class PaginatedResponse(BaseModel, Generic[DataType]):
    """
    Paginated response model for list endpoints.
    """
    success: bool = Field(..., description="Indicates if the request was successful")
    message: str = Field(..., description="Human-readable message describing the result")
    data: List[DataType] = Field(..., description="List of response data items")
    pagination: PaginationInfo = Field(..., description="Pagination information")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


class Status(str, Enum):
    """
    Common status enumeration.
    """
    ACTIVE = "active"
    INACTIVE = "inactive"
    PAUSED = "paused"
    DRAFT = "draft"
    ARCHIVED = "archived"
    DELETED = "deleted"


class Priority(str, Enum):
    """
    Priority levels enumeration.
    """
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class Currency(str, Enum):
    """
    Supported currencies.
    """
    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    CAD = "CAD"


class Language(str, Enum):
    """
    Supported languages for campaigns.
    """
    ENGLISH = "en"
    DUTCH = "nl"
    FRENCH = "fr"
    GERMAN = "de"
    SPANISH = "es"
    ITALIAN = "it"


class Country(str, Enum):
    """
    Supported countries/regions.
    """
    UNITED_STATES = "US"
    BELGIUM = "BE"
    NETHERLANDS = "NL"
    FRANCE = "FR"
    GERMANY = "DE"
    UNITED_KINGDOM = "GB"
    CANADA = "CA"


class BaseEntity(BaseModel):
    """
    Base entity model with common fields.
    """
    id: str = Field(..., description="Unique identifier")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    created_by: Optional[str] = Field(None, description="Creator user ID")
    updated_by: Optional[str] = Field(None, description="Last updater user ID")


class MoneyAmount(BaseModel):
    """
    Model for representing monetary amounts.
    """
    amount: float = Field(..., ge=0, description="Amount value")
    currency: Currency = Field(Currency.USD, description="Currency code")
    
    def __str__(self) -> str:
        """String representation of the money amount."""
        if self.currency == Currency.USD:
            return f"${self.amount:,.2f}"
        elif self.currency == Currency.EUR:
            return f"€{self.amount:,.2f}"
        elif self.currency == Currency.GBP:
            return f"£{self.amount:,.2f}"
        else:
            return f"{self.amount:,.2f} {self.currency}"


class Location(BaseModel):
    """
    Geographic location model.
    """
    country: Country = Field(..., description="Country code")
    region: Optional[str] = Field(None, description="Region or state")
    city: Optional[str] = Field(None, description="City name")
    postal_code: Optional[str] = Field(None, description="Postal/ZIP code")
    
    def __str__(self) -> str:
        """String representation of the location."""
        parts = [self.city, self.region, self.country.value]
        return ", ".join(filter(None, parts))


class TimeRange(BaseModel):
    """
    Time range model for analytics and reporting.
    """
    start_date: datetime = Field(..., description="Start date and time")
    end_date: datetime = Field(..., description="End date and time")
    timezone: str = Field("UTC", description="Timezone for the dates")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class MetricValue(BaseModel):
    """
    Model for representing metric values with metadata.
    """
    value: float = Field(..., description="Metric value")
    previous_value: Optional[float] = Field(None, description="Previous period value for comparison")
    change_percentage: Optional[float] = Field(None, description="Percentage change from previous period")
    trend: Optional[str] = Field(None, description="Trend direction (up, down, stable)")
    confidence: Optional[float] = Field(None, ge=0, le=1, description="Confidence score for the metric")


class Tag(BaseModel):
    """
    Tag model for categorizing entities.
    """
    key: str = Field(..., min_length=1, max_length=50, description="Tag key")
    value: str = Field(..., min_length=1, max_length=100, description="Tag value")
    
    def __str__(self) -> str:
        """String representation of the tag."""
        return f"{self.key}:{self.value}"


class Address(BaseModel):
    """
    Physical address model.
    """
    street_address: str = Field(..., description="Street address")
    city: str = Field(..., description="City name")
    state_province: Optional[str] = Field(None, description="State or province")
    postal_code: str = Field(..., description="Postal or ZIP code")
    country: Country = Field(..., description="Country code")
    
    def __str__(self) -> str:
        """String representation of the address."""
        parts = [
            self.street_address,
            self.city,
            self.state_province,
            self.postal_code,
            self.country.value
        ]
        return ", ".join(filter(None, parts))


class ContactInfo(BaseModel):
    """
    Contact information model.
    """
    email: Optional[str] = Field(None, description="Email address")
    phone: Optional[str] = Field(None, description="Phone number")
    website: Optional[str] = Field(None, description="Website URL")
    address: Optional[Address] = Field(None, description="Physical address")


class Percentage(BaseModel):
    """
    Model for representing percentage values.
    """
    value: float = Field(..., ge=0, le=100, description="Percentage value (0-100)")
    
    def __str__(self) -> str:
        """String representation of the percentage."""
        return f"{self.value:.2f}%"
    
    @property
    def decimal(self) -> float:
        """Get the decimal representation (0-1)."""
        return self.value / 100


class ErrorDetail(BaseModel):
    """
    Detailed error information.
    """
    code: str = Field(..., description="Error code")
    message: str = Field(..., description="Error message")
    field: Optional[str] = Field(None, description="Field that caused the error")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional error context")


class ValidationErrorResponse(BaseModel):
    """
    Validation error response model.
    """
    success: bool = Field(False, description="Always false for error responses")
    message: str = Field(..., description="Error summary message")
    errors: List[ErrorDetail] = Field(..., description="Detailed error information")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")


class HealthStatus(BaseModel):
    """
    Health check status model.
    """
    status: str = Field(..., description="Overall health status")
    checks: Dict[str, Dict[str, Any]] = Field(..., description="Individual service check results")
    timestamp: datetime = Field(..., description="Health check timestamp")
    uptime_seconds: float = Field(..., description="Application uptime in seconds")


class RateLimitInfo(BaseModel):
    """
    Rate limiting information.
    """
    limit: int = Field(..., description="Request limit per time window")
    remaining: int = Field(..., description="Remaining requests in current window")
    reset_time: datetime = Field(..., description="Time when the limit resets")
    window_seconds: int = Field(..., description="Time window in seconds")