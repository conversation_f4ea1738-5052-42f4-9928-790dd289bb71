"""
Analytics and reporting Pydantic models.
Data structures for performance metrics, insights, and reporting.
"""

from datetime import datetime, date
from typing import Any, Dict, List, Optional, Union
from enum import Enum

from pydantic import BaseModel, Field, validator

from .common import BaseEntity, BaseResponse, Currency, MetricValue


class MetricType(str, Enum):
    """
    Available metric types for analytics.
    """
    IMPRESSIONS = "impressions"
    CLICKS = "clicks"
    CONVERSIONS = "conversions"
    COST = "cost"
    REVENUE = "revenue"
    CTR = "ctr"
    CPC = "cpc"
    CPM = "cpm"
    CONVERSION_RATE = "conversion_rate"
    COST_PER_CONVERSION = "cost_per_conversion"
    ROAS = "roas"
    ROI = "roi"
    QUALITY_SCORE = "quality_score"
    IMPRESSION_SHARE = "impression_share"
    SEARCH_VOLUME = "search_volume"


class TimeRange(str, Enum):
    """
    Predefined time ranges for analytics.
    """
    TODAY = "today"
    YESTERDAY = "yesterday"
    LAST_7_DAYS = "last_7_days"
    LAST_14_DAYS = "last_14_days"
    LAST_30_DAYS = "last_30_days"
    LAST_90_DAYS = "last_90_days"
    THIS_MONTH = "this_month"
    LAST_MONTH = "last_month"
    THIS_QUARTER = "this_quarter"
    LAST_QUARTER = "last_quarter"
    THIS_YEAR = "this_year"
    LAST_YEAR = "last_year"
    CUSTOM = "custom"


class Granularity(str, Enum):
    """
    Data granularity levels.
    """
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


class ReportFormat(str, Enum):
    """
    Report export formats.
    """
    JSON = "json"
    CSV = "csv"
    XLSX = "xlsx"
    PDF = "pdf"


class InsightType(str, Enum):
    """
    Types of performance insights.
    """
    PERFORMANCE_ANOMALY = "performance_anomaly"
    OPTIMIZATION_OPPORTUNITY = "optimization_opportunity"
    TREND_ANALYSIS = "trend_analysis"
    BUDGET_RECOMMENDATION = "budget_recommendation"
    AUDIENCE_INSIGHT = "audience_insight"
    COMPETITIVE_ANALYSIS = "competitive_analysis"
    SEASONAL_PATTERN = "seasonal_pattern"
    QUALITY_ISSUE = "quality_issue"


class ImpactLevel(str, Enum):
    """
    Impact levels for insights and recommendations.
    """
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class DataPoint(BaseModel):
    """
    Single data point in a time series.
    """
    timestamp: datetime = Field(..., description="Data point timestamp")
    value: float = Field(..., description="Metric value")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class TimeSeries(BaseModel):
    """
    Time series data for a metric.
    """
    metric: MetricType = Field(..., description="Metric type")
    data_points: List[DataPoint] = Field(..., description="Time series data points")
    total: Optional[float] = Field(None, description="Total value across all data points")
    average: Optional[float] = Field(None, description="Average value")
    trend: Optional[str] = Field(None, description="Trend direction (up, down, stable)")


class CampaignMetrics(BaseModel):
    """
    Comprehensive campaign performance metrics.
    """
    campaign_id: str = Field(..., description="Campaign ID")
    date_range: Dict[str, date] = Field(..., description="Metrics date range")
    
    # Core performance metrics
    performance: Dict[str, Union[int, float]] = Field(..., description="Raw performance metrics")
    
    # Derived metrics
    derived_metrics: Dict[str, float] = Field(..., description="Calculated derived metrics")
    
    # Trend data
    trends: Dict[str, float] = Field(default={}, description="Period-over-period changes")
    
    # Benchmarks
    benchmarks: Optional[Dict[str, float]] = Field(None, description="Industry/account benchmarks")


class PerformanceInsight(BaseModel):
    """
    AI-generated performance insight.
    """
    id: Optional[str] = Field(None, description="Insight ID")
    type: InsightType = Field(..., description="Insight type")
    title: str = Field(..., description="Insight title")
    description: str = Field(..., description="Detailed description")
    confidence: float = Field(..., ge=0, le=1, description="Confidence score")
    impact: ImpactLevel = Field(..., description="Impact level")
    recommendation: str = Field(..., description="Recommended action")
    
    # Supporting data
    affected_entities: List[str] = Field(default=[], description="Affected campaigns/ad groups/keywords")
    metrics_impact: Dict[str, float] = Field(default={}, description="Expected metrics impact")
    timeframe: Optional[str] = Field(None, description="Relevant timeframe")
    
    # Metadata
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Generation timestamp")
    expires_at: Optional[datetime] = Field(None, description="Insight expiration time")
    priority: int = Field(1, ge=1, le=5, description="Priority level (1-5)")


class OptimizationSuggestion(BaseModel):
    """
    AI-generated optimization suggestion.
    """
    id: str = Field(..., description="Suggestion ID")
    type: str = Field(..., description="Suggestion type")
    category: str = Field(..., description="Suggestion category")
    priority: str = Field(..., description="Priority level")
    title: str = Field(..., description="Suggestion title")
    description: str = Field(..., description="Detailed description")
    
    # Impact estimation
    estimated_impact: Dict[str, float] = Field(..., description="Estimated impact metrics")
    confidence: float = Field(..., ge=0, le=1, description="Confidence score")
    
    # Implementation
    action_required: str = Field(..., description="Required action")
    implementation_effort: Optional[str] = Field(None, description="Implementation effort level")
    prerequisites: List[str] = Field(default=[], description="Implementation prerequisites")
    
    # Context
    affected_entities: List[str] = Field(default=[], description="Affected entities")
    supporting_data: Dict[str, Any] = Field(default={}, description="Supporting data")
    
    # Metadata
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Generation timestamp")
    status: str = Field("pending", description="Implementation status")


class AnalyticsReport(BaseEntity):
    """
    Analytics report model.
    """
    title: str = Field(..., description="Report title")
    description: Optional[str] = Field(None, description="Report description")
    type: str = Field(..., description="Report type")
    
    # Report configuration
    date_range: Dict[str, date] = Field(..., description="Report date range")
    granularity: Granularity = Field(Granularity.DAILY, description="Data granularity")
    metrics: List[MetricType] = Field(..., description="Included metrics")
    filters: Dict[str, Any] = Field(default={}, description="Applied filters")
    
    # Report data
    data: List[Dict[str, Any]] = Field(..., description="Report data")
    summary: Dict[str, Any] = Field(default={}, description="Report summary")
    insights: List[PerformanceInsight] = Field(default=[], description="Generated insights")
    
    # Metadata
    generated_at: datetime = Field(..., description="Report generation timestamp")
    generated_by: Optional[str] = Field(None, description="Report generator (user/system)")
    format: ReportFormat = Field(ReportFormat.JSON, description="Report format")
    
    # Status
    status: str = Field("completed", description="Report status")
    error_message: Optional[str] = Field(None, description="Error message if failed")


class DashboardWidget(BaseModel):
    """
    Dashboard widget configuration.
    """
    id: str = Field(..., description="Widget ID")
    type: str = Field(..., description="Widget type")
    title: str = Field(..., description="Widget title")
    position: Dict[str, int] = Field(..., description="Widget position and size")
    
    # Configuration
    metrics: List[MetricType] = Field(..., description="Displayed metrics")
    time_range: TimeRange = Field(..., description="Data time range")
    filters: Dict[str, Any] = Field(default={}, description="Applied filters")
    
    # Data
    data: Dict[str, Any] = Field(default={}, description="Widget data")
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")


class Dashboard(BaseEntity):
    """
    Analytics dashboard model.
    """
    name: str = Field(..., description="Dashboard name")
    description: Optional[str] = Field(None, description="Dashboard description")
    
    # Configuration
    widgets: List[DashboardWidget] = Field(..., description="Dashboard widgets")
    layout: Dict[str, Any] = Field(default={}, description="Dashboard layout configuration")
    
    # Access control
    owner_id: str = Field(..., description="Dashboard owner ID")
    shared_with: List[str] = Field(default=[], description="Users with access")
    is_public: bool = Field(False, description="Public dashboard flag")
    
    # Metadata
    last_viewed: Optional[datetime] = Field(None, description="Last viewed timestamp")
    view_count: int = Field(0, description="Total view count")


class AlertRule(BaseModel):
    """
    Performance alert rule.
    """
    id: Optional[str] = Field(None, description="Alert rule ID")
    name: str = Field(..., description="Alert rule name")
    description: Optional[str] = Field(None, description="Alert rule description")
    
    # Conditions
    metric: MetricType = Field(..., description="Monitored metric")
    condition: str = Field(..., description="Alert condition (>, <, =, etc.)")
    threshold: float = Field(..., description="Alert threshold value")
    time_window_minutes: int = Field(5, description="Time window for evaluation")
    
    # Actions
    notification_channels: List[str] = Field(..., description="Notification channels")
    severity: str = Field("medium", description="Alert severity")
    auto_resolve: bool = Field(True, description="Auto-resolve when condition clears")
    
    # Status
    enabled: bool = Field(True, description="Alert rule enabled")
    last_triggered: Optional[datetime] = Field(None, description="Last trigger timestamp")
    trigger_count: int = Field(0, description="Total trigger count")


class Alert(BaseEntity):
    """
    Performance alert instance.
    """
    rule_id: str = Field(..., description="Alert rule ID")
    campaign_id: Optional[str] = Field(None, description="Related campaign ID")
    
    # Alert details
    title: str = Field(..., description="Alert title")
    message: str = Field(..., description="Alert message")
    severity: str = Field(..., description="Alert severity")
    metric: MetricType = Field(..., description="Triggered metric")
    
    # Values
    current_value: float = Field(..., description="Current metric value")
    threshold_value: float = Field(..., description="Threshold value")
    deviation_percentage: Optional[float] = Field(None, description="Deviation from threshold")
    
    # Status
    status: str = Field("active", description="Alert status")
    acknowledged: bool = Field(False, description="Alert acknowledged")
    acknowledged_by: Optional[str] = Field(None, description="User who acknowledged")
    acknowledged_at: Optional[datetime] = Field(None, description="Acknowledgment timestamp")
    resolved_at: Optional[datetime] = Field(None, description="Resolution timestamp")


class AnalyticsReportResponse(BaseResponse[AnalyticsReport]):
    """
    Analytics report response model.
    """
    pass


class DashboardResponse(BaseResponse[Dashboard]):
    """
    Dashboard response model.
    """
    pass


class CompetitorAnalysis(BaseModel):
    """
    Competitor analysis data.
    """
    competitor_domain: str = Field(..., description="Competitor domain")
    competitor_name: Optional[str] = Field(None, description="Competitor name")
    
    # Market share data
    estimated_traffic: Optional[int] = Field(None, description="Estimated monthly traffic")
    market_share: Optional[float] = Field(None, description="Estimated market share")
    
    # Ad performance
    estimated_ad_spend: Optional[float] = Field(None, description="Estimated monthly ad spend")
    active_keywords: Optional[int] = Field(None, description="Number of active keywords")
    ad_copy_samples: List[str] = Field(default=[], description="Sample ad copies")
    
    # Insights
    strengths: List[str] = Field(default=[], description="Competitor strengths")
    opportunities: List[str] = Field(default=[], description="Opportunities against competitor")
    
    # Metadata
    last_analyzed: datetime = Field(default_factory=datetime.utcnow, description="Last analysis timestamp")
    confidence_score: Optional[float] = Field(None, description="Analysis confidence score")


class SeasonalityPattern(BaseModel):
    """
    Seasonality pattern analysis.
    """
    metric: MetricType = Field(..., description="Analyzed metric")
    pattern_type: str = Field(..., description="Pattern type (weekly, monthly, yearly)")
    
    # Pattern data
    pattern: Dict[str, float] = Field(..., description="Seasonal pattern values")
    strength: float = Field(..., ge=0, le=1, description="Pattern strength")
    confidence: float = Field(..., ge=0, le=1, description="Pattern confidence")
    
    # Predictions
    forecast: Optional[Dict[str, float]] = Field(None, description="Forecast values")
    recommendations: List[str] = Field(default=[], description="Seasonality-based recommendations")
    
    # Metadata
    analyzed_period: Dict[str, date] = Field(..., description="Analysis period")
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Analysis timestamp")


class AttributionModel(BaseModel):
    """
    Attribution model configuration and results.
    """
    name: str = Field(..., description="Attribution model name")
    type: str = Field(..., description="Attribution type (first-click, last-click, linear, etc.)")
    description: Optional[str] = Field(None, description="Model description")
    
    # Configuration
    lookback_window_days: int = Field(30, description="Attribution lookback window")
    include_view_through: bool = Field(True, description="Include view-through conversions")
    
    # Results
    attribution_results: Dict[str, float] = Field(default={}, description="Attribution results by channel")
    conversion_paths: List[Dict[str, Any]] = Field(default=[], description="Common conversion paths")
    
    # Performance
    model_accuracy: Optional[float] = Field(None, description="Model accuracy score")
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")


@validator("confidence", "strength", "model_accuracy", pre=True, allow_reuse=True)
def validate_percentage(cls, v):
    """Validate percentage values are between 0 and 1."""
    if v is not None and (v < 0 or v > 1):
        raise ValueError("Value must be between 0 and 1")
    return v