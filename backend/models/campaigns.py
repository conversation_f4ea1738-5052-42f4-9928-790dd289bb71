"""
Campaign-related Pydantic models.
Data structures for Google Ads campaigns, ad groups, ads, and keywords.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from enum import Enum

from pydantic import BaseModel, Field, validator

from .common import BaseEntity, BaseResponse, PaginatedResponse, MoneyAmount, Currency, Language, Country, Status


class CampaignType(str, Enum):
    """
    Google Ads campaign types.
    """
    SEARCH = "search"
    DISPLAY = "display"
    SHOPPING = "shopping"
    VIDEO = "video"
    PERFORMANCE_MAX = "performance_max"
    DISCOVERY = "discovery"
    LOCAL = "local"
    SMART = "smart"


class CampaignStatus(str, Enum):
    """
    Campaign status enumeration.
    """
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    REMOVED = "removed"
    ENDED = "ended"


class BiddingStrategy(str, Enum):
    """
    Bidding strategy types.
    """
    MANUAL_CPC = "manual_cpc"
    ENHANCED_CPC = "enhanced_cpc"
    TARGET_CPA = "target_cpa"
    TARGET_ROAS = "target_roas"
    MAXIMIZE_CLICKS = "maximize_clicks"
    MAXIMIZE_CONVERSIONS = "maximize_conversions"
    MAXIMIZE_CONVERSION_VALUE = "maximize_conversion_value"


class AdType(str, Enum):
    """
    Ad types enumeration.
    """
    TEXT_AD = "text_ad"
    EXPANDED_TEXT_AD = "expanded_text_ad"
    RESPONSIVE_SEARCH_AD = "responsive_search_ad"
    DISPLAY_AD = "display_ad"
    IMAGE_AD = "image_ad"
    VIDEO_AD = "video_ad"
    SHOPPING_AD = "shopping_ad"
    CALL_AD = "call_ad"


class KeywordMatchType(str, Enum):
    """
    Keyword match types.
    """
    EXACT = "exact"
    PHRASE = "phrase"
    BROAD = "broad"
    BROAD_MODIFIED = "broad_modified"


class AdStatus(str, Enum):
    """
    Ad status enumeration.
    """
    ENABLED = "enabled"
    PAUSED = "paused"
    REMOVED = "removed"
    PENDING_REVIEW = "pending_review"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    DISAPPROVED = "disapproved"


class CampaignBudget(BaseModel):
    """
    Campaign budget configuration.
    """
    daily_amount: float = Field(..., gt=0, description="Daily budget amount")
    currency: Currency = Field(Currency.USD, description="Budget currency")
    delivery_method: str = Field("standard", description="Budget delivery method (standard, accelerated)")
    total_amount: Optional[float] = Field(None, description="Total campaign budget limit")


class TargetingCriteria(BaseModel):
    """
    Campaign targeting criteria.
    """
    locations: List[str] = Field(default=[], description="Targeted geographic locations")
    languages: List[Language] = Field(default=[], description="Targeted languages")
    age_ranges: Optional[List[str]] = Field(None, description="Targeted age ranges")
    genders: Optional[List[str]] = Field(None, description="Targeted genders")
    devices: Optional[List[str]] = Field(None, description="Targeted devices")
    audiences: Optional[List[str]] = Field(None, description="Targeted audience segments")


class Keyword(BaseModel):
    """
    Keyword model.
    """
    id: Optional[str] = Field(None, description="Keyword ID")
    text: str = Field(..., min_length=1, max_length=80, description="Keyword text")
    match_type: KeywordMatchType = Field(..., description="Keyword match type")
    max_cpc: Optional[float] = Field(None, gt=0, description="Maximum cost per click")
    quality_score: Optional[int] = Field(None, ge=1, le=10, description="Quality score (1-10)")
    status: AdStatus = Field(AdStatus.ENABLED, description="Keyword status")
    negative: bool = Field(False, description="Whether this is a negative keyword")


class AdAsset(BaseModel):
    """
    Ad asset (headline, description, image, etc.).
    """
    id: Optional[str] = Field(None, description="Asset ID")
    type: str = Field(..., description="Asset type (headline, description, image, etc.)")
    content: str = Field(..., description="Asset content or URL")
    pinned_position: Optional[int] = Field(None, description="Pinned position for the asset")
    performance_label: Optional[str] = Field(None, description="Performance label (good, poor, etc.)")


class Ad(BaseModel):
    """
    Ad model.
    """
    id: Optional[str] = Field(None, description="Ad ID")
    type: AdType = Field(..., description="Ad type")
    status: AdStatus = Field(AdStatus.ENABLED, description="Ad status")
    headlines: List[AdAsset] = Field(default=[], description="Ad headlines")
    descriptions: List[AdAsset] = Field(default=[], description="Ad descriptions")
    display_url: Optional[str] = Field(None, description="Display URL")
    final_urls: List[str] = Field(default=[], description="Final landing page URLs")
    image_assets: Optional[List[AdAsset]] = Field(None, description="Image assets for display ads")
    video_assets: Optional[List[AdAsset]] = Field(None, description="Video assets for video ads")


class AdGroup(BaseModel):
    """
    Ad group model.
    """
    id: Optional[str] = Field(None, description="Ad group ID")
    name: str = Field(..., min_length=1, max_length=255, description="Ad group name")
    status: AdStatus = Field(AdStatus.ENABLED, description="Ad group status")
    max_cpc: Optional[float] = Field(None, gt=0, description="Maximum cost per click")
    keywords: List[Keyword] = Field(default=[], description="Keywords in this ad group")
    ads: List[Ad] = Field(default=[], description="Ads in this ad group")
    targeting: Optional[TargetingCriteria] = Field(None, description="Additional targeting criteria")


class CampaignMetrics(BaseModel):
    """
    Campaign performance metrics.
    """
    impressions: int = Field(0, ge=0, description="Number of impressions")
    clicks: int = Field(0, ge=0, description="Number of clicks")
    conversions: float = Field(0, ge=0, description="Number of conversions")
    cost: float = Field(0, ge=0, description="Total cost")
    revenue: Optional[float] = Field(None, ge=0, description="Total revenue")
    
    # Calculated metrics
    ctr: Optional[float] = Field(None, ge=0, le=1, description="Click-through rate")
    cpc: Optional[float] = Field(None, ge=0, description="Cost per click")
    cpm: Optional[float] = Field(None, ge=0, description="Cost per thousand impressions")
    conversion_rate: Optional[float] = Field(None, ge=0, le=1, description="Conversion rate")
    cost_per_conversion: Optional[float] = Field(None, ge=0, description="Cost per conversion")
    roas: Optional[float] = Field(None, ge=0, description="Return on ad spend")
    roi: Optional[float] = Field(None, description="Return on investment")


class Campaign(BaseEntity):
    """
    Main campaign model.
    """
    name: str = Field(..., min_length=1, max_length=255, description="Campaign name")
    description: Optional[str] = Field(None, max_length=1000, description="Campaign description")
    type: CampaignType = Field(..., description="Campaign type")
    status: CampaignStatus = Field(CampaignStatus.DRAFT, description="Campaign status")
    
    # Budget and bidding
    budget: Optional[CampaignBudget] = Field(None, description="Campaign budget configuration")
    budget_amount: float = Field(..., gt=0, description="Daily budget amount (for backward compatibility)")
    bidding_strategy: BiddingStrategy = Field(BiddingStrategy.MANUAL_CPC, description="Bidding strategy")
    target_cpa: Optional[float] = Field(None, gt=0, description="Target cost per acquisition")
    target_roas: Optional[float] = Field(None, gt=0, description="Target return on ad spend")
    
    # Targeting
    target_locations: List[str] = Field(default=[], description="Targeted locations")
    target_languages: List[Language] = Field(default=[], description="Targeted languages")
    targeting: Optional[TargetingCriteria] = Field(None, description="Advanced targeting criteria")
    
    # Campaign structure
    ad_groups: List[AdGroup] = Field(default=[], description="Ad groups in this campaign")
    keywords: List[str] = Field(default=[], description="Campaign-level keywords")
    negative_keywords: List[str] = Field(default=[], description="Negative keywords")
    
    # Dates and scheduling
    start_date: Optional[datetime] = Field(None, description="Campaign start date")
    end_date: Optional[datetime] = Field(None, description="Campaign end date")
    ad_schedule: Optional[Dict[str, Any]] = Field(None, description="Ad scheduling configuration")
    
    # Performance data
    metrics: Optional[CampaignMetrics] = Field(None, description="Campaign performance metrics")
    
    # Google Ads specific
    google_ads_id: Optional[str] = Field(None, description="Google Ads campaign ID")
    customer_id: Optional[str] = Field(None, description="Google Ads customer ID")
    
    # AI optimization
    auto_optimization_enabled: bool = Field(True, description="Enable automatic optimization")
    optimization_score: Optional[float] = Field(None, ge=0, le=1, description="Optimization score")
    last_optimized: Optional[datetime] = Field(None, description="Last optimization timestamp")


class CampaignCreate(BaseModel):
    """
    Campaign creation request model.
    """
    name: str = Field(..., min_length=1, max_length=255, description="Campaign name")
    description: Optional[str] = Field(None, max_length=1000, description="Campaign description")
    type: CampaignType = Field(..., description="Campaign type")
    budget_amount: float = Field(..., gt=0, description="Daily budget amount")
    bidding_strategy: BiddingStrategy = Field(BiddingStrategy.MANUAL_CPC, description="Bidding strategy")
    target_locations: List[str] = Field(..., min_items=1, description="Targeted locations")
    target_languages: List[Language] = Field(..., min_items=1, description="Targeted languages")
    keywords: List[str] = Field(default=[], description="Initial keywords")
    start_date: Optional[datetime] = Field(None, description="Campaign start date")
    end_date: Optional[datetime] = Field(None, description="Campaign end date")
    auto_optimization_enabled: bool = Field(True, description="Enable automatic optimization")


class CampaignUpdate(BaseModel):
    """
    Campaign update request model.
    """
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Campaign name")
    description: Optional[str] = Field(None, max_length=1000, description="Campaign description")
    status: Optional[CampaignStatus] = Field(None, description="Campaign status")
    budget_amount: Optional[float] = Field(None, gt=0, description="Daily budget amount")
    bidding_strategy: Optional[BiddingStrategy] = Field(None, description="Bidding strategy")
    target_locations: Optional[List[str]] = Field(None, description="Targeted locations")
    target_languages: Optional[List[Language]] = Field(None, description="Targeted languages")
    keywords: Optional[List[str]] = Field(None, description="Campaign keywords")
    negative_keywords: Optional[List[str]] = Field(None, description="Negative keywords")
    start_date: Optional[datetime] = Field(None, description="Campaign start date")
    end_date: Optional[datetime] = Field(None, description="Campaign end date")
    auto_optimization_enabled: Optional[bool] = Field(None, description="Enable automatic optimization")


class CampaignResponse(BaseResponse[Campaign]):
    """
    Single campaign response model.
    """
    pass


class CampaignListResponse(PaginatedResponse[Campaign]):
    """
    Campaign list response model.
    """
    pass


class KeywordPerformance(BaseModel):
    """
    Keyword performance metrics.
    """
    keyword: Keyword = Field(..., description="Keyword details")
    metrics: CampaignMetrics = Field(..., description="Performance metrics")
    search_volume: Optional[int] = Field(None, description="Average monthly search volume")
    competition: Optional[str] = Field(None, description="Competition level (low, medium, high)")
    suggested_bid: Optional[float] = Field(None, description="Suggested bid amount")


class AdPerformance(BaseModel):
    """
    Ad performance metrics.
    """
    ad: Ad = Field(..., description="Ad details")
    metrics: CampaignMetrics = Field(..., description="Performance metrics")
    approval_status: Optional[str] = Field(None, description="Ad approval status")
    policy_summary: Optional[str] = Field(None, description="Policy review summary")


class CampaignOptimizationSuggestion(BaseModel):
    """
    Campaign optimization suggestion.
    """
    id: str = Field(..., description="Suggestion ID")
    type: str = Field(..., description="Suggestion type")
    priority: str = Field(..., description="Priority level (low, medium, high)")
    title: str = Field(..., description="Suggestion title")
    description: str = Field(..., description="Detailed description")
    estimated_impact: Dict[str, float] = Field(..., description="Estimated impact metrics")
    action_required: str = Field(..., description="Required action to implement")
    confidence: float = Field(..., ge=0, le=1, description="Confidence score")
    
    @validator("priority")
    def validate_priority(cls, v):
        """Validate priority level."""
        if v not in ["low", "medium", "high"]:
            raise ValueError("Priority must be low, medium, or high")
        return v