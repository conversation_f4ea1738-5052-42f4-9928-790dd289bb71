"""
AI Agent-related Pydantic models.
Data structures for CrewAI agents, tasks, and agent management.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from enum import Enum

from pydantic import BaseModel, Field, validator

from .common import BaseEntity, BaseResponse, PaginatedResponse, Priority, Status


class AgentType(str, Enum):
    """
    AI agent types in the system.
    """
    CAMPAIGN_PLANNING = "campaign_planning"
    AD_ASSET_GENERATION = "ad_asset_generation"
    KEYWORD_RESEARCH = "keyword_research"
    BID_OPTIMIZATION = "bid_optimization"
    BUDGET_MANAGEMENT = "budget_management"
    PERFORMANCE_ANALYSIS = "performance_analysis"
    AUDIENCE_TARGETING = "audience_targeting"
    COMPETITOR_ANALYSIS = "competitor_analysis"
    CONTENT_OPTIMIZATION = "content_optimization"
    QUALITY_ASSURANCE = "quality_assurance"


class AgentStatus(str, Enum):
    """
    Agent status enumeration.
    """
    CREATED = "created"
    INITIALIZING = "initializing"
    ACTIVE = "active"
    BUSY = "busy"
    IDLE = "idle"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"
    MAINTENANCE = "maintenance"


class TaskStatus(str, Enum):
    """
    Task status enumeration.
    """
    PENDING = "pending"
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class TaskPriority(str, Enum):
    """
    Task priority levels.
    """
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class ModelProvider(str, Enum):
    """
    AI model providers.
    """
    OPENAI = "openai"
    GOOGLE = "google"
    ANTHROPIC = "anthropic"
    AZURE_OPENAI = "azure_openai"


class AgentCapability(BaseModel):
    """
    Agent capability definition.
    """
    name: str = Field(..., description="Capability name")
    description: str = Field(..., description="Capability description")
    input_types: List[str] = Field(..., description="Supported input types")
    output_types: List[str] = Field(..., description="Supported output types")
    prerequisites: List[str] = Field(default=[], description="Required prerequisites")


class AgentModel(BaseModel):
    """
    AI model configuration for an agent.
    """
    provider: ModelProvider = Field(..., description="Model provider")
    model_name: str = Field(..., description="Model name/identifier")
    temperature: float = Field(0.7, ge=0, le=2, description="Model temperature")
    max_tokens: int = Field(2000, ge=1, le=8192, description="Maximum tokens")
    top_p: Optional[float] = Field(None, ge=0, le=1, description="Top-p sampling parameter")
    frequency_penalty: Optional[float] = Field(None, ge=-2, le=2, description="Frequency penalty")
    presence_penalty: Optional[float] = Field(None, ge=-2, le=2, description="Presence penalty")


class AgentMemory(BaseModel):
    """
    Agent memory configuration.
    """
    enabled: bool = Field(True, description="Enable memory for the agent")
    memory_type: str = Field("vector", description="Memory type (vector, episodic, semantic)")
    max_entries: int = Field(1000, description="Maximum memory entries")
    retention_days: int = Field(30, description="Memory retention period in days")
    similarity_threshold: float = Field(0.8, description="Similarity threshold for memory retrieval")


class AgentTool(BaseModel):
    """
    Tool available to an agent.
    """
    name: str = Field(..., description="Tool name")
    description: str = Field(..., description="Tool description")
    parameters: Dict[str, Any] = Field(default={}, description="Tool parameters")
    enabled: bool = Field(True, description="Whether the tool is enabled")
    rate_limit: Optional[int] = Field(None, description="Rate limit per hour")


class AgentConfig(BaseModel):
    """
    Agent configuration settings.
    """
    model: AgentModel = Field(..., description="AI model configuration")
    memory: AgentMemory = Field(default_factory=AgentMemory, description="Memory configuration")
    tools: List[AgentTool] = Field(default=[], description="Available tools")
    max_iterations: int = Field(10, description="Maximum task iterations")
    timeout_seconds: int = Field(300, description="Task timeout in seconds")
    retry_attempts: int = Field(3, description="Number of retry attempts")
    verbose: bool = Field(False, description="Enable verbose logging")
    allow_delegation: bool = Field(True, description="Allow delegation to other agents")
    system_message: Optional[str] = Field(None, description="Custom system message")


class Agent(BaseEntity):
    """
    Main agent model.
    """
    name: str = Field(..., min_length=1, max_length=255, description="Agent name")
    description: str = Field(..., max_length=1000, description="Agent description")
    type: AgentType = Field(..., description="Agent type")
    status: AgentStatus = Field(AgentStatus.CREATED, description="Current agent status")
    
    # Configuration
    config: AgentConfig = Field(..., description="Agent configuration")
    capabilities: List[AgentCapability] = Field(default=[], description="Agent capabilities")
    
    # Assignment and context
    campaign_id: Optional[str] = Field(None, description="Assigned campaign ID")
    user_id: Optional[str] = Field(None, description="Owner user ID")
    team_id: Optional[str] = Field(None, description="Team ID for collaboration")
    
    # Performance tracking
    tasks_completed: int = Field(0, description="Number of completed tasks")
    tasks_failed: int = Field(0, description="Number of failed tasks")
    average_execution_time: Optional[float] = Field(None, description="Average task execution time")
    success_rate: Optional[float] = Field(None, description="Task success rate")
    
    # Status tracking
    last_activity: Optional[datetime] = Field(None, description="Last activity timestamp")
    last_error: Optional[str] = Field(None, description="Last error message")
    uptime_hours: Optional[float] = Field(None, description="Agent uptime in hours")
    
    # Resource usage
    memory_usage_mb: Optional[float] = Field(None, description="Current memory usage in MB")
    cpu_usage_percent: Optional[float] = Field(None, description="Current CPU usage percentage")
    
    # Version and updates
    version: str = Field("1.0.0", description="Agent version")
    last_updated: Optional[datetime] = Field(None, description="Last update timestamp")


class AgentTask(BaseEntity):
    """
    Task assigned to an agent.
    """
    agent_id: str = Field(..., description="Agent ID")
    campaign_id: Optional[str] = Field(None, description="Related campaign ID")
    
    # Task details
    name: str = Field(..., description="Task name")
    description: str = Field(..., description="Task description")
    type: str = Field(..., description="Task type")
    priority: TaskPriority = Field(TaskPriority.NORMAL, description="Task priority")
    status: TaskStatus = Field(TaskStatus.PENDING, description="Task status")
    
    # Task data
    input_data: Dict[str, Any] = Field(default={}, description="Task input data")
    output_data: Optional[Dict[str, Any]] = Field(None, description="Task output data")
    context: Dict[str, Any] = Field(default={}, description="Additional context")
    
    # Execution details
    started_at: Optional[datetime] = Field(None, description="Task start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Task completion timestamp")
    execution_time_seconds: Optional[float] = Field(None, description="Task execution time")
    retry_count: int = Field(0, description="Number of retries")
    
    # Results and errors
    result: Optional[str] = Field(None, description="Task result summary")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    logs: List[str] = Field(default=[], description="Task execution logs")
    
    # Dependencies
    parent_task_id: Optional[str] = Field(None, description="Parent task ID")
    dependent_task_ids: List[str] = Field(default=[], description="Dependent task IDs")
    
    # Scheduling
    scheduled_at: Optional[datetime] = Field(None, description="Scheduled execution time")
    deadline: Optional[datetime] = Field(None, description="Task deadline")


class AgentMetrics(BaseModel):
    """
    Agent performance metrics.
    """
    agent_id: str = Field(..., description="Agent ID")
    period_start: datetime = Field(..., description="Metrics period start")
    period_end: datetime = Field(..., description="Metrics period end")
    
    # Task metrics
    total_tasks: int = Field(0, description="Total tasks assigned")
    completed_tasks: int = Field(0, description="Successfully completed tasks")
    failed_tasks: int = Field(0, description="Failed tasks")
    cancelled_tasks: int = Field(0, description="Cancelled tasks")
    
    # Performance metrics
    success_rate: float = Field(0, description="Task success rate")
    average_execution_time: float = Field(0, description="Average execution time in seconds")
    throughput_per_hour: float = Field(0, description="Tasks completed per hour")
    
    # Resource metrics
    avg_memory_usage_mb: float = Field(0, description="Average memory usage")
    avg_cpu_usage_percent: float = Field(0, description="Average CPU usage")
    uptime_percentage: float = Field(0, description="Uptime percentage")
    
    # Quality metrics
    output_quality_score: Optional[float] = Field(None, description="Output quality score")
    user_satisfaction_score: Optional[float] = Field(None, description="User satisfaction score")
    error_rate: float = Field(0, description="Error rate")


class AgentCreate(BaseModel):
    """
    Agent creation request model.
    """
    name: str = Field(..., min_length=1, max_length=255, description="Agent name")
    description: str = Field(..., max_length=1000, description="Agent description")
    type: AgentType = Field(..., description="Agent type")
    campaign_id: Optional[str] = Field(None, description="Assigned campaign ID")
    config: AgentConfig = Field(..., description="Agent configuration")


class AgentUpdate(BaseModel):
    """
    Agent update request model.
    """
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Agent name")
    description: Optional[str] = Field(None, max_length=1000, description="Agent description")
    status: Optional[AgentStatus] = Field(None, description="Agent status")
    campaign_id: Optional[str] = Field(None, description="Assigned campaign ID")
    config: Optional[AgentConfig] = Field(None, description="Agent configuration")


class TaskCreate(BaseModel):
    """
    Task creation request model.
    """
    agent_id: str = Field(..., description="Agent ID")
    name: str = Field(..., description="Task name")
    description: str = Field(..., description="Task description")
    type: str = Field(..., description="Task type")
    priority: TaskPriority = Field(TaskPriority.NORMAL, description="Task priority")
    input_data: Dict[str, Any] = Field(default={}, description="Task input data")
    context: Dict[str, Any] = Field(default={}, description="Additional context")
    scheduled_at: Optional[datetime] = Field(None, description="Scheduled execution time")
    deadline: Optional[datetime] = Field(None, description="Task deadline")


class TaskUpdate(BaseModel):
    """
    Task update request model.
    """
    status: Optional[TaskStatus] = Field(None, description="Task status")
    priority: Optional[TaskPriority] = Field(None, description="Task priority")
    output_data: Optional[Dict[str, Any]] = Field(None, description="Task output data")
    result: Optional[str] = Field(None, description="Task result summary")
    error_message: Optional[str] = Field(None, description="Error message")
    scheduled_at: Optional[datetime] = Field(None, description="Scheduled execution time")
    deadline: Optional[datetime] = Field(None, description="Task deadline")


class AgentResponse(BaseResponse[Agent]):
    """
    Single agent response model.
    """
    pass


class AgentListResponse(PaginatedResponse[Agent]):
    """
    Agent list response model.
    """
    pass


class AgentTaskResponse(PaginatedResponse[AgentTask]):
    """
    Agent task list response model.
    """
    pass


class TaskResponse(BaseResponse[AgentTask]):
    """
    Single task response model.
    """
    pass


class AgentCommand(BaseModel):
    """
    Command to be executed by an agent.
    """
    command: str = Field(..., description="Command to execute")
    parameters: Dict[str, Any] = Field(default={}, description="Command parameters")
    timeout_seconds: Optional[int] = Field(None, description="Command timeout")
    priority: TaskPriority = Field(TaskPriority.NORMAL, description="Command priority")


class AgentCollaboration(BaseModel):
    """
    Agent collaboration configuration.
    """
    enabled: bool = Field(True, description="Enable collaboration")
    max_collaborators: int = Field(5, description="Maximum number of collaborating agents")
    collaboration_strategy: str = Field("hierarchical", description="Collaboration strategy")
    communication_protocol: str = Field("direct", description="Communication protocol")
    shared_memory: bool = Field(False, description="Enable shared memory")


class AgentPerformanceReport(BaseModel):
    """
    Agent performance report.
    """
    agent_id: str = Field(..., description="Agent ID")
    report_period: str = Field(..., description="Report period")
    generated_at: datetime = Field(..., description="Report generation timestamp")
    
    metrics: AgentMetrics = Field(..., description="Performance metrics")
    trends: Dict[str, float] = Field(..., description="Performance trends")
    recommendations: List[str] = Field(..., description="Performance improvement recommendations")
    issues: List[str] = Field(default=[], description="Identified issues")
    
    comparison_period: Optional[AgentMetrics] = Field(None, description="Previous period metrics for comparison")
    
    @validator("report_period")
    def validate_report_period(cls, v):
        """Validate report period format."""
        valid_periods = ["hourly", "daily", "weekly", "monthly"]
        if v not in valid_periods:
            raise ValueError(f"Report period must be one of: {valid_periods}")
        return v