"""
Comprehensive Pydantic schemas for request/response validation.
Provides detailed validation for all API endpoints with proper error handling.
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Dict, List, Optional, Union, Any
from enum import Enum
from uuid import UUID

from pydantic import BaseModel, Field, validator, root_validator
from pydantic.types import PositiveFloat, PositiveInt, EmailStr
from pydantic import constr, conint, confloat


# ================================================================
# ENUMS FOR VALIDATION
# ================================================================

class GoogleAdsNetworkType(str, Enum):
    """Google Ads network type enumeration."""
    SEARCH = "search"
    SEARCH_PARTNERS = "search_partners"
    DISPLAY = "display"
    YOUTUBE_SEARCH = "youtube_search"
    YOUTUBE_WATCH = "youtube_watch"
    GMAIL = "gmail"
    DISCOVER = "discover"


class GoogleAdsDeviceType(str, Enum):
    """Google Ads device type enumeration."""
    DESKTOP = "desktop"
    MOBILE = "mobile"
    TABLET = "tablet"
    TV = "tv"
    UNKNOWN = "unknown"


class GoogleAdsLocationTargetType(str, Enum):
    """Google Ads location target type enumeration."""
    PRESENCE = "presence"
    INTEREST = "interest"
    PRESENCE_OR_INTEREST = "presence_or_interest"


class GoogleAdsAgeRange(str, Enum):
    """Google Ads age range enumeration."""
    AGE_18_24 = "18-24"
    AGE_25_34 = "25-34"
    AGE_35_44 = "35-44"
    AGE_45_54 = "45-54"
    AGE_55_64 = "55-64"
    AGE_65_PLUS = "65+"
    UNDETERMINED = "undetermined"


class GoogleAdsGender(str, Enum):
    """Google Ads gender enumeration."""
    MALE = "male"
    FEMALE = "female"
    UNDETERMINED = "undetermined"


class CurrencyCode(str, Enum):
    """ISO 4217 currency codes supported by Google Ads."""
    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    CAD = "CAD"
    AUD = "AUD"
    JPY = "JPY"
    CHF = "CHF"
    SEK = "SEK"
    NOK = "NOK"
    DKK = "DKK"
    PLN = "PLN"
    CZK = "CZK"
    HUF = "HUF"
    BGN = "BGN"
    RON = "RON"
    HRK = "HRK"
    RSD = "RSD"
    BAM = "BAM"
    MKD = "MKD"
    ALL = "ALL"
    

class LanguageCode(str, Enum):
    """ISO 639-1 language codes supported by Google Ads."""
    EN = "en"  # English
    ES = "es"  # Spanish
    FR = "fr"  # French
    DE = "de"  # German
    IT = "it"  # Italian
    PT = "pt"  # Portuguese
    RU = "ru"  # Russian
    JA = "ja"  # Japanese
    KO = "ko"  # Korean
    ZH = "zh"  # Chinese
    AR = "ar"  # Arabic
    HI = "hi"  # Hindi
    BN = "bn"  # Bengali
    UR = "ur"  # Urdu
    TR = "tr"  # Turkish
    NL = "nl"  # Dutch
    SV = "sv"  # Swedish
    DA = "da"  # Danish
    NO = "no"  # Norwegian
    FI = "fi"  # Finnish
    PL = "pl"  # Polish
    CS = "cs"  # Czech
    HU = "hu"  # Hungarian
    RO = "ro"  # Romanian
    BG = "bg"  # Bulgarian
    HR = "hr"  # Croatian
    SK = "sk"  # Slovak
    SL = "sl"  # Slovenian
    ET = "et"  # Estonian
    LV = "lv"  # Latvian
    LT = "lt"  # Lithuanian
    MT = "mt"  # Maltese
    EL = "el"  # Greek
    

class AdApprovalStatus(str, Enum):
    """Google Ads ad approval status enumeration."""
    APPROVED = "approved"
    APPROVED_LIMITED = "approved_limited"
    DISAPPROVED = "disapproved"
    UNDER_REVIEW = "under_review"
    AREA_OF_INTEREST_ONLY = "area_of_interest_only"

class CampaignType(str, Enum):
    """Campaign type enumeration."""
    SEARCH = "search"
    DISPLAY = "display"
    SHOPPING = "shopping"
    VIDEO = "video"
    PERFORMANCE_MAX = "performance_max"
    DISCOVERY = "discovery"
    LOCAL = "local"
    SMART = "smart"


class CampaignStatus(str, Enum):
    """Campaign status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    REMOVED = "removed"
    ENDED = "ended"
    DELETED = "deleted"


class BiddingStrategy(str, Enum):
    """Bidding strategy enumeration."""
    MANUAL_CPC = "manual_cpc"
    ENHANCED_CPC = "enhanced_cpc"
    TARGET_CPA = "target_cpa"
    TARGET_ROAS = "target_roas"
    MAXIMIZE_CLICKS = "maximize_clicks"
    MAXIMIZE_CONVERSIONS = "maximize_conversions"
    MAXIMIZE_CONVERSION_VALUE = "maximize_conversion_value"


class AdType(str, Enum):
    """Ad type enumeration."""
    TEXT_AD = "text_ad"
    EXPANDED_TEXT_AD = "expanded_text_ad"
    RESPONSIVE_SEARCH_AD = "responsive_search_ad"
    DISPLAY_AD = "display_ad"
    IMAGE_AD = "image_ad"
    VIDEO_AD = "video_ad"
    SHOPPING_AD = "shopping_ad"
    CALL_AD = "call_ad"


class AdStatus(str, Enum):
    """Ad status enumeration."""
    ENABLED = "enabled"
    PAUSED = "paused"
    REMOVED = "removed"
    PENDING_REVIEW = "pending_review"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    DISAPPROVED = "disapproved"


class AgentType(str, Enum):
    """Agent type enumeration."""
    CAMPAIGN_PLANNING = "campaign_planning"
    AD_ASSET_GENERATION = "ad_asset_generation"
    KEYWORD_RESEARCH = "keyword_research"
    BID_OPTIMIZATION = "bid_optimization"
    BUDGET_MANAGEMENT = "budget_management"
    PERFORMANCE_ANALYSIS = "performance_analysis"
    AUDIENCE_TARGETING = "audience_targeting"
    COMPETITOR_ANALYSIS = "competitor_analysis"
    CONTENT_OPTIMIZATION = "content_optimization"
    QUALITY_ASSURANCE = "quality_assurance"


class AgentStatus(str, Enum):
    """Agent status enumeration."""
    CREATED = "created"
    INITIALIZING = "initializing"
    ACTIVE = "active"
    BUSY = "busy"
    IDLE = "idle"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"
    MAINTENANCE = "maintenance"


class TaskStatus(str, Enum):
    """Task status enumeration."""
    PENDING = "pending"
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class TaskPriority(str, Enum):
    """Task priority enumeration."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class MatchType(str, Enum):
    """Keyword match type enumeration."""
    EXACT = "exact"
    PHRASE = "phrase"
    BROAD = "broad"
    BROAD_MODIFIED = "broad_modified"


# ================================================================
# BASE MODELS
# ================================================================

class BaseSchema(BaseModel):
    """Base schema with common configuration."""
    
    class Config:
        extra = "forbid"  # Reject extra fields
        validate_assignment = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            date: lambda v: v.isoformat(),
            Decimal: lambda v: str(v),
            UUID: lambda v: str(v),
        }


class TimestampMixin(BaseModel):
    """Mixin for timestamp fields."""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    deleted_at: Optional[datetime] = None


class UserContextMixin(BaseModel):
    """Mixin for user context fields."""
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None


# ================================================================
# CAMPAIGN SCHEMAS
# ================================================================

class GoogleAdsKeyword(BaseSchema):
    """Google Ads keyword configuration."""
    text: constr(min_length=1, max_length=80, strip_whitespace=True) = Field(..., description="Keyword text")
    match_type: MatchType = Field(..., description="Keyword match type")
    max_cpc: Optional[confloat(ge=0.01, le=1000.00)] = Field(None, description="Maximum cost per click")
    negative: bool = Field(default=False, description="Whether this is a negative keyword")
    quality_score: Optional[conint(ge=1, le=10)] = Field(None, description="Keyword quality score")
    
    @validator('text')
    def validate_keyword_text(cls, v):
        """Validate keyword text for Google Ads compliance."""
        # Remove extra whitespace
        v = ' '.join(v.split())
        
        # Check for prohibited characters
        prohibited_chars = ['!', '@', '%', '^', '*', '(', ')', '=', '+', '[', ']', '{', '}', '\\', '|', ';', ':', '"', "'", '<', '>', '?', '/', '~', '`']
        for char in prohibited_chars:
            if char in v:
                raise ValueError(f"Keyword contains prohibited character: {char}")
        
        # Check for trademark symbols
        if '®' in v or '™' in v or '©' in v:
            raise ValueError("Keywords cannot contain trademark symbols")
        
        return v


class GoogleAdsLocationTarget(BaseSchema):
    """Google Ads location targeting configuration."""
    location_id: conint(ge=1) = Field(..., description="Google Ads location criterion ID")
    location_name: constr(min_length=1, max_length=255) = Field(..., description="Location name")
    target_type: GoogleAdsLocationTargetType = Field(
        default=GoogleAdsLocationTargetType.PRESENCE,
        description="Location targeting type"
    )
    bid_modifier: Optional[confloat(ge=0.1, le=9.0)] = Field(
        None, 
        description="Bid modifier for this location (0.1 to 9.0)"
    )
    
    @validator('location_id')
    def validate_location_id(cls, v):
        """Validate Google Ads location criterion ID."""
        # Google Ads location IDs are typically 6-9 digits
        if not (100000 <= v <= 999999999):
            raise ValueError("Invalid Google Ads location criterion ID")
        return v


class GoogleAdsDemographicTarget(BaseSchema):
    """Google Ads demographic targeting configuration."""
    age_ranges: List[GoogleAdsAgeRange] = Field(default=[], max_items=10, description="Target age ranges")
    genders: List[GoogleAdsGender] = Field(default=[], max_items=3, description="Target genders")
    parental_status: List[str] = Field(default=[], max_items=10, description="Parental status targeting")
    household_income: List[str] = Field(default=[], max_items=10, description="Household income targeting")
    
    @validator('age_ranges', 'genders', 'parental_status', 'household_income')
    def validate_demographic_lists(cls, v):
        """Ensure no duplicate demographic targets."""
        if len(v) != len(set(v)):
            raise ValueError("Duplicate demographic targets are not allowed")
        return v


class GoogleAdsDeviceTarget(BaseSchema):
    """Google Ads device targeting configuration."""
    device_type: GoogleAdsDeviceType = Field(..., description="Device type")
    bid_modifier: Optional[confloat(ge=0.1, le=9.0)] = Field(
        None,
        description="Bid modifier for this device type (0.1 to 9.0)"
    )
    operating_system: Optional[str] = Field(None, max_length=50, description="Operating system targeting")
    
    @validator('operating_system')
    def validate_operating_system(cls, v):
        """Validate operating system values."""
        if v:
            valid_os = ['Windows', 'macOS', 'Linux', 'iOS', 'Android', 'Chrome OS']
            if v not in valid_os:
                raise ValueError(f"Invalid operating system. Must be one of: {valid_os}")
        return v


class GoogleAdsNetworkTarget(BaseSchema):
    """Google Ads network targeting configuration."""
    networks: List[GoogleAdsNetworkType] = Field(
        default=[GoogleAdsNetworkType.SEARCH],
        min_items=1,
        max_items=10,
        description="Target ad networks"
    )
    search_partners: bool = Field(default=True, description="Include search partners")
    display_network: bool = Field(default=False, description="Include display network")
    
    @validator('networks')
    def validate_networks(cls, v):
        """Ensure no duplicate networks."""
        if len(v) != len(set(v)):
            raise ValueError("Duplicate networks are not allowed")
        return v


class CampaignTargeting(BaseSchema):
    """Enhanced campaign targeting configuration for Google Ads."""
    locations: List[GoogleAdsLocationTarget] = Field(default=[], max_items=1000, description="Location targeting")
    languages: List[LanguageCode] = Field(default=[LanguageCode.EN], max_items=50, description="Language targeting")
    demographics: GoogleAdsDemographicTarget = Field(default_factory=GoogleAdsDemographicTarget, description="Demographic targeting")
    devices: List[GoogleAdsDeviceTarget] = Field(default=[], max_items=10, description="Device targeting")
    networks: GoogleAdsNetworkTarget = Field(default_factory=GoogleAdsNetworkTarget, description="Network targeting")
    interests: List[str] = Field(default=[], max_items=100, description="Interest targeting")
    custom_audiences: List[str] = Field(default=[], max_items=50, description="Custom audience targeting")
    
    # Advanced targeting options
    dayparting: Optional[Dict[str, Any]] = Field(None, description="Day and time targeting")
    exclude_placements: List[str] = Field(default=[], max_items=1000, description="Excluded placements")
    include_placements: List[str] = Field(default=[], max_items=1000, description="Included placements")
    
    @validator('exclude_placements', 'include_placements')
    def validate_placements(cls, v):
        """Validate placement URLs."""
        import re
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        for placement in v:
            if not url_pattern.match(placement):
                raise ValueError(f"Invalid placement URL: {placement}")
        return v


class CampaignSchedule(BaseSchema):
    """Campaign ad schedule configuration."""
    timezone: str = Field(default="UTC", max_length=50)
    schedule: Dict[str, List[Dict[str, str]]] = Field(default={})
    
    @validator('schedule')
    def validate_schedule(cls, v):
        """Validate schedule format."""
        valid_days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        for day, times in v.items():
            if day.lower() not in valid_days:
                raise ValueError(f"Invalid day: {day}")
            for time_slot in times:
                if not isinstance(time_slot, dict) or 'start' not in time_slot or 'end' not in time_slot:
                    raise ValueError("Time slots must have 'start' and 'end' keys")
        return v


class CampaignCreateRequest(BaseSchema):
    """Request schema for creating campaigns."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=2000)
    type: CampaignType
    status: CampaignStatus = CampaignStatus.DRAFT
    
    # Budget configuration with Google Ads specific validation
    budget_amount: confloat(ge=0.01, le=1000000.00) = Field(..., description="Daily budget amount")
    budget_currency: CurrencyCode = Field(default=CurrencyCode.USD, description="Budget currency code")
    budget_delivery_method: constr(regex="^(standard|accelerated)$") = Field(
        default="standard", 
        description="Budget delivery method"
    )
    total_budget_limit: Optional[confloat(ge=0.01, le=10000000.00)] = Field(
        None, 
        description="Total campaign budget limit"
    )
    shared_budget_id: Optional[constr(min_length=1, max_length=255)] = Field(
        None,
        description="Shared budget ID if using shared budget"
    )
    
    # Enhanced bidding configuration
    bidding_strategy: BiddingStrategy = BiddingStrategy.MANUAL_CPC
    target_cpa: Optional[confloat(ge=0.01, le=1000.00)] = Field(
        None, 
        description="Target cost per acquisition (CPA)"
    )
    target_roas: Optional[confloat(ge=0.01, le=100.00)] = Field(
        None, 
        description="Target return on ad spend (ROAS)"
    )
    max_cpc: Optional[confloat(ge=0.01, le=100.00)] = Field(
        None, 
        description="Maximum cost per click (CPC)"
    )
    target_cpm: Optional[confloat(ge=0.01, le=1000.00)] = Field(
        None,
        description="Target cost per mille (CPM) for display campaigns"
    )
    target_impression_share: Optional[confloat(ge=0.1, le=1.0)] = Field(
        None,
        description="Target impression share (0.1 to 1.0)"
    )
    portfolio_bidding_strategy_id: Optional[str] = Field(
        None,
        description="Portfolio bidding strategy ID"
    )
    
    # Enhanced targeting with Google Ads specific validation
    targeting: CampaignTargeting = Field(default_factory=CampaignTargeting, description="Campaign targeting settings")
    keywords: List[GoogleAdsKeyword] = Field(default=[], max_items=20000, description="Campaign keywords")
    negative_keywords: List[GoogleAdsKeyword] = Field(default=[], max_items=5000, description="Negative keywords")
    
    # Google Ads specific settings
    google_ads_settings: Optional[Dict[str, Any]] = Field(
        default={},
        description="Google Ads specific campaign settings"
    )
    conversion_goals: List[str] = Field(
        default=[],
        max_items=20,
        description="Conversion goals for the campaign"
    )
    audience_targets: List[str] = Field(
        default=[],
        max_items=100,
        description="Audience targeting IDs"
    )
    
    # Scheduling
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    ad_schedule: Optional[CampaignSchedule] = None
    
    # Google Ads integration
    google_ads_id: Optional[str] = Field(None, max_length=255)
    customer_id: Optional[str] = Field(None, max_length=255)
    
    # AI optimization
    auto_optimization_enabled: bool = True
    
    # GDPR Compliance
    data_processing_consent: bool = False
    data_retention_expires_at: Optional[datetime] = None
    
    @validator('keywords')
    def validate_keywords_list(cls, v):
        """Validate keywords list for Google Ads compliance."""
        if not v:
            return v
        
        # Check for duplicate keywords
        keyword_texts = [kw.text.lower() for kw in v]
        if len(keyword_texts) != len(set(keyword_texts)):
            raise ValueError("Duplicate keywords are not allowed")
        
        return v
    
    @validator('negative_keywords')
    def validate_negative_keywords_list(cls, v):
        """Validate negative keywords list."""
        if not v:
            return v
        
        # Ensure all negative keywords have negative flag set
        for kw in v:
            if not kw.negative:
                kw.negative = True
        
        # Check for duplicates
        keyword_texts = [kw.text.lower() for kw in v]
        if len(keyword_texts) != len(set(keyword_texts)):
            raise ValueError("Duplicate negative keywords are not allowed")
        
        return v
    
    @validator('conversion_goals')
    def validate_conversion_goals(cls, v):
        """Validate conversion goals."""
        valid_goals = [
            'purchase', 'add_to_cart', 'begin_checkout', 'lead', 'sign_up',
            'download', 'page_view', 'subscribe', 'contact', 'call'
        ]
        
        for goal in v:
            if goal not in valid_goals:
                raise ValueError(f"Invalid conversion goal: {goal}. Must be one of {valid_goals}")
        
        return v
    
    @root_validator
    def validate_dates(cls, values):
        """Validate date constraints."""
        start_date = values.get('start_date')
        end_date = values.get('end_date')
        
        if start_date and end_date and start_date >= end_date:
            raise ValueError("End date must be after start date")
        
        if start_date and start_date < datetime.now():
            raise ValueError("Start date cannot be in the past")
        
        return values
    
    @root_validator
    def validate_budget_constraints(cls, values):
        """Validate budget constraints with Google Ads specific rules."""
        budget_amount = values.get('budget_amount')
        total_budget_limit = values.get('total_budget_limit')
        shared_budget_id = values.get('shared_budget_id')
        
        # If using shared budget, individual budget amount should not be set
        if shared_budget_id and budget_amount:
            raise ValueError("Cannot set budget_amount when using shared_budget_id")
        
        if total_budget_limit and budget_amount and total_budget_limit < budget_amount:
            raise ValueError("Total budget limit must be greater than or equal to daily budget")
        
        # Validate minimum budget amounts by currency
        if budget_amount:
            currency = values.get('budget_currency', 'USD')
            min_budgets = {
                'USD': 1.00, 'EUR': 1.00, 'GBP': 0.80, 'CAD': 1.30,
                'AUD': 1.40, 'JPY': 100.00, 'CHF': 1.00
            }
            
            min_budget = min_budgets.get(currency, 1.00)
            if budget_amount < min_budget:
                raise ValueError(f"Minimum daily budget for {currency} is {min_budget}")
        
        return values
    
    @root_validator
    def validate_bidding_strategy_constraints(cls, values):
        """Validate bidding strategy constraints."""
        bidding_strategy = values.get('bidding_strategy')
        target_cpa = values.get('target_cpa')
        target_roas = values.get('target_roas')
        max_cpc = values.get('max_cpc')
        target_cpm = values.get('target_cpm')
        
        # Validate required fields for specific bidding strategies
        if bidding_strategy == BiddingStrategy.TARGET_CPA and not target_cpa:
            raise ValueError("target_cpa is required for TARGET_CPA bidding strategy")
        
        if bidding_strategy == BiddingStrategy.TARGET_ROAS and not target_roas:
            raise ValueError("target_roas is required for TARGET_ROAS bidding strategy")
        
        if bidding_strategy == BiddingStrategy.MANUAL_CPC and not max_cpc:
            values['max_cpc'] = 1.00  # Set default max CPC
        
        # Ensure incompatible bidding options are not set together
        if target_cpm and bidding_strategy not in [BiddingStrategy.MANUAL_CPC]:
            raise ValueError("target_cpm can only be used with compatible bidding strategies")
        
        return values
    
    @root_validator 
    def validate_campaign_type_constraints(cls, values):
        """Validate campaign type specific constraints."""
        campaign_type = values.get('type')
        targeting = values.get('targeting', {})
        
        # Shopping campaigns have specific requirements
        if campaign_type == CampaignType.SHOPPING:
            if not values.get('google_ads_settings', {}).get('merchant_id'):
                raise ValueError("Shopping campaigns require merchant_id in google_ads_settings")
        
        # Video campaigns require YouTube network
        if campaign_type == CampaignType.VIDEO:
            networks = targeting.get('networks', {}).get('networks', [])
            youtube_networks = [GoogleAdsNetworkType.YOUTUBE_SEARCH, GoogleAdsNetworkType.YOUTUBE_WATCH]
            if not any(net in networks for net in youtube_networks):
                raise ValueError("Video campaigns must target YouTube networks")
        
        return values


class CampaignUpdateRequest(BaseSchema):
    """Request schema for updating campaigns."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=2000)
    status: Optional[CampaignStatus] = None
    
    # Budget configuration
    budget_amount: Optional[PositiveFloat] = Field(None, ge=0.01, le=1000000.00)
    budget_delivery_method: Optional[str] = Field(None, regex="^(standard|accelerated)$")
    total_budget_limit: Optional[PositiveFloat] = Field(None, ge=0.01, le=10000000.00)
    
    # Bidding configuration
    bidding_strategy: Optional[BiddingStrategy] = None
    target_cpa: Optional[PositiveFloat] = Field(None, ge=0.01, le=1000.00)
    target_roas: Optional[PositiveFloat] = Field(None, ge=0.01, le=100.00)
    max_cpc: Optional[PositiveFloat] = Field(None, ge=0.01, le=100.00)
    
    # Targeting
    targeting: Optional[CampaignTargeting] = None
    keywords: Optional[List[str]] = Field(None, max_items=1000)
    negative_keywords: Optional[List[str]] = Field(None, max_items=1000)
    
    # Scheduling
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    ad_schedule: Optional[CampaignSchedule] = None
    
    # AI optimization
    auto_optimization_enabled: Optional[bool] = None


class CampaignResponse(BaseSchema, TimestampMixin, UserContextMixin):
    """Response schema for campaigns."""
    id: UUID
    name: str
    description: Optional[str] = None
    type: CampaignType
    status: CampaignStatus
    
    # Budget configuration
    budget_amount: Decimal
    budget_currency: str
    budget_delivery_method: str
    total_budget_limit: Optional[Decimal] = None
    
    # Bidding configuration
    bidding_strategy: BiddingStrategy
    target_cpa: Optional[Decimal] = None
    target_roas: Optional[Decimal] = None
    max_cpc: Optional[Decimal] = None
    
    # Targeting
    targeting: Dict[str, Any]
    keywords: List[str]
    negative_keywords: List[str]
    
    # Scheduling
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    ad_schedule: Optional[Dict[str, Any]] = None
    
    # Google Ads integration
    google_ads_id: Optional[str] = None
    customer_id: Optional[str] = None
    
    # AI optimization
    auto_optimization_enabled: bool
    optimization_score: Optional[Decimal] = None
    last_optimized: Optional[datetime] = None
    
    # GDPR Compliance
    data_processing_consent: bool
    data_retention_expires_at: Optional[datetime] = None


# ================================================================
# AD GROUP SCHEMAS
# ================================================================

class AdGroupCreateRequest(BaseSchema):
    """Request schema for creating ad groups."""
    campaign_id: UUID
    name: str = Field(..., min_length=1, max_length=255)
    status: AdStatus = AdStatus.ENABLED
    
    # Bidding configuration
    bid_strategy: str = Field(default="manual_cpc", max_length=50)
    max_cpc: Optional[PositiveFloat] = Field(None, ge=0.01, le=100.00)
    target_cpa: Optional[PositiveFloat] = Field(None, ge=0.01, le=1000.00)
    
    # Keywords
    keywords: List[Dict[str, Any]] = Field(default=[], max_items=1000)
    negative_keywords: List[str] = Field(default=[], max_items=1000)
    
    @validator('keywords')
    def validate_keywords(cls, v):
        """Validate keyword structure."""
        for keyword in v:
            if not isinstance(keyword, dict):
                raise ValueError("Keywords must be objects")
            if 'text' not in keyword or 'match_type' not in keyword:
                raise ValueError("Keywords must have 'text' and 'match_type' fields")
            if keyword['match_type'] not in ['exact', 'phrase', 'broad', 'broad_modified']:
                raise ValueError("Invalid match type")
        return v


class AdGroupResponse(BaseSchema, TimestampMixin, UserContextMixin):
    """Response schema for ad groups."""
    id: UUID
    campaign_id: UUID
    name: str
    status: AdStatus
    
    # Bidding configuration
    bid_strategy: str
    max_cpc: Optional[Decimal] = None
    target_cpa: Optional[Decimal] = None
    
    # Keywords
    keywords: List[Dict[str, Any]]
    negative_keywords: List[str]
    
    # Google Ads integration
    google_ads_id: Optional[str] = None


# ================================================================
# AD SCHEMAS
# ================================================================

class AdAsset(BaseSchema):
    """Ad asset schema."""
    type: str = Field(..., max_length=50)
    text: Optional[str] = Field(None, max_length=1000)
    url: Optional[str] = Field(None, max_length=2083)
    width: Optional[int] = Field(None, ge=1, le=10000)
    height: Optional[int] = Field(None, ge=1, le=10000)
    file_size: Optional[int] = Field(None, ge=1, le=50000000)  # 50MB max


class AdCreateRequest(BaseSchema):
    """Request schema for creating ads."""
    ad_group_id: UUID
    type: AdType
    status: AdStatus = AdStatus.ENABLED
    
    # Ad content
    headlines: List[str] = Field(..., min_items=1, max_items=15)
    descriptions: List[str] = Field(..., min_items=1, max_items=4)
    display_url: Optional[str] = Field(None, max_length=255)
    final_urls: List[str] = Field(..., min_items=1, max_items=20)
    
    # Assets
    assets: Dict[str, Any] = Field(default={})
    image_assets: List[AdAsset] = Field(default=[], max_items=20)
    video_assets: List[AdAsset] = Field(default=[], max_items=5)
    
    @validator('headlines')
    def validate_headlines(cls, v):
        """Validate headlines."""
        for headline in v:
            if len(headline) > 30:
                raise ValueError("Headlines must be 30 characters or less")
        return v
    
    @validator('descriptions')
    def validate_descriptions(cls, v):
        """Validate descriptions."""
        for description in v:
            if len(description) > 90:
                raise ValueError("Descriptions must be 90 characters or less")
        return v
    
    @validator('final_urls')
    def validate_urls(cls, v):
        """Validate URLs."""
        import re
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        for url in v:
            if not url_pattern.match(url):
                raise ValueError(f"Invalid URL: {url}")
        return v


class AdResponse(BaseSchema, TimestampMixin, UserContextMixin):
    """Response schema for ads."""
    id: UUID
    ad_group_id: UUID
    type: AdType
    status: AdStatus
    
    # Ad content
    headlines: List[str]
    descriptions: List[str]
    display_url: Optional[str] = None
    final_urls: List[str]
    
    # Assets
    assets: Dict[str, Any]
    image_assets: List[Dict[str, Any]]
    video_assets: List[Dict[str, Any]]
    
    # Performance tracking
    performance: Dict[str, Any]
    approval_status: Optional[str] = None
    policy_summary: Optional[str] = None
    
    # Google Ads integration
    google_ads_id: Optional[str] = None


# ================================================================
# AGENT SCHEMAS
# ================================================================

class AgentCapability(BaseSchema):
    """Agent capability schema."""
    name: str = Field(..., max_length=100)
    description: str = Field(..., max_length=500)
    enabled: bool = True
    parameters: Dict[str, Any] = Field(default={})


class AgentConfig(BaseSchema):
    """Agent configuration schema."""
    max_tasks_per_hour: int = Field(default=100, ge=1, le=1000)
    retry_attempts: int = Field(default=3, ge=1, le=10)
    timeout_seconds: int = Field(default=300, ge=30, le=3600)
    priority_threshold: TaskPriority = TaskPriority.NORMAL
    capabilities: List[AgentCapability] = Field(default=[])
    custom_settings: Dict[str, Any] = Field(default={})


class AgentCreateRequest(BaseSchema):
    """Request schema for creating agents."""
    name: str = Field(..., min_length=1, max_length=255)
    description: str = Field(..., min_length=1, max_length=2000)
    type: AgentType
    status: AgentStatus = AgentStatus.CREATED
    
    # Configuration
    config: AgentConfig = Field(default_factory=AgentConfig)
    capabilities: List[str] = Field(default=[], max_items=50)
    
    # Assignment
    campaign_id: Optional[UUID] = None
    user_id: Optional[UUID] = None
    team_id: Optional[UUID] = None
    
    # Version control
    version: str = Field(default="1.0.0", regex=r"^\d+\.\d+\.\d+$")


class AgentUpdateRequest(BaseSchema):
    """Request schema for updating agents."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, min_length=1, max_length=2000)
    status: Optional[AgentStatus] = None
    config: Optional[AgentConfig] = None
    campaign_id: Optional[UUID] = None


class AgentResponse(BaseSchema, TimestampMixin, UserContextMixin):
    """Response schema for agents."""
    id: UUID
    name: str
    description: str
    type: AgentType
    status: AgentStatus
    
    # Configuration
    config: Dict[str, Any]
    capabilities: List[str]
    
    # Assignment
    campaign_id: Optional[UUID] = None
    user_id: Optional[UUID] = None
    team_id: Optional[UUID] = None
    
    # Performance metrics
    tasks_completed: int
    tasks_failed: int
    average_execution_time: Optional[Decimal] = None
    success_rate: Optional[Decimal] = None
    
    # Status tracking
    last_activity: Optional[datetime] = None
    last_error: Optional[str] = None
    uptime_hours: Optional[Decimal] = None
    
    # Resource usage
    memory_usage_mb: Optional[Decimal] = None
    cpu_usage_percent: Optional[Decimal] = None
    
    # Version control
    version: str


# ================================================================
# TASK SCHEMAS
# ================================================================

class TaskCreateRequest(BaseSchema):
    """Request schema for creating agent tasks."""
    agent_id: UUID
    campaign_id: Optional[UUID] = None
    
    # Task details
    name: str = Field(..., min_length=1, max_length=255)
    description: str = Field(..., min_length=1, max_length=2000)
    type: str = Field(..., min_length=1, max_length=100)
    priority: TaskPriority = TaskPriority.NORMAL
    
    # Task data
    input_data: Dict[str, Any] = Field(default={})
    context: Dict[str, Any] = Field(default={})
    
    # Dependencies
    parent_task_id: Optional[UUID] = None
    dependent_task_ids: List[UUID] = Field(default=[], max_items=100)
    
    # Scheduling
    scheduled_at: Optional[datetime] = None
    deadline: Optional[datetime] = None
    
    @root_validator
    def validate_scheduling(cls, values):
        """Validate scheduling constraints."""
        scheduled_at = values.get('scheduled_at')
        deadline = values.get('deadline')
        
        if scheduled_at and deadline and scheduled_at >= deadline:
            raise ValueError("Deadline must be after scheduled time")
        
        return values


class TaskResponse(BaseSchema, TimestampMixin):
    """Response schema for agent tasks."""
    id: UUID
    agent_id: UUID
    campaign_id: Optional[UUID] = None
    
    # Task details
    name: str
    description: str
    type: str
    priority: TaskPriority
    status: TaskStatus
    
    # Task data
    input_data: Dict[str, Any]
    output_data: Optional[Dict[str, Any]] = None
    context: Dict[str, Any]
    
    # Execution details
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    execution_time_seconds: Optional[Decimal] = None
    retry_count: int
    
    # Results
    result: Optional[str] = None
    error_message: Optional[str] = None
    logs: List[Dict[str, Any]]
    
    # Dependencies
    parent_task_id: Optional[UUID] = None
    dependent_task_ids: List[UUID]
    
    # Scheduling
    scheduled_at: Optional[datetime] = None
    deadline: Optional[datetime] = None
    
    created_by: Optional[UUID] = None


# ================================================================
# PERFORMANCE METRICS SCHEMAS
# ================================================================

class MetricsRequest(BaseSchema):
    """Request schema for performance metrics."""
    campaign_id: UUID
    ad_group_id: Optional[UUID] = None
    ad_id: Optional[UUID] = None
    
    # Date range
    start_date: date
    end_date: date
    
    # Metrics to include
    metrics: List[str] = Field(default=[], max_items=50)
    
    # Grouping and aggregation
    group_by: List[str] = Field(default=[], max_items=10)
    aggregation: str = Field(default="sum", regex="^(sum|avg|min|max|count)$")
    
    @root_validator
    def validate_date_range(cls, values):
        """Validate date range."""
        start_date = values.get('start_date')
        end_date = values.get('end_date')
        
        if start_date and end_date and start_date > end_date:
            raise ValueError("Start date must be before or equal to end date")
        
        # Limit to 2 years of data
        if start_date and end_date:
            if (end_date - start_date).days > 730:
                raise ValueError("Date range cannot exceed 2 years")
        
        return values


class MetricsResponse(BaseSchema):
    """Response schema for performance metrics."""
    campaign_id: UUID
    ad_group_id: Optional[UUID] = None
    ad_id: Optional[UUID] = None
    date: date
    hour: Optional[int] = None
    
    # Core metrics
    impressions: int = 0
    clicks: int = 0
    conversions: Decimal = Decimal('0')
    cost: Decimal = Decimal('0')
    revenue: Optional[Decimal] = None
    
    # Calculated metrics
    ctr: Optional[Decimal] = None
    cpc: Optional[Decimal] = None
    cpm: Optional[Decimal] = None
    conversion_rate: Optional[Decimal] = None
    cost_per_conversion: Optional[Decimal] = None
    roas: Optional[Decimal] = None
    roi: Optional[Decimal] = None
    
    # Quality metrics
    quality_score: Optional[Decimal] = None
    
    # Additional metrics
    view_through_conversions: Decimal = Decimal('0')
    interaction_rate: Optional[Decimal] = None
    average_position: Optional[Decimal] = None


# ================================================================
# COMMON RESPONSE SCHEMAS
# ================================================================

class PaginationParams(BaseSchema):
    """Pagination parameters."""
    page: int = Field(default=1, ge=1, le=10000)
    page_size: int = Field(default=20, ge=1, le=1000)
    order_by: str = Field(default="created_at", max_length=50)
    order_direction: str = Field(default="desc", regex="^(asc|desc)$")


class PaginatedResponse(BaseSchema):
    """Paginated response wrapper."""
    items: List[Any]
    total: int = Field(..., ge=0)
    page: int = Field(..., ge=1)
    page_size: int = Field(..., ge=1)
    total_pages: int = Field(..., ge=0)
    has_next: bool
    has_previous: bool


class ErrorDetail(BaseSchema):
    """Error detail schema."""
    field: Optional[str] = None
    message: str
    code: Optional[str] = None


class ErrorResponse(BaseSchema):
    """Error response schema."""
    detail: str
    errors: Optional[List[ErrorDetail]] = None
    request_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class SuccessResponse(BaseSchema):
    """Success response schema."""
    message: str
    data: Optional[Any] = None
    request_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


# ================================================================
# GOOGLE ADS SPECIFIC VALIDATION SCHEMAS
# ================================================================

class GoogleAdsAdExtension(BaseSchema):
    """Google Ads ad extension schema."""
    type: constr(regex="^(sitelink|callout|structured_snippet|call|location|price|promotion|app)$") = Field(..., description="Extension type")
    headline: Optional[constr(min_length=1, max_length=25)] = Field(None, description="Extension headline")
    description1: Optional[constr(min_length=1, max_length=35)] = Field(None, description="First description line")
    description2: Optional[constr(min_length=1, max_length=35)] = Field(None, description="Second description line")
    final_url: Optional[str] = Field(None, description="Final URL")
    display_url: Optional[constr(min_length=1, max_length=35)] = Field(None, description="Display URL")
    
    @validator('final_url')
    def validate_final_url(cls, v):
        """Validate final URL format."""
        if v:
            import re
            url_pattern = re.compile(
                r'^https?://'  # http:// or https://
                r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\\.)+[A-Z]{2,6}\\.?|'  # domain...
                r'localhost|'  # localhost...
                r'\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})'  # ...or ip
                r'(?::\\d+)?'  # optional port
                r'(?:/?|[/?]\\S+)$', re.IGNORECASE)
            
            if not url_pattern.match(v):
                raise ValueError(f"Invalid URL format: {v}")
            
            # Check for prohibited parameters
            prohibited_params = ['gclid', 'utm_campaign', 'utm_source', 'utm_medium']
            for param in prohibited_params:
                if param in v.lower():
                    raise ValueError(f"URL cannot contain prohibited parameter: {param}")
        
        return v


class GoogleAdsQualityScoreFactors(BaseSchema):
    """Google Ads quality score factors."""
    expected_ctr: Optional[constr(regex="^(below_average|average|above_average)$")] = Field(
        None, 
        description="Expected click-through rate"
    )
    ad_relevance: Optional[constr(regex="^(below_average|average|above_average)$")] = Field(
        None,
        description="Ad relevance"
    )
    landing_page_experience: Optional[constr(regex="^(below_average|average|above_average)$")] = Field(
        None,
        description="Landing page experience"
    )


class GoogleAdsBudgetValidation(BaseSchema):
    """Google Ads budget validation schema."""
    
    @staticmethod
    def validate_budget_amount(amount: float, currency: str) -> bool:
        """Validate budget amount based on currency and Google Ads minimums."""
        min_budgets = {
            'USD': 1.00, 'EUR': 1.00, 'GBP': 0.80, 'CAD': 1.30, 'AUD': 1.40,
            'JPY': 100.00, 'CHF': 1.00, 'SEK': 10.00, 'NOK': 10.00, 'DKK': 7.00,
            'PLN': 4.00, 'CZK': 25.00, 'HUF': 300.00, 'BGN': 2.00, 'RON': 5.00,
        }
        
        min_amount = min_budgets.get(currency, 1.00)
        return amount >= min_amount
    
    @staticmethod
    def validate_bid_amount(bid: float, currency: str) -> bool:
        """Validate bid amount based on currency and Google Ads minimums."""
        min_bids = {
            'USD': 0.01, 'EUR': 0.01, 'GBP': 0.01, 'CAD': 0.01, 'AUD': 0.01,
            'JPY': 1.00, 'CHF': 0.01, 'SEK': 0.10, 'NOK': 0.10, 'DKK': 0.07,
            'PLN': 0.04, 'CZK': 0.25, 'HUF': 3.00, 'BGN': 0.02, 'RON': 0.05,
        }
        
        min_bid = min_bids.get(currency, 0.01)
        return bid >= min_bid


class GoogleAdsGeoTargetValidation(BaseSchema):
    """Google Ads geographic targeting validation."""
    
    @staticmethod
    def validate_location_criterion_id(location_id: int) -> bool:
        """Validate Google Ads location criterion ID format."""
        # Location IDs are typically 6-9 digits
        return 100000 <= location_id <= 999999999
    
    @staticmethod
    def validate_radius_targeting(radius: float, radius_unit: str) -> bool:
        """Validate radius targeting parameters."""
        if radius_unit.lower() not in ['miles', 'kilometers', 'km', 'mi']:
            return False
        
        # Google Ads radius limits
        if radius_unit.lower() in ['miles', 'mi']:
            return 0.1 <= radius <= 500
        else:  # kilometers
            return 0.16 <= radius <= 804.67


class GoogleAdsTextAdValidation(BaseSchema):
    """Google Ads text ad validation schema."""
    
    @staticmethod
    def validate_headline(headline: str) -> Dict[str, Any]:
        """Validate ad headline with Google Ads rules."""
        errors = []
        warnings = []
        
        # Length validation
        if len(headline) > 30:
            errors.append(f"Headline too long: {len(headline)} characters (max: 30)")
        
        # Character validation
        prohibited_chars = ['!', '?', '&', '<', '>', '"', "'", '%', '@', '#', '$', '^', '*', '(', ')', '=', '+', '[', ']', '{', '}', '\\', '|', ';', ':', '/', '~', '`']
        found_prohibited = [char for char in prohibited_chars if char in headline]
        if found_prohibited:
            errors.append(f"Headline contains prohibited characters: {', '.join(found_prohibited)}")
        
        # Content validation
        prohibited_words = ['click here', 'best', 'number one', '#1', 'guaranteed', 'free money', 'get rich quick']
        for word in prohibited_words:
            if word.lower() in headline.lower():
                warnings.append(f"Headline contains potentially prohibited phrase: '{word}'")
        
        # Capitalization check
        if headline.isupper() and len(headline) > 10:
            warnings.append("Avoid using all caps in headlines")
        
        # Repetitive text check
        words = headline.lower().split()
        if len(words) != len(set(words)):
            warnings.append("Avoid repetitive text in headlines")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings,
            'character_count': len(headline),
            'remaining_characters': 30 - len(headline)
        }
    
    @staticmethod
    def validate_description(description: str) -> Dict[str, Any]:
        """Validate ad description with Google Ads rules."""
        errors = []
        warnings = []
        
        # Length validation
        if len(description) > 90:
            errors.append(f"Description too long: {len(description)} characters (max: 90)")
        
        # Content validation similar to headlines
        prohibited_words = ['click here', 'click now', 'act now', 'limited time', 'hurry', 'don\'t wait']
        for word in prohibited_words:
            if word.lower() in description.lower():
                warnings.append(f"Description contains potentially problematic phrase: '{word}'")
        
        # Call-to-action validation
        cta_words = ['buy', 'shop', 'order', 'call', 'learn', 'discover', 'get', 'try', 'start']
        has_cta = any(word.lower() in description.lower() for word in cta_words)
        if not has_cta:
            warnings.append("Consider adding a call-to-action to improve performance")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings,
            'character_count': len(description),
            'remaining_characters': 90 - len(description)
        }


class GoogleAdsComplianceValidator(BaseSchema):
    """Google Ads compliance validation utilities."""
    
    @staticmethod
    def validate_landing_page(url: str) -> Dict[str, Any]:
        """Validate landing page compliance."""
        errors = []
        warnings = []
        
        # URL format validation
        import re
        if not re.match(r'^https?://', url):
            errors.append("Landing page must use HTTP or HTTPS protocol")
        
        # Prohibited domains (example list)
        prohibited_domains = ['bit.ly', 'tinyurl.com', 'goo.gl', 't.co', 'ow.ly']
        domain = re.search(r'://([^/]+)', url)
        if domain and any(prohibited in domain.group(1) for prohibited in prohibited_domains):
            errors.append("URL shorteners are not allowed as landing pages")
        
        # Check for tracking parameters
        tracking_params = ['utm_source', 'utm_medium', 'utm_campaign', 'gclid']
        for param in tracking_params:
            if param in url:
                warnings.append(f"Landing page contains tracking parameter: {param}")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings,
            'url': url
        }
    
    @staticmethod
    def validate_keyword_compliance(keyword: str) -> Dict[str, Any]:
        """Validate keyword compliance with Google Ads policies."""
        errors = []
        warnings = []
        
        # Trademark validation (basic check)
        trademark_indicators = ['®', '™', '©']
        for indicator in trademark_indicators:
            if indicator in keyword:
                errors.append(f"Keyword contains trademark symbol: {indicator}")
        
        # Competitive terms validation
        competitive_terms = ['vs', 'versus', 'compared to', 'alternative to', 'better than']
        for term in competitive_terms:
            if term.lower() in keyword.lower():
                warnings.append(f"Keyword contains competitive term: '{term}' - ensure compliance")
        
        # Brand terms validation
        major_brands = ['google', 'facebook', 'amazon', 'apple', 'microsoft', 'nike', 'coca-cola']
        for brand in major_brands:
            if brand.lower() in keyword.lower():
                warnings.append(f"Keyword contains brand term: '{brand}' - verify trademark compliance")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings,
            'keyword': keyword
        }


class GoogleAdsValidationRequest(BaseSchema):
    """Request schema for Google Ads specific validation."""
    campaign_data: Dict[str, Any] = Field(..., description="Campaign data to validate")
    validation_type: constr(regex="^(campaign|ad_group|ad|keyword|extension|budget)$") = Field(
        ..., 
        description="Type of validation to perform"
    )
    strict_mode: bool = Field(default=True, description="Enable strict validation mode")
    google_ads_account_id: Optional[str] = Field(None, description="Google Ads account ID")
    
    @validator('google_ads_account_id')
    def validate_account_id(cls, v):
        """Validate Google Ads account ID format."""
        if v:
            # Remove dashes and validate format
            account_id = v.replace('-', '')
            if not (account_id.isdigit() and len(account_id) == 10):
                raise ValueError("Google Ads account ID must be 10 digits")
        return v


class GoogleAdsValidationResponse(BaseSchema):
    """Response schema for Google Ads validation."""
    valid: bool = Field(..., description="Whether the data is valid")
    errors: List[Dict[str, Any]] = Field(default=[], description="Validation errors")
    warnings: List[Dict[str, Any]] = Field(default=[], description="Validation warnings")
    suggestions: List[Dict[str, Any]] = Field(default=[], description="Improvement suggestions")
    compliance_score: Optional[confloat(ge=0.0, le=100.0)] = Field(None, description="Compliance score (0-100)")
    validation_timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# ================================================================
# BULK OPERATION SCHEMAS
# ================================================================

class BulkOperationRequest(BaseSchema):
    """Request schema for bulk operations."""
    operation: str = Field(..., regex="^(create|update|delete)$")
    items: List[Dict[str, Any]] = Field(..., min_items=1, max_items=1000)
    validate_only: bool = False
    continue_on_error: bool = False


class BulkOperationResult(BaseSchema):
    """Result for a single bulk operation item."""
    index: int
    success: bool
    id: Optional[UUID] = None
    error: Optional[str] = None


class BulkOperationResponse(BaseSchema):
    """Response schema for bulk operations."""
    total_items: int
    successful_items: int
    failed_items: int
    results: List[BulkOperationResult]
    request_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)