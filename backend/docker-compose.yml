version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ailex-postgres
    environment:
      POSTGRES_DB: ailex_db
      POSTGRES_USER: ailex_user
      POSTGRES_PASSWORD: ailex_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/migrations:/docker-entrypoint-initdb.d
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ailex_user -d ailex_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    networks:
      - ailex-network

  # Redis for caching and task queue
  redis:
    image: redis:7-alpine
    container_name: ailex-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s
    networks:
      - ailex-network

  # FastAPI Backend
  backend:
    build: 
      context: .
      target: production
    container_name: ailex-backend
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=****************************************************/ailex_db
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - CELERY_BROKER_URL=redis://:redis_password@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:redis_password@redis:6379/2
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=true
      - LOG_LEVEL=INFO
    volumes:
      - .:/app
      - /app/__pycache__
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health/liveness"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ailex-network

  # Celery Worker for background tasks
  celery-worker:
    build: 
      context: .
      target: production
    container_name: ailex-celery-worker
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=****************************************************/ailex_db
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - CELERY_BROKER_URL=redis://:redis_password@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:redis_password@redis:6379/2
    volumes:
      - .:/app
      - celery_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      backend:
        condition: service_healthy
    restart: unless-stopped
    command: celery -A main.celery worker --loglevel=info --concurrency=4
    healthcheck:
      test: ["CMD", "celery", "-A", "main.celery", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ailex-network

  # Celery Beat for scheduled tasks
  celery-beat:
    build: 
      context: .
      target: production
    container_name: ailex-celery-beat
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=****************************************************/ailex_db
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - CELERY_BROKER_URL=redis://:redis_password@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:redis_password@redis:6379/2
    volumes:
      - .:/app
      - celery_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      backend:
        condition: service_healthy
    restart: unless-stopped
    command: celery -A main.celery beat --loglevel=info
    healthcheck:
      test: ["CMD-SHELL", "ps aux | grep '[c]elery beat' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ailex-network

  # Nginx reverse proxy (optional, for production-like setup)
  nginx:
    image: nginx:alpine
    container_name: ailex-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - ailex-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  celery_logs:
    driver: local

networks:
  ailex-network:
    driver: bridge