"""
AiLex Ad Agent System - FastAPI Backend
Main application entry point with CORS, middleware, and routing configuration.
"""

import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import sentry_sdk
import structlog
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.starlette import StarletteIntegration

from api.campaigns import router as campaigns_router
from api.agents import router as agents_router
from api.analytics import router as analytics_router
from api.health import router as health_router
from api.google_ads import router as google_ads_router
from api.auth import router as auth_router
from utils.config import settings
from utils.logging import configure_logging
from utils.exceptions import (
    CustomException,
    custom_exception_handler,
    validation_exception_handler,
)
from middleware.integration import middleware_manager, setup_exception_handlers
from services.redis_service import RedisService


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan management.
    Handles startup and shutdown events.
    """
    # Startup
    logger = structlog.get_logger()
    logger.info("Starting AiLex Ad Agent System", version=settings.VERSION)
    
    # Initialize Redis service for caching and rate limiting
    redis_service = None
    try:
        redis_service = RedisService()
        await redis_service.authenticate()
        logger.info("Redis service initialized successfully")
    except Exception as e:
        logger.warning("Redis service initialization failed", error=str(e))
    
    # Store Redis service in app state for middleware access
    app.state.redis_service = redis_service
    logger.info("Redis service stored in app state")
    
    # Initialize external services
    if settings.ENVIRONMENT == "production":
        logger.info("Initializing production services")
        # Add production-specific initialization here
        
    yield
    
    # Shutdown
    logger.info("Shutting down AiLex Ad Agent System")


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        FastAPI: Configured FastAPI application instance
    """
    # Configure logging
    configure_logging(settings.LOG_LEVEL, settings.ENVIRONMENT)
    
    # Initialize Sentry for error tracking
    if settings.SENTRY_DSN:
        sentry_sdk.init(
            dsn=settings.SENTRY_DSN,
            integrations=[
                FastApiIntegration(auto_enabling=True),
                StarletteIntegration(auto_enabling=True),
            ],
            environment=settings.ENVIRONMENT,
            traces_sample_rate=settings.SENTRY_TRACES_SAMPLE_RATE,
            profiles_sample_rate=settings.SENTRY_PROFILES_SAMPLE_RATE,
        )
    
    # Create FastAPI application with comprehensive documentation
    app = FastAPI(
        title="Google Ads AI Agent System",
        description="""
        **AI-powered Google Ads campaign management system with autonomous optimization**
        
        This API provides comprehensive campaign management, AI agent orchestration, and 
        performance analytics for Google Ads campaigns with autonomous optimization capabilities.
        
        ## Features
        
        * **Campaign Management**: Create, update, and manage Google Ads campaigns
        * **AI Agent Orchestration**: Deploy and manage AI agents for campaign optimization
        * **Performance Analytics**: Real-time metrics, insights, and reporting
        * **Autonomous Optimization**: AI-driven campaign optimization and recommendations
        * **Health Monitoring**: Comprehensive system health checks and monitoring
        
        ## Authentication
        
        This API uses service-to-service authentication for Google Ads API integration.
        Contact your system administrator for access credentials.
        
        ## Rate Limits
        
        API requests are rate limited to ensure system stability:
        - Database operations: 2000 requests/minute
        - Google Ads API: Per Google's rate limits
        - Analytics queries: 100 requests/minute
        
        ## Support
        
        For technical support, please contact the development team or check the health endpoints
        for system status information.
        """,
        version=settings.VERSION,
        contact={
            "name": "Google Ads AI Agent System Support",
            "email": "<EMAIL>",
        },
        license_info={
            "name": "Proprietary",
        },
        docs_url="/docs" if settings.ENVIRONMENT != "production" else None,
        redoc_url="/redoc" if settings.ENVIRONMENT != "production" else None,
        openapi_url="/openapi.json" if settings.ENVIRONMENT != "production" else None,
        lifespan=lifespan,
        servers=[
            {
                "url": "http://localhost:8000",
                "description": "Development server"
            },
            {
                "url": "https://api.staging.googleads-ai.com",
                "description": "Staging server"
            },
            {
                "url": "https://api.googleads-ai.com",
                "description": "Production server"
            }
        ],
        tags_metadata=[
            {
                "name": "health",
                "description": "System health monitoring and diagnostics"
            },
            {
                "name": "campaigns",
                "description": "Google Ads campaign management operations"
            },
            {
                "name": "agents",
                "description": "AI agent lifecycle and task management"
            },
            {
                "name": "analytics",
                "description": "Performance metrics, insights, and reporting"
            },
            {
                "name": "google-ads",
                "description": "Google Ads API integration, synchronization, and direct operations"
            },
            {
                "name": "authentication",
                "description": "User authentication, registration, and session management"
            }
        ]
    )
    
    # Temporarily disable complex middleware setup to resolve deployment issues
    # TODO: Re-enable once middleware interface issues are resolved
    logger = structlog.get_logger()
    logger.info("Complex middleware setup temporarily disabled for deployment")
    
    # Custom middleware
    @app.middleware("http")
    async def add_request_id_header(request: Request, call_next) -> Response:
        """Add unique request ID to all responses."""
        import uuid
        request_id = str(uuid.uuid4())
        
        # Add request ID to structlog context
        structlog.contextvars.clear_contextvars()
        structlog.contextvars.bind_contextvars(request_id=request_id)
        
        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id
        return response
    
    @app.middleware("http")
    async def log_requests(request: Request, call_next) -> Response:
        """Log all incoming requests."""
        logger = structlog.get_logger()
        
        start_time = time.time()
        
        logger.info(
            "Request started",
            method=request.method,
            url=str(request.url),
            client_ip=request.client.host if request.client else None,
        )
        
        response = await call_next(request)
        
        process_time = time.time() - start_time
        
        logger.info(
            "Request completed",
            method=request.method,
            url=str(request.url),
            status_code=response.status_code,
            process_time=round(process_time, 4),
        )
        
        return response
    
    # Exception handlers - setup comprehensive middleware exception handlers
    setup_exception_handlers(app)
    
    # Additional application-specific exception handlers
    app.add_exception_handler(CustomException, custom_exception_handler)
    app.add_exception_handler(422, validation_exception_handler)
    
    # Include routers
    app.include_router(
        health_router,
        prefix="/api/v1/health",
        tags=["health"],
    )
    
    app.include_router(
        campaigns_router,
        prefix="/api/v1/campaigns",
        tags=["campaigns"],
    )
    
    app.include_router(
        agents_router,
        prefix="/api/v1/agents",
        tags=["agents"],
    )
    
    app.include_router(
        analytics_router,
        prefix="/api/v1/analytics",
        tags=["analytics"],
    )
    
    app.include_router(
        google_ads_router,
        prefix="/api/v1/google-ads",
        tags=["google-ads"],
    )
    
    app.include_router(
        auth_router,
        prefix="/api/v1/auth",
        tags=["authentication"],
    )
    
    return app


# Import time here to avoid circular import issues
import time

# Create the application instance
app = create_app()


@app.get("/", include_in_schema=False)
async def root() -> dict:
    """
    Root endpoint returning basic API information.
    
    Returns:
        dict: API information and status
    """
    return {
        "name": "AiLex Ad Agent System",
        "version": settings.VERSION,
        "status": "operational",
        "environment": settings.ENVIRONMENT,
        "docs": "/docs" if settings.ENVIRONMENT != "production" else "disabled",
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.ENVIRONMENT == "development",
        log_config=None,  # Use our custom logging
    )