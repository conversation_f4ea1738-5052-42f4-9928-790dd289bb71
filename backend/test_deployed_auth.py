#!/usr/bin/env python3
"""
Test authentication on the deployed backend.
"""

import asyncio
import aiohttp
import json
import random
import string
from datetime import datetime


BASE_URL = "https://ailex-ad-agent-backend.fly.dev/api/v1"


def generate_test_email():
    """Generate a unique test email."""
    random_string = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    return f"test_{random_string}@example.com"


async def test_health_check():
    """Test if the API is accessible."""
    print("🏥 Testing API Health")
    print("=" * 40)
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/health/liveness") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ API is alive: {data.get('status')}")
                    return True
                else:
                    print(f"❌ API health check failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Cannot reach API: {e}")
            return False


async def test_signup(email, password):
    """Test user signup via API."""
    print("\n📝 Testing User Signup")
    print("=" * 40)
    print(f"Email: {email}")
    
    async with aiohttp.ClientSession() as session:
        try:
            payload = {
                "email": email,
                "password": password,
                "full_name": "Test User",
                "company": "Test Company"
            }
            
            async with session.post(
                f"{BASE_URL}/auth/sign-up",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                data = await response.json()
                
                if response.status in [200, 201]:
                    print(f"✅ Signup successful!")
                    if "user" in data:
                        print(f"   User ID: {data['user'].get('id')}")
                    if "access_token" in data:
                        print(f"   Token received: {data['access_token'][:20]}...")
                    return data.get("access_token")
                else:
                    error = data.get("detail", data.get("error", "Unknown error"))
                    print(f"❌ Signup failed ({response.status}): {error}")
                    return None
                    
        except Exception as e:
            print(f"❌ Signup error: {e}")
            return None


async def test_login(email, password):
    """Test user login via API."""
    print("\n🔐 Testing User Login")
    print("=" * 40)
    print(f"Email: {email}")
    
    async with aiohttp.ClientSession() as session:
        try:
            payload = {
                "email": email,
                "password": password
            }
            
            async with session.post(
                f"{BASE_URL}/auth/sign-in",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                data = await response.json()
                
                if response.status == 200:
                    print(f"✅ Login successful!")
                    if "user" in data:
                        print(f"   User ID: {data['user'].get('id')}")
                    if "access_token" in data:
                        print(f"   Token received: {data['access_token'][:20]}...")
                    return data.get("access_token")
                else:
                    error = data.get("detail", data.get("error", "Unknown error"))
                    print(f"❌ Login failed ({response.status}): {error}")
                    return None
                    
        except Exception as e:
            print(f"❌ Login error: {e}")
            return None


async def test_protected_endpoint(token):
    """Test accessing a protected endpoint."""
    print("\n🔒 Testing Protected Endpoint")
    print("=" * 40)
    
    if not token:
        print("⚠️  No token available, skipping test")
        return False
    
    async with aiohttp.ClientSession() as session:
        try:
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            async with session.get(
                f"{BASE_URL}/auth/user",
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Access granted!")
                    print(f"   User: {data.get('email')}")
                    print(f"   ID: {data.get('id')}")
                    return True
                elif response.status == 401:
                    print(f"❌ Unauthorized (token invalid or expired)")
                    return False
                else:
                    print(f"❌ Failed ({response.status})")
                    return False
                    
        except Exception as e:
            print(f"❌ Protected endpoint error: {e}")
            return False


async def test_logout(token):
    """Test user logout."""
    print("\n🚪 Testing Logout")
    print("=" * 40)
    
    if not token:
        print("⚠️  No token available, skipping test")
        return False
    
    async with aiohttp.ClientSession() as session:
        try:
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            async with session.post(
                f"{BASE_URL}/auth/sign-out",
                headers=headers
            ) as response:
                if response.status == 200:
                    print(f"✅ Logout successful!")
                    return True
                else:
                    data = await response.json()
                    error = data.get("detail", "Unknown error")
                    print(f"❌ Logout failed ({response.status}): {error}")
                    return False
                    
        except Exception as e:
            print(f"❌ Logout error: {e}")
            return False


async def run_tests():
    """Run all authentication tests against deployed API."""
    print("🚀 Testing Deployed Authentication System")
    print(f"🌐 API URL: {BASE_URL}")
    print("=" * 60)
    
    # Test health check
    if not await test_health_check():
        print("\n❌ API is not accessible. Stopping tests.")
        return
    
    # Generate test credentials
    test_email = generate_test_email()
    test_password = "TestPassword123!"
    
    # Test signup
    token = await test_signup(test_email, test_password)
    
    # If signup failed, try with a known test account
    if not token:
        print("\n🔄 Trying with existing test account...")
        test_email = "<EMAIL>"
        test_password = "TestPassword123!"
        token = await test_login(test_email, test_password)
    
    # Test protected endpoint
    if token:
        await test_protected_endpoint(token)
        await test_logout(token)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    if token:
        print("✅ Authentication system is working!")
        print("   • User registration/login functional")
        print("   • JWT tokens being issued")
        print("   • Protected endpoints accessible")
    else:
        print("⚠️  Authentication needs configuration")
        print("   • Check Supabase keys in Fly.io secrets")
        print("   • Verify SUPABASE_ANON_KEY is set")
        print("   • Ensure database is properly configured")
    
    print("\n📝 Configuration Commands:")
    print("fly secrets set SUPABASE_URL='your-supabase-url'")
    print("fly secrets set SUPABASE_ANON_KEY='your-anon-key'")
    print("fly secrets set SUPABASE_SERVICE_ROLE_KEY='your-service-key'")


if __name__ == "__main__":
    asyncio.run(run_tests())