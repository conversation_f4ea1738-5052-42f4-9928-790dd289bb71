# AiLex Ad Agent System - Production Environment Configuration
# WARNING: This file contains sensitive information. Never commit actual secrets to version control.

# Application Settings
APP_NAME="AiLex Ad Agent System"
VERSION="1.0.0"
ENVIRONMENT="production"
DEBUG=false
HOST="0.0.0.0"
PORT=8000

# CORS Settings (restrict for production)
CORS_ORIGINS=""  # Set in Fly.io secrets

# Trusted Hosts
TRUSTED_HOSTS=""  # Set in Fly.io secrets

# Database Settings (Supabase)
SUPABASE_URL=""  # Set in Fly.io secrets
SUPABASE_ANON_KEY=""  # Set in Fly.io secrets  
SUPABASE_SERVICE_ROLE_KEY=""  # Set in Fly.io secrets

# Legacy Database Settings (for backward compatibility)
DATABASE_URL=""  # Set in Fly.io secrets (Supabase PostgreSQL URL)
DATABASE_KEY=""  # Set in Fly.io secrets (same as SUPABASE_SERVICE_ROLE_KEY)

# Authentication Settings  
SECRET_KEY=""  # Set in Fly.io secrets

# Email Service (Resend)
RESEND_API_KEY=""  # Set in Fly.io secrets
FROM_EMAIL=""  # Set in Fly.io secrets (<EMAIL>)
FROM_NAME="AiLex Ad Agent System"

# Redis Settings (Fly.io Redis)
REDIS_URL=""  # Set automatically by Fly.io Redis attachment

# Google Ads API Settings
GOOGLE_ADS_DEVELOPER_TOKEN=""  # Set in Fly.io secrets
GOOGLE_ADS_CLIENT_ID=""  # Set in Fly.io secrets
GOOGLE_ADS_CLIENT_SECRET=""  # Set in Fly.io secrets
GOOGLE_ADS_REFRESH_TOKEN=""  # Set in Fly.io secrets
GOOGLE_ADS_CUSTOMER_ID=""  # Set in Fly.io secrets

# Google Analytics Settings
GOOGLE_ANALYTICS_PROPERTY_ID=""  # Set in Fly.io secrets
GOOGLE_ANALYTICS_CREDENTIALS_PATH="/app/credentials/google-analytics.json"

# OpenAI Settings
OPENAI_API_KEY=""  # Set in Fly.io secrets
OPENAI_MODEL="gpt-4o"

# Google Gemini Settings
GEMINI_API_KEY=""  # Set in Fly.io secrets

# Pinecone Settings
PINECONE_API_KEY=""  # Set in Fly.io secrets
PINECONE_ENVIRONMENT=""  # Set in Fly.io secrets
PINECONE_INDEX_NAME="ailex-memory-prod"

# Celery Settings
CELERY_BROKER_URL=""  # Set automatically by Redis attachment
CELERY_RESULT_BACKEND=""  # Set automatically by Redis attachment

# Logging Settings
LOG_LEVEL="INFO"

# Sentry Settings (Error Tracking)
SENTRY_DSN=""  # Set in Fly.io secrets
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_PROFILES_SAMPLE_RATE=0.1

# Phoenix Tracing Settings
PHOENIX_COLLECTOR_ENDPOINT=""  # Set in Fly.io secrets if using
PHOENIX_PROJECT_NAME="ailex-ad-agents-prod"

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=200

# Campaign Optimization Settings
MAX_BID_ADJUSTMENT_PERCENT=15.0
MAX_BUDGET_ADJUSTMENT_PERCENT_PER_HOUR=3.0
OPTIMIZATION_INTERVAL_MINUTES=15

# A/B Testing Settings
MIN_SAMPLE_SIZE=500
STATISTICAL_SIGNIFICANCE_THRESHOLD=0.05

# GDPR and Compliance Settings
GDPR_ENABLED=true
DATA_RETENTION_DAYS=365

# Webhooks and Notifications
SLACK_WEBHOOK_URL=""  # Set in Fly.io secrets if using