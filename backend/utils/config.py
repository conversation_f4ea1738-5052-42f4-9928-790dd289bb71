"""
Configuration management using Pydantic Settings.
Handles environment variables and application settings.
"""

from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.
    """
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore"
    )
    
    # Application settings
    APP_NAME: str = "AiLex Ad Agent System"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = Field(default="development", description="Environment: development, staging, production")
    DEBUG: bool = Field(default=False, description="Enable debug mode")
    
    # Server settings
    HOST: str = Field(default="0.0.0.0", description="Server host")
    PORT: int = Field(default=8000, description="Server port")
    
    # CORS settings
    CORS_ORIGINS: List[str] = Field(
        default=[
            "http://localhost:3000",
            "http://localhost:3001",
            "https://localhost:3000",
            "https://localhost:3001",
        ],
        description="Allowed CORS origins"
    )
    
    # Trusted hosts for security
    TRUSTED_HOSTS: Optional[List[str]] = Field(
        default=None,
        description="Trusted hosts for security middleware"
    )
    
    # Supabase Database settings
    SUPABASE_URL: Optional[str] = Field(
        default=None,
        description="Supabase project URL"
    )
    SUPABASE_KEY: Optional[str] = Field(
        default=None,
        description="Supabase service role key for backend operations"
    )
    SUPABASE_ANON_KEY: Optional[str] = Field(
        default=None,
        description="Supabase anonymous/public key"
    )
    SUPABASE_SERVICE_ROLE_KEY: Optional[str] = Field(
        default=None,
        description="Supabase service role key for backend operations"
    )
    
    # Database connection pooling settings
    DATABASE_POOL_SIZE: int = Field(
        default=20,
        description="Database connection pool size"
    )
    DATABASE_MAX_OVERFLOW: int = Field(
        default=30,
        description="Maximum overflow connections"
    )
    DATABASE_POOL_TIMEOUT: int = Field(
        default=30,
        description="Database connection timeout in seconds"
    )
    DATABASE_POOL_RECYCLE: int = Field(
        default=3600,
        description="Connection recycling time in seconds"
    )
    
    # Legacy database settings (for backward compatibility)
    DATABASE_URL: Optional[str] = Field(
        default=None,
        description="Legacy: Supabase database URL (use SUPABASE_URL instead)"
    )
    DATABASE_KEY: Optional[str] = Field(
        default=None,
        description="Legacy: Supabase service key (use SUPABASE_SERVICE_ROLE_KEY instead)"
    )
    
    # Redis settings
    REDIS_URL: str = Field(
        default="redis://localhost:6379/0",
        description="Redis connection URL"
    )
    
    # Google Ads API settings
    GOOGLE_ADS_DEVELOPER_TOKEN: Optional[str] = Field(
        default=None,
        description="Google Ads Developer Token"
    )
    GOOGLE_ADS_CLIENT_ID: Optional[str] = Field(
        default=None,
        description="Google Ads OAuth2 Client ID"
    )
    GOOGLE_ADS_CLIENT_SECRET: Optional[str] = Field(
        default=None,
        description="Google Ads OAuth2 Client Secret"
    )
    GOOGLE_ADS_REFRESH_TOKEN: Optional[str] = Field(
        default=None,
        description="Google Ads OAuth2 Refresh Token"
    )
    GOOGLE_ADS_CUSTOMER_ID: Optional[str] = Field(
        default=None,
        description="Google Ads Customer ID (without dashes)"
    )
    
    # Google Analytics settings
    GOOGLE_ANALYTICS_PROPERTY_ID: Optional[str] = Field(
        default=None,
        description="Google Analytics 4 Property ID"
    )
    GOOGLE_ANALYTICS_CREDENTIALS_PATH: Optional[str] = Field(
        default=None,
        description="Path to Google Analytics service account credentials"
    )
    
    # OpenAI settings
    OPENAI_API_KEY: Optional[str] = Field(
        default=None,
        description="OpenAI API key for GPT models"
    )
    OPENAI_MODEL: str = Field(
        default="gpt-4o",
        description="Default OpenAI model to use"
    )
    
    # Google Gemini settings
    GEMINI_API_KEY: Optional[str] = Field(
        default=None,
        description="Google Gemini API key"
    )
    
    # Pinecone settings
    PINECONE_API_KEY: Optional[str] = Field(
        default=None,
        description="Pinecone API key for vector database"
    )
    PINECONE_ENVIRONMENT: Optional[str] = Field(
        default=None,
        description="Pinecone environment"
    )
    PINECONE_INDEX_NAME: str = Field(
        default="ailex-memory",
        description="Pinecone index name"
    )
    
    # Celery settings
    CELERY_BROKER_URL: Optional[str] = Field(
        default=None,
        description="Celery broker URL (Redis)"
    )
    CELERY_RESULT_BACKEND: Optional[str] = Field(
        default=None,
        description="Celery result backend URL"
    )
    
    # Logging settings
    LOG_LEVEL: str = Field(
        default="INFO",
        description="Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL"
    )
    
    # Sentry settings for error tracking
    SENTRY_DSN: Optional[str] = Field(
        default=None,
        description="Sentry DSN for error tracking"
    )
    SENTRY_TRACES_SAMPLE_RATE: float = Field(
        default=0.1,
        description="Sentry traces sample rate (0.0 to 1.0)"
    )
    SENTRY_PROFILES_SAMPLE_RATE: float = Field(
        default=0.1,
        description="Sentry profiles sample rate (0.0 to 1.0)"
    )
    
    # Phoenix Tracing settings
    PHOENIX_COLLECTOR_ENDPOINT: Optional[str] = Field(
        default=None,
        description="Phoenix collector endpoint for tracing"
    )
    PHOENIX_PROJECT_NAME: str = Field(
        default="ailex-ad-agents",
        description="Phoenix project name"
    )
    
    # Rate limiting settings
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = Field(
        default=100,
        description="Rate limit: requests per minute per IP"
    )
    
    # Campaign optimization settings
    MAX_BID_ADJUSTMENT_PERCENT: float = Field(
        default=20.0,
        description="Maximum bid adjustment percentage"
    )
    MAX_BUDGET_ADJUSTMENT_PERCENT_PER_HOUR: float = Field(
        default=5.0,
        description="Maximum budget adjustment percentage per hour"
    )
    OPTIMIZATION_INTERVAL_MINUTES: int = Field(
        default=10,
        description="Optimization interval in minutes"
    )
    
    # A/B Testing settings
    MIN_SAMPLE_SIZE: int = Field(
        default=100,
        description="Minimum sample size for A/B tests"
    )
    STATISTICAL_SIGNIFICANCE_THRESHOLD: float = Field(
        default=0.05,
        description="P-value threshold for statistical significance"
    )
    
    # GDPR and Compliance settings
    GDPR_ENABLED: bool = Field(
        default=True,
        description="Enable GDPR compliance features"
    )
    DATA_RETENTION_DAYS: int = Field(
        default=365,
        description="Data retention period in days"
    )
    
    # Security settings
    SECRET_KEY: Optional[str] = Field(
        default=None,
        description="Secret key for session middleware and other security features"
    )
    
    # Supabase Authentication settings
    SUPABASE_URL: Optional[str] = Field(
        default=None,
        description="Supabase project URL"
    )
    SUPABASE_SERVICE_ROLE_KEY: Optional[str] = Field(
        default=None,
        description="Supabase service role key for backend authentication"
    )
    SUPABASE_ANON_KEY: Optional[str] = Field(
        default=None,
        description="Supabase anonymous key for frontend"
    )
    
    # Email Service (Resend) settings
    RESEND_API_KEY: Optional[str] = Field(
        default=None,
        description="Resend API key for email service"
    )
    FROM_EMAIL: str = Field(
        default="<EMAIL>",
        description="Default from email address"
    )
    FROM_NAME: str = Field(
        default="AiLex Ad Agent System",
        description="Default from name for emails"
    )
    
    # Webhooks and notifications
    SLACK_WEBHOOK_URL: Optional[str] = Field(
        default=None,
        description="Slack webhook URL for notifications"
    )
    
    @validator("ENVIRONMENT")
    def validate_environment(cls, v):
        """Validate environment setting."""
        allowed_environments = ["development", "staging", "production"]
        if v not in allowed_environments:
            raise ValueError(f"Environment must be one of: {allowed_environments}")
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        """Validate log level setting."""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"Log level must be one of: {allowed_levels}")
        return v.upper()
    
    @validator("CORS_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("TRUSTED_HOSTS", pre=True)
    def parse_trusted_hosts(cls, v):
        """Parse trusted hosts from string or list."""
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @property
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.ENVIRONMENT == "development"
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return self.ENVIRONMENT == "production"
    
    @property
    def is_staging(self) -> bool:
        """Check if running in staging mode."""
        return self.ENVIRONMENT == "staging"
    
    @property
    def database_url(self) -> Optional[str]:
        """Get the database URL, preferring new SUPABASE_URL over legacy DATABASE_URL."""
        return self.SUPABASE_URL or self.DATABASE_URL
    
    @property
    def database_service_key(self) -> Optional[str]:
        """Get the database service key, preferring new SUPABASE_SERVICE_ROLE_KEY over SUPABASE_KEY over legacy DATABASE_KEY."""
        return self.SUPABASE_SERVICE_ROLE_KEY or self.SUPABASE_KEY or self.DATABASE_KEY


# Global settings instance
settings = Settings()