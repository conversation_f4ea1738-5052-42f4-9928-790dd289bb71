"""
Custom exception classes and error handling utilities.
Provides structured error handling across the application.
"""

from typing import Any, Dict, Optional
from fastapi import Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
import structlog


class CustomException(Exception):
    """
    Base custom exception class with structured error information.
    """
    
    def __init__(
        self,
        message: str,
        error_code: str = "GENERIC_ERROR",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(CustomException):
    """Exception for validation errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details,
        )


class AuthenticationException(CustomException):
    """Exception for authentication errors."""
    
    def __init__(self, message: str = "Authentication failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=status.HTTP_401_UNAUTHORIZED,
            details=details,
        )


class AuthorizationException(CustomException):
    """Exception for authorization errors."""
    
    def __init__(self, message: str = "Access denied", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=status.HTTP_403_FORBIDDEN,
            details=details,
        )


class NotFoundException(CustomException):
    """Exception for resource not found errors."""
    
    def __init__(self, resource: str, identifier: str, details: Optional[Dict[str, Any]] = None):
        message = f"{resource} with identifier '{identifier}' not found"
        super().__init__(
            message=message,
            error_code="NOT_FOUND",
            status_code=status.HTTP_404_NOT_FOUND,
            details=details,
        )


class ConflictException(CustomException):
    """Exception for resource conflict errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="CONFLICT",
            status_code=status.HTTP_409_CONFLICT,
            details=details,
        )


class RateLimitException(CustomException):
    """Exception for rate limiting errors."""
    
    def __init__(self, message: str = "Rate limit exceeded", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_EXCEEDED",
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            details=details,
        )


class ExternalServiceException(CustomException):
    """Exception for external service errors."""
    
    def __init__(
        self,
        service_name: str,
        message: str,
        status_code: int = status.HTTP_502_BAD_GATEWAY,
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=f"{service_name}: {message}",
            error_code="EXTERNAL_SERVICE_ERROR",
            status_code=status_code,
            details={**(details or {}), "service": service_name},
        )


class GoogleAdsException(ExternalServiceException):
    """Exception for Google Ads API errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            service_name="Google Ads API",
            message=message,
            details=details,
        )


class OpenAIException(ExternalServiceException):
    """Exception for OpenAI API errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            service_name="OpenAI API",
            message=message,
            details=details,
        )


class DatabaseException(CustomException):
    """Exception for database errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details,
        )


class CampaignException(CustomException):
    """Exception for campaign-related errors."""
    
    def __init__(
        self,
        message: str,
        campaign_id: Optional[str] = None,
        error_code: str = "CAMPAIGN_ERROR",
        status_code: int = status.HTTP_400_BAD_REQUEST,
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status_code,
            details={**(details or {}), "campaign_id": campaign_id} if campaign_id else details,
        )


class AgentException(CustomException):
    """Exception for AI agent errors."""
    
    def __init__(
        self,
        message: str,
        agent_name: Optional[str] = None,
        error_code: str = "AGENT_ERROR",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details={**(details or {}), "agent_name": agent_name} if agent_name else details,
        )


class EmailServiceException(ExternalServiceException):
    """Exception for email service errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            service_name="Email Service",
            message=message,
            details=details,
        )


async def custom_exception_handler(request: Request, exc: CustomException) -> JSONResponse:
    """
    Handle custom exceptions and return structured error responses.
    
    Args:
        request: FastAPI request object
        exc: Custom exception instance
        
    Returns:
        JSONResponse: Structured error response
    """
    logger = structlog.get_logger()
    
    # Log the error
    logger.error(
        "Custom exception occurred",
        error_code=exc.error_code,
        message=exc.message,
        status_code=exc.status_code,
        details=exc.details,
        path=request.url.path,
        method=request.method,
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": exc.error_code,
                "message": exc.message,
                "details": exc.details,
            },
            "request_id": request.headers.get("X-Request-ID"),
        },
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """
    Handle validation exceptions and return structured error responses.
    
    Args:
        request: FastAPI request object
        exc: Validation exception instance
        
    Returns:
        JSONResponse: Structured error response
    """
    logger = structlog.get_logger()
    
    # Format validation errors
    errors = []
    for error in exc.errors():
        errors.append({
            "field": " -> ".join(str(loc) for loc in error["loc"]),
            "message": error["msg"],
            "type": error["type"],
        })
    
    # Log the error
    logger.warning(
        "Validation error occurred",
        errors=errors,
        path=request.url.path,
        method=request.method,
    )
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": {
                "code": "VALIDATION_ERROR",
                "message": "Request validation failed",
                "details": {
                    "errors": errors,
                },
            },
            "request_id": request.headers.get("X-Request-ID"),
        },
    )


def handle_external_api_error(
    service_name: str,
    error: Exception,
    context: Optional[Dict[str, Any]] = None,
) -> CustomException:
    """
    Convert external API errors to custom exceptions.
    
    Args:
        service_name: Name of the external service
        error: Original exception
        context: Additional context information
        
    Returns:
        CustomException: Converted custom exception
    """
    logger = structlog.get_logger()
    
    # Log the original error
    logger.error(
        "External API error",
        service=service_name,
        error=str(error),
        error_type=type(error).__name__,
        context=context or {},
    )
    
    # Convert common error types
    if "rate limit" in str(error).lower():
        return RateLimitException(
            message=f"Rate limit exceeded for {service_name}",
            details={"original_error": str(error), **(context or {})},
        )
    elif "authentication" in str(error).lower() or "unauthorized" in str(error).lower():
        return AuthenticationException(
            message=f"Authentication failed for {service_name}",
            details={"original_error": str(error), **(context or {})},
        )
    else:
        return ExternalServiceException(
            service_name=service_name,
            message=str(error),
            details={"original_error": str(error), **(context or {})},
        )


def safe_execute(func, *args, **kwargs):
    """
    Safely execute a function and handle exceptions.
    
    Args:
        func: Function to execute
        *args: Function arguments
        **kwargs: Function keyword arguments
        
    Returns:
        Function result or None if exception occurred
        
    Raises:
        CustomException: If an error occurs during execution
    """
    try:
        return func(*args, **kwargs)
    except CustomException:
        # Re-raise custom exceptions
        raise
    except Exception as e:
        logger = structlog.get_logger()
        logger.error(
            "Unexpected error in safe_execute",
            function=func.__name__,
            error=str(e),
            error_type=type(e).__name__,
        )
        raise CustomException(
            message=f"Unexpected error in {func.__name__}: {str(e)}",
            error_code="UNEXPECTED_ERROR",
            details={"function": func.__name__, "original_error": str(e)},
        )