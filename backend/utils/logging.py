"""
Structured logging configuration using structlog.
Provides consistent logging across the application.
"""

import logging
import sys
from typing import Any, Dict

import structlog
from structlog.stdlib import LoggerFactory


def configure_logging(log_level: str = "INFO", environment: str = "development") -> None:
    """
    Configure structured logging for the application.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        environment: Environment name (development, staging, production)
    """
    # Set the log level
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level.upper()),
    )
    
    # Configure processors based on environment
    processors = [
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.TimeStamper(fmt="ISO"),
        structlog.processors.StackInfoRenderer(),
    ]
    
    if environment == "development":
        # Pretty output for development
        processors.extend([
            structlog.dev.Console<PERSON>enderer(colors=True),
        ])
    else:
        # JSON output for production/staging
        processors.extend([
            structlog.processors.dict_tracebacks,
            structlog.processors.JSONRenderer(),
        ])
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, log_level.upper())
        ),
        context_class=dict,
        logger_factory=LoggerFactory(),
        cache_logger_on_first_use=True,
    )


def get_logger(name: str = "") -> structlog.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        structlog.BoundLogger: Configured logger instance
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """
    Mixin class to add logging capability to any class.
    """
    
    @property
    def logger(self) -> structlog.BoundLogger:
        """Get logger for this class."""
        return get_logger(self.__class__.__name__)


def log_function_call(func_name: str, **kwargs: Any) -> None:
    """
    Log a function call with parameters.
    
    Args:
        func_name: Name of the function being called
        **kwargs: Function parameters to log
    """
    logger = get_logger()
    logger.info(
        "Function called",
        function=func_name,
        parameters=kwargs,
    )


def log_function_result(func_name: str, result: Any = None, error: Exception = None) -> None:
    """
    Log the result of a function call.
    
    Args:
        func_name: Name of the function
        result: Function result (if successful)
        error: Exception (if failed)
    """
    logger = get_logger()
    
    if error:
        logger.error(
            "Function failed",
            function=func_name,
            error=str(error),
            error_type=type(error).__name__,
        )
    else:
        logger.info(
            "Function completed",
            function=func_name,
            result_type=type(result).__name__ if result is not None else "None",
        )


def log_api_request(method: str, url: str, headers: Dict[str, Any] = None, **kwargs: Any) -> None:
    """
    Log an API request.
    
    Args:
        method: HTTP method
        url: Request URL
        headers: Request headers
        **kwargs: Additional request parameters
    """
    logger = get_logger()
    
    # Remove sensitive headers
    safe_headers = {}
    if headers:
        for key, value in headers.items():
            if key.lower() in ["authorization", "x-api-key", "cookie"]:
                safe_headers[key] = "[REDACTED]"
            else:
                safe_headers[key] = value
    
    logger.info(
        "API request",
        method=method,
        url=url,
        headers=safe_headers,
        **kwargs,
    )


def log_api_response(status_code: int, response_time: float, **kwargs: Any) -> None:
    """
    Log an API response.
    
    Args:
        status_code: HTTP status code
        response_time: Response time in seconds
        **kwargs: Additional response data
    """
    logger = get_logger()
    
    log_level = "info"
    if status_code >= 400:
        log_level = "error" if status_code >= 500 else "warning"
    
    getattr(logger, log_level)(
        "API response",
        status_code=status_code,
        response_time=round(response_time, 4),
        **kwargs,
    )


def log_campaign_event(
    event_type: str,
    campaign_id: str,
    event_data: Dict[str, Any] = None,
    **kwargs: Any
) -> None:
    """
    Log a campaign-related event.
    
    Args:
        event_type: Type of event (created, updated, optimized, etc.)
        campaign_id: Campaign identifier
        event_data: Event-specific data
        **kwargs: Additional context
    """
    logger = get_logger()
    
    logger.info(
        "Campaign event",
        event_type=event_type,
        campaign_id=campaign_id,
        event_data=event_data or {},
        **kwargs,
    )


def log_agent_activity(
    agent_name: str,
    activity: str,
    details: Dict[str, Any] = None,
    **kwargs: Any
) -> None:
    """
    Log an AI agent activity.
    
    Args:
        agent_name: Name of the agent
        activity: Description of the activity
        details: Activity details
        **kwargs: Additional context
    """
    logger = get_logger()
    
    logger.info(
        "Agent activity",
        agent_name=agent_name,
        activity=activity,
        details=details or {},
        **kwargs,
    )