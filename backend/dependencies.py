"""
Dependency providers for FastAPI dependency injection.
Provides singleton instances and configurations for API endpoints.
"""

from typing import Generator
from fastapi import Depends

from services.database import database_service
from services.google_ads import google_ads_service
from services.redis_service import redis_service
from utils.config import settings


async def get_database_service():
    """
    Provide database service dependency.
    
    Returns:
        DatabaseService: Authenticated database service instance
    """
    await database_service.authenticate()
    return database_service


async def get_google_ads_service():
    """
    Provide Google Ads service dependency.
    
    Returns:
        GoogleAdsService: Authenticated Google Ads service instance
    """
    await google_ads_service.authenticate()
    return google_ads_service


async def get_redis_service():
    """
    Provide Redis service dependency.
    
    Returns:
        RedisService: Authenticated Redis service instance
    """
    try:
        await redis_service.authenticate()
        return redis_service
    except Exception:
        # Redis is optional, return None if unavailable
        return None


def get_settings():
    """
    Provide application settings dependency.
    
    Returns:
        Settings: Application configuration settings
    """
    return settings


# Type aliases for dependencies
DatabaseServiceDep = Depends(get_database_service)
GoogleAdsServiceDep = Depends(get_google_ads_service)
RedisServiceDep = Depends(get_redis_service)
SettingsDep = Depends(get_settings)