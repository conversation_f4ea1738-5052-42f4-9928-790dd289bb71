# Fly.io configuration for API-only service (lightweight, fast deployment)
app = "ailex-ad-agent-api"
primary_region = "iad"

[build]
  dockerfile = "Dockerfile.api"

[env]
  SERVICE_TYPE = "api"
  ENVIRONMENT = "production"
  LOG_LEVEL = "INFO"
  PORT = "8000"

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  max_machines_running = 5
  processes = ["app"]
  
  [http_service.concurrency]
    type = "requests"
    hard_limit = 1000
    soft_limit = 800

  [[http_service.checks]]
    grace_period = "5s"
    interval = "10s"
    method = "GET"
    timeout = "3s"
    path = "/api/v1/health/liveness"
    
  [[http_service.checks]]
    grace_period = "10s"
    interval = "30s"
    method = "GET"
    timeout = "5s"
    path = "/api/v1/health/readiness"

[[vm]]
  memory = "512mb"  # Reduced memory for API-only
  cpu_kind = "shared"
  cpus = 1

# Deploy configuration
[deploy]
  strategy = "rolling"
  max_unavailable = 0.33

# Metrics
[metrics]
  port = 9091
  path = "/metrics"
