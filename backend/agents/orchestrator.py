"""
Central Agent Orchestrator for AiLex Ad Agent System.
Coordinates multiple agents for campaign management tasks using CrewAI framework.
"""

import asyncio
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum

import structlog
from crewai import Crew, Task, Agent
from pydantic import BaseModel

from .base import BaseAiLexAgent, AgentContext, AgentMessage, AgentError
from .communication import AgentCommunicationHub
from .core.campaign_planning import CampaignPlanningAgent
from .core.ad_asset_generation import AdAssetGenerationAgent  
from .core.audience_targeting import AudienceTargetingAgent
from .core.budget_management import BudgetManagementAgent
from .core.performance_analysis import PerformanceAnalysisAgent
from models.agents import AgentType, AgentConfig, AgentStatus
from services.gemini_service import gemini_service
from services.google_ads import google_ads_service
from services.database import database_service
from utils.config import settings


logger = structlog.get_logger(__name__)


class WorkflowStatus(Enum):
    """Status of workflow execution."""
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class WorkflowTask:
    """Individual task within a workflow."""
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    agent_type: AgentType = AgentType.CAMPAIGN_PLANNING
    dependencies: List[str] = field(default_factory=list)
    inputs: Dict[str, Any] = field(default_factory=dict)
    outputs: Dict[str, Any] = field(default_factory=dict)
    status: WorkflowStatus = WorkflowStatus.PENDING
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    execution_time: float = 0.0


@dataclass
class CampaignWorkflow:
    """Complete campaign workflow with multiple agent tasks."""
    workflow_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    campaign_id: Optional[str] = None
    user_id: str = ""
    tasks: List[WorkflowTask] = field(default_factory=list)
    status: WorkflowStatus = WorkflowStatus.PENDING
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    total_execution_time: float = 0.0
    results: Dict[str, Any] = field(default_factory=dict)
    context: AgentContext = field(default_factory=AgentContext)


class CampaignRequest(BaseModel):
    """Request model for campaign creation workflow."""
    business_description: str
    industry: str
    target_audience: str
    campaign_objectives: List[str]
    budget: float
    duration_days: int
    location: Optional[str] = None
    additional_requirements: Optional[Dict[str, Any]] = None


class CampaignOrchestrator:
    """
    Central orchestrator for coordinating multiple AI agents in campaign management.
    """
    
    def __init__(self):
        self.orchestrator_id = str(uuid.uuid4())
        self.logger = structlog.get_logger().bind(orchestrator_id=self.orchestrator_id)
        
        # Agent instances
        self.agents: Dict[AgentType, BaseAiLexAgent] = {}
        self.agent_communicator = AgentCommunicationHub()
        
        # Workflow management
        self.active_workflows: Dict[str, CampaignWorkflow] = {}
        self.workflow_history: List[CampaignWorkflow] = []
        
        # Services
        self.gemini_service = gemini_service
        self.google_ads_service = google_ads_service
        self.database_service = database_service
        
        # Configuration
        self.max_concurrent_workflows = 10
        self.default_timeout_minutes = 30
        
        self.initialized = False
    
    async def initialize(self) -> None:
        """Initialize the orchestrator and all agent instances."""
        if self.initialized:
            return
        
        try:
            self.logger.info("Initializing Campaign Orchestrator")
            
            # Initialize services
            await self.gemini_service.authenticate()
            
            # Create default agent configurations
            agent_configs = await self._create_agent_configurations()
            
            # Initialize core agents
            await self._initialize_agents(agent_configs)
            
            # Initialize agent communication
            await self.agent_communicator.initialize()
            
            self.initialized = True
            self.logger.info(
                "Campaign Orchestrator initialized successfully",
                agents_count=len(self.agents),
                available_agent_types=[agent_type.value for agent_type in self.agents.keys()]
            )
            
        except Exception as e:
            self.logger.error("Failed to initialize orchestrator", error=str(e))
            raise AgentError(f"Orchestrator initialization failed: {str(e)}")
    
    async def _create_agent_configurations(self) -> Dict[AgentType, AgentConfig]:
        """Create configurations for all agent types."""
        base_config = {
            "timeout_seconds": 300,  # 5 minutes per agent task
            "max_iterations": 3,
            "verbose": settings.DEBUG,
            "allow_delegation": True,
            "memory": {"enabled": True, "type": "short_term"},
            "model": {
                "model_name": "gemini-1.5-flash",
                "temperature": 0.7,
                "max_tokens": 2048
            }
        }
        
        configurations = {}
        
        # Campaign Planning Agent
        configurations[AgentType.CAMPAIGN_PLANNING] = AgentConfig(
            **base_config,
            system_message="You are an expert campaign planning agent specializing in market research, strategy development, and competitive analysis for Google Ads campaigns."
        )
        
        # Ad Asset Generation Agent
        configurations[AgentType.AD_ASSET_GENERATION] = AgentConfig(
            **base_config,
            system_message="You are an expert ad creative agent specializing in generating compelling ad copy, headlines, and descriptions for Google Ads campaigns.",
            model={
                **base_config["model"],
                "temperature": 0.8  # Higher creativity for ad generation
            }
        )
        
        # Audience Targeting Agent
        configurations[AgentType.AUDIENCE_TARGETING] = AgentConfig(
            **base_config,
            system_message="You are an expert audience targeting agent specializing in demographic analysis, interest targeting, and customer segmentation for Google Ads campaigns."
        )
        
        # Budget Management Agent
        configurations[AgentType.BUDGET_MANAGEMENT] = AgentConfig(
            **base_config,
            system_message="You are an expert budget optimization agent specializing in bid management, budget allocation, and campaign cost optimization.",
            model={
                **base_config["model"],
                "temperature": 0.3  # Lower temperature for financial decisions
            }
        )
        
        # Performance Analysis Agent
        configurations[AgentType.PERFORMANCE_ANALYSIS] = AgentConfig(
            **base_config,
            system_message="You are an expert performance analysis agent specializing in campaign metrics analysis, reporting, and optimization recommendations.",
            model={
                **base_config["model"],
                "temperature": 0.3  # Lower temperature for analytical tasks
            }
        )
        
        return configurations
    
    async def _initialize_agents(self, configs: Dict[AgentType, AgentConfig]) -> None:
        """Initialize all agent instances."""
        agent_initializations = []
        
        # Campaign Planning Agent
        if AgentType.CAMPAIGN_PLANNING in configs:
            agent_id = f"campaign_planning_{str(uuid.uuid4())[:8]}"
            agent = CampaignPlanningAgent(agent_id, configs[AgentType.CAMPAIGN_PLANNING])
            agent_initializations.append(self._safe_agent_init(agent, AgentType.CAMPAIGN_PLANNING))
        
        # Ad Asset Generation Agent
        if AgentType.AD_ASSET_GENERATION in configs:
            agent_id = f"ad_generation_{str(uuid.uuid4())[:8]}"
            agent = AdAssetGenerationAgent(agent_id, configs[AgentType.AD_ASSET_GENERATION])
            agent_initializations.append(self._safe_agent_init(agent, AgentType.AD_ASSET_GENERATION))
        
        # Audience Targeting Agent
        if AgentType.AUDIENCE_TARGETING in configs:
            agent_id = f"audience_targeting_{str(uuid.uuid4())[:8]}"
            agent = AudienceTargetingAgent(agent_id, configs[AgentType.AUDIENCE_TARGETING])
            agent_initializations.append(self._safe_agent_init(agent, AgentType.AUDIENCE_TARGETING))
        
        # Budget Management Agent
        if AgentType.BUDGET_MANAGEMENT in configs:
            agent_id = f"budget_mgmt_{str(uuid.uuid4())[:8]}"
            agent = BudgetManagementAgent(agent_id, configs[AgentType.BUDGET_MANAGEMENT])
            agent_initializations.append(self._safe_agent_init(agent, AgentType.BUDGET_MANAGEMENT))
        
        # Performance Analysis Agent
        if AgentType.PERFORMANCE_ANALYSIS in configs:
            agent_id = f"performance_{str(uuid.uuid4())[:8]}"
            agent = PerformanceAnalysisAgent(agent_id, configs[AgentType.PERFORMANCE_ANALYSIS])
            agent_initializations.append(self._safe_agent_init(agent, AgentType.PERFORMANCE_ANALYSIS))
        
        # Wait for all agents to initialize
        await asyncio.gather(*agent_initializations)
        
        self.logger.info(
            "Agents initialized",
            successful_agents=[agent_type.value for agent_type in self.agents.keys()],
            total_agents=len(self.agents)
        )
    
    async def _safe_agent_init(self, agent: BaseAiLexAgent, agent_type: AgentType) -> None:
        """Safely initialize an agent with error handling."""
        try:
            await agent.initialize(agent.config)
            self.agents[agent_type] = agent
            self.logger.info(f"Successfully initialized {agent_type.value} agent", agent_id=agent.agent_id)
        except Exception as e:
            self.logger.error(f"Failed to initialize {agent_type.value} agent", error=str(e))
            # Continue with other agents even if one fails
    
    async def create_campaign_workflow(self, request: CampaignRequest) -> CampaignWorkflow:
        """
        Create a complete campaign workflow based on request.
        
        Args:
            request: Campaign creation request
            
        Returns:
            CampaignWorkflow: Created workflow with all tasks
        """
        if not self.initialized:
            await self.initialize()
        
        workflow = CampaignWorkflow(
            name=f"Campaign Creation - {request.industry}",
            description=f"Complete campaign creation workflow for {request.business_description}",
            user_id=request.additional_requirements.get("user_id", "system") if request.additional_requirements else "system"
        )
        
        # Create workflow context
        workflow.context = AgentContext(
            campaign_id=workflow.workflow_id,
            user_id=workflow.user_id,
            session_id=str(uuid.uuid4()),
            metadata={
                "business_description": request.business_description,
                "industry": request.industry,
                "target_audience": request.target_audience,
                "budget": request.budget,
                "duration_days": request.duration_days,
                "location": request.location,
                "objectives": request.campaign_objectives
            }
        )
        
        # Define workflow tasks with dependencies
        tasks = [
            # 1. Market Research & Strategy (Campaign Planning Agent)
            WorkflowTask(
                name="Market Research & Campaign Strategy",
                agent_type=AgentType.CAMPAIGN_PLANNING,
                dependencies=[],  # No dependencies - first task
                inputs={
                    "business_description": request.business_description,
                    "industry": request.industry,
                    "target_audience": request.target_audience,
                    "campaign_objectives": request.campaign_objectives,
                    "budget": request.budget,
                    "duration_days": request.duration_days,
                    "location": request.location
                }
            ),
            
            # 2. Audience Research & Targeting (Audience Targeting Agent)
            WorkflowTask(
                name="Audience Research & Targeting",
                agent_type=AgentType.AUDIENCE_TARGETING,
                dependencies=[],  # Can run in parallel with market research
                inputs={
                    "business_description": request.business_description,
                    "industry": request.industry,
                    "target_audience": request.target_audience,
                    "location": request.location
                }
            ),
            
            # 3. Ad Asset Generation (Ad Asset Generation Agent)
            WorkflowTask(
                name="Ad Creative Generation",
                agent_type=AgentType.AD_ASSET_GENERATION,
                dependencies=["Market Research & Campaign Strategy", "Audience Research & Targeting"],
                inputs={
                    "business_description": request.business_description,
                    "campaign_objectives": request.campaign_objectives
                }
            ),
            
            # 4. Budget Planning & Allocation (Budget Management Agent)
            WorkflowTask(
                name="Budget Planning & Allocation",
                agent_type=AgentType.BUDGET_MANAGEMENT,
                dependencies=["Market Research & Campaign Strategy"],
                inputs={
                    "total_budget": request.budget,
                    "duration_days": request.duration_days,
                    "campaign_objectives": request.campaign_objectives
                }
            ),
            
            # 5. Campaign Setup Validation (Performance Analysis Agent)
            WorkflowTask(
                name="Campaign Setup Validation",
                agent_type=AgentType.PERFORMANCE_ANALYSIS,
                dependencies=["Ad Creative Generation", "Budget Planning & Allocation"],
                inputs={
                    "validate_setup": True,
                    "campaign_objectives": request.campaign_objectives
                }
            )
        ]
        
        workflow.tasks = tasks
        
        # Store workflow
        self.active_workflows[workflow.workflow_id] = workflow
        
        self.logger.info(
            "Created campaign workflow",
            workflow_id=workflow.workflow_id,
            tasks_count=len(tasks),
            industry=request.industry
        )
        
        return workflow
    
    async def execute_workflow(self, workflow_id: str) -> CampaignWorkflow:
        """
        Execute a campaign workflow.
        
        Args:
            workflow_id: ID of workflow to execute
            
        Returns:
            CampaignWorkflow: Completed workflow with results
            
        Raises:
            AgentError: If workflow execution fails
        """
        if workflow_id not in self.active_workflows:
            raise AgentError(f"Workflow {workflow_id} not found")
        
        workflow = self.active_workflows[workflow_id]
        workflow.status = WorkflowStatus.RUNNING
        workflow.started_at = datetime.utcnow()
        
        try:
            self.logger.info(
                "Starting workflow execution",
                workflow_id=workflow_id,
                tasks_count=len(workflow.tasks)
            )
            
            # Execute tasks in dependency order
            completed_tasks = set()
            task_results = {}
            
            while len(completed_tasks) < len(workflow.tasks):
                # Find tasks ready to execute
                ready_tasks = [
                    task for task in workflow.tasks
                    if (task.status == WorkflowStatus.PENDING and
                        all(dep in [t.name for t in workflow.tasks if t.task_id in [completed_task_id for completed_task_id in completed_tasks]] or not task.dependencies for dep in task.dependencies))
                ]
                
                if not ready_tasks:
                    # Check if we're stuck
                    pending_tasks = [task for task in workflow.tasks if task.status == WorkflowStatus.PENDING]
                    if pending_tasks:
                        raise AgentError("Workflow stuck - dependency deadlock or failed tasks")
                    break
                
                # Execute ready tasks (can be run in parallel)
                task_executions = [
                    self._execute_workflow_task(task, workflow, task_results)
                    for task in ready_tasks
                ]
                
                # Wait for all ready tasks to complete
                completed_task_results = await asyncio.gather(*task_executions, return_exceptions=True)
                
                # Process results
                for i, task in enumerate(ready_tasks):
                    result = completed_task_results[i]
                    
                    if isinstance(result, Exception):
                        task.status = WorkflowStatus.FAILED
                        task.error_message = str(result)
                        self.logger.error(
                            "Task execution failed",
                            task_id=task.task_id,
                            task_name=task.name,
                            error=str(result)
                        )
                        # For now, continue with other tasks
                        # In production, you might want to implement retry logic or fail-fast behavior
                    else:
                        task.status = WorkflowStatus.COMPLETED
                        task.completed_at = datetime.utcnow()
                        task.outputs = result
                        task_results[task.name] = result
                        completed_tasks.add(task.task_id)
                        
                        self.logger.info(
                            "Task completed successfully",
                            task_id=task.task_id,
                            task_name=task.name,
                            execution_time=task.execution_time
                        )
            
            # Compile final results
            workflow.results = task_results
            workflow.status = WorkflowStatus.COMPLETED
            workflow.completed_at = datetime.utcnow()
            workflow.total_execution_time = (workflow.completed_at - workflow.started_at).total_seconds()
            
            # Move to history
            self.workflow_history.append(workflow)
            del self.active_workflows[workflow_id]
            
            self.logger.info(
                "Workflow completed successfully",
                workflow_id=workflow_id,
                total_execution_time=workflow.total_execution_time,
                successful_tasks=len([t for t in workflow.tasks if t.status == WorkflowStatus.COMPLETED]),
                failed_tasks=len([t for t in workflow.tasks if t.status == WorkflowStatus.FAILED])
            )
            
            return workflow
            
        except Exception as e:
            workflow.status = WorkflowStatus.FAILED
            workflow.completed_at = datetime.utcnow()
            if workflow.started_at:
                workflow.total_execution_time = (workflow.completed_at - workflow.started_at).total_seconds()
            
            self.logger.error("Workflow execution failed", workflow_id=workflow_id, error=str(e))
            raise AgentError(f"Workflow execution failed: {str(e)}")
    
    async def _execute_workflow_task(
        self,
        task: WorkflowTask,
        workflow: CampaignWorkflow,
        previous_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute a single workflow task.
        
        Args:
            task: Task to execute
            workflow: Parent workflow
            previous_results: Results from previously completed tasks
            
        Returns:
            Dict[str, Any]: Task execution results
        """
        task.status = WorkflowStatus.RUNNING
        task.started_at = datetime.utcnow()
        
        try:
            # Get the appropriate agent
            agent = self.agents.get(task.agent_type)
            if not agent:
                raise AgentError(f"Agent type {task.agent_type.value} not available")
            
            # Prepare task inputs with results from dependencies
            enriched_inputs = task.inputs.copy()
            for dep_name in task.dependencies:
                if dep_name in previous_results:
                    enriched_inputs[f"dependency_{dep_name.lower().replace(' ', '_')}"] = previous_results[dep_name]
            
            # Create CrewAI Task
            crew_task = Task(
                description=f"Execute {task.name}: {self._get_task_description(task)}",
                expected_output=self._get_expected_output(task),
                agent=agent._crew_agent
            )
            
            # Execute the task
            self.logger.info(
                "Executing task",
                task_id=task.task_id,
                task_name=task.name,
                agent_type=task.agent_type.value
            )
            
            result = await agent.execute_task(crew_task, workflow.context)
            
            # Calculate execution time
            if task.started_at:
                task.execution_time = (datetime.utcnow() - task.started_at).total_seconds()
            
            return result
            
        except Exception as e:
            task.status = WorkflowStatus.FAILED
            task.error_message = str(e)
            if task.started_at:
                task.execution_time = (datetime.utcnow() - task.started_at).total_seconds()
            
            self.logger.error(
                "Task execution failed",
                task_id=task.task_id,
                task_name=task.name,
                error=str(e)
            )
            raise
    
    def _get_task_description(self, task: WorkflowTask) -> str:
        """Get detailed task description based on task type."""
        descriptions = {
            AgentType.CAMPAIGN_PLANNING: "Conduct comprehensive market research, competitor analysis, and develop campaign strategy including keyword research and budget recommendations.",
            AgentType.AUDIENCE_TARGETING: "Analyze target audience demographics, interests, and behaviors to create detailed audience segments and targeting recommendations.",
            AgentType.AD_ASSET_GENERATION: "Generate compelling ad copy, headlines, descriptions, and creative assets optimized for the target audience and campaign objectives.",
            AgentType.BUDGET_MANAGEMENT: "Analyze budget requirements, allocate funds across channels and campaigns, and provide bid management recommendations.",
            AgentType.PERFORMANCE_ANALYSIS: "Validate campaign setup, analyze potential performance, and provide optimization recommendations before launch."
        }
        return descriptions.get(task.agent_type, f"Execute {task.name}")
    
    def _get_expected_output(self, task: WorkflowTask) -> str:
        """Get expected output description based on task type."""
        outputs = {
            AgentType.CAMPAIGN_PLANNING: "Comprehensive campaign strategy with market research findings, competitor analysis, keyword recommendations, and strategic insights.",
            AgentType.AUDIENCE_TARGETING: "Detailed audience targeting plan with demographics, interests, behaviors, and custom audience recommendations.",
            AgentType.AD_ASSET_GENERATION: "Complete set of ad creatives including headlines, descriptions, display URLs, and creative variations optimized for performance.",
            AgentType.BUDGET_MANAGEMENT: "Budget allocation plan with recommended spend across channels, bid strategies, and budget optimization recommendations.",
            AgentType.PERFORMANCE_ANALYSIS: "Campaign setup validation report with performance predictions, optimization opportunities, and launch readiness assessment."
        }
        return outputs.get(task.agent_type, "Task completion confirmation with results")
    
    async def get_workflow_status(self, workflow_id: str) -> Optional[CampaignWorkflow]:
        """Get current status of a workflow."""
        if workflow_id in self.active_workflows:
            return self.active_workflows[workflow_id]
        
        # Check workflow history
        for workflow in self.workflow_history:
            if workflow.workflow_id == workflow_id:
                return workflow
        
        return None
    
    async def list_active_workflows(self) -> List[CampaignWorkflow]:
        """List all active workflows."""
        return list(self.active_workflows.values())
    
    async def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel an active workflow."""
        if workflow_id not in self.active_workflows:
            return False
        
        workflow = self.active_workflows[workflow_id]
        workflow.status = WorkflowStatus.CANCELLED
        workflow.completed_at = datetime.utcnow()
        if workflow.started_at:
            workflow.total_execution_time = (workflow.completed_at - workflow.started_at).total_seconds()
        
        # Move to history
        self.workflow_history.append(workflow)
        del self.active_workflows[workflow_id]
        
        self.logger.info("Workflow cancelled", workflow_id=workflow_id)
        return True
    
    async def get_agent_status(self, agent_type: AgentType) -> Optional[Dict[str, Any]]:
        """Get status of a specific agent."""
        agent = self.agents.get(agent_type)
        if not agent:
            return None
        
        return {
            "agent_id": agent.agent_id,
            "agent_type": agent_type.value,
            "status": await agent.get_status(),
            "performance_metrics": agent.get_performance_metrics()
        }
    
    async def get_orchestrator_status(self) -> Dict[str, Any]:
        """Get overall orchestrator status."""
        return {
            "orchestrator_id": self.orchestrator_id,
            "initialized": self.initialized,
            "active_workflows": len(self.active_workflows),
            "total_workflows_processed": len(self.workflow_history),
            "available_agents": [agent_type.value for agent_type in self.agents.keys()],
            "agent_count": len(self.agents),
            "max_concurrent_workflows": self.max_concurrent_workflows
        }
    
    async def shutdown(self) -> None:
        """Gracefully shutdown the orchestrator and all agents."""
        self.logger.info("Shutting down Campaign Orchestrator")
        
        # Cancel all active workflows
        for workflow_id in list(self.active_workflows.keys()):
            await self.cancel_workflow(workflow_id)
        
        # Shutdown all agents
        shutdown_tasks = [
            agent.shutdown() for agent in self.agents.values()
        ]
        await asyncio.gather(*shutdown_tasks, return_exceptions=True)
        
        # Shutdown communicator
        await self.agent_communicator.shutdown()
        
        self.initialized = False
        self.logger.info("Campaign Orchestrator shutdown completed")


# Global orchestrator instance
campaign_orchestrator = CampaignOrchestrator()