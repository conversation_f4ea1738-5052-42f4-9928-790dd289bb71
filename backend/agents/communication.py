"""
Agent Communication System for AiLex Ad Agent System.
Handles inter-agent messaging, coordination, and collaboration protocols.
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable, Set
from dataclasses import dataclass, field
from collections import defaultdict
from enum import Enum

import structlog

from .base import BaseAiLexAgent, AgentMessage, AgentContext


logger = structlog.get_logger(__name__)


class CommunicationProtocol(str, Enum):
    """Communication protocols between agents."""
    DIRECT = "direct"
    BROADCAST = "broadcast"
    REQUEST_RESPONSE = "request_response"
    PUBLISH_SUBSCRIBE = "publish_subscribe"
    HIERARCHICAL = "hierarchical"


class MessagePriority(str, Enum):
    """Message priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"


@dataclass
class CommunicationChannel:
    """Communication channel between agents."""
    channel_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    protocol: CommunicationProtocol = CommunicationProtocol.DIRECT
    participants: Set[str] = field(default_factory=set)
    message_history: List[AgentMessage] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.utcnow)
    is_active: bool = True
    max_history_size: int = 1000


@dataclass
class CollaborationSession:
    """Multi-agent collaboration session."""
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    participants: Dict[str, BaseAiLexAgent] = field(default_factory=dict)
    coordinator_id: Optional[str] = None
    context: AgentContext = field(default_factory=AgentContext)
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: str = "pending"
    shared_memory: Dict[str, Any] = field(default_factory=dict)
    collaboration_metrics: Dict[str, Any] = field(default_factory=dict)


class AgentCommunicationHub:
    """
    Central hub for managing agent communication and collaboration.
    Supports the full 8-agent team with specialized communication patterns.
    """
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
        
        # Message routing
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.priority_queues: Dict[MessagePriority, asyncio.Queue] = {
            priority: asyncio.Queue() for priority in MessagePriority
        }
        
        # Agent registry
        self.registered_agents: Dict[str, BaseAiLexAgent] = {}
        self.agent_roles: Dict[str, str] = {}  # agent_id -> role
        
        # Communication channels
        self.channels: Dict[str, CommunicationChannel] = {}
        self.subscriptions: Dict[str, Set[str]] = defaultdict(set)  # topic -> agent_ids
        
        # Collaboration sessions
        self.active_sessions: Dict[str, CollaborationSession] = {}
        
        # Message handlers and routing
        self.message_handlers: Dict[str, Callable] = {}
        self.routing_rules: Dict[str, List[Callable]] = defaultdict(list)
        
        # Communication metrics
        self.communication_stats = {
            "messages_sent": 0,
            "messages_delivered": 0,
            "messages_failed": 0,
            "active_channels": 0,
            "active_sessions": 0,
            "average_response_time": 0.0
        }
        
        # Control flags
        self.is_running = False
        self._setup_default_handlers()
    
    def _setup_default_handlers(self) -> None:
        """Set up default message handlers."""
        self.message_handlers.update({
            "heartbeat": self._handle_heartbeat,
            "status_update": self._handle_status_update,
            "collaboration_request": self._handle_collaboration_request,
            "collaboration_response": self._handle_collaboration_response,
            "task_delegation": self._handle_task_delegation,
            "resource_request": self._handle_resource_request,
            "knowledge_share": self._handle_knowledge_share,
            "error_report": self._handle_error_report
        })
    
    async def start(self) -> None:
        """Start the communication hub."""
        if self.is_running:
            return
        
        self.is_running = True
        self.logger.info("Starting agent communication hub")
        
        # Start message processing tasks
        asyncio.create_task(self._process_messages())
        asyncio.create_task(self._process_priority_queues())
        asyncio.create_task(self._monitor_agent_health())
        asyncio.create_task(self._cleanup_old_sessions())
        
        self.logger.info("Agent communication hub started")
    
    async def stop(self) -> None:
        """Stop the communication hub."""
        if not self.is_running:
            return
        
        self.logger.info("Stopping agent communication hub")
        self.is_running = False
        
        # Clean up active sessions
        for session in self.active_sessions.values():
            if session.status == "running":
                session.status = "cancelled"
                session.completed_at = datetime.utcnow()
        
        self.logger.info("Agent communication hub stopped")
    
    def register_agent(self, agent: BaseAiLexAgent, role: str = "") -> None:
        """
        Register an agent for communication.
        
        Args:
            agent: Agent instance to register
            role: Agent role for specialized routing
        """
        self.registered_agents[agent.agent_id] = agent
        self.agent_roles[agent.agent_id] = role
        
        # Create default channels for the agent
        self._create_agent_channels(agent.agent_id, role)
        
        self.logger.info(
            "Agent registered for communication",
            agent_id=agent.agent_id,
            agent_name=agent.name,
            role=role
        )
    
    def unregister_agent(self, agent_id: str) -> None:
        """Unregister an agent from communication."""
        if agent_id in self.registered_agents:
            # Remove from all channels
            for channel in self.channels.values():
                channel.participants.discard(agent_id)
            
            # Remove from subscriptions
            for topic_agents in self.subscriptions.values():
                topic_agents.discard(agent_id)
            
            # Clean up
            del self.registered_agents[agent_id]
            if agent_id in self.agent_roles:
                del self.agent_roles[agent_id]
            
            self.logger.info("Agent unregistered", agent_id=agent_id)
    
    def _create_agent_channels(self, agent_id: str, role: str) -> None:
        """Create default communication channels for an agent."""
        # Role-based channels
        role_channel_id = f"role_{role}"
        if role_channel_id not in self.channels:
            self.channels[role_channel_id] = CommunicationChannel(
                channel_id=role_channel_id,
                name=f"{role.title()} Role Channel",
                protocol=CommunicationProtocol.BROADCAST
            )
        
        self.channels[role_channel_id].participants.add(agent_id)
        
        # General broadcast channel
        if "general" not in self.channels:
            self.channels["general"] = CommunicationChannel(
                channel_id="general",
                name="General Broadcast Channel",
                protocol=CommunicationProtocol.BROADCAST
            )
        
        self.channels["general"].participants.add(agent_id)
    
    async def send_message(
        self,
        sender_id: str,
        recipient_id: str,
        message_type: str,
        content: Dict[str, Any],
        priority: MessagePriority = MessagePriority.NORMAL,
        requires_response: bool = False,
        correlation_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Send a direct message between agents.
        
        Args:
            sender_id: Sender agent ID
            recipient_id: Recipient agent ID
            message_type: Type of message
            content: Message content
            priority: Message priority
            requires_response: Whether response is required
            correlation_id: Optional correlation ID
            
        Returns:
            Optional[str]: Message ID if sent successfully
        """
        if sender_id not in self.registered_agents:
            self.logger.error("Sender agent not registered", sender_id=sender_id)
            return None
        
        if recipient_id != "broadcast" and recipient_id not in self.registered_agents:
            self.logger.error("Recipient agent not registered", recipient_id=recipient_id)
            return None
        
        message = AgentMessage(
            sender_id=sender_id,
            recipient_id=recipient_id,
            message_type=message_type,
            content=content,
            priority=priority.value,
            requires_response=requires_response,
            correlation_id=correlation_id
        )
        
        # Add to appropriate queue based on priority
        await self.priority_queues[priority].put(message)
        self.communication_stats["messages_sent"] += 1
        
        self.logger.debug(
            "Message queued for delivery",
            message_id=message.id,
            sender_id=sender_id,
            recipient_id=recipient_id,
            message_type=message_type,
            priority=priority.value
        )
        
        return message.id
    
    async def broadcast_message(
        self,
        sender_id: str,
        message_type: str,
        content: Dict[str, Any],
        channel_id: str = "general",
        exclude_sender: bool = True
    ) -> List[str]:
        """
        Broadcast message to all agents in a channel.
        
        Args:
            sender_id: Sender agent ID
            message_type: Type of message
            content: Message content
            channel_id: Channel to broadcast to
            exclude_sender: Whether to exclude sender from broadcast
            
        Returns:
            List[str]: List of message IDs
        """
        channel = self.channels.get(channel_id)
        if not channel:
            self.logger.error("Channel not found", channel_id=channel_id)
            return []
        
        message_ids = []
        for participant_id in channel.participants:
            if exclude_sender and participant_id == sender_id:
                continue
            
            message_id = await self.send_message(
                sender_id=sender_id,
                recipient_id=participant_id,
                message_type=message_type,
                content=content,
                priority=MessagePriority.NORMAL
            )
            
            if message_id:
                message_ids.append(message_id)
        
        return message_ids
    
    async def create_collaboration_session(
        self,
        name: str,
        description: str,
        participant_ids: List[str],
        coordinator_id: Optional[str] = None,
        context: Optional[AgentContext] = None
    ) -> str:
        """
        Create a new collaboration session.
        
        Args:
            name: Session name
            description: Session description
            participant_ids: List of participating agent IDs
            coordinator_id: Optional coordinator agent ID
            context: Optional collaboration context
            
        Returns:
            str: Session ID
        """
        # Validate participants
        participants = {}
        for agent_id in participant_ids:
            if agent_id not in self.registered_agents:
                raise ValueError(f"Agent {agent_id} not registered")
            participants[agent_id] = self.registered_agents[agent_id]
        
        session = CollaborationSession(
            name=name,
            description=description,
            participants=participants,
            coordinator_id=coordinator_id,
            context=context or AgentContext()
        )
        
        self.active_sessions[session.session_id] = session
        
        # Create dedicated channel for the session
        session_channel = CommunicationChannel(
            channel_id=f"session_{session.session_id}",
            name=f"Collaboration: {name}",
            protocol=CommunicationProtocol.BROADCAST,
            participants=set(participant_ids)
        )
        
        self.channels[session_channel.channel_id] = session_channel
        
        # Notify participants
        await self.broadcast_message(
            sender_id="system",
            message_type="collaboration_invite",
            content={
                "session_id": session.session_id,
                "name": name,
                "description": description,
                "coordinator_id": coordinator_id
            },
            channel_id=session_channel.channel_id,
            exclude_sender=False
        )
        
        self.logger.info(
            "Collaboration session created",
            session_id=session.session_id,
            name=name,
            participants=len(participants)
        )
        
        return session.session_id
    
    async def start_collaboration_session(self, session_id: str) -> bool:
        """Start a collaboration session."""
        session = self.active_sessions.get(session_id)
        if not session:
            return False
        
        session.status = "running"
        session.started_at = datetime.utcnow()
        
        # Initialize shared memory
        session.shared_memory = {
            "session_state": "active",
            "contributions": {},
            "decisions": [],
            "artifacts": {}
        }
        
        # Notify participants
        await self.broadcast_message(
            sender_id="system",
            message_type="collaboration_started",
            content={
                "session_id": session_id,
                "started_at": session.started_at.isoformat()
            },
            channel_id=f"session_{session_id}",
            exclude_sender=False
        )
        
        self.logger.info("Collaboration session started", session_id=session_id)
        return True
    
    async def end_collaboration_session(
        self,
        session_id: str,
        results: Optional[Dict[str, Any]] = None
    ) -> bool:
        """End a collaboration session."""
        session = self.active_sessions.get(session_id)
        if not session:
            return False
        
        session.status = "completed"
        session.completed_at = datetime.utcnow()
        
        if results:
            session.shared_memory["final_results"] = results
        
        # Calculate collaboration metrics
        session.collaboration_metrics = self._calculate_session_metrics(session)
        
        # Notify participants
        await self.broadcast_message(
            sender_id="system",
            message_type="collaboration_ended",
            content={
                "session_id": session_id,
                "completed_at": session.completed_at.isoformat(),
                "results": results or {},
                "metrics": session.collaboration_metrics
            },
            channel_id=f"session_{session_id}",
            exclude_sender=False
        )
        
        self.logger.info("Collaboration session ended", session_id=session_id)
        return True
    
    def _calculate_session_metrics(self, session: CollaborationSession) -> Dict[str, Any]:
        """Calculate metrics for a collaboration session."""
        if not session.started_at or not session.completed_at:
            return {}
        
        duration = (session.completed_at - session.started_at).total_seconds()
        
        # Get session channel messages
        session_channel_id = f"session_{session.session_id}"
        session_channel = self.channels.get(session_channel_id)
        message_count = len(session_channel.message_history) if session_channel else 0
        
        # Calculate participation metrics
        participant_activity = {}
        if session_channel:
            for message in session_channel.message_history:
                sender = message.sender_id
                if sender != "system":
                    participant_activity[sender] = participant_activity.get(sender, 0) + 1
        
        return {
            "duration_seconds": duration,
            "total_messages": message_count,
            "participant_count": len(session.participants),
            "participant_activity": participant_activity,
            "messages_per_minute": (message_count / duration) * 60 if duration > 0 else 0,
            "engagement_score": len(participant_activity) / len(session.participants) if session.participants else 0
        }
    
    # Message processing methods
    
    async def _process_messages(self) -> None:
        """Process messages from the main queue."""
        while self.is_running:
            try:
                if not self.message_queue.empty():
                    message = await self.message_queue.get()
                    await self._deliver_message(message)
            except Exception as e:
                self.logger.error("Error processing messages", error=str(e))
            
            await asyncio.sleep(0.01)
    
    async def _process_priority_queues(self) -> None:
        """Process messages from priority queues."""
        while self.is_running:
            try:
                # Process in priority order
                for priority in [MessagePriority.CRITICAL, MessagePriority.URGENT, 
                               MessagePriority.HIGH, MessagePriority.NORMAL, MessagePriority.LOW]:
                    queue = self.priority_queues[priority]
                    if not queue.empty():
                        message = await queue.get()
                        await self._deliver_message(message)
            except Exception as e:
                self.logger.error("Error processing priority queues", error=str(e))
            
            await asyncio.sleep(0.01)
    
    async def _deliver_message(self, message: AgentMessage) -> None:
        """Deliver a message to its recipient(s)."""
        try:
            start_time = datetime.utcnow()
            
            # Handle broadcast messages
            if message.recipient_id == "broadcast":
                await self._handle_broadcast_message(message)
                return
            
            # Direct message delivery
            recipient = self.registered_agents.get(message.recipient_id)
            if not recipient:
                self.logger.error(
                    "Recipient not found",
                    message_id=message.id,
                    recipient_id=message.recipient_id
                )
                self.communication_stats["messages_failed"] += 1
                return
            
            # Deliver message to recipient
            response = await recipient.handle_message(message)
            
            # Handle response if provided
            if response:
                await self._deliver_message(response)
            
            # Update statistics
            delivery_time = (datetime.utcnow() - start_time).total_seconds()
            self.communication_stats["messages_delivered"] += 1
            
            # Update average response time
            current_avg = self.communication_stats["average_response_time"]
            delivered_count = self.communication_stats["messages_delivered"]
            new_avg = ((current_avg * (delivered_count - 1)) + delivery_time) / delivered_count
            self.communication_stats["average_response_time"] = new_avg
            
            # Store in relevant channels
            await self._store_message_in_channels(message)
            
            self.logger.debug(
                "Message delivered successfully",
                message_id=message.id,
                delivery_time_ms=delivery_time * 1000
            )
            
        except Exception as e:
            self.logger.error(
                "Message delivery failed",
                message_id=message.id,
                error=str(e)
            )
            self.communication_stats["messages_failed"] += 1
    
    async def _handle_broadcast_message(self, message: AgentMessage) -> None:
        """Handle broadcast message delivery."""
        for agent_id, agent in self.registered_agents.items():
            if agent_id != message.sender_id:  # Don't send to sender
                try:
                    await agent.handle_message(message)
                except Exception as e:
                    self.logger.error(
                        "Broadcast message delivery failed",
                        message_id=message.id,
                        recipient_id=agent_id,
                        error=str(e)
                    )
    
    async def _store_message_in_channels(self, message: AgentMessage) -> None:
        """Store message in relevant communication channels."""
        for channel in self.channels.values():
            if (message.sender_id in channel.participants or 
                message.recipient_id in channel.participants or
                message.recipient_id == "broadcast"):
                
                channel.message_history.append(message)
                
                # Maintain history size limit
                if len(channel.message_history) > channel.max_history_size:
                    channel.message_history = channel.message_history[-channel.max_history_size:]
    
    # Default message handlers
    
    async def _handle_heartbeat(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle agent heartbeat messages."""
        self.logger.debug("Heartbeat received", sender_id=message.sender_id)
        return None
    
    async def _handle_status_update(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle agent status update messages."""
        agent_id = message.sender_id
        status = message.content.get("status")
        
        self.logger.info(
            "Agent status updated",
            agent_id=agent_id,
            status=status
        )
        return None
    
    async def _handle_collaboration_request(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle collaboration request messages."""
        requester_id = message.sender_id
        requested_agents = message.content.get("requested_agents", [])
        task_description = message.content.get("task_description", "")
        
        self.logger.info(
            "Collaboration request received",
            requester_id=requester_id,
            requested_agents=requested_agents,
            task_description=task_description
        )
        
        # Create collaboration session
        try:
            session_id = await self.create_collaboration_session(
                name=f"Collaboration: {task_description[:50]}",
                description=task_description,
                participant_ids=[requester_id] + requested_agents,
                coordinator_id=requester_id
            )
            
            return AgentMessage(
                sender_id="system",
                recipient_id=requester_id,
                message_type="collaboration_response",
                content={
                    "status": "accepted",
                    "session_id": session_id
                },
                correlation_id=message.id
            )
            
        except Exception as e:
            return AgentMessage(
                sender_id="system",
                recipient_id=requester_id,
                message_type="collaboration_response",
                content={
                    "status": "failed",
                    "error": str(e)
                },
                correlation_id=message.id
            )
    
    async def _handle_collaboration_response(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle collaboration response messages."""
        # This would be handled by the requesting agent
        return None
    
    async def _handle_task_delegation(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle task delegation messages."""
        delegator_id = message.sender_id
        task_details = message.content.get("task_details", {})
        
        self.logger.info(
            "Task delegation received",
            delegator_id=delegator_id,
            task_details=task_details
        )
        
        # This would integrate with the workflow orchestrator
        return None
    
    async def _handle_resource_request(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle resource request messages."""
        requester_id = message.sender_id
        resource_type = message.content.get("resource_type")
        resource_details = message.content.get("resource_details", {})
        
        self.logger.info(
            "Resource request received",
            requester_id=requester_id,
            resource_type=resource_type
        )
        
        # This would integrate with resource management
        return None
    
    async def _handle_knowledge_share(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle knowledge sharing messages."""
        sender_id = message.sender_id
        knowledge_type = message.content.get("knowledge_type")
        knowledge_data = message.content.get("data", {})
        
        self.logger.info(
            "Knowledge shared",
            sender_id=sender_id,
            knowledge_type=knowledge_type
        )
        
        # Store knowledge in shared memory or knowledge base
        return None
    
    async def _handle_error_report(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle error report messages."""
        reporter_id = message.sender_id
        error_details = message.content.get("error_details", {})
        
        self.logger.error(
            "Error reported by agent",
            reporter_id=reporter_id,
            error_details=error_details
        )
        
        # This would integrate with error handling and monitoring
        return None
    
    # Monitoring and maintenance
    
    async def _monitor_agent_health(self) -> None:
        """Monitor agent health and connectivity."""
        while self.is_running:
            try:
                # Send heartbeat requests to all agents
                for agent_id in self.registered_agents:
                    await self.send_message(
                        sender_id="system",
                        recipient_id=agent_id,
                        message_type="heartbeat_request",
                        content={"timestamp": datetime.utcnow().isoformat()},
                        priority=MessagePriority.LOW
                    )
                
                # Update communication stats
                self.communication_stats["active_channels"] = len([
                    c for c in self.channels.values() if c.is_active
                ])
                self.communication_stats["active_sessions"] = len([
                    s for s in self.active_sessions.values() if s.status == "running"
                ])
                
            except Exception as e:
                self.logger.error("Error monitoring agent health", error=str(e))
            
            await asyncio.sleep(30)  # Monitor every 30 seconds
    
    async def _cleanup_old_sessions(self) -> None:
        """Clean up old collaboration sessions."""
        while self.is_running:
            try:
                current_time = datetime.utcnow()
                cleanup_threshold = current_time - timedelta(hours=24)
                
                sessions_to_remove = []
                for session_id, session in self.active_sessions.items():
                    if (session.status in ["completed", "cancelled", "failed"] and
                        (session.completed_at or session.created_at) < cleanup_threshold):
                        sessions_to_remove.append(session_id)
                
                for session_id in sessions_to_remove:
                    # Remove session channel
                    channel_id = f"session_{session_id}"
                    if channel_id in self.channels:
                        del self.channels[channel_id]
                    
                    # Remove session
                    del self.active_sessions[session_id]
                    
                    self.logger.info(
                        "Old collaboration session cleaned up",
                        session_id=session_id
                    )
                
            except Exception as e:
                self.logger.error("Error cleaning up old sessions", error=str(e))
            
            await asyncio.sleep(3600)  # Clean up every hour
    
    # Public API methods
    
    def get_communication_stats(self) -> Dict[str, Any]:
        """Get communication statistics."""
        return {
            **self.communication_stats,
            "registered_agents": len(self.registered_agents),
            "total_channels": len(self.channels),
            "total_sessions": len(self.active_sessions),
            "queue_sizes": {
                priority.value: queue.qsize() 
                for priority, queue in self.priority_queues.items()
            }
        }
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get collaboration session information."""
        session = self.active_sessions.get(session_id)
        if not session:
            return None
        
        return {
            "session_id": session_id,
            "name": session.name,
            "description": session.description,
            "status": session.status,
            "participant_count": len(session.participants),
            "coordinator_id": session.coordinator_id,
            "created_at": session.created_at.isoformat(),
            "started_at": session.started_at.isoformat() if session.started_at else None,
            "completed_at": session.completed_at.isoformat() if session.completed_at else None,
            "metrics": session.collaboration_metrics
        }
    
    def get_channel_info(self, channel_id: str) -> Optional[Dict[str, Any]]:
        """Get communication channel information."""
        channel = self.channels.get(channel_id)
        if not channel:
            return None
        
        return {
            "channel_id": channel_id,
            "name": channel.name,
            "protocol": channel.protocol.value,
            "participant_count": len(channel.participants),
            "message_count": len(channel.message_history),
            "created_at": channel.created_at.isoformat(),
            "is_active": channel.is_active
        }


# Global communication hub instance
communication_hub = AgentCommunicationHub()