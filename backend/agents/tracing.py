"""
Phoenix tracing integration for AiLex AI agents.
Provides comprehensive observability and monitoring for agent operations.
"""

import asyncio
import json
import time
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict, List, Optional, Union, AsyncGenerator
from functools import wraps
from dataclasses import dataclass, field

import structlog

# Phoenix tracing imports with fallback
try:
    from phoenix.trace import SpanKind
    from phoenix.trace.semantic_conventions import DocumentAttributes, EmbeddingAttributes
    from phoenix.trace.exporter import HttpExporter
    from phoenix.trace.processor import SimpleSpanProcessor
    from phoenix.trace.tracer import Tracer
    from phoenix.trace.span import Span
    PHOENIX_AVAILABLE = True
except ImportError:
    # Fallback classes when Phoenix is not available
    class SpanKind:
        INTERNAL = "internal"
        CLIENT = "client"
        SERVER = "server"
        PRODUCER = "producer"
        CONSUMER = "consumer"

    class DocumentAttributes:
        DOCUMENT_CONTENT = "document.content"
        DOCUMENT_ID = "document.id"
        DOCUMENT_METADATA = "document.metadata"

    class EmbeddingAttributes:
        EMBEDDING_EMBEDDINGS = "embedding.embeddings"
        EMBEDDING_MODEL_NAME = "embedding.model_name"
        EMBEDDING_TEXT = "embedding.text"

    # Mock classes for when Phoenix is not available
    class HttpExporter:
        def __init__(self, *args, **kwargs):
            pass

    class SimpleSpanProcessor:
        def __init__(self, *args, **kwargs):
            pass

    class Tracer:
        def __init__(self, *args, **kwargs):
            pass

        def start_span(self, *args, **kwargs):
            return MockSpan()

    class Span:
        def __init__(self, *args, **kwargs):
            pass

    class MockSpan:
        def __init__(self):
            pass

        def __enter__(self):
            return self

        def __exit__(self, *args):
            pass

        def set_attribute(self, *args, **kwargs):
            pass

        def set_status(self, *args, **kwargs):
            pass

        def record_exception(self, *args, **kwargs):
            pass

    PHOENIX_AVAILABLE = False
    # Create mock classes for when Phoenix is not available
    class Span:
        def __init__(self, *args, **kwargs):
            pass
        def set_attribute(self, *args, **kwargs):
            pass
        def set_status(self, *args, **kwargs):
            pass
        def add_event(self, *args, **kwargs):
            pass
    
    class Tracer:
        def __init__(self, *args, **kwargs):
            pass
        def start_span(self, *args, **kwargs):
            return Span()

from utils.config import settings


logger = structlog.get_logger(__name__)


@dataclass
class TraceMetrics:
    """Metrics collected during tracing."""
    span_id: str
    trace_id: str
    operation_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_ms: Optional[float] = None
    status: str = "started"
    attributes: Dict[str, Any] = field(default_factory=dict)
    events: List[Dict[str, Any]] = field(default_factory=list)
    error: Optional[str] = None


class PhoenixTracer:
    """
    Phoenix tracing integration for agent operations.
    Provides comprehensive observability and monitoring.
    """
    
    def __init__(self, project_name: str = None, collector_endpoint: str = None):
        self.project_name = project_name or settings.PHOENIX_PROJECT_NAME
        self.collector_endpoint = collector_endpoint or settings.PHOENIX_COLLECTOR_ENDPOINT
        self.enabled = PHOENIX_AVAILABLE and bool(self.collector_endpoint)
        
        self._tracer: Optional[Tracer] = None
        self._processor = None
        self._exporter = None
        self._active_spans: Dict[str, Span] = {}
        self._metrics: Dict[str, TraceMetrics] = {}
        
        if self.enabled:
            self._initialize_tracer()
        else:
            logger.warning(
                "Phoenix tracing disabled",
                phoenix_available=PHOENIX_AVAILABLE,
                collector_endpoint=bool(self.collector_endpoint)
            )
    
    def _initialize_tracer(self) -> None:
        """Initialize Phoenix tracer with configuration."""
        try:
            if not PHOENIX_AVAILABLE:
                return
            
            # Create exporter
            self._exporter = HttpExporter(endpoint=self.collector_endpoint)
            
            # Create processor
            self._processor = SimpleSpanProcessor(span_exporter=self._exporter)
            
            # Create tracer
            self._tracer = Tracer(
                resource_attributes={
                    "service.name": self.project_name,
                    "service.version": "1.0.0",
                    "service.namespace": "ailex-agents"
                }
            )
            
            logger.info(
                "Phoenix tracer initialized",
                project_name=self.project_name,
                collector_endpoint=self.collector_endpoint
            )
            
        except Exception as e:
            logger.error("Failed to initialize Phoenix tracer", error=str(e))
            self.enabled = False
    
    @asynccontextmanager
    async def trace_operation(
        self,
        operation_name: str,
        agent_id: Optional[str] = None,
        task_id: Optional[str] = None,
        campaign_id: Optional[str] = None,
        attributes: Optional[Dict[str, Any]] = None,
        span_kind: SpanKind = SpanKind.INTERNAL
    ) -> AsyncGenerator[Span, None]:
        """
        Context manager for tracing agent operations.
        
        Args:
            operation_name: Name of the operation being traced
            agent_id: ID of the agent performing the operation
            task_id: ID of the task being executed
            campaign_id: ID of the related campaign
            attributes: Additional attributes to attach to the span
            span_kind: Type of span (INTERNAL, CLIENT, SERVER, etc.)
            
        Yields:
            Span: Active span for the operation
        """
        if not self.enabled or not self._tracer:
            # Return a mock span if tracing is disabled
            yield Span()
            return
        
        span_id = f"{operation_name}_{int(time.time() * 1000)}"
        start_time = datetime.utcnow()
        
        try:
            # Start span
            span = self._tracer.start_span(
                name=operation_name,
                kind=span_kind
            )
            
            # Set basic attributes
            span.set_attribute("agent.id", agent_id or "unknown")
            span.set_attribute("agent.operation", operation_name)
            span.set_attribute("service.name", self.project_name)
            span.set_attribute("timestamp", start_time.isoformat())
            
            if task_id:
                span.set_attribute("task.id", task_id)
            if campaign_id:
                span.set_attribute("campaign.id", campaign_id)
            
            # Add custom attributes
            if attributes:
                for key, value in attributes.items():
                    if isinstance(value, (str, int, float, bool)):
                        span.set_attribute(f"custom.{key}", value)
                    else:
                        span.set_attribute(f"custom.{key}", str(value))
            
            # Store span and metrics
            self._active_spans[span_id] = span
            self._metrics[span_id] = TraceMetrics(
                span_id=span_id,
                trace_id=getattr(span, 'trace_id', 'unknown'),
                operation_name=operation_name,
                start_time=start_time,
                attributes=attributes or {}
            )
            
            logger.debug(
                "Started tracing operation",
                operation_name=operation_name,
                span_id=span_id,
                agent_id=agent_id
            )
            
            yield span
            
        except Exception as e:
            # Record error in span
            if span_id in self._active_spans:
                span = self._active_spans[span_id]
                span.set_attribute("error", True)
                span.set_attribute("error.message", str(e))
                span.set_attribute("error.type", type(e).__name__)
                span.add_event("exception", {
                    "exception.message": str(e),
                    "exception.type": type(e).__name__
                })
            
            # Update metrics
            if span_id in self._metrics:
                self._metrics[span_id].error = str(e)
                self._metrics[span_id].status = "error"
            
            logger.error(
                "Error in traced operation",
                operation_name=operation_name,
                span_id=span_id,
                error=str(e)
            )
            raise
            
        finally:
            # Finish span
            end_time = datetime.utcnow()
            duration_ms = (end_time - start_time).total_seconds() * 1000
            
            if span_id in self._active_spans:
                span = self._active_spans[span_id]
                span.set_attribute("duration_ms", duration_ms)
                span.end()
                del self._active_spans[span_id]
            
            # Update metrics
            if span_id in self._metrics:
                metrics = self._metrics[span_id]
                metrics.end_time = end_time
                metrics.duration_ms = duration_ms
                if metrics.status != "error":
                    metrics.status = "completed"
            
            logger.debug(
                "Finished tracing operation",
                operation_name=operation_name,
                span_id=span_id,
                duration_ms=duration_ms
            )
    
    def trace_agent_method(
        self,
        operation_name: Optional[str] = None,
        include_args: bool = False,
        include_result: bool = False
    ):
        """
        Decorator for tracing agent methods.
        
        Args:
            operation_name: Custom operation name (defaults to method name)
            include_args: Whether to include method arguments in trace
            include_result: Whether to include method result in trace
        """
        def decorator(func):
            @wraps(func)
            async def async_wrapper(self, *args, **kwargs):
                op_name = operation_name or f"{self.__class__.__name__}.{func.__name__}"
                agent_id = getattr(self, 'agent_id', None)
                
                attributes = {}
                if include_args:
                    attributes.update({
                        f"arg_{i}": str(arg) for i, arg in enumerate(args)
                    })
                    attributes.update({
                        f"kwarg_{k}": str(v) for k, v in kwargs.items()
                    })
                
                async with self.tracer.trace_operation(
                    operation_name=op_name,
                    agent_id=agent_id,
                    attributes=attributes
                ) as span:
                    try:
                        result = await func(self, *args, **kwargs)
                        
                        if include_result and result is not None:
                            if isinstance(result, (str, int, float, bool)):
                                span.set_attribute("result", result)
                            else:
                                span.set_attribute("result", str(result)[:1000])  # Truncate large results
                        
                        return result
                        
                    except Exception as e:
                        span.add_event("method_error", {
                            "error_message": str(e),
                            "error_type": type(e).__name__
                        })
                        raise
            
            @wraps(func)
            def sync_wrapper(self, *args, **kwargs):
                # For synchronous methods, we can't use async context manager
                # So we'll just log the operation
                op_name = operation_name or f"{self.__class__.__name__}.{func.__name__}"
                agent_id = getattr(self, 'agent_id', None)
                
                logger.debug("Executing traced method", operation=op_name, agent_id=agent_id)
                
                try:
                    return func(self, *args, **kwargs)
                except Exception as e:
                    logger.error("Error in traced method", operation=op_name, error=str(e))
                    raise
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        
        return decorator
    
    def add_event(self, span_id: str, event_name: str, attributes: Dict[str, Any]) -> None:
        """
        Add event to active span.
        
        Args:
            span_id: ID of the span to add event to
            event_name: Name of the event
            attributes: Event attributes
        """
        if not self.enabled or span_id not in self._active_spans:
            return
        
        span = self._active_spans[span_id]
        span.add_event(event_name, attributes)
        
        # Update metrics
        if span_id in self._metrics:
            self._metrics[span_id].events.append({
                "name": event_name,
                "attributes": attributes,
                "timestamp": datetime.utcnow().isoformat()
            })
    
    def set_span_attribute(self, span_id: str, key: str, value: Any) -> None:
        """
        Set attribute on active span.
        
        Args:
            span_id: ID of the span
            key: Attribute key
            value: Attribute value
        """
        if not self.enabled or span_id not in self._active_spans:
            return
        
        span = self._active_spans[span_id]
        span.set_attribute(key, value)
        
        # Update metrics
        if span_id in self._metrics:
            self._metrics[span_id].attributes[key] = value
    
    def get_metrics(self, span_id: Optional[str] = None) -> Union[TraceMetrics, Dict[str, TraceMetrics]]:
        """
        Get tracing metrics.
        
        Args:
            span_id: Specific span ID to get metrics for
            
        Returns:
            TraceMetrics or dict of all metrics
        """
        if span_id:
            return self._metrics.get(span_id)
        return dict(self._metrics)
    
    def get_active_spans(self) -> List[str]:
        """Get list of active span IDs."""
        return list(self._active_spans.keys())
    
    async def shutdown(self) -> None:
        """Shutdown tracer and flush pending spans."""
        if not self.enabled:
            return
        
        try:
            # End all active spans
            for span_id, span in self._active_spans.items():
                span.set_attribute("shutdown", True)
                span.end()
            
            # Clear active spans
            self._active_spans.clear()
            
            # Flush processor
            if self._processor:
                self._processor.shutdown()
            
            logger.info("Phoenix tracer shutdown completed")
            
        except Exception as e:
            logger.error("Error during tracer shutdown", error=str(e))


class AgentTracer:
    """
    Agent-specific tracing wrapper that integrates with Phoenix.
    """
    
    def __init__(self, agent_id: str, agent_name: str, phoenix_tracer: PhoenixTracer):
        self.agent_id = agent_id
        self.agent_name = agent_name
        self.phoenix_tracer = phoenix_tracer
        self.enabled = phoenix_tracer.enabled
        
        logger.debug(
            "Created agent tracer",
            agent_id=agent_id,
            agent_name=agent_name,
            enabled=self.enabled
        )
    
    @asynccontextmanager
    async def trace_task_execution(
        self,
        task_id: str,
        task_name: str,
        campaign_id: Optional[str] = None,
        task_data: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Span, None]:
        """
        Trace task execution for this agent.
        
        Args:
            task_id: Task identifier
            task_name: Task name/description
            campaign_id: Related campaign ID
            task_data: Task input data
            
        Yields:
            Span: Active span for the task
        """
        attributes = {
            "task.name": task_name,
            "agent.name": self.agent_name,
            "agent.type": "ailex_agent"
        }
        
        if task_data:
            # Add sanitized task data
            for key, value in task_data.items():
                if isinstance(value, (str, int, float, bool)):
                    attributes[f"task.input.{key}"] = value
                elif isinstance(value, (list, dict)):
                    attributes[f"task.input.{key}"] = json.dumps(value)[:500]  # Truncate large data
        
        async with self.phoenix_tracer.trace_operation(
            operation_name=f"agent_task_{task_name}",
            agent_id=self.agent_id,
            task_id=task_id,
            campaign_id=campaign_id,
            attributes=attributes,
            span_kind=SpanKind.INTERNAL
        ) as span:
            yield span
    
    @asynccontextmanager
    async def trace_llm_call(
        self,
        model_name: str,
        prompt: str,
        temperature: float,
        max_tokens: int
    ) -> AsyncGenerator[Span, None]:
        """
        Trace LLM API calls.
        
        Args:
            model_name: Name of the LLM model
            prompt: Input prompt
            temperature: Model temperature
            max_tokens: Maximum tokens
            
        Yields:
            Span: Active span for the LLM call
        """
        attributes = {
            "llm.model": model_name,
            "llm.temperature": temperature,
            "llm.max_tokens": max_tokens,
            "llm.prompt_length": len(prompt),
            "llm.prompt_preview": prompt[:200] + "..." if len(prompt) > 200 else prompt
        }
        
        async with self.phoenix_tracer.trace_operation(
            operation_name=f"llm_call_{model_name}",
            agent_id=self.agent_id,
            attributes=attributes,
            span_kind=SpanKind.CLIENT
        ) as span:
            yield span
    
    @asynccontextmanager
    async def trace_tool_usage(
        self,
        tool_name: str,
        tool_input: Dict[str, Any]
    ) -> AsyncGenerator[Span, None]:
        """
        Trace tool usage by agent.
        
        Args:
            tool_name: Name of the tool being used
            tool_input: Tool input parameters
            
        Yields:
            Span: Active span for the tool usage
        """
        attributes = {
            "tool.name": tool_name,
            "tool.input_keys": list(tool_input.keys()),
            "tool.input_size": len(str(tool_input))
        }
        
        # Add sanitized tool input
        for key, value in tool_input.items():
            if isinstance(value, (str, int, float, bool)):
                attributes[f"tool.input.{key}"] = value
            else:
                attributes[f"tool.input.{key}"] = str(value)[:200]
        
        async with self.phoenix_tracer.trace_operation(
            operation_name=f"tool_usage_{tool_name}",
            agent_id=self.agent_id,
            attributes=attributes,
            span_kind=SpanKind.CLIENT
        ) as span:
            yield span
    
    def record_task_result(self, span: Span, result: Dict[str, Any], success: bool) -> None:
        """
        Record task execution result.
        
        Args:
            span: Active span
            result: Task result
            success: Whether task was successful
        """
        if not self.enabled:
            return
        
        span.set_attribute("task.success", success)
        span.set_attribute("task.result_size", len(str(result)))
        
        if success:
            span.set_attribute("task.status", "completed")
        else:
            span.set_attribute("task.status", "failed")
            if "error" in result:
                span.set_attribute("task.error", str(result["error"]))
        
        # Add result preview
        if isinstance(result.get("output"), str):
            preview = result["output"][:300] + "..." if len(result["output"]) > 300 else result["output"]
            span.set_attribute("task.output_preview", preview)
    
    def record_llm_response(self, span: Span, response: str, token_count: int, cost: float = None) -> None:
        """
        Record LLM response details.
        
        Args:
            span: Active span
            response: LLM response text
            token_count: Number of tokens in response
            cost: API call cost (if available)
        """
        if not self.enabled:
            return
        
        span.set_attribute("llm.response_length", len(response))
        span.set_attribute("llm.response_tokens", token_count)
        span.set_attribute("llm.response_preview", response[:300] + "..." if len(response) > 300 else response)
        
        if cost is not None:
            span.set_attribute("llm.cost", cost)
    
    def record_tool_result(self, span: Span, result: Any, success: bool, error: str = None) -> None:
        """
        Record tool usage result.
        
        Args:
            span: Active span
            result: Tool result
            success: Whether tool usage was successful
            error: Error message if failed
        """
        if not self.enabled:
            return
        
        span.set_attribute("tool.success", success)
        
        if success:
            span.set_attribute("tool.result_type", type(result).__name__)
            span.set_attribute("tool.result_size", len(str(result)))
            if isinstance(result, str):
                preview = result[:200] + "..." if len(result) > 200 else result
                span.set_attribute("tool.result_preview", preview)
        else:
            span.set_attribute("tool.error", error or "Unknown error")


# Global tracer instance
_global_tracer: Optional[PhoenixTracer] = None


def get_phoenix_tracer() -> PhoenixTracer:
    """Get global Phoenix tracer instance."""
    global _global_tracer
    if _global_tracer is None:
        _global_tracer = PhoenixTracer()
    return _global_tracer


def create_agent_tracer(agent_id: str, agent_name: str) -> AgentTracer:
    """Create agent-specific tracer."""
    phoenix_tracer = get_phoenix_tracer()
    return AgentTracer(agent_id, agent_name, phoenix_tracer)


async def shutdown_tracing():
    """Shutdown global tracing system."""
    global _global_tracer
    if _global_tracer:
        await _global_tracer.shutdown()
        _global_tracer = None