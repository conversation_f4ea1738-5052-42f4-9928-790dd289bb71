"""
Google Ads Specific Workflow Definitions for AiLex Ad Agent System.
Defines standard workflows for Google Ads campaign management and optimization.
"""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

import structlog

from .orchestration import WorkflowDefinition, WorkflowStep, ExecutionStrategy
from .factory import Agent<PERSON>ole
from models.agents import AgentType, TaskPriority


logger = structlog.get_logger(__name__)


class CampaignType(str, Enum):
    """Google Ads campaign types."""
    SEARCH = "search"
    DISPLAY = "display"
    SHOPPING = "shopping"
    VIDEO = "video"
    PERFORMANCE_MAX = "performance_max"
    SMART = "smart"
    DISCOVERY = "discovery"


class WorkflowType(str, Enum):
    """Available workflow types."""
    NEW_CAMPAIGN_CREATION = "new_campaign_creation"
    CAMPAIGN_OPTIMIZATION = "campaign_optimization"
    KEYWORD_EXPANSION = "keyword_expansion"
    AD_TESTING = "ad_testing"
    BUDGET_REALLOCATION = "budget_reallocation"
    COMPETITOR_RESPONSE = "competitor_response"
    SEASONAL_ADJUSTMENT = "seasonal_adjustment"
    QUALITY_SCORE_IMPROVEMENT = "quality_score_improvement"


@dataclass
class WorkflowContext:
    """Context data for workflow execution."""
    campaign_id: Optional[str] = None
    campaign_type: Optional[CampaignType] = None
    budget: Optional[float] = None
    target_audience: Optional[Dict[str, Any]] = None
    business_objectives: Optional[List[str]] = None
    constraints: Optional[Dict[str, Any]] = None
    performance_targets: Optional[Dict[str, float]] = None


class GoogleAdsWorkflowBuilder:
    """
    Builder for creating Google Ads specific workflows.
    """
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
    
    def build_new_campaign_workflow(
        self,
        campaign_type: CampaignType,
        context: WorkflowContext
    ) -> WorkflowDefinition:
        """
        Build workflow for creating new Google Ads campaign.
        
        Args:
            campaign_type: Type of campaign to create
            context: Workflow context and parameters
            
        Returns:
            WorkflowDefinition: Complete workflow definition
        """
        workflow_id = f"new_{campaign_type.value}_campaign_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        steps = []
        
        # Step 1: Market Research and Analysis
        steps.append(WorkflowStep(
            id="market_research",
            name="Market Research and Competitive Analysis",
            agent_type=AgentType.CAMPAIGN_PLANNING,
            description="Conduct comprehensive market research, competitor analysis, and audience insights",
            input_schema={
                "business_context": "dict",
                "target_markets": "list",
                "competitor_list": "list"
            },
            output_schema={
                "market_insights": "dict",
                "competitor_analysis": "dict",
                "audience_profiles": "dict"
            },
            timeout_seconds=1800,  # 30 minutes
            retry_attempts=2,
            priority=TaskPriority.HIGH,
            metadata={
                "research_depth": "comprehensive",
                "include_trends": True,
                "competitor_count": 5
            }
        ))
        
        # Step 2: Keyword Research and Strategy
        steps.append(WorkflowStep(
            id="keyword_research",
            name="Keyword Research and Strategy Development",
            agent_type=AgentType.CAMPAIGN_PLANNING,
            description="Develop comprehensive keyword strategy with search volume and competition analysis",
            dependencies=["market_research"],
            input_schema={
                "business_description": "string",
                "target_audience": "dict",
                "campaign_objectives": "list"
            },
            output_schema={
                "keyword_clusters": "dict",
                "negative_keywords": "list",
                "bid_recommendations": "dict"
            },
            timeout_seconds=1200,  # 20 minutes
            retry_attempts=2,
            priority=TaskPriority.HIGH,
            metadata={
                "include_long_tail": True,
                "competitor_keywords": True,
                "seasonal_analysis": True
            }
        ))
        
        # Step 3: Campaign Structure Planning
        steps.append(WorkflowStep(
            id="campaign_structure",
            name="Campaign Structure and Ad Group Planning",
            agent_type=AgentType.CAMPAIGN_PLANNING,
            description="Design optimal campaign structure with ad groups and targeting settings",
            dependencies=["market_research", "keyword_research"],
            input_schema={
                "keyword_clusters": "dict",
                "budget_allocation": "dict",
                "targeting_preferences": "dict"
            },
            output_schema={
                "campaign_structure": "dict",
                "ad_group_setup": "dict",
                "targeting_strategy": "dict"
            },
            timeout_seconds=900,  # 15 minutes
            retry_attempts=2,
            priority=TaskPriority.HIGH
        ))
        
        # Step 4: Creative Asset Generation
        steps.append(WorkflowStep(
            id="creative_generation",
            name="Ad Creative Asset Generation",
            agent_type=AgentType.AD_ASSET_GENERATION,
            description="Generate ad creatives, headlines, descriptions, and assets",
            dependencies=["campaign_structure"],
            input_schema={
                "campaign_structure": "dict",
                "brand_guidelines": "dict",
                "messaging_framework": "dict"
            },
            output_schema={
                "ad_creatives": "list",
                "asset_variations": "dict",
                "creative_recommendations": "list"
            },
            timeout_seconds=2400,  # 40 minutes
            retry_attempts=2,
            priority=TaskPriority.NORMAL,
            metadata={
                "variation_count": 5,
                "asset_types": ["headlines", "descriptions", "images"],
                "optimization_focus": "performance"
            }
        ))
        
        # Step 5: Quality Assurance and Compliance Review
        steps.append(WorkflowStep(
            id="quality_review",
            name="Quality Assurance and Compliance Check",
            agent_type=AgentType.QUALITY_ASSURANCE,
            description="Review campaign setup and assets for quality and compliance",
            dependencies=["creative_generation"],
            input_schema={
                "campaign_config": "dict",
                "ad_creatives": "list",
                "compliance_requirements": "list"
            },
            output_schema={
                "quality_report": "dict",
                "compliance_status": "dict",
                "recommendations": "list"
            },
            timeout_seconds=600,  # 10 minutes
            retry_attempts=1,
            priority=TaskPriority.HIGH,
            metadata={
                "check_brand_compliance": True,
                "validate_targeting": True,
                "review_budget_allocation": True
            }
        ))
        
        # Step 6: Campaign Implementation
        steps.append(WorkflowStep(
            id="campaign_implementation",
            name="Google Ads Campaign Implementation",
            agent_type=AgentType.CAMPAIGN_PLANNING,
            description="Implement campaign in Google Ads platform",
            dependencies=["quality_review"],
            input_schema={
                "approved_campaign": "dict",
                "approved_creatives": "list",
                "implementation_settings": "dict"
            },
            output_schema={
                "campaign_id": "string",
                "implementation_status": "dict",
                "live_campaign_urls": "list"
            },
            timeout_seconds=1200,  # 20 minutes
            retry_attempts=3,
            priority=TaskPriority.URGENT,
            conditions=["quality_review.compliance_status.approved == true"],
            metadata={
                "auto_activate": context.constraints.get("auto_activate", False),
                "bid_strategy": campaign_type.value,
                "tracking_setup": True
            }
        ))
        
        # Step 7: Initial Monitoring Setup
        steps.append(WorkflowStep(
            id="monitoring_setup",
            name="Campaign Monitoring and Alert Setup",
            agent_type=AgentType.PERFORMANCE_ANALYSIS,
            description="Set up monitoring, tracking, and alert systems",
            dependencies=["campaign_implementation"],
            input_schema={
                "campaign_id": "string",
                "performance_targets": "dict",
                "alert_thresholds": "dict"
            },
            output_schema={
                "monitoring_config": "dict",
                "alert_setup": "dict",
                "dashboard_urls": "list"
            },
            timeout_seconds=600,  # 10 minutes
            retry_attempts=2,
            priority=TaskPriority.NORMAL,
            metadata={
                "real_time_monitoring": True,
                "automated_reports": True,
                "performance_alerts": True
            }
        ))
        
        # Determine execution strategy based on campaign type
        if campaign_type in [CampaignType.SMART, CampaignType.PERFORMANCE_MAX]:
            strategy = ExecutionStrategy.SEQUENTIAL  # These need careful sequential setup
        else:
            strategy = ExecutionStrategy.PIPELINE  # Allow some parallelization
        
        return WorkflowDefinition(
            id=workflow_id,
            name=f"New {campaign_type.value.title()} Campaign Creation",
            description=f"Complete workflow for creating new {campaign_type.value} Google Ads campaign",
            version="1.0",
            steps=steps,
            strategy=strategy,
            max_duration_seconds=7200,  # 2 hours
            error_handling="retry",
            metadata={
                "campaign_type": campaign_type.value,
                "estimated_duration": "90-120 minutes",
                "complexity": "high",
                "requires_approval": True,
                "context": context.__dict__ if context else {}
            }
        )
    
    def build_campaign_optimization_workflow(
        self,
        campaign_id: str,
        optimization_goals: List[str],
        context: WorkflowContext
    ) -> WorkflowDefinition:
        """
        Build workflow for optimizing existing Google Ads campaign.
        
        Args:
            campaign_id: ID of campaign to optimize
            optimization_goals: Specific optimization objectives
            context: Workflow context
            
        Returns:
            WorkflowDefinition: Optimization workflow definition
        """
        workflow_id = f"optimize_campaign_{campaign_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        steps = []
        
        # Step 1: Performance Analysis
        steps.append(WorkflowStep(
            id="performance_analysis",
            name="Campaign Performance Analysis",
            agent_type=AgentType.PERFORMANCE_ANALYSIS,
            description="Analyze current campaign performance and identify optimization opportunities",
            input_schema={
                "campaign_id": "string",
                "analysis_period": "dict",
                "performance_metrics": "list"
            },
            output_schema={
                "performance_report": "dict",
                "optimization_opportunities": "list",
                "benchmark_comparison": "dict"
            },
            timeout_seconds=900,  # 15 minutes
            priority=TaskPriority.HIGH,
            metadata={
                "include_historical_data": True,
                "competitor_benchmarking": True,
                "trend_analysis": True
            }
        ))
        
        # Step 2: Keyword Optimization
        steps.append(WorkflowStep(
            id="keyword_optimization",
            name="Keyword Performance Optimization",
            agent_type=AgentType.KEYWORD_RESEARCH,
            description="Optimize keyword targeting, bids, and negative keyword lists",
            dependencies=["performance_analysis"],
            input_schema={
                "current_keywords": "list",
                "performance_data": "dict",
                "optimization_goals": "list"
            },
            output_schema={
                "keyword_recommendations": "dict",
                "bid_adjustments": "dict",
                "negative_keyword_additions": "list"
            },
            timeout_seconds=1200,  # 20 minutes
            priority=TaskPriority.HIGH,
            metadata={
                "include_search_terms": True,
                "bid_optimization": True,
                "quality_score_focus": True
            }
        ))
        
        # Step 3: Ad Creative Optimization
        steps.append(WorkflowStep(
            id="creative_optimization",
            name="Ad Creative Performance Optimization",
            agent_type=AgentType.AD_ASSET_GENERATION,
            description="Optimize ad creatives based on performance data",
            dependencies=["performance_analysis"],
            input_schema={
                "current_ads": "list",
                "performance_metrics": "dict",
                "audience_insights": "dict"
            },
            output_schema={
                "creative_recommendations": "list",
                "new_ad_variations": "list",
                "pause_recommendations": "list"
            },
            timeout_seconds=1800,  # 30 minutes
            priority=TaskPriority.NORMAL,
            metadata={
                "a_b_testing": True,
                "creative_refresh": True,
                "performance_focus": True
            }
        ))
        
        # Step 4: Bid and Budget Optimization
        steps.append(WorkflowStep(
            id="bid_budget_optimization",
            name="Bid Strategy and Budget Optimization",
            agent_type=AgentType.BID_OPTIMIZATION,
            description="Optimize bidding strategies and budget allocation",
            dependencies=["performance_analysis", "keyword_optimization"],
            input_schema={
                "current_bids": "dict",
                "budget_performance": "dict",
                "conversion_data": "dict"
            },
            output_schema={
                "bid_strategy_recommendations": "dict",
                "budget_reallocation": "dict",
                "automated_bidding_setup": "dict"
            },
            timeout_seconds=900,  # 15 minutes
            priority=TaskPriority.HIGH,
            metadata={
                "smart_bidding": True,
                "budget_optimization": True,
                "roi_focus": True
            }
        ))
        
        # Step 5: Implementation and Testing
        steps.append(WorkflowStep(
            id="optimization_implementation",
            name="Apply Optimization Changes",
            agent_type=AgentType.CAMPAIGN_PLANNING,
            description="Implement approved optimization changes",
            dependencies=["keyword_optimization", "creative_optimization", "bid_budget_optimization"],
            input_schema={
                "approved_changes": "dict",
                "implementation_schedule": "dict",
                "testing_parameters": "dict"
            },
            output_schema={
                "implementation_status": "dict",
                "change_log": "list",
                "monitoring_alerts": "dict"
            },
            timeout_seconds=1200,  # 20 minutes
            priority=TaskPriority.URGENT,
            conditions=["optimization_changes.approved == true"],
            metadata={
                "gradual_rollout": True,
                "change_tracking": True,
                "rollback_plan": True
            }
        ))
        
        return WorkflowDefinition(
            id=workflow_id,
            name=f"Campaign Optimization - {campaign_id}",
            description="Comprehensive campaign performance optimization workflow",
            version="1.0",
            steps=steps,
            strategy=ExecutionStrategy.HYBRID,
            max_duration_seconds=4800,  # 80 minutes
            error_handling="continue",
            metadata={
                "campaign_id": campaign_id,
                "optimization_goals": optimization_goals,
                "estimated_duration": "60-80 minutes",
                "complexity": "medium",
                "context": context.__dict__ if context else {}
            }
        )
    
    def build_keyword_expansion_workflow(
        self,
        campaign_id: str,
        expansion_criteria: Dict[str, Any]
    ) -> WorkflowDefinition:
        """Build workflow for keyword expansion."""
        workflow_id = f"keyword_expansion_{campaign_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        steps = [
            WorkflowStep(
                id="search_term_analysis",
                name="Search Term Analysis",
                agent_type=AgentType.KEYWORD_RESEARCH,
                description="Analyze search terms to identify expansion opportunities",
                timeout_seconds=900,
                priority=TaskPriority.HIGH
            ),
            WorkflowStep(
                id="competitor_keyword_analysis",
                name="Competitor Keyword Gap Analysis",
                agent_type=AgentType.COMPETITOR_ANALYSIS,
                description="Identify keyword gaps compared to competitors",
                dependencies=["search_term_analysis"],
                timeout_seconds=1200,
                priority=TaskPriority.NORMAL
            ),
            WorkflowStep(
                id="keyword_research_expansion",
                name="New Keyword Research",
                agent_type=AgentType.KEYWORD_RESEARCH,
                description="Research and validate new keyword opportunities",
                dependencies=["search_term_analysis", "competitor_keyword_analysis"],
                timeout_seconds=1500,
                priority=TaskPriority.HIGH
            ),
            WorkflowStep(
                id="keyword_implementation",
                name="Implement New Keywords",
                agent_type=AgentType.CAMPAIGN_PLANNING,
                description="Add new keywords to campaigns with appropriate bids",
                dependencies=["keyword_research_expansion"],
                timeout_seconds=600,
                priority=TaskPriority.NORMAL
            )
        ]
        
        return WorkflowDefinition(
            id=workflow_id,
            name="Keyword Expansion Workflow",
            description="Expand keyword targeting for improved reach and performance",
            steps=steps,
            strategy=ExecutionStrategy.SEQUENTIAL,
            max_duration_seconds=3600,
            metadata={"campaign_id": campaign_id, "expansion_criteria": expansion_criteria}
        )
    
    def build_ad_testing_workflow(
        self,
        campaign_id: str,
        test_parameters: Dict[str, Any]
    ) -> WorkflowDefinition:
        """Build workflow for A/B testing ad creatives."""
        workflow_id = f"ad_testing_{campaign_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        steps = [
            WorkflowStep(
                id="test_design",
                name="A/B Test Design",
                agent_type=AgentType.AD_ASSET_GENERATION,
                description="Design A/B test structure and variations",
                timeout_seconds=600,
                priority=TaskPriority.HIGH
            ),
            WorkflowStep(
                id="creative_generation",
                name="Generate Test Creatives",
                agent_type=AgentType.AD_ASSET_GENERATION,
                description="Generate creative variations for testing",
                dependencies=["test_design"],
                timeout_seconds=1800,
                priority=TaskPriority.NORMAL
            ),
            WorkflowStep(
                id="test_implementation",
                name="Implement A/B Test",
                agent_type=AgentType.CAMPAIGN_PLANNING,
                description="Set up and launch A/B test in campaigns",
                dependencies=["creative_generation"],
                timeout_seconds=900,
                priority=TaskPriority.HIGH
            ),
            WorkflowStep(
                id="test_monitoring",
                name="Monitor Test Performance",
                agent_type=AgentType.PERFORMANCE_ANALYSIS,
                description="Monitor and analyze test performance",
                dependencies=["test_implementation"],
                timeout_seconds=300,
                priority=TaskPriority.LOW
            )
        ]
        
        return WorkflowDefinition(
            id=workflow_id,
            name="Ad Creative A/B Testing",
            description="Systematic A/B testing of ad creative variations",
            steps=steps,
            strategy=ExecutionStrategy.SEQUENTIAL,
            max_duration_seconds=2700,
            metadata={"campaign_id": campaign_id, "test_parameters": test_parameters}
        )
    
    def get_workflow_recommendations(
        self,
        campaign_data: Dict[str, Any],
        performance_metrics: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Get workflow recommendations based on campaign data and performance.
        
        Args:
            campaign_data: Current campaign configuration and data
            performance_metrics: Recent performance metrics
            
        Returns:
            List[Dict[str, Any]]: Recommended workflows with priorities and reasons
        """
        recommendations = []
        
        # Analyze performance metrics
        ctr = performance_metrics.get("ctr", 0)
        conversion_rate = performance_metrics.get("conversion_rate", 0)
        cpc = performance_metrics.get("cpc", 0)
        roas = performance_metrics.get("roas", 0)
        
        # Campaign age
        campaign_age_days = (datetime.utcnow() - datetime.fromisoformat(
            campaign_data.get("created_date", datetime.utcnow().isoformat())
        )).days
        
        # Recommendation logic
        if ctr < 2.0:  # Low CTR
            recommendations.append({
                "workflow_type": WorkflowType.AD_TESTING,
                "priority": "high",
                "reason": "Low click-through rate indicates need for creative optimization",
                "expected_improvement": "15-30% CTR increase",
                "estimated_duration": "45 minutes"
            })
        
        if conversion_rate < 3.0:  # Low conversion rate
            recommendations.append({
                "workflow_type": WorkflowType.KEYWORD_EXPANSION,
                "priority": "medium",
                "reason": "Low conversion rate suggests need for better keyword targeting",
                "expected_improvement": "10-25% conversion rate increase", 
                "estimated_duration": "60 minutes"
            })
        
        if roas < 4.0:  # Low ROAS
            recommendations.append({
                "workflow_type": WorkflowType.CAMPAIGN_OPTIMIZATION,
                "priority": "high",
                "reason": "Low ROAS indicates comprehensive optimization needed",
                "expected_improvement": "20-40% ROAS improvement",
                "estimated_duration": "80 minutes"
            })
        
        if campaign_age_days > 30 and not campaign_data.get("last_optimization"):
            recommendations.append({
                "workflow_type": WorkflowType.CAMPAIGN_OPTIMIZATION,
                "priority": "medium", 
                "reason": "Campaign has not been optimized in over 30 days",
                "expected_improvement": "10-20% overall performance",
                "estimated_duration": "80 minutes"
            })
        
        # Seasonal recommendations
        current_month = datetime.utcnow().month
        if current_month in [11, 12, 1]:  # Holiday season
            recommendations.append({
                "workflow_type": WorkflowType.SEASONAL_ADJUSTMENT,
                "priority": "medium",
                "reason": "Holiday season requires seasonal campaign adjustments",
                "expected_improvement": "Seasonal performance optimization",
                "estimated_duration": "45 minutes"
            })
        
        return recommendations


# Global workflow builder instance
workflow_builder = GoogleAdsWorkflowBuilder()