"""
Quality Assurance Agent for Google Ads Campaign Management.
Handles campaign validation, compliance checking, and quality optimization.
"""

import asyncio
import json
import re
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union, Set
from dataclasses import dataclass, field
from enum import Enum

import structlog
from crewai import Agent, Task

from ..base import BaseAiLexAgent, AgentContext, AgentError
from ..tracing import AgentTracer, create_agent_tracer
from models.agents import AgentType, AgentConfig
from services.google_ads import GoogleAdsService
from services.openai_service import OpenAIService
from utils.config import settings


logger = structlog.get_logger(__name__)


class QualityCheckType(str, Enum):
    """Types of quality checks."""
    COMPLIANCE = "compliance"
    POLICY = "policy"
    PERFORMANCE = "performance"
    STRUCTURE = "structure"
    CONTENT = "content"
    TECHNICAL = "technical"
    ACCESSIBILITY = "accessibility"
    BRAND_SAFETY = "brand_safety"


class SeverityLevel(str, Enum):
    """Severity levels for quality issues."""
    CRITICAL = "critical"  # Blocks campaign launch
    HIGH = "high"         # Major performance impact
    MEDIUM = "medium"     # Moderate impact
    LOW = "low"          # Minor optimization opportunity
    INFO = "info"        # Information only


class QualityStatus(str, Enum):
    """Quality check status."""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    PENDING = "pending"
    SKIPPED = "skipped"


@dataclass
class QualityIssue:
    """Individual quality issue found during checks."""
    issue_id: str
    check_type: QualityCheckType
    severity: SeverityLevel
    title: str
    description: str
    affected_entity: str  # campaign, ad_group, keyword, ad, etc.
    entity_id: str
    location: Optional[str] = None  # Specific location within entity
    current_value: Optional[str] = None
    recommended_value: Optional[str] = None
    fix_instructions: List[str] = field(default_factory=list)
    auto_fixable: bool = False
    policy_reference: Optional[str] = None
    impact_assessment: str = ""
    detected_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class QualityCheckResult:
    """Result of a quality check run."""
    check_id: str
    check_type: QualityCheckType
    entity_type: str
    entity_id: str
    status: QualityStatus
    score: Optional[float] = None  # 0-100 quality score
    issues_found: List[QualityIssue] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    execution_time_seconds: float = 0.0
    checked_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class QualityReport:
    """Comprehensive quality assurance report."""
    report_id: str
    account_id: str
    campaign_ids: List[str]
    check_types_run: List[QualityCheckType]
    overall_quality_score: float
    total_issues: int
    critical_issues: int
    high_issues: int
    medium_issues: int
    low_issues: int
    check_results: List[QualityCheckResult]
    all_issues: List[QualityIssue]
    auto_fix_summary: Dict[str, int]
    compliance_status: str
    recommendations_summary: List[str]
    next_review_date: datetime
    created_at: datetime = field(default_factory=datetime.utcnow)


class QualityAssuranceAgent(BaseAiLexAgent):
    """
    AI agent specialized in Google Ads campaign quality assurance, compliance checking, and optimization validation.
    """
    
    def __init__(self, agent_id: str, config: AgentConfig):
        super().__init__(
            agent_id=agent_id,
            name="Quality Assurance Agent",
            description="Specialized AI agent for Google Ads campaign quality assurance, policy compliance, performance validation, and optimization recommendations",
            agent_type=AgentType.QUALITY_ASSURANCE,
            config=config
        )
        
        # Initialize services
        self.google_ads_service: Optional[GoogleAdsService] = None
        self.openai_service: Optional[OpenAIService] = None
        
        # Initialize tracer
        self.tracer = create_agent_tracer(self.agent_id, self.name)
        
        # Quality standards and thresholds
        self.quality_thresholds = {
            "min_quality_score": 5.0,
            "min_ctr_threshold": 1.0,
            "max_cpc_threshold": 10.0,
            "min_conversion_rate": 1.0,
            "max_cpa_threshold": 100.0,
            "min_impression_share": 50.0,
            "max_bounce_rate": 70.0,
            "min_ad_relevance_score": 3.0,
            "min_landing_page_experience_score": 3.0
        }
        
        # Google Ads policy rules
        self.policy_rules = {
            "prohibited_content": [
                "adult_content", "alcohol", "tobacco", "gambling", "healthcare",
                "political", "weapons", "explosives", "illegal_substances"
            ],
            "prohibited_words": [
                "click here", "free money", "guaranteed", "miracle", "secret",
                "limited time", "act now", "urgent", "exclusive", "amazing"
            ],
            "character_limits": {
                "headline_1": 30,
                "headline_2": 30,
                "headline_3": 30,
                "description_1": 90,
                "description_2": 90,
                "path_1": 15,
                "path_2": 15,
                "display_url": 35
            },
            "required_disclaimers": {
                "financial_services": "Investment involves risk",
                "healthcare": "Consult your healthcare provider",
                "legal_services": "Legal outcomes not guaranteed"
            }
        }
        
        # Content quality patterns
        self.content_quality_patterns = {
            "spelling_errors": re.compile(r'\b(teh|recieve|seperate|neccessary|occured)\b', re.IGNORECASE),
            "excessive_caps": re.compile(r'[A-Z]{4,}'),
            "excessive_punctuation": re.compile(r'[!?]{2,}'),
            "price_mentions": re.compile(r'\$\d+|\d+\s*dollars?|\d+\s*USD', re.IGNORECASE),
            "promotional_language": re.compile(r'\b(buy now|call now|order today|limited time)\b', re.IGNORECASE)
        }
        
        # Check registry
        self.quality_checks = {
            QualityCheckType.COMPLIANCE: self._check_compliance,
            QualityCheckType.POLICY: self._check_policy_adherence,
            QualityCheckType.PERFORMANCE: self._check_performance_quality,
            QualityCheckType.STRUCTURE: self._check_campaign_structure,
            QualityCheckType.CONTENT: self._check_content_quality,
            QualityCheckType.TECHNICAL: self._check_technical_setup,
            QualityCheckType.ACCESSIBILITY: self._check_accessibility,
            QualityCheckType.BRAND_SAFETY: self._check_brand_safety
        }
        
        # Issue tracking
        self.issue_history: Dict[str, List[QualityIssue]] = {}
        self.auto_fix_results: Dict[str, bool] = {}
    
    async def _custom_initialize(self) -> None:
        """Custom initialization for quality assurance agent."""
        try:
            # Initialize Google Ads service
            if all([
                settings.GOOGLE_ADS_DEVELOPER_TOKEN,
                settings.GOOGLE_ADS_CLIENT_ID,
                settings.GOOGLE_ADS_CLIENT_SECRET,
                settings.GOOGLE_ADS_REFRESH_TOKEN
            ]):
                self.google_ads_service = GoogleAdsService()
            
            # Initialize OpenAI service
            if settings.OPENAI_API_KEY:
                self.openai_service = OpenAIService()
            
            self.logger.info(
                "Quality assurance agent initialized",
                has_google_ads=bool(self.google_ads_service),
                has_openai=bool(self.openai_service),
                quality_checks=len(self.quality_checks),
                policy_rules=len(self.policy_rules)
            )
            
        except Exception as e:
            raise AgentError(f"Failed to initialize quality assurance agent: {str(e)}")
    
    async def run_comprehensive_quality_check(
        self,
        account_id: str,
        campaign_ids: List[str],
        check_types: Optional[List[QualityCheckType]] = None,
        include_auto_fix: bool = False
    ) -> QualityReport:
        """
        Run comprehensive quality checks on campaigns.
        
        Args:
            account_id: Google Ads account ID
            campaign_ids: Campaigns to check
            check_types: Specific types of checks to run (all if None)
            include_auto_fix: Whether to attempt automatic fixes
            
        Returns:
            QualityReport: Comprehensive quality assessment report
        """
        async with self.tracer.trace_task_execution(
            task_id=f"qa_comprehensive_{account_id}_{hash(str(campaign_ids))}",
            task_name="Comprehensive Quality Check",
            task_data={
                "account_id": account_id,
                "campaign_count": len(campaign_ids),
                "check_types": [ct.value for ct in check_types] if check_types else "all",
                "include_auto_fix": include_auto_fix
            }
        ) as span:
            try:
                self.logger.info(
                    "Starting comprehensive quality check",
                    account_id=account_id,
                    campaign_count=len(campaign_ids),
                    check_types=check_types or "all"
                )
                
                # Determine which checks to run
                checks_to_run = check_types or list(QualityCheckType)
                
                # Run all quality checks
                all_check_results = []
                all_issues = []
                
                for campaign_id in campaign_ids:
                    for check_type in checks_to_run:
                        try:
                            check_result = await self._run_single_quality_check(
                                campaign_id, check_type
                            )
                            all_check_results.append(check_result)
                            all_issues.extend(check_result.issues_found)
                            
                        except Exception as e:
                            self.logger.warning(
                                "Quality check failed",
                                campaign_id=campaign_id,
                                check_type=check_type.value,
                                error=str(e)
                            )
                
                # Attempt auto-fixes if requested
                auto_fix_summary = {}
                if include_auto_fix:
                    auto_fix_summary = await self._attempt_auto_fixes(all_issues)
                
                # Calculate quality scores and metrics
                quality_metrics = await self._calculate_quality_metrics(
                    all_check_results, all_issues
                )
                
                # Generate compliance status
                compliance_status = await self._determine_compliance_status(all_issues)
                
                # Generate summary recommendations
                recommendations_summary = await self._generate_quality_recommendations(
                    all_issues, quality_metrics
                )
                
                # Create comprehensive report
                report = QualityReport(
                    report_id=f"qa_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                    account_id=account_id,
                    campaign_ids=campaign_ids,
                    check_types_run=checks_to_run,
                    overall_quality_score=quality_metrics["overall_score"],
                    total_issues=len(all_issues),
                    critical_issues=len([i for i in all_issues if i.severity == SeverityLevel.CRITICAL]),
                    high_issues=len([i for i in all_issues if i.severity == SeverityLevel.HIGH]),
                    medium_issues=len([i for i in all_issues if i.severity == SeverityLevel.MEDIUM]),
                    low_issues=len([i for i in all_issues if i.severity == SeverityLevel.LOW]),
                    check_results=all_check_results,
                    all_issues=all_issues,
                    auto_fix_summary=auto_fix_summary,
                    compliance_status=compliance_status,
                    recommendations_summary=recommendations_summary,
                    next_review_date=datetime.utcnow() + timedelta(days=7)
                )
                
                self.tracer.record_task_result(span, {
                    "report_id": report.report_id,
                    "overall_quality_score": quality_metrics["overall_score"],
                    "total_issues": len(all_issues),
                    "critical_issues": report.critical_issues,
                    "compliance_status": compliance_status,
                    "checks_run": len(all_check_results)
                }, True)
                
                self.logger.info(
                    "Comprehensive quality check completed",
                    report_id=report.report_id,
                    overall_score=quality_metrics["overall_score"],
                    total_issues=len(all_issues),
                    critical_issues=report.critical_issues
                )
                
                return report
                
            except Exception as e:
                self.logger.error("Comprehensive quality check failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Comprehensive quality check failed: {str(e)}")
    
    async def validate_campaign_launch_readiness(
        self,
        campaign_id: str,
        launch_requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate if a campaign is ready for launch based on requirements.
        
        Args:
            campaign_id: Campaign to validate
            launch_requirements: Launch requirements and criteria
            
        Returns:
            Dict[str, Any]: Launch readiness assessment
        """
        async with self.tracer.trace_task_execution(
            task_id=f"validate_launch_{campaign_id}",
            task_name="Campaign Launch Validation",
            task_data={
                "campaign_id": campaign_id,
                "requirements": list(launch_requirements.keys())
            }
        ) as span:
            try:
                self.logger.info(
                    "Validating campaign launch readiness",
                    campaign_id=campaign_id,
                    requirements=list(launch_requirements.keys())
                )
                
                # Run critical quality checks
                critical_checks = [
                    QualityCheckType.COMPLIANCE,
                    QualityCheckType.POLICY,
                    QualityCheckType.TECHNICAL
                ]
                
                validation_results = []
                blocking_issues = []
                
                for check_type in critical_checks:
                    check_result = await self._run_single_quality_check(campaign_id, check_type)
                    validation_results.append(check_result)
                    
                    # Identify blocking issues
                    critical_issues = [
                        issue for issue in check_result.issues_found
                        if issue.severity == SeverityLevel.CRITICAL
                    ]
                    blocking_issues.extend(critical_issues)
                
                # Check specific launch requirements
                requirement_checks = await self._check_launch_requirements(
                    campaign_id, launch_requirements
                )
                
                # Determine launch readiness
                is_launch_ready = (
                    len(blocking_issues) == 0 and
                    requirement_checks["all_requirements_met"]
                )
                
                # Generate readiness assessment
                readiness_assessment = {
                    "campaign_id": campaign_id,
                    "is_launch_ready": is_launch_ready,
                    "blocking_issues_count": len(blocking_issues),
                    "blocking_issues": [
                        {
                            "issue_id": issue.issue_id,
                            "title": issue.title,
                            "description": issue.description,
                            "fix_instructions": issue.fix_instructions
                        }
                        for issue in blocking_issues
                    ],
                    "requirement_checks": requirement_checks,
                    "validation_results": [
                        {
                            "check_type": result.check_type.value,
                            "status": result.status.value,
                            "issues_count": len(result.issues_found),
                            "score": result.score
                        }
                        for result in validation_results
                    ],
                    "recommendations_before_launch": await self._generate_pre_launch_recommendations(
                        blocking_issues, requirement_checks
                    ),
                    "estimated_fix_time": await self._estimate_fix_time(blocking_issues),
                    "validation_timestamp": datetime.utcnow().isoformat()
                }
                
                self.tracer.record_task_result(span, {
                    "is_launch_ready": is_launch_ready,
                    "blocking_issues": len(blocking_issues),
                    "requirements_met": requirement_checks["all_requirements_met"],
                    "validation_score": sum(r.score or 0 for r in validation_results) / len(validation_results) if validation_results else 0
                }, True)
                
                self.logger.info(
                    "Campaign launch validation completed",
                    campaign_id=campaign_id,
                    is_launch_ready=is_launch_ready,
                    blocking_issues=len(blocking_issues)
                )
                
                return readiness_assessment
                
            except Exception as e:
                self.logger.error("Campaign launch validation failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Campaign launch validation failed: {str(e)}")
    
    async def monitor_quality_degradation(
        self,
        campaign_ids: List[str],
        monitoring_period_days: int = 7,
        alert_thresholds: Optional[Dict[str, float]] = None
    ) -> Dict[str, Any]:
        """
        Monitor campaigns for quality degradation over time.
        
        Args:
            campaign_ids: Campaigns to monitor
            monitoring_period_days: Period to monitor
            alert_thresholds: Custom alert thresholds
            
        Returns:
            Dict[str, Any]: Quality monitoring results
        """
        async with self.tracer.trace_task_execution(
            task_id=f"monitor_quality_{hash(str(campaign_ids))}",
            task_name="Quality Degradation Monitoring",
            task_data={
                "campaign_count": len(campaign_ids),
                "monitoring_days": monitoring_period_days
            }
        ) as span:
            try:
                self.logger.info(
                    "Starting quality degradation monitoring",
                    campaign_count=len(campaign_ids),
                    monitoring_days=monitoring_period_days
                )
                
                # Use default thresholds if not provided
                thresholds = alert_thresholds or {
                    "quality_score_drop": 1.0,  # Alert if QS drops by 1 point
                    "ctr_drop_percentage": 20.0,  # Alert if CTR drops by 20%
                    "conversion_rate_drop": 25.0,  # Alert if CR drops by 25%
                    "cpc_increase_percentage": 30.0  # Alert if CPC increases by 30%
                }
                
                monitoring_results = []
                alerts_triggered = []
                
                for campaign_id in campaign_ids:
                    try:
                        # Get historical performance data
                        historical_data = await self._get_historical_quality_data(
                            campaign_id, monitoring_period_days
                        )
                        
                        # Analyze quality trends
                        quality_trends = await self._analyze_quality_trends(
                            campaign_id, historical_data
                        )
                        
                        # Check for degradation alerts
                        campaign_alerts = await self._check_quality_degradation_alerts(
                            campaign_id, quality_trends, thresholds
                        )
                        
                        monitoring_results.append({
                            "campaign_id": campaign_id,
                            "quality_trends": quality_trends,
                            "alerts": campaign_alerts,
                            "monitoring_period": monitoring_period_days,
                            "data_points": len(historical_data)
                        })
                        
                        alerts_triggered.extend(campaign_alerts)
                        
                    except Exception as e:
                        self.logger.warning(
                            "Failed to monitor campaign quality",
                            campaign_id=campaign_id,
                            error=str(e)
                        )
                
                # Generate monitoring summary
                monitoring_summary = {
                    "campaigns_monitored": len(campaign_ids),
                    "total_alerts": len(alerts_triggered),
                    "critical_alerts": len([a for a in alerts_triggered if a.get("severity") == "critical"]),
                    "monitoring_period_days": monitoring_period_days,
                    "campaign_results": monitoring_results,
                    "alert_summary": await self._summarize_quality_alerts(alerts_triggered),
                    "recommendations": await self._generate_quality_monitoring_recommendations(
                        monitoring_results, alerts_triggered
                    ),
                    "monitoring_timestamp": datetime.utcnow().isoformat()
                }
                
                self.tracer.record_task_result(span, {
                    "campaigns_monitored": len(campaign_ids),
                    "alerts_triggered": len(alerts_triggered),
                    "critical_alerts": len([a for a in alerts_triggered if a.get("severity") == "critical"])
                }, True)
                
                self.logger.info(
                    "Quality degradation monitoring completed",
                    campaigns_monitored=len(campaign_ids),
                    alerts_triggered=len(alerts_triggered)
                )
                
                return monitoring_summary
                
            except Exception as e:
                self.logger.error("Quality degradation monitoring failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Quality degradation monitoring failed: {str(e)}")
    
    # Core quality check methods
    
    async def _run_single_quality_check(
        self,
        campaign_id: str,
        check_type: QualityCheckType
    ) -> QualityCheckResult:
        """Run a single quality check on a campaign."""
        try:
            start_time = datetime.utcnow()
            
            # Get the check function
            check_function = self.quality_checks.get(check_type)
            if not check_function:
                raise AgentError(f"Unknown quality check type: {check_type}")
            
            # Run the check
            issues_found = await check_function(campaign_id)
            
            # Calculate execution time
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Determine overall status
            if any(issue.severity == SeverityLevel.CRITICAL for issue in issues_found):
                status = QualityStatus.FAILED
            elif any(issue.severity == SeverityLevel.HIGH for issue in issues_found):
                status = QualityStatus.WARNING
            elif issues_found:
                status = QualityStatus.WARNING
            else:
                status = QualityStatus.PASSED
            
            # Calculate quality score (0-100)
            score = await self._calculate_check_quality_score(issues_found)
            
            # Generate recommendations
            recommendations = await self._generate_check_recommendations(issues_found)
            
            return QualityCheckResult(
                check_id=f"{check_type.value}_{campaign_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                check_type=check_type,
                entity_type="campaign",
                entity_id=campaign_id,
                status=status,
                score=score,
                issues_found=issues_found,
                recommendations=recommendations,
                execution_time_seconds=execution_time
            )
            
        except Exception as e:
            self.logger.error("Single quality check failed", campaign_id=campaign_id, check_type=check_type.value, error=str(e))
            return QualityCheckResult(
                check_id=f"{check_type.value}_{campaign_id}_error",
                check_type=check_type,
                entity_type="campaign",
                entity_id=campaign_id,
                status=QualityStatus.FAILED,
                issues_found=[
                    QualityIssue(
                        issue_id=f"check_error_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                        check_type=check_type,
                        severity=SeverityLevel.HIGH,
                        title="Quality Check Error",
                        description=f"Failed to run {check_type.value} check: {str(e)}",
                        affected_entity="campaign",
                        entity_id=campaign_id
                    )
                ]
            )
    
    async def _check_compliance(self, campaign_id: str) -> List[QualityIssue]:
        """Check campaign compliance with Google Ads policies."""
        try:
            issues = []
            
            # Get campaign data
            if self.google_ads_service:
                campaign_data = await self.google_ads_service.get_campaign_details(campaign_id)
                ads_data = await self.google_ads_service.get_campaign_ads(campaign_id)
            else:
                # Mock data for development
                campaign_data = {
                    "name": "Test Campaign",
                    "status": "ENABLED",
                    "type": "SEARCH"
                }
                ads_data = [
                    {
                        "id": "ad_123",
                        "headlines": ["Premium Software Solutions", "Best Business Tools"],
                        "descriptions": ["Get the best software for your business needs", "Professional tools for modern businesses"],
                        "final_urls": ["https://example.com"],
                        "status": "ENABLED"
                    }
                ]
            
            # Check campaign-level compliance
            if campaign_data.get("status") != "ENABLED":
                issues.append(QualityIssue(
                    issue_id=f"compliance_campaign_status_{campaign_id}",
                    check_type=QualityCheckType.COMPLIANCE,
                    severity=SeverityLevel.HIGH,
                    title="Campaign Not Enabled",
                    description="Campaign is not in enabled status",
                    affected_entity="campaign",
                    entity_id=campaign_id,
                    current_value=campaign_data.get("status"),
                    recommended_value="ENABLED",
                    fix_instructions=["Enable the campaign to start serving ads"],
                    auto_fixable=True
                ))
            
            # Check ad-level compliance
            for ad in ads_data:
                ad_id = ad.get("id", "unknown")
                
                # Check for prohibited content in headlines
                for i, headline in enumerate(ad.get("headlines", [])):
                    for prohibited in self.policy_rules["prohibited_words"]:
                        if prohibited.lower() in headline.lower():
                            issues.append(QualityIssue(
                                issue_id=f"compliance_prohibited_word_{ad_id}_{i}",
                                check_type=QualityCheckType.COMPLIANCE,
                                severity=SeverityLevel.CRITICAL,
                                title="Prohibited Content in Headline",
                                description=f"Headline contains prohibited word: '{prohibited}'",
                                affected_entity="ad",
                                entity_id=ad_id,
                                location=f"headline_{i+1}",
                                current_value=headline,
                                fix_instructions=[f"Remove or replace the word '{prohibited}' in headline"],
                                auto_fixable=False,
                                policy_reference="Google Ads Content Policy"
                            ))
                
                # Check character limits
                for i, headline in enumerate(ad.get("headlines", [])):
                    if len(headline) > self.policy_rules["character_limits"]["headline_1"]:
                        issues.append(QualityIssue(
                            issue_id=f"compliance_headline_length_{ad_id}_{i}",
                            check_type=QualityCheckType.COMPLIANCE,
                            severity=SeverityLevel.HIGH,
                            title="Headline Exceeds Character Limit",
                            description=f"Headline {i+1} is {len(headline)} characters (limit: {self.policy_rules['character_limits']['headline_1']})",
                            affected_entity="ad",
                            entity_id=ad_id,
                            location=f"headline_{i+1}",
                            current_value=f"{len(headline)} characters",
                            recommended_value=f"{self.policy_rules['character_limits']['headline_1']} characters max",
                            fix_instructions=[f"Shorten headline {i+1} to {self.policy_rules['character_limits']['headline_1']} characters or less"],
                            auto_fixable=False
                        ))
            
            return issues
            
        except Exception as e:
            self.logger.error("Compliance check failed", campaign_id=campaign_id, error=str(e))
            return []
    
    async def _check_policy_adherence(self, campaign_id: str) -> List[QualityIssue]:
        """Check adherence to Google Ads policies."""
        try:
            issues = []
            
            # Mock policy check implementation
            # In real implementation, this would check against comprehensive policy rules
            
            # Check for trademark issues
            issues.append(QualityIssue(
                issue_id=f"policy_trademark_{campaign_id}",
                check_type=QualityCheckType.POLICY,
                severity=SeverityLevel.MEDIUM,
                title="Potential Trademark Usage",
                description="Campaign may contain trademarked terms that require approval",
                affected_entity="campaign",
                entity_id=campaign_id,
                fix_instructions=[
                    "Review ad copy for trademarked terms",
                    "Obtain necessary trademark permissions",
                    "Consider alternative wording"
                ],
                policy_reference="Google Ads Trademark Policy"
            ))
            
            return issues
            
        except Exception as e:
            self.logger.error("Policy adherence check failed", campaign_id=campaign_id, error=str(e))
            return []
    
    async def _check_performance_quality(self, campaign_id: str) -> List[QualityIssue]:
        """Check campaign performance quality."""
        try:
            issues = []
            
            # Get performance data
            if self.google_ads_service:
                performance_data = await self.google_ads_service.get_campaign_performance(
                    campaign_id, 
                    datetime.utcnow() - timedelta(days=30),
                    datetime.utcnow()
                )
            else:
                # Mock performance data
                performance_data = {
                    "quality_score": 4.2,
                    "ctr": 1.8,
                    "conversion_rate": 2.1,
                    "cpc": 12.5,
                    "impression_share": 45.0
                }
            
            # Check Quality Score
            quality_score = performance_data.get("quality_score", 0)
            if quality_score < self.quality_thresholds["min_quality_score"]:
                issues.append(QualityIssue(
                    issue_id=f"performance_quality_score_{campaign_id}",
                    check_type=QualityCheckType.PERFORMANCE,
                    severity=SeverityLevel.HIGH,
                    title="Low Quality Score",
                    description=f"Campaign Quality Score ({quality_score}) is below recommended threshold ({self.quality_thresholds['min_quality_score']})",
                    affected_entity="campaign",
                    entity_id=campaign_id,
                    current_value=str(quality_score),
                    recommended_value=f"{self.quality_thresholds['min_quality_score']}+",
                    fix_instructions=[
                        "Improve ad relevance to keywords",
                        "Optimize landing page experience",
                        "Increase expected click-through rate"
                    ],
                    impact_assessment="Low Quality Score increases costs and reduces ad visibility"
                ))
            
            # Check CTR
            ctr = performance_data.get("ctr", 0)
            if ctr < self.quality_thresholds["min_ctr_threshold"]:
                issues.append(QualityIssue(
                    issue_id=f"performance_ctr_{campaign_id}",
                    check_type=QualityCheckType.PERFORMANCE,
                    severity=SeverityLevel.MEDIUM,
                    title="Low Click-Through Rate",
                    description=f"Campaign CTR ({ctr:.2f}%) is below recommended threshold ({self.quality_thresholds['min_ctr_threshold']}%)",
                    affected_entity="campaign",
                    entity_id=campaign_id,
                    current_value=f"{ctr:.2f}%",
                    recommended_value=f"{self.quality_thresholds['min_ctr_threshold']}%+",
                    fix_instructions=[
                        "Test more compelling ad copy",
                        "Improve keyword relevance",
                        "Add ad extensions",
                        "Refine audience targeting"
                    ],
                    impact_assessment="Low CTR indicates poor ad relevance and wastes budget"
                ))
            
            # Check Impression Share
            impression_share = performance_data.get("impression_share", 0)
            if impression_share < self.quality_thresholds["min_impression_share"]:
                issues.append(QualityIssue(
                    issue_id=f"performance_impression_share_{campaign_id}",
                    check_type=QualityCheckType.PERFORMANCE,
                    severity=SeverityLevel.MEDIUM,
                    title="Low Impression Share",
                    description=f"Campaign Impression Share ({impression_share:.1f}%) indicates missed opportunities",
                    affected_entity="campaign",
                    entity_id=campaign_id,
                    current_value=f"{impression_share:.1f}%",
                    recommended_value=f"{self.quality_thresholds['min_impression_share']}%+",
                    fix_instructions=[
                        "Increase campaign budget",
                        "Improve Quality Score to reduce costs",
                        "Adjust bid strategies",
                        "Expand keyword coverage"
                    ],
                    impact_assessment="Low impression share means missing potential customers"
                ))
            
            return issues
            
        except Exception as e:
            self.logger.error("Performance quality check failed", campaign_id=campaign_id, error=str(e))
            return []
    
    async def _check_campaign_structure(self, campaign_id: str) -> List[QualityIssue]:
        """Check campaign structure quality."""
        try:
            issues = []
            
            # Mock structure check - in real implementation would check:
            # - Number of ad groups
            # - Keywords per ad group
            # - Ads per ad group
            # - Keyword match type distribution
            # - Negative keyword coverage
            
            issues.append(QualityIssue(
                issue_id=f"structure_ad_groups_{campaign_id}",
                check_type=QualityCheckType.STRUCTURE,
                severity=SeverityLevel.LOW,
                title="Limited Ad Group Diversity",
                description="Campaign has fewer than 5 ad groups, which may limit targeting precision",
                affected_entity="campaign",
                entity_id=campaign_id,
                fix_instructions=[
                    "Create additional ad groups for different keyword themes",
                    "Segment keywords by intent or product category",
                    "Ensure each ad group has tightly themed keywords"
                ],
                impact_assessment="Better structure improves relevance and Quality Score"
            ))
            
            return issues
            
        except Exception as e:
            self.logger.error("Campaign structure check failed", campaign_id=campaign_id, error=str(e))
            return []
    
    async def _check_content_quality(self, campaign_id: str) -> List[QualityIssue]:
        """Check content quality of ads."""
        try:
            issues = []
            
            # Get ads data
            if self.google_ads_service:
                ads_data = await self.google_ads_service.get_campaign_ads(campaign_id)
            else:
                # Mock ads data
                ads_data = [
                    {
                        "id": "ad_123",
                        "headlines": ["AMAZING SOFTWARE!!!", "Best Tools EVER"],
                        "descriptions": ["Get the best software with GUARANTEED results", "Professional tools"]
                    }
                ]
            
            # Check each ad
            for ad in ads_data:
                ad_id = ad.get("id", "unknown")
                
                # Check headlines for quality issues
                for i, headline in enumerate(ad.get("headlines", [])):
                    # Check for excessive caps
                    if self.content_quality_patterns["excessive_caps"].search(headline):
                        issues.append(QualityIssue(
                            issue_id=f"content_excessive_caps_{ad_id}_{i}",
                            check_type=QualityCheckType.CONTENT,
                            severity=SeverityLevel.MEDIUM,
                            title="Excessive Capitalization",
                            description=f"Headline {i+1} contains excessive capitalization",
                            affected_entity="ad",
                            entity_id=ad_id,
                            location=f"headline_{i+1}",
                            current_value=headline,
                            fix_instructions=["Use normal capitalization", "Avoid all-caps words except for acronyms"],
                            auto_fixable=True
                        ))
                    
                    # Check for excessive punctuation
                    if self.content_quality_patterns["excessive_punctuation"].search(headline):
                        issues.append(QualityIssue(
                            issue_id=f"content_excessive_punctuation_{ad_id}_{i}",
                            check_type=QualityCheckType.CONTENT,
                            severity=SeverityLevel.LOW,
                            title="Excessive Punctuation",
                            description=f"Headline {i+1} contains excessive punctuation",
                            affected_entity="ad",
                            entity_id=ad_id,
                            location=f"headline_{i+1}",
                            current_value=headline,
                            fix_instructions=["Use single punctuation marks", "Avoid multiple exclamation marks"],
                            auto_fixable=True
                        ))
                
                # Check descriptions
                for i, description in enumerate(ad.get("descriptions", [])):
                    # Check for promotional language overuse
                    if self.content_quality_patterns["promotional_language"].search(description):
                        issues.append(QualityIssue(
                            issue_id=f"content_promotional_language_{ad_id}_{i}",
                            check_type=QualityCheckType.CONTENT,
                            severity=SeverityLevel.LOW,
                            title="Overuse of Promotional Language",
                            description=f"Description {i+1} may overuse promotional language",
                            affected_entity="ad",
                            entity_id=ad_id,
                            location=f"description_{i+1}",
                            current_value=description,
                            fix_instructions=[
                                "Balance promotional language with informative content",
                                "Focus on benefits rather than urgency",
                                "Use varied messaging approaches"
                            ]
                        ))
            
            return issues
            
        except Exception as e:
            self.logger.error("Content quality check failed", campaign_id=campaign_id, error=str(e))
            return []
    
    async def _check_technical_setup(self, campaign_id: str) -> List[QualityIssue]:
        """Check technical setup and configuration."""
        try:
            issues = []
            
            # Mock technical checks - in real implementation would check:
            # - Conversion tracking setup
            # - UTM parameters
            # - Landing page accessibility
            # - Mobile-friendliness
            # - Site speed
            
            issues.append(QualityIssue(
                issue_id=f"technical_conversion_tracking_{campaign_id}",
                check_type=QualityCheckType.TECHNICAL,
                severity=SeverityLevel.CRITICAL,
                title="Missing Conversion Tracking",
                description="No conversion tracking detected for this campaign",
                affected_entity="campaign",
                entity_id=campaign_id,
                fix_instructions=[
                    "Set up Google Ads conversion tracking",
                    "Install conversion tracking code on website",
                    "Test conversion tracking functionality",
                    "Verify conversion attribution"
                ],
                auto_fixable=False,
                impact_assessment="Cannot measure campaign effectiveness without conversion tracking"
            ))
            
            return issues
            
        except Exception as e:
            self.logger.error("Technical setup check failed", campaign_id=campaign_id, error=str(e))
            return []
    
    async def _check_accessibility(self, campaign_id: str) -> List[QualityIssue]:
        """Check accessibility compliance."""
        try:
            issues = []
            
            # Mock accessibility check
            issues.append(QualityIssue(
                issue_id=f"accessibility_alt_text_{campaign_id}",
                check_type=QualityCheckType.ACCESSIBILITY,
                severity=SeverityLevel.LOW,
                title="Missing Alt Text for Images",
                description="Some ad images may be missing descriptive alt text",
                affected_entity="campaign",
                entity_id=campaign_id,
                fix_instructions=[
                    "Add descriptive alt text to all images",
                    "Ensure alt text describes image content and purpose",
                    "Keep alt text concise but informative"
                ]
            ))
            
            return issues
            
        except Exception as e:
            self.logger.error("Accessibility check failed", campaign_id=campaign_id, error=str(e))
            return []
    
    async def _check_brand_safety(self, campaign_id: str) -> List[QualityIssue]:
        """Check brand safety compliance."""
        try:
            issues = []
            
            # Mock brand safety check
            issues.append(QualityIssue(
                issue_id=f"brand_safety_placement_{campaign_id}",
                check_type=QualityCheckType.BRAND_SAFETY,
                severity=SeverityLevel.MEDIUM,
                title="Brand Safety Settings Review Needed",
                description="Review brand safety settings to ensure appropriate ad placements",
                affected_entity="campaign",
                entity_id=campaign_id,
                fix_instructions=[
                    "Review current brand safety settings",
                    "Add appropriate content exclusions",
                    "Monitor placement reports regularly",
                    "Adjust settings based on brand guidelines"
                ]
            ))
            
            return issues
            
        except Exception as e:
            self.logger.error("Brand safety check failed", campaign_id=campaign_id, error=str(e))
            return []
    
    # Additional helper methods for quality calculations, auto-fixes, etc.
    # (Implementation continues with similar patterns...)