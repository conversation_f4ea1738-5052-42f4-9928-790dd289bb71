"""
Ad Asset Generation Agent for AiLex Ad Agent System.
Handles creative content generation using GPT-4o and Gemini integration.
"""

import asyncio
import json
import base64
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum

import structlog
from crewai import Agent, Task
from PIL import Image
import io

from ..base import BaseAiLexAgent, AgentContext, AgentError
from ..tracing import AgentTracer, create_agent_tracer
from models.agents import AgentType, AgentConfig
from services.gemini_service import gemini_service
from utils.config import settings


logger = structlog.get_logger(__name__)


class AssetType(str, Enum):
    """Types of ad assets that can be generated."""
    TEXT_AD = "text_ad"
    DISPLAY_AD = "display_ad"
    VIDEO_SCRIPT = "video_script"
    SOCIAL_POST = "social_post"
    EMAIL_CONTENT = "email_content"
    LANDING_PAGE_COPY = "landing_page_copy"
    PRODUCT_DESCRIPTION = "product_description"
    HEADLINE = "headline"
    DESCRIPTION = "description"
    CALL_TO_ACTION = "call_to_action"


class CreativeFormat(str, Enum):
    """Ad creative formats."""
    RESPONSIVE_SEARCH_AD = "responsive_search_ad"
    EXPANDED_TEXT_AD = "expanded_text_ad"
    DISPLAY_BANNER = "display_banner"
    SOCIAL_MEDIA_POST = "social_media_post"
    VIDEO_AD = "video_ad"
    EMAIL_TEMPLATE = "email_template"
    LANDING_PAGE = "landing_page"


@dataclass
class CreativeBrief:
    """Creative brief for asset generation."""
    business_context: Dict[str, Any]
    target_audience: Dict[str, Any]
    campaign_objectives: List[str]
    key_messages: List[str]
    value_propositions: List[str]
    brand_guidelines: Dict[str, Any]
    tone_of_voice: str
    competitive_differentiation: List[str]
    call_to_actions: List[str]
    compliance_requirements: List[str]
    channel_specifications: Dict[str, Any]


@dataclass
class GeneratedAsset:
    """Generated creative asset."""
    asset_id: str
    asset_type: AssetType
    format: CreativeFormat
    content: Dict[str, Any]
    metadata: Dict[str, Any]
    quality_score: float
    compliance_status: str
    variations: List[Dict[str, Any]]
    performance_prediction: Dict[str, float]
    created_at: datetime
    version: str = "1.0"


@dataclass
class AssetVariation:
    """Variation of a creative asset."""
    variation_id: str
    parent_asset_id: str
    content: Dict[str, Any]
    variation_type: str  # A/B test, localization, audience segment
    target_criteria: Dict[str, Any]
    quality_score: float
    created_at: datetime


class AdAssetGenerationAgent(BaseAiLexAgent):
    """
    AI agent specialized in generating creative advertising assets.
    Integrates with GPT-4o and Gemini for high-quality content generation.
    """
    
    def __init__(self, agent_id: str, config: AgentConfig):
        super().__init__(
            agent_id=agent_id,
            name="Ad Asset Generation Agent",
            description="Specialized AI agent for generating creative advertising assets including text, images, and video content",
            agent_type=AgentType.AD_ASSET_GENERATION,
            config=config
        )
        
        # Initialize AI services
        self.gemini_service = gemini_service
        
        # Initialize tracer
        self.tracer = create_agent_tracer(self.agent_id, self.name)
        
        # Asset generation capabilities
        self.supported_formats = {
            CreativeFormat.RESPONSIVE_SEARCH_AD: self._generate_responsive_search_ad,
            CreativeFormat.EXPANDED_TEXT_AD: self._generate_expanded_text_ad,
            CreativeFormat.DISPLAY_BANNER: self._generate_display_banner,
            CreativeFormat.SOCIAL_MEDIA_POST: self._generate_social_media_post,
            CreativeFormat.VIDEO_AD: self._generate_video_script,
            CreativeFormat.EMAIL_TEMPLATE: self._generate_email_content,
            CreativeFormat.LANDING_PAGE: self._generate_landing_page_copy
        }
        
        # Quality assessment criteria
        self.quality_criteria = {
            "relevance": 0.3,
            "clarity": 0.2,
            "persuasiveness": 0.2,
            "brand_alignment": 0.15,
            "compliance": 0.15
        }
        
        # Asset generation cache
        self.generation_cache: Dict[str, GeneratedAsset] = {}
        
        # Performance prediction models (simplified)
        self.performance_models = {
            "ctr_prediction": self._predict_click_through_rate,
            "engagement_prediction": self._predict_engagement_rate,
            "conversion_prediction": self._predict_conversion_rate
        }
    
    async def _custom_initialize(self) -> None:
        """Custom initialization for ad asset generation agent."""
        try:
            # Ensure Gemini service is authenticated
            if settings.GEMINI_API_KEY:
                await self.gemini_service.authenticate()
                self.logger.info("Gemini service initialized for asset generation")
            else:
                raise AgentError("Gemini API key not configured for asset generation")
            
            self.logger.info(
                "Ad asset generation agent initialized",
                has_gemini=bool(settings.GEMINI_API_KEY),
                supported_formats=len(self.supported_formats)
            )
            
        except Exception as e:
            raise AgentError(f"Failed to initialize ad asset generation agent: {str(e)}")
    
    async def generate_creative_assets(
        self,
        creative_brief: CreativeBrief,
        asset_types: List[AssetType],
        formats: List[CreativeFormat],
        variation_count: int = 3
    ) -> List[GeneratedAsset]:
        """
        Generate creative assets based on brief.
        
        Args:
            creative_brief: Detailed creative brief
            asset_types: Types of assets to generate
            formats: Creative formats to generate
            variation_count: Number of variations per asset
            
        Returns:
            List[GeneratedAsset]: Generated creative assets
        """
        async with self.tracer.trace_task_execution(
            task_id=f"generate_assets_{hash(str(asset_types))}",
            task_name="Creative Asset Generation",
            task_data={
                "asset_types": [at.value for at in asset_types],
                "formats": [f.value for f in formats],
                "variation_count": variation_count
            }
        ) as span:
            try:
                self.logger.info(
                    "Starting creative asset generation",
                    asset_types=[at.value for at in asset_types],
                    formats=[f.value for f in formats],
                    variation_count=variation_count
                )
                
                generated_assets = []
                
                # Generate assets for each format
                for format_type in formats:
                    if format_type in self.supported_formats:
                        try:
                            asset = await self.supported_formats[format_type](
                                creative_brief, variation_count
                            )
                            generated_assets.append(asset)
                            
                        except Exception as e:
                            self.logger.warning(
                                "Failed to generate asset for format",
                                format=format_type.value,
                                error=str(e)
                            )
                
                # Generate additional variations if requested
                if variation_count > 1:
                    for asset in generated_assets:
                        variations = await self._generate_asset_variations(
                            asset, creative_brief, variation_count - 1
                        )
                        asset.variations.extend(variations)
                
                # Assess quality and predict performance
                for asset in generated_assets:
                    asset.quality_score = await self._assess_asset_quality(asset, creative_brief)
                    asset.performance_prediction = await self._predict_asset_performance(asset, creative_brief)
                
                self.tracer.record_task_result(span, {
                    "assets_generated": len(generated_assets),
                    "total_variations": sum(len(asset.variations) for asset in generated_assets),
                    "avg_quality_score": sum(asset.quality_score for asset in generated_assets) / len(generated_assets) if generated_assets else 0
                }, True)
                
                self.logger.info(
                    "Creative asset generation completed",
                    assets_generated=len(generated_assets),
                    total_variations=sum(len(asset.variations) for asset in generated_assets),
                    avg_quality_score=sum(asset.quality_score for asset in generated_assets) / len(generated_assets) if generated_assets else 0
                )
                
                return generated_assets
                
            except Exception as e:
                self.logger.error("Creative asset generation failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Creative asset generation failed: {str(e)}")
    
    async def optimize_existing_assets(
        self,
        existing_assets: List[GeneratedAsset],
        performance_data: Dict[str, Any],
        optimization_goals: List[str]
    ) -> List[GeneratedAsset]:
        """
        Optimize existing assets based on performance data.
        
        Args:
            existing_assets: Current assets to optimize
            performance_data: Historical performance data
            optimization_goals: Optimization objectives
            
        Returns:
            List[GeneratedAsset]: Optimized assets
        """
        async with self.tracer.trace_task_execution(
            task_id=f"optimize_assets_{len(existing_assets)}",
            task_name="Asset Optimization",
            task_data={
                "assets_count": len(existing_assets),
                "goals": optimization_goals
            }
        ) as span:
            try:
                self.logger.info(
                    "Starting asset optimization",
                    assets_count=len(existing_assets),
                    goals=optimization_goals
                )
                
                optimized_assets = []
                
                for asset in existing_assets:
                    # Analyze performance
                    asset_performance = performance_data.get(asset.asset_id, {})
                    
                    # Identify optimization opportunities
                    opportunities = await self._identify_optimization_opportunities(
                        asset, asset_performance, optimization_goals
                    )
                    
                    # Generate optimized version
                    if opportunities:
                        optimized_asset = await self._create_optimized_asset(
                            asset, opportunities, asset_performance
                        )
                        optimized_assets.append(optimized_asset)
                
                self.tracer.record_task_result(span, {
                    "optimized_assets": len(optimized_assets),
                    "optimization_rate": len(optimized_assets) / len(existing_assets) if existing_assets else 0
                }, True)
                
                self.logger.info(
                    "Asset optimization completed",
                    optimized_assets=len(optimized_assets),
                    optimization_rate=len(optimized_assets) / len(existing_assets) if existing_assets else 0
                )
                
                return optimized_assets
                
            except Exception as e:
                self.logger.error("Asset optimization failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Asset optimization failed: {str(e)}")
    
    async def generate_asset_variations(
        self,
        base_asset: GeneratedAsset,
        variation_types: List[str],
        target_audiences: List[Dict[str, Any]]
    ) -> List[AssetVariation]:
        """
        Generate variations of an existing asset.
        
        Args:
            base_asset: Base asset to create variations from
            variation_types: Types of variations (A/B test, localization, etc.)
            target_audiences: Different target audience segments
            
        Returns:
            List[AssetVariation]: Generated asset variations
        """
        async with self.tracer.trace_task_execution(
            task_id=f"generate_variations_{base_asset.asset_id}",
            task_name="Asset Variation Generation",
            task_data={
                "base_asset_id": base_asset.asset_id,
                "variation_types": variation_types,
                "audience_count": len(target_audiences)
            }
        ) as span:
            try:
                variations = []
                
                for variation_type in variation_types:
                    if variation_type == "audience_segment":
                        for audience in target_audiences:
                            variation = await self._create_audience_specific_variation(
                                base_asset, audience
                            )
                            variations.append(variation)
                    
                    elif variation_type == "a_b_test":
                        ab_variations = await self._create_ab_test_variations(base_asset)
                        variations.extend(ab_variations)
                    
                    elif variation_type == "localization":
                        # Placeholder for localization logic
                        pass
                
                return variations
                
            except Exception as e:
                self.logger.error("Asset variation generation failed", error=str(e))
                raise AgentError(f"Asset variation generation failed: {str(e)}")
    
    # Format-specific generation methods
    
    async def _generate_responsive_search_ad(
        self,
        creative_brief: CreativeBrief,
        variation_count: int
    ) -> GeneratedAsset:
        """Generate responsive search ad assets."""
        try:
            # Generate multiple headlines (15 max for RSA)
            headlines = await self._generate_headlines(
                creative_brief, count=15, max_length=30
            )
            
            # Generate multiple descriptions (4 max for RSA)
            descriptions = await self._generate_descriptions(
                creative_brief, count=4, max_length=90
            )
            
            # Generate path suggestions
            paths = await self._generate_url_paths(creative_brief, count=2)
            
            content = {
                "headlines": headlines,
                "descriptions": descriptions,
                "paths": paths,
                "final_urls": [creative_brief.channel_specifications.get("landing_url", "")]
            }
            
            asset = GeneratedAsset(
                asset_id=f"rsa_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                asset_type=AssetType.TEXT_AD,
                format=CreativeFormat.RESPONSIVE_SEARCH_AD,
                content=content,
                metadata={
                    "character_counts": {
                        "headlines": [len(h) for h in headlines],
                        "descriptions": [len(d) for d in descriptions]
                    },
                    "total_combinations": len(headlines) * len(descriptions)
                },
                quality_score=0.0,  # Will be calculated later
                compliance_status="pending",
                variations=[],
                performance_prediction={},
                created_at=datetime.utcnow()
            )
            
            return asset
            
        except Exception as e:
            raise AgentError(f"Failed to generate responsive search ad: {str(e)}")
    
    async def _generate_expanded_text_ad(
        self,
        creative_brief: CreativeBrief,
        variation_count: int
    ) -> GeneratedAsset:
        """Generate expanded text ad assets."""
        try:
            # Generate headlines
            headline1 = await self._generate_single_headline(creative_brief, max_length=30)
            headline2 = await self._generate_single_headline(creative_brief, max_length=30)
            
            # Generate description
            description = await self._generate_single_description(creative_brief, max_length=80)
            
            content = {
                "headline1": headline1,
                "headline2": headline2,
                "description": description,
                "path1": await self._generate_single_path(creative_brief),
                "path2": await self._generate_single_path(creative_brief),
                "final_url": creative_brief.channel_specifications.get("landing_url", "")
            }
            
            asset = GeneratedAsset(
                asset_id=f"eta_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                asset_type=AssetType.TEXT_AD,
                format=CreativeFormat.EXPANDED_TEXT_AD,
                content=content,
                metadata={
                    "character_counts": {
                        "headline1": len(headline1),
                        "headline2": len(headline2),
                        "description": len(description)
                    }
                },
                quality_score=0.0,
                compliance_status="pending",
                variations=[],
                performance_prediction={},
                created_at=datetime.utcnow()
            )
            
            return asset
            
        except Exception as e:
            raise AgentError(f"Failed to generate expanded text ad: {str(e)}")
    
    async def _generate_display_banner(
        self,
        creative_brief: CreativeBrief,
        variation_count: int
    ) -> GeneratedAsset:
        """Generate display banner content."""
        try:
            # Generate display ad copy elements
            headline = await self._generate_single_headline(creative_brief, max_length=40)
            subheadline = await self._generate_single_description(creative_brief, max_length=60)
            cta = await self._generate_call_to_action(creative_brief)
            
            # Generate design specifications
            design_specs = await self._generate_design_specifications(creative_brief)
            
            content = {
                "headline": headline,
                "subheadline": subheadline,
                "call_to_action": cta,
                "design_specifications": design_specs,
                "sizes": ["728x90", "300x250", "160x600", "320x50"],  # Common banner sizes
                "brand_elements": {
                    "logo_placement": "top-right",
                    "color_scheme": creative_brief.brand_guidelines.get("colors", ["#000000", "#FFFFFF"]),
                    "font_family": creative_brief.brand_guidelines.get("font", "Arial")
                }
            }
            
            asset = GeneratedAsset(
                asset_id=f"display_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                asset_type=AssetType.DISPLAY_AD,
                format=CreativeFormat.DISPLAY_BANNER,
                content=content,
                metadata={
                    "dimensions": content["sizes"],
                    "design_complexity": "medium"
                },
                quality_score=0.0,
                compliance_status="pending",
                variations=[],
                performance_prediction={},
                created_at=datetime.utcnow()
            )
            
            return asset
            
        except Exception as e:
            raise AgentError(f"Failed to generate display banner: {str(e)}")
    
    async def _generate_social_media_post(
        self,
        creative_brief: CreativeBrief,
        variation_count: int
    ) -> GeneratedAsset:
        """Generate social media post content."""
        try:
            platform = creative_brief.channel_specifications.get("platform", "facebook")
            
            # Platform-specific content generation
            if platform.lower() in ["facebook", "instagram"]:
                content = await self._generate_facebook_instagram_post(creative_brief)
            elif platform.lower() == "twitter":
                content = await self._generate_twitter_post(creative_brief)
            elif platform.lower() == "linkedin":
                content = await self._generate_linkedin_post(creative_brief)
            else:
                content = await self._generate_generic_social_post(creative_brief)
            
            asset = GeneratedAsset(
                asset_id=f"social_{platform}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                asset_type=AssetType.SOCIAL_POST,
                format=CreativeFormat.SOCIAL_MEDIA_POST,
                content=content,
                metadata={
                    "platform": platform,
                    "character_count": len(content.get("text", "")),
                    "hashtag_count": len(content.get("hashtags", []))
                },
                quality_score=0.0,
                compliance_status="pending",
                variations=[],
                performance_prediction={},
                created_at=datetime.utcnow()
            )
            
            return asset
            
        except Exception as e:
            raise AgentError(f"Failed to generate social media post: {str(e)}")
    
    async def _generate_video_script(
        self,
        creative_brief: CreativeBrief,
        variation_count: int
    ) -> GeneratedAsset:
        """Generate video ad script."""
        try:
            duration = creative_brief.channel_specifications.get("duration", 30)  # seconds
            
            script = await self._create_video_script(creative_brief, duration)
            
            content = {
                "script": script,
                "duration": duration,
                "scenes": script.get("scenes", []),
                "voiceover": script.get("voiceover", ""),
                "visual_cues": script.get("visual_cues", []),
                "call_to_action": script.get("call_to_action", ""),
                "music_suggestions": script.get("music", "Upbeat, professional")
            }
            
            asset = GeneratedAsset(
                asset_id=f"video_{duration}s_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                asset_type=AssetType.VIDEO_SCRIPT,
                format=CreativeFormat.VIDEO_AD,
                content=content,
                metadata={
                    "duration_seconds": duration,
                    "scene_count": len(content["scenes"]),
                    "script_length": len(content["voiceover"])
                },
                quality_score=0.0,
                compliance_status="pending",
                variations=[],
                performance_prediction={},
                created_at=datetime.utcnow()
            )
            
            return asset
            
        except Exception as e:
            raise AgentError(f"Failed to generate video script: {str(e)}")
    
    async def _generate_email_content(
        self,
        creative_brief: CreativeBrief,
        variation_count: int
    ) -> GeneratedAsset:
        """Generate email marketing content."""
        try:
            email_type = creative_brief.channel_specifications.get("email_type", "promotional")
            
            content = await self._create_email_template(creative_brief, email_type)
            
            asset = GeneratedAsset(
                asset_id=f"email_{email_type}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                asset_type=AssetType.EMAIL_CONTENT,
                format=CreativeFormat.EMAIL_TEMPLATE,
                content=content,
                metadata={
                    "email_type": email_type,
                    "subject_line_count": len(content.get("subject_lines", [])),
                    "content_length": len(content.get("body", ""))
                },
                quality_score=0.0,
                compliance_status="pending",
                variations=[],
                performance_prediction={},
                created_at=datetime.utcnow()
            )
            
            return asset
            
        except Exception as e:
            raise AgentError(f"Failed to generate email content: {str(e)}")
    
    async def _generate_landing_page_copy(
        self,
        creative_brief: CreativeBrief,
        variation_count: int
    ) -> GeneratedAsset:
        """Generate landing page copy."""
        try:
            content = await self._create_landing_page_content(creative_brief)
            
            asset = GeneratedAsset(
                asset_id=f"landing_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                asset_type=AssetType.LANDING_PAGE_COPY,
                format=CreativeFormat.LANDING_PAGE,
                content=content,
                metadata={
                    "section_count": len(content.get("sections", [])),
                    "total_word_count": sum(len(section.get("content", "").split()) for section in content.get("sections", []))
                },
                quality_score=0.0,
                compliance_status="pending",
                variations=[],
                performance_prediction={},
                created_at=datetime.utcnow()
            )
            
            return asset
            
        except Exception as e:
            raise AgentError(f"Failed to generate landing page copy: {str(e)}")
    
    # Content generation helper methods
    
    async def _generate_headlines(self, creative_brief: CreativeBrief, count: int, max_length: int) -> List[str]:
        """Generate multiple headlines."""
        if not self.gemini_service:
            return [f"Sample Headline {i+1}" for i in range(count)]
        
        prompt = f"""
        Generate {count} compelling ad headlines based on this creative brief:
        
        Business: {creative_brief.business_context.get('description', 'N/A')}
        Target Audience: {creative_brief.target_audience}
        Key Messages: {', '.join(creative_brief.key_messages)}
        Value Propositions: {', '.join(creative_brief.value_propositions)}
        Tone: {creative_brief.tone_of_voice}
        
        Requirements:
        - Maximum {max_length} characters per headline
        - Include key benefits and value propositions
        - Use compelling, action-oriented language
        - Vary the approach and style
        - Ensure compliance with advertising standards
        
        Return as a JSON array of strings.
        """
        
        try:
            if self.gemini_service:
                async with self.tracer.trace_llm_call(
                    model_name="gemini-1.5-flash",
                    prompt=prompt,
                    temperature=0.8,
                    max_tokens=1000
                ) as span:
                    response = await self.gemini_service.generate_text(
                        prompt=prompt,
                        temperature=0.8,  # Higher creativity for headlines
                        max_tokens=1000
                    )
                    
                    try:
                        # Try to parse as JSON
                        headlines = json.loads(response)
                        # Ensure headlines meet length requirements
                        return [h[:max_length] for h in headlines if len(h) <= max_length][:count]
                    except json.JSONDecodeError:
                        # Fallback: split by lines
                        return [line.strip() for line in response.split('\n') if line.strip()][:count]
        
        except Exception as e:
            self.logger.warning("Failed to generate headlines with AI", error=str(e))
            # Fallback headlines
            return [f"Quality {creative_brief.business_context.get('product', 'Products')} - Get {benefit}" 
                   for i, benefit in enumerate(creative_brief.value_propositions[:count])]
    
    async def _generate_descriptions(self, creative_brief: CreativeBrief, count: int, max_length: int) -> List[str]:
        """Generate multiple descriptions."""
        if not self.gemini_service:
            return [f"Sample description {i+1} about our quality products and services." for i in range(count)]
        
        prompt = f"""
        Generate {count} compelling ad descriptions based on this creative brief:
        
        Business: {creative_brief.business_context.get('description', 'N/A')}
        Target Audience: {creative_brief.target_audience}
        Key Messages: {', '.join(creative_brief.key_messages)}
        Value Propositions: {', '.join(creative_brief.value_propositions)}
        Tone: {creative_brief.tone_of_voice}
        
        Requirements:
        - Maximum {max_length} characters per description
        - Include specific benefits and features
        - Use persuasive language with clear value proposition
        - Include relevant call-to-action elements
        - Vary the messaging approach
        
        Return as a JSON array of strings.
        """
        
        try:
            if self.gemini_service:
                response = await self.gemini_service.generate_text(
                    prompt=prompt,
                    temperature=0.7,
                    max_tokens=1000
                )
                
                try:
                    descriptions = json.loads(response)
                    return [d[:max_length] for d in descriptions if len(d) <= max_length][:count]
                except json.JSONDecodeError:
                    return [line.strip() for line in response.split('\n') if line.strip()][:count]
        
        except Exception as e:
            self.logger.warning("Failed to generate descriptions with AI", error=str(e))
        
        # Fallback descriptions
        return [f"Discover {creative_brief.business_context.get('product', 'our solutions')} that deliver {vp}." 
               for vp in creative_brief.value_propositions[:count]]
    
    async def _generate_single_headline(self, creative_brief: CreativeBrief, max_length: int) -> str:
        """Generate a single headline."""
        headlines = await self._generate_headlines(creative_brief, 1, max_length)
        return headlines[0] if headlines else f"Quality {creative_brief.business_context.get('product', 'Products')}"
    
    async def _generate_single_description(self, creative_brief: CreativeBrief, max_length: int) -> str:
        """Generate a single description."""
        descriptions = await self._generate_descriptions(creative_brief, 1, max_length)
        return descriptions[0] if descriptions else f"Discover our quality {creative_brief.business_context.get('product', 'products')}."
    
    async def _generate_call_to_action(self, creative_brief: CreativeBrief) -> str:
        """Generate call-to-action text."""
        if creative_brief.call_to_actions:
            return creative_brief.call_to_actions[0]
        
        # Generate based on campaign objectives
        objectives = creative_brief.campaign_objectives
        if "leads" in str(objectives).lower():
            return "Get Your Free Quote"
        elif "sales" in str(objectives).lower():
            return "Shop Now"
        elif "awareness" in str(objectives).lower():
            return "Learn More"
        else:
            return "Get Started"
    
    async def _generate_url_paths(self, creative_brief: CreativeBrief, count: int) -> List[str]:
        """Generate URL path suggestions."""
        business = creative_brief.business_context.get('name', 'business')
        product = creative_brief.business_context.get('product', 'product')
        
        paths = [
            business.lower().replace(' ', ''),
            product.lower().replace(' ', ''),
            f"{business.lower().replace(' ', '')}-{product.lower().replace(' ', '')}",
            "special-offer",
            "get-started"
        ]
        
        return paths[:count]
    
    async def _generate_single_path(self, creative_brief: CreativeBrief) -> str:
        """Generate a single URL path."""
        paths = await self._generate_url_paths(creative_brief, 1)
        return paths[0] if paths else "products"
    
    # Additional helper methods for other content types...
    
    async def _generate_design_specifications(self, creative_brief: CreativeBrief) -> Dict[str, Any]:
        """Generate design specifications for display ads."""
        return {
            "layout": "hero_image_with_text",
            "color_scheme": creative_brief.brand_guidelines.get("colors", ["#000000", "#FFFFFF"]),
            "typography": {
                "headline_font": creative_brief.brand_guidelines.get("headline_font", "Arial Bold"),
                "body_font": creative_brief.brand_guidelines.get("body_font", "Arial"),
                "font_sizes": {"headline": "24px", "body": "14px", "cta": "16px"}
            },
            "imagery": {
                "style": "professional",
                "subject": creative_brief.business_context.get("product", "product"),
                "mood": creative_brief.tone_of_voice
            }
        }
    
    async def _generate_facebook_instagram_post(self, creative_brief: CreativeBrief) -> Dict[str, Any]:
        """Generate Facebook/Instagram post content."""
        # Implementation for social media post generation
        return {
            "text": f"Discover {creative_brief.business_context.get('product', 'our amazing products')}! {creative_brief.value_propositions[0] if creative_brief.value_propositions else 'Quality you can trust.'} #business #quality",
            "hashtags": ["#business", "#quality", "#products"],
            "call_to_action": {"text": "Learn More", "type": "button"},
            "image_suggestions": ["product_showcase", "lifestyle_image", "brand_logo"]
        }
    
    async def _generate_twitter_post(self, creative_brief: CreativeBrief) -> Dict[str, Any]:
        """Generate Twitter post content."""
        return {
            "text": f"{creative_brief.business_context.get('product', 'Our products')} deliver {creative_brief.value_propositions[0][:50] if creative_brief.value_propositions else 'quality'}... Learn more!",
            "hashtags": ["#business", "#innovation"],
            "call_to_action": {"text": "Learn More", "url": creative_brief.channel_specifications.get("landing_url", "")}
        }
    
    async def _generate_linkedin_post(self, creative_brief: CreativeBrief) -> Dict[str, Any]:
        """Generate LinkedIn post content."""
        return {
            "text": f"Industry professionals trust {creative_brief.business_context.get('name', 'our company')} for {creative_brief.business_context.get('product', 'quality solutions')}. Here's why: {creative_brief.value_propositions[0] if creative_brief.value_propositions else 'Excellence in every detail.'}",
            "hashtags": ["#business", "#professional", "#industry"],
            "call_to_action": {"text": "Connect with us", "type": "professional"}
        }
    
    async def _generate_generic_social_post(self, creative_brief: CreativeBrief) -> Dict[str, Any]:
        """Generate generic social media post content."""
        return {
            "text": f"Experience {creative_brief.business_context.get('product', 'our products')} - {creative_brief.value_propositions[0] if creative_brief.value_propositions else 'quality you can count on'}!",
            "hashtags": ["#quality", "#products"],
            "call_to_action": {"text": "Learn More"}
        }
    
    async def _create_video_script(self, creative_brief: CreativeBrief, duration: int) -> Dict[str, Any]:
        """Create video script based on duration and brief."""
        # Simple script structure based on duration
        if duration <= 15:
            structure = ["hook", "value_prop", "cta"]
        elif duration <= 30:
            structure = ["hook", "problem", "solution", "benefit", "cta"]
        else:
            structure = ["hook", "problem", "solution", "benefits", "social_proof", "cta"]
        
        return {
            "scenes": [{"scene": i+1, "content": part, "duration": duration//len(structure)} for i, part in enumerate(structure)],
            "voiceover": f"Professional voiceover script for {creative_brief.business_context.get('product', 'product')}",
            "visual_cues": ["product_shots", "lifestyle_imagery", "logo_display"],
            "call_to_action": await self._generate_call_to_action(creative_brief),
            "music": "Upbeat, professional background music"
        }
    
    async def _create_email_template(self, creative_brief: CreativeBrief, email_type: str) -> Dict[str, Any]:
        """Create email template content."""
        return {
            "subject_lines": [
                f"Discover {creative_brief.business_context.get('product', 'Our Products')}",
                f"Special Offer: {creative_brief.value_propositions[0] if creative_brief.value_propositions else 'Quality Products'}",
                f"Don't Miss Out - {creative_brief.business_context.get('name', 'Our Company')}"
            ],
            "preheader": f"Experience {creative_brief.value_propositions[0] if creative_brief.value_propositions else 'quality'}",
            "body": f"Dear [Name],\n\nDiscover {creative_brief.business_context.get('product', 'our amazing products')} that deliver {creative_brief.value_propositions[0] if creative_brief.value_propositions else 'exceptional value'}.\n\nBest regards,\n{creative_brief.business_context.get('name', 'Our Team')}",
            "call_to_actions": [await self._generate_call_to_action(creative_brief)],
            "footer": "Unsubscribe | Update Preferences | Privacy Policy"
        }
    
    async def _create_landing_page_content(self, creative_brief: CreativeBrief) -> Dict[str, Any]:
        """Create landing page content structure."""
        return {
            "sections": [
                {
                    "type": "hero",
                    "content": f"Welcome to {creative_brief.business_context.get('name', 'Our Company')}",
                    "subheading": creative_brief.value_propositions[0] if creative_brief.value_propositions else "Quality products and services"
                },
                {
                    "type": "benefits",
                    "content": "Why Choose Us",
                    "points": creative_brief.value_propositions
                },
                {
                    "type": "cta",
                    "content": await self._generate_call_to_action(creative_brief)
                }
            ],
            "seo_title": f"{creative_brief.business_context.get('product', 'Products')} | {creative_brief.business_context.get('name', 'Company')}",
            "meta_description": f"Discover {creative_brief.business_context.get('product', 'our products')} - {creative_brief.value_propositions[0][:150] if creative_brief.value_propositions else 'quality you can trust'}"
        }
    
    # Asset optimization and quality assessment methods
    
    async def _assess_asset_quality(self, asset: GeneratedAsset, creative_brief: CreativeBrief) -> float:
        """Assess the quality of a generated asset."""
        try:
            scores = {}
            
            # Relevance score (0-1)
            scores["relevance"] = await self._score_relevance(asset, creative_brief)
            
            # Clarity score (0-1)
            scores["clarity"] = await self._score_clarity(asset)
            
            # Persuasiveness score (0-1)
            scores["persuasiveness"] = await self._score_persuasiveness(asset)
            
            # Brand alignment score (0-1)
            scores["brand_alignment"] = await self._score_brand_alignment(asset, creative_brief)
            
            # Compliance score (0-1)
            scores["compliance"] = await self._score_compliance(asset, creative_brief)
            
            # Calculate weighted average
            total_score = sum(scores[criterion] * weight for criterion, weight in self.quality_criteria.items())
            
            return round(total_score, 2)
            
        except Exception as e:
            self.logger.warning("Failed to assess asset quality", error=str(e))
            return 0.5  # Default neutral score
    
    async def _predict_asset_performance(self, asset: GeneratedAsset, creative_brief: CreativeBrief) -> Dict[str, float]:
        """Predict asset performance metrics."""
        try:
            predictions = {}
            
            for metric, predictor in self.performance_models.items():
                predictions[metric] = await predictor(asset, creative_brief)
            
            return predictions
            
        except Exception as e:
            self.logger.warning("Failed to predict asset performance", error=str(e))
            return {"ctr_prediction": 0.02, "engagement_prediction": 0.05, "conversion_prediction": 0.03}
    
    # Quality scoring methods (simplified implementations)
    
    async def _score_relevance(self, asset: GeneratedAsset, creative_brief: CreativeBrief) -> float:
        """Score asset relevance to the brief."""
        # Simple keyword matching for now
        content_text = str(asset.content).lower()
        brief_keywords = [msg.lower() for msg in creative_brief.key_messages]
        
        matches = sum(1 for keyword in brief_keywords if keyword in content_text)
        return min(1.0, matches / len(brief_keywords) if brief_keywords else 0.5)
    
    async def _score_clarity(self, asset: GeneratedAsset) -> float:
        """Score asset clarity and readability."""
        # Simple length and structure checks
        if asset.asset_type == AssetType.TEXT_AD:
            headlines = asset.content.get("headlines", [])
            if headlines:
                avg_length = sum(len(h) for h in headlines) / len(headlines)
                return 1.0 if 10 <= avg_length <= 30 else 0.7
        return 0.8  # Default score
    
    async def _score_persuasiveness(self, asset: GeneratedAsset) -> float:
        """Score asset persuasiveness."""
        # Look for persuasive elements
        content_text = str(asset.content).lower()
        persuasive_words = ["save", "free", "exclusive", "limited", "now", "today", "discover", "get"]
        
        matches = sum(1 for word in persuasive_words if word in content_text)
        return min(1.0, matches / 3)  # Score based on presence of persuasive elements
    
    async def _score_brand_alignment(self, asset: GeneratedAsset, creative_brief: CreativeBrief) -> float:
        """Score brand alignment."""
        # Check tone alignment
        content_text = str(asset.content).lower()
        tone = creative_brief.tone_of_voice.lower()
        
        if "professional" in tone and any(word in content_text for word in ["quality", "professional", "expert"]):
            return 0.9
        elif "friendly" in tone and any(word in content_text for word in ["friendly", "easy", "simple"]):
            return 0.9
        else:
            return 0.7  # Default alignment score
    
    async def _score_compliance(self, asset: GeneratedAsset, creative_brief: CreativeBrief) -> float:
        """Score compliance with advertising standards."""
        # Basic compliance checks
        content_text = str(asset.content).lower()
        
        # Check for problematic claims
        problematic = ["guaranteed", "100%", "miracle", "instant"]
        has_problems = any(word in content_text for word in problematic)
        
        return 0.5 if has_problems else 1.0
    
    # Performance prediction methods (simplified)
    
    async def _predict_click_through_rate(self, asset: GeneratedAsset, creative_brief: CreativeBrief) -> float:
        """Predict click-through rate."""
        # Simple model based on asset characteristics
        base_ctr = 0.02  # 2% baseline
        
        # Adjust based on quality score
        quality_multiplier = 1 + (asset.quality_score - 0.5)
        
        return round(base_ctr * quality_multiplier, 4)
    
    async def _predict_engagement_rate(self, asset: GeneratedAsset, creative_brief: CreativeBrief) -> float:
        """Predict engagement rate."""
        base_engagement = 0.05  # 5% baseline
        quality_multiplier = 1 + (asset.quality_score - 0.5)
        
        return round(base_engagement * quality_multiplier, 4)
    
    async def _predict_conversion_rate(self, asset: GeneratedAsset, creative_brief: CreativeBrief) -> float:
        """Predict conversion rate."""
        base_conversion = 0.03  # 3% baseline
        quality_multiplier = 1 + (asset.quality_score - 0.5)
        
        return round(base_conversion * quality_multiplier, 4)
    
    # Asset variation and optimization methods
    
    async def _generate_asset_variations(
        self,
        base_asset: GeneratedAsset,
        creative_brief: CreativeBrief,
        count: int
    ) -> List[Dict[str, Any]]:
        """Generate variations of a base asset."""
        variations = []
        
        for i in range(count):
            # Create variations by modifying specific elements
            variation = {
                "variation_id": f"{base_asset.asset_id}_v{i+1}",
                "content": dict(base_asset.content),  # Copy base content
                "variation_type": "style_variation",
                "changes": []
            }
            
            # Modify based on asset type
            if base_asset.asset_type == AssetType.TEXT_AD:
                # Vary headlines or descriptions
                if "headlines" in variation["content"]:
                    # Generate alternative headlines
                    alt_headlines = await self._generate_headlines(creative_brief, 3, 30)
                    variation["content"]["headlines"] = alt_headlines
                    variation["changes"].append("headlines_modified")
            
            variations.append(variation)
        
        return variations
    
    async def _identify_optimization_opportunities(
        self,
        asset: GeneratedAsset,
        performance_data: Dict[str, Any],
        goals: List[str]
    ) -> List[str]:
        """Identify optimization opportunities for an asset."""
        opportunities = []
        
        # Analyze performance metrics
        ctr = performance_data.get("ctr", 0)
        if ctr < 0.02:  # Below 2%
            opportunities.append("improve_headline_appeal")
        
        conversion_rate = performance_data.get("conversion_rate", 0)
        if conversion_rate < 0.03:  # Below 3%
            opportunities.append("strengthen_call_to_action")
        
        # Check quality score
        if asset.quality_score < 0.7:
            opportunities.append("improve_content_quality")
        
        return opportunities
    
    async def _create_optimized_asset(
        self,
        original_asset: GeneratedAsset,
        opportunities: List[str],
        performance_data: Dict[str, Any]
    ) -> GeneratedAsset:
        """Create optimized version of an asset."""
        # Create new asset based on optimization opportunities
        optimized_content = dict(original_asset.content)
        
        for opportunity in opportunities:
            if opportunity == "improve_headline_appeal":
                # Generate more appealing headlines
                if "headlines" in optimized_content:
                    optimized_content["headlines"] = [
                        "EXCLUSIVE: " + optimized_content["headlines"][0],
                        "LIMITED TIME: " + optimized_content["headlines"][1] if len(optimized_content["headlines"]) > 1 else "Special Offer Available"
                    ]
            
            elif opportunity == "strengthen_call_to_action":
                # Improve call-to-action
                if "call_to_action" in optimized_content:
                    optimized_content["call_to_action"] = "Act Now - Limited Time!"
        
        optimized_asset = GeneratedAsset(
            asset_id=f"{original_asset.asset_id}_optimized",
            asset_type=original_asset.asset_type,
            format=original_asset.format,
            content=optimized_content,
            metadata={
                "optimization_type": "performance_based",
                "original_asset_id": original_asset.asset_id,
                "optimization_opportunities": opportunities,
                "performance_trigger": performance_data
            },
            quality_score=0.0,  # Will be calculated
            compliance_status="pending",
            variations=[],
            performance_prediction={},
            created_at=datetime.utcnow(),
            version="1.1"
        )
        
        return optimized_asset
    
    async def _create_audience_specific_variation(
        self,
        base_asset: GeneratedAsset,
        audience: Dict[str, Any]
    ) -> AssetVariation:
        """Create audience-specific variation of an asset."""
        # Modify content based on audience characteristics
        audience_content = dict(base_asset.content)
        
        # Simple audience targeting adjustments
        if audience.get("age_group") == "18-24":
            # More casual, trendy language
            if "headlines" in audience_content:
                audience_content["headlines"] = [h.replace("Discover", "Check out") for h in audience_content["headlines"]]
        
        elif audience.get("age_group") == "45-65":
            # More professional, value-focused language
            if "headlines" in audience_content:
                audience_content["headlines"] = [h.replace("Check out", "Discover") for h in audience_content["headlines"]]
        
        return AssetVariation(
            variation_id=f"{base_asset.asset_id}_audience_{hash(str(audience))}",
            parent_asset_id=base_asset.asset_id,
            content=audience_content,
            variation_type="audience_segment",
            target_criteria=audience,
            quality_score=base_asset.quality_score,
            created_at=datetime.utcnow()
        )
    
    async def _create_ab_test_variations(self, base_asset: GeneratedAsset) -> List[AssetVariation]:
        """Create A/B test variations of an asset."""
        variations = []
        
        if base_asset.asset_type == AssetType.TEXT_AD:
            # Create variation with different headlines
            variant_content = dict(base_asset.content)
            if "headlines" in variant_content:
                variant_content["headlines"] = [h.upper() for h in variant_content["headlines"]]  # All caps variation
            
            variations.append(AssetVariation(
                variation_id=f"{base_asset.asset_id}_ab_caps",
                parent_asset_id=base_asset.asset_id,
                content=variant_content,
                variation_type="a_b_test",
                target_criteria={"test_type": "headline_caps"},
                quality_score=base_asset.quality_score,
                created_at=datetime.utcnow()
            ))
        
        return variations