"""
Project Orchestrator Agent for AiLex Ad Agent System.
Coordinates overall project workflow, manages agent delegation, and ensures task completion.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

import structlog
from crewai import Agent, Task, Crew

from ..base import BaseAiLexAgent, AgentContext, AgentError, AgentMessage
from ..workflow_types import WorkflowDefinition, WorkflowStep, ExecutionStrategy
from ..tracing import AgentTracer, create_agent_tracer
from models.agents import AgentType, AgentConfig, TaskPriority, TaskStatus
from services.openai_service import OpenAIService
from utils.config import settings


logger = structlog.get_logger(__name__)


class ProjectPhase(str, Enum):
    """Project phases in the ad campaign workflow."""
    DISCOVERY = "discovery"
    PLANNING = "planning"
    DEVELOPMENT = "development"
    TESTING = "testing"
    DEPLOYMENT = "deployment"
    MONITORING = "monitoring"
    OPTIMIZATION = "optimization"


class TaskType(str, Enum):
    """Types of tasks that can be orchestrated."""
    RESEARCH = "research"
    ANALYSIS = "analysis"
    CREATION = "creation"
    VALIDATION = "validation"
    DEPLOYMENT = "deployment"
    MONITORING = "monitoring"
    OPTIMIZATION = "optimization"


@dataclass
class ProjectPlan:
    """Comprehensive project plan for ad campaign management."""
    project_id: str
    campaign_objectives: List[str]
    timeline: Dict[str, datetime]
    resource_allocation: Dict[str, Any]
    task_dependencies: Dict[str, List[str]]
    risk_assessment: Dict[str, Any]
    success_metrics: List[Dict[str, Any]]
    phase_gates: Dict[ProjectPhase, List[str]]
    quality_gates: List[Dict[str, Any]]
    escalation_procedures: Dict[str, Any]


@dataclass
class TaskAssignment:
    """Task assignment to specific agents."""
    task_id: str
    agent_type: AgentType
    agent_id: Optional[str]
    priority: TaskPriority
    estimated_duration: timedelta
    dependencies: List[str]
    success_criteria: List[str]
    deliverables: List[str]
    allocated_resources: Dict[str, Any]


@dataclass
class ProjectStatus:
    """Current project status and progress."""
    project_id: str
    current_phase: ProjectPhase
    overall_progress: float  # 0.0 to 1.0
    phase_progress: Dict[ProjectPhase, float]
    active_tasks: List[str]
    completed_tasks: List[str]
    blocked_tasks: List[str]
    issues: List[Dict[str, Any]]
    risks: List[Dict[str, Any]]
    resource_utilization: Dict[str, float]
    quality_metrics: Dict[str, float]
    last_updated: datetime


class ProjectOrchestratorAgent(BaseAiLexAgent):
    """
    AI agent responsible for overall project coordination and workflow orchestration.
    Acts as the central coordinator for the entire ad campaign management system.
    """
    
    def __init__(self, agent_id: str, config: AgentConfig):
        super().__init__(
            agent_id=agent_id,
            name="Project Orchestrator Agent",
            description="Central coordinator for ad campaign projects, managing workflow orchestration, task delegation, and quality assurance",
            agent_type=AgentType.CAMPAIGN_PLANNING,  # Using closest available type
            config=config
        )
        
        # Initialize services
        self.openai_service: Optional[OpenAIService] = None
        # TODO: Re-enable FlowOrchestrator after fixing circular imports
        # self.flow_orchestrator: Optional[FlowOrchestrator] = None
        
        # Initialize tracer
        self.tracer = create_agent_tracer(self.agent_id, self.name)
        
        # Project management state
        self.active_projects: Dict[str, ProjectPlan] = {}
        self.project_statuses: Dict[str, ProjectStatus] = {}
        self.agent_registry: Dict[str, BaseAiLexAgent] = {}
        self.task_assignments: Dict[str, TaskAssignment] = {}
        
        # Workflow templates
        self.workflow_templates: Dict[str, WorkflowDefinition] = {}
        
        # Quality gates and checkpoints
        self.quality_gates = {
            "research_completion": ["market_research", "competitor_analysis", "keyword_research"],
            "strategy_approval": ["campaign_strategy", "budget_allocation", "timeline"],
            "asset_quality": ["content_quality", "brand_compliance", "performance_prediction"],
            "deployment_readiness": ["technical_validation", "security_review", "performance_test"],
            "go_live": ["final_approval", "monitoring_setup", "rollback_plan"]
        }
        
        # Performance thresholds
        self.performance_thresholds = {
            "task_completion_rate": 0.95,
            "quality_score": 0.8,
            "timeline_adherence": 0.9,
            "resource_efficiency": 0.85
        }
    
    async def _custom_initialize(self) -> None:
        """Custom initialization for project orchestrator."""
        try:
            # Initialize OpenAI service
            if settings.OPENAI_API_KEY:
                self.openai_service = OpenAIService()
            
            # TODO: Re-enable FlowOrchestrator after fixing circular imports
            # self.flow_orchestrator = FlowOrchestrator(tracer=self.tracer)
            
            # Load workflow templates
            await self._initialize_workflow_templates()
            
            # Set up event handlers
            await self._setup_event_handlers()
            
            self.logger.info(
                "Project orchestrator initialized",
                has_openai=bool(self.openai_service),
                workflow_templates=len(self.workflow_templates)
            )
            
        except Exception as e:
            raise AgentError(f"Failed to initialize project orchestrator: {str(e)}")
    
    async def create_project_plan(
        self,
        campaign_requirements: Dict[str, Any],
        business_context: Dict[str, Any],
        timeline_constraints: Dict[str, Any],
        resource_constraints: Dict[str, Any]
    ) -> ProjectPlan:
        """
        Create comprehensive project plan for ad campaign.
        
        Args:
            campaign_requirements: Campaign requirements and objectives
            business_context: Business context and constraints
            timeline_constraints: Timeline requirements and deadlines
            resource_constraints: Available resources and limitations
            
        Returns:
            ProjectPlan: Detailed project plan
        """
        async with self.tracer.trace_task_execution(
            task_id=f"create_project_plan_{hash(str(campaign_requirements))}",
            task_name="Project Plan Creation",
            task_data={
                "objectives": campaign_requirements.get("objectives", []),
                "budget": business_context.get("budget", 0),
                "timeline": timeline_constraints
            }
        ) as span:
            try:
                project_id = f"project_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
                
                self.logger.info(
                    "Creating project plan",
                    project_id=project_id,
                    objectives=campaign_requirements.get("objectives", [])
                )
                
                # Analyze requirements and create timeline
                timeline = await self._create_project_timeline(
                    campaign_requirements, timeline_constraints
                )
                
                # Allocate resources across phases
                resource_allocation = await self._allocate_project_resources(
                    campaign_requirements, resource_constraints
                )
                
                # Map task dependencies
                task_dependencies = await self._map_task_dependencies(
                    campaign_requirements
                )
                
                # Assess project risks
                risk_assessment = await self._assess_project_risks(
                    campaign_requirements, business_context, timeline_constraints
                )
                
                # Define success metrics
                success_metrics = await self._define_success_metrics(
                    campaign_requirements, business_context
                )
                
                # Create phase gates
                phase_gates = await self._create_phase_gates(campaign_requirements)
                
                # Define quality gates
                quality_gates = await self._define_quality_gates(campaign_requirements)
                
                # Set up escalation procedures
                escalation_procedures = await self._create_escalation_procedures(
                    business_context
                )
                
                project_plan = ProjectPlan(
                    project_id=project_id,
                    campaign_objectives=campaign_requirements.get("objectives", []),
                    timeline=timeline,
                    resource_allocation=resource_allocation,
                    task_dependencies=task_dependencies,
                    risk_assessment=risk_assessment,
                    success_metrics=success_metrics,
                    phase_gates=phase_gates,
                    quality_gates=quality_gates,
                    escalation_procedures=escalation_procedures
                )
                
                # Store project plan
                self.active_projects[project_id] = project_plan
                
                # Initialize project status
                self.project_statuses[project_id] = ProjectStatus(
                    project_id=project_id,
                    current_phase=ProjectPhase.DISCOVERY,
                    overall_progress=0.0,
                    phase_progress={phase: 0.0 for phase in ProjectPhase},
                    active_tasks=[],
                    completed_tasks=[],
                    blocked_tasks=[],
                    issues=[],
                    risks=[],
                    resource_utilization={},
                    quality_metrics={},
                    last_updated=datetime.utcnow()
                )
                
                self.tracer.record_task_result(span, {
                    "project_id": project_id,
                    "phases_count": len(phase_gates),
                    "quality_gates_count": len(quality_gates),
                    "success_metrics_count": len(success_metrics)
                }, True)
                
                self.logger.info(
                    "Project plan created",
                    project_id=project_id,
                    phases_count=len(phase_gates),
                    timeline_days=(timeline.get("project_end", datetime.utcnow()) - timeline.get("project_start", datetime.utcnow())).days
                )
                
                return project_plan
                
            except Exception as e:
                self.logger.error("Project plan creation failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Project plan creation failed: {str(e)}")
    
    async def orchestrate_workflow(
        self,
        project_id: str,
        workflow_type: str = "standard_campaign"
    ) -> str:
        """
        Orchestrate the execution of a project workflow.
        
        Args:
            project_id: Project identifier
            workflow_type: Type of workflow to execute
            
        Returns:
            str: Workflow execution ID
        """
        if project_id not in self.active_projects:
            raise AgentError(f"Project not found: {project_id}")
        
        async with self.tracer.trace_task_execution(
            task_id=f"orchestrate_workflow_{project_id}",
            task_name="Workflow Orchestration",
            task_data={"project_id": project_id, "workflow_type": workflow_type}
        ) as span:
            try:
                project_plan = self.active_projects[project_id]
                
                self.logger.info(
                    "Starting workflow orchestration",
                    project_id=project_id,
                    workflow_type=workflow_type
                )
                
                # Get workflow template
                workflow_template = self.workflow_templates.get(workflow_type)
                if not workflow_template:
                    raise AgentError(f"Workflow template not found: {workflow_type}")
                
                # Customize workflow for project
                customized_workflow = await self._customize_workflow_for_project(
                    workflow_template, project_plan
                )
                
                # TODO: Re-enable workflow orchestration after fixing circular imports
                # Register workflow with orchestrator
                # self.flow_orchestrator.register_workflow(customized_workflow)

                # Create execution context
                context = AgentContext(
                    campaign_id=project_id,
                    task_id=f"workflow_{workflow_type}",
                    metadata={
                        "project_plan": project_plan.__dict__,
                        "workflow_type": workflow_type
                    }
                )

                # Start workflow execution
                # execution_id = await self.flow_orchestrator.execute_workflow(
                #     customized_workflow.id,
                #     input_data={"project_id": project_id},
                #     context=context
                # )
                execution_id = f"temp_execution_{project_id}_{workflow_type}"
                
                # Update project status
                project_status = self.project_statuses[project_id]
                project_status.current_phase = ProjectPhase.PLANNING
                project_status.last_updated = datetime.utcnow()
                
                self.tracer.record_task_result(span, {
                    "execution_id": execution_id,
                    "workflow_steps": len(customized_workflow.steps)
                }, True)
                
                self.logger.info(
                    "Workflow orchestration started",
                    project_id=project_id,
                    execution_id=execution_id,
                    workflow_steps=len(customized_workflow.steps)
                )
                
                return execution_id
                
            except Exception as e:
                self.logger.error("Workflow orchestration failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Workflow orchestration failed: {str(e)}")
    
    async def delegate_task(
        self,
        task_specification: Dict[str, Any],
        target_agent_type: AgentType,
        priority: TaskPriority = TaskPriority.NORMAL,
        deadline: Optional[datetime] = None
    ) -> str:
        """
        Delegate a task to a specific agent type.
        
        Args:
            task_specification: Task details and requirements
            target_agent_type: Type of agent to handle the task
            priority: Task priority level
            deadline: Optional task deadline
            
        Returns:
            str: Task assignment ID
        """
        async with self.tracer.trace_task_execution(
            task_id=f"delegate_task_{target_agent_type.value}",
            task_name="Task Delegation",
            task_data={
                "task_type": task_specification.get("type"),
                "agent_type": target_agent_type.value,
                "priority": priority.value
            }
        ) as span:
            try:
                task_id = f"task_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
                
                self.logger.info(
                    "Delegating task",
                    task_id=task_id,
                    agent_type=target_agent_type.value,
                    priority=priority.value
                )
                
                # Find available agent
                available_agent = await self._find_available_agent(target_agent_type)
                
                # Create task assignment
                assignment = TaskAssignment(
                    task_id=task_id,
                    agent_type=target_agent_type,
                    agent_id=available_agent.agent_id if available_agent else None,
                    priority=priority,
                    estimated_duration=timedelta(hours=task_specification.get("estimated_hours", 2)),
                    dependencies=task_specification.get("dependencies", []),
                    success_criteria=task_specification.get("success_criteria", []),
                    deliverables=task_specification.get("deliverables", []),
                    allocated_resources=task_specification.get("resources", {})
                )
                
                self.task_assignments[task_id] = assignment
                
                # Send task to agent if available
                if available_agent:
                    await self._send_task_to_agent(available_agent, task_specification, task_id)
                else:
                    self.logger.warning(
                        "No available agent found for task",
                        task_id=task_id,
                        agent_type=target_agent_type.value
                    )
                
                self.tracer.record_task_result(span, {
                    "task_id": task_id,
                    "agent_assigned": bool(available_agent),
                    "estimated_hours": task_specification.get("estimated_hours", 2)
                }, True)
                
                return task_id
                
            except Exception as e:
                self.logger.error("Task delegation failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Task delegation failed: {str(e)}")
    
    async def monitor_project_progress(
        self,
        project_id: str
    ) -> ProjectStatus:
        """
        Monitor and update project progress.
        
        Args:
            project_id: Project identifier
            
        Returns:
            ProjectStatus: Current project status
        """
        if project_id not in self.project_statuses:
            raise AgentError(f"Project status not found: {project_id}")
        
        async with self.tracer.trace_task_execution(
            task_id=f"monitor_progress_{project_id}",
            task_name="Progress Monitoring",
            task_data={"project_id": project_id}
        ) as span:
            try:
                # Get current status
                status = self.project_statuses[project_id]
                
                # Update task statuses
                await self._update_task_statuses(project_id)
                
                # Calculate progress metrics
                await self._calculate_progress_metrics(project_id)
                
                # Check quality gates
                await self._check_quality_gates(project_id)
                
                # Identify risks and issues
                await self._identify_risks_and_issues(project_id)
                
                # Update resource utilization
                await self._update_resource_utilization(project_id)
                
                status.last_updated = datetime.utcnow()
                
                self.tracer.record_task_result(span, {
                    "overall_progress": status.overall_progress,
                    "active_tasks": len(status.active_tasks),
                    "completed_tasks": len(status.completed_tasks),
                    "issues_count": len(status.issues)
                }, True)
                
                self.logger.info(
                    "Project progress updated",
                    project_id=project_id,
                    progress=status.overall_progress,
                    active_tasks=len(status.active_tasks),
                    issues_count=len(status.issues)
                )
                
                return status
                
            except Exception as e:
                self.logger.error("Progress monitoring failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Progress monitoring failed: {str(e)}")
    
    async def handle_escalation(
        self,
        project_id: str,
        issue: Dict[str, Any],
        escalation_level: str = "standard"
    ) -> Dict[str, Any]:
        """
        Handle project escalations and issues.
        
        Args:
            project_id: Project identifier
            issue: Issue details
            escalation_level: Level of escalation (standard, urgent, critical)
            
        Returns:
            Dict[str, Any]: Escalation response and actions taken
        """
        async with self.tracer.trace_task_execution(
            task_id=f"handle_escalation_{project_id}",
            task_name="Escalation Handling",
            task_data={
                "project_id": project_id,
                "issue_type": issue.get("type"),
                "escalation_level": escalation_level
            }
        ) as span:
            try:
                self.logger.warning(
                    "Handling project escalation",
                    project_id=project_id,
                    issue_type=issue.get("type"),
                    escalation_level=escalation_level
                )
                
                # Analyze issue impact
                impact_analysis = await self._analyze_issue_impact(project_id, issue)
                
                # Determine response strategy
                response_strategy = await self._determine_response_strategy(
                    issue, escalation_level, impact_analysis
                )
                
                # Execute response actions
                actions_taken = await self._execute_response_actions(
                    project_id, response_strategy
                )
                
                # Update stakeholders
                await self._notify_stakeholders(project_id, issue, actions_taken)
                
                # Document escalation
                escalation_record = {
                    "timestamp": datetime.utcnow().isoformat(),
                    "issue": issue,
                    "escalation_level": escalation_level,
                    "impact_analysis": impact_analysis,
                    "response_strategy": response_strategy,
                    "actions_taken": actions_taken,
                    "status": "resolved" if actions_taken.get("resolved") else "in_progress"
                }
                
                # Add to project issues
                if project_id in self.project_statuses:
                    self.project_statuses[project_id].issues.append(escalation_record)
                
                self.tracer.record_task_result(span, {
                    "actions_count": len(actions_taken),
                    "resolved": actions_taken.get("resolved", False),
                    "impact_level": impact_analysis.get("level", "unknown")
                }, True)
                
                return escalation_record
                
            except Exception as e:
                self.logger.error("Escalation handling failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Escalation handling failed: {str(e)}")
    
    # Helper methods for project management
    
    async def _initialize_workflow_templates(self) -> None:
        """Initialize standard workflow templates."""
        # Standard campaign workflow
        standard_campaign = WorkflowDefinition(
            id="standard_campaign",
            name="Standard Ad Campaign Workflow",
            description="Standard workflow for ad campaign development",
            steps=[
                WorkflowStep(
                    id="market_research",
                    name="Market Research",
                    agent_type=AgentType.CAMPAIGN_PLANNING,
                    description="Conduct comprehensive market research and analysis",
                    timeout_seconds=1800,  # 30 minutes
                    priority=TaskPriority.HIGH
                ),
                WorkflowStep(
                    id="campaign_strategy",
                    name="Campaign Strategy Development",
                    agent_type=AgentType.CAMPAIGN_PLANNING,
                    description="Develop comprehensive campaign strategy",
                    dependencies=["market_research"],
                    timeout_seconds=2400,  # 40 minutes
                    priority=TaskPriority.HIGH
                ),
                WorkflowStep(
                    id="asset_generation",
                    name="Creative Asset Generation",
                    agent_type=AgentType.AD_ASSET_GENERATION,
                    description="Generate creative assets for campaign",
                    dependencies=["campaign_strategy"],
                    timeout_seconds=3600,  # 60 minutes
                    priority=TaskPriority.NORMAL
                ),
                WorkflowStep(
                    id="quality_review",
                    name="Quality Review",
                    agent_type=AgentType.QUALITY_ASSURANCE,
                    description="Review and validate campaign assets",
                    dependencies=["asset_generation"],
                    timeout_seconds=1200,  # 20 minutes
                    priority=TaskPriority.HIGH
                ),
                WorkflowStep(
                    id="deployment",
                    name="Campaign Deployment",
                    agent_type=AgentType.CAMPAIGN_PLANNING,  # Using closest available
                    description="Deploy campaign to advertising platforms",
                    dependencies=["quality_review"],
                    timeout_seconds=1800,  # 30 minutes
                    priority=TaskPriority.URGENT
                )
            ],
            strategy=ExecutionStrategy.SEQUENTIAL,
            max_duration_seconds=10800,  # 3 hours
            error_handling="stop"
        )
        
        self.workflow_templates["standard_campaign"] = standard_campaign
    
    async def _setup_event_handlers(self) -> None:
        """Set up event handlers for workflow events."""
        # TODO: Re-enable event handlers after fixing circular imports
        # if self.flow_orchestrator:
        #     self.flow_orchestrator.add_event_callback("workflow_completed", self._on_workflow_completed)
        #     self.flow_orchestrator.add_event_callback("workflow_failed", self._on_workflow_failed)
        #     self.flow_orchestrator.add_event_callback("task_completed", self._on_task_completed)
        #     self.flow_orchestrator.add_event_callback("task_failed", self._on_task_failed)
        pass
    
    async def _on_workflow_completed(self, event_type: str, data: Dict[str, Any]) -> None:
        """Handle workflow completion events."""
        execution_id = data.get("execution_id")
        self.logger.info("Workflow completed", execution_id=execution_id)
        
        # Update project status
        # Implementation would update relevant project status
    
    async def _on_workflow_failed(self, event_type: str, data: Dict[str, Any]) -> None:
        """Handle workflow failure events."""
        execution_id = data.get("execution_id")
        error = data.get("error")
        self.logger.error("Workflow failed", execution_id=execution_id, error=error)
        
        # Trigger escalation
        # Implementation would trigger appropriate escalation procedures
    
    async def _on_task_completed(self, event_type: str, data: Dict[str, Any]) -> None:
        """Handle task completion events."""
        task_id = data.get("task_id")
        self.logger.info("Task completed", task_id=task_id)
    
    async def _on_task_failed(self, event_type: str, data: Dict[str, Any]) -> None:
        """Handle task failure events."""
        task_id = data.get("task_id")
        error = data.get("error")
        self.logger.warning("Task failed", task_id=task_id, error=error)
    
    # Implementation helper methods (abbreviated for space)
    
    async def _create_project_timeline(self, requirements: Dict[str, Any], constraints: Dict[str, Any]) -> Dict[str, datetime]:
        """Create project timeline based on requirements and constraints."""
        start_date = datetime.utcnow() + timedelta(days=1)
        duration_days = constraints.get("duration_days", 30)
        
        return {
            "project_start": start_date,
            "discovery_end": start_date + timedelta(days=3),
            "planning_end": start_date + timedelta(days=8),
            "development_end": start_date + timedelta(days=20),
            "testing_end": start_date + timedelta(days=25),
            "deployment_end": start_date + timedelta(days=28),
            "project_end": start_date + timedelta(days=duration_days)
        }
    
    async def _allocate_project_resources(self, requirements: Dict[str, Any], constraints: Dict[str, Any]) -> Dict[str, Any]:
        """Allocate resources across project phases."""
        total_budget = constraints.get("budget", 100000)
        
        return {
            "discovery": {"budget": total_budget * 0.1, "agents": 2},
            "planning": {"budget": total_budget * 0.15, "agents": 3},
            "development": {"budget": total_budget * 0.4, "agents": 5},
            "testing": {"budget": total_budget * 0.15, "agents": 3},
            "deployment": {"budget": total_budget * 0.1, "agents": 2},
            "monitoring": {"budget": total_budget * 0.1, "agents": 2}
        }
    
    async def _map_task_dependencies(self, requirements: Dict[str, Any]) -> Dict[str, List[str]]:
        """Map task dependencies for project."""
        return {
            "campaign_strategy": ["market_research", "competitor_analysis"],
            "asset_generation": ["campaign_strategy", "brand_guidelines"],
            "quality_review": ["asset_generation"],
            "deployment": ["quality_review", "technical_validation"],
            "monitoring": ["deployment"]
        }
    
    async def _assess_project_risks(self, requirements: Dict[str, Any], business_context: Dict[str, Any], timeline: Dict[str, Any]) -> Dict[str, Any]:
        """Assess project risks and mitigation strategies."""
        return {
            "high_risks": [
                {"risk": "Timeline constraints", "probability": 0.7, "impact": "high"},
                {"risk": "Resource availability", "probability": 0.5, "impact": "medium"}
            ],
            "medium_risks": [
                {"risk": "Scope creep", "probability": 0.6, "impact": "medium"},
                {"risk": "Quality issues", "probability": 0.4, "impact": "high"}
            ],
            "mitigation_strategies": [
                "Implement regular checkpoint reviews",
                "Maintain resource buffer of 20%",
                "Establish clear scope boundaries",
                "Implement automated quality checks"
            ]
        }
    
    async def _define_success_metrics(self, requirements: Dict[str, Any], business_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Define project success metrics."""
        return [
            {"metric": "Timeline adherence", "target": ">90%", "weight": 0.3},
            {"metric": "Quality score", "target": ">80%", "weight": 0.25},
            {"metric": "Budget efficiency", "target": "<105%", "weight": 0.2},
            {"metric": "Stakeholder satisfaction", "target": ">85%", "weight": 0.25}
        ]
    
    async def _create_phase_gates(self, requirements: Dict[str, Any]) -> Dict[ProjectPhase, List[str]]:
        """Create phase gate criteria."""
        return {
            ProjectPhase.DISCOVERY: ["Requirements finalized", "Stakeholders aligned"],
            ProjectPhase.PLANNING: ["Strategy approved", "Resources allocated"],
            ProjectPhase.DEVELOPMENT: ["Assets created", "Quality reviewed"],
            ProjectPhase.TESTING: ["Tests passed", "Performance validated"],
            ProjectPhase.DEPLOYMENT: ["Deployment successful", "Monitoring active"]
        }
    
    async def _define_quality_gates(self, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Define quality gate criteria."""
        return [
            {"gate": "Strategy Quality", "criteria": ["completeness", "feasibility", "alignment"]},
            {"gate": "Asset Quality", "criteria": ["brand_compliance", "message_clarity", "technical_specs"]},
            {"gate": "Deployment Quality", "criteria": ["functionality", "performance", "security"]}
        ]
    
    async def _create_escalation_procedures(self, business_context: Dict[str, Any]) -> Dict[str, Any]:
        """Create escalation procedures."""
        return {
            "level_1": {"threshold": "minor_issues", "response_time": "4_hours", "stakeholders": ["project_manager"]},
            "level_2": {"threshold": "major_issues", "response_time": "2_hours", "stakeholders": ["project_manager", "team_lead"]},
            "level_3": {"threshold": "critical_issues", "response_time": "1_hour", "stakeholders": ["project_manager", "team_lead", "executive"]}
        }
    
    # Additional helper methods would be implemented here for:
    # - _customize_workflow_for_project
    # - _find_available_agent  
    # - _send_task_to_agent
    # - _update_task_statuses
    # - _calculate_progress_metrics
    # - _check_quality_gates
    # - _identify_risks_and_issues
    # - _update_resource_utilization
    # - _analyze_issue_impact
    # - _determine_response_strategy
    # - _execute_response_actions
    # - _notify_stakeholders
    
    # For brevity, providing simplified implementations
    
    async def _customize_workflow_for_project(self, template: WorkflowDefinition, plan: ProjectPlan) -> WorkflowDefinition:
        """Customize workflow template for specific project."""
        customized = WorkflowDefinition(
            id=f"{template.id}_{plan.project_id}",
            name=f"{template.name} - {plan.project_id}",
            description=template.description,
            steps=template.steps.copy(),
            strategy=template.strategy,
            max_duration_seconds=template.max_duration_seconds
        )
        return customized
    
    async def _find_available_agent(self, agent_type: AgentType) -> Optional[BaseAiLexAgent]:
        """Find available agent of specified type."""
        # In real implementation, would check agent registry and availability
        return None
    
    async def _send_task_to_agent(self, agent: BaseAiLexAgent, task_spec: Dict[str, Any], task_id: str) -> None:
        """Send task to agent for execution."""
        message = AgentMessage(
            sender_id=self.agent_id,
            recipient_id=agent.agent_id,
            message_type="task_request",
            content={
                "task_id": task_id,
                "specification": task_spec
            },
            requires_response=True
        )
        await agent.handle_message(message)
    
    # Simplified implementations for monitoring methods
    async def _update_task_statuses(self, project_id: str) -> None:
        """Update task statuses for project."""
        pass
    
    async def _calculate_progress_metrics(self, project_id: str) -> None:
        """Calculate progress metrics."""
        pass
    
    async def _check_quality_gates(self, project_id: str) -> None:
        """Check quality gate compliance."""
        pass
    
    async def _identify_risks_and_issues(self, project_id: str) -> None:
        """Identify current risks and issues."""
        pass
    
    async def _update_resource_utilization(self, project_id: str) -> None:
        """Update resource utilization metrics."""
        pass
    
    async def _analyze_issue_impact(self, project_id: str, issue: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze impact of an issue."""
        return {"level": "medium", "affected_tasks": [], "timeline_impact": 0}
    
    async def _determine_response_strategy(self, issue: Dict[str, Any], level: str, impact: Dict[str, Any]) -> Dict[str, Any]:
        """Determine response strategy for issue."""
        return {"type": "mitigation", "actions": ["reassign_resources", "adjust_timeline"]}
    
    async def _execute_response_actions(self, project_id: str, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Execute response actions."""
        return {"resolved": True, "actions_completed": strategy.get("actions", [])}
    
    async def _notify_stakeholders(self, project_id: str, issue: Dict[str, Any], actions: Dict[str, Any]) -> None:
        """Notify relevant stakeholders."""
        pass