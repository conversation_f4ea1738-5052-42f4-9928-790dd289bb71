"""
Keyword Research Agent for Google Ads Campaign Optimization.
Handles keyword discovery, analysis, and optimization recommendations.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union, Set
from dataclasses import dataclass, field
from enum import Enum

import structlog
from crewai import Agent, Task
from crewai_tools import SerperDevTool, WebsiteSearchTool

from ..base import BaseAiLexAgent, AgentContext, AgentError
from ..tracing import AgentTracer, create_agent_tracer
from models.agents import AgentType, AgentConfig
from services.google_ads import GoogleAdsService
from services.openai_service import OpenAIService
from utils.config import settings


logger = structlog.get_logger(__name__)


class KeywordMatchType(str, Enum):
    """Google Ads keyword match types."""
    EXACT = "exact"
    PHRASE = "phrase"
    BROAD = "broad"
    BROAD_MODIFIED = "broad_modified"


class KeywordIntentType(str, Enum):
    """User intent types for keywords."""
    INFORMATIONAL = "informational"
    NAVIGATIONAL = "navigational"
    TRANSACTIONAL = "transactional"
    COMMERCIAL = "commercial"


class CompetitionLevel(str, Enum):
    """Keyword competition levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    UNKNOWN = "unknown"


@dataclass
class KeywordData:
    """Individual keyword data structure."""
    keyword: str
    match_type: KeywordMatchType
    search_volume: Optional[int] = None
    competition: CompetitionLevel = CompetitionLevel.UNKNOWN
    competition_index: Optional[float] = None
    avg_cpc: Optional[float] = None
    intent_type: Optional[KeywordIntentType] = None
    difficulty_score: Optional[float] = None
    opportunity_score: Optional[float] = None
    seasonal_trends: List[float] = field(default_factory=list)
    related_keywords: List[str] = field(default_factory=list)
    top_competitors: List[str] = field(default_factory=list)
    commercial_intent_score: Optional[float] = None
    quality_score_estimate: Optional[float] = None
    created_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class KeywordCluster:
    """Grouped keywords by theme or intent."""
    cluster_id: str
    theme: str
    keywords: List[KeywordData]
    primary_keyword: str
    total_search_volume: int
    avg_cpc: float
    competition_level: CompetitionLevel
    intent_type: KeywordIntentType
    cluster_score: float
    recommended_ad_groups: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class CompetitorKeywordAnalysis:
    """Competitor keyword analysis results."""
    competitor_domain: str
    shared_keywords: List[str]
    competitor_only_keywords: List[str]
    missed_opportunities: List[KeywordData]
    overlap_percentage: float
    competitive_gaps: List[str]
    outranking_opportunities: List[str]
    analyzed_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class KeywordResearchReport:
    """Complete keyword research report."""
    report_id: str
    business_description: str
    target_audience: Dict[str, Any]
    research_scope: Dict[str, Any]
    primary_keywords: List[KeywordData]
    secondary_keywords: List[KeywordData]
    long_tail_keywords: List[KeywordData]
    negative_keywords: List[str]
    keyword_clusters: List[KeywordCluster]
    competitor_analysis: List[CompetitorKeywordAnalysis]
    seasonal_insights: Dict[str, Any]
    recommendations: List[str]
    implementation_plan: Dict[str, Any]
    total_keywords_analyzed: int
    estimated_budget_requirements: Dict[str, float]
    created_at: datetime = field(default_factory=datetime.utcnow)


class KeywordResearchAgent(BaseAiLexAgent):
    """
    AI agent specialized in Google Ads keyword research, analysis, and optimization.
    """
    
    def __init__(self, agent_id: str, config: AgentConfig):
        super().__init__(
            agent_id=agent_id,
            name="Keyword Research Agent",
            description="Specialized AI agent for Google Ads keyword research, competitor analysis, and keyword strategy optimization",
            agent_type=AgentType.KEYWORD_RESEARCH,
            config=config
        )
        
        # Initialize services
        self.google_ads_service: Optional[GoogleAdsService] = None
        self.openai_service: Optional[OpenAIService] = None
        
        # Initialize tracer
        self.tracer = create_agent_tracer(self.agent_id, self.name)
        
        # Research tools
        self.research_tools = []
        
        # Keyword scoring weights
        self.scoring_weights = {
            "search_volume": 0.3,
            "competition": 0.25,
            "commercial_intent": 0.2,
            "relevance": 0.15,
            "trend": 0.1
        }
        
        # Intent indicators
        self.intent_indicators = {
            KeywordIntentType.TRANSACTIONAL: [
                "buy", "purchase", "order", "shop", "price", "cost", "deal", "discount",
                "cheap", "best", "review", "compare", "vs", "alternative"
            ],
            KeywordIntentType.COMMERCIAL: [
                "service", "company", "provider", "solution", "software", "tool",
                "platform", "consulting", "professional", "enterprise"
            ],
            KeywordIntentType.INFORMATIONAL: [
                "how", "what", "why", "when", "where", "guide", "tutorial",
                "tips", "learn", "training", "course", "help"
            ],
            KeywordIntentType.NAVIGATIONAL: [
                "login", "sign in", "official", "website", "homepage", "contact",
                "support", "account", "portal", "dashboard"
            ]
        }
        
        # Data storage
        self.keyword_cache: Dict[str, KeywordData] = {}
        self.research_cache: Dict[str, KeywordResearchReport] = {}
        self.competitor_cache: Dict[str, CompetitorKeywordAnalysis] = {}
    
    async def _custom_initialize(self) -> None:
        """Custom initialization for keyword research agent."""
        try:
            # Initialize Google Ads service
            if all([
                settings.GOOGLE_ADS_DEVELOPER_TOKEN,
                settings.GOOGLE_ADS_CLIENT_ID,
                settings.GOOGLE_ADS_CLIENT_SECRET,
                settings.GOOGLE_ADS_REFRESH_TOKEN
            ]):
                self.google_ads_service = GoogleAdsService()
            
            # Initialize OpenAI service
            if settings.OPENAI_API_KEY:
                self.openai_service = OpenAIService()
            
            # Initialize research tools
            await self._initialize_research_tools()
            
            self.logger.info(
                "Keyword research agent initialized",
                has_google_ads=bool(self.google_ads_service),
                has_openai=bool(self.openai_service),
                tools_count=len(self.research_tools)
            )
            
        except Exception as e:
            raise AgentError(f"Failed to initialize keyword research agent: {str(e)}")
    
    async def _initialize_research_tools(self) -> None:
        """Initialize keyword research and analysis tools."""
        try:
            # Search tool for keyword discovery
            if hasattr(SerperDevTool, '__init__'):
                self.research_tools.append(SerperDevTool())
            
            # Website analysis tool
            self.research_tools.append(WebsiteSearchTool())
            
            self.logger.debug("Research tools initialized", tools_count=len(self.research_tools))
            
        except Exception as e:
            self.logger.warning("Some research tools not available", error=str(e))
    
    async def conduct_comprehensive_keyword_research(
        self,
        business_description: str,
        target_audience: Dict[str, Any],
        competitors: List[str],
        seed_keywords: List[str],
        research_scope: Dict[str, Any]
    ) -> KeywordResearchReport:
        """
        Conduct comprehensive keyword research for Google Ads campaigns.
        
        Args:
            business_description: Description of the business and offerings
            target_audience: Target audience characteristics
            competitors: List of competitor domains/names
            seed_keywords: Initial keyword ideas
            research_scope: Research parameters (volume thresholds, etc.)
            
        Returns:
            KeywordResearchReport: Complete keyword research report
        """
        async with self.tracer.trace_task_execution(
            task_id=f"keyword_research_{hash(business_description)}",
            task_name="Comprehensive Keyword Research",
            task_data={
                "business_description": business_description,
                "seed_keywords_count": len(seed_keywords),
                "competitors_count": len(competitors)
            }
        ) as span:
            try:
                self.logger.info(
                    "Starting comprehensive keyword research",
                    business=business_description[:100],
                    seed_keywords_count=len(seed_keywords),
                    competitors_count=len(competitors)
                )
                
                # Phase 1: Seed keyword expansion
                expanded_keywords = await self._expand_seed_keywords(
                    seed_keywords, business_description, target_audience
                )
                
                # Phase 2: Competitor keyword analysis
                competitor_analysis = await self._analyze_competitor_keywords(competitors)
                
                # Phase 3: Keyword data enrichment
                enriched_keywords = await self._enrich_keyword_data(
                    expanded_keywords, research_scope
                )
                
                # Phase 4: Keyword classification and clustering
                keyword_clusters = await self._cluster_keywords(enriched_keywords)
                
                # Phase 5: Negative keyword identification
                negative_keywords = await self._identify_negative_keywords(
                    enriched_keywords, business_description
                )
                
                # Phase 6: Seasonal and trend analysis
                seasonal_insights = await self._analyze_seasonal_trends(
                    enriched_keywords[:50]  # Limit to top 50 for trend analysis
                )
                
                # Categorize keywords by priority
                primary_keywords, secondary_keywords, long_tail_keywords = await self._categorize_keywords(
                    enriched_keywords
                )
                
                # Generate recommendations and implementation plan
                recommendations = await self._generate_keyword_recommendations(
                    primary_keywords, secondary_keywords, keyword_clusters, competitor_analysis
                )
                
                implementation_plan = await self._create_implementation_plan(
                    keyword_clusters, research_scope
                )
                
                # Calculate budget requirements
                budget_requirements = await self._estimate_budget_requirements(
                    primary_keywords, secondary_keywords, research_scope
                )
                
                # Create comprehensive report
                report = KeywordResearchReport(
                    report_id=f"keyword_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                    business_description=business_description,
                    target_audience=target_audience,
                    research_scope=research_scope,
                    primary_keywords=primary_keywords,
                    secondary_keywords=secondary_keywords,
                    long_tail_keywords=long_tail_keywords,
                    negative_keywords=negative_keywords,
                    keyword_clusters=keyword_clusters,
                    competitor_analysis=competitor_analysis,
                    seasonal_insights=seasonal_insights,
                    recommendations=recommendations,
                    implementation_plan=implementation_plan,
                    total_keywords_analyzed=len(enriched_keywords),
                    estimated_budget_requirements=budget_requirements
                )
                
                # Cache report
                self.research_cache[report.report_id] = report
                
                self.tracer.record_task_result(span, {
                    "report_id": report.report_id,
                    "total_keywords": len(enriched_keywords),
                    "primary_keywords": len(primary_keywords),
                    "clusters": len(keyword_clusters),
                    "competitors_analyzed": len(competitor_analysis)
                }, True)
                
                self.logger.info(
                    "Comprehensive keyword research completed",
                    report_id=report.report_id,
                    total_keywords=len(enriched_keywords),
                    primary_keywords=len(primary_keywords),
                    clusters=len(keyword_clusters)
                )
                
                return report
                
            except Exception as e:
                self.logger.error("Comprehensive keyword research failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Comprehensive keyword research failed: {str(e)}")
    
    async def analyze_existing_keywords(
        self,
        existing_keywords: List[str],
        campaign_performance: Dict[str, Any],
        optimization_goals: List[str]
    ) -> Dict[str, Any]:
        """
        Analyze existing keyword performance and provide optimization recommendations.
        
        Args:
            existing_keywords: Current campaign keywords
            campaign_performance: Performance data for existing keywords
            optimization_goals: Optimization objectives
            
        Returns:
            Dict[str, Any]: Keyword analysis and optimization recommendations
        """
        async with self.tracer.trace_task_execution(
            task_id=f"analyze_keywords_{hash(str(existing_keywords))}",
            task_name="Existing Keywords Analysis",
            task_data={
                "keywords_count": len(existing_keywords),
                "goals": optimization_goals
            }
        ) as span:
            try:
                self.logger.info(
                    "Analyzing existing keywords",
                    keywords_count=len(existing_keywords),
                    goals=optimization_goals
                )
                
                # Enrich existing keywords with fresh data
                enriched_keywords = await self._enrich_keyword_data(existing_keywords, {})
                
                # Analyze performance patterns
                performance_analysis = await self._analyze_keyword_performance_patterns(
                    enriched_keywords, campaign_performance
                )
                
                # Identify optimization opportunities
                optimization_opportunities = await self._identify_keyword_optimization_opportunities(
                    enriched_keywords, campaign_performance, optimization_goals
                )
                
                # Generate expansion suggestions
                expansion_suggestions = await self._generate_keyword_expansion_suggestions(
                    enriched_keywords, campaign_performance
                )
                
                # Identify underperforming keywords
                underperforming_keywords = await self._identify_underperforming_keywords(
                    enriched_keywords, campaign_performance
                )
                
                # Create match type recommendations
                match_type_recommendations = await self._recommend_match_type_changes(
                    enriched_keywords, campaign_performance
                )
                
                analysis_results = {
                    "performance_analysis": performance_analysis,
                    "optimization_opportunities": optimization_opportunities,
                    "expansion_suggestions": expansion_suggestions,
                    "underperforming_keywords": underperforming_keywords,
                    "match_type_recommendations": match_type_recommendations,
                    "negative_keyword_suggestions": await self._suggest_negative_keywords_from_performance(
                        campaign_performance
                    ),
                    "budget_reallocation_suggestions": await self._suggest_budget_reallocation(
                        enriched_keywords, campaign_performance
                    )
                }
                
                self.tracer.record_task_result(span, {
                    "keywords_analyzed": len(enriched_keywords),
                    "opportunities_found": len(optimization_opportunities),
                    "expansion_suggestions": len(expansion_suggestions),
                    "underperforming_count": len(underperforming_keywords)
                }, True)
                
                self.logger.info(
                    "Existing keywords analysis completed",
                    keywords_analyzed=len(enriched_keywords),
                    opportunities_found=len(optimization_opportunities)
                )
                
                return analysis_results
                
            except Exception as e:
                self.logger.error("Existing keywords analysis failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Existing keywords analysis failed: {str(e)}")
    
    async def discover_competitor_keywords(
        self,
        competitor_domains: List[str],
        focus_areas: List[str],
        analysis_depth: str = "standard"
    ) -> List[CompetitorKeywordAnalysis]:
        """
        Discover and analyze competitor keywords for opportunities.
        
        Args:
            competitor_domains: List of competitor domains to analyze
            focus_areas: Specific areas to focus analysis on
            analysis_depth: Depth of analysis (basic, standard, comprehensive)
            
        Returns:
            List[CompetitorKeywordAnalysis]: Competitor keyword analysis results
        """
        async with self.tracer.trace_task_execution(
            task_id=f"competitor_keywords_{hash(str(competitor_domains))}",
            task_name="Competitor Keyword Discovery",
            task_data={
                "competitors_count": len(competitor_domains),
                "focus_areas": focus_areas,
                "analysis_depth": analysis_depth
            }
        ) as span:
            try:
                self.logger.info(
                    "Starting competitor keyword discovery",
                    competitors_count=len(competitor_domains),
                    focus_areas=focus_areas,
                    depth=analysis_depth
                )
                
                competitor_analyses = []
                
                for competitor_domain in competitor_domains:
                    try:
                        analysis = await self._analyze_single_competitor_keywords(
                            competitor_domain, focus_areas, analysis_depth
                        )
                        competitor_analyses.append(analysis)
                        
                        # Cache analysis
                        self.competitor_cache[competitor_domain] = analysis
                        
                    except Exception as e:
                        self.logger.warning(
                            "Failed to analyze competitor",
                            competitor=competitor_domain,
                            error=str(e)
                        )
                
                self.tracer.record_task_result(span, {
                    "competitors_analyzed": len(competitor_analyses),
                    "total_keywords_found": sum(
                        len(analysis.competitor_only_keywords) for analysis in competitor_analyses
                    )
                }, True)
                
                self.logger.info(
                    "Competitor keyword discovery completed",
                    competitors_analyzed=len(competitor_analyses),
                    total_opportunities=sum(
                        len(analysis.missed_opportunities) for analysis in competitor_analyses
                    )
                )
                
                return competitor_analyses
                
            except Exception as e:
                self.logger.error("Competitor keyword discovery failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Competitor keyword discovery failed: {str(e)}")
    
    async def generate_keyword_variations(
        self,
        base_keywords: List[str],
        variation_types: List[str],
        target_audience: Dict[str, Any]
    ) -> Dict[str, List[KeywordData]]:
        """
        Generate keyword variations and long-tail opportunities.
        
        Args:
            base_keywords: Base keywords to expand
            variation_types: Types of variations to generate
            target_audience: Target audience characteristics
            
        Returns:
            Dict[str, List[KeywordData]]: Generated keyword variations by type
        """
        async with self.tracer.trace_task_execution(
            task_id=f"keyword_variations_{hash(str(base_keywords))}",
            task_name="Keyword Variations Generation",
            task_data={
                "base_keywords_count": len(base_keywords),
                "variation_types": variation_types
            }
        ) as span:
            try:
                self.logger.info(
                    "Generating keyword variations",
                    base_keywords_count=len(base_keywords),
                    variation_types=variation_types
                )
                
                variations = {}
                
                for variation_type in variation_types:
                    if variation_type == "long_tail":
                        variations[variation_type] = await self._generate_long_tail_variations(
                            base_keywords, target_audience
                        )
                    elif variation_type == "local":
                        variations[variation_type] = await self._generate_local_variations(
                            base_keywords, target_audience
                        )
                    elif variation_type == "commercial":
                        variations[variation_type] = await self._generate_commercial_variations(
                            base_keywords
                        )
                    elif variation_type == "informational":
                        variations[variation_type] = await self._generate_informational_variations(
                            base_keywords
                        )
                    elif variation_type == "brand_comparison":
                        variations[variation_type] = await self._generate_brand_comparison_variations(
                            base_keywords
                        )
                    elif variation_type == "seasonal":
                        variations[variation_type] = await self._generate_seasonal_variations(
                            base_keywords
                        )
                
                total_variations = sum(len(var_list) for var_list in variations.values())
                
                self.tracer.record_task_result(span, {
                    "variation_types_count": len(variations),
                    "total_variations": total_variations,
                    "variations_by_type": {k: len(v) for k, v in variations.items()}
                }, True)
                
                self.logger.info(
                    "Keyword variations generation completed",
                    variation_types_count=len(variations),
                    total_variations=total_variations
                )
                
                return variations
                
            except Exception as e:
                self.logger.error("Keyword variations generation failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Keyword variations generation failed: {str(e)}")
    
    # Core keyword research methods
    
    async def _expand_seed_keywords(
        self,
        seed_keywords: List[str],
        business_description: str,
        target_audience: Dict[str, Any]
    ) -> List[str]:
        """Expand seed keywords using various techniques."""
        try:
            expanded_keywords = set(seed_keywords)
            
            # AI-powered keyword expansion
            if self.openai_service:
                ai_keywords = await self._ai_keyword_expansion(
                    seed_keywords, business_description, target_audience
                )
                expanded_keywords.update(ai_keywords)
            
            # Google Ads keyword ideas
            if self.google_ads_service:
                for seed_keyword in seed_keywords:
                    try:
                        keyword_ideas = await self.google_ads_service.get_keyword_ideas(
                            [seed_keyword]
                        )
                        for idea in keyword_ideas:
                            expanded_keywords.add(idea.get("keyword", ""))
                    except Exception as e:
                        self.logger.warning(
                            "Failed to get keyword ideas from Google Ads",
                            seed_keyword=seed_keyword,
                            error=str(e)
                        )
            
            # Related keyword generation
            for seed_keyword in seed_keywords:
                related = await self._generate_related_keywords(seed_keyword)
                expanded_keywords.update(related)
            
            # Remove empty strings and duplicates
            expanded_keywords = [kw for kw in expanded_keywords if kw.strip()]
            
            self.logger.debug(
                "Seed keywords expanded",
                original_count=len(seed_keywords),
                expanded_count=len(expanded_keywords)
            )
            
            return list(expanded_keywords)
            
        except Exception as e:
            self.logger.error("Seed keyword expansion failed", error=str(e))
            return seed_keywords
    
    async def _ai_keyword_expansion(
        self,
        seed_keywords: List[str],
        business_description: str,
        target_audience: Dict[str, Any]
    ) -> List[str]:
        """Use AI to generate relevant keyword expansions."""
        try:
            prompt = f"""
            Generate 20-30 highly relevant keyword variations and expansions for Google Ads campaigns based on:
            
            Seed Keywords: {', '.join(seed_keywords)}
            Business: {business_description}
            Target Audience: {target_audience}
            
            Focus on generating keywords that are:
            1. Highly relevant to the business and audience
            2. Likely to have commercial intent
            3. Varied in length (2-5 words)
            4. Include both broad and specific variations
            5. Consider user pain points and needs
            
            Return as a JSON array of keyword strings only.
            """
            
            async with self.tracer.trace_llm_call(
                model_name=self.config.model.model_name,
                prompt=prompt,
                temperature=0.7,
                max_tokens=1000
            ) as span:
                response = await self.openai_service.generate_completion(
                    prompt=prompt,
                    model=self.config.model.model_name,
                    temperature=0.7,
                    max_tokens=1000
                )
                
                try:
                    keywords = json.loads(response)
                    if isinstance(keywords, list):
                        return [kw for kw in keywords if isinstance(kw, str) and len(kw.strip()) > 0]
                except json.JSONDecodeError:
                    # Fallback: extract keywords from text
                    lines = response.split('\n')
                    return [line.strip().strip('"').strip("'") for line in lines 
                           if line.strip() and not line.strip().startswith(('#', '//', '-'))]
                
                self.tracer.record_llm_response(span, response, len(response.split()))
            
            return []
            
        except Exception as e:
            self.logger.warning("AI keyword expansion failed", error=str(e))
            return []
    
    async def _generate_related_keywords(self, seed_keyword: str) -> List[str]:
        """Generate related keywords using patterns and modifiers."""
        try:
            related_keywords = []
            
            # Common modifiers for business keywords
            modifiers = {
                "quality": ["best", "top", "premium", "professional", "quality", "expert"],
                "action": ["buy", "get", "find", "choose", "compare", "review"],
                "local": ["near me", "local", "nearby", "in [city]"],
                "price": ["cheap", "affordable", "budget", "cost", "price", "free"],
                "features": ["with", "for", "that", "which", "how to"]
            }
            
            # Generate combinations
            for category, mod_list in modifiers.items():
                for modifier in mod_list:
                    # Prefix modifiers
                    related_keywords.append(f"{modifier} {seed_keyword}")
                    # Suffix modifiers (for some categories)
                    if category in ["local", "features"]:
                        related_keywords.append(f"{seed_keyword} {modifier}")
            
            # Plural/singular variations
            if seed_keyword.endswith('s'):
                related_keywords.append(seed_keyword[:-1])  # Remove 's'
            else:
                related_keywords.append(f"{seed_keyword}s")  # Add 's'
            
            return related_keywords[:15]  # Limit to top 15
            
        except Exception as e:
            self.logger.warning("Related keyword generation failed", error=str(e))
            return []
    
    async def _enrich_keyword_data(
        self,
        keywords: List[str],
        research_scope: Dict[str, Any]
    ) -> List[KeywordData]:
        """Enrich keywords with search volume, competition, and other data."""
        try:
            enriched_keywords = []
            
            # Process keywords in batches for API efficiency
            batch_size = 50
            for i in range(0, len(keywords), batch_size):
                batch = keywords[i:i + batch_size]
                
                # Get Google Ads keyword data
                keyword_data_map = {}
                if self.google_ads_service:
                    try:
                        ads_data = await self.google_ads_service.get_keyword_ideas(batch)
                        for data in ads_data:
                            keyword_data_map[data.get("keyword", "")] = data
                    except Exception as e:
                        self.logger.warning("Failed to get Google Ads data for batch", error=str(e))
                
                # Create KeywordData objects
                for keyword in batch:
                    try:
                        ads_data = keyword_data_map.get(keyword, {})
                        
                        keyword_obj = KeywordData(
                            keyword=keyword,
                            match_type=KeywordMatchType.BROAD,  # Default match type
                            search_volume=ads_data.get("search_volume"),
                            competition=self._map_competition_level(ads_data.get("competition", "UNKNOWN")),
                            competition_index=ads_data.get("competition_index"),
                            avg_cpc=ads_data.get("avg_cpc"),
                            intent_type=await self._classify_keyword_intent(keyword),
                            difficulty_score=await self._calculate_keyword_difficulty(keyword, ads_data),
                            opportunity_score=await self._calculate_opportunity_score(keyword, ads_data),
                            commercial_intent_score=await self._calculate_commercial_intent_score(keyword)
                        )
                        
                        enriched_keywords.append(keyword_obj)
                        
                        # Cache keyword data
                        self.keyword_cache[keyword] = keyword_obj
                        
                    except Exception as e:
                        self.logger.warning("Failed to enrich keyword", keyword=keyword, error=str(e))
                
                # Small delay between batches to respect API limits
                await asyncio.sleep(0.1)
            
            # Sort by opportunity score
            enriched_keywords.sort(key=lambda x: x.opportunity_score or 0, reverse=True)
            
            self.logger.debug(
                "Keywords enriched with data",
                total_keywords=len(enriched_keywords),
                with_search_volume=len([k for k in enriched_keywords if k.search_volume]),
                with_cpc_data=len([k for k in enriched_keywords if k.avg_cpc])
            )
            
            return enriched_keywords
            
        except Exception as e:
            self.logger.error("Keyword data enrichment failed", error=str(e))
            # Return basic keyword objects if enrichment fails
            return [
                KeywordData(keyword=kw, match_type=KeywordMatchType.BROAD)
                for kw in keywords
            ]
    
    async def _classify_keyword_intent(self, keyword: str) -> KeywordIntentType:
        """Classify keyword by user intent."""
        try:
            keyword_lower = keyword.lower()
            
            # Score each intent type
            intent_scores = {}
            for intent_type, indicators in self.intent_indicators.items():
                score = sum(1 for indicator in indicators if indicator in keyword_lower)
                intent_scores[intent_type] = score
            
            # Return intent type with highest score
            if intent_scores:
                max_intent = max(intent_scores, key=intent_scores.get)
                if intent_scores[max_intent] > 0:
                    return max_intent
            
            # Default classification based on keyword structure
            if len(keyword.split()) >= 4:
                return KeywordIntentType.INFORMATIONAL  # Long-tail often informational
            elif any(word in keyword_lower for word in ["service", "company", "solution"]):
                return KeywordIntentType.COMMERCIAL
            else:
                return KeywordIntentType.TRANSACTIONAL  # Default for business keywords
            
        except Exception as e:
            self.logger.warning("Intent classification failed", keyword=keyword, error=str(e))
            return KeywordIntentType.COMMERCIAL
    
    async def _calculate_keyword_difficulty(self, keyword: str, ads_data: Dict[str, Any]) -> float:
        """Calculate keyword difficulty score (0-1)."""
        try:
            difficulty_score = 0.5  # Base difficulty
            
            # Factor in competition
            competition = ads_data.get("competition", "UNKNOWN")
            if competition == "HIGH":
                difficulty_score += 0.3
            elif competition == "MEDIUM":
                difficulty_score += 0.1
            elif competition == "LOW":
                difficulty_score -= 0.1
            
            # Factor in CPC (higher CPC = more competitive)
            avg_cpc = ads_data.get("avg_cpc", 0)
            if avg_cpc > 5.0:
                difficulty_score += 0.2
            elif avg_cpc > 2.0:
                difficulty_score += 0.1
            elif avg_cpc < 1.0:
                difficulty_score -= 0.1
            
            # Factor in search volume (higher volume = more competitive)
            search_volume = ads_data.get("search_volume", 0)
            if search_volume > 10000:
                difficulty_score += 0.1
            elif search_volume < 1000:
                difficulty_score -= 0.1
            
            # Ensure score stays within bounds
            return max(0.0, min(1.0, difficulty_score))
            
        except Exception as e:
            self.logger.warning("Difficulty calculation failed", keyword=keyword, error=str(e))
            return 0.5
    
    async def _calculate_opportunity_score(self, keyword: str, ads_data: Dict[str, Any]) -> float:
        """Calculate keyword opportunity score (0-1)."""
        try:
            # Base score components
            search_volume = ads_data.get("search_volume", 0)
            competition = ads_data.get("competition", "UNKNOWN")
            avg_cpc = ads_data.get("avg_cpc", 0)
            
            # Search volume score (0-1)
            volume_score = min(1.0, search_volume / 10000) if search_volume else 0.1
            
            # Competition score (inverted - lower competition = higher opportunity)
            competition_score = 0.3
            if competition == "LOW":
                competition_score = 0.8
            elif competition == "MEDIUM":
                competition_score = 0.5
            elif competition == "HIGH":
                competition_score = 0.2
            
            # CPC efficiency score (reasonable CPC = higher opportunity)
            cpc_score = 0.5
            if 1.0 <= avg_cpc <= 3.0:  # Sweet spot for CPC
                cpc_score = 0.8
            elif avg_cpc > 5.0:  # Too expensive
                cpc_score = 0.2
            elif avg_cpc < 0.5:  # Potentially low value
                cpc_score = 0.3
            
            # Commercial intent bonus
            intent_score = 0.5
            intent_type = await self._classify_keyword_intent(keyword)
            if intent_type in [KeywordIntentType.TRANSACTIONAL, KeywordIntentType.COMMERCIAL]:
                intent_score = 0.8
            
            # Weighted opportunity score
            opportunity_score = (
                volume_score * self.scoring_weights["search_volume"] +
                competition_score * self.scoring_weights["competition"] +
                cpc_score * 0.2 +
                intent_score * self.scoring_weights["commercial_intent"]
            )
            
            return min(1.0, opportunity_score)
            
        except Exception as e:
            self.logger.warning("Opportunity score calculation failed", keyword=keyword, error=str(e))
            return 0.5
    
    async def _calculate_commercial_intent_score(self, keyword: str) -> float:
        """Calculate commercial intent score for keyword."""
        try:
            keyword_lower = keyword.lower()
            
            # Commercial intent indicators with weights
            commercial_indicators = {
                # High commercial intent
                "buy": 1.0, "purchase": 1.0, "order": 1.0, "shop": 0.9,
                "price": 0.8, "cost": 0.8, "deal": 0.7, "discount": 0.7,
                "best": 0.6, "top": 0.6, "review": 0.6, "compare": 0.6,
                # Medium commercial intent
                "service": 0.5, "solution": 0.5, "software": 0.5, "tool": 0.5,
                "professional": 0.4, "company": 0.4, "provider": 0.4,
                # Low commercial intent reduction
                "free": -0.3, "tutorial": -0.2, "how to": -0.2, "guide": -0.2
            }
            
            score = 0.3  # Base score
            
            for indicator, weight in commercial_indicators.items():
                if indicator in keyword_lower:
                    score += weight
            
            # Bonus for multiple commercial indicators
            commercial_count = sum(1 for indicator in commercial_indicators 
                                 if indicator in keyword_lower and commercial_indicators[indicator] > 0)
            if commercial_count > 1:
                score += 0.2
            
            return max(0.0, min(1.0, score))
            
        except Exception as e:
            self.logger.warning("Commercial intent calculation failed", keyword=keyword, error=str(e))
            return 0.5
    
    def _map_competition_level(self, competition_str: str) -> CompetitionLevel:
        """Map Google Ads competition string to enum."""
        competition_map = {
            "LOW": CompetitionLevel.LOW,
            "MEDIUM": CompetitionLevel.MEDIUM,
            "HIGH": CompetitionLevel.HIGH,
            "UNKNOWN": CompetitionLevel.UNKNOWN
        }
        return competition_map.get(competition_str.upper(), CompetitionLevel.UNKNOWN)
    
    async def _cluster_keywords(self, keywords: List[KeywordData]) -> List[KeywordCluster]:
        """Group keywords into thematic clusters for ad group organization."""
        try:
            # Simple clustering based on keyword similarity and intent
            clusters = {}
            
            for keyword_data in keywords:
                keyword = keyword_data.keyword.lower()
                
                # Find cluster based on primary term (first significant word)
                words = keyword.split()
                primary_term = None
                
                # Skip common modifiers to find the core term
                skip_words = {"best", "top", "cheap", "buy", "get", "find", "how", "to"}
                for word in words:
                    if word not in skip_words and len(word) > 2:
                        primary_term = word
                        break
                
                if not primary_term:
                    primary_term = words[0] if words else "misc"
                
                # Create or add to cluster
                cluster_key = f"{primary_term}_{keyword_data.intent_type.value}"
                
                if cluster_key not in clusters:
                    clusters[cluster_key] = {
                        "theme": primary_term,
                        "intent_type": keyword_data.intent_type,
                        "keywords": [],
                        "total_search_volume": 0,
                        "total_cpc": 0,
                        "competition_levels": []
                    }
                
                clusters[cluster_key]["keywords"].append(keyword_data)
                clusters[cluster_key]["total_search_volume"] += keyword_data.search_volume or 0
                clusters[cluster_key]["total_cpc"] += keyword_data.avg_cpc or 0
                clusters[cluster_key]["competition_levels"].append(keyword_data.competition)
            
            # Convert to KeywordCluster objects
            keyword_clusters = []
            
            for cluster_key, cluster_data in clusters.items():
                if len(cluster_data["keywords"]) >= 2:  # Only create clusters with 2+ keywords
                    # Find primary keyword (highest opportunity score)
                    primary_keyword = max(
                        cluster_data["keywords"],
                        key=lambda x: x.opportunity_score or 0
                    )
                    
                    # Calculate average metrics
                    keyword_count = len(cluster_data["keywords"])
                    avg_cpc = cluster_data["total_cpc"] / keyword_count if keyword_count > 0 else 0
                    
                    # Determine dominant competition level
                    competition_counts = {}
                    for comp in cluster_data["competition_levels"]:
                        competition_counts[comp] = competition_counts.get(comp, 0) + 1
                    dominant_competition = max(competition_counts, key=competition_counts.get)
                    
                    # Calculate cluster score
                    cluster_score = sum(kw.opportunity_score or 0 for kw in cluster_data["keywords"]) / keyword_count
                    
                    cluster = KeywordCluster(
                        cluster_id=f"cluster_{len(keyword_clusters) + 1}",
                        theme=cluster_data["theme"],
                        keywords=cluster_data["keywords"],
                        primary_keyword=primary_keyword.keyword,
                        total_search_volume=cluster_data["total_search_volume"],
                        avg_cpc=avg_cpc,
                        competition_level=dominant_competition,
                        intent_type=cluster_data["intent_type"],
                        cluster_score=cluster_score,
                        recommended_ad_groups=[f"{cluster_data['theme']} - {cluster_data['intent_type'].value}"]
                    )
                    
                    keyword_clusters.append(cluster)
            
            # Sort clusters by score
            keyword_clusters.sort(key=lambda x: x.cluster_score, reverse=True)
            
            self.logger.debug(
                "Keywords clustered",
                total_keywords=len(keywords),
                clusters_created=len(keyword_clusters),
                avg_keywords_per_cluster=sum(len(c.keywords) for c in keyword_clusters) / len(keyword_clusters) if keyword_clusters else 0
            )
            
            return keyword_clusters
            
        except Exception as e:
            self.logger.error("Keyword clustering failed", error=str(e))
            return []
    
    async def _identify_negative_keywords(
        self,
        keywords: List[KeywordData],
        business_description: str
    ) -> List[str]:
        """Identify negative keywords to prevent irrelevant traffic."""
        try:
            negative_keywords = set()
            
            # Common negative keywords for business contexts
            common_negatives = [
                "free", "cheap", "discount", "coupon", "deal", "sale",
                "job", "jobs", "career", "careers", "hiring", "employment",
                "tutorial", "how to", "diy", "course", "training", "learn",
                "wikipedia", "definition", "meaning", "what is",
                "pirated", "cracked", "illegal", "torrent", "download"
            ]
            
            # Industry-specific negative keywords
            business_lower = business_description.lower()
            
            if "software" in business_lower:
                negative_keywords.update([
                    "free software", "open source", "alternative", "replacement",
                    "crack", "serial", "keygen"
                ])
            
            if "service" in business_lower:
                negative_keywords.update([
                    "diy", "yourself", "manual", "guide", "tutorial"
                ])
            
            if "consulting" in business_lower:
                negative_keywords.update([
                    "job", "salary", "career", "freelance", "remote"
                ])
            
            # Add common negatives
            negative_keywords.update(common_negatives)
            
            # AI-powered negative keyword suggestions
            if self.openai_service:
                try:
                    ai_negatives = await self._ai_negative_keyword_suggestions(
                        business_description, [kw.keyword for kw in keywords[:20]]
                    )
                    negative_keywords.update(ai_negatives)
                except Exception as e:
                    self.logger.warning("AI negative keyword suggestions failed", error=str(e))
            
            return list(negative_keywords)
            
        except Exception as e:
            self.logger.error("Negative keyword identification failed", error=str(e))
            return []
    
    async def _ai_negative_keyword_suggestions(
        self,
        business_description: str,
        sample_keywords: List[str]
    ) -> List[str]:
        """Get AI-powered negative keyword suggestions."""
        try:
            prompt = f"""
            Based on this business description and sample keywords, suggest 15-20 negative keywords that should be excluded from Google Ads campaigns to prevent irrelevant clicks:
            
            Business: {business_description}
            Sample Keywords: {', '.join(sample_keywords)}
            
            Focus on terms that would attract users looking for:
            - Free alternatives
            - Job/career opportunities  
            - Educational/tutorial content
            - Irrelevant products/services
            - Competitor brands (if appropriate)
            
            Return as a JSON array of negative keyword strings.
            """
            
            response = await self.openai_service.generate_completion(
                prompt=prompt,
                model=self.config.model.model_name,
                temperature=0.3,
                max_tokens=500
            )
            
            try:
                negatives = json.loads(response)
                if isinstance(negatives, list):
                    return [neg for neg in negatives if isinstance(neg, str)]
            except json.JSONDecodeError:
                # Extract from text response
                lines = response.split('\n')
                return [line.strip().strip('"').strip("'") for line in lines 
                       if line.strip() and not line.strip().startswith(('#', '//', '-'))]
            
            return []
            
        except Exception as e:
            self.logger.warning("AI negative keyword suggestions failed", error=str(e))
            return []
    
    async def _categorize_keywords(
        self,
        keywords: List[KeywordData]
    ) -> Tuple[List[KeywordData], List[KeywordData], List[KeywordData]]:
        """Categorize keywords into primary, secondary, and long-tail."""
        try:
            # Sort by opportunity score
            sorted_keywords = sorted(keywords, key=lambda x: x.opportunity_score or 0, reverse=True)
            
            primary_keywords = []
            secondary_keywords = []
            long_tail_keywords = []
            
            for keyword_data in sorted_keywords:
                keyword = keyword_data.keyword
                word_count = len(keyword.split())
                search_volume = keyword_data.search_volume or 0
                opportunity_score = keyword_data.opportunity_score or 0
                
                # Primary keywords: high volume, high opportunity, 1-3 words
                if (search_volume >= 1000 and opportunity_score >= 0.6 and 
                    word_count <= 3 and len(primary_keywords) < 50):
                    primary_keywords.append(keyword_data)
                
                # Long-tail keywords: 4+ words or very specific
                elif word_count >= 4 or search_volume < 100:
                    long_tail_keywords.append(keyword_data)
                
                # Secondary keywords: everything else
                else:
                    secondary_keywords.append(keyword_data)
            
            self.logger.debug(
                "Keywords categorized",
                primary_count=len(primary_keywords),
                secondary_count=len(secondary_keywords),
                long_tail_count=len(long_tail_keywords)
            )
            
            return primary_keywords, secondary_keywords, long_tail_keywords
            
        except Exception as e:
            self.logger.error("Keyword categorization failed", error=str(e))
            # Fallback: distribute evenly
            third = len(keywords) // 3
            return keywords[:third], keywords[third:2*third], keywords[2*third:]
    
    # Additional methods for competitor analysis, seasonal insights, etc.
    # (Implementation continues with similar patterns...)
    
    async def _analyze_competitor_keywords(self, competitors: List[str]) -> List[CompetitorKeywordAnalysis]:
        """Analyze competitor keywords (simplified implementation)."""
        try:
            competitor_analyses = []
            
            for competitor in competitors:
                # Mock competitor analysis - real implementation would use tools like SEMrush API
                analysis = CompetitorKeywordAnalysis(
                    competitor_domain=competitor,
                    shared_keywords=["software solution", "business tools", "professional service"],
                    competitor_only_keywords=[
                        f"{competitor} specific keyword 1",
                        f"{competitor} specific keyword 2"
                    ],
                    missed_opportunities=[
                        KeywordData(
                            keyword=f"competitor {competitor} alternative",
                            match_type=KeywordMatchType.PHRASE,
                            search_volume=1500,
                            opportunity_score=0.7
                        )
                    ],
                    overlap_percentage=25.5,
                    competitive_gaps=[f"Missing coverage in {competitor}'s main keywords"],
                    outranking_opportunities=[f"Can outrank {competitor} on long-tail keywords"]
                )
                competitor_analyses.append(analysis)
            
            return competitor_analyses
            
        except Exception as e:
            self.logger.error("Competitor keyword analysis failed", error=str(e))
            return []
    
    async def _analyze_seasonal_trends(self, keywords: List[KeywordData]) -> Dict[str, Any]:
        """Analyze seasonal trends for keywords."""
        try:
            # Mock seasonal analysis - real implementation would use Google Trends API
            seasonal_insights = {
                "peak_months": ["November", "December"],
                "low_months": ["July", "August"],
                "trending_keywords": [kw.keyword for kw in keywords[:5]],
                "seasonal_multipliers": {
                    "Q1": 0.9,
                    "Q2": 1.0,
                    "Q3": 0.8,
                    "Q4": 1.3
                },
                "recommendations": [
                    "Increase budget during Q4 for seasonal peak",
                    "Focus on evergreen keywords during summer months",
                    "Plan seasonal campaigns 2 months in advance"
                ]
            }
            
            return seasonal_insights
            
        except Exception as e:
            self.logger.error("Seasonal trends analysis failed", error=str(e))
            return {}
    
    async def _generate_keyword_recommendations(
        self,
        primary_keywords: List[KeywordData],
        secondary_keywords: List[KeywordData],
        keyword_clusters: List[KeywordCluster],
        competitor_analysis: List[CompetitorKeywordAnalysis]
    ) -> List[str]:
        """Generate keyword strategy recommendations."""
        try:
            recommendations = []
            
            # Primary keyword recommendations
            if len(primary_keywords) < 20:
                recommendations.append("Expand primary keyword list to 20-30 high-opportunity keywords")
            
            if primary_keywords:
                avg_competition = sum(1 for kw in primary_keywords if kw.competition == CompetitionLevel.HIGH) / len(primary_keywords)
                if avg_competition > 0.7:
                    recommendations.append("Consider focusing on medium-competition keywords for better ROI")
            
            # Cluster recommendations
            if len(keyword_clusters) > 10:
                recommendations.append("Consider consolidating keyword clusters to improve Quality Score")
            elif len(keyword_clusters) < 5:
                recommendations.append("Expand keyword themes to cover more user intents")
            
            # Competition recommendations
            total_opportunities = sum(len(comp.missed_opportunities) for comp in competitor_analysis)
            if total_opportunities > 10:
                recommendations.append(f"Target {total_opportunities} competitor keyword opportunities")
            
            # Match type recommendations
            recommendations.extend([
                "Start with phrase match for primary keywords to balance reach and relevance",
                "Use exact match for high-converting keywords to maximize control",
                "Test broad match modified for keyword discovery",
                "Implement comprehensive negative keyword list from day one"
            ])
            
            return recommendations
            
        except Exception as e:
            self.logger.error("Keyword recommendations generation failed", error=str(e))
            return ["Implement comprehensive keyword strategy with proper match types and negative keywords"]
    
    async def _create_implementation_plan(
        self,
        keyword_clusters: List[KeywordCluster],
        research_scope: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create keyword implementation plan."""
        try:
            implementation_plan = {
                "phase_1": {
                    "name": "Primary Keywords Launch",
                    "duration_weeks": 2,
                    "keywords_count": sum(len(cluster.keywords) for cluster in keyword_clusters[:3]),
                    "ad_groups": [cluster.recommended_ad_groups[0] for cluster in keyword_clusters[:3] if cluster.recommended_ad_groups],
                    "tasks": [
                        "Set up top 3 keyword clusters as separate ad groups",
                        "Create targeted ad copy for each cluster",
                        "Implement negative keyword list",
                        "Set up conversion tracking"
                    ]
                },
                "phase_2": {
                    "name": "Secondary Keywords Expansion",
                    "duration_weeks": 3,
                    "keywords_count": sum(len(cluster.keywords) for cluster in keyword_clusters[3:6]),
                    "tasks": [
                        "Launch remaining keyword clusters",
                        "Test different match types",
                        "Optimize based on initial performance",
                        "Expand high-performing themes"
                    ]
                },
                "phase_3": {
                    "name": "Long-tail and Optimization",
                    "duration_weeks": 4,
                    "tasks": [
                        "Add long-tail keyword variations",
                        "Implement automated bidding",
                        "Conduct match type optimization",
                        "Scale successful campaigns"
                    ]
                },
                "ongoing": {
                    "name": "Continuous Optimization",
                    "tasks": [
                        "Weekly keyword performance review",
                        "Monthly negative keyword updates",
                        "Quarterly keyword expansion",
                        "Seasonal trend adjustments"
                    ]
                }
            }
            
            return implementation_plan
            
        except Exception as e:
            self.logger.error("Implementation plan creation failed", error=str(e))
            return {}
    
    async def _estimate_budget_requirements(
        self,
        primary_keywords: List[KeywordData],
        secondary_keywords: List[KeywordData],
        research_scope: Dict[str, Any]
    ) -> Dict[str, float]:
        """Estimate budget requirements for keyword strategy."""
        try:
            # Calculate estimated costs
            primary_monthly_cost = 0.0
            secondary_monthly_cost = 0.0
            
            for keyword_data in primary_keywords:
                if keyword_data.avg_cpc and keyword_data.search_volume:
                    # Estimate 5% CTR and 30% impression share
                    estimated_clicks = (keyword_data.search_volume * 0.30 * 0.05)
                    primary_monthly_cost += estimated_clicks * keyword_data.avg_cpc
            
            for keyword_data in secondary_keywords:
                if keyword_data.avg_cpc and keyword_data.search_volume:
                    # Lower estimates for secondary keywords
                    estimated_clicks = (keyword_data.search_volume * 0.20 * 0.03)
                    secondary_monthly_cost += estimated_clicks * keyword_data.avg_cpc
            
            budget_requirements = {
                "primary_keywords_monthly": primary_monthly_cost,
                "secondary_keywords_monthly": secondary_monthly_cost,
                "total_monthly_estimate": primary_monthly_cost + secondary_monthly_cost,
                "recommended_daily_budget": (primary_monthly_cost + secondary_monthly_cost) / 30,
                "minimum_test_budget": max(500, (primary_monthly_cost + secondary_monthly_cost) * 0.1),
                "scaling_budget": (primary_monthly_cost + secondary_monthly_cost) * 2
            }
            
            return budget_requirements
            
        except Exception as e:
            self.logger.error("Budget estimation failed", error=str(e))
            return {}
    
    # Additional helper methods for existing keyword analysis...
    
    async def _analyze_keyword_performance_patterns(
        self,
        keywords: List[KeywordData],
        performance: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze performance patterns in existing keywords."""
        try:
            # Mock performance pattern analysis
            return {
                "high_performers": ["top performing keyword 1", "top performing keyword 2"],
                "underperformers": ["low performing keyword 1", "low performing keyword 2"],
                "patterns": {
                    "long_tail_performance": "better",
                    "match_type_performance": {"exact": 3.2, "phrase": 2.8, "broad": 2.1},
                    "intent_performance": {
                        "transactional": 4.5,
                        "commercial": 3.2,
                        "informational": 1.8
                    }
                }
            }
        except Exception as e:
            self.logger.error("Performance pattern analysis failed", error=str(e))
            return {}
    
    # ... Additional methods continue with similar implementation patterns