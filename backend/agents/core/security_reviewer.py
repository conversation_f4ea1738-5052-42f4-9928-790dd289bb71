"""
Security Reviewer Agent for AiLex Ad Agent System.
Handles security assessments, compliance reviews, and vulnerability analysis.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

import structlog
from crewai import Agent, Task

from ..base import BaseAiLexAgent, AgentContext, AgentError
from ..tracing import AgentTracer, create_agent_tracer
from models.agents import AgentType, AgentConfig
from services.openai_service import OpenAIService
from utils.config import settings


logger = structlog.get_logger(__name__)


class SecurityLevel(str, Enum):
    """Security assessment levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ComplianceStandard(str, Enum):
    """Compliance standards to check against."""
    GDPR = "gdpr"
    CCPA = "ccpa"
    SOC2 = "soc2"
    ISO27001 = "iso27001"
    OWASP = "owasp"
    PCI_DSS = "pci_dss"


@dataclass
class SecurityAssessment:
    """Security assessment results."""
    assessment_id: str
    target_system: str
    security_level: SecurityLevel
    vulnerabilities: List[Dict[str, Any]]
    compliance_gaps: List[Dict[str, Any]]
    risk_score: float
    recommendations: List[str]
    remediation_plan: Dict[str, Any]
    assessment_date: datetime
    next_review_date: datetime


@dataclass
class ComplianceReport:
    """Compliance assessment report."""
    report_id: str
    standards_reviewed: List[ComplianceStandard]
    compliance_score: float
    gaps_identified: List[Dict[str, Any]]
    remediation_items: List[Dict[str, Any]]
    certification_status: Dict[str, str]
    recommendations: List[str]
    created_at: datetime


class SecurityReviewerAgent(BaseAiLexAgent):
    """
    AI agent specialized in security reviews, compliance assessments, and vulnerability analysis.
    """
    
    def __init__(self, agent_id: str, config: AgentConfig):
        super().__init__(
            agent_id=agent_id,
            name="Security Reviewer Agent",
            description="Specialized AI agent for security assessments, compliance reviews, and vulnerability analysis",
            agent_type=AgentType.QUALITY_ASSURANCE,  # Using closest available type
            config=config
        )
        
        # Initialize services
        self.openai_service: Optional[OpenAIService] = None
        
        # Initialize tracer
        self.tracer = create_agent_tracer(self.agent_id, self.name)
        
        # Security assessment frameworks
        self.security_frameworks = {
            "owasp_top10": [
                "injection", "broken_authentication", "sensitive_data_exposure",
                "xml_external_entities", "broken_access_control", "security_misconfiguration",
                "cross_site_scripting", "insecure_deserialization", "known_vulnerabilities",
                "insufficient_logging"
            ],
            "api_security": [
                "broken_object_level_auth", "broken_user_auth", "excessive_data_exposure",
                "lack_of_resources_rate_limiting", "broken_function_level_auth",
                "mass_assignment", "security_misconfiguration", "injection",
                "improper_assets_management", "insufficient_logging"
            ]
        }
        
        # Compliance requirements
        self.compliance_requirements = {
            ComplianceStandard.GDPR: {
                "data_protection": ["consent", "data_minimization", "purpose_limitation"],
                "user_rights": ["access", "rectification", "erasure", "portability"],
                "security": ["data_protection_by_design", "breach_notification"]
            },
            ComplianceStandard.CCPA: {
                "consumer_rights": ["know", "delete", "opt_out", "non_discrimination"],
                "data_handling": ["collection_notice", "data_categories", "business_purposes"]
            }
        }
        
        # Risk assessment matrix
        self.risk_matrix = {
            "critical": {"score": 90, "timeframe": "immediate"},
            "high": {"score": 70, "timeframe": "24_hours"},
            "medium": {"score": 50, "timeframe": "1_week"},
            "low": {"score": 30, "timeframe": "1_month"}
        }
    
    async def _custom_initialize(self) -> None:
        """Custom initialization for security reviewer agent."""
        try:
            # Initialize OpenAI service
            if settings.OPENAI_API_KEY:
                self.openai_service = OpenAIService()
            
            self.logger.info(
                "Security reviewer agent initialized",
                has_openai=bool(self.openai_service),
                frameworks_count=len(self.security_frameworks),
                compliance_standards=len(self.compliance_requirements)
            )
            
        except Exception as e:
            raise AgentError(f"Failed to initialize security reviewer agent: {str(e)}")
    
    async def conduct_security_assessment(
        self,
        target_system: str,
        assessment_scope: Dict[str, Any],
        security_requirements: List[str]
    ) -> SecurityAssessment:
        """
        Conduct comprehensive security assessment.
        
        Args:
            target_system: System being assessed
            assessment_scope: Scope and boundaries of assessment
            security_requirements: Specific security requirements to validate
            
        Returns:
            SecurityAssessment: Detailed security assessment results
        """
        async with self.tracer.trace_task_execution(
            task_id=f"security_assessment_{target_system}",
            task_name="Security Assessment",
            task_data={
                "target_system": target_system,
                "scope": assessment_scope,
                "requirements_count": len(security_requirements)
            }
        ) as span:
            try:
                self.logger.info(
                    "Starting security assessment",
                    target_system=target_system,
                    scope=assessment_scope
                )
                
                assessment_id = f"security_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
                
                # Identify vulnerabilities
                vulnerabilities = await self._identify_vulnerabilities(
                    target_system, assessment_scope
                )
                
                # Check compliance gaps
                compliance_gaps = await self._check_compliance_gaps(
                    target_system, assessment_scope
                )
                
                # Calculate risk score
                risk_score = await self._calculate_risk_score(
                    vulnerabilities, compliance_gaps
                )
                
                # Determine security level
                security_level = await self._determine_security_level(risk_score)
                
                # Generate recommendations
                recommendations = await self._generate_security_recommendations(
                    vulnerabilities, compliance_gaps, security_requirements
                )
                
                # Create remediation plan
                remediation_plan = await self._create_remediation_plan(
                    vulnerabilities, compliance_gaps, security_level
                )
                
                assessment = SecurityAssessment(
                    assessment_id=assessment_id,
                    target_system=target_system,
                    security_level=security_level,
                    vulnerabilities=vulnerabilities,
                    compliance_gaps=compliance_gaps,
                    risk_score=risk_score,
                    recommendations=recommendations,
                    remediation_plan=remediation_plan,
                    assessment_date=datetime.utcnow(),
                    next_review_date=datetime.utcnow() + timedelta(days=90)
                )
                
                self.tracer.record_task_result(span, {
                    "assessment_id": assessment_id,
                    "vulnerabilities_found": len(vulnerabilities),
                    "compliance_gaps": len(compliance_gaps),
                    "risk_score": risk_score,
                    "security_level": security_level.value
                }, True)
                
                self.logger.info(
                    "Security assessment completed",
                    assessment_id=assessment_id,
                    vulnerabilities=len(vulnerabilities),
                    risk_score=risk_score,
                    security_level=security_level.value
                )
                
                return assessment
                
            except Exception as e:
                self.logger.error("Security assessment failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Security assessment failed: {str(e)}")
    
    async def review_compliance(
        self,
        target_system: str,
        standards: List[ComplianceStandard],
        system_documentation: Dict[str, Any]
    ) -> ComplianceReport:
        """
        Review compliance against specified standards.
        
        Args:
            target_system: System being reviewed
            standards: Compliance standards to check
            system_documentation: System documentation and policies
            
        Returns:
            ComplianceReport: Compliance assessment report
        """
        async with self.tracer.trace_task_execution(
            task_id=f"compliance_review_{target_system}",
            task_name="Compliance Review",
            task_data={
                "target_system": target_system,
                "standards": [s.value for s in standards]
            }
        ) as span:
            try:
                self.logger.info(
                    "Starting compliance review",
                    target_system=target_system,
                    standards=[s.value for s in standards]
                )
                
                report_id = f"compliance_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
                
                # Review each standard
                gaps_identified = []
                remediation_items = []
                certification_status = {}
                
                for standard in standards:
                    standard_gaps = await self._review_compliance_standard(
                        standard, target_system, system_documentation
                    )
                    gaps_identified.extend(standard_gaps)
                    
                    # Generate remediation items for this standard
                    standard_remediation = await self._generate_compliance_remediation(
                        standard, standard_gaps
                    )
                    remediation_items.extend(standard_remediation)
                    
                    # Determine certification status
                    certification_status[standard.value] = await self._assess_certification_status(
                        standard, standard_gaps
                    )
                
                # Calculate overall compliance score
                compliance_score = await self._calculate_compliance_score(
                    standards, gaps_identified
                )
                
                # Generate recommendations
                recommendations = await self._generate_compliance_recommendations(
                    gaps_identified, remediation_items
                )
                
                report = ComplianceReport(
                    report_id=report_id,
                    standards_reviewed=standards,
                    compliance_score=compliance_score,
                    gaps_identified=gaps_identified,
                    remediation_items=remediation_items,
                    certification_status=certification_status,
                    recommendations=recommendations,
                    created_at=datetime.utcnow()
                )
                
                self.tracer.record_task_result(span, {
                    "report_id": report_id,
                    "compliance_score": compliance_score,
                    "gaps_count": len(gaps_identified),
                    "remediation_items": len(remediation_items)
                }, True)
                
                self.logger.info(
                    "Compliance review completed",
                    report_id=report_id,
                    compliance_score=compliance_score,
                    gaps_count=len(gaps_identified)
                )
                
                return report
                
            except Exception as e:
                self.logger.error("Compliance review failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Compliance review failed: {str(e)}")
    
    async def analyze_code_security(
        self,
        code_files: List[str],
        security_standards: List[str]
    ) -> Dict[str, Any]:
        """
        Analyze code for security vulnerabilities.
        
        Args:
            code_files: List of code files to analyze
            security_standards: Security standards to apply
            
        Returns:
            Dict[str, Any]: Security analysis results
        """
        async with self.tracer.trace_task_execution(
            task_id=f"code_security_analysis_{len(code_files)}_files",
            task_name="Code Security Analysis",
            task_data={
                "files_count": len(code_files),
                "standards": security_standards
            }
        ) as span:
            try:
                self.logger.info(
                    "Starting code security analysis",
                    files_count=len(code_files),
                    standards=security_standards
                )
                
                analysis_results = {
                    "vulnerabilities": [],
                    "security_score": 0.0,
                    "file_results": {},
                    "recommendations": [],
                    "high_risk_files": [],
                    "summary": {}
                }
                
                total_score = 0.0
                
                for file_path in code_files:
                    try:
                        # Analyze single file
                        file_analysis = await self._analyze_file_security(
                            file_path, security_standards
                        )
                        
                        analysis_results["file_results"][file_path] = file_analysis
                        analysis_results["vulnerabilities"].extend(
                            file_analysis.get("vulnerabilities", [])
                        )
                        
                        file_score = file_analysis.get("security_score", 0.0)
                        total_score += file_score
                        
                        # Track high-risk files
                        if file_score < 60:  # Below 60% security score
                            analysis_results["high_risk_files"].append(file_path)
                    
                    except Exception as e:
                        self.logger.warning(
                            "Failed to analyze file",
                            file_path=file_path,
                            error=str(e)
                        )
                
                # Calculate overall security score
                if code_files:
                    analysis_results["security_score"] = total_score / len(code_files)
                
                # Generate recommendations
                analysis_results["recommendations"] = await self._generate_code_security_recommendations(
                    analysis_results["vulnerabilities"]
                )
                
                # Generate summary
                analysis_results["summary"] = await self._generate_security_analysis_summary(
                    analysis_results
                )
                
                self.tracer.record_task_result(span, {
                    "files_analyzed": len(analysis_results["file_results"]),
                    "vulnerabilities_found": len(analysis_results["vulnerabilities"]),
                    "security_score": analysis_results["security_score"],
                    "high_risk_files": len(analysis_results["high_risk_files"])
                }, True)
                
                self.logger.info(
                    "Code security analysis completed",
                    files_analyzed=len(analysis_results["file_results"]),
                    vulnerabilities=len(analysis_results["vulnerabilities"]),
                    security_score=analysis_results["security_score"]
                )
                
                return analysis_results
                
            except Exception as e:
                self.logger.error("Code security analysis failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Code security analysis failed: {str(e)}")
    
    # Helper methods for security assessment
    
    async def _identify_vulnerabilities(
        self,
        target_system: str,
        scope: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Identify security vulnerabilities in the target system."""
        vulnerabilities = []
        
        # Check OWASP Top 10
        for vuln_type in self.security_frameworks["owasp_top10"]:
            vuln_check = await self._check_vulnerability_type(
                target_system, vuln_type, scope
            )
            if vuln_check:
                vulnerabilities.append(vuln_check)
        
        # Check API-specific vulnerabilities
        if scope.get("includes_api", True):
            for api_vuln in self.security_frameworks["api_security"]:
                api_check = await self._check_api_vulnerability(
                    target_system, api_vuln, scope
                )
                if api_check:
                    vulnerabilities.append(api_check)
        
        return vulnerabilities
    
    async def _check_vulnerability_type(
        self,
        system: str,
        vuln_type: str,
        scope: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Check for specific vulnerability type."""
        # Simplified vulnerability detection
        # In real implementation, this would use actual security scanning tools
        
        risk_patterns = {
            "injection": ["sql", "nosql", "command", "ldap"],
            "broken_authentication": ["weak_passwords", "session_management", "brute_force"],
            "sensitive_data_exposure": ["encryption", "data_transmission", "storage"],
            "cross_site_scripting": ["input_validation", "output_encoding", "content_type"]
        }
        
        if vuln_type in risk_patterns:
            # Mock vulnerability found
            return {
                "type": vuln_type,
                "severity": "medium",
                "description": f"Potential {vuln_type} vulnerability detected",
                "location": f"{system} system",
                "recommendation": f"Review and remediate {vuln_type} risks",
                "cvss_score": 5.5
            }
        
        return None
    
    async def _check_api_vulnerability(
        self,
        system: str,
        vuln_type: str,
        scope: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Check for API-specific vulnerabilities."""
        # Simplified API vulnerability check
        api_risks = {
            "broken_object_level_auth": "Object-level authorization not properly implemented",
            "excessive_data_exposure": "API returns more data than necessary",
            "lack_of_resources_rate_limiting": "No rate limiting implemented",
            "security_misconfiguration": "Security headers not properly configured"
        }
        
        if vuln_type in api_risks:
            return {
                "type": f"api_{vuln_type}",
                "severity": "medium",
                "description": api_risks[vuln_type],
                "location": f"{system} API",
                "recommendation": f"Implement proper {vuln_type} controls",
                "cvss_score": 6.0
            }
        
        return None
    
    async def _check_compliance_gaps(
        self,
        system: str,
        scope: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Check for compliance gaps."""
        gaps = []
        
        # Basic compliance checks
        common_gaps = [
            {
                "standard": "GDPR",
                "requirement": "Data Protection Impact Assessment",
                "gap": "DPIA not documented",
                "severity": "high",
                "remediation": "Conduct and document DPIA"
            },
            {
                "standard": "SOC2",
                "requirement": "Access Controls",
                "gap": "Privileged access not properly monitored",
                "severity": "medium",
                "remediation": "Implement privileged access monitoring"
            }
        ]
        
        return common_gaps
    
    async def _calculate_risk_score(
        self,
        vulnerabilities: List[Dict[str, Any]],
        compliance_gaps: List[Dict[str, Any]]
    ) -> float:
        """Calculate overall risk score."""
        vuln_score = 0.0
        for vuln in vulnerabilities:
            severity = vuln.get("severity", "low")
            if severity == "critical":
                vuln_score += 25
            elif severity == "high":
                vuln_score += 15
            elif severity == "medium":
                vuln_score += 10
            else:
                vuln_score += 5
        
        compliance_score = len(compliance_gaps) * 10
        
        total_score = min(100, vuln_score + compliance_score)
        return total_score
    
    async def _determine_security_level(self, risk_score: float) -> SecurityLevel:
        """Determine security level based on risk score."""
        if risk_score >= 80:
            return SecurityLevel.CRITICAL
        elif risk_score >= 60:
            return SecurityLevel.HIGH
        elif risk_score >= 40:
            return SecurityLevel.MEDIUM
        else:
            return SecurityLevel.LOW
    
    async def _generate_security_recommendations(
        self,
        vulnerabilities: List[Dict[str, Any]],
        compliance_gaps: List[Dict[str, Any]],
        requirements: List[str]
    ) -> List[str]:
        """Generate security recommendations."""
        recommendations = []
        
        # Vulnerability-based recommendations
        vuln_types = set(v.get("type", "") for v in vulnerabilities)
        for vuln_type in vuln_types:
            if "injection" in vuln_type:
                recommendations.append("Implement input validation and parameterized queries")
            elif "authentication" in vuln_type:
                recommendations.append("Strengthen authentication mechanisms and session management")
            elif "data_exposure" in vuln_type:
                recommendations.append("Implement data encryption and access controls")
        
        # Compliance-based recommendations
        for gap in compliance_gaps:
            recommendations.append(gap.get("remediation", "Address compliance gap"))
        
        # General security recommendations
        recommendations.extend([
            "Implement regular security assessments",
            "Establish incident response procedures",
            "Provide security awareness training",
            "Maintain security documentation and policies"
        ])
        
        return list(set(recommendations))  # Remove duplicates
    
    async def _create_remediation_plan(
        self,
        vulnerabilities: List[Dict[str, Any]],
        compliance_gaps: List[Dict[str, Any]],
        security_level: SecurityLevel
    ) -> Dict[str, Any]:
        """Create remediation plan."""
        plan = {
            "immediate_actions": [],
            "short_term_actions": [],
            "long_term_actions": [],
            "timeline": {},
            "resources_required": {},
            "success_criteria": []
        }
        
        # Prioritize based on security level
        if security_level in [SecurityLevel.CRITICAL, SecurityLevel.HIGH]:
            plan["immediate_actions"] = [
                "Address critical vulnerabilities within 24 hours",
                "Implement emergency security controls",
                "Notify stakeholders of security issues"
            ]
        
        plan["short_term_actions"] = [
            "Remediate identified vulnerabilities",
            "Close compliance gaps",
            "Update security policies"
        ]
        
        plan["long_term_actions"] = [
            "Implement continuous security monitoring",
            "Establish regular security assessments",
            "Improve security culture and training"
        ]
        
        return plan
    
    # Additional helper methods (simplified implementations)
    
    async def _review_compliance_standard(
        self,
        standard: ComplianceStandard,
        system: str,
        documentation: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Review compliance for a specific standard."""
        gaps = []
        
        if standard == ComplianceStandard.GDPR:
            gaps = [
                {
                    "requirement": "Consent Management",
                    "gap": "Consent tracking not implemented",
                    "severity": "high"
                },
                {
                    "requirement": "Data Subject Rights",
                    "gap": "Data export functionality missing",
                    "severity": "medium"
                }
            ]
        elif standard == ComplianceStandard.SOC2:
            gaps = [
                {
                    "requirement": "Access Controls",
                    "gap": "Role-based access not fully implemented",
                    "severity": "medium"
                }
            ]
        
        return gaps
    
    async def _generate_compliance_remediation(
        self,
        standard: ComplianceStandard,
        gaps: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Generate remediation items for compliance gaps."""
        return [
            {
                "standard": standard.value,
                "gap": gap.get("requirement", "Unknown"),
                "action": f"Implement {gap.get('requirement', 'compliance requirement')}",
                "priority": gap.get("severity", "medium"),
                "timeline": "30 days"
            }
            for gap in gaps
        ]
    
    async def _assess_certification_status(
        self,
        standard: ComplianceStandard,
        gaps: List[Dict[str, Any]]
    ) -> str:
        """Assess certification readiness."""
        if not gaps:
            return "compliant"
        elif len(gaps) < 3:
            return "mostly_compliant"
        else:
            return "non_compliant"
    
    async def _calculate_compliance_score(
        self,
        standards: List[ComplianceStandard],
        gaps: List[Dict[str, Any]]
    ) -> float:
        """Calculate overall compliance score."""
        if not gaps:
            return 100.0
        
        total_requirements = len(standards) * 10  # Assume 10 requirements per standard
        gap_penalty = len(gaps) * 5  # 5 points per gap
        
        score = max(0, 100 - (gap_penalty / total_requirements * 100))
        return round(score, 2)
    
    async def _generate_compliance_recommendations(
        self,
        gaps: List[Dict[str, Any]],
        remediation_items: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate compliance recommendations."""
        return [
            "Prioritize high-severity compliance gaps",
            "Implement regular compliance monitoring",
            "Establish compliance training programs",
            "Document all compliance procedures",
            "Conduct regular compliance audits"
        ]
    
    async def _analyze_file_security(
        self,
        file_path: str,
        standards: List[str]
    ) -> Dict[str, Any]:
        """Analyze security of a single file."""
        # Simplified file security analysis
        return {
            "file_path": file_path,
            "security_score": 75.0,
            "vulnerabilities": [
                {
                    "type": "potential_injection",
                    "line": 42,
                    "description": "Potential SQL injection vulnerability",
                    "severity": "medium"
                }
            ],
            "recommendations": [
                "Use parameterized queries",
                "Validate input data",
                "Implement proper error handling"
            ]
        }
    
    async def _generate_code_security_recommendations(
        self,
        vulnerabilities: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate code security recommendations."""
        return [
            "Implement static code analysis in CI/CD pipeline",
            "Use secure coding practices",
            "Regular security code reviews",
            "Implement automated security testing"
        ]
    
    async def _generate_security_analysis_summary(
        self,
        results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate security analysis summary."""
        return {
            "total_files": len(results["file_results"]),
            "vulnerabilities_by_severity": {
                "critical": len([v for v in results["vulnerabilities"] if v.get("severity") == "critical"]),
                "high": len([v for v in results["vulnerabilities"] if v.get("severity") == "high"]),
                "medium": len([v for v in results["vulnerabilities"] if v.get("severity") == "medium"]),
                "low": len([v for v in results["vulnerabilities"] if v.get("severity") == "low"])
            },
            "overall_security_posture": "needs_improvement" if results["security_score"] < 80 else "good"
        }