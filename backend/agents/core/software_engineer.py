"""
Software Engineer Agent for AiLex Ad Agent System.
Handles code development, API integrations, and technical implementation tasks.
"""

import asyncio
import json
import os
import subprocess
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

import structlog
from crewai import Agent, Task

from ..base import BaseAiLexAgent, AgentContext, AgentError
from ..tracing import AgentTracer, create_agent_tracer
from models.agents import AgentType, AgentConfig
from services.openai_service import OpenAIService
from utils.config import settings


logger = structlog.get_logger(__name__)


class DevelopmentTask(str, Enum):
    """Types of development tasks."""
    API_INTEGRATION = "api_integration"
    FRONTEND_DEVELOPMENT = "frontend_development"
    BACKEND_DEVELOPMENT = "backend_development"
    DATABASE_SCHEMA = "database_schema"
    TESTING = "testing"
    DEPLOYMENT = "deployment"
    BUG_FIX = "bug_fix"
    FEATURE_ENHANCEMENT = "feature_enhancement"
    CODE_REVIEW = "code_review"
    DOCUMENTATION = "documentation"


class TechStack(str, Enum):
    """Supported technology stacks."""
    FASTAPI = "fastapi"
    NEXTJS = "nextjs"
    REACT = "react"
    PYTHON = "python"
    TYPESCRIPT = "typescript"
    JAVASCRIPT = "javascript"
    POSTGRESQL = "postgresql"
    SUPABASE = "supabase"
    DOCKER = "docker"
    KUBERNETES = "kubernetes"


@dataclass
class DevelopmentSpec:
    """Development specification for tasks."""
    task_type: DevelopmentTask
    tech_stack: List[TechStack]
    requirements: Dict[str, Any]
    acceptance_criteria: List[str]
    technical_constraints: Dict[str, Any]
    dependencies: List[str]
    estimated_effort: timedelta
    quality_standards: Dict[str, Any]
    testing_requirements: List[str]
    documentation_requirements: List[str]


@dataclass
class CodeArtifact:
    """Generated code artifact."""
    artifact_id: str
    file_path: str
    content: str
    language: str
    framework: Optional[str]
    dependencies: List[str]
    test_files: List[str]
    documentation: str
    quality_score: float
    complexity_score: float
    maintainability_score: float
    security_score: float
    created_at: datetime
    version: str = "1.0"


@dataclass
class IntegrationSpec:
    """API integration specification."""
    api_name: str
    api_version: str
    base_url: str
    authentication: Dict[str, Any]
    endpoints: List[Dict[str, Any]]
    rate_limits: Dict[str, int]
    error_handling: Dict[str, Any]
    data_models: List[Dict[str, Any]]
    testing_strategy: Dict[str, Any]


class SoftwareEngineerAgent(BaseAiLexAgent):
    """
    AI agent specialized in software development tasks including code generation,
    API integrations, testing, and technical implementation.
    """
    
    def __init__(self, agent_id: str, config: AgentConfig, specialization: str = "fullstack"):
        super().__init__(
            agent_id=agent_id,
            name=f"Software Engineer Agent ({specialization})",
            description=f"Specialized AI agent for {specialization} software development, API integrations, and technical implementation",
            agent_type=AgentType.CAMPAIGN_PLANNING,  # Using closest available type
            config=config
        )
        
        self.specialization = specialization  # fullstack, frontend, backend, devops
        
        # Initialize services
        self.openai_service: Optional[OpenAIService] = None
        
        # Initialize tracer
        self.tracer = create_agent_tracer(self.agent_id, self.name)
        
        # Development capabilities
        self.supported_languages = {
            "python": {"frameworks": ["fastapi", "django", "flask"], "test_frameworks": ["pytest", "unittest"]},
            "typescript": {"frameworks": ["nextjs", "react", "express"], "test_frameworks": ["jest", "vitest"]},
            "javascript": {"frameworks": ["react", "vue", "express"], "test_frameworks": ["jest", "mocha"]},
            "sql": {"frameworks": ["postgresql", "mysql", "sqlite"], "test_frameworks": ["pytest", "pgtest"]}
        }
        
        # Code quality standards
        self.quality_standards = {
            "complexity_threshold": 10,
            "maintainability_threshold": 80,
            "test_coverage_threshold": 85,
            "security_score_threshold": 90,
            "documentation_coverage": 80
        }
        
        # Integration templates
        self.integration_templates = {}
        
        # Code generation patterns
        self.code_patterns = {
            "api_endpoint": self._generate_api_endpoint,
            "data_model": self._generate_data_model,
            "service_class": self._generate_service_class,
            "react_component": self._generate_react_component,
            "database_migration": self._generate_database_migration,
            "test_suite": self._generate_test_suite
        }
    
    async def _custom_initialize(self) -> None:
        """Custom initialization for software engineer agent."""
        try:
            # Initialize OpenAI service
            if settings.OPENAI_API_KEY:
                self.openai_service = OpenAIService()
            
            # Load integration templates
            await self._load_integration_templates()
            
            # Set up development environment
            await self._setup_development_environment()
            
            self.logger.info(
                "Software engineer agent initialized",
                specialization=self.specialization,
                has_openai=bool(self.openai_service),
                supported_languages=list(self.supported_languages.keys())
            )
            
        except Exception as e:
            raise AgentError(f"Failed to initialize software engineer agent: {str(e)}")
    
    async def implement_feature(
        self,
        feature_spec: DevelopmentSpec,
        project_context: Dict[str, Any]
    ) -> List[CodeArtifact]:
        """
        Implement a feature based on specifications.
        
        Args:
            feature_spec: Detailed feature specification
            project_context: Project context and constraints
            
        Returns:
            List[CodeArtifact]: Generated code artifacts
        """
        async with self.tracer.trace_task_execution(
            task_id=f"implement_feature_{feature_spec.task_type.value}",
            task_name="Feature Implementation",
            task_data={
                "task_type": feature_spec.task_type.value,
                "tech_stack": [ts.value for ts in feature_spec.tech_stack],
                "estimated_hours": feature_spec.estimated_effort.total_seconds() / 3600
            }
        ) as span:
            try:
                self.logger.info(
                    "Starting feature implementation",
                    task_type=feature_spec.task_type.value,
                    tech_stack=[ts.value for ts in feature_spec.tech_stack]
                )
                
                artifacts = []
                
                # Generate code based on task type
                if feature_spec.task_type == DevelopmentTask.API_INTEGRATION:
                    artifacts = await self._implement_api_integration(feature_spec, project_context)
                
                elif feature_spec.task_type == DevelopmentTask.FRONTEND_DEVELOPMENT:
                    artifacts = await self._implement_frontend_feature(feature_spec, project_context)
                
                elif feature_spec.task_type == DevelopmentTask.BACKEND_DEVELOPMENT:
                    artifacts = await self._implement_backend_feature(feature_spec, project_context)
                
                elif feature_spec.task_type == DevelopmentTask.DATABASE_SCHEMA:
                    artifacts = await self._implement_database_schema(feature_spec, project_context)
                
                elif feature_spec.task_type == DevelopmentTask.TESTING:
                    artifacts = await self._implement_testing_suite(feature_spec, project_context)
                
                else:
                    # Generic feature implementation
                    artifacts = await self._implement_generic_feature(feature_spec, project_context)
                
                # Assess code quality for all artifacts
                for artifact in artifacts:
                    await self._assess_code_quality(artifact)
                
                # Generate documentation
                for artifact in artifacts:
                    artifact.documentation = await self._generate_documentation(artifact, feature_spec)
                
                self.tracer.record_task_result(span, {
                    "artifacts_generated": len(artifacts),
                    "avg_quality_score": sum(a.quality_score for a in artifacts) / len(artifacts) if artifacts else 0,
                    "total_lines": sum(len(a.content.split('\n')) for a in artifacts)
                }, True)
                
                self.logger.info(
                    "Feature implementation completed",
                    artifacts_count=len(artifacts),
                    avg_quality_score=sum(a.quality_score for a in artifacts) / len(artifacts) if artifacts else 0
                )
                
                return artifacts
                
            except Exception as e:
                self.logger.error("Feature implementation failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Feature implementation failed: {str(e)}")
    
    async def integrate_api(
        self,
        integration_spec: IntegrationSpec,
        project_context: Dict[str, Any]
    ) -> List[CodeArtifact]:
        """
        Integrate external API into the system.
        
        Args:
            integration_spec: API integration specification
            project_context: Project context
            
        Returns:
            List[CodeArtifact]: Integration code artifacts
        """
        async with self.tracer.trace_task_execution(
            task_id=f"integrate_api_{integration_spec.api_name}",
            task_name="API Integration",
            task_data={
                "api_name": integration_spec.api_name,
                "endpoints_count": len(integration_spec.endpoints),
                "auth_type": integration_spec.authentication.get("type", "unknown")
            }
        ) as span:
            try:
                self.logger.info(
                    "Starting API integration",
                    api_name=integration_spec.api_name,
                    endpoints_count=len(integration_spec.endpoints)
                )
                
                artifacts = []
                
                # Generate service class for API
                service_artifact = await self._generate_api_service_class(integration_spec, project_context)
                artifacts.append(service_artifact)
                
                # Generate data models
                for model_spec in integration_spec.data_models:
                    model_artifact = await self._generate_api_data_model(model_spec, integration_spec)
                    artifacts.append(model_artifact)
                
                # Generate client wrapper
                client_artifact = await self._generate_api_client(integration_spec, project_context)
                artifacts.append(client_artifact)
                
                # Generate error handling
                error_handler_artifact = await self._generate_api_error_handler(integration_spec)
                artifacts.append(error_handler_artifact)
                
                # Generate tests
                test_artifact = await self._generate_api_tests(integration_spec, artifacts)
                artifacts.append(test_artifact)
                
                # Generate configuration
                config_artifact = await self._generate_api_config(integration_spec)
                artifacts.append(config_artifact)
                
                self.tracer.record_task_result(span, {
                    "artifacts_generated": len(artifacts),
                    "service_classes": 1,
                    "data_models": len(integration_spec.data_models),
                    "test_files": 1
                }, True)
                
                self.logger.info(
                    "API integration completed",
                    api_name=integration_spec.api_name,
                    artifacts_count=len(artifacts)
                )
                
                return artifacts
                
            except Exception as e:
                self.logger.error("API integration failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"API integration failed: {str(e)}")
    
    async def perform_code_review(
        self,
        code_files: List[str],
        review_criteria: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Perform code review on specified files.
        
        Args:
            code_files: List of file paths to review
            review_criteria: Review criteria and standards
            
        Returns:
            Dict[str, Any]: Code review results
        """
        async with self.tracer.trace_task_execution(
            task_id=f"code_review_{len(code_files)}_files",
            task_name="Code Review",
            task_data={"files_count": len(code_files)}
        ) as span:
            try:
                self.logger.info("Starting code review", files_count=len(code_files))
                
                review_results = {
                    "overall_score": 0.0,
                    "file_reviews": {},
                    "issues": [],
                    "recommendations": [],
                    "summary": {}
                }
                
                total_score = 0.0
                
                for file_path in code_files:
                    try:
                        # Read file content
                        if os.path.exists(file_path):
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            # Review file
                            file_review = await self._review_single_file(
                                file_path, content, review_criteria
                            )
                            
                            review_results["file_reviews"][file_path] = file_review
                            total_score += file_review["score"]
                            
                            # Collect issues and recommendations
                            review_results["issues"].extend(file_review.get("issues", []))
                            review_results["recommendations"].extend(file_review.get("recommendations", []))
                        
                        else:
                            self.logger.warning("File not found for review", file_path=file_path)
                    
                    except Exception as e:
                        self.logger.warning("Failed to review file", file_path=file_path, error=str(e))
                
                # Calculate overall score
                if code_files:
                    review_results["overall_score"] = total_score / len(code_files)
                
                # Generate summary
                review_results["summary"] = await self._generate_review_summary(review_results)
                
                self.tracer.record_task_result(span, {
                    "files_reviewed": len(review_results["file_reviews"]),
                    "overall_score": review_results["overall_score"],
                    "issues_found": len(review_results["issues"]),
                    "recommendations": len(review_results["recommendations"])
                }, True)
                
                self.logger.info(
                    "Code review completed",
                    files_reviewed=len(review_results["file_reviews"]),
                    overall_score=review_results["overall_score"],
                    issues_count=len(review_results["issues"])
                )
                
                return review_results
                
            except Exception as e:
                self.logger.error("Code review failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Code review failed: {str(e)}")
    
    async def generate_tests(
        self,
        code_artifacts: List[CodeArtifact],
        test_strategy: Dict[str, Any]
    ) -> List[CodeArtifact]:
        """
        Generate test suites for code artifacts.
        
        Args:
            code_artifacts: Code artifacts to test
            test_strategy: Testing strategy and requirements
            
        Returns:
            List[CodeArtifact]: Generated test artifacts
        """
        async with self.tracer.trace_task_execution(
            task_id=f"generate_tests_{len(code_artifacts)}_artifacts",
            task_name="Test Generation",
            task_data={
                "artifacts_count": len(code_artifacts),
                "test_types": test_strategy.get("types", [])
            }
        ) as span:
            try:
                self.logger.info(
                    "Starting test generation",
                    artifacts_count=len(code_artifacts),
                    test_types=test_strategy.get("types", [])
                )
                
                test_artifacts = []
                
                for artifact in code_artifacts:
                    # Generate unit tests
                    if "unit" in test_strategy.get("types", []):
                        unit_test = await self._generate_unit_tests(artifact, test_strategy)
                        test_artifacts.append(unit_test)
                    
                    # Generate integration tests
                    if "integration" in test_strategy.get("types", []):
                        integration_test = await self._generate_integration_tests(artifact, test_strategy)
                        test_artifacts.append(integration_test)
                    
                    # Generate end-to-end tests
                    if "e2e" in test_strategy.get("types", []):
                        e2e_test = await self._generate_e2e_tests(artifact, test_strategy)
                        test_artifacts.append(e2e_test)
                
                # Generate test configuration
                test_config = await self._generate_test_configuration(test_strategy)
                test_artifacts.append(test_config)
                
                self.tracer.record_task_result(span, {
                    "test_artifacts": len(test_artifacts),
                    "coverage_target": test_strategy.get("coverage_threshold", 80)
                }, True)
                
                self.logger.info(
                    "Test generation completed",
                    test_artifacts=len(test_artifacts)
                )
                
                return test_artifacts
                
            except Exception as e:
                self.logger.error("Test generation failed", error=str(e))
                self.tracer.record_task_result(span, {"error": str(e)}, False)
                raise AgentError(f"Test generation failed: {str(e)}")
    
    # Implementation methods for different task types
    
    async def _implement_api_integration(
        self,
        spec: DevelopmentSpec,
        context: Dict[str, Any]
    ) -> List[CodeArtifact]:
        """Implement API integration functionality."""
        artifacts = []
        
        # Generate API client
        client_code = await self._generate_code_with_ai(
            "api_client",
            spec.requirements,
            context,
            spec.tech_stack
        )
        
        client_artifact = CodeArtifact(
            artifact_id=f"api_client_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            file_path=f"services/{spec.requirements.get('api_name', 'api')}_client.py",
            content=client_code,
            language="python",
            framework="fastapi",
            dependencies=["httpx", "pydantic"],
            test_files=[],
            documentation="",
            quality_score=0.0,
            complexity_score=0.0,
            maintainability_score=0.0,
            security_score=0.0,
            created_at=datetime.utcnow()
        )
        
        artifacts.append(client_artifact)
        return artifacts
    
    async def _implement_frontend_feature(
        self,
        spec: DevelopmentSpec,
        context: Dict[str, Any]
    ) -> List[CodeArtifact]:
        """Implement frontend feature."""
        artifacts = []
        
        # Generate React component
        if TechStack.REACT in spec.tech_stack or TechStack.NEXTJS in spec.tech_stack:
            component_code = await self._generate_code_with_ai(
                "react_component",
                spec.requirements,
                context,
                spec.tech_stack
            )
            
            component_artifact = CodeArtifact(
                artifact_id=f"component_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                file_path=f"components/{spec.requirements.get('component_name', 'Component')}.tsx",
                content=component_code,
                language="typescript",
                framework="react",
                dependencies=["react", "@types/react"],
                test_files=[],
                documentation="",
                quality_score=0.0,
                complexity_score=0.0,
                maintainability_score=0.0,
                security_score=0.0,
                created_at=datetime.utcnow()
            )
            
            artifacts.append(component_artifact)
        
        return artifacts
    
    async def _implement_backend_feature(
        self,
        spec: DevelopmentSpec,
        context: Dict[str, Any]
    ) -> List[CodeArtifact]:
        """Implement backend feature."""
        artifacts = []
        
        # Generate FastAPI endpoints
        if TechStack.FASTAPI in spec.tech_stack:
            endpoint_code = await self._generate_code_with_ai(
                "fastapi_endpoint",
                spec.requirements,
                context,
                spec.tech_stack
            )
            
            endpoint_artifact = CodeArtifact(
                artifact_id=f"endpoint_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                file_path=f"api/{spec.requirements.get('endpoint_name', 'endpoint')}.py",
                content=endpoint_code,
                language="python",
                framework="fastapi",
                dependencies=["fastapi", "pydantic"],
                test_files=[],
                documentation="",
                quality_score=0.0,
                complexity_score=0.0,
                maintainability_score=0.0,
                security_score=0.0,
                created_at=datetime.utcnow()
            )
            
            artifacts.append(endpoint_artifact)
        
        return artifacts
    
    async def _implement_database_schema(
        self,
        spec: DevelopmentSpec,
        context: Dict[str, Any]
    ) -> List[CodeArtifact]:
        """Implement database schema."""
        artifacts = []
        
        # Generate SQL migration
        migration_code = await self._generate_code_with_ai(
            "sql_migration",
            spec.requirements,
            context,
            spec.tech_stack
        )
        
        migration_artifact = CodeArtifact(
            artifact_id=f"migration_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            file_path=f"database/migrations/{spec.requirements.get('migration_name', 'migration')}.sql",
            content=migration_code,
            language="sql",
            framework="postgresql",
            dependencies=[],
            test_files=[],
            documentation="",
            quality_score=0.0,
            complexity_score=0.0,
            maintainability_score=0.0,
            security_score=0.0,
            created_at=datetime.utcnow()
        )
        
        artifacts.append(migration_artifact)
        return artifacts
    
    async def _implement_testing_suite(
        self,
        spec: DevelopmentSpec,
        context: Dict[str, Any]
    ) -> List[CodeArtifact]:
        """Implement testing suite."""
        artifacts = []
        
        # Generate pytest test file
        test_code = await self._generate_code_with_ai(
            "pytest_test",
            spec.requirements,
            context,
            spec.tech_stack
        )
        
        test_artifact = CodeArtifact(
            artifact_id=f"test_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            file_path=f"tests/test_{spec.requirements.get('test_name', 'feature')}.py",
            content=test_code,
            language="python",
            framework="pytest",
            dependencies=["pytest", "pytest-asyncio"],
            test_files=[],
            documentation="",
            quality_score=0.0,
            complexity_score=0.0,
            maintainability_score=0.0,
            security_score=0.0,
            created_at=datetime.utcnow()
        )
        
        artifacts.append(test_artifact)
        return artifacts
    
    async def _implement_generic_feature(
        self,
        spec: DevelopmentSpec,
        context: Dict[str, Any]
    ) -> List[CodeArtifact]:
        """Implement generic feature."""
        # Default implementation for unspecified task types
        return await self._implement_backend_feature(spec, context)
    
    # Code generation methods
    
    async def _generate_code_with_ai(
        self,
        code_type: str,
        requirements: Dict[str, Any],
        context: Dict[str, Any],
        tech_stack: List[TechStack]
    ) -> str:
        """Generate code using AI service."""
        if not self.openai_service:
            return self._generate_fallback_code(code_type, requirements)
        
        try:
            prompt = self._build_code_generation_prompt(
                code_type, requirements, context, tech_stack
            )
            
            async with self.tracer.trace_llm_call(
                model_name=self.config.model.model_name,
                prompt=prompt,
                temperature=self.config.model.temperature,
                max_tokens=self.config.model.max_tokens
            ) as span:
                response = await self.openai_service.generate_completion(
                    prompt=prompt,
                    model=self.config.model.model_name,
                    temperature=0.3,  # Lower temperature for code generation
                    max_tokens=2000
                )
                
                # Extract code from response
                code = self._extract_code_from_response(response)
                
                self.tracer.record_llm_response(span, response, len(response.split()))
                
                return code
        
        except Exception as e:
            self.logger.warning("AI code generation failed", error=str(e))
            return self._generate_fallback_code(code_type, requirements)
    
    def _build_code_generation_prompt(
        self,
        code_type: str,
        requirements: Dict[str, Any],
        context: Dict[str, Any],
        tech_stack: List[TechStack]
    ) -> str:
        """Build prompt for code generation."""
        tech_stack_str = ", ".join([ts.value for ts in tech_stack])
        
        prompt = f"""
        Generate {code_type} code based on the following requirements:
        
        Technology Stack: {tech_stack_str}
        Requirements: {json.dumps(requirements, indent=2)}
        Context: {json.dumps(context, indent=2)}
        
        Please generate clean, well-documented, production-ready code that follows best practices for {tech_stack_str}.
        Include proper error handling, type hints where applicable, and follow the specified coding standards.
        
        Return only the code without explanations or markdown formatting.
        """
        
        return prompt
    
    def _extract_code_from_response(self, response: str) -> str:
        """Extract code from AI response."""
        # Remove markdown code blocks if present
        if "```" in response:
            lines = response.split('\n')
            in_code_block = False
            code_lines = []
            
            for line in lines:
                if line.strip().startswith('```'):
                    in_code_block = not in_code_block
                    continue
                if in_code_block:
                    code_lines.append(line)
            
            return '\n'.join(code_lines)
        
        return response.strip()
    
    def _generate_fallback_code(self, code_type: str, requirements: Dict[str, Any]) -> str:
        """Generate fallback code when AI is not available."""
        if code_type == "api_client":
            return self._generate_api_client_template(requirements)
        elif code_type == "react_component":
            return self._generate_react_component_template(requirements)
        elif code_type == "fastapi_endpoint":
            return self._generate_fastapi_endpoint_template(requirements)
        elif code_type == "sql_migration":
            return self._generate_sql_migration_template(requirements)
        elif code_type == "pytest_test":
            return self._generate_pytest_test_template(requirements)
        else:
            return f"# {code_type} implementation\n# TODO: Implement {requirements.get('name', 'feature')}"
    
    # Template generators for fallback code
    
    def _generate_api_client_template(self, requirements: Dict[str, Any]) -> str:
        """Generate API client template."""
        api_name = requirements.get('api_name', 'API')
        return f'''"""
{api_name} client implementation.
"""

import httpx
from typing import Dict, Any, Optional
from pydantic import BaseModel


class {api_name}Client:
    """Client for {api_name} API integration."""
    
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.client = httpx.AsyncClient(
            headers={{"Authorization": f"Bearer {{api_key}}"}},
            timeout=30.0
        )
    
    async def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make GET request to API."""
        url = f"{{self.base_url}}/{{endpoint.lstrip('/')}}"
        response = await self.client.get(url, params=params)
        response.raise_for_status()
        return response.json()
    
    async def post(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make POST request to API."""
        url = f"{{self.base_url}}/{{endpoint.lstrip('/')}}"
        response = await self.client.post(url, json=data)
        response.raise_for_status()
        return response.json()
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
'''
    
    def _generate_react_component_template(self, requirements: Dict[str, Any]) -> str:
        """Generate React component template."""
        component_name = requirements.get('component_name', 'Component')
        return f'''import React from 'react';

interface {component_name}Props {{
  // Define props here
}}

const {component_name}: React.FC<{component_name}Props> = () => {{
  return (
    <div className="{component_name.lower()}">
      <h1>{component_name}</h1>
      {{/* Component implementation */}}
    </div>
  );
}};

export default {component_name};
'''
    
    def _generate_fastapi_endpoint_template(self, requirements: Dict[str, Any]) -> str:
        """Generate FastAPI endpoint template."""
        endpoint_name = requirements.get('endpoint_name', 'endpoint')
        return f'''"""
{endpoint_name} API endpoints.
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional

router = APIRouter(prefix="/{endpoint_name}", tags=["{endpoint_name}"])


class {endpoint_name.title()}Request(BaseModel):
    """Request model for {endpoint_name}."""
    # Define request fields here
    pass


class {endpoint_name.title()}Response(BaseModel):
    """Response model for {endpoint_name}."""
    # Define response fields here
    pass


@router.get("/", response_model=List[{endpoint_name.title()}Response])
async def get_{endpoint_name}s():
    """Get all {endpoint_name}s."""
    # Implementation here
    return []


@router.post("/", response_model={endpoint_name.title()}Response)
async def create_{endpoint_name}(request: {endpoint_name.title()}Request):
    """Create new {endpoint_name}."""
    # Implementation here
    return {endpoint_name.title()}Response()
'''
    
    def _generate_sql_migration_template(self, requirements: Dict[str, Any]) -> str:
        """Generate SQL migration template."""
        table_name = requirements.get('table_name', 'example_table')
        return f'''-- Migration: Create {table_name}
-- Created: {datetime.utcnow().isoformat()}

CREATE TABLE IF NOT EXISTS {table_name} (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    -- Add additional columns here
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_{table_name}_created_at ON {table_name}(created_at);

-- Add triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_{table_name}_updated_at 
    BEFORE UPDATE ON {table_name} 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
'''
    
    def _generate_pytest_test_template(self, requirements: Dict[str, Any]) -> str:
        """Generate pytest test template."""
        test_name = requirements.get('test_name', 'feature')
        return f'''"""
Tests for {test_name}.
"""

import pytest
from unittest.mock import Mock, patch


class Test{test_name.title()}:
    """Test class for {test_name}."""
    
    def test_{test_name}_basic(self):
        """Test basic {test_name} functionality."""
        # Arrange
        
        # Act
        
        # Assert
        assert True  # Replace with actual test
    
    @pytest.mark.asyncio
    async def test_{test_name}_async(self):
        """Test async {test_name} functionality."""
        # Arrange
        
        # Act
        
        # Assert
        assert True  # Replace with actual test
    
    def test_{test_name}_error_handling(self):
        """Test {test_name} error handling."""
        # Arrange
        
        # Act & Assert
        with pytest.raises(Exception):
            pass  # Replace with actual test
'''
    
    # Additional helper methods (abbreviated for space)
    
    async def _load_integration_templates(self) -> None:
        """Load API integration templates."""
        # Implementation would load templates from configuration
        pass
    
    async def _setup_development_environment(self) -> None:
        """Set up development environment."""
        # Implementation would set up development tools and environment
        pass
    
    async def _assess_code_quality(self, artifact: CodeArtifact) -> None:
        """Assess code quality for an artifact."""
        # Simple quality assessment based on code characteristics
        lines = artifact.content.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        
        # Basic metrics
        artifact.complexity_score = min(100, max(0, 100 - len(non_empty_lines) / 10))
        artifact.maintainability_score = 80.0  # Default score
        artifact.security_score = 85.0  # Default score
        
        # Overall quality score
        artifact.quality_score = (
            artifact.complexity_score * 0.3 +
            artifact.maintainability_score * 0.4 +
            artifact.security_score * 0.3
        ) / 100
    
    async def _generate_documentation(self, artifact: CodeArtifact, spec: DevelopmentSpec) -> str:
        """Generate documentation for code artifact."""
        return f"""# {artifact.file_path}

## Description
{spec.requirements.get('description', 'Generated code artifact')}

## Usage
{artifact.file_path} - {artifact.language} implementation

## Dependencies
{', '.join(artifact.dependencies)}

## Generated: {artifact.created_at.isoformat()}
"""
    
    # Additional methods for API integration, testing, etc. would be implemented here
    # For brevity, providing simplified stubs
    
    async def _generate_api_service_class(self, spec: IntegrationSpec, context: Dict[str, Any]) -> CodeArtifact:
        """Generate API service class."""
        content = self._generate_api_client_template({"api_name": spec.api_name})
        return CodeArtifact(
            artifact_id=f"api_service_{spec.api_name}",
            file_path=f"services/{spec.api_name.lower()}_service.py",
            content=content,
            language="python",
            framework="fastapi",
            dependencies=["httpx", "pydantic"],
            test_files=[],
            documentation="",
            quality_score=0.8,
            complexity_score=75.0,
            maintainability_score=80.0,
            security_score=85.0,
            created_at=datetime.utcnow()
        )
    
    async def _generate_api_data_model(self, model_spec: Dict[str, Any], integration_spec: IntegrationSpec) -> CodeArtifact:
        """Generate API data model."""
        model_name = model_spec.get("name", "Model")
        content = f'''from pydantic import BaseModel
from typing import Optional

class {model_name}(BaseModel):
    """Data model for {model_name}."""
    # Add fields based on model_spec
    pass
'''
        return CodeArtifact(
            artifact_id=f"model_{model_name}",
            file_path=f"models/{model_name.lower()}.py",
            content=content,
            language="python",
            framework="pydantic",
            dependencies=["pydantic"],
            test_files=[],
            documentation="",
            quality_score=0.8,
            complexity_score=70.0,
            maintainability_score=85.0,
            security_score=90.0,
            created_at=datetime.utcnow()
        )
    
    # Additional stub implementations for other methods...
    async def _generate_api_client(self, spec: IntegrationSpec, context: Dict[str, Any]) -> CodeArtifact: pass
    async def _generate_api_error_handler(self, spec: IntegrationSpec) -> CodeArtifact: pass
    async def _generate_api_tests(self, spec: IntegrationSpec, artifacts: List[CodeArtifact]) -> CodeArtifact: pass
    async def _generate_api_config(self, spec: IntegrationSpec) -> CodeArtifact: pass
    async def _review_single_file(self, file_path: str, content: str, criteria: Dict[str, Any]) -> Dict[str, Any]: pass
    async def _generate_review_summary(self, results: Dict[str, Any]) -> Dict[str, Any]: pass
    async def _generate_unit_tests(self, artifact: CodeArtifact, strategy: Dict[str, Any]) -> CodeArtifact: pass
    async def _generate_integration_tests(self, artifact: CodeArtifact, strategy: Dict[str, Any]) -> CodeArtifact: pass
    async def _generate_e2e_tests(self, artifact: CodeArtifact, strategy: Dict[str, Any]) -> CodeArtifact: pass
    async def _generate_test_configuration(self, strategy: Dict[str, Any]) -> CodeArtifact: pass