"""
Agent Factory and Registry System for AiLex Ad Agent System.
Handles dynamic agent creation, registration, and lifecycle management.
"""

import asyncio
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Type, Union
from dataclasses import dataclass, field
from enum import Enum

import structlog

from .base import BaseAiLexAgent, AgentError
from .roles import Agent<PERSON><PERSON>
from .core.campaign_planning import CampaignPlanningAgent
from .core.ad_asset_generation import AdAssetGenerationAgent
from .core.project_orchestrator import ProjectOrchestratorAgent
from .core.software_engineer import SoftwareEngineerAgent
from .core.security_reviewer import SecurityReviewerAgent
from .core.frontend_ux_expert import FrontendUXExpertAgent
from .core.middleware_validation_expert import MiddlewareValidationExpertAgent
from .core.crewai_implementation_specialist import CrewAIImplementationSpecialistAgent
from .core.infra_deployment_specialist import InfraDeploymentSpecialistAgent
from models.agents import AgentType, AgentConfig, AgentStatus, AgentModel, ModelProvider
from utils.config import settings


logger = structlog.get_logger(__name__)


@dataclass
class AgentTemplate:
    """Template for agent creation."""
    role: AgentRole
    agent_class: Type[BaseAiLexAgent]
    default_config: AgentConfig
    required_capabilities: List[str]
    resource_requirements: Dict[str, Any]
    initialization_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AgentInstance:
    """Registered agent instance."""
    agent_id: str
    agent: BaseAiLexAgent
    role: AgentRole
    status: AgentStatus
    created_at: datetime
    last_activity: datetime
    resource_usage: Dict[str, Any] = field(default_factory=dict)
    performance_metrics: Dict[str, Any] = field(default_factory=dict)
    assigned_tasks: List[str] = field(default_factory=list)


class AgentFactory:
    """
    Factory for creating and managing AI agents.
    Handles agent instantiation, configuration, and lifecycle management.
    """
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
        
        # Agent templates registry
        self.agent_templates: Dict[AgentRole, AgentTemplate] = {}
        
        # Active agent instances
        self.active_agents: Dict[str, AgentInstance] = {}
        
        # Agent pools by role
        self.agent_pools: Dict[AgentRole, List[str]] = {}
        
        # Load balancing and resource management
        self.max_agents_per_role = 3
        self.resource_limits = {
            "memory_mb": 512,
            "cpu_percent": 50,
            "concurrent_tasks": 5
        }
        
        # Initialize templates
        self._initialize_agent_templates()
    
    def _initialize_agent_templates(self) -> None:
        """Initialize agent templates for all supported roles."""
        
        # Default model configuration
        default_model = AgentModel(
            provider=ModelProvider.OPENAI,
            model_name="gpt-4",
            temperature=0.7,
            max_tokens=2000
        )
        
        # Project Orchestrator
        self.agent_templates[AgentRole.PROJECT_ORCHESTRATOR] = AgentTemplate(
            role=AgentRole.PROJECT_ORCHESTRATOR,
            agent_class=ProjectOrchestratorAgent,
            default_config=AgentConfig(
                model=default_model,
                max_iterations=15,
                timeout_seconds=600,
                verbose=True,
                allow_delegation=True
            ),
            required_capabilities=["workflow_orchestration", "task_delegation", "progress_monitoring"],
            resource_requirements={"memory_mb": 256, "cpu_percent": 30}
        )
        
        # Campaign Planning Agent
        self.agent_templates[AgentRole.CAMPAIGN_PLANNER] = AgentTemplate(
            role=AgentRole.CAMPAIGN_PLANNER,
            agent_class=CampaignPlanningAgent,
            default_config=AgentConfig(
                model=default_model,
                max_iterations=10,
                timeout_seconds=1800,
                verbose=True
            ),
            required_capabilities=["market_research", "competitor_analysis", "keyword_research"],
            resource_requirements={"memory_mb": 512, "cpu_percent": 40}
        )
        
        # Ad Asset Generation Agent
        self.agent_templates[AgentRole.ASSET_GENERATOR] = AgentTemplate(
            role=AgentRole.ASSET_GENERATOR,
            agent_class=AdAssetGenerationAgent,
            default_config=AgentConfig(
                model=default_model,
                max_iterations=8,
                timeout_seconds=1200,
                verbose=True
            ),
            required_capabilities=["content_generation", "creative_optimization", "asset_validation"],
            resource_requirements={"memory_mb": 768, "cpu_percent": 50}
        )
        
        # Software Engineer Agents (different specializations)
        software_engineer_config = AgentConfig(
            model=default_model,
            max_iterations=12,
            timeout_seconds=2400,
            verbose=True
        )
        
        self.agent_templates[AgentRole.SOFTWARE_ENGINEER_FULLSTACK] = AgentTemplate(
            role=AgentRole.SOFTWARE_ENGINEER_FULLSTACK,
            agent_class=SoftwareEngineerAgent,
            default_config=software_engineer_config,
            required_capabilities=["code_generation", "api_integration", "testing", "deployment"],
            resource_requirements={"memory_mb": 1024, "cpu_percent": 60},
            initialization_params={"specialization": "fullstack"}
        )
        
        self.agent_templates[AgentRole.SOFTWARE_ENGINEER_FRONTEND] = AgentTemplate(
            role=AgentRole.SOFTWARE_ENGINEER_FRONTEND,
            agent_class=SoftwareEngineerAgent,
            default_config=software_engineer_config,
            required_capabilities=["frontend_development", "ui_ux", "component_design"],
            resource_requirements={"memory_mb": 512, "cpu_percent": 40},
            initialization_params={"specialization": "frontend"}
        )
        
        self.agent_templates[AgentRole.SOFTWARE_ENGINEER_BACKEND] = AgentTemplate(
            role=AgentRole.SOFTWARE_ENGINEER_BACKEND,
            agent_class=SoftwareEngineerAgent,
            default_config=software_engineer_config,
            required_capabilities=["backend_development", "database_design", "api_development"],
            resource_requirements={"memory_mb": 768, "cpu_percent": 50},
            initialization_params={"specialization": "backend"}
        )
        
        # Security Reviewer Agent
        self.agent_templates[AgentRole.SECURITY_REVIEWER] = AgentTemplate(
            role=AgentRole.SECURITY_REVIEWER,
            agent_class=SecurityReviewerAgent,
            default_config=AgentConfig(
                model=default_model,
                max_iterations=8,
                timeout_seconds=1800,
                verbose=True
            ),
            required_capabilities=["security_assessment", "compliance_review", "vulnerability_analysis"],
            resource_requirements={"memory_mb": 384, "cpu_percent": 35}
        )
        
        # Frontend UX Expert Agent
        self.agent_templates[AgentRole.FRONTEND_UX_EXPERT] = AgentTemplate(
            role=AgentRole.FRONTEND_UX_EXPERT,
            agent_class=FrontendUXExpertAgent,
            default_config=AgentConfig(
                model=AgentModel(
                    provider=ModelProvider.OPENAI,
                    model_name="gpt-4",
                    temperature=0.8,  # More creative for UI/UX
                    max_tokens=2000
                ),
                max_iterations=10,
                timeout_seconds=1200,
                verbose=True
            ),
            required_capabilities=["frontend_development", "ux_design", "component_architecture"],
            resource_requirements={"memory_mb": 512, "cpu_percent": 40}
        )
        
        # Middleware Validation Expert Agent
        self.agent_templates[AgentRole.MIDDLEWARE_VALIDATOR] = AgentTemplate(
            role=AgentRole.MIDDLEWARE_VALIDATOR,
            agent_class=MiddlewareValidationExpertAgent,
            default_config=AgentConfig(
                model=AgentModel(
                    provider=ModelProvider.OPENAI,
                    model_name="gpt-4",
                    temperature=0.3,  # More precise for validation
                    max_tokens=2000
                ),
                max_iterations=8,
                timeout_seconds=900,
                verbose=True
            ),
            required_capabilities=["middleware_development", "validation_schemas", "security_validation"],
            resource_requirements={"memory_mb": 384, "cpu_percent": 35}
        )
        
        # CrewAI Implementation Specialist Agent
        self.agent_templates[AgentRole.CAMPAIGN_PLANNER] = AgentTemplate(
            role=AgentRole.CAMPAIGN_PLANNER,
            agent_class=CrewAIImplementationSpecialistAgent,
            default_config=AgentConfig(
                model=default_model,
                max_iterations=12,
                timeout_seconds=1800,
                verbose=True,
                allow_delegation=True
            ),
            required_capabilities=["crewai_implementation", "agent_orchestration", "workflow_design"],
            resource_requirements={"memory_mb": 768, "cpu_percent": 50}
        )
        
        # Infrastructure Deployment Specialist Agent
        self.agent_templates[AgentRole.INFRA_DEPLOYMENT_SPECIALIST] = AgentTemplate(
            role=AgentRole.INFRA_DEPLOYMENT_SPECIALIST,
            agent_class=InfraDeploymentSpecialistAgent,
            default_config=AgentConfig(
                model=default_model,
                max_iterations=15,
                timeout_seconds=2400,  # Longer for deployments
                verbose=True
            ),
            required_capabilities=["infrastructure_deployment", "ci_cd_pipelines", "cloud_management"],
            resource_requirements={"memory_mb": 512, "cpu_percent": 45}
        )
        
        self.logger.info(
            "Agent templates initialized",
            templates_count=len(self.agent_templates),
            roles=[role.value for role in self.agent_templates.keys()]
        )
    
    async def create_agent(
        self,
        role: AgentRole,
        agent_id: Optional[str] = None,
        config_overrides: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create and register a new agent instance.
        
        Args:
            role: Agent role to create
            agent_id: Optional specific agent ID
            config_overrides: Optional configuration overrides
            
        Returns:
            str: Created agent ID
            
        Raises:
            AgentError: If agent creation fails
        """
        try:
            if role not in self.agent_templates:
                raise AgentError(f"Unknown agent role: {role}")
            
            template = self.agent_templates[role]
            
            # Generate agent ID if not provided
            if agent_id is None:
                agent_id = f"{role.value}_{uuid.uuid4().hex[:8]}"
            
            # Check if agent already exists
            if agent_id in self.active_agents:
                raise AgentError(f"Agent with ID {agent_id} already exists")
            
            # Prepare configuration
            config = template.default_config
            if config_overrides:
                # Apply configuration overrides
                config = self._apply_config_overrides(config, config_overrides)
            
            # Check resource availability
            if not await self._check_resource_availability(template.resource_requirements):
                raise AgentError("Insufficient resources to create agent")
            
            self.logger.info(
                "Creating agent instance",
                agent_id=agent_id,
                role=role.value,
                agent_class=template.agent_class.__name__
            )
            
            # Create agent instance
            initialization_params = template.initialization_params.copy()
            agent_instance = template.agent_class(
                agent_id=agent_id,
                config=config,
                **initialization_params
            )
            
            # Initialize the agent
            await agent_instance.initialize(config)
            
            # Register agent
            agent_registration = AgentInstance(
                agent_id=agent_id,
                agent=agent_instance,
                role=role,
                status=AgentStatus.IDLE,
                created_at=datetime.utcnow(),
                last_activity=datetime.utcnow(),
                resource_usage=template.resource_requirements.copy(),
                performance_metrics={
                    "tasks_completed": 0,
                    "tasks_failed": 0,
                    "average_execution_time": 0.0,
                    "success_rate": 1.0
                }
            )
            
            self.active_agents[agent_id] = agent_registration
            
            # Add to agent pool
            if role not in self.agent_pools:
                self.agent_pools[role] = []
            self.agent_pools[role].append(agent_id)
            
            self.logger.info(
                "Agent created and registered",
                agent_id=agent_id,
                role=role.value,
                status=agent_registration.status.value
            )
            
            return agent_id
            
        except Exception as e:
            self.logger.error(
                "Agent creation failed",
                role=role.value,
                agent_id=agent_id,
                error=str(e)
            )
            raise AgentError(f"Failed to create agent: {str(e)}")
    
    async def get_agent(self, agent_id: str) -> Optional[BaseAiLexAgent]:
        """
        Get agent instance by ID.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            BaseAiLexAgent: Agent instance or None if not found
        """
        registration = self.active_agents.get(agent_id)
        if registration:
            # Update last activity
            registration.last_activity = datetime.utcnow()
            return registration.agent
        return None
    
    async def get_available_agent(self, role: AgentRole) -> Optional[BaseAiLexAgent]:
        """
        Get an available agent of the specified role.
        
        Args:
            role: Required agent role
            
        Returns:
            BaseAiLexAgent: Available agent or None
        """
        if role not in self.agent_pools:
            return None
        
        # Find idle agent with lowest task load
        best_agent_id = None
        min_task_count = float('inf')
        
        for agent_id in self.agent_pools[role]:
            registration = self.active_agents.get(agent_id)
            if (registration and 
                registration.status == AgentStatus.IDLE and
                len(registration.assigned_tasks) < min_task_count):
                best_agent_id = agent_id
                min_task_count = len(registration.assigned_tasks)
        
        if best_agent_id:
            registration = self.active_agents[best_agent_id]
            registration.last_activity = datetime.utcnow()
            return registration.agent
        
        # If no idle agent available, try to create one
        if len(self.agent_pools.get(role, [])) < self.max_agents_per_role:
            try:
                new_agent_id = await self.create_agent(role)
                return await self.get_agent(new_agent_id)
            except AgentError:
                self.logger.warning(
                    "Failed to create new agent on demand",
                    role=role.value
                )
        
        return None
    
    async def remove_agent(self, agent_id: str) -> bool:
        """
        Remove and cleanup agent instance.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            bool: True if agent was removed successfully
        """
        try:
            registration = self.active_agents.get(agent_id)
            if not registration:
                return False
            
            self.logger.info("Removing agent", agent_id=agent_id, role=registration.role.value)
            
            # Shutdown the agent
            await registration.agent.shutdown()
            
            # Remove from agent pool
            if registration.role in self.agent_pools:
                self.agent_pools[registration.role] = [
                    aid for aid in self.agent_pools[registration.role] if aid != agent_id
                ]
            
            # Remove from active agents
            del self.active_agents[agent_id]
            
            self.logger.info("Agent removed successfully", agent_id=agent_id)
            return True
            
        except Exception as e:
            self.logger.error("Agent removal failed", agent_id=agent_id, error=str(e))
            return False
    
    async def list_agents(
        self,
        role_filter: Optional[AgentRole] = None,
        status_filter: Optional[AgentStatus] = None
    ) -> List[Dict[str, Any]]:
        """
        List registered agents with optional filters.
        
        Args:
            role_filter: Optional role filter
            status_filter: Optional status filter
            
        Returns:
            List[Dict[str, Any]]: List of agent information
        """
        agents = []
        
        for agent_id, registration in self.active_agents.items():
            # Apply filters
            if role_filter and registration.role != role_filter:
                continue
            if status_filter and registration.status != status_filter:
                continue
            
            agents.append({
                "agent_id": agent_id,
                "role": registration.role.value,
                "status": registration.status.value,
                "created_at": registration.created_at.isoformat(),
                "last_activity": registration.last_activity.isoformat(),
                "assigned_tasks": len(registration.assigned_tasks),
                "performance_metrics": registration.performance_metrics,
                "resource_usage": registration.resource_usage
            })
        
        return agents
    
    async def update_agent_status(self, agent_id: str, status: AgentStatus) -> bool:
        """
        Update agent status.
        
        Args:
            agent_id: Agent identifier
            status: New status
            
        Returns:
            bool: True if status updated successfully
        """
        registration = self.active_agents.get(agent_id)
        if registration:
            old_status = registration.status
            registration.status = status
            registration.last_activity = datetime.utcnow()
            
            self.logger.info(
                "Agent status updated",
                agent_id=agent_id,
                old_status=old_status.value,
                new_status=status.value
            )
            return True
        
        return False
    
    async def assign_task(self, agent_id: str, task_id: str) -> bool:
        """
        Assign task to agent.
        
        Args:
            agent_id: Agent identifier
            task_id: Task identifier
            
        Returns:
            bool: True if task assigned successfully
        """
        registration = self.active_agents.get(agent_id)
        if registration:
            registration.assigned_tasks.append(task_id)
            registration.status = AgentStatus.BUSY
            registration.last_activity = datetime.utcnow()
            
            self.logger.info(
                "Task assigned to agent",
                agent_id=agent_id,
                task_id=task_id,
                total_tasks=len(registration.assigned_tasks)
            )
            return True
        
        return False
    
    async def complete_task(self, agent_id: str, task_id: str, success: bool = True) -> bool:
        """
        Mark task as completed for agent.
        
        Args:
            agent_id: Agent identifier
            task_id: Task identifier
            success: Whether task completed successfully
            
        Returns:
            bool: True if task completion recorded successfully
        """
        registration = self.active_agents.get(agent_id)
        if registration and task_id in registration.assigned_tasks:
            registration.assigned_tasks.remove(task_id)
            
            # Update performance metrics
            if success:
                registration.performance_metrics["tasks_completed"] += 1
            else:
                registration.performance_metrics["tasks_failed"] += 1
            
            # Calculate success rate
            total_tasks = (registration.performance_metrics["tasks_completed"] + 
                          registration.performance_metrics["tasks_failed"])
            if total_tasks > 0:
                registration.performance_metrics["success_rate"] = (
                    registration.performance_metrics["tasks_completed"] / total_tasks
                )
            
            # Update status
            if not registration.assigned_tasks:
                registration.status = AgentStatus.IDLE
            
            registration.last_activity = datetime.utcnow()
            
            self.logger.info(
                "Task completed for agent",
                agent_id=agent_id,
                task_id=task_id,
                success=success,
                remaining_tasks=len(registration.assigned_tasks)
            )
            return True
        
        return False
    
    async def get_system_metrics(self) -> Dict[str, Any]:
        """
        Get system-wide agent metrics.
        
        Returns:
            Dict[str, Any]: System metrics
        """
        total_agents = len(self.active_agents)
        active_agents = len([r for r in self.active_agents.values() if r.status == AgentStatus.BUSY])
        idle_agents = len([r for r in self.active_agents.values() if r.status == AgentStatus.IDLE])
        
        agents_by_role = {}
        for role in AgentRole:
            agents_by_role[role.value] = len(self.agent_pools.get(role, []))
        
        total_tasks_completed = sum(
            r.performance_metrics.get("tasks_completed", 0) 
            for r in self.active_agents.values()
        )
        
        total_tasks_failed = sum(
            r.performance_metrics.get("tasks_failed", 0) 
            for r in self.active_agents.values()
        )
        
        overall_success_rate = (
            total_tasks_completed / (total_tasks_completed + total_tasks_failed)
            if (total_tasks_completed + total_tasks_failed) > 0 else 1.0
        )
        
        return {
            "total_agents": total_agents,
            "active_agents": active_agents,
            "idle_agents": idle_agents,
            "agents_by_role": agents_by_role,
            "total_tasks_completed": total_tasks_completed,
            "total_tasks_failed": total_tasks_failed,
            "overall_success_rate": overall_success_rate,
            "supported_roles": [role.value for role in self.agent_templates.keys()],
            "resource_utilization": await self._calculate_resource_utilization()
        }
    
    # Helper methods
    
    def _apply_config_overrides(
        self,
        base_config: AgentConfig,
        overrides: Dict[str, Any]
    ) -> AgentConfig:
        """Apply configuration overrides to base config."""
        # Create a copy of the base config
        new_config = AgentConfig(
            model=base_config.model,
            memory=base_config.memory,
            tools=base_config.tools.copy(),
            max_iterations=base_config.max_iterations,
            timeout_seconds=base_config.timeout_seconds,
            retry_attempts=base_config.retry_attempts,
            verbose=base_config.verbose,
            allow_delegation=base_config.allow_delegation,
            system_message=base_config.system_message
        )
        
        # Apply overrides
        for key, value in overrides.items():
            if hasattr(new_config, key):
                setattr(new_config, key, value)
        
        return new_config
    
    async def _check_resource_availability(self, requirements: Dict[str, Any]) -> bool:
        """Check if resources are available for new agent."""
        # Simple resource check - in production would check actual system resources
        current_memory = sum(
            r.resource_usage.get("memory_mb", 0) 
            for r in self.active_agents.values()
        )
        
        required_memory = requirements.get("memory_mb", 0)
        max_total_memory = 4096  # 4GB limit
        
        return (current_memory + required_memory) <= max_total_memory
    
    async def _calculate_resource_utilization(self) -> Dict[str, float]:
        """Calculate current resource utilization."""
        total_memory = sum(
            r.resource_usage.get("memory_mb", 0) 
            for r in self.active_agents.values()
        )
        
        total_cpu = sum(
            r.resource_usage.get("cpu_percent", 0) 
            for r in self.active_agents.values()
        )
        
        return {
            "memory_mb": total_memory,
            "cpu_percent": min(100, total_cpu),  # Cap at 100%
            "active_agents": len([r for r in self.active_agents.values() if r.status == AgentStatus.BUSY])
        }


# Global agent factory instance
agent_factory = AgentFactory()