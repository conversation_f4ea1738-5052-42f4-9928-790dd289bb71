"""
Agent roles definition for AiLex Agent System.
Defines the available agent roles without any dependencies to avoid circular imports.
"""

from enum import Enum


class AgentR<PERSON>(str, Enum):
    """Specific agent roles in the system."""
    PROJECT_ORCHESTRATOR = "project_orchestrator"
    CAMPAIGN_PLANNER = "campaign_planner"
    ASSET_GENERATOR = "asset_generator"
    SOFTWARE_ENGINEER_FULLSTACK = "software_engineer_fullstack"
    SOFTWARE_ENGINEER_FRONTEND = "software_engineer_frontend"
    SOFTWARE_ENGINEER_BACKEND = "software_engineer_backend"
    SECURITY_REVIEWER = "security_reviewer"
    FRONTEND_UX_EXPERT = "frontend_ux_expert"
    MIDDLEWARE_VALIDATOR = "middleware_validator"
    CREWAI_IMPLEMENTATION_SPECIALIST = "crewai_implementation_specialist"
    INFRA_DEPLOYMENT_SPECIALIST = "infra_deployment_specialist"
