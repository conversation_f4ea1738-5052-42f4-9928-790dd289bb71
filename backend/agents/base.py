"""
Base classes and interfaces for AiLex AI agents.
Provides foundation for all agent implementations with CrewAI integration.
"""

import asyncio
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Callable
from enum import Enum
from dataclasses import dataclass, field

import structlog
from crewai import Agent, Task, Crew
from crewai.agent import Agent as CrewAIAgent
from pydantic import BaseModel, Field

from models.agents import AgentType, AgentStatus, AgentConfig, TaskStatus
from utils.exceptions import ValidationException


logger = structlog.get_logger(__name__)


class AgentError(Exception):
    """Base exception for agent-related errors."""
    
    def __init__(self, message: str, agent_id: Optional[str] = None, agent_name: Optional[str] = None):
        self.agent_id = agent_id
        self.agent_name = agent_name
        super().__init__(message)


class AgentInitializationError(AgentError):
    """Raised when agent initialization fails."""
    pass


class AgentExecutionError(AgentError):
    """Raised when agent task execution fails."""
    pass


class AgentCommunicationError(AgentError):
    """Raised when agent communication fails."""
    pass


@dataclass
class AgentMessage:
    """Message structure for agent communication."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender_id: str = ""
    recipient_id: str = ""
    message_type: str = "info"
    content: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    priority: str = "normal"
    requires_response: bool = False
    correlation_id: Optional[str] = None


@dataclass
class AgentContext:
    """Context data structure for agent operations."""
    campaign_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    task_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    parent_context: Optional['AgentContext'] = None


class AgentInterface(ABC):
    """
    Abstract interface defining the contract for all AiLex agents.
    """
    
    @abstractmethod
    async def initialize(self, config: AgentConfig) -> bool:
        """Initialize the agent with given configuration."""
        pass
    
    @abstractmethod
    async def execute_task(self, task: Task, context: AgentContext) -> Dict[str, Any]:
        """Execute a specific task."""
        pass
    
    @abstractmethod
    async def handle_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle incoming message from another agent."""
        pass
    
    @abstractmethod
    async def get_status(self) -> AgentStatus:
        """Get current agent status."""
        pass
    
    @abstractmethod
    async def shutdown(self) -> bool:
        """Gracefully shutdown the agent."""
        pass


class BaseAiLexAgent(AgentInterface):
    """
    Base class for all AiLex AI agents.
    Provides common functionality and CrewAI integration.
    """
    
    def __init__(
        self,
        agent_id: str,
        name: str,
        description: str,
        agent_type: AgentType,
        config: AgentConfig,
    ):
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.agent_type = agent_type
        self.config = config
        self.status = AgentStatus.CREATED
        self.created_at = datetime.utcnow()
        self.last_activity = datetime.utcnow()
        
        # CrewAI agent instance
        self._crew_agent: Optional[CrewAIAgent] = None
        self._crew: Optional[Crew] = None
        
        # Performance tracking
        self.tasks_completed = 0
        self.tasks_failed = 0
        self.total_execution_time = 0.0
        
        # Message handling
        self._message_handlers: Dict[str, Callable] = {}
        self._message_queue: asyncio.Queue = asyncio.Queue()
        
        # Context and memory
        self.current_context: Optional[AgentContext] = None
        self.memory: Dict[str, Any] = {}
        
        # Event callbacks
        self._event_callbacks: Dict[str, List[Callable]] = {
            'task_started': [],
            'task_completed': [],
            'task_failed': [],
            'status_changed': [],
            'message_received': [],
        }
        
        self.logger = structlog.get_logger().bind(
            agent_id=self.agent_id,
            agent_name=self.name,
            agent_type=self.agent_type.value
        )
    
    async def initialize(self, config: AgentConfig) -> bool:
        """
        Initialize the agent with configuration.
        
        Args:
            config: Agent configuration
            
        Returns:
            bool: True if initialization successful
            
        Raises:
            AgentInitializationError: If initialization fails
        """
        try:
            self.logger.info("Initializing agent")
            self.config = config
            self.status = AgentStatus.INITIALIZING
            
            # Create CrewAI agent
            await self._create_crew_agent()
            
            # Initialize memory system
            await self._initialize_memory()
            
            # Set up message handling
            await self._setup_message_handling()
            
            # Initialize tools
            await self._initialize_tools()
            
            # Run custom initialization
            await self._custom_initialize()
            
            self.status = AgentStatus.IDLE
            self.last_activity = datetime.utcnow()
            
            await self._emit_event('status_changed', {
                'old_status': AgentStatus.INITIALIZING,
                'new_status': self.status
            })
            
            self.logger.info("Agent initialized successfully")
            return True
            
        except Exception as e:
            self.status = AgentStatus.ERROR
            self.logger.error("Agent initialization failed", error=str(e))
            raise AgentInitializationError(f"Failed to initialize agent: {str(e)}", self.agent_id, self.name)
    
    async def execute_task(self, task: Task, context: AgentContext) -> Dict[str, Any]:
        """
        Execute a specific task.
        
        Args:
            task: Task to execute
            context: Execution context
            
        Returns:
            Dict[str, Any]: Task execution result
            
        Raises:
            AgentExecutionError: If task execution fails
        """
        task_id = str(uuid.uuid4())
        start_time = datetime.utcnow()
        
        try:
            self.logger.info("Starting task execution", task_id=task_id, task_description=task.description)
            
            self.status = AgentStatus.BUSY
            self.current_context = context
            self.last_activity = datetime.utcnow()
            
            await self._emit_event('task_started', {
                'task_id': task_id,
                'task': task,
                'context': context
            })
            
            # Pre-execution hooks
            await self._pre_execute_task(task, context)
            
            # Execute the task using CrewAI
            result = await self._execute_crew_task(task, context)
            
            # Post-execution hooks
            await self._post_execute_task(task, context, result)
            
            # Update performance metrics
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            self.tasks_completed += 1
            self.total_execution_time += execution_time
            
            self.status = AgentStatus.IDLE
            self.last_activity = datetime.utcnow()
            
            await self._emit_event('task_completed', {
                'task_id': task_id,
                'task': task,
                'result': result,
                'execution_time': execution_time
            })
            
            self.logger.info(
                "Task execution completed",
                task_id=task_id,
                execution_time=execution_time,
                tasks_completed=self.tasks_completed
            )
            
            return {
                'task_id': task_id,
                'status': 'completed',
                'result': result,
                'execution_time': execution_time,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            self.tasks_failed += 1
            self.status = AgentStatus.ERROR
            
            await self._emit_event('task_failed', {
                'task_id': task_id,
                'task': task,
                'error': str(e),
                'execution_time': execution_time
            })
            
            self.logger.error(
                "Task execution failed",
                task_id=task_id,
                error=str(e),
                execution_time=execution_time,
                tasks_failed=self.tasks_failed
            )
            
            raise AgentExecutionError(f"Failed to execute task: {str(e)}", self.agent_id, self.name)
    
    async def handle_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """
        Handle incoming message from another agent.
        
        Args:
            message: Incoming message
            
        Returns:
            Optional[AgentMessage]: Response message if required
        """
        try:
            self.logger.info(
                "Received message",
                message_id=message.id,
                sender_id=message.sender_id,
                message_type=message.message_type
            )
            
            await self._emit_event('message_received', {'message': message})
            
            # Add to message queue for processing
            await self._message_queue.put(message)
            
            # Handle message based on type
            handler = self._message_handlers.get(message.message_type)
            if handler:
                response = await handler(message)
                if response:
                    return response
            
            # Default message handling
            return await self._default_message_handler(message)
            
        except Exception as e:
            self.logger.error("Message handling failed", error=str(e), message_id=message.id)
            return None
    
    async def get_status(self) -> AgentStatus:
        """Get current agent status."""
        return self.status
    
    async def shutdown(self) -> bool:
        """
        Gracefully shutdown the agent.
        
        Returns:
            bool: True if shutdown successful
        """
        try:
            self.logger.info("Shutting down agent")
            
            self.status = AgentStatus.STOPPED
            
            # Clean up resources
            await self._cleanup_resources()
            
            # Custom shutdown logic
            await self._custom_shutdown()
            
            self.logger.info("Agent shutdown completed")
            return True
            
        except Exception as e:
            self.logger.error("Agent shutdown failed", error=str(e))
            return False
    
    # Protected methods for subclass customization
    
    async def _create_crew_agent(self) -> None:
        """Create CrewAI agent instance."""
        try:
            self._crew_agent = Agent(
                role=self.name,
                goal=self.description,
                backstory=f"I am {self.name}, specialized in {self.agent_type.value}",
                verbose=self.config.verbose,
                allow_delegation=self.config.allow_delegation,
                max_iter=self.config.max_iterations,
                max_execution_time=self.config.timeout_seconds,
                system_message=self.config.system_message,
                llm=await self._create_llm_config(),
                tools=await self._create_tools(),
                memory=self.config.memory.enabled,
            )
        except Exception as e:
            raise AgentInitializationError(f"Failed to create CrewAI agent: {str(e)}")
    
    async def _create_llm_config(self) -> Any:
        """Create LLM configuration for CrewAI agent using Gemini."""
        try:
            from services.gemini_service import gemini_service
            from utils.config import settings
            
            if not settings.GEMINI_API_KEY:
                self.logger.warning("Gemini API key not configured, using default LLM")
                return None
            
            # Ensure Gemini service is authenticated
            await gemini_service.authenticate()
            
            # Return Gemini service for CrewAI integration
            return gemini_service
            
        except Exception as e:
            self.logger.warning("Failed to configure Gemini LLM", error=str(e))
            return None
    
    async def _create_tools(self) -> List[Any]:
        """Create tools for CrewAI agent."""
        # This will be implemented in subclasses
        return []
    
    async def _initialize_memory(self) -> None:
        """Initialize agent memory system."""
        if self.config.memory.enabled:
            # Initialize memory based on configuration
            self.memory = {
                'short_term': {},
                'long_term': {},
                'episodic': [],
                'semantic': {}
            }
    
    async def _setup_message_handling(self) -> None:
        """Set up message handling system."""
        # Register default message handlers
        self._message_handlers.update({
            'task_request': self._handle_task_request_message,
            'status_inquiry': self._handle_status_inquiry_message,
            'collaboration_request': self._handle_collaboration_message,
            'shutdown_request': self._handle_shutdown_message,
        })
    
    async def _initialize_tools(self) -> None:
        """Initialize agent tools."""
        # This will be implemented in subclasses
        pass
    
    async def _custom_initialize(self) -> None:
        """Custom initialization logic for subclasses."""
        pass
    
    async def _pre_execute_task(self, task: Task, context: AgentContext) -> None:
        """Pre-execution hook."""
        pass
    
    async def _execute_crew_task(self, task: Task, context: AgentContext) -> Dict[str, Any]:
        """Execute task using CrewAI."""
        if not self._crew_agent:
            raise AgentExecutionError("CrewAI agent not initialized")
        
        # Create a crew with single agent and task
        crew = Crew(
            agents=[self._crew_agent],
            tasks=[task],
            verbose=self.config.verbose,
            memory=self.config.memory.enabled,
        )
        
        # Execute the crew
        result = crew.kickoff()
        
        return {
            'output': result,
            'agent_id': self.agent_id,
            'context': context.__dict__ if context else {}
        }
    
    async def _post_execute_task(self, task: Task, context: AgentContext, result: Dict[str, Any]) -> None:
        """Post-execution hook."""
        # Store result in memory if enabled
        if self.config.memory.enabled:
            self.memory['episodic'].append({
                'task': task.description,
                'result': result,
                'timestamp': datetime.utcnow(),
                'context': context.__dict__ if context else {}
            })
    
    async def _custom_shutdown(self) -> None:
        """Custom shutdown logic for subclasses."""
        pass
    
    async def _cleanup_resources(self) -> None:
        """Clean up agent resources."""
        self._crew = None
        self._crew_agent = None
        self.current_context = None
    
    # Message handlers
    
    async def _handle_task_request_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle task request message."""
        # This would integrate with the task management system
        return AgentMessage(
            sender_id=self.agent_id,
            recipient_id=message.sender_id,
            message_type="task_response",
            content={"status": "acknowledged"},
            correlation_id=message.id
        )
    
    async def _handle_status_inquiry_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle status inquiry message."""
        return AgentMessage(
            sender_id=self.agent_id,
            recipient_id=message.sender_id,
            message_type="status_response",
            content={
                "status": self.status.value,
                "last_activity": self.last_activity.isoformat(),
                "tasks_completed": self.tasks_completed,
                "tasks_failed": self.tasks_failed
            },
            correlation_id=message.id
        )
    
    async def _handle_collaboration_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle collaboration request."""
        # Default implementation - can be overridden
        return AgentMessage(
            sender_id=self.agent_id,
            recipient_id=message.sender_id,
            message_type="collaboration_response",
            content={"available": self.status == AgentStatus.IDLE},
            correlation_id=message.id
        )
    
    async def _handle_shutdown_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle shutdown request."""
        await self.shutdown()
        return AgentMessage(
            sender_id=self.agent_id,
            recipient_id=message.sender_id,
            message_type="shutdown_response",
            content={"status": "shutting_down"},
            correlation_id=message.id
        )
    
    async def _default_message_handler(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Default message handler for unknown message types."""
        self.logger.warning("Unknown message type received", message_type=message.message_type)
        
        if message.requires_response:
            return AgentMessage(
                sender_id=self.agent_id,
                recipient_id=message.sender_id,
                message_type="error_response",
                content={"error": f"Unknown message type: {message.message_type}"},
                correlation_id=message.id
            )
        return None
    
    # Event system
    
    def add_event_callback(self, event_type: str, callback: Callable) -> None:
        """Add event callback."""
        if event_type not in self._event_callbacks:
            self._event_callbacks[event_type] = []
        self._event_callbacks[event_type].append(callback)
    
    async def _emit_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Emit event to registered callbacks."""
        if event_type in self._event_callbacks:
            for callback in self._event_callbacks[event_type]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(self, event_type, data)
                    else:
                        callback(self, event_type, data)
                except Exception as e:
                    self.logger.error("Event callback failed", event_type=event_type, error=str(e))
    
    # Utility methods
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get agent performance metrics."""
        uptime = (datetime.utcnow() - self.created_at).total_seconds() / 3600  # hours
        success_rate = (
            self.tasks_completed / (self.tasks_completed + self.tasks_failed)
            if (self.tasks_completed + self.tasks_failed) > 0 else 0
        )
        avg_execution_time = (
            self.total_execution_time / self.tasks_completed
            if self.tasks_completed > 0 else 0
        )
        
        return {
            'agent_id': self.agent_id,
            'uptime_hours': uptime,
            'tasks_completed': self.tasks_completed,
            'tasks_failed': self.tasks_failed,
            'success_rate': success_rate,
            'average_execution_time': avg_execution_time,
            'status': self.status.value,
            'last_activity': self.last_activity.isoformat(),
        }
    
    def register_message_handler(self, message_type: str, handler: Callable) -> None:
        """Register custom message handler."""
        self._message_handlers[message_type] = handler
    
    async def send_message(self, recipient_id: str, message_type: str, content: Dict[str, Any]) -> None:
        """Send message to another agent (would be handled by communicator)."""
        # This would be handled by the AgentCommunicator
        pass
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.agent_id}, name={self.name}, status={self.status})>"