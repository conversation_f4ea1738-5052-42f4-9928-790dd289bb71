"""
Example usage of the CrewAI orchestration system for Google Ads campaign management.
Demonstrates how to create and execute Google Ads workflows using the enhanced orchestration system.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any

from orchestration import FlowOrchestrator, CrewOrchestrator
from google_ads_config import GoogleAdsCampaignConfig, GoogleAdsCampaignType, GoogleAdsObjective
from models.agents import AgentType, AgentConfig
from core.campaign_planning import CampaignPlanningAgent
from core.performance_analysis import PerformanceAnalysisAgent
from core.keyword_research import KeywordResearchAgent
from core.bid_optimization import BidOptimizationAgent
from core.budget_management import BudgetManagementAgent
from core.audience_targeting import AudienceTargetingAgent
from core.quality_assurance import QualityAssuranceAgent


async def create_sample_agents() -> Dict[str, Any]:
    """Create sample agents for demonstration."""
    agents = {}
    
    # Campaign Planning Agent
    campaign_agent = CampaignPlanningAgent(
        agent_id="campaign_planner_001",
        config=AgentConfig(
            model_name="gpt-4",
            temperature=0.3,
            max_tokens=4000
        )
    )
    agents[campaign_agent.agent_id] = campaign_agent
    
    # Performance Analysis Agent
    performance_agent = PerformanceAnalysisAgent(
        agent_id="performance_analyzer_001", 
        config=AgentConfig(
            model_name="gpt-4",
            temperature=0.2,
            max_tokens=4000
        )
    )
    agents[performance_agent.agent_id] = performance_agent
    
    # Keyword Research Agent
    keyword_agent = KeywordResearchAgent(
        agent_id="keyword_researcher_001",
        config=AgentConfig(
            model_name="gpt-4",
            temperature=0.3,
            max_tokens=4000
        )
    )
    agents[keyword_agent.agent_id] = keyword_agent
    
    # Bid Optimization Agent
    bid_agent = BidOptimizationAgent(
        agent_id="bid_optimizer_001",
        config=AgentConfig(
            model_name="gpt-4",
            temperature=0.2,
            max_tokens=4000
        )
    )
    agents[bid_agent.agent_id] = bid_agent
    
    # Budget Management Agent
    budget_agent = BudgetManagementAgent(
        agent_id="budget_manager_001",
        config=AgentConfig(
            model_name="gpt-4",
            temperature=0.2,
            max_tokens=4000
        )
    )
    agents[budget_agent.agent_id] = budget_agent
    
    # Audience Targeting Agent
    audience_agent = AudienceTargetingAgent(
        agent_id="audience_targeter_001",
        config=AgentConfig(
            model_name="gpt-4",
            temperature=0.3,
            max_tokens=4000
        )
    )
    agents[audience_agent.agent_id] = audience_agent
    
    # Quality Assurance Agent
    qa_agent = QualityAssuranceAgent(
        agent_id="quality_assurance_001",
        config=AgentConfig(
            model_name="gpt-4",
            temperature=0.1,
            max_tokens=4000
        )
    )
    agents[qa_agent.agent_id] = qa_agent
    
    return agents


async def example_new_search_campaign():
    """Example: Create a new Google Ads search campaign using CrewAI coordination."""
    print("🚀 Starting New Search Campaign Creation Example")
    
    # Initialize orchestrator
    orchestrator = FlowOrchestrator()
    
    # Create and register agents
    agents = await create_sample_agents()
    for agent in agents.values():
        orchestrator.register_agent(agent)
    
    # Campaign configuration
    campaign_config = {
        "campaign_name": "AiLex AI Platform Search Campaign",
        "campaign_type": GoogleAdsCampaignType.SEARCH.value,
        "objective": GoogleAdsObjective.CONVERSIONS.value,
        "budget_daily": 500.0,
        "target_locations": ["United States", "Canada", "United Kingdom"],
        "target_keywords": ["AI platform", "artificial intelligence", "business automation"],
        "business_description": "Advanced AI platform for business process automation and optimization",
        "target_audience": "Tech-savvy business owners and executives aged 30-55",
        "landing_page_url": "https://ailex.com/platform",
        "conversion_goals": ["Sign-up completion", "Demo request", "Free trial start"],
        "user_id": "user_12345",
        "campaign_id": "campaign_67890"
    }
    
    # Execute new search campaign workflow
    execution_id = await orchestrator.execute_google_ads_crew(
        workflow_type="new_search_campaign",
        campaign_config=campaign_config
    )
    
    print(f"✅ Campaign creation started. Execution ID: {execution_id}")
    
    # Monitor execution progress
    while True:
        status = await orchestrator.get_google_ads_crew_status(execution_id)
        if not status:
            print("❌ Execution not found")
            break
        
        print(f"📊 Status: {status['status']}")
        print(f"🎯 Progress: {status['progress']['completed_tasks']}/{status['progress']['completed_tasks'] + len(status['progress']['failed_tasks'])} tasks")
        
        if status['current_task']:
            print(f"🔄 Current Task: {status['current_task']}")
        
        if status['status'] in ['completed', 'failed', 'cancelled']:
            if status['status'] == 'completed':
                print("🎉 Campaign creation completed successfully!")
                print(f"📈 Results: {status['output_data']}")
            else:
                print(f"❌ Campaign creation {status['status']}")
                if status['errors']:
                    print(f"🔍 Errors: {status['errors']}")
            break
        
        await asyncio.sleep(10)  # Check every 10 seconds
    
    # Get final metrics
    metrics = orchestrator.get_metrics()
    print(f"📊 Final Metrics: {metrics}")


async def example_campaign_optimization():
    """Example: Optimize an existing Google Ads campaign using CrewAI coordination."""
    print("🔧 Starting Campaign Optimization Example")
    
    # Initialize orchestrator
    orchestrator = FlowOrchestrator()
    
    # Create and register agents
    agents = await create_sample_agents()
    for agent in agents.values():
        orchestrator.register_agent(agent)
    
    # Campaign optimization configuration
    optimization_config = {
        "campaign_id": "existing_campaign_123",
        "campaign_name": "Existing E-commerce Campaign",
        "campaign_type": GoogleAdsCampaignType.SEARCH.value,
        "current_performance": {
            "ctr": 1.8,
            "cpc": 2.50,
            "conversion_rate": 3.2,
            "roas": 2.8,
            "quality_score": 6.5
        },
        "optimization_goals": {
            "target_ctr": 2.5,
            "target_cpc": 2.0,
            "target_conversion_rate": 4.0,
            "target_roas": 4.0,
            "target_quality_score": 8.0
        },
        "budget_daily": 750.0,
        "time_period": "last_30_days",
        "user_id": "user_12345"
    }
    
    # Execute campaign optimization workflow
    execution_id = await orchestrator.execute_google_ads_crew(
        workflow_type="campaign_optimization",
        campaign_config=optimization_config,
        required_agents=[
            AgentType.CAMPAIGN_PLANNING,
            AgentType.PERFORMANCE_ANALYSIS,
            AgentType.BID_OPTIMIZATION,
            AgentType.KEYWORD_RESEARCH,
            AgentType.QUALITY_ASSURANCE
        ]
    )
    
    print(f"✅ Campaign optimization started. Execution ID: {execution_id}")
    
    # Monitor progress (simplified for example)
    await asyncio.sleep(5)  # Simulate some processing time
    
    status = await orchestrator.get_google_ads_crew_status(execution_id)
    if status:
        print(f"📊 Optimization Status: {status['status']}")
        print(f"👥 Agents Working: {len(status['agents'])}")
        print(f"⚡ Workflow: {status['workflow_type']}")


async def example_multi_campaign_management():
    """Example: Manage multiple campaigns simultaneously using CrewAI coordination."""
    print("🎯 Starting Multi-Campaign Management Example")
    
    # Initialize orchestrator
    orchestrator = FlowOrchestrator()
    
    # Create and register agents
    agents = await create_sample_agents()
    for agent in agents.values():
        orchestrator.register_agent(agent)
    
    # Multi-campaign configuration
    multi_campaign_config = {
        "campaigns": [
            {
                "campaign_id": "search_campaign_001",
                "name": "Search Campaign A",
                "type": GoogleAdsCampaignType.SEARCH.value,
                "budget_daily": 300.0
            },
            {
                "campaign_id": "display_campaign_001", 
                "name": "Display Campaign B",
                "type": GoogleAdsCampaignType.DISPLAY.value,
                "budget_daily": 200.0
            },
            {
                "campaign_id": "shopping_campaign_001",
                "name": "Shopping Campaign C", 
                "type": GoogleAdsCampaignType.SHOPPING.value,
                "budget_daily": 400.0
            }
        ],
        "total_budget": 900.0,
        "coordination_required": True,
        "optimization_frequency": "daily",
        "user_id": "user_12345"
    }
    
    # Execute multi-campaign management workflow
    execution_id = await orchestrator.execute_google_ads_crew(
        workflow_type="multi_campaign_management",
        campaign_config=multi_campaign_config
    )
    
    print(f"✅ Multi-campaign management started. Execution ID: {execution_id}")
    
    # Get orchestrator metrics
    metrics = orchestrator.get_metrics()
    print(f"📊 Orchestrator Metrics:")
    print(f"   - Total Agents: {metrics['total_agents']}")
    print(f"   - Running Executions: {metrics['running_executions']}")
    print(f"   - Success Rate: {metrics['success_rate']:.2%}")
    print(f"   - Agent Workloads: {metrics['agent_workloads']}")
    print(f"   - Crew Metrics: {metrics['crew_metrics']}")


async def example_agent_communication():
    """Example: Demonstrate agent-to-agent communication in the orchestration system."""
    print("💬 Starting Agent Communication Example")
    
    # Initialize orchestrator and communicator
    orchestrator = FlowOrchestrator()
    
    # Create and register agents
    agents = await create_sample_agents()
    for agent in agents.values():
        orchestrator.register_agent(agent)
    
    # Start the agent communicator
    communicator = orchestrator.crew_orchestrator.agents.get(list(agents.keys())[0])
    
    print("✅ Agent communication system initialized")
    print(f"👥 {len(agents)} agents registered and ready for communication")
    print("🔄 Agents can now coordinate and share information during workflow execution")


async def main():
    """Main function demonstrating all orchestration examples."""
    print("🎬 Google Ads CrewAI Orchestration System Examples")
    print("=" * 60)
    
    try:
        # Example 1: New Search Campaign Creation
        await example_new_search_campaign()
        print("\n" + "=" * 60 + "\n")
        
        # Example 2: Campaign Optimization
        await example_campaign_optimization()
        print("\n" + "=" * 60 + "\n")
        
        # Example 3: Multi-Campaign Management
        await example_multi_campaign_management()
        print("\n" + "=" * 60 + "\n")
        
        # Example 4: Agent Communication
        await example_agent_communication()
        
        print("\n🎉 All examples completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during execution: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())