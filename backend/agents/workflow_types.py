"""
Workflow types and definitions for AiLex Agent System.
Contains workflow-related classes without dependencies to avoid circular imports.
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import List, Dict, Any, Optional
from datetime import datetime

from models.agents import AgentType, TaskPriority


class ExecutionStrategy(str, Enum):
    """Strategy for executing workflow steps."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    HYBRID = "hybrid"


@dataclass
class WorkflowStep:
    """Definition of a single step in a workflow."""
    id: str
    name: str
    agent_type: AgentType
    description: str
    dependencies: List[str] = field(default_factory=list)
    timeout_seconds: int = 3600  # 1 hour default
    priority: TaskPriority = TaskPriority.NORMAL
    parameters: Dict[str, Any] = field(default_factory=dict)
    conditions: Dict[str, Any] = field(default_factory=dict)
    retry_count: int = 3
    
    def __post_init__(self):
        """Validate step configuration."""
        if not self.id or not self.name:
            raise ValueError("Step ID and name are required")
        if self.timeout_seconds <= 0:
            raise ValueError("Timeout must be positive")


@dataclass
class WorkflowDefinition:
    """Definition of a complete workflow."""
    id: str
    name: str
    description: str
    steps: List[WorkflowStep]
    strategy: ExecutionStrategy = ExecutionStrategy.SEQUENTIAL
    max_duration_seconds: int = 14400  # 4 hours default
    error_handling: str = "stop"  # stop, continue, retry
    metadata: Dict[str, Any] = field(default_factory=dict)
    version: str = "1.0"
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Validate workflow definition."""
        if not self.id or not self.name:
            raise ValueError("Workflow ID and name are required")
        if not self.steps:
            raise ValueError("Workflow must have at least one step")
        if self.max_duration_seconds <= 0:
            raise ValueError("Max duration must be positive")
        
        # Validate step dependencies
        step_ids = {step.id for step in self.steps}
        for step in self.steps:
            for dep in step.dependencies:
                if dep not in step_ids:
                    raise ValueError(f"Step {step.id} depends on non-existent step {dep}")
        
        if self.created_at is None:
            self.created_at = datetime.utcnow()
    
    def get_step(self, step_id: str) -> Optional[WorkflowStep]:
        """Get a step by ID."""
        for step in self.steps:
            if step.id == step_id:
                return step
        return None
    
    def get_dependencies(self, step_id: str) -> List[str]:
        """Get dependencies for a specific step."""
        step = self.get_step(step_id)
        return step.dependencies if step else []
    
    def get_dependents(self, step_id: str) -> List[str]:
        """Get steps that depend on the given step."""
        dependents = []
        for step in self.steps:
            if step_id in step.dependencies:
                dependents.append(step.id)
        return dependents
    
    def validate_execution_order(self) -> bool:
        """Validate that the workflow can be executed without circular dependencies."""
        # Simple topological sort check
        visited = set()
        rec_stack = set()
        
        def has_cycle(step_id: str) -> bool:
            visited.add(step_id)
            rec_stack.add(step_id)
            
            for dep in self.get_dependencies(step_id):
                if dep not in visited:
                    if has_cycle(dep):
                        return True
                elif dep in rec_stack:
                    return True
            
            rec_stack.remove(step_id)
            return False
        
        for step in self.steps:
            if step.id not in visited:
                if has_cycle(step.id):
                    return False
        
        return True


@dataclass
class WorkflowExecution:
    """Runtime execution state of a workflow."""
    workflow_id: str
    execution_id: str
    status: str = "pending"  # pending, running, completed, failed, cancelled
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    current_step: Optional[str] = None
    step_results: Dict[str, Any] = field(default_factory=dict)
    step_statuses: Dict[str, str] = field(default_factory=dict)
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize execution state."""
        if self.started_at is None and self.status == "running":
            self.started_at = datetime.utcnow()
    
    def mark_step_completed(self, step_id: str, result: Any = None):
        """Mark a step as completed with optional result."""
        self.step_statuses[step_id] = "completed"
        if result is not None:
            self.step_results[step_id] = result
    
    def mark_step_failed(self, step_id: str, error: str):
        """Mark a step as failed with error message."""
        self.step_statuses[step_id] = "failed"
        self.error_message = f"Step {step_id} failed: {error}"
    
    def get_completed_steps(self) -> List[str]:
        """Get list of completed step IDs."""
        return [step_id for step_id, status in self.step_statuses.items() 
                if status == "completed"]
    
    def get_failed_steps(self) -> List[str]:
        """Get list of failed step IDs."""
        return [step_id for step_id, status in self.step_statuses.items() 
                if status == "failed"]
    
    def is_step_ready(self, step: WorkflowStep) -> bool:
        """Check if a step is ready to execute (all dependencies completed)."""
        completed_steps = set(self.get_completed_steps())
        return all(dep in completed_steps for dep in step.dependencies)
