"""
AiLex Ad Agent System - CrewAI Agent Framework
Main entry point for the agent system with base classes and utilities.
"""

from .base import BaseAiLexA<PERSON>, AgentInterface, AgentError
from .communication import AgentCommunicationHub
# from .orchestration import FlowOrchestrator  # To be implemented
# Registry components will be implemented later
# from .registry import AgentRegistry, AgentLifecycleManager
# Memory components will be implemented later
# from .memory import AgentMemoryManager, ContextPersistence
from .tracing import PhoenixTracer, AgentTracer
from .core import *

__version__ = "1.0.0"

__all__ = [
    # Base classes
    "BaseAiLexAgent",
    "AgentInterface", 
    "AgentError",
    
    # Communication
    "AgentCommunicationHub",
    # "FlowOrchestrator",  # To be implemented
    
    # Management (to be implemented)
    # "AgentRegistry",
    # "AgentLifecycleManager",
    
    # Memory (to be implemented)
    # "AgentMemoryManager",
    # "ContextPersistence",
    
    # Tracing
    "PhoenixTracer",
    "AgentTracer",
    
    # Core agents
    "CampaignPlanningAgent",
    "AdAssetGenerationAgent", 
    "OptimizationAgent",
    "ComplianceAgent",
]