"""
Google Ads Campaign Management Configuration for AiLex Agent System.
Configures the 8-agent team for specialized Google Ads campaign tasks.
"""

from typing import Dict, List, Any, Optional
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

from .roles import AgentRole
from models.agents import TaskPriority


class GoogleAdsCampaignType(str, Enum):
    """Google Ads campaign types."""
    SEARCH = "search"
    DISPLAY = "display"
    VIDEO = "video" 
    SHOPPING = "shopping"
    APP = "app"
    DISCOVERY = "discovery"
    LOCAL = "local"
    SMART = "smart"
    PERFORMANCE_MAX = "performance_max"


class GoogleAdsObjective(str, Enum):
    """Google Ads campaign objectives."""
    AWARENESS = "awareness"
    CONSIDERATION = "consideration"
    CONVERSIONS = "conversions"
    LEADS = "leads"
    SALES = "sales"
    TRAFFIC = "traffic"
    APP_PROMOTION = "app_promotion"


@dataclass
class GoogleAdsCampaignConfig:
    """Configuration for Google Ads campaign management."""
    campaign_name: str
    campaign_type: GoogleAdsCampaignType
    objective: GoogleAdsObjective
    budget_daily: float
    target_locations: List[str]
    target_keywords: List[str]
    business_description: str
    target_audience: str
    landing_page_url: str
    conversion_goals: List[str]
    bid_strategy: str = "maximize_conversions"
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


class GoogleAdsAgentSpecialization:
    """
    Maps each of the 8 agents to specific Google Ads campaign tasks.
    """
    
    @staticmethod
    def get_agent_tasks_mapping() -> Dict[AgentRole, Dict[str, Any]]:
        """Get specialized task mapping for each agent role in Google Ads context."""
        return {
            AgentRole.PROJECT_ORCHESTRATOR: {
                "primary_tasks": [
                    "campaign_strategy_coordination",
                    "workflow_management",
                    "resource_allocation",
                    "progress_monitoring",
                    "stakeholder_communication"
                ],
                "google_ads_focus": [
                    "Campaign timeline planning",
                    "Budget allocation across campaigns",
                    "Performance milestone tracking",
                    "Cross-campaign optimization coordination"
                ],
                "tools": ["project_management", "reporting", "communication"],
                "priority": TaskPriority.HIGH
            },
            
            AgentRole.CAMPAIGN_PLANNER: {
                "primary_tasks": [
                    "market_research",
                    "competitor_analysis", 
                    "keyword_research",
                    "audience_targeting",
                    "campaign_structure_design"
                ],
                "google_ads_focus": [
                    "Google Ads account structure planning",
                    "Campaign and ad group organization",
                    "Keyword research and grouping",
                    "Audience research and targeting",
                    "Geographic and demographic targeting"
                ],
                "tools": ["google_ads_api", "keyword_planner", "audience_insights"],
                "priority": TaskPriority.HIGH
            },
            
            AgentRole.ASSET_GENERATOR: {
                "primary_tasks": [
                    "ad_copy_creation",
                    "creative_asset_generation",
                    "landing_page_optimization",
                    "asset_performance_testing",
                    "brand_compliance_checking"
                ],
                "google_ads_focus": [
                    "Search ad headlines and descriptions",
                    "Display ad creative generation",
                    "Video ad scripts and storyboards", 
                    "Shopping product titles and descriptions",
                    "Ad extensions creation"
                ],
                "tools": ["creative_generation", "image_creation", "copywriting"],
                "priority": TaskPriority.HIGH
            },
            
            AgentRole.SOFTWARE_ENGINEER_FULLSTACK: {
                "primary_tasks": [
                    "integration_development",
                    "api_implementation",
                    "data_pipeline_creation",
                    "automation_scripts",
                    "performance_monitoring"
                ],
                "google_ads_focus": [
                    "Google Ads API integration",
                    "Conversion tracking implementation",
                    "Automated bidding system development",
                    "Campaign automation workflows",
                    "Real-time performance dashboards"
                ],
                "tools": ["google_ads_api", "python", "javascript", "databases"],
                "priority": TaskPriority.NORMAL
            },
            
            AgentRole.SOFTWARE_ENGINEER_BACKEND: {
                "primary_tasks": [
                    "backend_service_development",
                    "database_optimization",
                    "api_endpoint_creation",
                    "data_processing",
                    "system_integration"
                ],
                "google_ads_focus": [
                    "Campaign data storage and retrieval",
                    "Performance metrics aggregation",
                    "Automated reporting systems",
                    "Budget management backend",
                    "Multi-account management"
                ],
                "tools": ["fastapi", "postgresql", "redis", "celery"],
                "priority": TaskPriority.NORMAL
            },
            
            AgentRole.SECURITY_REVIEWER: {
                "primary_tasks": [
                    "security_assessment",
                    "compliance_verification",
                    "data_protection_audit",
                    "access_control_review",
                    "vulnerability_scanning"
                ],
                "google_ads_focus": [
                    "Google Ads API security compliance",
                    "Customer data protection (GDPR/CCPA)",
                    "Account access management",
                    "Billing and payment security",
                    "Ad policy compliance verification"
                ],
                "tools": ["security_scanners", "compliance_checkers", "audit_tools"],
                "priority": TaskPriority.HIGH
            },
            
            AgentRole.FRONTEND_UX_EXPERT: {
                "primary_tasks": [
                    "dashboard_development",
                    "user_interface_design",
                    "user_experience_optimization",
                    "responsive_design",
                    "accessibility_implementation"
                ],
                "google_ads_focus": [
                    "Campaign management dashboard",
                    "Performance visualization interfaces",
                    "Campaign creation wizards",
                    "Real-time monitoring displays",
                    "Mobile-responsive ad preview tools"
                ],
                "tools": ["react", "nextjs", "tailwind", "chart_libraries"],
                "priority": TaskPriority.NORMAL
            },
            
            AgentRole.MIDDLEWARE_VALIDATOR: {
                "primary_tasks": [
                    "input_validation",
                    "data_transformation",
                    "api_middleware_development",
                    "request_sanitization",
                    "response_formatting"
                ],
                "google_ads_focus": [
                    "Google Ads API request validation",
                    "Campaign data validation",
                    "Budget and bid validation",
                    "Keyword and audience data sanitization",
                    "Performance data formatting"
                ],
                "tools": ["pydantic", "zod", "validation_libraries"],
                "priority": TaskPriority.NORMAL
            },
            
            AgentRole.INFRA_DEPLOYMENT_SPECIALIST: {
                "primary_tasks": [
                    "infrastructure_setup",
                    "deployment_automation",
                    "monitoring_configuration",
                    "scaling_management",
                    "backup_and_recovery"
                ],
                "google_ads_focus": [
                    "Google Ads campaign automation deployment",
                    "Performance monitoring infrastructure",
                    "Multi-region campaign management",
                    "Automated backup of campaign data",
                    "Disaster recovery for ad operations"
                ],
                "tools": ["docker", "kubernetes", "terraform", "monitoring"],
                "priority": TaskPriority.LOW
            }
        }
    
    @staticmethod
    def get_campaign_workflow_stages() -> List[Dict[str, Any]]:
        """Get the standard workflow stages for Google Ads campaign management."""
        return [
            {
                "stage": "planning",
                "name": "Campaign Planning & Research",
                "agents": [AgentRole.PROJECT_ORCHESTRATOR, AgentRole.CAMPAIGN_PLANNER],
                "tasks": [
                    "market_research",
                    "competitor_analysis",
                    "keyword_research",
                    "audience_targeting",
                    "budget_planning"
                ],
                "duration_hours": 4,
                "dependencies": []
            },
            {
                "stage": "creative_development", 
                "name": "Creative Asset Development",
                "agents": [AgentRole.ASSET_GENERATOR, AgentRole.FRONTEND_UX_EXPERT],
                "tasks": [
                    "ad_copy_creation",
                    "creative_asset_generation",
                    "landing_page_optimization",
                    "asset_testing"
                ],
                "duration_hours": 6,
                "dependencies": ["planning"]
            },
            {
                "stage": "technical_implementation",
                "name": "Technical Implementation",
                "agents": [
                    AgentRole.SOFTWARE_ENGINEER_FULLSTACK,
                    AgentRole.SOFTWARE_ENGINEER_BACKEND,
                    AgentRole.MIDDLEWARE_VALIDATOR
                ],
                "tasks": [
                    "google_ads_api_integration",
                    "conversion_tracking_setup",
                    "data_validation_implementation",
                    "automation_development"
                ],
                "duration_hours": 8,
                "dependencies": ["planning"]
            },
            {
                "stage": "security_compliance",
                "name": "Security & Compliance Review",
                "agents": [AgentRole.SECURITY_REVIEWER],
                "tasks": [
                    "security_assessment",
                    "compliance_verification",
                    "data_protection_audit",
                    "policy_compliance_check"
                ],
                "duration_hours": 3,
                "dependencies": ["technical_implementation", "creative_development"]
            },
            {
                "stage": "deployment",
                "name": "Deployment & Launch",
                "agents": [AgentRole.INFRA_DEPLOYMENT_SPECIALIST, AgentRole.PROJECT_ORCHESTRATOR],
                "tasks": [
                    "infrastructure_deployment",
                    "campaign_launch",
                    "monitoring_setup",
                    "performance_baseline_establishment"
                ],
                "duration_hours": 2,
                "dependencies": ["security_compliance"]
            },
            {
                "stage": "optimization",
                "name": "Performance Monitoring & Optimization",
                "agents": [
                    AgentRole.PROJECT_ORCHESTRATOR,
                    AgentRole.CAMPAIGN_PLANNER,
                    AgentRole.ASSET_GENERATOR
                ],
                "tasks": [
                    "performance_monitoring",
                    "optimization_recommendations",
                    "a_b_testing",
                    "continuous_improvement"
                ],
                "duration_hours": 24,  # Ongoing
                "dependencies": ["deployment"]
            }
        ]
    
    @staticmethod
    def get_agent_collaboration_matrix() -> Dict[str, List[str]]:
        """Get collaboration matrix showing which agents work together on tasks."""
        return {
            "campaign_strategy": [
                AgentRole.PROJECT_ORCHESTRATOR.value,
                AgentRole.CAMPAIGN_PLANNER.value
            ],
            "creative_development": [
                AgentRole.ASSET_GENERATOR.value,
                AgentRole.FRONTEND_UX_EXPERT.value,
                AgentRole.CAMPAIGN_PLANNER.value
            ],
            "technical_integration": [
                AgentRole.SOFTWARE_ENGINEER_FULLSTACK.value,
                AgentRole.SOFTWARE_ENGINEER_BACKEND.value,
                AgentRole.MIDDLEWARE_VALIDATOR.value
            ],
            "security_review": [
                AgentRole.SECURITY_REVIEWER.value,
                AgentRole.MIDDLEWARE_VALIDATOR.value,
                AgentRole.SOFTWARE_ENGINEER_BACKEND.value
            ],
            "deployment_operations": [
                AgentRole.INFRA_DEPLOYMENT_SPECIALIST.value,
                AgentRole.PROJECT_ORCHESTRATOR.value,
                AgentRole.SECURITY_REVIEWER.value
            ],
            "performance_optimization": [
                AgentRole.PROJECT_ORCHESTRATOR.value,
                AgentRole.CAMPAIGN_PLANNER.value,
                AgentRole.ASSET_GENERATOR.value,
                AgentRole.SOFTWARE_ENGINEER_FULLSTACK.value
            ]
        }
    
    @staticmethod
    def get_google_ads_kpis() -> Dict[str, Dict[str, Any]]:
        """Get Google Ads KPIs that agents should monitor and optimize."""
        return {
            "performance_metrics": {
                "impressions": {"target": ">= 10000", "importance": "medium"},
                "clicks": {"target": ">= 500", "importance": "high"},
                "ctr": {"target": ">= 2.0%", "importance": "high"},
                "cpc": {"target": "<= budget/clicks", "importance": "high"},
                "conversions": {"target": ">= 50", "importance": "critical"},
                "conversion_rate": {"target": ">= 5.0%", "importance": "critical"},
                "cost_per_conversion": {"target": "<= target_cpa", "importance": "critical"},
                "roas": {"target": ">= 4.0", "importance": "critical"}
            },
            "quality_metrics": {
                "quality_score": {"target": ">= 7", "importance": "high"},
                "ad_relevance": {"target": "above_average", "importance": "medium"},
                "landing_page_experience": {"target": "above_average", "importance": "medium"},
                "expected_ctr": {"target": "above_average", "importance": "medium"}
            },
            "budget_metrics": {
                "budget_utilization": {"target": "85-95%", "importance": "high"},
                "impression_share": {"target": ">= 80%", "importance": "medium"},
                "search_lost_is_budget": {"target": "<= 10%", "importance": "high"},
                "search_lost_is_rank": {"target": "<= 5%", "importance": "medium"}
            }
        }


class GoogleAdsWorkflowTemplates:
    """
    Pre-configured workflow templates for common Google Ads scenarios.
    """
    
    @staticmethod
    def new_search_campaign_workflow() -> Dict[str, Any]:
        """Template for creating a new Google Ads search campaign."""
        return {
            "name": "New Google Ads Search Campaign",
            "description": "Complete workflow for creating and launching a new search campaign",
            "estimated_duration_hours": 23,
            "agents_required": 8,
            "stages": GoogleAdsAgentSpecialization.get_campaign_workflow_stages(),
            "success_criteria": [
                "Campaign launched successfully",
                "All security compliance checks passed",
                "Conversion tracking implemented",
                "Performance monitoring active",
                "Quality Score >= 7 for primary keywords"
            ]
        }
    
    @staticmethod
    def campaign_optimization_workflow() -> Dict[str, Any]:
        """Template for optimizing existing campaigns."""
        return {
            "name": "Google Ads Campaign Optimization",
            "description": "Comprehensive campaign performance analysis and optimization",
            "estimated_duration_hours": 8,
            "agents_required": 5,
            "stages": [
                {
                    "stage": "performance_analysis",
                    "agents": [AgentRole.CAMPAIGN_PLANNER, AgentRole.PROJECT_ORCHESTRATOR],
                    "tasks": [
                        "performance_data_analysis",
                        "kpi_assessment",
                        "bottleneck_identification",
                        "optimization_opportunity_analysis"
                    ]
                },
                {
                    "stage": "optimization_implementation",
                    "agents": [
                        AgentRole.CAMPAIGN_PLANNER,
                        AgentRole.ASSET_GENERATOR,
                        AgentRole.SOFTWARE_ENGINEER_FULLSTACK
                    ],
                    "tasks": [
                        "keyword_optimization",
                        "ad_copy_refresh",
                        "bid_strategy_adjustment",
                        "audience_refinement",
                        "landing_page_optimization"
                    ]
                },
                {
                    "stage": "testing_validation",
                    "agents": [AgentRole.SECURITY_REVIEWER, AgentRole.MIDDLEWARE_VALIDATOR],
                    "tasks": [
                        "change_validation",
                        "performance_impact_assessment",
                        "compliance_verification"
                    ]
                }
            ]
        }
    
    @staticmethod
    def multi_campaign_management_workflow() -> Dict[str, Any]:
        """Template for managing multiple campaigns simultaneously."""
        return {
            "name": "Multi-Campaign Management",
            "description": "Coordinated management of multiple Google Ads campaigns",
            "estimated_duration_hours": 16,
            "agents_required": 8,
            "coordination_required": True,
            "stages": [
                {
                    "stage": "portfolio_analysis", 
                    "agents": [AgentRole.PROJECT_ORCHESTRATOR, AgentRole.CAMPAIGN_PLANNER],
                    "tasks": [
                        "cross_campaign_performance_analysis",
                        "budget_allocation_optimization",
                        "audience_overlap_analysis",
                        "keyword_cannibalization_check"
                    ]
                },
                {
                    "stage": "coordinated_optimization",
                    "agents": [
                        AgentRole.CAMPAIGN_PLANNER,
                        AgentRole.ASSET_GENERATOR,
                        AgentRole.SOFTWARE_ENGINEER_FULLSTACK
                    ],
                    "tasks": [
                        "cross_campaign_keyword_optimization",
                        "unified_creative_strategy",
                        "coordinated_bid_management",
                        "shared_negative_keyword_implementation"
                    ]
                }
            ]
        }


# Configuration constants
GOOGLE_ADS_API_VERSION = "v14"
GOOGLE_ADS_API_SCOPE = "https://www.googleapis.com/auth/adwords"
DEFAULT_BUDGET_CURRENCY = "USD"
DEFAULT_TIME_ZONE = "America/New_York"

# Agent specialization configuration
AGENT_GOOGLE_ADS_CONFIG = GoogleAdsAgentSpecialization.get_agent_tasks_mapping()
WORKFLOW_TEMPLATES = {
    "new_search_campaign": GoogleAdsWorkflowTemplates.new_search_campaign_workflow(),
    "campaign_optimization": GoogleAdsWorkflowTemplates.campaign_optimization_workflow(),
    "multi_campaign_management": GoogleAdsWorkflowTemplates.multi_campaign_management_workflow()
}