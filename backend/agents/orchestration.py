"""
Flow orchestration system for AiLex AI agents.
Manages agent coordination, workflow execution, and resource allocation.
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable, Union, Set
from dataclasses import dataclass, field
from collections import defaultdict

import structlog
from crewai import Crew, Task, Agent
from crewai.flow import Flow, start, listen, or_, and_, router
from crewai.process import Process
from crewai.memory import LongTermMemory, ShortTermMemory

from .base import BaseAiLexAgent, AgentMessage, AgentContext, AgentError
from .tracing import PhoenixTracer, create_agent_tracer
from .google_ads_config import GoogleAdsAgentSpecialization, GoogleAdsWorkflowTemplates, WORKFLOW_TEMPLATES
from models.agents import AgentType, AgentStatus, TaskStatus, TaskPriority
from utils.config import settings


logger = structlog.get_logger(__name__)


class WorkflowStatus(str, Enum):
    """Workflow execution status."""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ExecutionStrategy(str, Enum):
    """Workflow execution strategies."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    PIPELINE = "pipeline"
    HYBRID = "hybrid"


@dataclass
class WorkflowStep:
    """Individual workflow step definition."""
    id: str
    name: str
    agent_type: AgentType
    description: str
    input_schema: Dict[str, Any] = field(default_factory=dict)
    output_schema: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    conditions: List[str] = field(default_factory=list)
    timeout_seconds: int = 300
    retry_attempts: int = 3
    priority: TaskPriority = TaskPriority.NORMAL
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class WorkflowDefinition:
    """Complete workflow definition."""
    id: str
    name: str
    description: str
    version: str = "1.0.0"
    steps: List[WorkflowStep] = field(default_factory=list)
    strategy: ExecutionStrategy = ExecutionStrategy.SEQUENTIAL
    max_duration_seconds: int = 3600
    error_handling: str = "stop"  # stop, continue, retry
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CrewExecution:
    """CrewAI crew execution state and tracking."""
    id: str
    crew_id: str
    crew: Optional[Crew] = None
    status: WorkflowStatus = WorkflowStatus.PENDING
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    context: AgentContext = field(default_factory=AgentContext)
    input_data: Dict[str, Any] = field(default_factory=dict)
    output_data: Dict[str, Any] = field(default_factory=dict)
    task_results: Dict[str, Any] = field(default_factory=dict)
    error_log: List[str] = field(default_factory=list)
    current_task: Optional[str] = None
    completed_tasks: List[str] = field(default_factory=list)
    failed_tasks: List[str] = field(default_factory=list)
    retry_counts: Dict[str, int] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class WorkflowExecution:
    """Workflow execution state and tracking."""
    id: str
    workflow_id: str
    status: WorkflowStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    context: AgentContext = field(default_factory=AgentContext)
    input_data: Dict[str, Any] = field(default_factory=dict)
    output_data: Dict[str, Any] = field(default_factory=dict)
    step_results: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    error_log: List[str] = field(default_factory=list)
    current_step: Optional[str] = None
    completed_steps: List[str] = field(default_factory=list)
    failed_steps: List[str] = field(default_factory=list)
    retry_counts: Dict[str, int] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    crew_execution: Optional[CrewExecution] = None


class CrewOrchestrator:
    """
    CrewAI-based orchestrator for Google Ads agent coordination.
    Manages crew creation, task delegation, and agent collaboration.
    """
    
    def __init__(self, tracer: Optional[PhoenixTracer] = None):
        self.tracer = tracer
        self.crews: Dict[str, Crew] = {}
        self.crew_executions: Dict[str, CrewExecution] = {}
        self.agents: Dict[str, BaseAiLexAgent] = {}
        self.crew_agents: Dict[str, Agent] = {}  # CrewAI Agent instances
        self.running_crews: Set[str] = set()
        
        # Google Ads specialization
        self.specialization = GoogleAdsAgentSpecialization()
        self.workflow_templates = WORKFLOW_TEMPLATES
        
        # Resource management
        self.agent_pools: Dict[AgentType, List[str]] = defaultdict(list)
        self.agent_workloads: Dict[str, int] = defaultdict(int)
        self.max_concurrent_crews = 5
        
        # Memory systems
        self.long_term_memory = LongTermMemory()
        self.short_term_memory = ShortTermMemory()
        
        # Event system
        self.event_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        
        self.logger = structlog.get_logger().bind(component="crew_orchestrator")
    
    def register_agent(self, agent: BaseAiLexAgent) -> None:
        """
        Register an agent with the crew orchestrator.
        
        Args:
            agent: Agent instance to register
        """
        self.agents[agent.agent_id] = agent
        self.agent_pools[agent.agent_type].append(agent.agent_id)
        self.agent_workloads[agent.agent_id] = 0
        
        # Create CrewAI Agent instance
        crew_agent = self._create_crew_agent(agent)
        self.crew_agents[agent.agent_id] = crew_agent
        
        self.logger.info(
            "Agent registered with crew orchestrator",
            agent_id=agent.agent_id,
            agent_name=agent.name,
            agent_type=agent.agent_type.value
        )
    
    def _create_crew_agent(self, ailex_agent: BaseAiLexAgent) -> Agent:
        """
        Create a CrewAI Agent from an AiLex agent.
        
        Args:
            ailex_agent: AiLex agent instance
            
        Returns:
            CrewAI Agent instance
        """
        # Get Google Ads specialization for this agent type
        specialization = self.specialization.get_agent_tasks_mapping().get(
            ailex_agent.agent_type, {}
        )
        
        return Agent(
            role=ailex_agent.name,
            goal=f"Execute {ailex_agent.agent_type.value} tasks for Google Ads campaigns with expertise in {', '.join(specialization.get('google_ads_focus', []))}",
            backstory=f"You are a specialized AI agent for Google Ads campaign management with deep expertise in {ailex_agent.agent_type.value}. "
                     f"Your primary responsibilities include: {', '.join(specialization.get('primary_tasks', []))}. "
                     f"You excel at Google Ads-specific tasks such as: {', '.join(specialization.get('google_ads_focus', []))}.",
            tools=getattr(ailex_agent, 'tools', []),
            verbose=settings.DEBUG,
            memory=True,
            max_iter=5,
            max_execution_time=300,  # 5 minutes timeout per task
            allow_delegation=True,
            system_template=f"""You are {ailex_agent.name}, a specialized AI agent for Google Ads campaign management.
            
Your Role: {ailex_agent.agent_type.value}
Your Goal: Execute high-quality Google Ads optimization tasks with precision and expertise.

Key Responsibilities:
{chr(10).join([f"- {task}" for task in specialization.get('primary_tasks', [])])}

Google Ads Expertise:
{chr(10).join([f"- {focus}" for focus in specialization.get('google_ads_focus', [])])}

Always provide detailed, actionable insights and maintain focus on campaign performance optimization."""
        )
    
    async def create_google_ads_crew(
        self,
        workflow_type: str,
        campaign_config: Dict[str, Any],
        required_agents: Optional[List[AgentType]] = None
    ) -> str:
        """
        Create a specialized crew for Google Ads campaign management.
        
        Args:
            workflow_type: Type of workflow (e.g., 'new_search_campaign', 'campaign_optimization')
            campaign_config: Campaign configuration data
            required_agents: Specific agents required for this crew
            
        Returns:
            str: Crew ID
            
        Raises:
            AgentError: If crew cannot be created
        """
        if len(self.running_crews) >= self.max_concurrent_crews:
            raise AgentError("Maximum concurrent crews reached")
        
        workflow_template = self.workflow_templates.get(workflow_type)
        if not workflow_template:
            raise AgentError(f"Unknown workflow type: {workflow_type}")
        
        crew_id = str(uuid.uuid4())
        
        # Determine required agents from workflow template or explicit list
        if required_agents is None:
            required_agents = self._extract_required_agents(workflow_template)
        
        # Select agents for the crew
        selected_agents = []
        crew_agents = []
        
        for agent_type in required_agents:
            agent = await self._find_available_agent(agent_type)
            if not agent:
                raise AgentError(f"No available agent for type: {agent_type}")
            
            selected_agents.append(agent)
            crew_agents.append(self.crew_agents[agent.agent_id])
            self.agent_workloads[agent.agent_id] += 1
        
        # Create tasks for the crew
        tasks = self._create_crew_tasks(workflow_template, campaign_config)
        
        # Create the crew
        crew = Crew(
            agents=crew_agents,
            tasks=tasks,
            process=Process.hierarchical if len(crew_agents) > 3 else Process.sequential,
            verbose=settings.DEBUG,
            memory=True,
            manager_llm=settings.OPENAI_MODEL,
            max_rpm=30,  # Rate limiting
            language="en",
            full_output=True,
            share_crew=False,
            step_callback=self._crew_step_callback,
            task_callback=self._crew_task_callback
        )
        
        self.crews[crew_id] = crew
        
        # Create crew execution tracking
        crew_execution = CrewExecution(
            id=crew_id,
            crew_id=crew_id,
            crew=crew,
            context=AgentContext(
                user_id=campaign_config.get('user_id'),
                campaign_id=campaign_config.get('campaign_id'),
                metadata=campaign_config
            ),
            input_data=campaign_config,
            metadata={
                'workflow_type': workflow_type,
                'selected_agents': [agent.agent_id for agent in selected_agents],
                'estimated_duration': workflow_template.get('estimated_duration_hours', 0)
            }
        )
        
        self.crew_executions[crew_id] = crew_execution
        self.running_crews.add(crew_id)
        
        self.logger.info(
            "Google Ads crew created",
            crew_id=crew_id,
            workflow_type=workflow_type,
            agents_count=len(selected_agents),
            tasks_count=len(tasks)
        )
        
        return crew_id
    
    def _extract_required_agents(self, workflow_template: Dict[str, Any]) -> List[AgentType]:
        """
        Extract required agent types from workflow template.
        
        Args:
            workflow_template: Workflow template configuration
            
        Returns:
            List of required agent types
        """
        required_agents = set()
        
        for stage in workflow_template.get('stages', []):
            for agent_role in stage.get('agents', []):
                # Convert agent role to AgentType
                try:
                    if hasattr(agent_role, 'value'):
                        agent_type = next(
                            at for at in AgentType 
                            if at.value.lower() == agent_role.value.lower()
                        )
                    else:
                        agent_type = next(
                            at for at in AgentType 
                            if at.value.lower() == str(agent_role).lower()
                        )
                    required_agents.add(agent_type)
                except StopIteration:
                    # If agent role doesn't match AgentType, use default mapping
                    self.logger.warning(
                        "Unknown agent role in workflow template",
                        agent_role=str(agent_role)
                    )
        
        return list(required_agents)
    
    def _create_crew_tasks(
        self,
        workflow_template: Dict[str, Any],
        campaign_config: Dict[str, Any]
    ) -> List[Task]:
        """
        Create CrewAI tasks from workflow template.
        
        Args:
            workflow_template: Workflow template configuration
            campaign_config: Campaign configuration data
            
        Returns:
            List of CrewAI Task instances
        """
        tasks = []
        
        for stage in workflow_template.get('stages', []):
            stage_name = stage.get('stage', 'unknown')
            
            for task_name in stage.get('tasks', []):
                task = Task(
                    description=self._generate_task_description(task_name, campaign_config),
                    expected_output=self._generate_expected_output(task_name),
                    agent=None,  # Will be assigned by CrewAI based on agents list
                    tools=[],
                    async_execution=False,
                    context=[],  # Dependencies will be set up later
                    output_json=None,
                    output_pydantic=None,
                    output_file=None,
                    callback=None
                )
                
                tasks.append(task)
        
        return tasks
    
    def _generate_task_description(self, task_name: str, campaign_config: Dict[str, Any]) -> str:
        """
        Generate detailed task description based on task name and campaign config.
        
        Args:
            task_name: Name of the task
            campaign_config: Campaign configuration data
            
        Returns:
            Detailed task description
        """
        base_descriptions = {
            "market_research": f"Conduct comprehensive market research for {campaign_config.get('business_description', 'the business')} campaign targeting {campaign_config.get('target_audience', 'defined audience')}. Analyze industry trends, market size, competitive landscape, and identify opportunities and threats.",
            
            "competitor_analysis": f"Perform detailed competitor analysis for {campaign_config.get('campaign_name', 'the campaign')}. Identify top competitors, analyze their advertising strategies, keywords, ad copy, and positioning. Provide actionable insights for competitive advantage.",
            
            "keyword_research": f"Execute comprehensive keyword research for {campaign_config.get('campaign_type', 'search')} campaign. Identify high-value primary and secondary keywords, long-tail variations, and negative keywords. Include search volume, competition, and cost estimates.",
            
            "audience_targeting": f"Develop precise audience targeting strategy for {campaign_config.get('objective', 'conversions')} campaign. Define demographic, geographic, behavioral, and interest-based targeting parameters. Create audience personas and recommend targeting optimizations.",
            
            "campaign_structure_design": f"Design optimal campaign structure for {campaign_config.get('campaign_type', 'search')} campaign with budget of ${campaign_config.get('budget_daily', 0)}/day. Create campaign hierarchy, ad group organization, and keyword distribution strategy.",
            
            "ad_copy_creation": f"Create compelling ad copy for {campaign_config.get('campaign_type', 'search')} campaign targeting {campaign_config.get('target_keywords', [])}. Develop multiple headline and description variations optimized for {campaign_config.get('objective', 'conversions')}.",
            
            "creative_asset_generation": f"Generate creative assets for {campaign_config.get('campaign_type', 'display')} campaign. Create visual assets, video content, and multimedia elements aligned with brand guidelines and campaign objectives.",
            
            "landing_page_optimization": f"Optimize landing page for {campaign_config.get('landing_page_url', 'campaign landing page')}. Analyze page performance, user experience, conversion optimization, and provide actionable improvement recommendations.",
            
            "google_ads_api_integration": f"Implement Google Ads API integration for {campaign_config.get('campaign_name', 'the campaign')}. Set up campaign creation, management, and optimization automation using Google Ads API.",
            
            "conversion_tracking_setup": f"Set up comprehensive conversion tracking for {campaign_config.get('conversion_goals', [])}. Implement Google Analytics integration, conversion pixels, and attribution modeling.",
            
            "performance_monitoring": f"Establish performance monitoring system for {campaign_config.get('campaign_name', 'the campaign')}. Create dashboards, alerts, and automated reporting for key metrics and KPIs.",
            
            "optimization_recommendations": f"Analyze campaign performance and provide optimization recommendations. Focus on improving {campaign_config.get('primary_kpis', ['ROAS', 'CPA', 'CTR'])} based on current performance data.",
            
            "security_assessment": f"Conduct security assessment for Google Ads campaign setup. Verify data protection, access controls, API security, and compliance with privacy regulations.",
            
            "deployment": f"Deploy {campaign_config.get('campaign_name', 'the campaign')} to Google Ads platform. Execute launch sequence, verify tracking, and confirm all components are functioning correctly."
        }
        
        return base_descriptions.get(
            task_name,
            f"Execute {task_name} task for Google Ads campaign with focus on achieving campaign objectives and performance targets."
        )
    
    def _generate_expected_output(self, task_name: str) -> str:
        """Generate expected output description for a task."""
        expected_outputs = {
            "market_research": "Comprehensive market research report with industry analysis, target audience insights, competitive landscape overview, and strategic recommendations.",
            "competitor_analysis": "Detailed competitor analysis report with competitor profiles, strategy analysis, keyword gaps, and competitive positioning recommendations.",
            "keyword_research": "Complete keyword research report with primary/secondary keywords, search volumes, competition analysis, cost estimates, and negative keyword recommendations.",
            "audience_targeting": "Audience targeting strategy document with detailed persona definitions, targeting parameters, audience segments, and optimization recommendations.",
            "campaign_structure_design": "Campaign structure blueprint with hierarchy design, ad group organization, budget allocation, and setup guidelines.",
            "ad_copy_creation": "Collection of optimized ad copy variations including headlines, descriptions, and ad extensions with performance predictions.",
            "creative_asset_generation": "Set of creative assets including images, videos, graphics, and multimedia content optimized for campaign objectives.",
            "landing_page_optimization": "Landing page optimization report with analysis, recommendations, and implementation guidelines for conversion improvement.",
            "google_ads_api_integration": "Functional Google Ads API integration with automated campaign management capabilities and documentation.",
            "conversion_tracking_setup": "Complete conversion tracking implementation with analytics integration, pixel setup, and attribution configuration.",
            "performance_monitoring": "Performance monitoring dashboard with real-time metrics, alerts, and automated reporting system.",
            "optimization_recommendations": "Performance optimization report with specific recommendations, expected impact analysis, and implementation priorities.",
            "security_assessment": "Security assessment report with compliance verification, risk analysis, and security recommendations.",
            "deployment": "Successful campaign deployment with launch confirmation, tracking verification, and go-live report."
        }
        
        return expected_outputs.get(
            task_name,
            f"Completed {task_name} deliverable with detailed analysis and actionable recommendations."
        )
    
    async def execute_crew(self, crew_id: str) -> str:
        """
        Execute a Google Ads crew.
        
        Args:
            crew_id: ID of the crew to execute
            
        Returns:
            str: Execution ID
            
        Raises:
            AgentError: If crew cannot be executed
        """
        if crew_id not in self.crews:
            raise AgentError(f"Crew not found: {crew_id}")
        
        crew_execution = self.crew_executions[crew_id]
        crew = self.crews[crew_id]
        
        execution_id = str(uuid.uuid4())
        crew_execution.started_at = datetime.utcnow()
        crew_execution.status = WorkflowStatus.RUNNING
        
        self.logger.info(
            "Starting crew execution",
            crew_id=crew_id,
            execution_id=execution_id,
            workflow_type=crew_execution.metadata.get('workflow_type')
        )
        
        try:
            # Execute the crew
            if self.tracer:
                async with self.tracer.trace_operation(
                    operation_name=f"crew_execution_{crew_execution.metadata.get('workflow_type')}",
                    attributes={
                        "crew.id": crew_id,
                        "execution.id": execution_id,
                        "agents.count": len(crew.agents),
                        "tasks.count": len(crew.tasks)
                    }
                ) as span:
                    result = await self._execute_crew_async(crew, crew_execution, span)
            else:
                result = await self._execute_crew_async(crew, crew_execution)
            
            crew_execution.output_data = result
            crew_execution.status = WorkflowStatus.COMPLETED
            crew_execution.completed_at = datetime.utcnow()
            
            await self._emit_event("crew_completed", {
                "crew_id": crew_id,
                "execution_id": execution_id,
                "output_data": result,
                "duration": (crew_execution.completed_at - crew_execution.started_at).total_seconds()
            })
            
            self.logger.info(
                "Crew execution completed",
                crew_id=crew_id,
                execution_id=execution_id,
                duration=(crew_execution.completed_at - crew_execution.started_at).total_seconds()
            )
            
        except Exception as e:
            crew_execution.status = WorkflowStatus.FAILED
            crew_execution.completed_at = datetime.utcnow()
            crew_execution.error_log.append(f"Crew execution failed: {str(e)}")
            
            await self._emit_event("crew_failed", {
                "crew_id": crew_id,
                "execution_id": execution_id,
                "error": str(e)
            })
            
            self.logger.error(
                "Crew execution failed",
                crew_id=crew_id,
                execution_id=execution_id,
                error=str(e)
            )
            
        finally:
            self.running_crews.discard(crew_id)
            # Reduce agent workloads
            for agent_id in crew_execution.metadata.get('selected_agents', []):
                if agent_id in self.agent_workloads:
                    self.agent_workloads[agent_id] = max(0, self.agent_workloads[agent_id] - 1)
        
        return execution_id
    
    async def _execute_crew_async(
        self,
        crew: Crew,
        crew_execution: CrewExecution,
        span: Optional[Any] = None
    ) -> Dict[str, Any]:
        """
        Execute crew asynchronously.
        
        Args:
            crew: CrewAI Crew instance
            crew_execution: Crew execution tracking
            span: Optional tracing span
            
        Returns:
            Crew execution results
        """
        # Execute the crew using CrewAI
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None,
            crew.kickoff,
            crew_execution.input_data
        )
        
        # Process and structure the results
        structured_result = {
            "status": "completed",
            "raw_output": str(result),
            "tasks_completed": len(crew.tasks),
            "execution_time": (datetime.utcnow() - crew_execution.started_at).total_seconds(),
            "metadata": crew_execution.metadata
        }
        
        if hasattr(result, 'tasks_output'):
            structured_result["task_outputs"] = [
                {
                    "task_description": task.description,
                    "output": str(output),
                    "agent": output.agent if hasattr(output, 'agent') else None
                }
                for task, output in zip(crew.tasks, result.tasks_output)
            ]
        
        return structured_result
    
    def _crew_step_callback(self, step_output):
        """Callback for crew step execution."""
        self.logger.debug("Crew step completed", step_output=str(step_output))
    
    def _crew_task_callback(self, task_output):
        """Callback for crew task execution."""
        self.logger.debug("Crew task completed", task_output=str(task_output))
    
    async def _find_available_agent(self, agent_type: AgentType) -> Optional[BaseAiLexAgent]:
        """
        Find an available agent of the specified type.
        
        Args:
            agent_type: Required agent type
            
        Returns:
            Available agent or None
        """
        if agent_type not in self.agent_pools:
            return None
        
        # Find agent with lowest workload
        best_agent_id = None
        min_workload = float('inf')
        
        for agent_id in self.agent_pools[agent_type]:
            agent = self.agents[agent_id]
            workload = self.agent_workloads[agent_id]
            
            if (agent.status in [AgentStatus.IDLE, AgentStatus.ACTIVE] and 
                workload < min_workload):
                best_agent_id = agent_id
                min_workload = workload
        
        if best_agent_id:
            return self.agents[best_agent_id]
        
        return None
    
    async def get_crew_status(self, crew_id: str) -> Optional[CrewExecution]:
        """
        Get crew execution status.
        
        Args:
            crew_id: Crew ID
            
        Returns:
            Crew execution status or None if not found
        """
        return self.crew_executions.get(crew_id)
    
    async def cancel_crew(self, crew_id: str) -> bool:
        """
        Cancel crew execution.
        
        Args:
            crew_id: Crew ID
            
        Returns:
            True if cancelled successfully
        """
        if crew_id not in self.crew_executions:
            return False
        
        crew_execution = self.crew_executions[crew_id]
        crew_execution.status = WorkflowStatus.CANCELLED
        crew_execution.completed_at = datetime.utcnow()
        
        self.running_crews.discard(crew_id)
        
        # Reduce agent workloads
        for agent_id in crew_execution.metadata.get('selected_agents', []):
            if agent_id in self.agent_workloads:
                self.agent_workloads[agent_id] = max(0, self.agent_workloads[agent_id] - 1)
        
        await self._emit_event("crew_cancelled", {
            "crew_id": crew_id
        })
        
        self.logger.info("Crew execution cancelled", crew_id=crew_id)
        return True
    
    async def _emit_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """
        Emit event to registered callbacks.
        
        Args:
            event_type: Event type
            data: Event data
        """
        for callback in self.event_callbacks[event_type]:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(event_type, data)
                else:
                    callback(event_type, data)
            except Exception as e:
                self.logger.error("Event callback failed", event_type=event_type, error=str(e))
    
    def get_crew_metrics(self) -> Dict[str, Any]:
        """Get crew orchestrator metrics."""
        completed_crews = [
            e for e in self.crew_executions.values()
            if e.status == WorkflowStatus.COMPLETED
        ]
        failed_crews = [
            e for e in self.crew_executions.values()
            if e.status == WorkflowStatus.FAILED
        ]
        
        return {
            "total_crews": len(self.crews),
            "total_agents": len(self.agents),
            "total_executions": len(self.crew_executions),
            "running_crews": len(self.running_crews),
            "completed_crews": len(completed_crews),
            "failed_crews": len(failed_crews),
            "success_rate": (
                len(completed_crews) / len(self.crew_executions)
                if self.crew_executions else 0
            ),
            "agent_pools": {
                agent_type.value: len(agents)
                for agent_type, agents in self.agent_pools.items()
            },
            "agent_workloads": dict(self.agent_workloads)
        }

class FlowOrchestrator:
    """
    Main orchestrator for agent workflows and coordination.
    Manages workflow execution, agent coordination, and resource allocation.
    """
    
    def __init__(self, tracer: Optional[PhoenixTracer] = None):
        self.tracer = tracer
        self.workflows: Dict[str, WorkflowDefinition] = {}
        self.executions: Dict[str, WorkflowExecution] = {}
        self.agents: Dict[str, BaseAiLexAgent] = {}
        self.running_executions: Set[str] = set()
        
        # CrewAI integration
        self.crew_orchestrator = CrewOrchestrator(tracer)
        
        # Resource management
        self.agent_pools: Dict[AgentType, List[str]] = defaultdict(list)
        self.agent_workloads: Dict[str, int] = defaultdict(int)
        self.max_concurrent_executions = 10
        
        # Event system
        self.event_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        
        self.logger = structlog.get_logger().bind(component="flow_orchestrator")
    
    def register_workflow(self, workflow: WorkflowDefinition) -> None:
        """
        Register a workflow definition.
        
        Args:
            workflow: Workflow definition to register
        """
        self.workflows[workflow.id] = workflow
        self.logger.info(
            "Workflow registered",
            workflow_id=workflow.id,
            workflow_name=workflow.name,
            steps_count=len(workflow.steps)
        )
    
    def register_agent(self, agent: BaseAiLexAgent) -> None:
        """
        Register an agent with the orchestrator.
        
        Args:
            agent: Agent instance to register
        """
        self.agents[agent.agent_id] = agent
        self.agent_pools[agent.agent_type].append(agent.agent_id)
        self.agent_workloads[agent.agent_id] = 0
        
        # Also register with crew orchestrator
        self.crew_orchestrator.register_agent(agent)
        
        self.logger.info(
            "Agent registered",
            agent_id=agent.agent_id,
            agent_name=agent.name,
            agent_type=agent.agent_type.value
        )
    
    async def execute_workflow(
        self,
        workflow_id: str,
        input_data: Dict[str, Any],
        context: Optional[AgentContext] = None
    ) -> str:
        """
        Start workflow execution.
        
        Args:
            workflow_id: ID of the workflow to execute
            input_data: Input data for the workflow
            context: Execution context
            
        Returns:
            str: Execution ID
            
        Raises:
            AgentError: If workflow cannot be started
        """
        if workflow_id not in self.workflows:
            raise AgentError(f"Workflow not found: {workflow_id}")
        
        if len(self.running_executions) >= self.max_concurrent_executions:
            raise AgentError("Maximum concurrent executions reached")
        
        workflow = self.workflows[workflow_id]
        execution_id = str(uuid.uuid4())
        
        execution = WorkflowExecution(
            id=execution_id,
            workflow_id=workflow_id,
            status=WorkflowStatus.PENDING,
            created_at=datetime.utcnow(),
            context=context or AgentContext(),
            input_data=input_data
        )
        
        self.executions[execution_id] = execution
        self.running_executions.add(execution_id)
        
        self.logger.info(
            "Workflow execution started",
            execution_id=execution_id,
            workflow_id=workflow_id,
            workflow_name=workflow.name
        )
        
        # Start execution in background
        asyncio.create_task(self._execute_workflow_async(execution_id))
        
        await self._emit_event("workflow_started", {
            "execution_id": execution_id,
            "workflow_id": workflow_id,
            "input_data": input_data
        })
        
        return execution_id
    
    async def execute_google_ads_crew(
        self,
        workflow_type: str,
        campaign_config: Dict[str, Any],
        required_agents: Optional[List[AgentType]] = None
    ) -> str:
        """
        Execute a Google Ads workflow using CrewAI coordination.
        
        Args:
            workflow_type: Type of Google Ads workflow
            campaign_config: Campaign configuration data
            required_agents: Specific agents required for this workflow
            
        Returns:
            str: Crew execution ID
        """
        # Create the crew
        crew_id = await self.crew_orchestrator.create_google_ads_crew(
            workflow_type=workflow_type,
            campaign_config=campaign_config,
            required_agents=required_agents
        )
        
        # Execute the crew
        execution_id = await self.crew_orchestrator.execute_crew(crew_id)
        
        # Create workflow execution tracking that references the crew
        workflow_execution = WorkflowExecution(
            id=execution_id,
            workflow_id=workflow_type,
            status=WorkflowStatus.RUNNING,
            created_at=datetime.utcnow(),
            started_at=datetime.utcnow(),
            input_data=campaign_config,
            crew_execution=self.crew_orchestrator.crew_executions.get(crew_id)
        )
        
        self.executions[execution_id] = workflow_execution
        self.running_executions.add(execution_id)
        
        return execution_id
    
    async def get_google_ads_crew_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """
        Get status of a Google Ads crew execution.
        
        Args:
            execution_id: Execution ID
            
        Returns:
            Crew status information
        """
        execution = self.executions.get(execution_id)
        if not execution or not execution.crew_execution:
            return None
        
        crew_execution = execution.crew_execution
        return {
            "execution_id": execution_id,
            "crew_id": crew_execution.crew_id,
            "status": crew_execution.status.value,
            "workflow_type": crew_execution.metadata.get("workflow_type"),
            "agents": crew_execution.metadata.get("selected_agents", []),
            "started_at": crew_execution.started_at.isoformat() if crew_execution.started_at else None,
            "completed_at": crew_execution.completed_at.isoformat() if crew_execution.completed_at else None,
            "progress": {
                "completed_tasks": len(crew_execution.completed_tasks),
                "total_tasks": len(crew_execution.task_results),
                "current_task": crew_execution.current_task,
                "failed_tasks": len(crew_execution.failed_tasks)
            },
            "output_data": crew_execution.output_data,
            "errors": crew_execution.error_log
        }
    
    async def cancel_google_ads_crew(self, execution_id: str) -> bool:
        """
        Cancel a Google Ads crew execution.
        
        Args:
            execution_id: Execution ID
            
        Returns:
            True if cancelled successfully
        """
        execution = self.executions.get(execution_id)
        if not execution or not execution.crew_execution:
            return False
        
        # Cancel the crew
        success = await self.crew_orchestrator.cancel_crew(execution.crew_execution.crew_id)
        
        if success:
            execution.status = WorkflowStatus.CANCELLED
            execution.completed_at = datetime.utcnow()
            self.running_executions.discard(execution_id)
        
        return success
    
    async def _execute_workflow_async(self, execution_id: str) -> None:
        """
        Execute workflow asynchronously.
        
        Args:
            execution_id: Execution ID
        """
        execution = self.executions[execution_id]
        workflow = self.workflows[execution.workflow_id]
        
        try:
            execution.status = WorkflowStatus.RUNNING
            execution.started_at = datetime.utcnow()
            
            if self.tracer:
                async with self.tracer.trace_operation(
                    operation_name=f"workflow_execution_{workflow.name}",
                    attributes={
                        "workflow.id": workflow.id,
                        "workflow.name": workflow.name,
                        "execution.id": execution_id,
                        "steps.count": len(workflow.steps)
                    }
                ) as span:
                    await self._execute_workflow_steps(execution, workflow, span)
            else:
                await self._execute_workflow_steps(execution, workflow)
            
            execution.status = WorkflowStatus.COMPLETED
            execution.completed_at = datetime.utcnow()
            
            await self._emit_event("workflow_completed", {
                "execution_id": execution_id,
                "workflow_id": workflow.id,
                "output_data": execution.output_data,
                "duration": (execution.completed_at - execution.started_at).total_seconds()
            })
            
            self.logger.info(
                "Workflow execution completed",
                execution_id=execution_id,
                workflow_id=workflow.id,
                duration=(execution.completed_at - execution.started_at).total_seconds()
            )
            
        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.completed_at = datetime.utcnow()
            execution.error_log.append(f"Workflow failed: {str(e)}")
            
            await self._emit_event("workflow_failed", {
                "execution_id": execution_id,
                "workflow_id": workflow.id,
                "error": str(e)
            })
            
            self.logger.error(
                "Workflow execution failed",
                execution_id=execution_id,
                workflow_id=workflow.id,
                error=str(e)
            )
            
        finally:
            self.running_executions.discard(execution_id)
    
    async def _execute_workflow_steps(
        self,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        span: Optional[Any] = None
    ) -> None:
        """
        Execute workflow steps based on strategy.
        
        Args:
            execution: Workflow execution instance
            workflow: Workflow definition
            span: Optional tracing span
        """
        if workflow.strategy == ExecutionStrategy.SEQUENTIAL:
            await self._execute_sequential(execution, workflow, span)
        elif workflow.strategy == ExecutionStrategy.PARALLEL:
            await self._execute_parallel(execution, workflow, span)
        elif workflow.strategy == ExecutionStrategy.CONDITIONAL:
            await self._execute_conditional(execution, workflow, span)
        elif workflow.strategy == ExecutionStrategy.PIPELINE:
            await self._execute_pipeline(execution, workflow, span)
        elif workflow.strategy == ExecutionStrategy.HYBRID:
            await self._execute_hybrid(execution, workflow, span)
        else:
            raise AgentError(f"Unsupported execution strategy: {workflow.strategy}")
    
    async def _execute_sequential(
        self,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        span: Optional[Any] = None
    ) -> None:
        """Execute steps sequentially."""
        for step in workflow.steps:
            if execution.status != WorkflowStatus.RUNNING:
                break
                
            await self._execute_step(execution, step, span)
            
            if step.id in execution.failed_steps and workflow.error_handling == "stop":
                raise AgentError(f"Step {step.id} failed and error handling is set to stop")
    
    async def _execute_parallel(
        self,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        span: Optional[Any] = None
    ) -> None:
        """Execute steps in parallel."""
        tasks = []
        for step in workflow.steps:
            task = asyncio.create_task(self._execute_step(execution, step, span))
            tasks.append(task)
        
        # Wait for all tasks to complete
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _execute_conditional(
        self,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        span: Optional[Any] = None
    ) -> None:
        """Execute steps based on conditions."""
        for step in workflow.steps:
            if execution.status != WorkflowStatus.RUNNING:
                break
            
            # Check step conditions
            if await self._check_step_conditions(execution, step):
                await self._execute_step(execution, step, span)
    
    async def _execute_pipeline(
        self,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        span: Optional[Any] = None
    ) -> None:
        """Execute steps in pipeline mode with data flow."""
        # Build dependency graph
        dependency_graph = self._build_dependency_graph(workflow.steps)
        
        # Execute in topological order
        executed = set()
        while len(executed) < len(workflow.steps):
            ready_steps = [
                step for step in workflow.steps
                if step.id not in executed and all(dep in executed for dep in step.dependencies)
            ]
            
            if not ready_steps:
                break
            
            # Execute ready steps in parallel
            tasks = []
            for step in ready_steps:
                task = asyncio.create_task(self._execute_step(execution, step, span))
                tasks.append((step.id, task))
            
            # Wait for completion
            for step_id, task in tasks:
                await task
                executed.add(step_id)
    
    async def _execute_hybrid(
        self,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        span: Optional[Any] = None
    ) -> None:
        """Execute with hybrid strategy combining other strategies."""
        # For now, use pipeline strategy as default hybrid approach
        await self._execute_pipeline(execution, workflow, span)
    
    async def _execute_step(
        self,
        execution: WorkflowExecution,
        step: WorkflowStep,
        span: Optional[Any] = None
    ) -> None:
        """
        Execute a single workflow step.
        
        Args:
            execution: Workflow execution
            step: Step to execute
            span: Optional tracing span
        """
        step_start_time = datetime.utcnow()
        execution.current_step = step.id
        
        try:
            self.logger.info(
                "Executing workflow step",
                execution_id=execution.id,
                step_id=step.id,
                step_name=step.name,
                agent_type=step.agent_type.value
            )
            
            # Find available agent
            agent = await self._find_available_agent(step.agent_type)
            if not agent:
                raise AgentError(f"No available agent for type: {step.agent_type}")
            
            # Prepare step input data
            step_input = await self._prepare_step_input(execution, step)
            
            # Create task
            task = Task(
                description=step.description,
                expected_output=step.output_schema.get("description", "Task output"),
                agent=agent._crew_agent if hasattr(agent, '_crew_agent') else None,
            )
            
            # Execute step with timeout
            try:
                step_result = await asyncio.wait_for(
                    agent.execute_task(task, execution.context),
                    timeout=step.timeout_seconds
                )
                
                execution.step_results[step.id] = step_result
                execution.completed_steps.append(step.id)
                
                # Update agent workload
                self.agent_workloads[agent.agent_id] -= 1
                
                step_duration = (datetime.utcnow() - step_start_time).total_seconds()
                
                self.logger.info(
                    "Workflow step completed",
                    execution_id=execution.id,
                    step_id=step.id,
                    agent_id=agent.agent_id,
                    duration=step_duration
                )
                
                if span:
                    span.add_event(f"step_completed_{step.id}", {
                        "step.name": step.name,
                        "step.duration": step_duration,
                        "agent.id": agent.agent_id
                    })
                
            except asyncio.TimeoutError:
                raise AgentError(f"Step {step.id} timed out after {step.timeout_seconds} seconds")
            
        except Exception as e:
            execution.failed_steps.append(step.id)
            execution.error_log.append(f"Step {step.id} failed: {str(e)}")
            
            # Handle retries
            retry_count = execution.retry_counts.get(step.id, 0)
            if retry_count < step.retry_attempts:
                execution.retry_counts[step.id] = retry_count + 1
                self.logger.warning(
                    "Retrying failed step",
                    execution_id=execution.id,
                    step_id=step.id,
                    retry_count=retry_count + 1,
                    max_retries=step.retry_attempts
                )
                await asyncio.sleep(2 ** retry_count)  # Exponential backoff
                await self._execute_step(execution, step, span)
            else:
                self.logger.error(
                    "Workflow step failed after retries",
                    execution_id=execution.id,
                    step_id=step.id,
                    error=str(e),
                    retry_count=retry_count
                )
                raise
        
        finally:
            execution.current_step = None
    
    async def _find_available_agent(self, agent_type: AgentType) -> Optional[BaseAiLexAgent]:
        """
        Find an available agent of the specified type.
        
        Args:
            agent_type: Required agent type
            
        Returns:
            Available agent or None
        """
        if agent_type not in self.agent_pools:
            return None
        
        # Find agent with lowest workload
        best_agent_id = None
        min_workload = float('inf')
        
        for agent_id in self.agent_pools[agent_type]:
            agent = self.agents[agent_id]
            workload = self.agent_workloads[agent_id]
            
            if (agent.status in [AgentStatus.IDLE, AgentStatus.ACTIVE] and 
                workload < min_workload):
                best_agent_id = agent_id
                min_workload = workload
        
        if best_agent_id:
            self.agent_workloads[best_agent_id] += 1
            return self.agents[best_agent_id]
        
        return None
    
    async def _prepare_step_input(
        self,
        execution: WorkflowExecution,
        step: WorkflowStep
    ) -> Dict[str, Any]:
        """
        Prepare input data for a workflow step.
        
        Args:
            execution: Workflow execution
            step: Workflow step
            
        Returns:
            Input data for the step
        """
        step_input = execution.input_data.copy()
        
        # Add results from dependency steps
        for dep_step_id in step.dependencies:
            if dep_step_id in execution.step_results:
                step_input[f"{dep_step_id}_result"] = execution.step_results[dep_step_id]
        
        # Add metadata
        step_input["_step_metadata"] = {
            "step_id": step.id,
            "step_name": step.name,
            "execution_id": execution.id,
            "workflow_id": execution.workflow_id
        }
        
        return step_input
    
    async def _check_step_conditions(
        self,
        execution: WorkflowExecution,
        step: WorkflowStep
    ) -> bool:
        """
        Check if step conditions are met.
        
        Args:
            execution: Workflow execution
            step: Workflow step
            
        Returns:
            True if conditions are met
        """
        if not step.conditions:
            return True
        
        # Simple condition evaluation (can be extended)
        for condition in step.conditions:
            if not await self._evaluate_condition(execution, condition):
                return False
        
        return True
    
    async def _evaluate_condition(self, execution: WorkflowExecution, condition: str) -> bool:
        """
        Evaluate a single condition.
        
        Args:
            execution: Workflow execution
            condition: Condition string
            
        Returns:
            True if condition is met
        """
        # Simple condition evaluation - can be extended with a proper expression parser
        try:
            # Example: "step1_result.success == true"
            # For now, just check if referenced steps completed successfully
            if "completed" in condition.lower():
                step_id = condition.split(".")[0].replace("_result", "")
                return step_id in execution.completed_steps
            
            return True
        except Exception:
            return False
    
    def _build_dependency_graph(self, steps: List[WorkflowStep]) -> Dict[str, List[str]]:
        """
        Build dependency graph for steps.
        
        Args:
            steps: List of workflow steps
            
        Returns:
            Dependency graph
        """
        graph = {}
        for step in steps:
            graph[step.id] = step.dependencies.copy()
        return graph
    
    async def get_execution_status(self, execution_id: str) -> Optional[WorkflowExecution]:
        """
        Get workflow execution status.
        
        Args:
            execution_id: Execution ID
            
        Returns:
            Execution status or None if not found
        """
        return self.executions.get(execution_id)
    
    async def cancel_execution(self, execution_id: str) -> bool:
        """
        Cancel workflow execution.
        
        Args:
            execution_id: Execution ID
            
        Returns:
            True if cancelled successfully
        """
        if execution_id not in self.executions:
            return False
        
        execution = self.executions[execution_id]
        execution.status = WorkflowStatus.CANCELLED
        execution.completed_at = datetime.utcnow()
        
        self.running_executions.discard(execution_id)
        
        await self._emit_event("workflow_cancelled", {
            "execution_id": execution_id,
            "workflow_id": execution.workflow_id
        })
        
        self.logger.info("Workflow execution cancelled", execution_id=execution_id)
        return True
    
    async def pause_execution(self, execution_id: str) -> bool:
        """
        Pause workflow execution.
        
        Args:
            execution_id: Execution ID
            
        Returns:
            True if paused successfully
        """
        if execution_id not in self.executions:
            return False
        
        execution = self.executions[execution_id]
        if execution.status == WorkflowStatus.RUNNING:
            execution.status = WorkflowStatus.PAUSED
            
            await self._emit_event("workflow_paused", {
                "execution_id": execution_id,
                "workflow_id": execution.workflow_id
            })
            
            self.logger.info("Workflow execution paused", execution_id=execution_id)
            return True
        
        return False
    
    async def resume_execution(self, execution_id: str) -> bool:
        """
        Resume paused workflow execution.
        
        Args:
            execution_id: Execution ID
            
        Returns:
            True if resumed successfully
        """
        if execution_id not in self.executions:
            return False
        
        execution = self.executions[execution_id]
        if execution.status == WorkflowStatus.PAUSED:
            execution.status = WorkflowStatus.RUNNING
            
            await self._emit_event("workflow_resumed", {
                "execution_id": execution_id,
                "workflow_id": execution.workflow_id
            })
            
            self.logger.info("Workflow execution resumed", execution_id=execution_id)
            return True
        
        return False
    
    def add_event_callback(self, event_type: str, callback: Callable) -> None:
        """
        Add event callback.
        
        Args:
            event_type: Event type to listen for
            callback: Callback function
        """
        self.event_callbacks[event_type].append(callback)
    
    async def _emit_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """
        Emit event to registered callbacks.
        
        Args:
            event_type: Event type
            data: Event data
        """
        for callback in self.event_callbacks[event_type]:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(event_type, data)
                else:
                    callback(event_type, data)
            except Exception as e:
                self.logger.error("Event callback failed", event_type=event_type, error=str(e))
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get orchestrator metrics including CrewAI crew metrics."""
        completed_executions = [
            e for e in self.executions.values()
            if e.status == WorkflowStatus.COMPLETED
        ]
        failed_executions = [
            e for e in self.executions.values()
            if e.status == WorkflowStatus.FAILED
        ]
        
        # Get crew metrics
        crew_metrics = self.crew_orchestrator.get_crew_metrics()
        
        return {
            "total_workflows": len(self.workflows),
            "total_agents": len(self.agents),
            "total_executions": len(self.executions),
            "running_executions": len(self.running_executions),
            "completed_executions": len(completed_executions),
            "failed_executions": len(failed_executions),
            "success_rate": (
                len(completed_executions) / len(self.executions)
                if self.executions else 0
            ),
            "agent_pools": {
                agent_type.value: len(agents)
                for agent_type, agents in self.agent_pools.items()
            },
            "agent_workloads": dict(self.agent_workloads),
            "crew_metrics": crew_metrics
        }


class AgentCommunicator:
    """
    Handles communication between agents in the orchestration system.
    """
    
    def __init__(self, orchestrator: FlowOrchestrator):
        self.orchestrator = orchestrator
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.message_handlers: Dict[str, Callable] = {}
        self.running = False
        
        self.logger = structlog.get_logger().bind(component="agent_communicator")
    
    async def start(self) -> None:
        """Start the communicator service."""
        self.running = True
        asyncio.create_task(self._process_messages())
        self.logger.info("Agent communicator started")
    
    async def stop(self) -> None:
        """Stop the communicator service."""
        self.running = False
        self.logger.info("Agent communicator stopped")
    
    async def _process_messages(self) -> None:
        """Process messages from the queue."""
        while self.running:
            try:
                # Wait for message with timeout
                message = await asyncio.wait_for(
                    self.message_queue.get(),
                    timeout=1.0
                )
                
                await self._handle_message(message)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error("Error processing message", error=str(e))
    
    async def _handle_message(self, message: AgentMessage) -> None:
        """
        Handle a message.
        
        Args:
            message: Message to handle
        """
        try:
            # Find recipient agent
            recipient_agent = self.orchestrator.agents.get(message.recipient_id)
            if not recipient_agent:
                self.logger.warning(
                    "Message recipient not found",
                    recipient_id=message.recipient_id,
                    message_id=message.id
                )
                return
            
            # Deliver message to agent
            response = await recipient_agent.handle_message(message)
            
            # Handle response if provided
            if response and message.requires_response:
                await self.send_message(response)
            
            self.logger.debug(
                "Message delivered",
                message_id=message.id,
                sender_id=message.sender_id,
                recipient_id=message.recipient_id,
                has_response=bool(response)
            )
            
        except Exception as e:
            self.logger.error(
                "Failed to handle message",
                message_id=message.id,
                error=str(e)
            )
    
    async def send_message(self, message: AgentMessage) -> None:
        """
        Send a message.
        
        Args:
            message: Message to send
        """
        await self.message_queue.put(message)
        
        self.logger.debug(
            "Message queued",
            message_id=message.id,
            sender_id=message.sender_id,
            recipient_id=message.recipient_id,
            message_type=message.message_type
        )
    
    async def broadcast_message(
        self,
        sender_id: str,
        message_type: str,
        content: Dict[str, Any],
        agent_filter: Optional[Callable[[BaseAiLexAgent], bool]] = None
    ) -> None:
        """
        Broadcast message to multiple agents.
        
        Args:
            sender_id: Sender agent ID
            message_type: Message type
            content: Message content
            agent_filter: Optional filter function for recipients
        """
        recipients = self.orchestrator.agents.values()
        
        if agent_filter:
            recipients = [agent for agent in recipients if agent_filter(agent)]
        
        for agent in recipients:
            if agent.agent_id != sender_id:  # Don't send to self
                message = AgentMessage(
                    sender_id=sender_id,
                    recipient_id=agent.agent_id,
                    message_type=message_type,
                    content=content
                )
                await self.send_message(message)
        
        self.logger.info(
            "Message broadcasted",
            sender_id=sender_id,
            message_type=message_type,
            recipient_count=len([a for a in recipients if a.agent_id != sender_id])
        )
    
    def register_message_handler(self, message_type: str, handler: Callable) -> None:
        """
        Register a message handler.
        
        Args:
            message_type: Message type to handle
            handler: Handler function
        """
        self.message_handlers[message_type] = handler
        self.logger.debug("Message handler registered", message_type=message_type)
    
    async def request_response(
        self,
        sender_id: str,
        recipient_id: str,
        message_type: str,
        content: Dict[str, Any],
        timeout_seconds: int = 30
    ) -> Optional[AgentMessage]:
        """
        Send message and wait for response.
        
        Args:
            sender_id: Sender agent ID
            recipient_id: Recipient agent ID
            message_type: Message type
            content: Message content
            timeout_seconds: Response timeout
            
        Returns:
            Response message or None if timeout
        """
        correlation_id = str(uuid.uuid4())
        
        message = AgentMessage(
            sender_id=sender_id,
            recipient_id=recipient_id,
            message_type=message_type,
            content=content,
            requires_response=True,
            correlation_id=correlation_id
        )
        
        # Create response future
        response_future = asyncio.Future()
        response_handler = lambda msg: response_future.set_result(msg) if msg.correlation_id == correlation_id else None
        
        # Register temporary handler
        original_handler = self.message_handlers.get(f"{message_type}_response")
        self.message_handlers[f"{message_type}_response"] = response_handler
        
        try:
            await self.send_message(message)
            response = await asyncio.wait_for(response_future, timeout=timeout_seconds)
            return response
            
        except asyncio.TimeoutError:
            self.logger.warning(
                "Request-response timeout",
                sender_id=sender_id,
                recipient_id=recipient_id,
                correlation_id=correlation_id
            )
            return None
            
        finally:
            # Restore original handler
            if original_handler:
                self.message_handlers[f"{message_type}_response"] = original_handler
            else:
                self.message_handlers.pop(f"{message_type}_response", None)