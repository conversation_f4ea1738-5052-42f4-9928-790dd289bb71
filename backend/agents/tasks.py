"""
Celery tasks for AI agent orchestration and campaign management.
These tasks handle heavy AI processing, agent workflows, and campaign operations.
"""

import asyncio
from typing import Dict, Any, List, Optional
from celery import Task
from celery.utils.log import get_task_logger

from celery_config import celery_app
from .orchestrator import campaign_orchestrator, CampaignRequest
from .orchestration import FlowOrchestrator
from .workflows import WorkflowType
from models.agents import AgentType, AgentConfig
from models.campaigns import CampaignStatus
from services.database import database_service
from services.google_ads import google_ads_service
from agents.base import AgentError
from utils.exceptions import CampaignException

logger = get_task_logger(__name__)


class AsyncTask(Task):
    """Base task class that supports async operations."""
    
    def __call__(self, *args, **kwargs):
        """Execute async task in event loop."""
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        return loop.run_until_complete(self.run_async(*args, **kwargs))
    
    async def run_async(self, *args, **kwargs):
        """Override this method for async task implementation."""
        raise NotImplementedError


@celery_app.task(bind=True, base=AsyncTask, name="agents.create_campaign")
async def create_campaign_task(self, campaign_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a new Google Ads campaign using AI agents.
    
    Args:
        campaign_data: Campaign configuration and requirements
        
    Returns:
        dict: Campaign creation results and metadata
    """
    try:
        logger.info(f"Starting campaign creation task: {campaign_data.get('name', 'Unknown')}")
        
        # Create campaign request
        request = CampaignRequest(
            business_description=campaign_data["business_description"],
            campaign_objectives=campaign_data["campaign_objectives"],
            target_audience=campaign_data["target_audience"],
            budget=campaign_data["budget"],
            industry=campaign_data.get("industry", ""),
            location=campaign_data.get("location", ""),
            duration_days=campaign_data.get("duration_days", 30)
        )
        
        # Execute campaign orchestration
        result = await campaign_orchestrator.create_campaign(request)
        
        logger.info(f"Campaign creation completed: {result.campaign_id}")
        return {
            "status": "success",
            "campaign_id": result.campaign_id,
            "campaign_name": result.campaign_name,
            "ad_groups": len(result.ad_groups),
            "keywords": len(result.keywords),
            "ads": len(result.ads),
            "estimated_performance": result.estimated_performance
        }
        
    except Exception as e:
        logger.error(f"Campaign creation failed: {str(e)}")
        raise self.retry(exc=e, countdown=60, max_retries=3)


@celery_app.task(bind=True, base=AsyncTask, name="agents.optimize_campaign")
async def optimize_campaign_task(self, campaign_id: str, optimization_type: str = "performance") -> Dict[str, Any]:
    """
    Optimize an existing campaign using AI agents.
    
    Args:
        campaign_id: Google Ads campaign ID
        optimization_type: Type of optimization (performance, budget, keywords)
        
    Returns:
        dict: Optimization results and recommendations
    """
    try:
        logger.info(f"Starting campaign optimization: {campaign_id}")
        
        # Execute optimization workflow
        result = await campaign_orchestrator.optimize_campaign(
            campaign_id=campaign_id,
            optimization_type=optimization_type
        )
        
        logger.info(f"Campaign optimization completed: {campaign_id}")
        return {
            "status": "success",
            "campaign_id": campaign_id,
            "optimization_type": optimization_type,
            "improvements": result.improvements,
            "estimated_impact": result.estimated_impact,
            "recommendations": result.recommendations
        }
        
    except Exception as e:
        logger.error(f"Campaign optimization failed: {str(e)}")
        raise self.retry(exc=e, countdown=120, max_retries=2)


@celery_app.task(bind=True, base=AsyncTask, name="agents.analyze_performance")
async def analyze_performance_task(self, campaign_ids: List[str], date_range: Dict[str, str]) -> Dict[str, Any]:
    """
    Analyze campaign performance using AI agents.
    
    Args:
        campaign_ids: List of campaign IDs to analyze
        date_range: Date range for analysis (start_date, end_date)
        
    Returns:
        dict: Performance analysis results
    """
    try:
        logger.info(f"Starting performance analysis for {len(campaign_ids)} campaigns")
        
        # Execute performance analysis workflow
        result = await campaign_orchestrator.analyze_performance(
            campaign_ids=campaign_ids,
            start_date=date_range["start_date"],
            end_date=date_range["end_date"]
        )
        
        logger.info(f"Performance analysis completed for {len(campaign_ids)} campaigns")
        return {
            "status": "success",
            "campaigns_analyzed": len(campaign_ids),
            "analysis_period": date_range,
            "performance_metrics": result.metrics,
            "insights": result.insights,
            "recommendations": result.recommendations
        }
        
    except Exception as e:
        logger.error(f"Performance analysis failed: {str(e)}")
        raise self.retry(exc=e, countdown=180, max_retries=2)


@celery_app.task(bind=True, base=AsyncTask, name="agents.generate_ad_assets")
async def generate_ad_assets_task(self, campaign_config: Dict[str, Any], asset_types: List[str]) -> Dict[str, Any]:
    """
    Generate ad assets (headlines, descriptions, images) using AI agents.
    
    Args:
        campaign_config: Campaign configuration
        asset_types: Types of assets to generate
        
    Returns:
        dict: Generated ad assets
    """
    try:
        logger.info(f"Starting ad asset generation for types: {asset_types}")
        
        # Execute asset generation workflow
        result = await campaign_orchestrator.generate_ad_assets(
            campaign_config=campaign_config,
            asset_types=asset_types
        )
        
        logger.info(f"Ad asset generation completed: {len(result.assets)} assets")
        return {
            "status": "success",
            "asset_types": asset_types,
            "assets_generated": len(result.assets),
            "assets": result.assets,
            "quality_scores": result.quality_scores,
            "recommendations": result.recommendations
        }
        
    except Exception as e:
        logger.error(f"Ad asset generation failed: {str(e)}")
        raise self.retry(exc=e, countdown=90, max_retries=3)


@celery_app.task(bind=True, base=AsyncTask, name="agents.research_keywords")
async def research_keywords_task(self, business_info: Dict[str, Any], target_audience: Dict[str, Any]) -> Dict[str, Any]:
    """
    Research keywords using AI agents.
    
    Args:
        business_info: Business description and industry
        target_audience: Target audience information
        
    Returns:
        dict: Keyword research results
    """
    try:
        logger.info("Starting keyword research task")
        
        # Execute keyword research workflow
        result = await campaign_orchestrator.research_keywords(
            business_info=business_info,
            target_audience=target_audience
        )
        
        logger.info(f"Keyword research completed: {len(result.keywords)} keywords")
        return {
            "status": "success",
            "keywords_found": len(result.keywords),
            "keywords": result.keywords,
            "keyword_groups": result.keyword_groups,
            "search_volumes": result.search_volumes,
            "competition_analysis": result.competition_analysis
        }
        
    except Exception as e:
        logger.error(f"Keyword research failed: {str(e)}")
        raise self.retry(exc=e, countdown=60, max_retries=3)


@celery_app.task(bind=True, name="agents.health_check")
def agent_health_check_task(self) -> Dict[str, Any]:
    """
    Health check task for agent system.
    
    Returns:
        dict: Health status of agent system
    """
    try:
        logger.info("Running agent health check")
        
        # Check agent availability
        available_agents = {}
        for agent_type in AgentType:
            try:
                # This would check if agents are available and responsive
                available_agents[agent_type.value] = True
            except Exception:
                available_agents[agent_type.value] = False
        
        all_healthy = all(available_agents.values())
        
        return {
            "status": "healthy" if all_healthy else "degraded",
            "timestamp": asyncio.get_event_loop().time(),
            "agent_availability": available_agents,
            "total_agents": len(AgentType),
            "healthy_agents": sum(available_agents.values())
        }
        
    except Exception as e:
        logger.error(f"Agent health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": asyncio.get_event_loop().time()
        }


# Task routing is configured in celery_config.py
