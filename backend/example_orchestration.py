"""
Example script demonstrating the AiLex Multi-Agent Campaign Orchestration System.
Shows how multiple AI agents collaborate to create a complete Google Ads campaign.
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any

from agents.orchestrator import campaign_orchestrator, CampaignRequest
from utils.config import settings
import structlog

# Set up logging
logger = structlog.get_logger(__name__)


async def run_example_campaign_creation():
    """
    Demonstrates a complete campaign creation workflow using multiple agents.
    """
    logger.info("Starting AiLex Multi-Agent Campaign Orchestration Example")
    
    try:
        # Initialize the orchestrator
        logger.info("Initializing campaign orchestrator...")
        await campaign_orchestrator.initialize()
        
        # Check orchestrator status
        status_data = await campaign_orchestrator.get_orchestrator_status()
        logger.info("Orchestrator status", **status_data)
        
        # Example campaign request for a law firm
        campaign_request = CampaignRequest(
            business_description="AiLex is a modern legal technology company that provides AI-powered legal research, document analysis, and case management solutions for law firms and legal professionals.",
            industry="Legal Technology",
            target_audience="Small to medium-sized law firms, solo practitioners, and legal professionals who want to leverage AI to improve efficiency and outcomes.",
            campaign_objectives=[
                "Generate qualified leads",
                "Increase brand awareness in legal tech space",
                "Drive trial sign-ups for AI legal tools"
            ],
            budget=5000.0,  # $5,000 campaign budget
            duration_days=30,  # 30-day campaign
            location="United States",
            additional_requirements={
                "user_id": "demo_user",
                "compliance_requirements": ["Legal advertising compliance", "State bar regulations"],
                "preferred_channels": ["Google Search", "LinkedIn", "Legal publications"]
            }
        )
        
        logger.info("Creating campaign workflow for AiLex legal tech campaign...")
        
        # Step 1: Create the workflow
        workflow = await campaign_orchestrator.create_campaign_workflow(campaign_request)
        
        logger.info("Campaign workflow created", 
                   workflow_id=workflow.workflow_id,
                   tasks_count=len(workflow.tasks))
        
        # Display workflow details
        print("\n" + "="*80)
        print("CAMPAIGN WORKFLOW CREATED")
        print("="*80)
        print(f"Workflow ID: {workflow.workflow_id}")
        print(f"Name: {workflow.name}")
        print(f"Description: {workflow.description}")
        print(f"Status: {workflow.status.value}")
        print(f"Total Tasks: {len(workflow.tasks)}")
        print("\nTasks to be executed:")
        for i, task in enumerate(workflow.tasks, 1):
            print(f"  {i}. {task.name}")
            print(f"     Agent: {task.agent_type.value}")
            print(f"     Dependencies: {task.dependencies or 'None'}")
            print(f"     Status: {task.status.value}")
            print()
        
        # Step 2: Execute the workflow
        print("="*80)
        print("EXECUTING WORKFLOW - MULTI-AGENT COLLABORATION")
        print("="*80)
        print("Agents will now collaborate to create a complete campaign...")
        print("This demonstrates:")
        print("- Market research and strategy (Campaign Planning Agent)")
        print("- Audience analysis and segmentation (Audience Targeting Agent)") 
        print("- Creative asset generation using Gemini AI (Ad Asset Generation Agent)")
        print("- Budget allocation and optimization (Budget Management Agent)")
        print("- Campaign validation and recommendations (Performance Analysis Agent)")
        print()
        
        # Execute the workflow
        logger.info("Executing workflow - agents will collaborate...")
        completed_workflow = await campaign_orchestrator.execute_workflow(workflow.workflow_id)
        
        # Step 3: Display results
        print("="*80)
        print("WORKFLOW EXECUTION COMPLETED")
        print("="*80)
        print(f"Status: {completed_workflow.status.value}")
        print(f"Total Execution Time: {completed_workflow.total_execution_time:.2f} seconds")
        print(f"Started: {completed_workflow.started_at}")
        print(f"Completed: {completed_workflow.completed_at}")
        print()
        
        # Show task execution summary
        print("TASK EXECUTION SUMMARY:")
        print("-" * 40)
        successful_tasks = 0
        failed_tasks = 0
        
        for task in completed_workflow.tasks:
            status_icon = "✅" if task.status.value == "completed" else "❌" if task.status.value == "failed" else "⏳"
            print(f"{status_icon} {task.name}")
            print(f"   Agent: {task.agent_type.value}")
            print(f"   Status: {task.status.value}")
            print(f"   Execution Time: {task.execution_time:.2f}s")
            if task.error_message:
                print(f"   Error: {task.error_message}")
            print()
            
            if task.status.value == "completed":
                successful_tasks += 1
            elif task.status.value == "failed":
                failed_tasks += 1
        
        print(f"Success Rate: {successful_tasks}/{len(completed_workflow.tasks)} tasks completed successfully")
        print()
        
        # Show workflow results
        print("AGENT COLLABORATION RESULTS:")
        print("-" * 40)
        
        for task_name, result in completed_workflow.results.items():
            print(f"\n📊 {task_name.upper()}:")
            if isinstance(result, dict):
                if 'output' in result:
                    output = result['output']
                    if isinstance(output, str):
                        # Truncate very long outputs
                        display_output = output[:500] + "..." if len(output) > 500 else output
                        print(f"   Result: {display_output}")
                    else:
                        print(f"   Result: {type(output).__name__} with {len(str(output))} characters")
                else:
                    print(f"   Result: {json.dumps(result, indent=2)[:200]}...")
            else:
                print(f"   Result: {str(result)[:200]}...")
            print()
        
        # Demonstrate agent status checking
        print("="*80)
        print("AGENT STATUS CHECK")
        print("="*80)
        
        from models.agents import AgentType
        agent_types = [
            AgentType.CAMPAIGN_PLANNING,
            AgentType.AUDIENCE_TARGETING,
            AgentType.AD_ASSET_GENERATION,
            AgentType.BUDGET_MANAGEMENT,
            AgentType.PERFORMANCE_ANALYSIS
        ]
        
        for agent_type in agent_types:
            try:
                agent_status = await campaign_orchestrator.get_agent_status(agent_type)
                if agent_status:
                    print(f"🤖 {agent_type.value}:")
                    print(f"   Status: {agent_status['status'].value}")
                    print(f"   Tasks Completed: {agent_status['performance_metrics']['tasks_completed']}")
                    print(f"   Success Rate: {agent_status['performance_metrics']['success_rate']:.1%}")
                    print()
            except Exception as e:
                print(f"❌ {agent_type.value}: Status check failed - {str(e)}")
        
        # Final summary
        print("="*80)
        print("EXAMPLE COMPLETION SUMMARY")
        print("="*80)
        print("✅ Multi-agent orchestration system successfully demonstrated!")
        print("✅ Campaign workflow created with 5 specialized AI agents")
        print("✅ Agents collaborated to produce comprehensive campaign strategy")
        print("✅ Gemini AI integration working for content generation")
        print("✅ All agents coordinated through central orchestrator")
        print()
        print("The system shows:")
        print("- Agent specialization (each agent has specific expertise)")
        print("- Task dependencies and coordination")
        print("- Error handling and fallback mechanisms") 
        print("- Performance tracking and monitoring")
        print("- Integration with Google's Gemini AI for content generation")
        print()
        print("This demonstrates a production-ready AI agent orchestration system")
        print("suitable for automated Google Ads campaign creation at scale.")
        
        logger.info("Example completed successfully")
        return completed_workflow
        
    except Exception as e:
        logger.error("Example execution failed", error=str(e))
        print(f"\n❌ Error during example execution: {str(e)}")
        raise


async def run_simple_agent_test():
    """
    Simple test to verify individual agent functionality.
    """
    logger.info("Running simple agent functionality test")
    
    try:
        # Test Gemini service directly
        from services.gemini_service import gemini_service
        
        if not settings.GEMINI_API_KEY:
            print("❌ GEMINI_API_KEY not configured - skipping Gemini test")
            return False
        
        print("Testing Gemini AI service...")
        
        # Test basic text generation
        response = await gemini_service.generate_text(
            prompt="Generate a brief, professional headline for a legal technology company that helps lawyers with AI-powered research. Keep it under 30 characters.",
            temperature=0.7,
            max_tokens=100
        )
        
        print(f"✅ Gemini AI Response: {response}")
        
        # Test ad copy generation
        ad_copy = await gemini_service.generate_ad_copy(
            business_description="AI-powered legal research platform",
            target_keywords=["legal research", "AI lawyer tools", "case analysis"],
            target_audience="Small law firms and solo practitioners",
            ad_type="search",
            num_variations=2
        )
        
        print(f"✅ Ad Copy Generation: {len(ad_copy)} variations created")
        for i, variation in enumerate(ad_copy, 1):
            print(f"   Variation {i}: {len(variation.get('headlines', []))} headlines, {len(variation.get('descriptions', []))} descriptions")
        
        return True
        
    except Exception as e:
        logger.error("Simple agent test failed", error=str(e))
        print(f"❌ Agent test failed: {str(e)}")
        return False


async def main():
    """
    Main function to run the orchestration examples.
    """
    print("🚀 AiLex AI Agent Orchestration System - Live Demo")
    print("=" * 80)
    print()
    
    # Check configuration
    if not settings.GEMINI_API_KEY:
        print("⚠️  Warning: GEMINI_API_KEY not configured")
        print("   The system will use fallback responses instead of live AI generation")
        print("   For full functionality, configure your Gemini API key")
        print()
    else:
        print("✅ Gemini API key configured - full AI functionality available")
        print()
    
    # Run simple test first
    print("Step 1: Testing individual agent components...")
    test_success = await run_simple_agent_test()
    print()
    
    if test_success or not settings.GEMINI_API_KEY:
        print("Step 2: Running complete multi-agent campaign orchestration...")
        print()
        workflow = await run_example_campaign_creation()
        
        if workflow:
            print("\n🎉 Demo completed successfully!")
            print("\nNext steps:")
            print("- Use the API endpoints to integrate with frontend applications")
            print("- Monitor agent performance through the provided metrics")
            print("- Customize agent configurations for specific use cases")
            print("- Scale the system by adding more specialized agents")
    else:
        print("❌ Skipping full orchestration due to component test failure")


if __name__ == "__main__":
    asyncio.run(main())