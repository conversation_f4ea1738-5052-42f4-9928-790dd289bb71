[mypy]
# Basic configuration
python_version = 3.11
warn_return_any = True
warn_unused_configs = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_unreachable = True

# Strictness settings
disallow_any_generics = True
disallow_untyped_calls = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
disallow_untyped_decorators = True
disallow_subclassing_any = True

# Error handling
strict_optional = True
no_implicit_optional = True
strict_equality = True

# Import discovery
namespace_packages = True
explicit_package_bases = True

# Error reporting
show_error_context = True
show_column_numbers = True
show_error_codes = True
pretty = True

# Performance
cache_dir = .mypy_cache
sqlite_cache = True

# Exclude patterns
exclude = tests/.*|migrations/.*|venv/.*|\.venv/.*

# Third-party library configurations
[mypy-crewai.*]
ignore_missing_imports = True

[mypy-phoenix_trace.*]
ignore_missing_imports = True

[mypy-google.*]
ignore_missing_imports = True

[mypy-openai.*]
ignore_missing_imports = True

[mypy-supabase.*]
ignore_missing_imports = True

[mypy-pinecone.*]
ignore_missing_imports = True

[mypy-celery.*]
ignore_missing_imports = True

[mypy-redis.*]
ignore_missing_imports = True

[mypy-structlog.*]
ignore_missing_imports = True

[mypy-sentry_sdk.*]
ignore_missing_imports = True

[mypy-passlib.*]
ignore_missing_imports = True

[mypy-jose.*]
ignore_missing_imports = True

[mypy-pandas.*]
ignore_missing_imports = True

[mypy-numpy.*]
ignore_missing_imports = True

[mypy-scipy.*]
ignore_missing_imports = True

[mypy-statsmodels.*]
ignore_missing_imports = True

[mypy-PIL.*]
ignore_missing_imports = True

[mypy-aiofiles.*]
ignore_missing_imports = True

# FastAPI and Pydantic are well-typed, but may need some overrides
[mypy-pydantic.*]
warn_return_any = False

[mypy-fastapi.*]
warn_return_any = False

# Module-specific settings for gradual adoption
[mypy-models.*]
# Models should have strict typing since they're data contracts
disallow_any_generics = True
disallow_untyped_defs = True
disallow_incomplete_defs = True

[mypy-api.*]
# API endpoints should have good type coverage
disallow_untyped_defs = True
disallow_incomplete_defs = True

[mypy-services.*]
# Service layer should be well-typed
disallow_untyped_defs = True
disallow_incomplete_defs = True

[mypy-middleware.*]
# Middleware should have good type coverage
disallow_untyped_defs = True
disallow_incomplete_defs = True

[mypy-utils.*]
# Utilities should be well-typed
disallow_untyped_defs = True
disallow_incomplete_defs = True

# Agents can be gradually typed due to CrewAI complexity
[mypy-agents.*]
disallow_untyped_defs = False
disallow_incomplete_defs = False
warn_return_any = False