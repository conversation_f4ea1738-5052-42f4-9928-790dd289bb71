# Fly.io configuration for AiLex Ad Agent System Backend
app = "ailex-ad-agent-backend"
primary_region = "iad"

[build]
  dockerfile = "Dockerfile"

[env]
  ENVIRONMENT = "production"
  HOST = "0.0.0.0"
  PORT = "8000"
  PYTHONDONTWRITEBYTECODE = "1"
  PYTHONUNBUFFERED = "1"

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  max_machines_running = 3
  processes = ["app"]
  
  [http_service.concurrency]
    type = "requests"
    hard_limit = 1000
    soft_limit = 800

  [[http_service.checks]]
    grace_period = "5s"
    interval = "10s"
    method = "GET"
    timeout = "3s"
    path = "/api/v1/health/liveness"
    
  [[http_service.checks]]
    grace_period = "10s"
    interval = "30s"
    method = "GET"
    timeout = "5s"
    path = "/api/v1/health/readiness"

[[vm]]
  memory = "1gb"
  cpu_kind = "shared"
  cpus = 1

# Deploy configuration
[deploy]
  # release_command = "python setup_database.py && python -c 'print(\"Database setup completed\")'"
  strategy = "rolling"

kill_signal = "SIGTERM"
kill_timeout = "30s"

# Secrets (set via fly secrets set)
# DATABASE_URL - Supabase PostgreSQL connection string
# SUPABASE_URL - Supabase project URL
# SUPABASE_KEY - Supabase service key
# OPENAI_API_KEY - OpenAI API key
# GEMINI_API_KEY - Google Gemini API key
# GOOGLE_ADS_DEVELOPER_TOKEN - Google Ads API token
# GOOGLE_ADS_CLIENT_ID - Google Ads OAuth client ID
# GOOGLE_ADS_CLIENT_SECRET - Google Ads OAuth client secret
# GOOGLE_ADS_REFRESH_TOKEN - Google Ads OAuth refresh token
# GOOGLE_ADS_CUSTOMER_ID - Google Ads customer ID
# PINECONE_API_KEY - Pinecone vector database API key
# PINECONE_ENVIRONMENT - Pinecone environment
# SENTRY_DSN - Sentry error tracking DSN

# Regional settings for multi-region deployment (optional)
# [[regions]]
#   name = "iad"  # US East
#   primary = true
# 
# [[regions]]
#   name = "lax"  # US West
#   primary = false
#
# [[regions]]
#   name = "fra"  # Europe
#   primary = false