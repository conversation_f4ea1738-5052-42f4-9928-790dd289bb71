global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # AiLex Backend Application
  - job_name: 'ailex-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/api/v1/metrics'
    scrape_interval: 30s

  # Node Exporter (System Metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # Redis Metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # PostgreSQL Metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Cadvisor for Container Metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  # Fly.io Metrics (when deployed)
  - job_name: 'fly-backend'
    static_configs:
      - targets: ['ailex-ad-agent-backend.fly.dev']
    metrics_path: '/api/v1/metrics'
    scheme: https
    scrape_interval: 60s