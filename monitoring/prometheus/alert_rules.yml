groups:
  - name: ailex_backend_alerts
    rules:
      # High Error Rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second for the last 5 minutes"

      # High Response Time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }} seconds"

      # Service Down
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ $labels.job }} has been down for more than 1 minute"

      # High Memory Usage
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is above 90% for the last 5 minutes"

      # High CPU Usage
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is above 80% for the last 5 minutes"

      # Database Connection Issues
      - alert: DatabaseConnectionFailure
        expr: postgres_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failure"
          description: "PostgreSQL database is not accessible"

      # Redis Connection Issues
      - alert: RedisConnectionFailure
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis connection failure"
          description: "Redis server is not accessible"

      # High Request Rate
      - alert: HighRequestRate
        expr: rate(http_requests_total[5m]) > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High request rate detected"
          description: "Request rate is {{ $value }} requests per second"

  - name: google_ads_alerts
    rules:
      # Google Ads API Rate Limit
      - alert: GoogleAdsRateLimitHit
        expr: rate(google_ads_api_rate_limit_exceeded_total[5m]) > 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Google Ads API rate limit exceeded"
          description: "Google Ads API rate limit has been exceeded"

      # Campaign Optimization Failures
      - alert: CampaignOptimizationFailure
        expr: rate(campaign_optimization_failures_total[10m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High campaign optimization failure rate"
          description: "Campaign optimization failure rate is {{ $value }} per second"

  - name: celery_alerts
    rules:
      # High Task Queue Length
      - alert: HighTaskQueueLength
        expr: celery_queue_length > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High Celery task queue length"
          description: "Celery queue length is {{ $value }} tasks"

      # Task Failure Rate
      - alert: HighTaskFailureRate
        expr: rate(celery_task_failures_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High Celery task failure rate"
          description: "Task failure rate is {{ $value }} failures per second"